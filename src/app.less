// SHOULD sort by color levels
@gray-3: #333;
@gray-31: #313131;
@gray-4: #444;
@gray-6: #666;
@gray-7: #777;
@gray-8: #888;
@gray-9: #999;
@gray-c3: #c3c3c3;
@gray-c4: #c4c4c0;
@gray-b1: #b1b1b1;
@gray-d2d: #d2d6de;
@gray-d9: #d9d9d9;
@gray-d: #ddd;
@gray-d9: #d9d9d9;
@gray-e: #eee;
@gray-e6: #e6e6e6;
@gray-e8: #e8e8e8;
@gray-eb: #ebebeb;
@gray-f4: #f4f4f4;
@gray-e6f: #e6f7ff;
@gray-f6: #f6f6f6;
@gray-f7: #f7f7f7;
@gray-fc: #fcfcfc;
@gray-a5a: #a5a39e;
@gray-f5: #f5f5f5;
@gray-fa: #fafafa;
@gray-dd: #ddd;

@white: #fff;

@page-min-width: 500px;

@black: #000;
@black-1: rgba(0, 0, 0, 0.85);
@black-2: rgba(0, 0, 0, 0.45);
@black-3: rgba(0, 0, 0, 0.05);
@black-4: rgba(0, 0, 0, 0.65);
@black-a00: rgba(0, 0, 0, 0);
@black-a05: rgba(0, 0, 0, 0.05);
@black-a09: rgba(0, 0, 0, 0.09);
@black-a15: rgba(0, 0, 0, 0.15);
@black-a65: rgba(0, 0, 0, 0.65);
@black-b08: rgba(0, 21, 41, .08);

@orange-1: #ffaf60;
@orange-2: #f39c12;
@orange-3: #e08e0b;

@blue-2: #1890ff;
@blue-3: #576b95;
@blue-4: #00c0ef;
@blue-5: #002741;
@blue-7: #108ee9;
@blue-8: #00a7d0;
@blue-9: #3c8db9;
@blue-10: #00acd6;
@blue-11: #40a9ff;

@green-00: #00a65a;
@green-3c: #3c763d;

@red-1: #dd4b39;
@red-2: #ff4d4f;
@red-3: #d73925;
@red-4: #e51c23;
@red-5: #f00;

@min-visual-area-width: 1366px;
@max-visual-area-width: 1960px;

@page-min-width: 500px;

body {
  font-family: 'Myriad Pro', 'Helvetica Neue', Arial, Helvetica, sans-serif;
  font-size: 16px;

  #root {
    height: 100%;
  }
}

@font-face {
  font-family: 'webfont';
  font-display: swap;
  src: url('//at.alicdn.com/t/webfont_j9wnts271x.ttf');
}

.form-item {
  margin-bottom: 24px;

  .form-title {
    line-height: 32px;
  }
}

.common-form {
  .ant-form-item {
    margin-bottom: 5px !important;
  }
}

.empty-l {
  display: inline-block;
  width: 4em;
}

.empty-s {
  display: inline-block;
  width: 2em;
}

.empty-xs {
  display: inline-block;
  width: 1em;
}

.text-link {
  display: inline-block;
  width: 4.5em;
  vertical-align: top;

  &.s {
    width: 2.5em;
  }

  &.m {
    width: 3.5em;
  }

  &.l {
    width: 5.5em;
  }

  &.xl {
    width: 7.5em;
  }

  &.xxl {
    width: 9.5em;
  }

  &:hover {
    text-decoration: underline;
  }

  &.auto {
    width: 100%;
  }
}

.hide-wrap {
  visibility: hidden;
}

.ellipsis {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.multi-ellipsis {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
}

// 适配小屏幕模式, 所有表格不固定, 可滚动
@media screen and (max-width: 1440px) {
  .ant-table-content {
    overflow: auto;
  }

  .ant-table-thead > tr > th {
    min-width: 100px;
  }

  .ant-table-fixed-left, .ant-table-fixed-right {
    display: none;
    position: relative;
  }
}

// 针对更小屏幕的额外优化
@media screen and (max-width: 1024px) {
  .ant-table-content {
    overflow-x: auto;
    overflow-y: visible;
  }

  .ant-table-thead > tr > th {
    min-width: 120px;
    white-space: nowrap;
  }

  .ant-table-tbody > tr > td {
    min-width: 120px;
    white-space: nowrap;
  }
}

.poster-bg {
  float: left;
  border: 1px solid #ddd;
  background-color: darkviolet;
  background-position: center center !important;
  background-repeat: no-repeat !important;
  background-size: cover !important;
}
