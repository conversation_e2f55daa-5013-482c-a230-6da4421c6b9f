import { QrcodeOutlined, UploadOutlined } from '@ant-design/icons';
import { AliyunHelper } from '~/engine';
import { Button, Upload } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { Rnd } from 'react-rnd';

const style = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  border: '1px solid #ddd',
  background: '#f0f0f0',
};
const selectorBoundary = {
  width: '320px',
  height: '569px',
};
const parentBoundary = {
  background: '#fff',
  width: '100%',
  height: '100%',
};
const defaltQrData = { w: 100, h: 100, x: 10, y: 10 };
export default class Poster extends Component {
  static propTypes = {
    background: PropTypes.object,
    onChange: PropTypes.func,
  }

  static defaultProps = {
    background: { qr: defaltQrData },
    onChange: () => { },
  }

  uploadImage = () => {
    return (option) => {
      let aborted = false;
      (async () => {
        try {
          const bgUrl = await AliyunHelper.clipsUploadImage(option.file, (progress) => {
            const percent = Math.round((progress.loaded / progress.total) * 100);
            option.onProgress({ percent });
          });
          if (aborted) {
            return;
          }

          this.onChangeValue(bgUrl, 'bgUrl');
          option.onSuccess();
        } catch (e) {
          option.onError();
        }
      })();

      return {
        abort() {
          aborted = true;
        },
      };
    };
  }

  onChangePosition = async (value) => {
    const data = {};
    _.map(value, (v, k) => { data[k] = _.floor(v); });
    const background = _.cloneDeep(this.props.background) || { qr: defaltQrData };
    this.onChangeValue({ ...background?.qr, ...data }, 'qr');
  }

  onResize = (e, direction, { offsetWidth, offsetHeight }, delta, position) => {
    this.onChangePosition({ w: offsetWidth, h: offsetHeight, ...position });
  }

  onChangeValue = async (e, type) => {
    const value = e.target ? e.target.value : e;
    const background = _.cloneDeep(this.props.background) || { qr: defaltQrData };
    background[type] = value;
    if (type === 'bgUrl') {
      const info = await AliyunHelper.getFileInfo(value);
      background.imageInfo = info;
    }
    if (_.isEmpty(background?.qr)) {
      background.qr = defaltQrData;
    }

    this.props.onChange(background);
  }

  renderPoster = () => {
    const { bgUrl, qr, imageInfo } = this.props?.background || {};
    if (_.isEmpty(bgUrl) || _.isEmpty(qr)) return null;

    const { w, h, x, y } = qr || {};
    const { width, height } = imageInfo || {};
    let boundaryStyle = selectorBoundary;
    if (!_.isEmpty(imageInfo)) {
      boundaryStyle = { ...selectorBoundary, width: `${width / 2}px`, height: `${height / 2}px` };
    }

    return (
      <div className="boundary post-wrap" style={boundaryStyle}>
        <div className="poster-bg" style={{ ...parentBoundary, backgroundImage: `url(${bgUrl})` }} >
          <Rnd
            style={style}
            lockAspectRatio
            bounds=".boundary"
            position={{ x, y }}
            size={{ width: w, height: h }}
            onDragStop={(e, d) => { return this.onChangePosition({ x: d.x, y: d.y }); }}
            onResize={(...params) => { return this.onResize(...params); }}
          >
            <QrcodeOutlined style={{ fontSize: +w, color: '#000' }} />
          </Rnd>
        </div>
      </div>
    );
  }

  render = () => {
    return (
      <div className="custom-poster-wrap">
        <Upload
          name="poster"
          className="avatar-uploader"
          showUploadList={false}
          accept="image/jpg,image/jpeg,image/png"
          beforeUpload={this.beforeUpload}
          customRequest={this.uploadImage('bgUrl')}
        >
          <Button
            ghost
            size="small"
            type="primary"
            style={{ marginBottom: 10 }}
            icon={<UploadOutlined />}
          >上传海报
          </Button>
        </Upload>
        <div>{this.renderPoster()}</div>
      </div>
    );
  }
}
