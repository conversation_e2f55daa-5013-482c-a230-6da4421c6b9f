import { createFromIconfontCN } from '@ant-design/icons';
import Consts from '~/consts';
import _ from 'lodash';
import React, { PureComponent } from 'react';

const IconfontCN = createFromIconfontCN({ scriptUrl: Consts.ALI_ICON_URL });

export default class IconFont extends PureComponent {
  render = () => {
    let { type } = this.props; // eslint-disable-line
    if (!_.startsWith(type, 'icon-')) {
      type = `icon-${type}`;
    }


    return (
      <IconfontCN {...this.props} type={type} />
    );
  }
}
