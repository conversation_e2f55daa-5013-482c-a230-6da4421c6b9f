import './index.less';

import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class AudioPlayer extends PureComponent {
  static propTypes = {
    url: PropTypes.string,
    audioStyle: PropTypes.object,
    bindAudioPlayerRef: PropTypes.func,
    audioControlRef: PropTypes.func,
    loop: PropTypes.bool,
    onTimeUpdate: PropTypes.func,
    onEnded: PropTypes.func,
  }

  static defaultProps = {
    audioStyle: {},
    bindAudioPlayerRef: () => { },
    audioControlRef: () => { },
  }

  render = () => {
    return (
      <div className="message-audio" ref={this.props.bindAudioPlayerRef}>
        <audio
          controls="controls"
          loop={this.props.loop}
          style={this.props.audioStyle}
          src={this.props.url}
          ref={this.props.audioControlRef}
          onTimeUpdate={this.props.onTimeUpdate}
          onEnded={this.props.onEnded}
        >
          <track kind="captions" />
        </audio>
      </div>
    );
  }
}
