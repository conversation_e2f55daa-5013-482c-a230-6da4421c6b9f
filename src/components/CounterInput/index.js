import { StringExtension } from '~/plugins';
import { Input } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

const isChrome = !!window.chrome && !!window.chrome.webstore;

export default class CounterInput extends PureComponent {
  static propTypes = {
    value: PropTypes.any,
    onChange: PropTypes.func,
    onBlur: PropTypes.func,
    limitQuantity: PropTypes.number,
    tip: PropTypes.string,
  }

  static defaultProps = {
    tip: 'default',
    onChange: () => { },
    onBlur: () => { },
  }

  i18nRootPath = 'counterInput'

  constructor(props) {
    super(props);

    this.state = {
      validValue: props.value,
    };

    this.isOnComposition = false;
  }

  componentWillReceiveProps = (nextProps) => {
    if (nextProps.value !== this.props.value && !this.isOnComposition) {
      this.setState({ validValue: nextProps.value });
    }
  }

  i18n = (key) => {
    if (Array.isArray(key)) {
      return this.$i18n(key[0], key[1]);
    }
    return this.$i18n(key);
  }

  leftQuantity = () => {
    const quantity = StringExtension.calculateCharacterLength(this.state.validValue);
    return this.props.limitQuantity >= quantity ? this.props.limitQuantity - quantity : 0;
  }

  onComposition = (e) => {
    if (e.type === 'compositionend') {
      this.isOnComposition = false;

      if (isChrome) {
        this.onChange(e);
      }
    } else {
      this.isOnComposition = true;
    }
  }

  onChange = (e) => {
    let value = e.target ? e.target.value : '';

    if (!this.isOnComposition) {
      value = StringExtension.formatCharacterString(value, this.props.limitQuantity);
      this.setState({ validValue: value });
    }
    this.props.onChange(value);
  }

  render = () => {
    const {
      limitQuantity,
      tip,
      onChange,
      ...otherProps
    } = this.props;

    return (
      <div>
        <Input
          onCompositionStart={this.onComposition}
          onCompositionUpdate={this.onComposition}
          onCompositionEnd={this.onComposition}
          onChange={this.onChange}
          onBlur={this.onBlur}
          {...otherProps}
        />
        <div className="ant-form-extra">{this.$i18n(this.props.tip, this.leftQuantity())}</div>
      </div>
    );
  }
}
