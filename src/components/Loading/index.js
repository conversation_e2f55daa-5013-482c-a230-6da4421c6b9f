import './index.less';

import { store } from '~/pages/index';
import { Spin } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { connect } from 'react-redux';

import * as actions from './state';

@connect(
  (state) => { return state.loading; },
)
export default class Loading extends PureComponent {
  static show = () => {
    store.dispatch(actions.show());
  }

  static hide = () => {
    store.dispatch(actions.hide());
  }

  static propTypes = {
    isVisible: PropTypes.bool,
  }

  render = () => {
    return (
      <Spin
        className="loading"
        style={{ maxHeight: document.body.clientHeight }}
        size="large"
        spinning={this.props.isVisible}
      />
    );
  }
}
