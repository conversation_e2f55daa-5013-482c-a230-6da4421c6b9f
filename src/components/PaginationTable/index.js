import Configs from '~/consts';
import { Table } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class PaginationTable extends PureComponent {
  static propTypes = {
    needPagination: PropTypes.bool,
    wrapClassName: PropTypes.string,
    dataSource: PropTypes.array.isRequired,
    rowSelection: PropTypes.object,
    expandable: PropTypes.object,
    pagination: PropTypes.object,
    totalDataCount: PropTypes.number,
    columns: PropTypes.array.isRequired,
    rowKey: PropTypes.string,
    size: PropTypes.string,
    scroll: PropTypes.object,
    loading: PropTypes.bool,
    onPaginationChange: PropTypes.func,
    onTableChange: PropTypes.func,
  }

  static defaultProps = {
    needPagination: true,
    pagination: {},
    totalDataCount: 0,
    onPaginationChange: () => { },
    onTableChange: () => { },
  }

  getPagination = () => {
    const { pageIndex, pageSize } = this.props.pagination;
    return {
      pageSize,
      current: pageIndex,
      total: this.props.totalDataCount,
      showSizeChanger: true,
      onChange: this.onPaginationChange,
      onShowSizeChange: this.onShowSizeChange,
      pageSizeOptions: Configs.pagination.PAGE_SIZE_OPTIONS,
      showTotal: (total) => {
        return <b>{this.$i18n('paginationTable.totalInfo', total)}</b>;
      },
    };
  }

  onPaginationChange = (pageIndex, pageSize) => {
    this.props.onPaginationChange({ pageIndex, pageSize });
  }

  onShowSizeChange = (pageIndex, pageSize) => {
    const params = {
      pageIndex: Configs.pagination.DEFAULT_PAGE_INDEX,
      pageSize,
    };

    this.props.onPaginationChange(params);
  }

  render = () => {
    return (
      <Table
        className={this.props.wrapClassName}
        columns={this.props.columns}
        dataSource={this.props.dataSource}
        rowSelection={this.props.rowSelection}
        expandable={this.props.expandable}
        pagination={this.props.needPagination ? this.getPagination() : false}
        rowKey={this.props.rowKey}
        size={this.props.size}
        loading={this.props.loading}
        scroll={this.props.scroll}
        onChange={this.props.onTableChange}
      />
    );
  }
}
