import './index.less';

import { Toast } from '~/components';
import { Extentions } from '~/engine';
import { Platform } from '~/plugins';
import { Divider, Input, Modal, Popconfirm } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class AutoPrompt extends PureComponent {
  static propTypes = {
    text: PropTypes.string,
    onImprove: PropTypes.func,
  }

  static defaultProps = {
    text: '',
    onImprove: () => { },
  }

  constructor(props) {
    super(props);

    this.state = {
      isLoading: false,
      preText: '',
      prompt: '',
    };
  }

  onClickPrompts = () => {
    Platform.emit(Platform.Event.OPEN_PROMPT_DRAWER, { prompt: this.props.text });
  }

  render = () => {
    const { text } = this.props;
    const { isLoading, preText, prompt } = this.state;
    return (
      <div className="auto-prompt">
        {
          isLoading ? <a>AI优化中...</a> : (
            <>
              <div>
                <a onClick={() => { return this.onClickPrompts(); }}>Prompt 库</a>
                <Divider type="vertical" />
                <Popconfirm
                  placement="bottomRight"
                  title="确认优化？"
                  onConfirm={async () => {
                    if (text.trim()) {
                      await this.setState({ isLoading: true, preText: text });
                      Extentions.formatPrompt(text, (t, isStop) => {
                        if (this.state.isLoading) {
                          this.setState({ prompt: t });

                          setTimeout(() => {
                            const objDiv = document.getElementById('auto-prompt-area');
                            objDiv.scrollTop = objDiv?.scrollHeight;
                          }, 500);
                          if (isStop) {
                            this.setState({ isLoading: false });
                          }
                        }
                      });
                    } else {
                      Toast.show('请先输入系统提示词', Toast.Type.WARNING);
                    }
                  }}
                  okText="确认"
                  cancelText="取消"
                  disabled={!text}
                >
                  <a style={{ display: 'none' }} href="#" disabled={!text}>AI优化系统提示词</a>
                </Popconfirm>
              </div>
              {
                preText && (
                  <>
                    <a
                      style={{ marginRight: 10 }}
                      href="#"
                      onClick={() => {
                        this.props.onImprove(preText);
                        this.setState({ preText: '' });
                      }}
                    >
                      撤销
                    </a>
                  </>
                )
              }
            </>
          )
        }
        <Modal
          width={800}
          title="AI优化系统提示词"
          okText={isLoading ? '生成中...' : '应用'}
          open={!!(prompt || isLoading)}
          onCancel={() => {
            this.setState({ prompt: '', preText: '', isLoading: false });
          }}
          onOk={() => {
            if (isLoading) {
              Toast.show('生成中，请稍后!', Toast.Type.WARNING);
              return;
            }
            this.props.onImprove(prompt);
            this.setState({ prompt: '' });
          }}
        >
          <Input.TextArea
            id="auto-prompt-area"
            value={prompt}
            autoSize={{ minRows: 20, maxRows: 20 }}
            onChange={(e) => {
              this.setState({ prompt: e.target.value });
            }}
          />
        </Modal>
      </div>
    );
  }
}
