import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Divider, Form, Input, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { v4 as uuid } from 'uuid';

import InputUpload from '../InputUpload';
import ToolEditor from '../ToolEditor';

export default class ReplyMessages extends Component {
  static propTypes = {
    datas: PropTypes.array,
    onChange: PropTypes.func,
  }

  state = {
    datas: [],
  }

  componentDidMount = () => {
    const datas = (this.props.datas || [{ type: 'TEXT', text: {}, image: {}, card: {} }]).map((x) => {
      return { uid: uuid(), ...x };
    });
    this.setState({ datas });
  }

  onChangeWelcomeTips = (e, type, index, objKey) => {
    const value = e?.target ? e.target.value : e;
    const welcomeTips = _.cloneDeep(this.state.datas);
    if (objKey) {
      welcomeTips[index][objKey][type] = value;
    } else {
      welcomeTips[index][type] = value;
    }

    this.setState({ datas: welcomeTips });
    this.props.onChange(welcomeTips);
  }

  onAddWelcomeTips = (index) => {
    const welcomeTips = _.cloneDeep(this.state.datas);
    welcomeTips.splice(index + 1, 0, { type: 'TEXT', text: {}, image: {}, card: {} });
    this.setState({ datas: welcomeTips });
    this.props.onChange(welcomeTips);
  }

  onDelWelcomeTips = (index) => {
    if (!index) return;
    const welcomeTips = _.cloneDeep(this.state.datas);
    if (welcomeTips.length === 1) { return; }
    welcomeTips.splice(index, 1);
    this.setState({ datas: welcomeTips });
    this.props.onChange(welcomeTips);
  }

  render = () => {
    const { datas } = this.state;

    return (
      <Form labelCol={{ span: 2 }} wrapperCol={{ span: 22 }} className="common-form">
        {
          (datas || []).map((x, idx) => {
            return (
              <>
                <Form.Item label="类型">
                  <div style={{ display: 'flex' }}>
                    <Select
                      disabled={idx === 0}
                      value={x?.type}
                      onChange={(e) => { return this.onChangeWelcomeTips(e, 'type', idx); }}
                    >
                      <Select.Option value="TEXT">文本</Select.Option>
                      <Select.Option value="CARD">图文</Select.Option>
                      <Select.Option value="IMAGE">图片</Select.Option>
                    </Select>

                    <span style={{ display: 'flex', width: 120, alignItems: 'center', justifyContent: 'flex-end' }}>
                      <PlusOutlined onClick={() => { return this.onAddWelcomeTips(idx); }} />
                      <Divider type="vertical" />
                      <DeleteOutlined onClick={() => { return this.onDelWelcomeTips(idx); }} />
                    </span>
                  </div>
                </Form.Item>
                {
                  x.type === 'TEXT' &&
                  <Form.Item label="文本">
                    <ToolEditor
                      noEscaped
                      types={[
                        { type: 'message-user', name: '用户昵称', value: '#user#' },
                        { type: 'message-employee', name: '员工昵称', value: '#employee#' },
                      ]}
                      key={`${x.uid || 0}`}
                      value={x.text?.text}
                      editorId={`${x.uid || 0}`}
                      barKeys={[]}
                      onChange={(value) => { return this.onChangeWelcomeTips(value, 'text', idx, 'text'); }}
                    />
                  </Form.Item>
                }
                {
                  x.type === 'IMAGE' &&
                  <Form.Item label="图片">
                    <InputUpload
                      accept="image/*"
                      url={x.image?.fileUrl}
                      onChange={(e) => { return this.onChangeWelcomeTips(e, 'fileUrl', idx, 'image'); }}
                    />
                  </Form.Item>
                }
                {
                  x.type === 'CARD' &&
                  <Form.Item label="图文">
                    <div className="card-item">
                      <InputUpload
                        accept="image/*"
                        placeholder="封面"
                        url={(x.card || {})?.thumbnail}
                        onChange={(e) => { return this.onChangeWelcomeTips(e, 'thumbnail', idx, 'card'); }}
                      />
                      <Input
                        addonBefore="标题"
                        style={{ margin: '5px 0' }}
                        value={(x.card || {}).title}
                        onChange={(e) => { return this.onChangeWelcomeTips(e, 'title', idx, 'card'); }}
                      />
                      <Input
                        addonBefore="描述"
                        value={(x.card || {}).description}
                        onChange={(e) => { return this.onChangeWelcomeTips(e, 'description', idx, 'card'); }}
                      />
                      <Input
                        addonBefore="链接"
                        style={{ margin: '5px 0' }}
                        value={(x.card || {}).url}
                        onChange={(e) => { return this.onChangeWelcomeTips(e, 'url', idx, 'card'); }}
                      />
                    </div>
                  </Form.Item>

                }
              </>
            );
          })
        }

      </Form>
    );
  }
}
