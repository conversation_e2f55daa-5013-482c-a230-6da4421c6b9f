import './index.less';

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '~/engine';
import { Button, Input, Progress, Upload } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { v1 as uuid } from 'uuid';

export default class InputUpload extends PureComponent {
  static propTypes = {
    url: PropTypes.string,
    accept: PropTypes.string,
    addonBefore: PropTypes.string,
    size: PropTypes.string,
    placeholder: PropTypes.string,
    style: PropTypes.object,
    children: PropTypes.object,
    onChange: PropTypes.func,
    onPressEnter: PropTypes.func,
    onUpload: PropTypes.func,
    onProgress: PropTypes.func,
    onRemove: PropTypes.func,
  }

  static defaultProps = {
    accept: 'audio/*',
    size: 'middle',
    style: {},
    onChange: () => { },
    onPressEnter: () => { },
    onUpload: () => { },
    onRemove: () => { },
  }

  state = {
    percent: 0,
  }

  onChange = (e) => {
    this.props.onChange(e.target.value);
  }

  onPressEnter = () => {
    const { url } = this.props;
    const name = (url || '').split('/').pop().split('.').shift();
    this.props.onPressEnter(url, name);
  }

  onUpload = async (option) => {
    try {
      this.setState({ percent: 0, isLoading: true });
      const name = option.file.name.split('.').shift();
      const result = await AliyunHelper.getClipsSignature();
      const uuidStr = uuid();
      const url = await AliyunHelper.clipsUploadImage(option.file, (progress) => {
        const percent = Math.round((progress.loaded / progress.total) * 100);
        this.setState({ percent });
        option.onProgress({ percent });
        if (this.props.onProgress) {
          this.props.onProgress({ percent, name, uuid: uuidStr });
        }
      }, {
        filePath: result.directory,
        result: { ...result, uploadDomain: result.url },
      });
      option.onSuccess();
      this.props.onChange(url, uuidStr);
      this.props.onUpload(url, name);
    } catch (e) {
      option.onError();
    } finally {
      this.setState({ isLoading: false });
    }
  }

  render = () => {
    if (this.props.children) {
      return (
        <Upload
          accept={this.props.accept}
          showUploadList={false}
          customRequest={this.onUpload}
          onRemove={this.props.onRemove}
        >
          {this.props.children}
        </Upload>
      );
    }

    return (
      <div style={this.props.style}>
        <div className="input-upload-container">
          <Input
            size={this.props.size}
            value={this.props.url}
            addonBefore={this.props.addonBefore}
            placeholder={this.props.placeholder}
            onChange={this.onChange}
            onPressEnter={this.onPressEnter}
          />
          <Upload
            accept={this.props.accept}
            showUploadList={false}
            customRequest={this.onUpload}
          >
            <Button size={this.props.size}>上传</Button>
          </Upload>
        </div>
        {this.state.isLoading && <Progress percent={this.state.percent} />}
      </div>
    );
  }
}
