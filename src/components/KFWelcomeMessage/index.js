import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Divider, Form, Input, Radio, Select, Tag } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import Toast from '../Toast';

export default class KFWelcomeMessage extends PureComponent {
  static propTypes = {
    showTitle: PropTypes.bool,
    text: PropTypes.object,
    onChange: PropTypes.func,
  }

  defaultProps = {
    showTitle: true,
  }

  state = {
    payUrl: "{{'https://teacher.bzy.ai/benefits/%s/%s/:sku'|format(kf_id, inviter_user_id)|shorten_url}}",
    welcome: {},
  }

  componentDidMount = () => {
    let newText = (this.props.text || '').replace(/'\|format\(/g, "'&format(");
    newText = newText.replace(/\)\|shorten_url/g, '&&shorten_url');
    const data = this.formatMenu([newText]);

    if (data.welcomeType === 'menu') {
      const list = data.menus.list.map((item) => {
        if (item.type === 'click') {
          item.click.id = item.click.id.replace(/'&format\(/g, "'|format("); // eslint-disable-line
          item.click.id = item.click.id.replace(/&&shorten_url/g, ')|shorten_url');// eslint-disable-line
        }

        if (item.type === 'view') {
          item.view.url = item.view.url.replace(/'&format\(/g, "'|format("); // eslint-disable-line
          item.view.url = item.view.url.replace(/&&shorten_url/g, ')|shorten_url');// eslint-disable-line
        }

        if (item.type === 'text') {
          item.text.content = item.text.content.replace(/'&format\(/g, "'|format("); // eslint-disable-line
          item.text.content = item.text.content.replace(/&&shorten_url/g, ')|shorten_url');// eslint-disable-line
        }

        return item;
      });
      data.menus.list = list;
    }
    this.setState({ welcome: data });
  }

  formatMenu = (items = []) => {
    let welcomeType = 'text';
    const msg = items[0];
    let menus = { headContent: '', list: [{ type: 'text', text: { content: '' } }] };
    const isMenu = (_.head(items) || '').startsWith('menu:|');
    if (isMenu) {
      welcomeType = 'menu';
      menus = this.convertTextToMenu(_.head(items));
    }
    return { welcomeType, menus, msg };
  }

  convertTextToMenu = (txt) => {
    const result = { headContent: '', list: [] };
    const parts = txt.split('|');
    const headContent = parts[1].trim().startsWith('[') ? '' : parts[1].trim();
    const menuItems = headContent ? parts.slice(1) : parts;
    result.headContent = headContent;
    const regex = /\[(.*?)\]\((.*?)\)/;

    for (const item of menuItems) {
      const match = item.trim().match(regex);
      if (match) {
        const text = match[1];
        const idOrUrl = match[2];
        if (idOrUrl.startsWith('http')) {
          result.list.push({ type: 'view', view: { url: idOrUrl, content: text } });
        } else if (idOrUrl) {
          result.list.push({ type: 'click', click: { id: idOrUrl, content: text } });
        } else {
          result.list.push({ type: 'text', text: { content: text } });
        }
      }
    }

    result.list = result.list.slice(0, 10);
    return result;
  }

  onChangeValue = async (e, key) => {
    const value = e?.target ? e.target.value : e;
    const welcome = _.cloneDeep(this.state.welcome);
    welcome[key] = value;
    this.setState({ welcome });
    let text = welcome.msg;
    if (welcome?.welcomeType === 'menu') {
      let welcomeMsg = `menu:|${welcome?.menus.headContent}`;
      (welcome?.menus?.list || []).forEach((item) => {
        if (item.type === 'text') {
          welcomeMsg += `|[${item?.text?.content}]()`;
        } else {
          welcomeMsg += `|[${item[item.type].content}](${item[item.type].id || item[item.type].url})`;
        }
      });

      text = welcomeMsg;
    }

    this.props.onChange(text);
  }

  onChangeMenu = (e, key, index) => {
    const value = e?.target ? e.target.value : e;
    const menus = this.state?.welcome.menus || [];
    if (_.isUndefined(index)) {
      menus[key] = value;
    } else {
      if (key === 'type') { // eslint-disable-line
        const idOrUrl = key === 'click' ? 'id' : 'url';
        menus.list[index] = { type: value, [value]: { content: '', [idOrUrl]: '' } };
      } else {
        menus.list[index][menus.list[index].type][key] = value;
      }
    }

    this.onChangeValue(menus, 'menus');
  }

  renderContent = (welcome) => {
    return (
      <>
        <Input.Group compact style={{ display: 'flex', alignItems: 'center', margin: '5px 0 10px 0' }}>
          <span><Tag>支付链接:</Tag>{this.state.payUrl} </span>
          <Button
            size="small"
            style={{ marginLeft: 10 }}
            onClick={async () => {
              await navigator.clipboard.writeText(this.state.payUrl);
              Toast.show('复制成功', Toast.Type.SUCCESS);
            }}
          >复制
          </Button>
        </Input.Group>
        <Radio.Group
          value={welcome.welcomeType}
          style={{ marginBottom: 10 }}
          onChange={(e) => { return this.onChangeValue(e, 'welcomeType'); }}
        >
          <Radio value="text">文本</Radio>
          <Radio value="menu">菜单</Radio>
        </Radio.Group>

        {
          welcome.welcomeType === 'text' &&
          <Input.TextArea
            autoSize={{ minRows: 5 }}
            value={welcome.msg}
            onChange={(e) => { return this.onChangeValue(e, 'msg'); }}
          />
        }
        {
          welcome.welcomeType === 'menu' &&
          <Form labelCol={{ span: 2 }} wrapperCol={{ span: 22 }} className="common-form">
            <Form.Item label="起始">
              <Input.TextArea
                value={welcome.menus.headContent}
                onChange={(e) => { return this.onChangeMenu(e, 'headContent'); }}
              />
            </Form.Item>
            {
              welcome.menus.list.map((item, index) => {
                return (
                  <Form.Item label={`菜单${index + 1}`} className="common-form">
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Select
                        value={item.type}
                        style={{ width: 220 }}
                        onChange={(e) => { return this.onChangeMenu(e, 'type', index); }}
                      >
                        <Select.Option value="text">文本</Select.Option>
                        <Select.Option value="click">回复菜单</Select.Option>
                        <Select.Option value="view">超链菜单</Select.Option>
                      </Select>
                      <span>
                        <PlusOutlined
                          onClick={() => {
                            const menus = _.cloneDeep(welcome.menus);
                            if (menus.list.length >= 10) { return; }
                            menus.list.push({ type: 'text', text: { content: '' } });
                            this.setState({ welcome: { ...welcome, menus } });
                          }}
                        />
                        <Divider type="vertical" />
                        <DeleteOutlined
                          onClick={() => {
                            const menus = _.cloneDeep(welcome.menus);
                            if (menus.list.length === 1) { return; }
                            menus.list.splice(index, 1);
                            this.setState({ welcome: { ...welcome, menus } });
                          }}
                        />
                      </span>
                    </div>
                    <Input
                      style={{ margin: '5px 0' }}
                      addonBefore={item.type !== 'text' ? '菜单' : '内容'}
                      value={item[item.type]?.content}
                      onChange={(e) => { return this.onChangeMenu(e, 'content', index); }}
                    />
                    {
                      item.type !== 'text' &&
                      <Input
                        addonBefore="内容"
                        value={item.click?.id || item.view?.url}
                        onChange={(e) => { return this.onChangeMenu(e, item.type === 'click' ? 'id' : 'url', index); }}
                      />
                    }
                  </Form.Item>
                );
              })
            }
          </Form>
        }
      </>
    );
  }

  render = () => {
    const { welcome } = this.state;
    if (!this.props.showTitle) return this.renderContent(welcome);

    return (
      <Form.Item label="欢迎语">
        {this.renderContent(welcome)}
      </Form.Item>
    );
  }
}
