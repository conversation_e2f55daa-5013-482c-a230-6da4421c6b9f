/* eslint-disable react/prop-types, consistent-return, max-len */
import 'braft-editor/dist/index.css';
import 'braft-extensions/dist/emoticon.css';

import './index.less';

import { IconFont } from '~/components';
import { Editor } from '~/plugins';
import { Tooltip } from 'antd';
import BraftEditor from 'braft-editor';
import { ContentUtils } from 'braft-utils';
import classNames from 'classnames';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

const BASE_KEYS = [];
export default class ToolEditor extends PureComponent {
  static propTypes = {
    isMin: PropTypes.bool,
    noEscaped: PropTypes.bool,
    value: PropTypes.string,
    onChange: PropTypes.func,
    barKeys: PropTypes.array,
    editorId: PropTypes.string.isRequired, // 由外部传入, 需保证唯一
    types: PropTypes.array,
  }

  static defaultProps = {
    isMin: false,
    value: '',
    barKeys: [],
    noEscaped: true,
    onChange: () => { },
  }

  state = {
    editorState: null,
  }

  componentDidMount() {
    const { value, editorId } = this.props;
    BraftEditor.use([this.entityUser]);

    this.setState({ // eslint-disable-line
      editorState: BraftEditor.createEditorState(this.formatTextToHtml(value), { editorId }),
    });
  }

  entityUser = {
    type: 'entity',
    includeEditors: [this.props.editorId],
    name: 'ENTITY-USER',
    mutability: 'IMMUTABLE',
    data: { value: '', link: '' },
    component: (props) => {
      const entity = props.contentState.getEntity(props.entityKey);
      const { value, link } = entity.getData();
      return (
        <span
          data-value={value}
          data-link={link}
          onClick={() => { return this.onClickTag(props); }} // 别问为什么..
          className={classNames('user-item', { link })}
        >
          {props.children}
        </span>
      );
    },
    importer: (nodeName, node) => {
      if (nodeName.toLowerCase() === 'span' && node.classList && node.classList.contains('user-item')) {
        const { link, title, value, form } = node.dataset;
        return { mutability: 'IMMUTABLE', data: { link, title, value, form } };
      }
    },
    exporter: (entity) => {
      const { link, title, value } = entity.data;
      if (link && title) { // LINK
        return `<a href="${link}">${title}</a>`;
      }

      return value;
    },
  };

  getControlKeys = () => {
    let controlKeys = _.cloneDeep(BASE_KEYS);
    if (!_.isEmpty(this.props.barKeys)) {
      controlKeys = _.concat(controlKeys, this.props.barKeys);
    }

    return controlKeys;
  }

  formatTextToHtml = (text = '') => {
    const { types } = this.props;
    let keys = this.getControlKeys();
    const attachedKeys = types.map((item) => {
      return { key: item.value, value: item.name };
    });
    keys = keys.concat(attachedKeys);
    return Editor.formatTextToHtml(text, keys, true);
  }

  formatHtmlToText = (text = '') => {
    return this.props.noEscaped ? Editor.formatJsonToText(text, this.props.types) : Editor.formatHtmlToText(text);
  }

  insertEditor = (...args) => {
    const { editorState } = this.state;

    this.setState({
      editorState: ContentUtils.insertText(editorState, ...args), // 插入Tag
    }, () => { this.afterInsert(); });
  }

  afterInsert = () => {
    this.setState({ editorState: ContentUtils.insertText(this.state.editorState, ' ') }); // 后跟空格以显示光标
  }

  onChangeEditor = (editorState) => {
    this.setState({ editorState });
    const text = this.formatHtmlToText(this.props.noEscaped ? editorState.toRAW(true) : editorState.toHTML());
    this.props.onChange(text);
  }

  onClickTag = () => {
    // To do something
  }

  onChangevalue = (e, type) => {
    this.setState({ [type]: e.target.value });
  }

  onInsertItem = (icon = '') => {
    const keys = this.getControlKeys();
    const data = keys.find((item) => { return item.icon === icon; });
    const entity = { type: 'ENTITY-USER', mutability: 'IMMUTABLE', data: { value: data.key } };
    this.insertEditor(data.value, null, entity);
  }

  onInsertUser = (item = {}) => {
    const { types } = this.props;
    const data = types.find((t) => { return t.type === item.icon; });
    const entity = { type: 'ENTITY-USER', mutability: 'IMMUTABLE', data: { value: data.value } };
    this.insertEditor(data.name, null, entity);
  }

  renderIcon = ({ icon, value }) => {
    return (
      <Tooltip placement="top" title={value}>
        <IconFont className="editor-icon" type={icon} />
      </Tooltip>
    );
  }

  render = () => {
    const { editorId, isMin, types } = this.props;
    let controlKeys = this.getControlKeys();
    const attachedControlKeys = types.map((item) => {
      return { icon: item.type, value: `插入${item.name}` };
    });
    controlKeys = controlKeys.concat(attachedControlKeys);

    const extendControls = [];
    _.map(controlKeys, (item) => {
      extendControls.push({
        key: `${item.icon}-dropdown`,
        type: 'button',
        text: this.renderIcon(item),
        onClick: () => { return this.onInsertUser(item); },
      });
    });
    const editorStyle = _.isEmpty(extendControls) ? null : { height: 120 };

    return (
      <div className={classNames('base-common-editor-container base-editor', { 'min-tool-editor': isMin })}>
        <BraftEditor
          style={editorStyle}
          id={editorId}
          controls={extendControls}
          value={this.state.editorState}
          ref={(ref) => { this[`ref${editorId}`] = ref; }}
          onChange={this.onChangeEditor}
          stripPastedStyles
        />
      </div>
    );
  }
}
