import './index.less';

import { Input } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

const { TextArea } = Input;
export default class CounterTextArea extends PureComponent {
  static propTypes = {
    value: PropTypes.any,
    onChange: PropTypes.func,
    maxLength: PropTypes.number,
  }

  static defaultProps = {
    value: '',
    maxLength: 200,
    onChange: () => {},
  }

  render = () => {
    const { maxLength, value } = this.props;
    const suffix = `${value.length}/${maxLength}`;

    return (
      <div className="block">
        <TextArea {...this.props} />
        <span className="counter">{suffix}</span>
      </div>
    );
  }
}
