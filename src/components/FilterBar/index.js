import './index.less';

import { CloseCircleOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>, Divider, Input, Row } from 'antd';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';

export default class FilterBar extends Component {
  static propTypes = {
    canAdd: PropTypes.bool,
    canSearch: PropTypes.bool,
    canRefresh: PropTypes.bool,
    shouldShowReset: PropTypes.bool,
    shouldShowTopDivider: PropTypes.bool,
    shouldShowBottomDivider: PropTypes.bool,
    shouldShowSearchInput: PropTypes.bool,
    addText: PropTypes.string,
    placeholder: PropTypes.string,
    idPlaceholder: PropTypes.string,
    searchKeyWords: PropTypes.string,
    searchId: PropTypes.string,
    renderSelects: PropTypes.func,
    renderButtons: PropTypes.func,
    onSearch: PropTypes.func,
    onChange: PropTypes.func,
    onIdChange: PropTypes.func,
    onReset: PropTypes.func,
    onRefresh: PropTypes.func,
    onAdd: PropTypes.func,
  }

  static defaultProps = {
    canAdd: false,
    canSearch: true,
    canRefresh: false,
    shouldShowReset: false,
    shouldShowTopDivider: true,
    shouldShowBottomDivider: true,
    shouldShowSearchInput: true,
    addText: '',
    placeholder: '',
    idPlaceholder: '',
    searchKeyWords: '',
    searchId: undefined,
    renderSelects: null,
    onAdd: () => {},
    onSearch: () => {},
    onChange: () => {},
    onIdChange: () => {},
    onReset: () => {},
    onRefresh: () => {},
  }

  emitEmpty = (name) => {
    return () => {
      if (name === 'keywords') {
        this.props.onChange('');
      } else if (name === 'id') {
        this.props.onIdChange('');
      }
    };
  }

  getRangePickerDisabledDates = (dates) => {
    return dates ? dates.isAfter(moment(), 'day') : null;
  }

  onChange = (e) => {
    this.props.onChange(e.target.value);
  }

  onIdChange = (e) => {
    this.props.onIdChange(e.target.value);
  }

  renderInput = () => {
    if (!this.props.shouldShowSearchInput) {
      return null;
    }

    const suffix = this.props.searchKeyWords ?
      (<CloseCircleOutlined onClick={this.emitEmpty('keywords')} />) : <span />;
    const IdSuffix = this.props.searchId ?
      (<CloseCircleOutlined onClick={this.emitEmpty('id')} />) : <span />;

    return (
      <Col>
        <Input
          className="filter-input"
          placeholder={this.props.placeholder}
          value={this.props.searchKeyWords}
          suffix={suffix}
          onChange={this.onChange}
          onPressEnter={this.props.onSearch}
          disabled={!this.props.canSearch}
          style={{ marginBottom: 16 }}
        />
        {
        this.props.searchId !== undefined && (<Input
          className="filter-input"
          placeholder={this.props.idPlaceholder}
          value={this.props.searchId}
          suffix={IdSuffix}
          onChange={this.onIdChange}
          onPressEnter={this.props.onSearch}
          disabled={!this.props.canSearch}
          style={{ marginBottom: 16 }}
        />)
        }

        <Button
          type="primary"
          onClick={this.props.onSearch}
          disabled={!this.props.canSearch}
          style={{ marginBottom: 16 }}
        >
          {this.$i18n('filterBar.search')}
        </Button>
      </Col>
    );
  }

  renderBar = () => {
    return (
      <Row align="middle" type="flex" justify="space-between" className="filter-bar-wrap" gutter={16}>
        <Col className="filter-bar-select-wrap">
          <Row align="middle" type="flex" gutter={16}>
            {
              this.props.renderSelects &&
              <Col>
                {this.props.renderSelects()}
              </Col>
            }
            {this.renderInput()}
            {
              this.props.shouldShowReset &&
              <Col className="col">
                <Button onClick={this.props.onReset}>
                  {this.$i18n('filterBar.reset')}
                </Button>
              </Col>
            }
            {
              this.props.canRefresh &&
              <Col className="col">
                <Button onClick={this.props.onRefresh}>
                  {this.$i18n('filterBar.refresh')}
                </Button>
              </Col>
            }
          </Row>
        </Col>
        <Col className="filter-bar-btn-wrap">
          <Row align="middle" type="flex" gutter={16}>
            {
              this.props.canAdd &&
              <Col className="col">
                <Button type="primary" onClick={this.props.onAdd}>
                  {this.props.addText || this.$i18n('filterBar.add')}
                </Button>
              </Col>
            }
            {
              this.props.renderButtons &&
              <Col className="col">
                {this.props.renderButtons()}
              </Col>
            }
          </Row>
        </Col>
      </Row>
    );
  }

  render = () => {
    return (
      <div className="filter-bar">
        {
          this.props.shouldShowTopDivider && <Divider className="divider" />
        }
        {
          this.renderBar()
        }
        {
          this.props.shouldShowBottomDivider && <Divider className="divider" />
        }
      </div>
    );
  }
}
