@import 'app.less';

.color-picker {
  position: relative;
  line-height: 1.5;

  .color-picker-preview {
    display: inline-block;
    width: 32px;
    height: 32px;
    padding: 4px;
    border: 1px solid @gray-dd;
    vertical-align: middle;
    border-radius: 4px;
    cursor: pointer;

    .color-preview-box {
      width: 100%;
      height: 100%;
      border: 1px solid @gray-dd;
      border-radius: 2px;
    }
  }

  &.color-picker-rectangle {
    .color-picker-preview {
      width: 100px;
    }
  }
}

.color-picker-popover {
  .sketch-picker {
    width: 250px !important;
    border: 1px solid rgba(0, 0, 0, 0.15);
    box-shadow: none !important;
  }

  input {
    display: inline-block;
    margin: 0;
    padding: 4px 11px;
    font-size: 14px;
    line-height: 1.5;
    color: @gray-6;
    background-color: @white;
    border-radius: 4px;
    box-sizing: border-box;
    list-style: none;
    transition: all .3s;

    &:focus {
      outline: 0 !important;
      border-right-width: 1px !important;
      box-shadow: 0 0 0 1px @blue-3 !important;
    }
  }

  .ant-popover-inner-content {
    padding: 0;
  }
}
