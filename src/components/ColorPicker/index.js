import './index.less';

import { Popover } from 'antd';
import classNames from 'classnames';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { SketchPicker } from 'react-color';

export default class ColorPicker extends PureComponent {
  static propTypes = {
    color: PropTypes.string,
    placement: PropTypes.string,
    shape: PropTypes.oneOf(['square', 'rectangle']),
    onChangeValue: PropTypes.func.isRequired,
  }

  static defaultProps = {
    shape: 'square',
    color: '#FFFFFF',
    placement: 'bottomLeft',
  }

  renderPicker = () => {
    return (
      <SketchPicker
        color={this.props.color}
        onChangeComplete={(color) => { this.props.onChangeValue(color.hex); }}
      />
    );
  }

  render = () => {
    const { placement, color, shape } = this.props;

    return (
      <div className={classNames('color-picker', { [`color-picker-${shape}`]: shape })}>
        <Popover
          placement={placement}
          content={this.renderPicker()}
          trigger="click"
          overlayClassName="color-picker-popover"
        >
          <div className="color-picker-preview">
            <div className="color-preview-box" style={{ backgroundColor: color }} />
          </div>
        </Popover>
      </div>
    );
  }
}
