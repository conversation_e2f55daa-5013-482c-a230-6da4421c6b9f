import './index.less';

import { LogoutOutlined, SettingFilled, UserSwitchOutlined } from '@ant-design/icons';
import Consts from '~/consts';
import Engine, { Sessions } from '~/engine';
import { Platform } from '~/plugins';
import { Dropdown, Layout, Menu, Tabs } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { PureComponent } from 'react';
import { connect } from 'react-redux';

const { Header } = Layout;
const ActionType = { LOGOUT: 'logout' };

@connect()
export default class GlobalHeader extends PureComponent {
  static propTypes = {
    pathname: PropTypes.string,
    logoname: PropTypes.string,
    partners: PropTypes.array,
    menuSetting: PropTypes.array.isRequired,
    onUpdateSubMenus: PropTypes.func.isRequired,
    switchPartner: PropTypes.func.isRequired,
  }

  defaultProps = {
    onToggle: () => { },
    pathname: window.location.pathname || Consts.ROUTE.HOMEPAGE,
  }

  state = {
    logoUrl: Platform.isMai() ? '/static/maiLogo.png' : '/static/logoBigW.png',
  }

  logout = () => {
    Sessions.clear();
    let loginPath = Consts.ROUTE.CLIPS_LOGIN;
    const mode = Engine.getItem('MODE');
    const platform = Engine.getItem('PLATFORM');
    const qryObj = { mode, platform };
    if (mode && mode === 'pwd') {
      loginPath = Consts.ROUTE.LOGIN;
    }

    if (window.location.search) {
      const query = qs.parse(window.location.search, { ignoreQueryPrefix: true });
      if (query.channel) {
        qryObj.channel = query.channel;
      }
    }

    this.$replace(loginPath, qryObj);
  }

  switchPartner = (partnerId) => {
    this.props.switchPartner({ partnerId });
  }

  onClickMenu = (e) => {
    switch (e.key) {
      case ActionType.LOGOUT:
        this.logout();
        break;
      default:
        this.switchPartner(e.key);
        break;
    }
  }

  onNavigation = (key) => {
    const isClient = Engine.getItem('isClient');
    if (isClient && (key.indexOf('/market') > -1)) {
      const token = Engine.getItem('token');
      window.parent.postMessage({ token, key }, '*');
      return;
    }
    const domSider = document.querySelector('.bzy-sider');
    (domSider.style || {}).display = 'unset';
    this.$replace(key);
    this.props.onUpdateSubMenus(key);
  }

  renderTabs = (menus, pathname) => {
    const isClient = Engine.getItem('isClient');
    if (isClient) {
      menus.splice(2, 1, { label: '掘金工具', key: '/market-practice' }, { label: '我的权益', key: '/market-usages' });
    }
    const { partnerType } = Sessions.getPartner();
    if (partnerType !== 'playground') return null;

    return (
      <Tabs
        onTabClick={(e) => {
          const path = _.trimEnd(e, '-v2');
          if (['/knowledge', '/workflow'].includes(path) &&
            _.startsWith(window.location.pathname, path) &&
            !_.isEmpty(window.location.pathname.replace(path, ''))
          ) {
            if (window.location.pathname.includes('/workflow/')) {
              this.onNavigation('/workflow');
            } else {
              this.onNavigation(e);
            }
          }
        }}
        size="large"
        style={{ color: '#fff', fontSize: 32 }}
        items={menus}
        tabBarGutter={100}
        onChange={this.onNavigation}
        activeKey={pathname}
      />
    );
  }

  render = () => {
    const { nickname, avatar, partnerId } = Sessions.getProfile();
    const { menuSetting, pathname, logoname } = this.props;
    const menus = (menuSetting || []).map((x) => { return { label: x.name, key: x.routeKey }; });
    const currentPartner = _.find(this.props.partners || [], (x) => { return +x.partnerId === +partnerId; });

    const menu = (
      <Menu className="global-header-menu" onClick={this.onClickMenu}>
        <Menu.Item key={ActionType.LOGOUT}>
          <div className="drop-menu">
            <LogoutOutlined />
            <span>登出</span>
          </div>
        </Menu.Item>
        {
          _.map(this.props.partners || [], (x) => {
            return (
              <Menu.Item key={x.partnerId} disabled={+x.partnerId === +partnerId}>
                <div className="drop-menu">
                  <UserSwitchOutlined />
                  <span>{x.name}</span>
                </div>
              </Menu.Item>
            );
          })
        }
      </Menu>
    );

    return (
      <Header className="global-header" style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div
          className="logo-container"
          style={{
            width: logoname ? 'auto' : Consts.sideMenuWidth,
            textAlign: 'center',
            padding: logoname ? '0 20px' : 0,
          }}
        >
          {
            logoname ? (
              <div style={{ fontSize: 20, lineHeight: '64px', color: '#fff' }}>
                {logoname}
              </div>
            ) : (
              <img alt="" src={this.state.logoUrl} />
            )
          }
        </div>
        {(!logoname && !_.isUndefined(pathname)) && this.renderTabs(menus, pathname)}
        <Dropdown overlay={menu} trigger={['click']}>
          <div className="user" title={nickname}>
            <img src={avatar || '/static/favicon.ico'} alt="" />
            <span className="name">{currentPartner?.name || nickname}</span>
            <SettingFilled style={{ marginLeft: 30, color: '#fff', fontSize: 20 }} />
          </div>
        </Dropdown>
      </Header>
    );
  }
}
