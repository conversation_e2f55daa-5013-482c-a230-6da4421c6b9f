@import 'app.less';

.global-header {
  position: relative;
  padding: 0 12px 0 0;
  background: #002741;
  box-shadow: 0 1px 4px @black-b08;

  .ant-tabs-nav::before {
    border-bottom: 1px solid #002741 !important;
  }

  .trigger {
    padding: 0 24px;
    font-size: 18px;
    line-height: 64px;
    cursor: pointer;
    transition: color .3s;
  }

  .trigger:hover {
    color: @blue-2;
  }

  .user {
    display: flex;
    float: right;
    align-items: center;
    justify-content: flex-end;
    max-width: 220px;
    height: 64px;
    padding: 19px 12px;

    &:hover {
      color: @blue-2;
      cursor: pointer;
    }

    img {
      width: 24px;
      height: 24px;
      border-radius: 12px;
    }

    .name {
      overflow: hidden;
      margin-left: 10px;
      text-align: center;
      font-size: 14px;
      line-height: 21px;
      color: @white;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.global-header-menu {
  .drop-menu {
    width: 130px;

    span {
      margin-left: 10px;
    }
  }
}
