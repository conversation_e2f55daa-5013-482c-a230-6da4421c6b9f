export default [
  {
    name: 'AI 对话',
    icon: 'icon-talk',
    routeKey: '/playground',
    subMenus: [{
      name: '对话',
      icon: 'icon-talk',
      routeKey: '/playground',
    }, {
      name: '分组',
      icon: 'icon-group',
      routeKey: '/group',
    }],
  },
  {
    name: '智能体',
    icon: 'icon-workflow',
    routeKey: '/workflow-v2',
    subMenus: [{
      name: '工作流',
      icon: 'icon-workflow',
      routeKey: '/workflow-v2',
    }, {
      name: '函数插件',
      icon: 'icon-group',
      routeKey: '/workflow/functions',
    }, {
      name: '内容转换',
      icon: 'icon-content-transform',
      routeKey: '/workflow/transforms',
    }, {
      name: '文档知识库',
      icon: 'icon-knowledge',
      routeKey: '/knowledge',
    }, {
      name: 'Prompts',
      icon: 'icon-template',
      routeKey: '/prompts',
    }, {
      icon: 'icon-client-white',
      name: 'Api<PERSON><PERSON>',
      routeKey: '/market-apikey',
    }],
  },
  // {
  //   name: '知识库',
  //   icon: 'icon-knowledge',
  //   routeKey: '/knowledge',
  //   subMenus: [{
  //     name: '文档知识库',
  //     icon: 'icon-knowledge',
  //     routeKey: '/knowledge',
  //   }],
  //   // {
  //   //   name: '用户反馈',
  //   //   icon: 'icon-feedback',
  //   //   routeKey: '/feedback',
  //   // }
  // },
  // {
  //   name: 'Prompts',
  //   icon: 'icon-template',
  //   routeKey: '/prompts',
  // },
];
