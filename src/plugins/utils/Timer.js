export default class Timer {
  static setInterval = (func, interval, timeoutRef = {}) => {
    let continueInterval = true;

    const intervalFunc = () => {
      if (continueInterval) {
        if (func.length > 0) {
          const next = () => { setTimeout(intervalFunc, interval); };
          func(next);
        } else {
          func();
          setTimeout(intervalFunc, interval);
        }
      }
    };

    // eslint-disable-next-line
    timeoutRef.id = setTimeout(intervalFunc, interval);

    const clearHandle = () => {
      clearTimeout(timeoutRef.id);
      continueInterval = false;
    };

    return clearHandle;
  }

  static setIntervalAsync = (func, interval) => {
    let continueInterval = true;

    const timeoutRef = {};

    const intervalFunc = async () => {
      if (continueInterval) {
        const start = Date.now();
        await func();
        const elapsed = Date.now() - start;
        timeoutRef.id = setTimeout(intervalFunc, interval > elapsed ? interval - elapsed : 1);
      }
    };

    // eslint-disable-next-line
    timeoutRef.id = setTimeout(intervalFunc, interval);

    const clearHandle = () => {
      clearTimeout(timeoutRef.id);
      continueInterval = false;
    };

    return clearHandle;
  }

  static clearInterval = (clearHandle) => {
    if (clearHandle) {
      clearHandle();
    }
  }
}
