/* eslint-disable react/prop-types, react/react-in-jsx-scope */

import _ from 'lodash';

export default class Editor {
  static emotionsTitleNameMap = null
  static emotionsNameTitleMap = null
  static emotionOptions = null
  static emotionsRegExp = /(?:mo-)?(\[.+?\]|😄|😷|😂|😝|😳|😱|😔|😒|👻|🙏|💪|🎉)/g

  static getLinks = (value = '') => {
    const links = [];
    if (_.isEmpty(value)) { return links; }

    while (!_.isEmpty(Editor.getLinkHTML(value))) {
      const { href, title } = Editor.getLinkHTML(value) || {};
      links.push({ href, title });
      value = value.replace(`<a href="${href}">${title}</a>`, `[${title}#${href}]`); // eslint-disable-line
    }

    return links;
  }

  static getLinkHTML = (value) => {
    const hrefRegex = /<a href="(.*?)">/gm;
    const hrefs = hrefRegex.exec(value);
    const textRegex = /">(.*?)<\/a>/gm;
    const texts = textRegex.exec(value);

    if (!_.isEmpty(hrefs) && !_.isEmpty(texts)) {
      return { href: hrefs[1], title: texts[1] };
    }
    return {};
  }

  /**
   * @text 文本
   * @keys 替换的变量 eg.[{ key: '#user#', value: '昵称', icon: 'user' }]
   * @hasForm 是否格式化 表单
   * @hasLink 是否格式化 超链
   */
  static formatTextToHtml = (text = '', keys = [], hasLink = false) => {
    if (_.isEmpty(text)) return '<p></p>';
    const textLine = text.split('\n');
    let value = '';
    textLine.forEach((item) => { value += `<p>${item}</p>`; });

    _.map(keys, (item) => {
      const html = `<span data-value="${item.key}" class="user-item">${item.value}</span>`;
      const pattern = new RegExp(item.key, 'g');
      value = _.replace(value, pattern, html);
    });

    if (hasLink) {
      const links = Editor.getLinks(text);
      links.forEach(({ href, title }) => {
        const linkHtml = `<span data-link="${href}" data-title="${title}" class="user-item form">${title}</span>`;
        value = value.replace(`<a href="${href}">${title}</a>`, linkHtml);
      });
    }

    return value;
  }

  /**
   * @text 文本
   * @keys 替换的变量 eg.[{ key: '#user#', value: '昵称', icon: 'user' }]
   */
  static formatTextToPreview = (text = '', keys = []) => {
    if (_.isEmpty(text)) return '';
    const textLine = text.split('\n');
    let value = '';
    textLine.forEach((item) => { value += `${item}`; });

    _.map(keys, (item) => {
      const html = item.value;
      const pattern = new RegExp(item.key, 'g');
      value = _.replace(value, pattern, html);
    });
    return value;
  }

  static formatJsonToText = (datas, types) => {
    const { blocks, entityMap } = datas;
    let value = '';
    blocks.forEach((item) => {
      value += `${item?.text}\n`;
    });

    const values = _.uniq(_.map(_.map(entityMap, 'data'), 'value'));
    _.forEach(values, (key) => {
      const item = types.find((t) => { return t.value === key; });
      value = _.replace(value, new RegExp(item?.name, 'g'), key);
    });

    return _.trimEnd(value, '\n');
  }

  static formatHtmlToText = (text = '') => {
    const regex = /<p>(.*?)<\/p>/gm;
    const texts = [];
    let m;
    while ((m = regex.exec(text)) !== null) { // eslint-disable-line
      if (m.index === regex.lastIndex) { regex.lastIndex++; }
      m.forEach((match, subIndex) => {
        if (subIndex) {
          texts.push(match || '');
        }
      });
    }

    let value = '';
    let result = texts;
    if (_.isEmpty(_.last(texts))) { result = _.dropRight(texts); }
    _.map(result, (item, index) => {
      value += (result.length === index + 1) ? item : `${item}\n`;
    });

    const pattern = new RegExp('<br/>', 'g');
    return _.replace(value, pattern, '');
  }

  static convertString = (str) => {
    const textarea = document.createElement('textarea');
    textarea.innerHTML = str;
    return textarea.value;
  }
}
