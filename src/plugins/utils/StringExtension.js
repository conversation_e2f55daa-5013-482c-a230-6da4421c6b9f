import Consts from '~/consts';
import md5 from 'blueimp-md5';
import _ from 'lodash';
import qs from 'qs';

export default class StringExtension {
  static calculateCharacterLength = (str = '') => {
    return str.split('').reduce((accumulator, codePoint) => {
      return accumulator + (Consts.REGEX.singleCharacter.test(codePoint) ? 1 : 2);
    }, 0);
  }

  static formatCharacterString = (value, length) => {
    let quantity = 0;
    return Array.from(value).reduce((accumulator, codePoint) => {
      codePoint.split('').forEach((charCode) => {
        quantity += (Consts.REGEX.singleCharacter.test(charCode) ? 1 : 2);
      });
      return quantity > length ? accumulator : accumulator + codePoint;
    }, '');
  }

  static truncateString = (text, maxLength) => {
    if (text.length <= maxLength) {
      return text;
    }

    const truncatedStr = text.slice(0, maxLength); // 截取指定长度的子字符串
    return `${truncatedStr}...`; // 添加省略号
  }

  static formatBoolValue = (value = '') => {
    return _.indexOf(Consts.BOOL_ARRAY, value) >= 1;
  }

  static fuzzyQuery = (list, key, keyword) => {
    const arr = [];
    const reg = new RegExp(['', ...keyword, ''].join('.*'));
    for (let i = 0; i < list.length; i++) {
      if (reg.test(list[i][key])) {
        arr.push(list[i]);
      }
    }
    return arr;
  }

  static camelToSnakeObj = (obj, emptyObj = null) => {
    const newObj = {};
    for (const key in obj) {
      if (Array.isArray(obj[key])) {
        const newArr = [];
        for (let i = 0; i < obj[key].length; i++) {
          if (typeof obj[key][i] === 'object') {
            newArr.push(this.camelToSnakeObj(obj[key][i], emptyObj));
          } else {
            newArr.push(obj[key][i]);
          }
        }
        newObj[this.camelToSnake(key)] = newArr;
      } else if (typeof obj[key] === 'object') {
        newObj[this.camelToSnake(key)] = this.camelToSnakeObj(obj[key], emptyObj);
      } else {
        newObj[this.camelToSnake(key)] = obj[key];
      }
    }
    return _.isEmpty(newObj) ? emptyObj : newObj;
  }

  static snakeToCamelObj = (obj) => {
    const newObj = {};
    for (const key in obj) {
      if (_.isNull(obj[key])) {
        newObj[this.snakeToCamel(key)] = undefined;
      } else if (Array.isArray(obj[key])) {
        const newArr = [];
        for (let i = 0; i < obj[key].length; i++) {
          if (typeof obj[key][i] === 'object') {
            newArr.push(this.snakeToCamelObj(obj[key][i]));
          } else {
            newArr.push(obj[key][i]);
          }
        }
        newObj[this.snakeToCamel(key)] = newArr;
      } else if (typeof obj[key] === 'object') {
        newObj[this.snakeToCamel(key)] = this.snakeToCamelObj(obj[key]);
      } else {
        newObj[this.snakeToCamel(key)] = obj[key];
      }
    }
    return newObj;
  }

  static camelToSnake = (str) => {
    const newStr = str.replace(/[A-Z]/g, (match) => {
      return `_${match.toLowerCase()}`;
    });
    return newStr;
  }

  static snakeToCamel = (str) => {
    const newStr = str.replace(/_([a-z])/g, (match, group) => {
      return group.toUpperCase();
    });
    return newStr;
  }

  static randomString = (n) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const array = _.sampleSize(chars, n);
    const result = array.join('');
    return result;
  }

  static getSign = (params = {}) => {
    const sortedKeys = Object.keys(params).sort();
    const sortedObj = {};
    for (const key of sortedKeys) {
      sortedObj[key] = params[key];
    }

    return md5(qs.stringify(sortedObj));
  }
}
