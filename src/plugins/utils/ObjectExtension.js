/* eslint-disable guard-for-in */
import _ from 'lodash';

export default class ObjectExtension {
  static verify = (...params) => {
    const index = params.findIndex((item) => { return _.isUndefined(item); });
    return index === -1;
  }

  static selectSearcFilter = (input, option) => {
    return (option.props.key.toLowerCase().indexOf(input.toLowerCase()) >= 0) ||
      (option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0);
  };

  static objToArray = (obj) => {
    const array = [];
    for (const key in obj) {
      array.push({ key, value: obj[key] });
    }
    return array;
  }
}
