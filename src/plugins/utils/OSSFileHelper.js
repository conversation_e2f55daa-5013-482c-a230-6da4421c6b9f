import { AliyunHelper, <PERSON> } from '~/engine';

import Platform from '../Platform';

const BASE_URL = 'https://video-clip.oss-cn-shanghai.aliyuncs.com';
export default class OSSFileHelper {
  static fetchData = async (path, fileName) => {
    try {
      const { openId } = Sessions.getPartner();
      const keys = `${Platform.isProd() ? 'prod' : 'stg'}-${fileName}_${openId}`;
      // 添加时间戳参数破除OSS和浏览器缓存，确保获取最新数据
      const timestamp = Date.now();
      const resp = await fetch(`${BASE_URL}/fe_data/${path}/${keys}.json?t=${timestamp}`);
      const result = await resp.json();
      return result;
    } catch (error) {
      return '';
    }
  };

  static updateData = async (content, path, fileName) => {
    const { openId } = Sessions.getPartner();
    const keys = `${Platform.isProd() ? 'prod' : 'stg'}-${fileName}_${openId}`;
    const blob = new Blob([`${content}`], { type: 'text/plain' });
    await AliyunHelper.clipsUploadImage(blob, () => { }, {
      filePath: `fe_data/${path}`,
      fileName: `${keys}`,
      fileType: '.json',
    });
  };
}
