/* eslint-disable no-undef */
import Engine from '~/engine';
import EventEmitter from 'eventemitter3';
import _ from 'lodash';
import qs from 'qs';

export default class Platform {
  static _eventEmitter = new EventEmitter()
  static Event = {
    SIDE_MENU_COLLAPSED: 'SIDE_MENU_COLLAPSED',
    ENTER_EDITOR_RE_RENDERED: 'ENTER_EDITOR_RE_RENDERED', // 重新渲染WechatEditor
    MONTAGE_CHANGE_MODULE: 'MONTAGE_CHANGE_MODULE',
    MONTAGE_CHANGE_SOURCE: 'MON<PERSON><PERSON>_CHANGE_SOURCE',
    OPEN_PROMPT_DRAWER: 'OPEN_PROMPT_DRAWER',
    FETCH_PROMPT: 'FETCH_PROMPT',
    RELOAD_FUNC_TOOLS: 'RELOAD_FUNC_TOOLS',
  }
  static _mediaQueryListHandles = [];

  static setPageTitle = (title) => {
    document.title = title;
  }

  static clientWidth = () => {
    return document.body.clientWidth;
  }

  static isMobile = () => {
    return /Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent);
  }

  static isMai = () => {
    return window.location.hostname === 'ai.maiscrm.com';
  }

  static isWebkit = () => {
    return /webkit/i.test(navigator.userAgent);
  }

  static isProd = () => {
    if (Platform.isMai()) {
      return true;
    }

    const isProd = window.location.href.indexOf('https://chat') > -1;
    return isProd;
  }

  static sseUrl = (path, query = {}) => {
    const domain = Engine.getApiEndpoint();
    // const qryObj = { access_token: Sessions.getToken(), ...query };
    return `${domain}${path}?${qs.stringify(query)}`;
  }

  static emit = (eventName, ...args) => {
    Platform._eventEmitter.emit(eventName, ...args);
  }

  static addEventListener = (eventName, listener) => {
    Platform._eventEmitter.addListener(eventName, listener);
  }

  static removeEventListener = (eventName, listener) => {
    Platform._eventEmitter.removeListener(eventName, listener);
  }

  static addCollapsedListener = (listener) => {
    Platform.addEventListener(Platform.Event.SIDE_MENU_COLLAPSED, listener);
  }

  static removeCollapsedListener = (listener) => {
    Platform.removeEventListener(Platform.Event.SIDE_MENU_COLLAPSED, listener);
  }

  static addWindowMatchMediaListeners = (mediaQueryStrings, listener) => {
    const mediaQueryLists = [];
    _.forEach(mediaQueryStrings, (mediaQueryString) => {
      const mediaQueryList = window.matchMedia(mediaQueryString);
      mediaQueryList.addListener(listener);
      mediaQueryLists.push(mediaQueryList);
    });

    Platform._mediaQueryListHandles.push({
      listener,
      mediaQueryLists,
    });
  }

  static removeWindowMatchMediaListeners = (listener) => {
    const handles = _.remove(
      Platform._mediaQueryListHandles,
      (mediaQueryListHandle) => {
        return mediaQueryListHandle.listener === listener;
      },
    );

    _.forEach(handles, (handle) => {
      _.forEach(handle.mediaQueryLists, (mediaQueryList) => {
        mediaQueryList.removeListener(listener);
      });
    });
  }
}
