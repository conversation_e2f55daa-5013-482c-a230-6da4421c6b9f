<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="format-detection" content="telephone=no">
  <meta name="robots" content="noindex">
  <title>
    <%= htmlWebpackPlugin.options.title %>
  </title>
  <link rel="icon" href="" sizes="48x48" type="image/ico">
  <link rel="stylesheet" type="text/css" href="//at.alicdn.com/t/font_522009_474j3funfqiwwmi.css">
  <script src="//video-clip.oss-cn-shanghai.aliyuncs.com/fe_data/pinyin-pro.min.js"></script>
  <script>
    var domain = window.location.hostname;
    var favicon = document.querySelector("link[rel='icon']");
    var title = document.querySelector("title");
    if (domain === "ai.maiscrm.com") {
      favicon.href = "https://www.maiscrm.com/favicon.ico";
      title.innerHTML = 'Mai·AI'
    } else {
      favicon.href = "/static/favicon.ico";
      title.innerHTML = 'BZY·AI'
    }
  </script>
</head>

<body>
  <div id="root"></div>
  <script type="text/javascript"
    src="/static/scripts/vendors-dll/<%= htmlWebpackPlugin.options.vendorJsName %>"></script>
</body>

</html>
