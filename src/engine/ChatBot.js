import rest from 'axios';
import { v4 as uuidV4 } from 'uuid';

export default class ChatBot {
  // ---------- V1 --------------
  static fetchChatBotTools = async (params) => {
    return rest.get('/partner/clips/chat-bot-tools', { params });
  }

  static getChatBotTool = async (id) => {
    return rest.get(`/partner/clips/chat-bot-tools/${id}`);
  }

  static upsertChatBotTool = async (params) => {
    return rest.post('/partner/clips/chat-bot-tools', params);
  }

  static delChatBotTool = async (id) => {
    return rest.delete(`/partner/clips/chat-bot-tools/${id}`);
  }

  static fetchChatBots = async (params) => {
    return rest.get('/partner/clips/partner-chat-bots', { params });
  }

  static getChatBot = async (id) => {
    return rest.get(`/partner/clips/partner-chat-bots/${id}`);
  }

  static upsertChatBot = async (params) => {
    return rest.post('/partner/clips/partner-chat-bots', params);
  }

  static delChatBot = async (id) => {
    return rest.delete(`/partner/clips/partner-chat-bots/${id}`);
  }

  static getChatbotReply = async (params) => {
    return rest.post('/partner/clips/get-chatbot-reply', params);
  }

  static getChatbotReplyV2 = async (params) => {
    return rest.post('/partner/clips/chatbot/reply', params);
  }

  static getChatbotReplyResult = async (params) => {
    return rest.get('/partner/clips/chatbot/reply-task-result', { params });
  }

  static importKnowledge = (params) => {
    return rest.post('/partner/clips/knowledge/import', params);
  }

  // ---------- V2 --------------

  static fetchKnowledgeLibraries = async (params) => {
    return rest.post('/v2/chatbot/knowledge-libraries/search', params);
  }

  static searchKnowledgeLibraries = async (params) => {
    return rest.post('/v2/chatbot/knowledge-libraries/search', params);
  }

  static upsertKnowledgeLibrary = async (params) => {
    return rest.post('/v2/chatbot/knowledge-libraries', params);
  }

  static getKnowledgeLibrary = async (id) => {
    return rest.get(`/v2/chatbot/knowledge-libraries/${id}`);
  }

  static updateKnowledgeLibrary = async (params) => {
    return rest.put(`/v2/chatbot/knowledge-libraries/${params?.id}`, params);
  }

  static delKnowledgeLibrary = async (id) => {
    return rest.delete(`/v2/chatbot/knowledge-libraries/${id}`);
  }

  static addKnowledge = (params) => {
    return rest.post('/v2/chatbot/knowledges', params);
  }

  static updateKnowledge = (params) => {
    return rest.put(`/v2/chatbot/knowledges/${params.id}`, params);
  }

  static importKnowledge = (params) => {
    return rest.post('/v2/chatbot/knowledge/import', params);
  }

  static fetchKnowledges = (params) => {
    return rest.get('/v2/chatbot/knowledges', { params });
  }

  static searchKnowledges = (params) => {
    return rest.post('/v2/chatbot/knowledges/search', params);
  }

  static searchKnowledgeEmbedding = (params) => {
    return rest.get('/v2/chatbot/knowledges/embeddings/search', { params });
  }

  static deleteKnowledge = (id) => {
    return rest.delete(`/v2/chatbot/knowledges/${id}`);
  }

  static fetchKnowledgeFiles = (params) => {
    return rest.post('/v2/chatbot/knowledge-files/search', params);
  }

  static delKnowledgeFile = (id) => {
    return rest.delete(`/v2/chatbot/knowledge-files/${id}`);
  }

  static addKnowledgeFiles = (params) => {
    return rest.post('/v2/chatbot/knowledge-file/import', params);
  }

  static addKnowledgeLibraryFunc = (params) => {
    return rest.post('/v2/chatbot/knowledge-library-funcs', params);
  }

  static fetchKnowledgeLibraryFuncs = (params) => {
    return rest.post('/v2/chatbot/knowledge-library-funcs/search', params);
  }

  static updateKnowledgeLibraryFunc = (params) => {
    return rest.put(`/v2/chatbot/knowledge-library-funcs/${params.id}`, params);
  }

  static delKnowledgeLibraryFunc = (id) => {
    return rest.delete(`/v2/chatbot/knowledge-library-funcs/${id}`);
  }

  static fetchChatbotPromptTemplates = async (params) => {
    return rest.get('/v2/chatbot/prompt-templates', { params });
  }

  static getChatbotPromptTemplate = async (id) => {
    return rest.get(`/v2/chatbot/prompt-templates/${id}`);
  }

  static addChatbotPromptTemplate = async (params) => {
    return rest.post('/v2/chatbot/prompt-templates', params);
  }

  static updateChatbotPromptTemplate = async (params) => {
    return rest.put(`/v2/chatbot/prompt-templates/${params.id}`, params);
  }

  static delChatbotPromptTemplate = async (id) => {
    return rest.delete(`/v2/chatbot/prompt-templates/${id}`);
  }

  static upsertChatbotSessionGroup = async (params) => {
    return rest.post('/v2/chatbot/session-groups', params);
  }

  static fetchChatbotSessionGroups = async (params) => {
    return rest.get('/v2/chatbot/session-groups', { params });
  }

  static updateChatbotSessionGroup = async (params) => {
    return rest.put(`/v2/chatbot/session-groups/${params?.id}`, params);
  }

  static delChatbotSessionGroup = async (id) => {
    return rest.delete(`/v2/chatbot/session-groups/${id}`);
  }

  static fetchChatbotAssistants = async (params) => {
    return rest.get('/v2/chatbot/assistants', { params });
  }

  static createChatbotAssistant = async (params) => {
    return rest.post('/v2/chatbot/assistants', params);
  }

  static updateChatbotAssistant = async (params) => {
    return rest.put('/v2/chatbot/assistants', params);
  }

  static delChatbotAssistant = async (id) => {
    return rest.delete(`/v2/chatbot/assistants/${id}`);
  }

  static fetchChatbotSessions = async (params) => {
    return rest.get('/v2/chatbot/sessions', { params });
  }

  static createChatbotSession = async (params) => {
    return rest.post('/v2/chatbot/sessions', params);
  }

  static copyChatbotSession = async (params) => {
    return rest.get(`/v2/chatbot/sessions/copy/${params?.sessionId}`, { params });
  }

  static updateChatbotSession = async (params) => {
    return rest.put(`/v2/chatbot/sessions/${params.id}`, params);
  }

  static delChatbotSession = async (id) => {
    return rest.delete(`/v2/chatbot/sessions/${id}`);
  }

  static fetchChatbotSessionLogs = async (params) => {
    return rest.get('/v2/chatbot/session-logs', { params });
  }

  static fetchChatbotLLMSettings = async (params) => {
    return rest.get('/v2/chatbot/llm-settings', { params });
  }

  static updateChatbotLLMSetting = async (params) => {
    return rest.put('/v2/chatbot/llm-settings', params);
  }

  static delChatbotLLMSetting = async (settingId) => {
    return rest.delete(`/v2/chatbot/llm-settings/${settingId}`);
  }

  static selectChatbotLLMSetting = async (params) => {
    return rest.post('/v2/chatbot/sessions/select-llm', params);
  }

  static fetchChatbotConversations = async (params) => {
    return rest.get('/v2/chatbot/conversations', { params });
  }

  static updateChatbotConversations = async (params) => {
    return rest.put('/v2/chatbot/conversations', params);
  }

  static chatbotReply = async (params, config = {}) => {
    const path = params?.sseMode === 1 ? '/v2/chatbot/chat?sse_mode=1' : '/v2/chatbot/chat';
    return rest.post(path, params, { ...config, ignoreLoading: true });
  }

  static fetchChatbotWeworkBots = async (params) => {
    return rest.post('/v2/chatbot/chatbot_wework_bots/search', params);
  }

  static getChatbotWeworkBot = async (id) => {
    return rest.get(`/v2/chatbot/chatbot_wework_bots/${id}`);
  }

  static updateChatbotWeworkBot = async (params) => {
    return rest.put(`/v2/chatbot/chatbot_wework_bots/${params?.id}`, params);
  }

  static deleteChatbotWeworkBot = async (id) => {
    return rest.delete(`/v2/chatbot/chatbot_wework_bots/${id}`);
  }

  static createChatbotWeworkBot = async (params) => {
    return rest.post('/v2/chatbot/chatbot_wework_bots', params);
  }

  static fetchChatbotWorkflowGroups = async (params) => {
    return rest.post('/v2/chatbot/workflow-groups/search', params);
  }

  static addWorkflowGroup = async (params) => {
    return rest.post('/v2/chatbot/workflow-groups', params);
  }

  static updateWorkflowGroup = async (params) => {
    return rest.put(`/v2/chatbot/workflow-groups/${params.id}`, params);
  }

  static delWorkflowGroup = async (id) => {
    return rest.delete(`/v2/chatbot/workflow-groups/${id}`);
  }

  static fetchChatbotWorkflows = async (params) => {
    params.version = 'v1'; // eslint-disable-line
    return rest.get('/v2/chatbot/workflows', { params });
  }

  static fetchSharedWorkflows = async (params) => {
    return rest.get('/v2/chatbot/shared-workflows', { params });
  }

  static createChatbotWorkflow = async (params) => {
    return rest.post('/v2/chatbot/workflows', params);
  }

  static getChatbotWorkflow = async (uuid) => {
    return rest.get(`/v2/chatbot/workflows/${uuid}`);
  }

  static updateChatbotWorkflow = async (params) => {
    return rest.put(`/v2/chatbot/workflows/${params.uuid}`, params);
  }

  static delChatbotWorkflow = async (id) => {
    return rest.delete(`/v2/chatbot/workflows/${id}`);
  }

  static publishChatbotWorkflow = async (id) => {
    return rest.post(`/v2/chatbot/workflows/${id}/publish`, {});
  }

  static runWorkflow = async (params, config = {}) => {
    let path = `/v2/chatbot/workflows/run/${params?.id}`;
    if (params?.sseMode === 1) {
      path += '?sse_mode=1';
    }
    return rest.post(path, params, { ...config, ignoreLoading: true });
  }

  static cancelWorkflow = async (flowId, jobId) => {
    return rest.get(`/v2/chatbot/workflows/cancel-job/${flowId}/${jobId}`);
  }

  static importWorkflow = (params) => {
    return rest.post('/v2/chatbot/workflow/import', params);
  }

  static exportWorkflow = (id) => {
    return rest.post(`/v2/chatbot/workflow/export/${id}`);
  }

  static updateWorkflowInputSpec = (params) => {
    return rest.put(`/v2/chatbot/workflows/input-spec/${params?.uuid}`, params);
  }

  static fetchWorkflowLogs = (params) => {
    return rest.post('/v2/chatbot/workflow-exec-logs/search', params);
  }

  static fetchWorkflowConversations = (params) => {
    return rest.post('/v2/chatbot/session-conversations/search', params);
  }

  static fetchChatbotSessionTopics = async (params) => {
    return rest.post('/v2/chatbot/session-topics/search', params);
  }

  static createChatbotSessionTopic = async (params) => {
    return rest.post('/v2/chatbot/session-topics', params);
  }

  static updateChatbotSessionTopic = async (params) => {
    return rest.put(`/v2/chatbot/session-topics/${params.id}`, params);
  }

  static deleteChatbotSessionTopic = (id) => {
    return rest.delete(`/v2/chatbot/session-topics/${id}`);
  }

  static fetchChatbotSessionConversations = (params) => {
    return rest.post('/v2/chatbot/session-conversations/search', { ...params, lastId: 0 });
  }

  static createChatbotSessionConversation = (params) => {
    return rest.post('/v2/chatbot/session-conversations', params);
  }

  static updateChatbotSessionConversation = (params) => {
    return rest.put(`/v2/chatbot/session-conversations/${params.id}`, params);
  }

  static deleteChatbotSessionConversation = (id) => {
    return rest.delete(`/v2/chatbot/session-conversations/${id}`);
  }

  static clearChatbotSessionConversation = (topicId) => {
    return rest.delete(`/v2/chatbot/session-conversations/clear/${topicId}`);
  }

  static fetchChatbotWorkflowJobs = (params) => {
    return rest.get(`/v2/chatbot/workflows/${params?.uuid}/run/jobs`, { params });
  }

  static fetchChatbotWorkflowJob = (id) => {
    return rest.get(`/v2/chatbot/workflow-jobs/${id}`);
  }

  static addChatbotWorkflowJob = (params) => {
    return rest.post('/v2/chatbot/workflow-jobs', { ...params, runId: uuidV4() });
  }

  static updateChatbotWorkflowJob = (params) => {
    return rest.put(`/v2/chatbot/workflow-jobs/${params?.id}`, params);
  }

  static delChatbotWorkflowJob = (id) => {
    return rest.delete(`/v2/chatbot/workflow-jobs/${id}`);
  }

  static fetchChatbotFeedbacks = (params) => {
    return rest.post('/v2/zone/feedbacks/search', params);
  }

  static fetchChatbotFeedback = (id) => {
    return rest.get(`/v2/zone/feedbacks/${id}`);
  }

  static importChatbotFeedback = (params) => {
    return rest.post('/v2/zone/feedbacks/import', params);
  }

  static addChatbotFeedback = (params) => {
    return rest.post('/v2/zone/feedbacks', params);
  }

  static updateChatbotFeedback = (params) => {
    return rest.put(`/v2/zone/feedbacks/${params?.id}`, params);
  }

  static delChatbotFeedback = (id) => {
    return rest.delete(`/v2/zone/feedbacks/${id}`);
  }

  static searchFeedbackEmbedding = (params) => {
    return rest.get('/v2/zone/feedbacks/embeddings/search', { params });
  }

  static fetchChatbotCharacterTemplates = (params) => {
    return rest.get('/v2/chatbot/character-templates', { params });
  }

  static createWorkflowGroup = (params) => {
    return rest.post(`/v2/chatbot/workflows/${params?.flowId}/link-chatroom`, params);
  }

  static fetchWorkflowGroups = (params) => {
    return rest.get(`/v2/chatbot/workflows/${params?.flowId}/linked-chatrooms`, { params });
  }

  static fetchWorkflowSLSLogs = (params) => {
    return rest.post(`/v2/chatbot/workflows/${params?.flowId}/logs`, params);
  }

  static fetchWorkflowFuncs = (params) => {
    return rest.post('/v2/chatbot/workflow-funcs/search', params);
  }

  static getFuncByFlow = (flowId) => {
    return rest.get(`/v2/chatbotworkflow-funcs/${flowId}`);
  }

  static addWorkflowFunc = (params) => {
    return rest.post('/v2/chatbot/workflow-funcs', params);
  }

  static updateWorkflowFunc = (params) => {
    return rest.put(`/v2/chatbot/workflow-funcs/${params.id}`, params);
  }

  static delWorkflowFunc = (id) => {
    return rest.delete(`/v2/chatbot/workflow-funcs/${id}`);
  }

  static fetchOpenApps = (params) => {
    return rest.get('/v2/chatbot/open-apps', { params });
  }

  static addOpenApp = (params) => {
    return rest.post('/v2/chatbot/open-apps', params);
  }

  static updateOpenApp = (params) => {
    return rest.put(`/v2/chatbot/open-apps/${params.id}`, params);
  }

  static fetchTables = () => {
    return rest.get('/v2/chatbot/workflow-tables');
  }

  static directChatCompletion = (params, config = {}) => {
    return rest.post('/v2/chatbot/direct/chat/completion?sse_mode=1', params, { ...config, ignoreLoading: true });
  }

  static fetchOpenApiFuncs = (params) => {
    return rest.post('/v2/chatbot/openapi-funcs/search', params);
  }

  static importOpenApiFunc = (params) => {
    return rest.post('/v2/chatbot/openapi-funcs/import', params);
  }

  static getOpenApiFunc = (id) => {
    return rest.get(`/v2/chatbot/openapi-funcs/${id}`);
  }

  static updateOpenApiFunc = (params) => {
    return rest.put(`/v2/chatbot/openapi-funcs/${params.id}`, params);
  }

  static delOpenApiFunc = (id) => {
    return rest.delete(`/v2/chatbot/openapi-funcs/${id}`);
  }

  static fetchCozeFuncs = (params) => {
    return rest.get('/v2/chatbot/coze-funcs/availables', params);
  }

  static addCozeFunc = (params) => {
    return rest.post('/v2/chatbot/coze-funcs', params);
  }

  static updateCozeFunc = (params) => {
    return rest.put(`/v2/chatbot/coze-funcs/${params.id}`, params);
  }

  static delCozeFunc = (id) => {
    return rest.delete(`/v2/chatbot/coze-funcs/${id}`);
  }

  static fetchCourseFuncs = (params) => {
    return rest.post('/v2/chatbot/course-funcs/search', params);
  }

  static addCourseFunc = (params) => {
    return rest.post('/v2/chatbot/course-funcs', params);
  }

  static updateCourseFunc = (params) => {
    return rest.put(`/v2/chatbot/course-funcs/${params.id}`, params);
  }

  static delCourseFunc = (id) => {
    return rest.delete(`/v2/chatbot/course-funcs/${id}`);
  }

  static fetchWorkflowInnerParams = () => {
    return rest.get('/v2/chatbot/workflow-inner-params');
  }

  static fetchWorkflowSessions = (params) => {
    return rest.post(`/v2/chatbot/workflow-sessions/${params?.flowId}/${params?.nodeId}`, {});
  }

  static fetchFunctionToolsByGroup = () => {
    return rest.get('/v2/chatbot/function-tools-by-group');
  }

  static fetchAllVoiceTrain = () => {
    return rest.post('/v2/chatbot/voice-train/list');
  }

  static trainVoice = (params) => {
    return rest.post('/v2/chatbot/voice-train/train', params);
  }

  static addVoice = (params) => {
    return rest.post('/v2/chatbot/voice-train/add-existed', params);
  }
}
