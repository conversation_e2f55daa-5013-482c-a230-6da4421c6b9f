import moment from 'moment';

import * as Enums from '../Enums';

const { TimeType, TimeUnit } = Enums;

export default class Moment {
  static init = () => {
    moment.formatToUTC = function (date) {
      return moment(date).format('YYYY-MM-DDTHH:mm:ssZZ');
    };

    moment.getTimeDistance = function (type, days) {
      const now = new Date();
      const oneDay = 1000 * 60 * 60 * 24;

      if (type === TimeType.TODAY) {
        if (days) {
          const begin = moment().hour(0).minute(0).second(0)
            .millisecond(0)
            .subtract(days - 1, TimeType.DAY);

          const end = moment();

          return [begin, end];
        }

        now.setHours(0);
        now.setMinutes(0);
        now.setSeconds(0);
        return [moment(now), moment(now.getTime() + (oneDay - 1000))];
      }

      if (type === TimeType.WEEK) {
        let day = now.getDay();
        now.setHours(0);
        now.setMinutes(0);
        now.setSeconds(0);

        if (day === 0) {
          day = 6;
        } else {
          day -= 1;
        }

        const beginTime = now.getTime() - (day * oneDay);

        return [moment(beginTime), moment(beginTime + ((7 * oneDay) - 1000))];
      }

      if (type === TimeType.MONTH) {
        const year = now.getFullYear();
        const month = now.getMonth();
        const nextDate = moment(now).add(1, 'months');
        const nextYear = nextDate.year();
        const nextMonth = nextDate.month();

        return [
          moment(`${year}-${Moment.fixedZero(month + 1)}-01 00:00:00`),
          moment(moment(`${nextYear}-${Moment.fixedZero(nextMonth + 1)}-01 00:00:00`).valueOf() - 1000),
        ];
      }

      if (type === TimeType.YEAR) {
        const year = now.getFullYear();

        return [moment(`${year}-01-01 00:00:00`), moment(`${year}-12-31 23:59:59`)];
      }

      if (type === TimeType.LAST_DAYS) {
        const begin = moment().hour(0).minute(0).second(0)
          .millisecond(0)
          .subtract(days, TimeType.DAY);

        const end = moment().hour(0).minute(0).second(0)
          .millisecond(0)
          .subtract(TimeUnit.ONE, TimeType.SECONDS);

        return [begin, end];
      }

      return [];
    };
    moment.countDownTime = function (startTime, endTime, maxUnit) {
      let aUnitArr = ['year', 'month', 'day', 'hour', 'minute', 'second'];
      const iMaxIndex = aUnitArr.indexOf(maxUnit);
      let end = moment(endTime);
      const start = moment(startTime);
      const result = {};
      aUnitArr = aUnitArr.filter((item, index) => { return index >= iMaxIndex; });
      result[maxUnit] = end.diff(start, maxUnit);
      if (aUnitArr.length > 1) {
        aUnitArr.reduce((previous, current) => {
          end = end.subtract(result[previous], previous);
          result[current] = end.diff(start, current);
          return current;
        });
      }
      return result;
    };
  }

  static fixedZero = (val) => {
    return val * 1 < 10 ? `0${val}` : val;
  }
}
