import { v1 as uuid } from 'uuid';

import Accounts from '../Accounts';
import Base64 from './Base64';

const ImageZoom = {
  // 将图缩略成长度为 800(小于800返回原图)， 高度按比例处理。
  RESIZE_ACCORDING_TO_WIDTH: '?x-oss-process=image/resize,w_800',
  THUMB_IMAGE_CONFIG: '?x-oss-process=image/resize,m_fill,h_35,w_35',
  UPLOAD_THUMB_IMAGE_CONFIG: '?x-oss-process=image/resize,m_fill,h_102,w_104',
};

export default class AliyunHelper {
  static uploadMp3 = async (blob, options = {}) => {
    const result = options.signature || await AliyunHelper.getClipsSignature();
    const { accessKeyId, domain, uploadDomain, policy, signature } = result;

    return new Promise((resolve, reject) => {
      const fileName = options.fileName || AliyunHelper.genVoiceFileName(options.fileType);
      const uploadKey = options.uploadKey || AliyunHelper.genVoiceUploadKey(fileName);
      const formData = new FormData();
      formData.append('OSSAccessKeyId', accessKeyId);
      formData.append('policy', policy);
      formData.append('signature', signature);
      formData.append('success_action_status', 200);
      formData.append('key', uploadKey);
      formData.append('name', fileName);
      formData.append('file', blob);
      const xhr = new XMLHttpRequest();
      xhr.open('post', uploadDomain);

      xhr.addEventListener('load', (e) => {
        if (e.target.status !== 200) {
          reject(e);
          return;
        }
        if (e.target.status === 200) {
          const voiceUrl = `${domain}/${uploadKey}`;
          resolve(voiceUrl);
        }
      }, false);

      xhr.send(formData);
    });
  }

  static uploadImage = async (file, onProgress = () => { }, config = {}) => {
    const uuidString = uuid();
    const fileType = /\.[^.]+$/.exec(file.name)[0];
    const uploadKey = `${config.filePath || 'upload'}/${config.fileName || uuidString}${config.fileType || fileType}`;
    const result = await AliyunHelper.getSignature();
    if (config.domain) {
      result.domain = config.domain;
      result.uploadDomain = config.uploadDomain;
    }
    const { accessKeyId, domain, uploadDomain, policy, signature } = result;

    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('OSSAccessKeyId', accessKeyId);
      formData.append('policy', policy);
      formData.append('signature', signature);
      formData.append('key', uploadKey);
      formData.append('success_action_status', 200);
      formData.append('file', file);
      const xhr = new XMLHttpRequest();
      xhr.open('post', uploadDomain);
      xhr.upload.addEventListener('progress', (evt) => {
        onProgress(evt);
      }, false);

      xhr.addEventListener('load', (e) => {
        if (e.target.status !== 200) {
          reject(e);
          return;
        }
        if (e.target.status === 200) {
          const imgUrl = `${domain}/${uploadKey}`;
          resolve(imgUrl);
        }
      }, false);

      xhr.send(formData);
    });
  }

  static genVoiceFileName = (fileType = 'mp3') => {
    return `${uuid()}.${fileType}`;
  }

  static genVoiceUploadKey = (fileName) => {
    return `voice/${uuid()}${fileName || AliyunHelper.genVoiceFileName()}`;
  }

  static clipsUploadImage = async (file, onProgress = () => { }, config = {}) => {
    const uuidString = uuid();
    const fileType = (/\.[^.]+$/.exec(file.name) || [])[0] || '';
    const uploadKey = `${config.filePath || 'upload'}/${config.fileName || uuidString}${config.fileType || fileType}`;
    const result = config.result || await AliyunHelper.getClipsSignature();
    if (config.domain) {
      result.domain = config.domain;
      result.uploadDomain = config.uploadDomain;
    }
    const { accessKeyId, domain, uploadDomain, policy, signature } = result;

    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('OSSAccessKeyId', accessKeyId);
      formData.append('policy', policy);
      formData.append('signature', signature);
      formData.append('key', uploadKey);
      formData.append('success_action_status', 200);
      formData.append('file', file);
      const xhr = new XMLHttpRequest();
      xhr.open('post', uploadDomain);
      xhr.upload.addEventListener('progress', (evt) => {
        onProgress(evt);
      }, false);

      xhr.addEventListener('load', (e) => {
        if (e.target.status !== 200) {
          reject(e);
          return;
        }
        if (e.target.status === 200) {
          const imgUrl = `${domain}/${uploadKey}`;
          resolve(imgUrl);
        }
      }, false);

      xhr.send(formData);
    });
  }

  static uploadImageBlob = async (blob, onProgress = () => { }, filePath) => {
    const uuidString = uuid();
    const uploadKey = filePath || `upload/${uuidString}`;
    const result = await AliyunHelper.getClipsSignature();
    const { accessKeyId, domain, uploadDomain, policy, signature } = result;

    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('OSSAccessKeyId', accessKeyId);
      formData.append('policy', policy);
      formData.append('signature', signature);
      formData.append('key', uploadKey);
      formData.append('success_action_status', 200);
      formData.append('file', blob);
      const xhr = new XMLHttpRequest();
      xhr.open('post', uploadDomain);
      xhr.upload.addEventListener('progress', (evt) => {
        onProgress(evt);
      }, false);

      xhr.addEventListener('load', (e) => {
        if (e.target.status !== 200) {
          reject(e);
          return;
        }
        if (e.target.status === 200) {
          const imgUrl = `${domain}/upload/${uuidString}`;
          resolve(imgUrl);
        }
      }, false);

      xhr.send(formData);
    });
  }

  static getClipsSignature = async () => {
    const result = await Accounts.getOssSignature();
    return { ...result, uploadDomain: result?.url };
  }

  static getFileInfo = (url = '') => {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open('GET', `${url}?x-oss-process=image/info`);
      xhr.addEventListener('load', (e) => {
        if (e.target.status !== 200) {
          reject(e);
          return;
        }
        if (e.target.status === 200) {
          const { ImageWidth, ImageHeight } = JSON.parse(e.target.response);
          resolve({ height: ImageHeight.value, width: ImageWidth.value });
        }
      }, false);
      xhr.send(null);
    });
  }

  static getThumbImageUrl = (rawUrl) => {
    return `${rawUrl}${ImageZoom.THUMB_IMAGE_CONFIG}`;
  }

  static getBigImageUrl = (rawUrl) => {
    return `${rawUrl}${ImageZoom.RESIZE_ACCORDING_TO_WIDTH}`;
  }

  static getThumbUrlForUpload = (rawUrl) => {
    return `${rawUrl}${ImageZoom.UPLOAD_THUMB_IMAGE_CONFIG}`;
  }

  static patchWechatEmojiAntiTheftChain = (url) => {
    // 微信emoji表情图片防盗链URL的正则
    const emojiAntiTheftChainReg = /^(https?:\/\/)mmbiz\.qpic\.cn/;
    if (emojiAntiTheftChainReg.test(url)) {
      return url.replace(emojiAntiTheftChainReg, (_, $1) => {
        return `${$1}emoji.qpic.cn`;
      });
    }

    return url;
  }

  static getSafePicDomain = () => {
    return 'https://mini-pics.bzy.ai';
  }

  static formatSafeUrl = (imageUrl = '') => {
    let safeUrl = imageUrl;
    if (imageUrl && !/bzy\.ai/.test(imageUrl)) {
      safeUrl = `${AliyunHelper.getSafePicDomain()}/mirror/${Base64.encode(imageUrl)}`;
    }

    return safeUrl;
  }

  static formatImageUrl = (imageUrl = '', params) => {
    if (!imageUrl) {
      return { url: imageUrl };
    }

    let safeUrl = imageUrl;
    let type;
    let height;
    let width;

    safeUrl = safeUrl.replace(/((\?|&)bzytype=.*?)?((\?|&)bzyw=\d+)?((\?|&)bzyh=\d+)?$/, '');
    if (/bzytype=(.*?)(&|$)/.test(imageUrl)) {
      [, type] = /bzytype=(.*?)(&|$)/.exec(imageUrl);
    } else if (/\.gif$/.test(imageUrl)) {
      type = 'gif';
    }

    if (/bzyw=\d+(&|$)/.test(imageUrl)) {
      width = parseInt(/bzyw=(\d+?)(&|$)/.exec(imageUrl)[1], 10);
    }

    if (/bzyh=\d+(&|$)/.test(imageUrl)) {
      height = parseInt(/bzyh=(\d+?)(&|$)/.exec(imageUrl)[1], 10);
    }

    safeUrl = AliyunHelper.formatSafeUrl(safeUrl);
    const url = `${safeUrl}${params}`;

    return { url, type, height, width };
  }

  static getThumbUrlStyle = () => {
    return '?x-oss-process=image/resize,m_fill,w_190,h_190,limit_0/quality,q_70';
  }

  static getImageUrlWithSalt = (imageUrl) => {
    const resize = `/resize,p_${Math.floor(Math.random() * 30) + 70}`;
    const quality = `/quality,q_${Math.floor(Math.random() * 30) + 70}`;
    const sharpen = `/sharpen,${Math.floor(Math.random() * 100) + 100}`;
    return `${imageUrl}?x-oss-process=image${resize}${quality}${sharpen}`;
  }
}
