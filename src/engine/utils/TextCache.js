/**
 * LLM文本缓存工具类
 * 使用IndexedDB存储ID-文本键值对
 */
class TextCache {
  constructor(dbName = 'LLMTextCache', version = 1) {
    this.dbName = dbName;
    this.version = version;
    this.storeName = 'texts';
    this.db = null;
    this.initialized = false;
  }

  /**
   * 初始化数据库
   */
  async init() {
    if (this.initialized) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        this.initialized = true;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;

        // 如果存储对象已存在，先删除
        if (db.objectStoreNames.contains(this.storeName)) {
          db.deleteObjectStore(this.storeName);
        }

        // 创建存储对象，使用id作为主键
        db.createObjectStore(this.storeName, { keyPath: 'id' });
      };
    });
  }

  /**
   * 确保数据库已初始化
   */
  async ensureInitialized() {
    if (!this.initialized) {
      await this.init();
    }
  }

  /**
   * 存储文本
   * @param {string} id - 文本ID
   * @param {string} text - LLM返回的文本内容
   */
  async setText(id, text) {
    await this.ensureInitialized();

    if (!id || typeof text !== 'string') {
      throw new Error('无效的参数: id和text都是必需的');
    }

    const data = {
      id,
      text,
    };

    const transaction = this.db.transaction([this.storeName], 'readwrite');
    const store = transaction.objectStore(this.storeName);

    return new Promise((resolve, reject) => {
      const request = store.put(data);
      request.onsuccess = () => { return resolve(); };
      request.onerror = () => { return reject(request.error); };
    });
  }

  /**
   * 获取文本
   * @param {string} id - 文本ID
   * @returns {string|null} 文本内容，如果不存在返回null
   */
  async getText(id) {
    await this.ensureInitialized();

    if (!id) {
      throw new Error('ID不能为空');
    }

    const transaction = this.db.transaction([this.storeName], 'readonly');
    const store = transaction.objectStore(this.storeName);

    return new Promise((resolve, reject) => {
      const request = store.get(id);

      request.onsuccess = () => {
        const { result } = request;

        if (!result) {
          resolve(null);
          return;
        }

        resolve(result.text);
      };

      request.onerror = () => { return reject(request.error); };
    });
  }

  /**
   * 批量存储文本
   * @param {object} textMap - 键值对对象 {id: text, ...}
   */
  async setTexts(textMap) {
    await this.ensureInitialized();

    if (!textMap || typeof textMap !== 'object') {
      throw new Error('textMap必须是一个对象');
    }

    const transaction = this.db.transaction([this.storeName], 'readwrite');
    const store = transaction.objectStore(this.storeName);

    const promises = [];

    for (const [id, text] of Object.entries(textMap)) {
      const data = {
        id,
        text,
      };

      promises.push(new Promise((resolve, reject) => {
        const request = store.put(data);
        request.onsuccess = () => { return resolve(); };
        request.onerror = () => { return reject(request.error); };
      }));
    }

    return Promise.all(promises);
  }

  /**
   * 批量获取文本
   * @param {string[]} ids - ID数组
   * @returns {object} 键值对对象 {id: text, ...}
   */
  async getTexts(ids) {
    await this.ensureInitialized();

    if (!Array.isArray(ids)) {
      throw new Error('ids必须是一个数组');
    }

    const promises = ids.map(async (id) => {
      const text = await this.getText(id);
      return { id, text };
    });

    const results = {};
    const textResults = await Promise.all(promises);

    textResults.forEach(({ id, text }) => {
      if (text !== null) {
        results[id] = text;
      }
    });

    return results;
  }

  /**
   * 检查文本是否存在
   * @param {string} id - 文本ID
   * @returns {boolean} 是否存在
   */
  async hasText(id) {
    const text = await this.getText(id);
    return text !== null;
  }

  /**
   * 删除文本
   * @param {string} id - 文本ID
   */
  async deleteText(id) {
    await this.ensureInitialized();

    if (!id) {
      throw new Error('ID不能为空');
    }

    const transaction = this.db.transaction([this.storeName], 'readwrite');
    const store = transaction.objectStore(this.storeName);

    return new Promise((resolve, reject) => {
      const request = store.delete(id);
      request.onsuccess = () => { return resolve(); };
      request.onerror = () => { return reject(request.error); };
    });
  }

  /**
   * 清空所有缓存
   */
  async clear() {
    await this.ensureInitialized();

    const transaction = this.db.transaction([this.storeName], 'readwrite');
    const store = transaction.objectStore(this.storeName);

    return new Promise((resolve, reject) => {
      const request = store.clear();
      request.onsuccess = () => { return resolve(); };
      request.onerror = () => { return reject(request.error); };
    });
  }

  /**
   * 关闭数据库连接
   */
  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
      this.initialized = false;
    }
  }
}

export default TextCache;
