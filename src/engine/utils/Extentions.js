import Consts from '~/consts';
import Utils from '~/pages/Playground/Utils';
import ChatBot from '../ChatBot';

export default class Extentions {
  static calculateCharacterLength = (str = '') => {
    return Array.from(str).reduce((accumulator, codePoint) => {
      return accumulator + (Consts.REGEX.unicodeCharacter.test(codePoint) ? 2 : 1);
    }, 0);
  }

  static downloadImage = (item) => {
    const x = new XMLHttpRequest();
    x.open('GET', item.fileUrl, true);
    x.responseType = 'blob';
    x.onload = () => {
      const url = window.URL.createObjectURL(x.response);
      const a = document.createElement('a');
      a.href = url;
      a.download = '';
      a.click();
    };
    x.send();
  }

  static downloadFile = (url) => {
    const aLink = document.createElement('a');
    aLink.style.display = 'none';
    aLink.href = url;
    aLink.target = '_blank';
    aLink.download = 'file.xlsx';
    document.body.appendChild(aLink);
    aLink.click();
    document.body.removeChild(aLink);
  }

  static formatPrompt = (text, callback, times = 1, prompt = Consts.PROMPT_TEMPLATE) => {
    if (!text || times > 3) {
      return;
    }
    try {
      ChatBot.directChatCompletion({
        model: 'gpt-4-0613',
        temperature: 1,
        top_p: 1,
        n: 1,
        stream: true,
        stop: '',
        max_tokens: 2048,
        presence_penalty: 0,
        frequency_penalty: 0,
        user: '',
        messages: [
          {
            role: 'system',
            content: prompt,
          },
          {
            role: 'user',
            content: prompt === Consts.PROMPT_TEMPLATE ? `优化这个prompt “${text}”，只需要输出prompt，不需要解释。` : text,
          },
        ],
      }, {
        responseType: 'stream',
        onDownloadProgress: (e) => {
          const arrs = Utils.formatSSE(e?.event?.target?.response);
          let isStop = false;
          const result = arrs.map(({ choices }) => {
            if (choices[0]?.finish_reason === 'stop') {
              isStop = true;
            }
            return choices[0]?.delta?.content;
          });
          callback(result.join(''), isStop);
        },
      });

      // const result = Object.keys(data).map((key) => { return data[key]; });
      // const reg = /"content": ".*?"/g;
      // const match = result.join('').match(reg);
      // const formatResult = match.map((item) => {
      //   const formatItem = item.replace(/"content": "/g, '').replace(/"/g, '');
      //   return formatItem.trim() === '\\' ? '' : JSON.parse(`"${formatItem}"`);
      // });

      // // eslint-disable-next-line consistent-return
      // return formatResult.join('');
    } catch (err) {
      // eslint-disable-next-line consistent-return
      return this.formatPrompt(text, times + 1);
    }
  }
}
