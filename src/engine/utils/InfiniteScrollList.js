import IndexedList from './IndexedList';

export default class InfiniteScrollList {
  _indexedItems = null
  _meta = {
    totalCount: 0,
    totalPage: 0,
    pageSize: 20,
    pageIndex: 1,
  }

  constructor({ requestFunction = () => {}, params = {}, itemsKey = 'items', ...otherOptions } = {}) {
    this._options = { ...otherOptions, requestFunction, params, itemsKey };
    this._options.params['pagination.pageIndex'] = params['pagination.pageIndex'] || 1;
    this._options.params['pagination.pageSize'] = params['pagination.pageSize'] || 20;
    this._indexedItems = new IndexedList();
  }

  loadMore = async () => {
    const data = await this._options.requestFunction(this._options.params);
    if (!data) {
      return;
    }
    // eslint-disable-next-line
    this._options.params['pagination.pageIndex'] = this._options.params['pagination.pageIndex'] + 1;

    for (const item of data[this._options.itemsKey]) {
      this.push(item);
    }

    this._meta = {
      currentCount: data[this._options.itemsKey].length,
      totalCount: data.total,
      totalPage: Math.floor(data.total / this._options.params['pagination.pageSize']) + 1,
      pageIndex: this._options.params['pagination.pageIndex'],
      pageSize: this._options.params['pagination.pageSize'],
    };
  }

  push = (item) => {
    this._indexedItems.push(item);
  }

  delete = (id) => {
    this._indexedItems.delete(id);
  }

  deleteItems = (ids) => {
    for (const id of ids) {
      this.delete(id);
    }
  }

  reset = () => {
    this._indexedItems.reset();
  }

  getArray = () => {
    return this._indexedItems.getArray();
  }

  getTotalCount = () => {
    return this._meta.totalCount;
  }

  getPageSize = () => {
    return this._meta.pageSize;
  }

  getCurrentCount = () => {
    return this.getArray().length;
  }

  hasMoreData = () => {
    return this.getPageSize() === this._meta.currentCount;
  }
}
