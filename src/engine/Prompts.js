import rest from 'axios';

export default class Prompts {
  static fetchAll = async (params) => {
    return rest.post('/v2/prompts/search', params);
  }

  static create = async (params) => {
    return rest.post('/v2/prompts/', params);
  }

  static updateVersion = async (params) => {
    return rest.post(`/v2/prompts/${params?.id}`, params);
  }

  static update = async (params) => {
    return rest.put(`/v2/prompts/${params?.id}`, params);
  }

  static delete = async (id) => {
    return rest.delete(`/v2/prompts/${id}`);
  }

  static get = async (id) => {
    return rest.get(`/v2/prompts/${id}`);
  }

  static getVersions = async (params) => {
    return rest.post(`/v2/prompts/${params?.id}/versions`, params);
  }
}
