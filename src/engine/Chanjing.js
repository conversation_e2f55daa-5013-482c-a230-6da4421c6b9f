import rest from 'axios';

export default class Chanjing {
  static getChanjingAuth = async (partnerId) => {
    return rest.get(`/v2/chatbot/chanjing/auth/${partnerId}`);
  }

  static upsertChanjingAuth = async (partnerId, params) => {
    return rest.post(`/v2/chatbot/chanjing/auth/upsert/${partnerId}`, params);
  }

  static deleteChanjingAuth = async (partnerId) => {
    return rest.delete(`/v2/chatbot/chanjing/auth/${partnerId}`);
  }
}
