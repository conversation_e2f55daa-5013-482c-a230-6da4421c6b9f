import rest from 'axios';

export default class AiTeacher {
  static fetchCourses = async (params) => {
    return rest.post('/v2/aiteacher/courses/search', params);
  }

  static fetchCourse = async (itemId) => {
    return rest.get(`/v2/aiteacher/courses/${itemId}`);
  }

  static createCourse = async (params) => {
    return rest.post('/v2/aiteacher/courses', params);
  }

  static updateCourse = async (params) => {
    return rest.put(`/v2/aiteacher/courses/${params.id}`, params);
  }

  static deleteCourse = async (itemId) => {
    return rest.delete(`/v2/aiteacher/courses/${itemId}`);
  }

  static fetchCourseChapters = async (courseId) => {
    return rest.get(`/v2/aiteacher/courses/${courseId}/chapters`);
  }

  static fetchCourseChapter = async (courseId, chapterId) => {
    return rest.get(`/v2/aiteacher/courses/${courseId}/chapters/${chapterId}`);
  }

  static upsertCourseChapter = async (params) => {
    return rest.post(`/v2/aiteacher/courses/${params?.courseId}/chapters`, params);
  }

  static deleteCourseChapter = async (courseId, chapterId) => {
    return rest.delete(`/v2/aiteacher/courses/${courseId}/chapters/${chapterId}`);
  }

  static fetchChapterParagraph = async (itemId) => {
    return rest.get(`/v2/aiteacher/chapter-paragraphs/${itemId}`);
  }

  static fetchChapterParagraphs = async (params) => {
    return rest.post('/v2/aiteacher/chapter-paragraphs/search', params);
  }

  static upsertChapterParagraph = async (params) => {
    return rest.post('/v2/aiteacher/chapter-paragraphs', params);
  }

  static dragCourseChapter = async (params) => {
    const { chapterId, courseId, ...restParams } = params;
    return rest.post(`/v2/aiteacher/courses/${courseId}/chapters/${chapterId}/drag`, restParams);
  }

  static processChapterParagraph = async (params) => {
    return rest.post(`/v2/aiteacher/chapter-paragraphs/jobs/process-job/${params.id}`, params);
  }

  static getChapterParagraphTimeline = async (paragraphId) => {
    return rest.get(`/v2/aiteacher/chapter-paragraphs/get-timeline/${paragraphId}`);
  }

  static upsertCoursePrompt = async (params) => {
    return rest.put(`/v2/aiteacher/courses/${params.courseId}/prompts`, params);
  }

  static saveTimelineAudio = async (params) => {
    return rest.post('/v2/aiteacher/chapter-paragraphs/timelines/save-audio', params);
  }

  static getDefaultAssistantPrompt = async (params) => {
    return rest.get('/v2/aiteacher/courses/prompts/assistant-prompts', { params });
  }

  static batchProcessChapterParagraph = async (params) => {
    return rest.post('/v2/aiteacher/chapter-paragraphs/jobs/batch-process-job', params);
  }

  static fetchBundles = async (params) => {
    return rest.post('/v2/aiteacher/bundles/search', params);
  }

  static getBundle = async (itemId) => {
    return rest.get(`/v2/aiteacher/bundles/${itemId}`);
  }

  static createBundle = async (params) => {
    return rest.post('/v2/aiteacher/bundles', params);
  }

  static updateBundle = async (params) => {
    return rest.put(`/v2/aiteacher/bundles/${params.id}`, params);
  }

  static deleteBundle = async (itemId) => {
    return rest.delete(`/v2/aiteacher/bundles/${itemId}`);
  }

  static getBundleCourses = (id) => {
    return rest.get(`/v2/aiteacher/bundles/${id}/courses`);
  }

  static bundleCourses = (params) => {
    return rest.post(`/v2/aiteacher/bundles/${params.id}/courses`, params);
  }

  static fetchRedeemCodes = async (params) => {
    return rest.post('/v2/aiteacher/redeem-codes/search', params);
  }

  static generateRedeemCodes = async (params) => {
    return rest.post('/v2/aiteacher/redeem-codes/generate', params);
  }

  static fetchRedeemCode = async (itemId) => {
    return rest.get(`/v2/aiteacher/redeem-codes/${itemId}`);
  }

  static fetchLiveRooms = async (params) => {
    return rest.post('/v2/aiteacher/live-rooms/search', params);
  }

  static fetchLiveRoom = async (itemId) => {
    return rest.get(`/v2/aiteacher/live-rooms/${itemId}`);
  }

  static createLiveRoom = async (params) => {
    return rest.post('/v2/aiteacher/live-rooms', params);
  }

  static updateLiveRoom = async (params) => {
    return rest.put(`/v2/aiteacher/live-rooms/${params.id}`, params);
  }

  static deleteLiveRoom = async (itemId) => {
    return rest.delete(`/v2/aiteacher/live-rooms/${itemId}`);
  }

  static fetchVideos = async (params) => {
    return rest.post('/v2/chatbot/course-to-video/search', params);
  }

  static fetchMergeRecords = async (itemId) => {
    return rest.post(
      `/v2/chatbot/course-to-video/merge-records/${itemId}/search`,
      { pagination: { skip: 0, limit: 1000 } },
    );
  }

  static getCategoriesTree = async (params) => {
    return rest.get('/v2/chatbot/course-to-video/categories/tree', params);
  }
}
