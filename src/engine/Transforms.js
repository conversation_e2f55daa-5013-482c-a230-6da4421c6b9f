import rest from 'axios';

export default class Transforms {
  static fetchGroups = async (params) => {
    return rest.post('/v2/chatbot/content-transform-rule-group/search', params);
  }

  static createGroup = async (params) => {
    return rest.post('/v2/chatbot/content-transform-rule-group', params);
  }

  static updateGroup = async (params) => {
    return rest.put(`/v2/chatbot/content-transform-rule-group/${params?.id}`, params);
  }

  static deleteGroup = async (id) => {
    return rest.delete(`/v2/chatbot/content-transform-rule-group/${id}`);
  }

  static fetchRules = async (params) => {
    return rest.post('/v2/chatbot/content-transform-rule/search', params);
  }

  static createRule = async (params) => {
    return rest.post('/v2/chatbot/content-transform-rule', params);
  }

  static updateRule = async (params) => {
    return rest.put(`/v2/chatbot/content-transform-rule/${params?.id}`, params);
  }

  static deleteRule = async (id) => {
    return rest.delete(`/v2/chatbot/content-transform-rule/${id}`);
  }

  static getRule = async (id) => {
    return rest.get(`/v2/chatbot/content-transform-rule/${id}`);
  }
}
