import rest from 'axios';

export default class Materials {
  static creatematerialSource = (params) => {
    return rest.post('/v2/chatbot/material-sources', params);
  }

  static getmaterialSource = (id) => {
    return rest.get('/v2/chatbot/material-sources/usages/get-by-usage-id', { params: { usageId: id } });
  }

  static putmaterialSource = (params) => {
    return rest.post('/v2/chatbot/material-sources', params);
  }

  static deletematerialSource = (id) => {
    return rest.delete(`/v2/chatbot/material-sources/usages/delete-with-id/${id}`);
  }

  static fetchmaterialSources = (params) => {
    return rest.get('/v2/chatbot/material-sources/usages/list-with-usages', { params });
  }

  static manualSyncMaterialSource = (sourceId) => {
    return rest.post(`/v2/chatbot/material-sources/${sourceId}/manual-sync`);
  }

  // 即时搜索 同步 || 手动新增
  static manualSyncMaterial = (params) => {
    return rest.post('/v2/chatbot/materials/manual-sync', params);
  }

  static fetchmaterialSourceGroups = (params) => {
    return rest.post('/v2/chatbot/material-source-groups/search', params);
  }

  static creatematerialSourceGroup = (params) => {
    return rest.post('/v2/chatbot/material-source-groups', params);
  }

  static getmaterialSourceGroup = (id) => {
    return rest.get(`/v2/chatbot/material-source-groups/${id}`);
  }

  static putmaterialSourceGroup = (params) => {
    return rest.put(`/v2/chatbot/material-source-groups/${params.id}`, params);
  }

  static deletematerialSourceGroup = (id) => {
    return rest.delete(`/v2/chatbot/material-source-groups/${id}`);
  }

  static getMaterialSourceGroups = (id) => {
    return rest.get(`/v2/chatbot/material-sources/groups/${id}`);
  }

  static putMaterialSourceGroups = (params) => {
    return rest.put(`/v2/chatbot/material-sources/groups/${params.id}`, params);
  }

  static batchDeletematerialSources = (params) => {
    return rest.delete('/v2/chatbot/material-sources/multi-delete/delete-with-ids', { data: params });
  }

  static creatematerial = (params) => {
    return rest.post('/v2/chatbot/materials', params);
  }

  static getmaterial = (id) => {
    return rest.get(`/v2/chatbot/materials/${id}`);
  }

  static putmaterial = (params) => {
    return rest.put(`/v2/chatbot/materials/${params.id}`, params);
  }

  static deletematerial = (id) => {
    return rest.delete(`/v2/chatbot/materials/${id}`);
  }

  static searchMaterialsByGroup = (params) => {
    return rest.post('/v2/chatbot/materials/search-by-group', params);
  }

  static fetchmaterials = async (params) => {
    const { items, total } = await rest.post('/v2/chatbot/materials/search-by-character', params);
    const newItems = items.map(({ characterMaterial, material }) => {
      return { ...material, character: characterMaterial };
    });
    return { items: newItems, total };
  }

  static fetcharticles = (params) => {
    return rest.post('/v2/chatbot/character-materials/search', params);
  }

  static createarticle = (params) => {
    return rest.post('/v2/chatbot/character-materials', params);
  }

  static getarticle = (id) => {
    return rest.get(`/v2/chatbot/character-materials/${id}`);
  }

  static putarticle = (params) => {
    return rest.put(`/v2/chatbot/character-materials/${params.id}`, params);
  }

  static deletearticle = (id) => {
    return rest.delete(`/v2/chatbot/character-materials/${id}`);
  }

  static searchArticleByMeta = (params) => {
    return rest.post('/v2/chatbot/article-meta/search-article', params);
  }

  static fetchKols = () => {
    return rest.post('/v2/chatbot/publish-characters/search', {
      'pagination.pageIndex': 1,
      'pagination.pageSize': 1000,
    });
  }

  static fetchjobs = (params) => {
    return rest.post('/v2/chatbot/material-fetch-jobs/search', params);
  }

  static retryJob = (id) => {
    return rest.post(`/v2/chatbot/material-fetch-jobs/retry/${id}`);
  }

  static createjob = (params) => {
    return rest.post('/v2/chatbot/material-fetch-jobs', params);
  }

  static getjob = (id) => {
    return rest.get(`/v2/chatbot/material-fetch-jobs/${id}`);
  }

  static putjob = (params) => {
    return rest.put(`/v2/chatbot/material-fetch-jobs/${params.id}`, params);
  }

  static deletejob = (id) => {
    return rest.delete(`/v2/chatbot/material-fetch-jobs/${id}`);
  }

  static getArticleWithId = (id) => {
    return rest.get(`/v2/chatbot/character-materials/get-with-material-id/${id}`);
  }

  static rerunOptimizeJob = (id) => {
    return rest.post(`/v2/chatbot/character-materials/rerun-optimize-job/${id}`);
  }

  static getMaterialOptimizeJobs = (id) => {
    return rest.get(`/v2/chatbot/material-optimize-jobs/${id}`);
  }

  static analyzeTopic = (params) => {
    return rest.post('/v2/chatbot/materials/analyze-topic', params);
  }

  static createFetchJob = (params) => {
    return rest.post('/v2/chatbot/material-fetch-jobs/jobs/new', params);
  }

  static fetchOpenMaterialSource = (groupId) => {
    return rest.get(`/v2/consumer/anon/material/chatbot-material-sources/list-by-source-group-id/${groupId}`);
  }

  static fetchOpenMaterials = (params) => {
    return rest.post(`/v2/consumer/anon/material/chatbot-materials/list-by-group-uuid/${params.uuid}`, params);
  }

  static getOpenMaterial = (id) => {
    return rest.get(`/v2/consumer/anon/material/chatbot-materials/${id}`);
  }

  static genCharacterMaterial = (params) => {
    return rest.post('/v2/chatbot/materials/gen-character-material', params);
  }

  static genAsrContent = (params) => {
    return rest.post('/v2/chatbot/materials/gen-asr-content', params);
  }

  static fetchMaterialTopics = (params) => {
    return rest.get('/v2/chatbot/material-topics', { params });
  }

  static genMaterialTopics = (materialId) => {
    return rest.get(`/v2/chatbot/material-topics/gen-topics/materials/${materialId}`);
  }

  static fetchDistinctMaterialTopics = (params) => {
    return rest.get('/v2/chatbot/material-topics/distinct', { params });
  }

  static fetchTopicMaterials = (params) => {
    return rest.post('/v2/chatbot/topic-materials/search', params);
  }

  static genTopicMaterial = (params) => {
    return rest.post('/v2/chatbot/materials/gen-topic-material', params);
  }
}
