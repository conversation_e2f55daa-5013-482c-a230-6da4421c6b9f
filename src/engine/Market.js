// import { StringExtension } from '~/plugins';
// import _ from 'lodash';
import rest from 'axios';

export default class Market {
  static getInviteCodes = async (params) => {
    return rest.get('/v2/chatbot/invite-codes', { params });
  }

  static addInviteCode = async (params) => {
    return rest.post('/v2/chatbot/invite-codes', params);
  }

  static addPublishCharacter = async (params) => {
    return rest.post('/v2/chatbot/publish-characters', params);
  }

  static fetchPublishCharacters = async (params) => {
    return rest.post('/v2/chatbot/publish-characters/search', params);
  }

  static getPublishCharacter = async (id) => {
    return rest.get(`/v2/chatbot/publish-characters/${id}`);
  }

  static updatePublishCharacter = async (params) => {
    return rest.put(`/v2/chatbot/publish-characters/${params.id}`, params);
  }

  static delPublishCharacter = async (id) => {
    return rest.delete(`/v2/chatbot/publish-characters/${id}`);
  }

  static fetchCharacterAccounts = async (params) => {
    return rest.post('/v2/chatbot/character-accounts/search', params);
  }

  static addCharacterAccount = async (params) => {
    return rest.post('/v2/chatbot/character-accounts', params);
  }

  static updateCharacterAccount = async (params) => {
    return rest.put(`/v2/chatbot/character-accounts/${params.id}`, params);
  }

  static delCharacterAccount = async (id) => {
    return rest.delete(`/v2/chatbot/character-accounts/${id}`);
  }

  static addPartner = (params) => {
    return rest.post('/v2/chatbot/partners', params);
  }

  static updatePartner = (params) => {
    return rest.put(`/v2/chatbot/partners/${params.id}`, params);
  }

  static getPartner = (id) => {
    return rest.get(`/v2/chatbot/partners/${id}`);
  }

  static fetchPartners = (params) => {
    return rest.get('/v2/chatbot/partners', { params });
  }

  static fetchPartnerMembers = (params) => {
    return rest.get(`/v2/chatbot/partner/${params.id}/members`, { params });
  }

  static delPartnerMember = (params) => {
    return rest.delete(`/v2/zone/members/${params?.id}`, { data: params });
  }

  static fetchMembers = (params) => {
    return rest.get('/v2/zone/members/list-with-partner', { params });
  }

  static fetchPartnerPermissions = (id) => {
    return rest.get(`/v2/chatbot/permissions/${id}`);
  }

  static addPartnerPermissions = (params) => {
    return rest.post(`/v2/chatbot/permissions/${params?.partnerId}`, params);
  }

  static updatePartnerPermissions = (params) => {
    return rest.put(`/v2/chatbot/permissions/${params?.partnerId}`, params);
  }

  static fetchModelQuotas = (id) => {
    return rest.get(`/v2/chatbot/model-quotas/${id}`);
  }

  static addModelQuotas = (params) => {
    return rest.post(`/v2/chatbot/model-quotas/${params?.partnerId}`, params);
  }

  static updateModelQuotas = (params) => {
    return rest.put(`/v2/chatbot/model-quotas/${params?.partnerId}`, params);
  }

  static fetchAllMembers = (params) => {
    return rest.get('/v2/zone/members', { params });
  }

  static bindMember = (params) => {
    return rest.post(`/v2/chatbot/partners/${params.partnerId}/bind-member`, params);
  }

  static addChatroomGroup = (params) => {
    return rest.post('/v2/chatbot/chatroom-groups', params);
  }

  static updateChatroomGroup = (params) => {
    return rest.put(`/v2/chatbot/chatroom-groups/${params.id}`, params);
  }

  static delChatroomGroup = (id) => {
    return rest.delete(`/v2/chatbot/chatroom-groups/${id}`);
  }

  static fetchChatroomGroups = (params) => {
    return rest.post('/v2/chatbot/chatroom-groups/search', { params });
  }

  static fetchChatrooms = (params) => {
    return rest.get('/v2/zone/chatrooms', { params });
  }

  static updateChatroom = (params) => {
    return rest.put(`/v2/zone/chatrooms/${params.id}`, params);
  }

  static delChatroom = (id) => {
    return rest.delete(`/v2/zone/chatrooms/${id}`);
  }

  static fetchChatroomGroupPublishRules = (id) => {
    return rest.get(`/v2/chatbot/chatroom-group-publish-rules/get-by-group-uuid/${id}`);
  }

  static addChatroomGroupPublishRule = (params) => {
    return rest.post('/v2/chatbot/chatroom-group-publish-rules', params);
  }

  static updateChatroomGroupPublishRule = (params) => {
    return rest.put(`/v2/chatbot/chatroom-group-publish-rules/${params.id}`, params);
  }

  static delChatroomGroupPublishRule = (id) => {
    return rest.delete(`/v2/chatbot/chatroom-group-publish-rules/${id}`);
  }

  static fetchBotsInRoom = (params) => {
    return rest.get('/v2/chatbot/chatbot-wework-bot/bots-in-room', { params });
  }

  static fetchChatbots = (params) => {
    return rest.post('/v2/chatbot/chatbot-wework-bots/search', params);
  }

  static addChatbot = (params) => {
    return rest.post('/v2/chatbot/chatbot-wework-bots', params);
  }

  static updateChatbot = (params) => {
    return rest.put(`/v2/chatbot/chatbot-wework-bots/${params.id}`, params);
  }

  static delChatbot = (id) => {
    return rest.delete(`/v2/chatbot/chatbot-wework-bots/${id}`);
  }

  static fetchBotMemberWorkflows = (params) => {
    return rest.get('/v2/chatbot/bot-workflows', { params });
  }

  static addBotMemberWorkflow = (params) => {
    return rest.post('/v2/chatbot/bot-workflows', params);
  }

  static delBotMemberWorkflow = (id) => {
    return rest.delete(`/v2/chatbot/bot-workflows/${id}`);
  }

  static fetchTokenUsagesMonthly = () => {
    return rest.get('/v2/chatbot/token-usages/monthly');
  }

  static fetchTokenUsages = () => {
    return rest.get('/v2/chatbot/token-usages');
  }

  static fetchTokenUsageLogs = (params) => {
    return rest.get('/v2/chatbot/token-usage/logs', { params });
  }

  static fetchImageUsageLogs = (params) => {
    return rest.post('/v2/chatbot/image-generate-logs/search', params);
  }

  static fetchImageCost = (params) => {
    return rest.get('/v2/chatbot/image-generate-logs/cost/count', { params });
  }

  static fetchNovels = (params) => {
    return rest.get('https://fn.bzy.ai/v2/novel/list/all', { params });
  }

  static addNovel = (params) => {
    return rest.post(`https://fn.bzy.ai/v2/novel/list/all?p_id=${params?.pId}`, params);
  }

  static fetchNovelChapter = (params) => {
    return rest.get('https://fn.bzy.ai/v2/novel/list/detail', { params });
  }

  static saveNovelChapter = (params) => {
    return rest.post(`https://fn.bzy.ai/v2/novel/chapter/${params.pId}`, params);
  }

  static fetchNovelKeywords = (params) => {
    return rest.get(`https://fn.bzy.ai/v2/novel/keyword/${params.pId}`, { params });
  }

  static saveNovelKeywords = (params) => {
    return rest.put(`https://fn.bzy.ai/v2/novel/keyword/${params.pId}`, params);
  }

  static fetchNovelPrompts = (params) => {
    return rest.get(`https://fn.bzy.ai/v2/novel/prompt/${params.pId}`, { params });
  }

  static saveNovelPrompts = (params) => {
    return rest.put(`https://fn.bzy.ai/v2/novel/prompt/${params.pId}`, params);
  }

  static getNovelSnippet = (params) => {
    return rest.post(`https://fn.bzy.ai/v2/novel/snippet/${params.action}`, params);
  }

  static fetchVoiceClones = (params) => {
    return rest.post('/v2/voice-clone/chatbot-voice-clones/search', params);
  }

  static importVoiceClone = (params) => {
    return rest.post('/v2/voice-clone/import-voice-clone', params);
  }

  static exportVoiceClone = (params) => {
    return rest.post('/v2/voice-clone/gen-audio', params);
  }

  static regenVoiceClone = (params) => {
    return rest.post('/v2/voice-clone/regen-shot', params);
  }

  static insertVoiceClone = (params) => {
    return rest.post('/v2/voice-clone/new-shot', params);
  }

  static removeVoiceClone = (params) => {
    return rest.post('/v2/voice-clone/remove-shot', params);
  }

  static updateVoiceCloneOffset = (params) => {
    return rest.post('/v2/voice-clone/update-shot-offset', params, { ignoreLoading: true });
  }

  static updateVoiceCloneSpeed = (params) => {
    return rest.post('/v2/voice-clone/update-shot-speed', params, { ignoreLoading: true });
  }

  static updateVoiceClone = (params) => {
    return rest.post('/v2/voice-clone/update-shot', params);
  }

  static fetchVoiceCloneSpeakers = async () => {
    return [];
    //   const data = await rest.get('/v2/voice-clone/speakers');
    // eslint-disable-next-line
    //   const resp = await fetch(`https://video-clip.oss-cn-shanghai.aliyuncs.com/models/GPT-SoVITS/speakers.json?v=${new Date().getTime()}`);
    //   const speakers = await resp.json();
    //   return StringExtension.snakeToCamelObj([..._.values(data), ...speakers]);
  }

  static getVoiceClone = (itemId) => {
    return rest.get(`/v2/voice-clone/chatbot-voice-clones/${itemId}`);
  }

  static delVoiceClone = (itemId) => {
    return rest.delete(`/v2/voice-clone/chatbot-voice-clones/${itemId}`);
  }

  static jobTrigger = (params) => {
    return rest.post('/v2/jobs/fc/trigger', params);
  }

  static fetchVoiceTones = (params) => {
    return rest.post('/v2/voice-clone/chatbot-voice-tones/search', params);
  }

  static addVoiceTone = (params) => {
    return rest.post('/v2/voice-clone/chatbot-voice-tones', params);
  }

  static updateVoiceTone = (params) => {
    return rest.post('/v2/voice-clone/update-speaker-tone', params);
  }

  static addLiveHelper = (params) => {
    return rest.post('/v2/live-helpers/', params);
  }

  static fetchLiveHelperOutlines = (params) => {
    return rest.get(`/v2/live-helpers/${params.id}/outlines`);
  }

  static fetchLiveHelpers = (params) => {
    return rest.post('/v2/live-helpers/search', params);
  }

  static delLiveHelper = (id) => {
    return rest.delete(`/v2/live-helpers/${id}`);
  }

  static updateLiveHelper = (params) => {
    return rest.post(`/v2/live-helpers/${params.id}`, params);
  }

  static fetchLibLabels = (params) => {
    return rest.get('/v2/live-helper/helper-libs/labels/search', params);
  }

  static fetchHelperLibs = (params) => {
    return rest.post('/v2/live-helper/helper-libs/search', params);
  }

  static fetchLibsByLabel = (params) => {
    return rest.post('/v2/live-helper/libs/search-by-label', params);
  }

  static addHelperLib = (params) => {
    return rest.post('/v2/live-helper/helper-libs', params);
  }

  static updateHelperLib = (params) => {
    return rest.put(`/v2/live-helper/helper-libs/${params.id}`, params);
  }

  static delHelperLib = (id) => {
    return rest.delete(`/v2/live-helper/helper-libs/${id}`);
  }

  static fetchHelperLibDetail = (params) => {
    return rest.post(`/v2/live-helper/libs/${params.id}/search`, params);
  }

  static addHelperKnowledge = (params) => {
    return rest.post(`/v2/live-helper/libs/${params.id}/knowledge`, params);
  }

  static delHelperKnowledge = (params) => {
    return rest.delete(`/v2/live-helper/libs/${params.libId}/knowledge/${params.knowledgeId}`);
  }

  static updateHelperKnowledge = (params) => {
    return rest.put(`/v2/live-helper/libs/${params.libId}/knowledge/${params.knowledgeId}`, params);
  }

  static detectLiveScript = async (params) => {
    return rest.post('/v2/utils/sentence/detect', params, { ignoreLoading: true });
  }

  static textToAudio = (params) => {
    return rest.post('/v2/tts/text-to-audio', params, { ignoreLoading: true });
  }

  static audioToText = (params) => {
    return rest.post('/v2/asr/audio-to-text', params, { ignoreLoading: false });
  }

  static fetchExternalKolClasses = (params) => {
    return rest.post('/v2/chatbot/external_kol_classes/search', params);
  }

  static importExternalKolClasses = (params) => {
    return rest.post('/v2/chatbot/external_kol_classes/external_kol_classes/import_excel', params);
  }

  static vectorSearchExternalKolClasses = async (params = {}) => {
    const url = '/v2/chatbot/external_kol_classes/external_kol_classes/vector_search';
    const result = await rest.post(url, params);
    return Object.values(result);
  }

  // 添加研报相关API方法
  static fetchResearchReports = async (params) => {
    return rest.post('/v2/chatbot/research-reports/search', params);
  }

  static getResearchReport = async (id) => {
    return rest.get(`/v2/chatbot/research-reports/${id}`);
  }

  static createResearchReport = async (params) => {
    return rest.post('/v2/chatbot/research-reports', params);
  }

  static updateResearchReport = async (params) => {
    return rest.put(`/v2/chatbot/research-reports/${params.id}`, params);
  }

  static deleteResearchReport = async (id) => {
    return rest.delete(`/v2/chatbot/research-reports/${id}`);
  }

  static uploadResearchReportFile = async (file, onProgress) => {
    const formData = new FormData();
    formData.append('file', file);

    return rest.post('/v2/chatbot/research-reports/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: onProgress,
    });
  }

  // 复盘相关API
  static fetchMaterialAnalyses = async (params) => {
    return rest.post('/v2/chatbot/material-analysis/search', params);
  }

  static createMaterialAnalysis = async (params) => {
    return rest.post('/v2/chatbot/material-analysis', params);
  }

  static getMaterialAnalysis = async (id) => {
    return rest.get(`/v2/chatbot/material-analysis/${id}`);
  }

  static deleteMaterialAnalysis = async (id) => {
    return rest.delete(`/v2/chatbot/material-analysis/${id}`);
  }

  // 获取直播记录列表
  static fetchLiveStreamRecords = async (params) => {
    return rest.post('/v2/chatbot/live-stream-records/search', params);
  }

  // 获取投放数据记录列表
  static fetchLiveStreamCampaigns = async (params) => {
    return rest.post('/v2/chatbot/live-stream-campaigns/search', params);
  }

  static generateGptImage = async (params) => {
    return rest.post('/v2/utils/gpt-image', params, { ignoreLoading: true });
  }

  // 获取AI对话记录
  static fetchAiConversations = async (params) => {
    return rest.post('/v2/chatbot/ai-conversations/search', params, { ignoreLoading: true });
  }
}
