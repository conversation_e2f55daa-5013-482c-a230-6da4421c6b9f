import rest from 'axios';

export default class Clips {
  static fetchPublishAccounts = async () => {
    return rest.get('/v2/clips/publish-accounts');
  }

  static fetchBundleTemplates = async () => {
    return rest.get('/v2/clips/bundle-templates');
  }

  static fetchArticleLibraries = async () => {
    return rest.get('/v2/clips/article-libraries');
  }

  static fetchMpAccounts = async () => {
    return rest.get('/v2/clips/mp-accounts');
  }
}
