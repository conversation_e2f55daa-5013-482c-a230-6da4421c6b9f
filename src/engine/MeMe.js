import rest from 'axios';

export default class MeMe {
  static fetchCourses = async (params) => {
    return rest.post('/v2/zone/courses/search', params);
  }

  static getCourse = async (id) => {
    return rest.get(`/v2/zone/courses/${id}`);
  }

  static addCourse = async (params) => {
    return rest.post('/v2/zone/courses', params);
  }

  static updateCourse = async (params) => {
    return rest.put(`/v2/zone/courses/${params?.id}`, params);
  }

  static delCourse = async (id) => {
    return rest.delete(`/v2/zone/courses/${id}`);
  }

  static linkWorkflow = async (params) => {
    return rest.post(`/v2/zone/courses/${params?.courseId}/link-workflows`, params);
  }

  static fetchLinkedWorkflows = async (params) => {
    return rest.get(`/v2/zone/courses/${params?.courseId}/linked-workflows`, { params });
  }

  static createCourseGroup = (params) => {
    return rest.post(`/v2/zone/courses/${params?.courseId}/link-chatroom`, params);
  }

  static fetchCourseGroups = (params) => {
    return rest.get(`/v2/zone/courses/${params?.courseId}/linked-chatrooms`, { params });
  }

  static fetchMaterials = (params) => {
    return rest.post('/v2/chatbot/character-materials/search', params);
  }

  static fetchMaterial = (id) => {
    return rest.get(`/v2/chatbot/character-materials/${id}`);
  }

  static addMaterial = (params) => {
    return rest.post('/v2/chatbot/character-materials', params);
  }

  static updateMaterial = (params) => {
    return rest.put(`/v2/chatbot/character-materials/${params.id}`, params);
  }

  static delMaterial = (id) => {
    return rest.delete(`/v2/chatbot/character-materials/${id}`);
  }
}
