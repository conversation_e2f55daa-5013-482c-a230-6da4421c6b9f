import rest from 'axios';

export default class Whatsapp {
  static fetchAccounts = async (params = {}) => {
    return rest.post('/v2/chatbot/whatsapp-bussiness-account/search', params);
  }

  static fetchAccount = async (id) => {
    return rest.get(`/v2/chatbot/whatsapp-bussiness-account${id}`);
  }

  static updateAccount = async (params) => {
    return rest.put(`/v2/chatbot/whatsapp-bussiness-account/${params?.id}`, params);
  }

  static fetchPhones = async (params) => {
    return rest.post('/v2/chatbot/whatsapp-bussiness-account-phone/search', params);
  }

  static fetchPhone = async (id) => {
    return rest.get(`/v2/chatbot/whatsapp-bussiness-account-phone${id}`);
  }

  static updatePhone = async (params) => {
    return rest.put(`/v2/chatbot/whatsapp-bussiness-account-phone/${params?.id}`, params);
  }

  static fetchChannels = async (data) => {
    return rest.post('/v2/chatbot/whatsapp-channel/search', data);
  }

  static fetchChannel = async (id) => {
    return rest.get(`/v2/chatbot/whatsapp-channel/${id}`);
  }

  static addChannel = async (params) => {
    return rest.post('/v2/chatbot/whatsapp-channel', params);
  }

  static updateChannel = async (params) => {
    return rest.put(`/v2/chatbot/whatsapp-channel/${params?.id}`, params);
  }

  static deleteChannel = async (id) => {
    return rest.delete(`/v2/chatbot/whatsapp-channel/${id}`);
  }

  static fetchConversations = async (params) => {
    return rest.post('/v2/chatbot/whatsapp-conversation/search', params);
  }

  static fetchMessages = async (params) => {
    return rest.post('/v2/chatbot/whatsapp-message/search', params);
  }
}
