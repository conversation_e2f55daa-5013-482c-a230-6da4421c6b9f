import { StringExtension } from '~/plugins';
import axios from 'axios';

import Engine from './Engine';
import Sessions from './Sessions';

export default class WeworkKF {
  static fetchCorps = (params) => {
    return axios.post('/v2/aiteacher/kf/corps/search', params);
  }

  static fetchCorp = (itemId) => {
    return axios.get(`/v2/aiteacher/kf/corps/${itemId}`);
  }

  static deleteCorp = (itemId) => {
    return axios.delete(`/v2/aiteacher/kf/corps/${itemId}`);
  }

  static createStaff = (params) => {
    return axios.post('/v2/aiteacher/kf/staffs', params);
  }

  static fetchStaffs = (params) => {
    return axios.post('/v2/aiteacher/kf/staffs/search', params);
  }

  static fetchStaff = (itemId) => {
    return axios.get(`/v2/aiteacher/kf/staffs/${itemId}`);
  }

  static deleteStaff = (itemId) => {
    return axios.delete(`/v2/aiteacher/kf/staffs/${itemId}`);
  }

  static updateStaff = (params) => {
    return axios.put(`/v2/aiteacher/kf/staffs/${params.id}`, params);
  }

  static createStaffChannel = (params) => {
    return axios.post('/v2/aiteacher/kf/staff-channels', params);
  }

  static fetchStaffChannels = (params) => {
    return axios.post('/v2/aiteacher/kf/staff-channels/search', params);
  }

  static fetchStaffConversations = (params) => {
    return axios.post('/v2/aiteacher/kf/conversations/search', params);
  }

  static exportConversations = async (params) => {
    const url = Engine._option.apiEndpoint;
    const resp = await fetch(`${url}/v2/aiteacher/kf/conversations/export-conversations?file_mode=1`, {
      method: 'POST',
      body: JSON.stringify(StringExtension.camelToSnakeObj(params)),
      headers: {
        'Content-Type': 'application/json',
        'grpc-metadata-token': Sessions.getToken(),
      },
    });

    return resp.blob();
  }

  static fetchStaffMessages = (params) => {
    return axios.post('/v2/aiteacher/kf/messages/search', params);
  }

  static fetchStaffChannel = (itemId) => {
    return axios.get(`/v2/aiteacher/kf/staff-channels/${itemId}`);
  }

  static deleteStaffChannel = (itemId) => {
    return axios.delete(`/v2/aiteacher/kf/staff-channels/${itemId}`);
  }

  static updateStaffChannel = (params) => {
    return axios.put(`/v2/aiteacher/kf/staff-channels/${params.id}`, params);
  }

  static fetchInvitationConfigs = (params) => {
    return axios.post('/v2/chatbot/wework-kf-invitation-configs/search', params);
  }

  static fetchInvitationConfig = (itemId) => {
    return axios.get(`/v2/chatbot/wework-kf-invitation-configs/${itemId}`);
  }

  static createInvitationConfig = (params) => {
    return axios.post('/v2/chatbot/wework-kf-invitation-configs', params);
  }

  static updateInvitationConfig = (params) => {
    return axios.put(`/v2/chatbot/wework-kf-invitation-configs/${params.id}`, params);
  }

  static deleteInvitationConfig = (id) => {
    return axios.delete(`/v2/chatbot/wework-kf-invitation-configs/${id}`);
  }

  static fetchActivityFuncs = (params) => {
    return axios.post('/v2/chatbot/wework-kf-invitation-funcs/search', params);
  }

  static fetchActivityFunc = (itemId) => {
    return axios.get(`/v2/chatbot/wework-kf-invitation-funcs/${itemId}`);
  }

  static createActivityFunc = (params) => {
    return axios.post('/v2/chatbot/wework-kf-invitation-funcs', params);
  }

  static updateActivityFunc = (params) => {
    return axios.put(`/v2/chatbot/wework-kf-invitation-funcs/${params.id}`, params);
  }

  static delActivityFunc = (id) => {
    return axios.delete(`/v2/chatbot/wework-kf-invitation-funcs/${id}`);
  }
}
