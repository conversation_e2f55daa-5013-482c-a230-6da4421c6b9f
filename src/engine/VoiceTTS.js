import rest from 'axios';

export default class VoiceTTS {
  static fetchVoiceTest = async (params = {}) => {
    return rest.post('/v2/chatbot/voice-test/search', params);
  }
  static addVoiceTest = async (params) => {
    return rest.post('/v2/chatbot/voice-test/', params);
  }

  static updateVoice = async (params) => {
    return rest.put(`/v2/chatbot/voice-test/${params.id}`, params);
  }

  static getVoiceTest = async (id) => {
    return rest.get(`/v2/chatbot/voice-test/${id}`);
  }

  static deleteVoiceTest = async (id) => {
    return rest.delete(`/v2/chatbot/voice-test/${id}`);
  }

  static asrVoiceTest = async (params) => {
    return rest.post(`/v2/chatbot/voice-test/asr/${params.id}`, params);
  }

  static ttsVoiceTest = async (params) => {
    return rest.post(`/v2/chatbot/voice-test/tts/${params.id}`, params);
  }

  static ttsVoiceTestSentence = async (params) => {
    return rest.post(`/v2/chatbot/voice-test/tts/${params.id}/sentence/${params.index}`, params);
  }
}
