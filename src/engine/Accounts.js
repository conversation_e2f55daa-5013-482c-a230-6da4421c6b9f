import { StringExtension } from '~/plugins';
import axios from 'axios';

import Engine from './Engine';
import Sessions from './Sessions';

export default class Accounts {
  static login = async ({ username, password, remember }) => {
    const auth = await axios.post('/partner/login', { username, password });
    Sessions.login(auth, remember);
    await Sessions.postLogin();
    return auth;
  }

  static ssoLogin = async (code = '') => {
    const auth = await axios.post('/partner/authorization-code/login', { authorizationCode: code });
    Sessions.login(auth, false);
    return auth;
  }

  static getConnectQr = async (params) => {
    params.platform = 'playground'; // eslint-disable-line
    const { url } = await axios.get('/v2/consumer/anon/wechat/qrconnect-url', { params });
    return url;
  }

  static getProfile = async () => {
    return axios.get('/v2/zone/members/detail');
  }

  static getPartner = async () => {
    return axios.get('/v2/zone/members/detail');
  }

  static getOssSignature = () => {
    return axios.get('/v2/utils/oss/signature');
  }

  static getQuota = async () => {
    return axios.get('/v2/consumer/member/quota');
  }

  static getMemberInfo = async () => {
    return axios.get('/v2/zone/members/info', { params: { platform: 'playground' } });
  }

  static fetchPartners = () => {
    return axios.get('/v2/zone/member/partners', { params: { platform: 'playground' } });
  }

  static switchPartner = (params) => {
    return axios.post('/v2/chatbot/partners/select-partners', params);
  }

  static fetchFuncTools = () => {
    return axios.get('/v2/chatbot/function-tools');
  }

  static fetchDouyinDataRooms = (params) => {
    return axios.post('/v2/live-helper/douyin-data/rooms/search?skip=0&limit=1000', params);
  }

  static exportDouyinRoomDatas = async (params) => {
    const url = Engine._option.apiEndpoint;
    const resp = await fetch(`${url}/v2/live-helper/douyin-data/rooms/export?file_mode=1`, {
      method: 'POST',
      body: JSON.stringify(StringExtension.camelToSnakeObj(params)),
      headers: {
        'Content-Type': 'application/json',
        'grpc-metadata-token': Sessions.getToken(),
      },
    });
    return resp.blob();
  }

  static fetchTtsSpeakers = async () => {
    const resp = await fetch(
      `https://video-clip.oss-cn-shanghai.aliyuncs.com/models/tts_speakers.json?v=${new Date().getTime()}`,
    );
    const speakers = await resp.json();
    return speakers;
  }
}
