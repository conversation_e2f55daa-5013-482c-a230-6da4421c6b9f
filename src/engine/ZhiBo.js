import rest from 'axios';

export default class ZhiBo {
  static fetchActivationCodes = async (params = {}) => {
    return rest.post('/v2/live-helper/activation-code/search', params);
  }

  static refreshAndExpireActivationCode = async () => {
    return rest.post('/v2/live-helper/activation-code/refresh-expired');
  }

  static generateActivationCode = async (params = {}) => {
    return rest.post('/v2/live-helper/activation-code/generate', params);
  }

  static deleteActivationCode = async (id) => {
    return rest.delete(`/v2/live-helper/activation-code/${id}`);
  }

  static deprecateActivationCode = async (id) => {
    return rest.post(`/v2/live-helper/activation-code/deprecate/${id}`);
  }

  static renewalActivationCode = async (params = {}) => {
    return rest.post('/v2/live-helper/activation-code/renewal', params);
  }
}
