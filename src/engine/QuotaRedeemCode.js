import rest from 'axios';

export default class QuotaRedeemCode {
  static fetchQuotaRedeemCodes = async (partnerId, params) => {
    return rest.post(`/v2/chatbot/partner/quota-redeem-code/search/${partnerId}`, params);
  }

  static createQuotaRedeemCode = async (partnerId, params) => {
    return rest.post(`/v2/chatbot/partner/quota-redeem-code/${partnerId}`, params);
  }

  static deleteQuotaRedeemCode = async (itemId) => {
    return rest.delete(`/v2/chatbot/partner/quota-redeem-code/${itemId}`);
  }
}
