/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */
/* eslint-disable no-use-before-define */
/* eslint-disable no-array-constructor */
/* eslint-disable camelcase */
/* eslint-disable eqeqeq */
/* eslint-disable no-console */
/**
 * Copyright FunASR (https://github.com/alibaba-damo-academy/FunASR). All Rights
 * Reserved. MIT License  (https://opensource.org/licenses/MIT)
 */
/* 2021-2023 by zhaoming,mali aihealthx.com */

function WebSocketConnectMethod(config) { // 定义socket连接方法类
  let speechSokt;
  // let connKeeperID;
  const isfilemode = false;

  const { msgHandle } = config;
  const { stateHandle } = config;

  this.wsStart = function () {
    const Uri = 'wss://gr.bzy.ai:9443/';
    if (Uri.match(/wss:\S*|ws:\S*/)) {
      console.log(`Uri${Uri}`);
    } else {
      alert('请检查wss地址正确性');
      return 0;
    }

    // eslint-disable-next-line no-restricted-syntax
    if ('WebSocket' in window) {
      speechSokt = new WebSocket(Uri); // 定义socket连接对象
      speechSokt.onopen = function (e) { onOpen(e); }; // 定义响应函数
      speechSokt.onclose = function (e) {
        console.log('onclose ws!', e);
        // speechSokt.close();
        onClose(e);
      };
      speechSokt.onmessage = function (e) { onMessage(e); };
      speechSokt.onerror = function (e) { onError(e); };
      return 1;
    }

    alert('当前浏览器不支持 WebSocket');
    return 0;
  };

  // 定义停止与发送函数
  this.wsStop = function () {
    if (speechSokt != undefined) {
      console.log('stop ws!');
      speechSokt.close();
    }
  };

  this.wsSend = function (oneData) {
    if (speechSokt == undefined) return;
    if (speechSokt.readyState === 1) { // 0:CONNECTING, 1:OPEN, 2:CLOSING, 3:CLOSED
      speechSokt.send(oneData);
    }
  };

  // SOCEKT连接中的消息与状态响应
  function onOpen(e) {
    // 发送json
    const chunk_size = new Array(5, 8, 5);
    const request = {
      chunk_size,
      wav_name: 'h5',
      is_speaking: true,
      chunk_interval: 10,
      itn: false,
      mode: '2pass',
    };
    if (isfilemode) {
      request.wav_format = file_ext;
      if (file_ext == 'wav') {
        request.wav_format = 'PCM';
        request.audio_fs = file_sample_rate;
      }
    }

    const hotwords = '{}';

    if (hotwords != null) {
      request.hotwords = hotwords;
    }
    console.log(JSON.stringify(request));
    speechSokt.send(JSON.stringify(request));
    console.log('连接成功');
    stateHandle(0);
  }

  function onClose(e) {
    stateHandle(1);
  }

  function onMessage(e) {
    console.log(e);
    msgHandle(e);
  }

  function onError(e) {
    console.log(e);
    stateHandle(2);
  }
}

export default WebSocketConnectMethod;
