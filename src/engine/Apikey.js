import rest from 'axios';

export default class Apikey {
  static fetchApikeys = async (params = {}) => {
    return rest.get('/v2/chatbot/apikey/list', params);
  }

  static createApikey = async (params = {}) => {
    return rest.post('/v2/chatbot/apikey/new', params);
  }

  static updateApikey = async (params = {}) => {
    return rest.post(`/v2/chatbot/apikey/edit/${params?.id}`, params);
  }

  static updateApikeyStatus = async (params = {}) => {
    return rest.post(`/v2/chatbot/apikey/status/${params?.id}`, params);
  }

  static deleteApikey = async (id) => {
    return rest.delete(`/v2/chatbot/apikey/delete/${id}`);
  }
}
