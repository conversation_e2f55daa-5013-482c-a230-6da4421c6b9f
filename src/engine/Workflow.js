import rest from 'axios';

export default class Workflow {
  static createWorkflow = (params) => {
    params.version = 'v2'; // eslint-disable-line
    return rest.post('/v2/chatbot/workflows', params);
  }

  static fetchWorkflows = (params) => {
    params.version = 'v2'; // eslint-disable-line
    return rest.get('/v2/chatbot/workflows', { params });
  }

  static fetchNodes = () => {
    return rest.get('/v2/chatbot/workflow-v2/config/nodes');
  }

  static fetchInnerParams = () => {
    return rest.get('/v2/chatbot/workflow-v2/config/inner-params');
  }

  static fetchStartEndSchema = (flowId) => {
    return rest.get(`/v2/chatbot/workflow-v2/config/start_end_schema/${flowId}`);
  }

  static fetchPluginTools = () => {
    return rest.get('/v2/chatbot/workflow-v2/config/plugin-tools');
  }
}
