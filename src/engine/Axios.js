/* eslint-disable no-param-reassign */
import { StringExtension } from '~/plugins';
import axios from 'axios';
import _ from 'lodash';
import qs from 'qs';

import Engine from './Engine';
import Sessions from './Sessions';

const debug = require('debug')('admin/Axios');

export default class Axios {
  static init = () => {
    axios.mock = {};
    ['get', 'put', 'delete', 'post'].forEach((item) => {
      axios.mock[item] = (...args) => {
        return {
          returns: (val) => {
            // eslint-disable-next-line
            console.warn('request params:', args);
            // eslint-disable-next-line
            console.warn('request response:', val);
            return val;
          },
        };
      };
    });

    axios.interceptors.request.use((config) => {
      let ignoreLoading = config?.ignoreLoading || config?.params?.ignoreLoading;
      if (window.location.pathname === '/playground') { // ToDo(Ben) Ugly fix onBlur loading blocked ui
        ignoreLoading = true;
      }

      if (!ignoreLoading) {
        Engine.showLoading();
      }

      let { url } = config;
      if (!/^(https:|http:)?\/\//.test(url)) {
        url = `${Engine.getApiEndpoint()}${config.url}`;
      }

      const params = {};
      const headers = {};
      if (Sessions.getToken()) {
        headers['grpc-metadata-token'] = Sessions.getToken();
        headers['grpc-metadata-client-ver'] = '0.0.12';
      }

      _.merge(config, {
        headers,
        timeout: 60 * 1000 * 60,
        url,
        params,
        paramsSerializer: (innerParams) => {
          return qs.stringify(innerParams, { arrayFormat: 'repeat' });
        },
      });

      if (config.url.includes('/v2/') && !config.url.includes('/rpc-mesh/')) {
        if (!_.isEmpty(config.params)) {
          const newParams = {};
          delete config.params['pagination.orderBy'];
          if (config.params['pagination.pageIndex']) {
            newParams.skip = (config.params['pagination.pageIndex'] - 1) * config.params['pagination.pageSize'];
            delete config.params['pagination.pageIndex'];
          }
          if (!_.isUndefined(config.params['pagination.pageSize'])) {
            newParams.limit = config.params['pagination.pageSize'];
            delete config.params['pagination.pageSize'];
          }
          config.params = { ...newParams, ...StringExtension.camelToSnakeObj(config.params) };
        }

        if (config.method === 'post' && config.url.indexOf('/search') >= 0) {
          const pagination = {};
          delete config.data['pagination.orderBy'];
          if (config.data['pagination.pageIndex']) {
            pagination.skip = (config.data['pagination.pageIndex'] - 1) * config.data['pagination.pageSize'];
            delete config.data['pagination.pageIndex'];
          }
          if (!_.isUndefined(config.data['pagination.pageSize'])) {
            pagination.limit = config.data['pagination.pageSize'];
            delete config.data['pagination.pageSize'];
          }
          config.data = { ...StringExtension.camelToSnakeObj(config.data), pagination };
        } else if (!_.isEmpty(config?.data)) {
          config.data = StringExtension.camelToSnakeObj(config.data);
        }
      }

      return config;
    }, Axios.onError);

    axios.interceptors.response.use((response) => {
      const { config: { ignoreLoading } = {} } = response;
      if (!ignoreLoading) {
        Engine.hideLoading();
      }

      if (response?.config?.url?.includes('/v2/') && !response?.config?.url.includes('/rpc-mesh/')) {
        // 列表
        if (response?.data?.items && response?.data?.total) {
          const items = response?.data?.items.map((x) => { return StringExtension.snakeToCamelObj(x); });
          return {
            items,
            total: response?.data?.total,
            pagination: {
              pageIndex: (response?.data?.skip / response?.data?.limit) + 1,
              pageSize: response?.data?.limit,
            },
          };
        }

        return StringExtension.snakeToCamelObj(response?.data);
      }
      return response.data;
    }, Axios.onError);

    axios.getPagedListRecursively = async (url, config = {}, allItems = []) => {
      const defaultConfig = {
        itemsKey: 'items',
        params: {
          'pagination.pageIndex': 1,
          'pagination.pageSize': 1000,
        },
      };

      const mergedConfig = _.merge(defaultConfig, config);
      const result = await axios.get(url, mergedConfig);
      if (!_.isArray(result[config.itemsKey])) {
        throw new Error(`wrong itemsKey ${config.itemsKey}`);
      }

      const newAllItems = allItems.concat(result[config.itemsKey]);
      if (newAllItems.length < result.total) {
        mergedConfig.params['pagination.pageIndex']++;
        return axios.getPagedListRecursively(url, mergedConfig, newAllItems);
      }

      return newAllItems;
    };
  }

  static onError = (error = {}) => {
    debug('Response error', `${error}`, error.response);
    const response = error.response || {};
    const { config: { ignoreLoading, ignoreToast } = {} } = error;
    if (!ignoreLoading) {
      Engine.hideLoading();
    }

    switch (response.status) {
      case 401:
      case 403: {
        Sessions.clear();
        break;
      }
      case 404:
        Engine.showToast(Engine.i18n(`request.${response.status}`));
        break;
      case 400:
      case 500: {
        if (!ignoreToast) {
          let errorMessage = '';
          if (_.isEmpty(response.data.errors)) {
            errorMessage = response.data.error;
          } else {
            const firstErrorKey = Object.keys(response.data.errors)[0];
            errorMessage = `${firstErrorKey}: ${response.data.errors[firstErrorKey]}`;
          }

          Engine.showToast(errorMessage);
        }

        break;
      }
      default: {
        if (error.message) {
          if (error.message.includes('Network Error')) {
            Engine.showToast(Engine.i18n('request.networkUnavailable'));
          } else if (error.message.includes('timeout')) {
            Engine.showToast(Engine.i18n('request.networkTimeout'));
          }
        }
      }
    }

    return Promise.reject(error);
  }
}
