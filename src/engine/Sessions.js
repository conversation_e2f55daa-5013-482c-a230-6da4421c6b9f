import Consts from '~/consts';
import { StringExtension } from '~/plugins';
import Route from '~/route';
import EventEmitter from 'eventemitter3';
import _ from 'lodash';
import moment from 'moment';

import Accounts from './Accounts';
import Engine from './Engine';

export default class Sessions {
  static _option = {};
  static _eventEmitter = null;
  static _menuSetting = [];
  static _modelNames = {};
  static _schema = {
    temporaryToken: { isTemporary: true },
    token: {},
    language: {},
    profile: { isTemporary: true },
    messagePanelConfigs: {},
  }
  static _ROUTES = {
  };

  static init = async (option) => {
    Sessions._restoreOption(option);
    Sessions._eventEmitter = new EventEmitter();

    try {
      if (Sessions.getToken()) {
        const partner = await Accounts.getPartner();
        Sessions.setPartner(partner);
        Sessions.postLogin(partner?.permissions);
        const { partners, models, ...profile } = await Accounts.getMemberInfo();
        const quotas = {};
        const modelConfigs = {};
        const parameterRules = {};
        _.map(models, (v, k) => {
          return quotas[k] = _.map(v, (item) => {
            modelConfigs[item.model] = item;
            item.parameterRules.forEach((rule) => {
              parameterRules[rule.name] = rule?.help?.zh_Hans;
            })
            return item.model;
          });
        });
        const modelNameMap = {};
        _.flatten(_.values(models)).forEach((v) => {
          modelNameMap[v.model] = _.head(_.values(v.label));
        });

        Sessions.setProfile(profile);
        Sessions.setModels(quotas);
        Sessions.setModelConfigs(modelConfigs);
        Sessions.setParameterRules(StringExtension.snakeToCamelObj(parameterRules));
        Sessions._modelNames = modelNameMap;
        const funcs = await Accounts.fetchFuncTools();
        let tools = {};
        _.map(funcs, (v) => { tools = { ...tools, ...{ [v.name]: v.displayName } }; });
        Sessions.setFuncTools(StringExtension.camelToSnakeObj(tools));
      }
    } catch (e) {
      Sessions.clear();
      throw e;
    }
  }

  static login = (auth, remember) => {
    if (remember) {
      Sessions._option.token = auth.token;
      Engine.setItem('profile', auth.profile);
    } else {
      Sessions._option.temporaryToken = auth.token;
    }

    Sessions._option.profile = auth.profile;
  }

  static postLogin = async (permissions = []) => {
    let menus = _.isEmpty(permissions) ? [] : permissions;
    menus = Sessions._formatMarketMenus(menus);
    Sessions.setMenuSetting([...Route, ...menus]);
  }

  static getToken = () => {
    return Sessions._option.token || Sessions._option.temporaryToken;
  }

  static setProfile = (profile = {}) => {
    if (_.isEmpty(Sessions.getProfile())) {
      Sessions._option.profile = profile;
    }
  }

  static getProfile = () => {
    if (Sessions._option.profile) {
      return Sessions._option.profile;
    }

    Sessions._option.profile = Engine.getItem('profile');
    return Sessions._option.profile || {};
  }

  static setModels = (models = {}) => {
    if (_.isEmpty(Sessions.getModels())) {
      Sessions._option.models = models;
    }
    Engine.setItem('models', JSON.stringify(models));
  }

  static getModels = () => {
    if (Sessions._option.models) {
      return Sessions._option.models;
    }

    const models = Engine.getItem('models');
    Sessions._option.models = _.isEmpty(models) ? [] : JSON.parse(models);
    return Sessions._option.models || [];
  }

  static setModelConfigs = (modelConfigs = {}) => {
    Sessions._option.modelConfigs = modelConfigs;
  }

  static getModelConfigs = () => {
    return Sessions._option.modelConfigs || {};
  }

  static setParameterRules = (parameterRules = {}) => {
    Sessions._option.parameterRules = parameterRules;
  }

  static getParameterRules = () => {
    return Sessions._option.parameterRules || {};
  }

  static setPartner = (partner) => {
    if (_.isEmpty(Sessions.getPartner())) {
      Sessions._option.partner = partner;
    }
  }

  static getPartner = () => {
    if (Sessions._option.partner) {
      return Sessions._option.partner;
    }

    Sessions._option.partner = Engine.getItem('partner');
    return Sessions._option.partner || {};
  }

  static setFuncTools = (tools) => {
    if (_.isEmpty(Sessions.getFuncTools())) {
      Sessions._option.tools = tools;
    }
    Engine.setItem('tools', JSON.stringify(tools));
  }

  static getFuncTools = () => {
    if (!_.isEmpty(Sessions._option.tools)) {
      return Sessions._option.tools;
    }

    Sessions._option.tools = JSON.parse(Engine.getItem('tools') || '{}');
    return Sessions._option.tools || [];
  }

  static setMenuSetting = (menuSetting) => {
    Sessions._menuSetting = menuSetting;
    Sessions._eventEmitter.emit(Consts.MENU_CHANGE, menuSetting);
  }

  static getMenuSetting = () => {
    return Sessions._menuSetting;
  }

  static getModelNames = () => {
    return Sessions._modelNames;
  }

  static setMessagePanelConfigs = (newConfigs, type) => {
    const configs = { ...Sessions._option.messagePanelConfigs };
    if (type) {
      configs[type] = newConfigs;
      Sessions._option.messagePanelConfigs = configs;
    } else {
      Sessions._option.messagePanelConfigs = newConfigs;
    }
  }

  static getMessagePanelConfigs = (type) => {
    const configs = Sessions._option.messagePanelConfigs;
    if (!configs) {
      return undefined;
    } else if (type) {
      return configs[type];
    }

    return configs;
  }

  static getSlsLogData = (data) => {
    const { id, name } = Sessions.getPartner();
    return {
      event: `${data?.name}-${Sessions._ROUTES[data.path]}`,
      partnerid: id,
      username: name,
      APIVersion: '0.6.0',
      eventtime: `${moment().format('YYYYMMDDHHmmss')}`,
      properties: JSON.stringify(data),
    };
  }

  static clear = () => {
    Sessions._option.token = undefined;
    Sessions._option.profile = undefined;
    Sessions._option.temporaryToken = undefined;
    Sessions._option.messagePanelConfigs = undefined;
    Engine.logout();
  }

  static addEventListener = (eventName, listener) => {
    Sessions._eventEmitter.addListener(eventName, listener);
  }

  static removeEventListener = (eventName, listener) => {
    Sessions._eventEmitter.removeListener(eventName, listener);
  }

  static getHomePage = () => {
    if (!Sessions.getToken()) {
      return `/account/login${window.location.search}`;
    }

    const { partnerType } = Sessions.getProfile();
    if (partnerType !== 'playground') {
      return Consts.USER_HOMEPAGE[partnerType];
    }

    const getDefaultSelectKey = (menuSetting) => {
      if (_.isEmpty(menuSetting)) {
        return '';
      }

      const firstChild = menuSetting[0];
      if (firstChild.routeKey) {
        return firstChild.routeKey;
      }

      return getDefaultSelectKey(firstChild.subMenus);
    };

    return getDefaultSelectKey(Sessions.getMenuSetting());
  }

  static _restoreOption = (params) => {
    const option = { $data: {} };
    const config = {};
    for (const [key, op] of Object.entries(Sessions._schema)) {
      Object.defineProperty(option, key, {
        set(newValue) {
          option.$data[key] = newValue;
          Engine.setItem(key, newValue, op);
        },
        get() {
          return option.$data[key];
        },
      });

      config[key] = Engine.getItem(key, op);
    }

    Object.assign(option, config, params);

    Sessions._option = option;
  }

  static _formatMarketMenus = (menus = []) => {
    const usage = { icon: 'icon-usage', name: 'Token消耗', needAuth: true, routeKey: '/market-token-usage' };
    if (_.isEmpty(menus)) {
      return [usage];
    }

    const data = _.head(menus);
    if (_.isUndefined(data?.subMenus)) {
      data.subMenus = [{ name: data.name, icon: data.icon, routeKey: data.routeKey }, usage];
      data.name = '应用';
      return [data];
    }

    data.subMenus.push(usage);
    if (Sessions._option.partner?.isAdmin) {
      data.subMenus.push(
        { icon: 'icon-account', name: '成员管理', needAuth: true, routeKey: '/market-partner/member' },
      );
    }
    return [data];
  }
}
