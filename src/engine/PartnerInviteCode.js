import rest from 'axios';

export default class PartnerInviteCode {
  static fetchInviteCodes = async (partnerId, params) => {
    return rest.post(`/v2/chatbot/partner/invite-code/search/${partnerId}`, params);
  }

  static createInviteCode = async (partnerId, params) => {
    return rest.post(`/v2/chatbot/partner/invite-code/${partnerId}`, params);
  }

  static deleteInviteCode = async (itemId) => {
    return rest.delete(`/v2/chatbot/partner/invite-code/${itemId}`);
  }
}
