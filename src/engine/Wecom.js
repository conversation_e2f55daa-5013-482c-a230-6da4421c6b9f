import rest from 'axios';

export default class Wecom {
  static async fetchEmployees(params = { partner: 'xinzhixinxi' }) {
    return rest.get('/v2/rpc-mesh/wecom/employees', { params });
  }


  static addPersonQrcode = async (params) => {
    const result = await rest.post('/v2/rpc-mesh/wecom/employee/qrcodes?partner=xinzhixinxi', params);
    return result;
  }

  static delPersonQrcode = async (params) => {
    const result = await rest.delete(`/v2/rpc-mesh/wecom/employee/qrcodes/${params.id}`);
    return result;
  }

  static updatePersonQrcode = async (params) => {
    const result = await rest.put('/v2/rpc-mesh/wecom/employee/qrcode?partner=xinzhixinxi', params);
    return result;
  }

  static getPersonQrcode = async (params = {}) => {
    const result = await rest.get(`/v2/rpc-mesh/wecom/employee/qrcodes/${params.id}?partner=xinzhixinxi`);
    return result;
  }
}
