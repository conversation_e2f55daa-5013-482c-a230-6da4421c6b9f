import './index.less';

import { But<PERSON>, Form, Input, Spin } from 'antd';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { Mention, MentionsInput } from 'react-mentions';

import ReconnectingWebSocket from '../Playground/WebSocket';

export default class NodeTest extends Component {
  static propTypes = {
    location: PropTypes.object,
  }

  state = {
    isConnected: true, // false,
  }

  state = {
    info: {},
    constantEnum: [],
  }

  componentDidMount = async () => {
    const { key } = this.props.location.query;
    const path = `wss://fn.bzy.ai/v2/webcast/connect/${key}`;
    this.ws = new ReconnectingWebSocket(path, [], this.onReceiveMsg, () => {
      this.ws.send(JSON.stringify({ status: 'ready', from: 'iframe' }));
    });
  }

  onReceiveMsg = async (e) => {
    const obj = JSON.parse(e?.data);
    if (obj?.status === 'done') {
      this.setState({ isConnected: true });
    } else if (obj?.status === 'waiting') {
      this.setState({ isConnected: false });
    }

    if (obj.type === 'message') {
      const { flowConstant, nodeConstant } = obj.content;
      const constantEnum = [];
      (flowConstant || []).forEach((x) => { constantEnum.push({ id: x, display: x }); });
      (nodeConstant || []).forEach((x) => { constantEnum.push({ id: x.id, display: x.value }); });
      this.setState({ info: obj.content, constantEnum });
    }
  }

  onSubmit = () => {
    this.ws.send(JSON.stringify({ type: 'action', action: 'close', to: 'workflow' }));
  }

  renderLoading = () => {
    if (this.state.isConnected) return null;
    return (
      <div style={{ height: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <div>
          <Spin size="large" tip="设置初始化..." />
        </div>
      </div>
    );
  }

  render = () => {
    const { name } = this.state.info;
    return (
      <div className="base-node-container">
        {this.renderLoading()}
        <Form labelCol={{ span: 2 }}>
          <Form.Item label="节点名称">
            <Input value={name} />
          </Form.Item>
          <Form.Item label="提示词">
            <MentionsInput
              value={this.state.value}
              onChange={(e) => { return this.setState({ value: e.target.value }); }}
            >
              <Mention
                trigger="#"
                data={this.state.constantEnum}
              />
            </MentionsInput>
          </Form.Item>
        </Form>

        <Button onClick={() => { return this.onSubmit(); }}>保存</Button>
      </div>
    );
  };
}
