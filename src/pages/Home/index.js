import { Spin } from 'antd';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.login;
  },
  actions,
)
export default class MpArticle extends Component {
  static propTypes = {
    clearState: PropTypes.func.isRequired,
  }

  componentDidMount = async () => {
    // window.location.href = `${window.location.origin}/home.html`;
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  render = () => {
    return (
      <div style={{ height: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <div>
          <Spin size="large" tip="loading..." />
        </div>
      </div>
    );
  }
}

export {
  reducer,
};
