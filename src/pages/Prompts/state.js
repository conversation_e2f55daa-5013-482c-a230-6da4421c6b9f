import { Prompts } from '~/engine';

const SET_STATE = 'PROMPTS/SET_STATE';
const CLEAR_STATE = 'PROMPTS/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchAll = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination, filter } = getState().prompts;
    const searchParams = {
      ...filter,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
    };

    const { items, total } = await Prompts.fetchAll(searchParams);
    dispatch(
      setState({
        list: items,
        total,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
        },
      }),
    );
  };
};

export const create = (params) => {
  return async (dispatch) => {
    await Prompts.create(params);
    dispatch(fetchAll());
  };
};

export const update = (params) => {
  return async (dispatch) => {
    await Prompts.update(params);
    dispatch(fetchAll());
  };
};

export const updateVersion = (params) => {
  return async () => {
    await Prompts.updateVersion(params);
  };
};

export const del = (id) => {
  return async (dispatch) => {
    await Prompts.delete(id);
    dispatch(fetchAll());
  };
};

export const getVersions = (id) => {
  return async () => {
    const { items } = await Prompts.getVersions({ pagination: { skip: 0, limit: 100 }, id });
    return items;
  };
};

const _getInitState = () => {
  return {
    list: [],
    total: 0,
    filter: {},
    pagination: {
      pageIndex: 1,
      pageSize: 10,
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
