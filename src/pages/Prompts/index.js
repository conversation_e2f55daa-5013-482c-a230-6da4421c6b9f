import { markdown } from '@codemirror/lang-markdown';
import CodeMirror from '@uiw/react-codemirror';
import { FilterBar, PaginationTable, Toast } from '~/components';
import { OPENAI_PARAMS_MAX_VALUE, OPENAI_PARAMS_STEP } from '~/pages/Playground/Configs';
import {
  Button,
  Collapse,
  Divider,
  Drawer,
  Form,
  Input,
  InputNumber,
  Popconfirm,
  Radio,
  Select,
  Tag,
  Typography,
} from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { OPENAI_PARAMS } from '../Playground/Configs';
import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.prompts;
  },
  actions,
)
export default class Prompts extends Component {
  static propTypes = {
    list: PropTypes.array.isRequired,
    total: PropTypes.number.isRequired,
    pagination: PropTypes.object.isRequired,
    filter: PropTypes.object.isRequired,
    fetchAll: PropTypes.func.isRequired,
    create: PropTypes.func.isRequired,
    update: PropTypes.func.isRequired,
    del: PropTypes.func.isRequired,
    updateVersion: PropTypes.func.isRequired,
    getVersions: PropTypes.func.isRequired,
    setState: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    open: false,
    data: {},
    oldContent: '',
    versions: [],
  }

  componentDidMount = async () => {
    await this.props.fetchAll();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e?.target.value : e;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onShowVersion = async (data) => {
    const versions = await this.props.getVersions(data.id);
    this.setState({ openVersion: true, versions });
  }

  onSubmit = async () => {
    const { data, oldContent } = this.state;
    if (!data?.name || !data?.content) {
      Toast.show('请填写完整', Toast.Type.WARNING);
      return;
    }

    if (data?.id && oldContent !== data.content) {
      await this.props.updateVersion({ id: data.id, content: data.content });
    }

    if (data?.id) {
      await this.props.update({ ...data, content: null });
    } else {
      await this.props.create(data);
    }
    Toast.show('保存成功', Toast.Type.SUCCESS);
    this.setState({ open: false, data: {}, oldContent: '' });
  }

  renderOpenAIParams = (data = {}, onChange = () => { }) => {
    return (
      <>
        {
          _.map(OPENAI_PARAMS, (v, k) => {
            const numProps = OPENAI_PARAMS_MAX_VALUE[k] > 1 ?
              { min: 0, max: OPENAI_PARAMS_MAX_VALUE[k], step: OPENAI_PARAMS_STEP[k], value: data[k] } :
              { min: 0, max: 1, step: 0.1, value: data[k] };

            return (
              <Form.Item label={_.upperFirst(k)}>
                <InputNumber
                  {...numProps}
                  onChange={(e) => { return onChange(e, k); }}
                />
              </Form.Item>
            );
          })
        }
      </>
    );
  }

  renderDetailDrawer = () => {
    const { open, data } = this.state;
    return (
      <Drawer
        title="Detail"
        width="50vw"
        visible={open}
        onClose={() => { return this.setState({ open: false }); }}
        extra={<Button onClick={this.onSubmit}>保存</Button>}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 18 }} className="common-form">
          <Form.Item label="名称">
            <Input
              value={data?.name || ''}
              onChange={(e) => { return this.onChangeValue(e, 'name'); }}
            />
          </Form.Item>
          <Form.Item label="Prompt">
            <CodeMirror
              id="sourceCode"
              value={data?.content || ''}
              height="20vh"
              extensions={[markdown()]}
              onChange={(e) => { return this.onChangeValue(e, 'content'); }}
            />
          </Form.Item>
          <Form.Item label="公开">
            <Radio.Group
              value={data?.public}
              onChange={(e) => { return this.onChangeValue(e, 'public'); }}
            >
              <Radio value>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>
          {/* {this.renderOpenAIParams(data, this.onChangeValue)} */}
        </Form>
      </Drawer>
    );
  }

  renderPromptVersionDrawer = () => {
    const { openVersion, versions } = this.state;

    return (
      <Drawer
        title="历史版本"
        width="50vw"
        visible={openVersion}
        onClose={() => { return this.setState({ openVersion: false }); }}
      >
        <div style={{ position: 'relative' }}>
          {
            (versions || []).map((x) => {
              return (
                <Collapse>
                  <Collapse.Panel header={`Version: ${x.version}.0`} key={x.version}>
                    <Input.TextArea value={x.content} bordered={false} autoSize />
                  </Collapse.Panel>
                </Collapse>
              );
            })
          }
        </div>
      </Drawer>
    );
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
      { title: '名称', dataIndex: 'name', key: 'name' },
      {
        title: '内容',
        dataIndex: 'content',
        key: 'content',
        width: '50vw',
        render: (txt, row) => {
          return (
            <Typography.Link
              style={{ width: '45vw' }}
              ellipsis
              onClick={() => { return this.onShowVersion(row); }}
            >
              {txt}
            </Typography.Link>
          );
        },
      },
      {
        title: '公开',
        dataIndex: 'public',
        key: 'public',
        align: 'center',
        width: 120,
        render: (txt) => { return <Tag color={txt ? 'green' : 'red'}>{txt ? '是' : '否'}</Tag>; },
      },
      { title: '版本', dataIndex: 'lastVersionNo', key: 'lastVersionNo', width: 120, align: 'center' },
      {
        title: '操作',
        key: 'action',
        render: (txt, row) => {
          if (!row.editable) {
            return <a onClick={() => { return this.onShowVersion(row); }}>查看</a>;
          }

          return (
            <>
              <a onClick={() => { return this.setState({ open: true, data: row, oldContent: row.content }); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm title="确认删除?" onConfirm={() => { return this.props.del(row.id); }}>
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  renderSelects = () => {
    const { filter } = this.props;
    return [
      <Select
        placeholder="请选择"
        value={filter?.public}
        style={{ marginBottom: 15, width: 200 }}
        onChange={(value) => { this.props.setState({ filter: { ...filter, public: value } }); }}
      >
        <Select.Option>全部</Select.Option>
        <Select.Option value>公开</Select.Option>
        <Select.Option value={false}>私有</Select.Option>
      </Select>,
    ];
  }

  render = () => {
    const { filter } = this.props;

    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <FilterBar
          canAdd
          placeholder="请输入名称"
          searchKeyWords={filter?.name}
          renderSelects={this.renderSelects}
          onChange={(value) => { this.props.setState({ filter: { ...filter, name: value } }); }}
          onAdd={() => { return this.setState({ open: true, data: { public: false } }); }}
          onSearch={() => { return this.props.fetchAll({ pageIndex: 1 }); }}
        />
        <PaginationTable
          columns={this.renderColumns()}
          dataSource={this.props.list}
          totalDataCount={this.props.total}
          pagination={this.props.pagination}
          onPaginationChange={(pagination) => { return this.props.fetchAll(pagination); }}
        />

        {this.state.open && this.renderDetailDrawer()}
        {this.state.openVersion && this.renderPromptVersionDrawer()}
      </div>
    );
  }
}

export {
  reducer,
};
