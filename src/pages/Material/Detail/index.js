import { Typography } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.materialDetail;
  },
  actions,
)
export default class MaterialDetail extends Component {
  static propTypes = {
    detail: PropTypes.object.isRequired,
    getMaterial: PropTypes.func.isRequired,
    location: PropTypes.object.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    texts: [],
    currentIdx: 0,
  }

  componentDidMount = async () => {
    const { id } = this.props.location.query;
    await this.props.getMaterial(id);
    this.setState({
      texts: JSON.parse(this.props.detail.content),
    });
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onClickText = (item) => {
    const startTime = item.begin_time / 1000;
    this.audio.currentTime = startTime;
    this.audio.play();
  }

  onTimeUpdate = (e) => {
    const time = _.ceil(e.target.currentTime * 1000);
    const { texts } = this.state;
    const currentIdx = texts.findIndex((item) => {
      return time >= item.begin_time && time <= item.end_time;
    });
    if (currentIdx !== -1 && currentIdx !== this.state.currentIdx) {
      this.setState({ currentIdx });
    }
  }

  render = () => {
    const { texts, currentIdx } = this.state;
    const { detail } = this.props;
    return (
      <div style={{ padding: 30 }}>
        <Typography.Title level={4}>{detail.title}</Typography.Title>

        <div style={{ marginBottom: 30 }}>
          {
            texts.map((item, index) => {
              return (
                <>
                  <Typography.Text
                    key={item.begin_time}
                    style={{ paddingBottom: 10 }}
                    strong={index === currentIdx}
                    type={index === currentIdx ? 'danger' : undefined}
                    onClick={() => { return this.onClickText(item); }}
                  >
                    {item.text}
                  </Typography.Text>
                  <br />
                </>
              );
            })
          }
        </div>
        <audio
          controls
          src={detail.sourceUrl}
          onTimeUpdate={this.onTimeUpdate}
          ref={(ref) => { this.audio = ref; }}
          style={{ position: 'fixed', bottom: 0, left: 0, width: '100%' }}
        />
      </div>
    );
  }
}

export {
  reducer,
};
