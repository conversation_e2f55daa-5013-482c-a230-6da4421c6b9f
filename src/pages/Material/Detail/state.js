import { Materials } from '~/engine';

const SET_STATE = 'MATERIAL_DETAIL/SET_STATE';
const CLEAR_STATE = 'MATERIAL_DETAIL/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const getMaterial = (id) => {
  return async (dispatch) => {
    const detail = await Materials.getOpenMaterial(id);
    dispatch(setState({ detail }));
  };
};

const _getInitState = () => {
  return {
    detail: {},
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
