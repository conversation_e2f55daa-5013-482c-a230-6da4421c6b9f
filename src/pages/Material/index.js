import { FilterBar } from '~/components';
import { Platform } from '~/plugins';
import { DatePicker, Select, Table } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.materials;
  },
  actions,
)
export default class Materials extends Component {
  static propTypes = {
    total: PropTypes.number.isRequired,
    list: PropTypes.array.isRequired,
    authorMap: PropTypes.object.isRequired,
    pagination: PropTypes.object.isRequired,
    searchParams: PropTypes.object.isRequired,
    fetchMaterials: PropTypes.func.isRequired,
    fetchMaterialSource: PropTypes.func.isRequired,
    setState: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
    match: PropTypes.object.isRequired,
  }

  state = {
  }

  componentDidMount = async () => {
    const { id } = this.props.match.params;
    await this.props.setState({ uuid: id });
    await this.props.fetchMaterials({});
    await this.props.fetchMaterialSource();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeSearchParams = async (key, value) => {
    const { searchParams } = this.props;
    searchParams[key] = value;
    await this.props.setState({ searchParams });
  }

  renderSelects = () => {
    const { startAt, endAt, authors } = this.props.searchParams;
    return [
      <DatePicker.RangePicker
        style={{ width: 260, marginRight: 16, marginBottom: 16 }}
        value={_.isUndefined(startAt) ? [] : [moment(startAt), moment(endAt)]}
        onChange={async (e) => {
          const [startTime, endTime] = e || [];
          await this.props.setState({
            searchParams: {
              ...this.props.searchParams,
              startAt: startTime?.format('YYYY-MM-DDT00:00:00.SSSSSS') || undefined,
              endAt: endTime?.format('YYYY-MM-DDT23:59:59.SSSSSS') || undefined,
            },
          });
          await this.props.fetchMaterials(this.props.searchParams);
        }}
      />,
      <Select
        style={{ width: 260, marginRight: 16, marginBottom: 16 }}
        placeholder="请选择作者"
        mode="multiple"
        allowClear
        value={authors}
        onChange={async (value) => {
          await this.props.setState({ searchParams: { ...this.props.searchParams, authors: value } });
        }}
      >
        {
          _.map(this.props.authorMap, (v, k) => {
            return <Select.Option value={k}>{v}</Select.Option>;
          })
        }
      </Select>,
    ];
  }

  renderColumns = () => {
    let pcColumns = [];
    if (!Platform.isMobile()) {
      pcColumns = [
        {
          title: '发布时间',
          dataIndex: 'pubDate',
          key: 'pubDate',
          align: 'center',
          render: (text) => { return moment(text).format('YYYY-MM-DD HH:mm:ss'); },
        },
      ];
    }

    return [
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        render: (txt, row) => {
          return (
            <>
              <a style={{ fontWeight: 'bold', marginBottom: 5 }}>{txt}</a>
              {
                row.meta['摘要'] &&
                <div>
                  摘要: {row.meta['摘要']}
                </div>
              }
              {
                !_.isEmpty(row.meta['话题']) &&
                <div>
                  话题: {_.map(row.meta['话题'], (v) => { return v; }).join('、')}
                </div>
              }
            </>
          );
        },
      },
      { title: '作者', dataIndex: 'author', key: 'author' },
      ...pcColumns,
    ];
  }

  render = () => {
    const { searchParams, total, list, pagination } = this.props;
    return (
      <div style={{ padding: Platform.isMobile() ? 5 : 30 }}>
        <FilterBar
          shouldShowSearchInput
          searchKeyWords={searchParams.title}
          onChange={(value) => {
            this.props.setState({ searchParams: { ...this.props.searchParams, title: value } });
          }}
          onSearch={() => { return this.props.fetchMaterials(this.props.searchParams); }}
          placeholder="请输入标题"
          renderSelects={this.renderSelects}
        />
        <Table
          onRow={(row) => {
            return {
              onClick: () => { return this.$push('/live/materials/detail', { id: row.id }); },
            };
          }}
          size={Platform.isMobile() ? 'small' : 'middle'}
          totalDataCount={total}
          dataSource={list}
          pagination={pagination}
          columns={this.renderColumns()}
        />
      </div>
    );
  }
}

export {
  reducer,
};
