import { Materials } from '~/engine';

const SET_STATE = 'MATERIALS/SET_STATE';
const CLEAR_STATE = 'MATERIALS/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchMaterialSource = () => {
  return async (dispatch, getState) => {
    const { uuid } = getState().materials;
    let { items } = await Materials.fetchOpenMaterialSource(uuid);
    items = items.filter((x) => { return x.platform === '抖音直播' || x.platform === '视频号直播'; });
    const authorMap = {};
    items.forEach((item) => { authorMap[item.id] = item.author; });
    dispatch(setState({ authorMap }));
  };
};

export const fetchMaterials = (params) => {
  return async (dispatch, getState) => {
    const { pagination, uuid } = getState().materials;
    const searchParams = {
      ...params,
      uuid,
      platform: '抖音直播',
      fetchStatus: 'done',
      sourceIds: params.authors || [],
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const { items, total } = await Materials.fetchOpenMaterials(searchParams);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

const _getInitState = () => {
  return {
    total: 0,
    authorMap: {},
    searchParams: {
      title: '',
      startAt: undefined,
      endAt: undefined,
    },
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    list: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
