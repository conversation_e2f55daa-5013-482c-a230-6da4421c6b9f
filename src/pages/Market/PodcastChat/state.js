import { Accounts, ChatBot, Market } from '~/engine';
import _ from 'lodash';

// import Azure from '../VoiceClone/Detail/azure';

const SET_STATE = 'MARKET_VOICE_TTS/SET_STATE';
const CLEAR_STATE = 'MARKET_VOICE_TTS/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchVoiceTrain = () => {
  return async (dispatch) => {
    const items = await ChatBot.fetchAllVoiceTrain();
    const speakers = await Market.fetchVoiceCloneSpeakers();
    const azureSpeakers = await Accounts.fetchTtsSpeakers();
    const tmpSpeakers = [
      ...azureSpeakers,
      ..._.values(items).map((item) => {
        return {
          label: item.name,
          value: item.id,
          ttsSettings: { provider: item.provider, voice: item.voiceId },
        };
      }),
      ..._.values(speakers).map((item) => {
        return {
          label: item.speaker,
          value: item.speaker,
          ttsSettings: { provider: 'gpt-sovits', voice: item.speaker, ref: item.refs[0] },
        };
      }),
    ];

    const groupedSpeakers = _.groupBy(tmpSpeakers, (item) => {
      return item.ttsSettings.provider;
    });

    dispatch(setState({ tmpSpeakers, groupedSpeakers }));
  };
};

export const textToAudio = (text, ttsSettings) => {
  return async () => {
    const data = await Market.textToAudio({ text, ttsSettings });
    return data;
  };
};

const _getInitState = () => {
  return {
    tmpSpeakers: [],
    groupedSpeakers: {},
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
