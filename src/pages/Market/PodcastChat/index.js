import './index.less';

import { <PERSON><PERSON>, Card, Col, Input, List, Row, Select, Spin, Tag, Typography, message } from 'antd';
import _ from 'lodash';
import { PropTypes } from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

const { TextArea } = Input;
const { Title, Text } = Typography;
@connect(
  (state) => {
    return state.marketPodcastChat;
  },
  actions,
)
export default class PodcastChat extends Component {
  static propTypes = {
    textToAudio: PropTypes.func.isRequired,
    fetchVoiceTrain: PropTypes.func.isRequired,
    tmpSpeakers: PropTypes.array,
    groupedSpeakers: PropTypes.array,
  };

  constructor(props) {
    super(props);
    this.state = {
      content: '',
      processedLines: [],
      speakerInfo: {},
      loading: false,
      selectedSpeakers: {}, // 存储每个说话者选择的speaker
      generatingAudio: {}, // 存储每个说话者的音频生成状态
      audioUrls: {}, // 存储生成的音频URL
      isAutoPlaying: false, // 是否自动连续播放
      currentPlayingIndex: -1, // 当前正在播放的音频索引
    };

    this.audioRefs = {}; // 存储音频元素的引用
  }

  async componentDidMount() {
    await this.props.fetchVoiceTrain();
  }

  handleContentChange = (e) => {
    this.setState({ content: e.target.value });
  };

  handleSpeakerSelect = (value, name) => {
    this.setState((prevState) => {
      return {
        selectedSpeakers: {
          ...prevState.selectedSpeakers,
          [name]: value,
        },
      };
    });
  };

  analyzeText = () => {
    const { content } = this.state;
    this.setState({ loading: true });

    try {
      // 使用提供的逻辑处理文本
      let text = content.replace(/\n\n/g, '\n');
      text = text.replace(/:\n/g, ':');
      text = text.replace(/：\n/g, '：');
      const lines = text.split('\n').filter((x) => { return !_.isEmpty(x); });
      const names = lines.map((line) => { return _.trim(line.split(/[：:]/)[0] || ''); });
      const usernames = _.uniq(names).filter((name) => { return !_.isEmpty(name); });

      this.setState({
        processedLines: lines,
        speakerInfo: {
          usernames,
        },
        loading: false,
      });
    } catch (error) {
      console.error('分析文本时出错:', error); // eslint-disable-line
      this.setState({ loading: false });
    }
  };

  generateAudio = async (name, text) => {
    const { selectedSpeakers } = this.state;
    const { tmpSpeakers, textToAudio } = this.props;

    if (!selectedSpeakers[name]) {
      message.warning(`请先为${name}选择声音`);
      return;
    }

    const selectedSpeakerValue = selectedSpeakers[name];
    const speakerConfig = tmpSpeakers.find((speaker) => {
      return (speaker.value || speaker) === selectedSpeakerValue;
    });

    if (!speakerConfig || !speakerConfig.ttsSettings) {
      message.error('所选声音配置有误');
      return;
    }

    const audioKey = `${name}-${text}`;

    this.setState((prevState) => {
      return {
        generatingAudio: {
          ...prevState.generatingAudio,
          [audioKey]: true,
        },
      };
    });

    try {
      const audioData = await textToAudio(text, speakerConfig.ttsSettings);

      if (audioData && audioData.fileUrl) {
        this.setState((prevState) => {
          return {
            audioUrls: {
              ...prevState.audioUrls,
              [audioKey]: audioData.fileUrl,
            },
            generatingAudio: {
              ...prevState.generatingAudio,
              [audioKey]: false,
            },
          };
        });
        message.success(`${name}的音频生成成功`);
      } else {
        throw new Error('生成音频失败，未返回URL');
      }
    } catch (error) {
      message.error(`${name}的音频生成失败: ${error.message || '未知错误'}`);
      this.setState((prevState) => {
        return {
          generatingAudio: {
            ...prevState.generatingAudio,
            [audioKey]: false,
          },
        };
      });
    }
  };

  generateAudioForSpeaker = async (name) => {
    const { processedLines } = this.state;
    const speakerLines = processedLines.filter((line) => {
      const parts = line.split(/[：:]/);
      const speaker = parts[0] ? _.trim(parts[0]) : '';
      return speaker === name;
    });

    for (const line of speakerLines) {
      const parts = line.split(/[：:]/);
      const content = parts.slice(1).join(':');
      await this.generateAudio(name, content); // eslint-disable-line no-await-in-loop
    }
  };

  generateAllAudio = async () => {
    const { processedLines, selectedSpeakers } = this.state;
    const speakerContents = {};

    // 按说话者分组内容
    processedLines.forEach((line) => {
      const parts = line.split(/[：:]/);
      const speaker = parts[0] ? _.trim(parts[0]) : '';
      const content = parts.slice(1).join(':');

      if (speaker && selectedSpeakers[speaker]) {
        if (!speakerContents[speaker]) {
          speakerContents[speaker] = [];
        }
        speakerContents[speaker].push(content);
      }
    });

    // 为每个说话者生成音频
    for (const [speaker, contents] of Object.entries(speakerContents)) {
      for (const content of contents) {
        await this.generateAudio(speaker, content); // eslint-disable-line no-await-in-loop
      }
    }
  };

  // 处理音频播放结束事件
  handleAudioEnd = (audioKey, index) => {
    const { processedLines, isAutoPlaying } = this.state;

    // 如果自动播放关闭，或者已经是最后一个音频，不执行后续操作
    if (!isAutoPlaying) return;

    // 获取下一个索引
    const nextIndex = index + 1;

    // 如果还有下一个音频，自动播放
    if (nextIndex < processedLines.length) {
      this.playAudioAtIndex(nextIndex);
    } else {
      // 没有下一个音频了，停止自动播放
      this.setState({
        isAutoPlaying: false,
        currentPlayingIndex: -1,
      });
      message.success('所有音频播放完毕');
    }
  };

  // 播放指定索引的音频
  playAudioAtIndex = (index) => {
    const { processedLines, audioUrls } = this.state;

    if (index < 0 || index >= processedLines.length) return;

    const line = processedLines[index];
    const parts = line.split(/[：:]/);
    const speaker = parts[0] ? _.trim(parts[0]) : '';
    const content = parts.slice(1).join(':');
    const audioKey = `${speaker}-${content}`;

    // 如果该行有音频URL
    if (audioUrls[audioKey] && this.audioRefs[audioKey]) {
      // 停止当前正在播放的音频
      this.stopCurrentPlayingAudio();

      // 设置当前播放索引
      this.setState({ currentPlayingIndex: index });

      // 播放新的音频
      const audioElement = this.audioRefs[audioKey];
      audioElement.currentTime = 0; // 从头开始播放
      audioElement.play().catch((err) => {
        console.error('播放音频失败:', err); // eslint-disable-line
        message.error('播放音频失败');
        this.handleAudioEnd(audioKey, index); // 尝试播放下一个
      });
    } else {
      // 如果该行没有音频，跳过
      this.handleAudioEnd(audioKey, index);
    }
  };

  // 停止当前播放的音频
  stopCurrentPlayingAudio = () => {
    const { currentPlayingIndex, processedLines } = this.state;

    if (currentPlayingIndex >= 0 && currentPlayingIndex < processedLines.length) {
      const line = processedLines[currentPlayingIndex];
      const parts = line.split(/[：:]/);
      const speaker = parts[0] ? _.trim(parts[0]) : '';
      const content = parts.slice(1).join(':');
      const audioKey = `${speaker}-${content}`;

      if (this.audioRefs[audioKey]) {
        this.audioRefs[audioKey].pause();
      }
    }
  };

  // 开始自动播放
  startAutoPlay = () => {
    this.setState({ isAutoPlaying: true }, () => {
      // 从第一个有音频的索引开始播放
      const { processedLines, audioUrls } = this.state;

      for (let i = 0; i < processedLines.length; i++) {
        const line = processedLines[i];
        const parts = line.split(/[：:]/);
        const speaker = parts[0] ? _.trim(parts[0]) : '';
        const content = parts.slice(1).join(':');
        const audioKey = `${speaker}-${content}`;

        if (audioUrls[audioKey]) {
          this.playAudioAtIndex(i);
          break;
        }
      }
    });
  };

  // 停止自动播放
  stopAutoPlay = () => {
    this.stopCurrentPlayingAudio();
    this.setState({
      isAutoPlaying: false,
      currentPlayingIndex: -1,
    });
  };

  renderSpeakerInfo = () => {
    const { speakerInfo, selectedSpeakers } = this.state;
    const { usernames } = speakerInfo;
    const { groupedSpeakers } = this.props;

    if (!usernames || usernames.length === 0) {
      return null;
    }

    return (
      <Card title="发言者信息" style={{ marginBottom: 16 }}>
        <List
          dataSource={usernames}
          renderItem={(name) => {
            // 获取该说话者在处理后文本中的数量
            const lineCount = this.state.processedLines.filter((line) => {
              const parts = line.split(/[：:]/);
              const speaker = parts[0] ? _.trim(parts[0]) : '';
              return speaker === name;
            }).length;

            return (
              <List.Item>
                <Row style={{ width: '100%' }} align="middle">
                  <Col span={4}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Text strong style={{ marginRight: 12 }}>{name}</Text>
                      <Tag color="blue">{lineCount}句</Tag>
                    </div>
                  </Col>
                  <Col span={10}>
                    <Select
                      style={{ width: 200 }}
                      placeholder="选择声音"
                      value={selectedSpeakers[name]}
                      onChange={(value) => { return this.handleSpeakerSelect(value, name); }}
                    >
                      {groupedSpeakers && Object.entries(groupedSpeakers).map(([provider, speakers]) => {
                        return (
                          <Select.OptGroup key={provider} label={provider}>
                            {Array.isArray(speakers) && speakers.map((speaker) => {
                              return (
                                <Select.Option key={speaker.value} value={speaker.value}>
                                  {speaker.label}
                                </Select.Option>
                              );
                            })}
                          </Select.OptGroup>
                        );
                      })}
                    </Select>
                  </Col>

                </Row>
              </List.Item>
            );
          }}
        />
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Button
            type="primary"
            onClick={this.generateAllAudio}
          >
            批量生成所有音频
          </Button>
        </div>
      </Card>
    );
  };

  renderProcessedLines = () => {
    const { processedLines, audioUrls, generatingAudio } = this.state;

    if (processedLines.length === 0) {
      return null;
    }

    return (
      <Card title="处理后的文本行">
        <List
          dataSource={processedLines}
          renderItem={(line, index) => {
            const parts = line.split(/[：:]/);
            const speaker = parts[0] ? _.trim(parts[0]) : '';
            const content = parts.slice(1).join(':');
            const audioKey = `${speaker}-${content}`;

            return (
              <List.Item>
                <Row style={{ width: '100%' }}>
                  <Col span={4}>
                    <Text strong>{speaker}</Text>
                  </Col>
                  <Col span={16}>
                    <Text>{content}</Text>
                  </Col>
                  <Col span={4}>
                    {audioUrls[audioKey] ? (
                      <audio
                        controls
                        src={audioUrls[audioKey]}
                        style={{ width: '100%' }}
                        ref={(ref) => { this.audioRefs[audioKey] = ref; }}
                        onEnded={() => { this.handleAudioEnd(audioKey, index); }}
                      />
                    ) : (
                      <Button
                        type="primary"
                        loading={generatingAudio[audioKey]}
                        onClick={() => { return this.generateAudio(speaker, content); }}
                      >
                        生成音频
                      </Button>
                    )}
                  </Col>
                </Row>
              </List.Item>
            );
          }}
        />
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Button
            type="primary"
            onClick={this.startAutoPlay}
            disabled={this.state.isAutoPlaying}
          >
            自动播放
          </Button>
          <Button
            style={{ marginLeft: 8 }}
            onClick={this.stopAutoPlay}
            disabled={!this.state.isAutoPlaying}
          >
            停止播放
          </Button>
        </div>
      </Card>
    );
  };

  render() {
    const { content, loading } = this.state;

    return (
      <div className="podcast-chat-container">
        <Title level={3}>播客文本分析</Title>

        <Card title="输入文本" style={{ marginBottom: 16 }}>
          <TextArea
            value={content}
            onChange={this.handleContentChange}
            placeholder={'请输入要分析的文本，格式为" 说话者：内容"'}
            autoSize={{ minRows: 5, maxRows: 10 }}
            className="podcast-textarea"
          />
          <div style={{ marginTop: 16, textAlign: 'right' }}>
            <Button
              type="primary"
              onClick={this.analyzeText}
              loading={loading}
            >
              分析
            </Button>
          </div>
        </Card>

        {loading ? (
          <div style={{ textAlign: 'center', margin: '20px 0' }}>
            <Spin tip="正在分析..." />
          </div>
        ) : (
          <>
            {this.renderSpeakerInfo()}
            {this.renderProcessedLines()}
          </>
        )}
      </div>
    );
  }
}

export {
  reducer,
};
