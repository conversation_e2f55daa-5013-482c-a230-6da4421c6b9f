import { PaginationTable } from '~/components';
import { <PERSON><PERSON>, <PERSON><PERSON>r, Input<PERSON><PERSON>ber, Modal, Popconfirm, Tag } from 'antd';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

const STATUS_MAP = {
  available: '未激活',
  used: '已激活',
  expired: '账号已过期',
  unavailable: '账号停用',
};

const STATUS_COLOR_MAP = {
  available: 'green',
  used: 'blue',
  expired: 'red',
  unavailable: 'gray',
};
@connect(
  (state) => {
    return state.marketZhiBo;
  },
  actions,
)
export default class ZhiBo extends Component {
  static propTypes = {
    list: PropTypes.array.isRequired,
    total: PropTypes.number.isRequired,
    pagination: PropTypes.object.isRequired,
    fetchActivationCodes: PropTypes.func.isRequired,
    refreshAndExpireActivationCode: PropTypes.func.isRequired,
    generateActivationCode: PropTypes.func.isRequired,
    deleteActivationCode: PropTypes.func.isRequired,
    deprecateActivationCode: PropTypes.func.isRequired,
    renewalActivationCode: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  componentDidMount = async () => {
    await this.props.refreshAndExpireActivationCode();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onGenCode = async () => {
    Modal.info({
      title: '账号激活后使用时长:',
      closable: true,
      content: (
        <InputNumber
          min={1}
          step={10}
          max={9999}
          defaultValue={30}
          placeholder="请输入时长"
          style={{ width: '100%' }}
          addonAfter="天"
          ref={(el) => { this.refTopic = el; }}
        />
      ),
      okText: '创建',
      onOk: async () => {
        await this.props.generateActivationCode({ expireDays: this.refTopic.value });
      },
    });
  }

  onRenewalActivationCode = async (id, expireDays) => {
    Modal.info({
      title: '续期时长:',
      closable: true,
      content: (
        <InputNumber
          min={1}
          step={10}
          max={9999}
          defaultValue={expireDays}
          placeholder="请输入时长"
          style={{ width: '100%' }}
          addonAfter="天"
          ref={(el) => { this.refTopic = el; }}
        />
      ),
      okText: '续期',
      onOk: async () => { await this.props.renewalActivationCode({ id, expireDays: this.refTopic.value }); },
    });
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', width: 80, align: 'center' },
      { title: '激活码', dataIndex: 'code', key: 'code', align: 'center' },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        align: 'center',
        render: (t) => { return <Tag color={STATUS_COLOR_MAP[t]}>{STATUS_MAP[t]}</Tag>; },
      },
      { title: '激活人', dataIndex: 'activatedBy', key: 'activatedBy', align: 'center' },
      {
        title: '首次激活时间',
        dataIndex: 'firstActivatedAt',
        key: 'firstActivatedAt',
        align: 'center',
        render: (t) => { return t ? moment(t).format('YYYY-MM-DD HH:mm:ss') : '-'; },
      },
      {
        title: '过期时间',
        dataIndex: 'expiredAt',
        key: 'expiredAt',
        align: 'center',
        render: (t) => { return moment(t).format('YYYY-MM-DD HH:mm:ss'); },
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (t) => { return moment(t).format('YYYY-MM-DD HH:mm:ss'); },
      },
      {
        title: 'Action',
        key: 'action',
        render: (text, row) => {
          const isActive = row.status === 'used' || row.status === 'expired';
          return (
            <>
              <Popconfirm title="确定删除吗?" onConfirm={() => { return this.props.deleteActivationCode(row.id); }}>
                <a>删除</a>
              </Popconfirm>
              <Divider type="vertical" />
              {
                row.status !== 'unavailable' ?
                  <Popconfirm
                    title="确定停用吗?"
                    onConfirm={() => { return this.props.deprecateActivationCode(row.id); }}
                  >
                    <a>停用</a>
                  </Popconfirm> :
                  <a disabled>停用</a>
              }
              <Divider type="vertical" />
              <a
                disabled={!isActive}
                onClick={() => {
                  if (isActive) {
                    this.onRenewalActivationCode(row.id, row.expire_days);
                  }
                }}
              >续期
              </a>
            </>
          );
        },
      },
    ];
  }

  render = () => {
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <Button type="primary" onClick={() => { return this.onGenCode(); }}>生成激活码</Button>
        <PaginationTable
          dataSource={this.props.list}
          totalDataCount={this.props.total}
          pagination={this.props.pagination}
          columns={this.renderColumns()}
          onPaginationChange={this.props.fetchActivationCodes}
        />
      </div>
    );
  }
}

export {
  reducer,
};
