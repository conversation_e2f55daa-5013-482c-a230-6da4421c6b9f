import { ZhiBo } from '~/engine';

const SET_STATE = 'mpArticle/SET_STATE';
const CLEAR_STATE = 'mpArticle/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchActivationCodes = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().marketZhiBo;

    const searchParams = {
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const { items, total } = await ZhiBo.fetchActivationCodes(searchParams);
    dispatch(
      setState({
        list: items,
        total,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const refreshAndExpireActivationCode = (id, expireDays) => {
  return async (dispatch) => {
    await ZhiBo.refreshAndExpireActivationCode(id, expireDays);
    dispatch(fetchActivationCodes());
  };
};

export const generateActivationCode = (params) => {
  return async (dispatch) => {
    await ZhiBo.generateActivationCode(params);
    dispatch(fetchActivationCodes());
  };
};

export const deleteActivationCode = (id) => {
  return async (dispatch) => {
    await ZhiBo.deleteActivationCode(id);
    dispatch(fetchActivationCodes());
  };
};

export const deprecateActivationCode = (id) => {
  return async (dispatch) => {
    await ZhiBo.deprecateActivationCode(id);
    dispatch(fetchActivationCodes());
  };
};

export const renewalActivationCode = (params) => {
  return async (dispatch) => {
    await ZhiBo.renewalActivationCode(params);
    dispatch(fetchActivationCodes());
  };
};


const _getInitState = () => {
  return {
    list: [],
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
