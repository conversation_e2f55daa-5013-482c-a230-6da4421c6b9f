import Configs from '~/consts';
import { Whatsapp } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'WHATAPP/SET_STATE';
const CLEAR_STATE = 'WHATAPP/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchAccounts = () => {
  return async (dispatch) => {
    const { items } = await Whatsapp.fetchAccounts();
    const account = _.head(items);
    const { wabaOpenid } = account;
    const phoneObj = await Whatsapp.fetchPhones({ wabaOpenid, ...Configs.ALL_PAGE_PARAMS });
    const phones = phoneObj.items;
    const phoneOpenid = _.head(phones)?.phoneOpenid;
    dispatch(setState({ account, phones, wabaOpenid, phoneOpenid }));
  };
};

export const updateAccount = (params) => {
  return async (dispatch) => {
    await Whatsapp.updateAccount(params);
    dispatch(fetchAccounts());
  };
};

export const updatePhone = (params) => {
  return async (dispatch) => {
    await Whatsapp.updatePhone(params);
    dispatch(fetchAccounts());
  };
};

export const fetchChannels = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination, wabaOpenid, phoneOpenid } = getState().marketWhatsapp;

    const searchParams = {
      wabaOpenid,
      phoneOpenid,
      name: params?.name,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const { items, total } = await Whatsapp.fetchChannels(searchParams);
    dispatch(setState({
      list: items,
      total,
      pagination: {
        pageIndex: searchParams['pagination.pageIndex'],
        pageSize: searchParams['pagination.pageSize'],
        orderBy: searchParams['pagination.orderBy'],
      },
    }));
  };
};

export const addChannel = (params) => {
  return async (dispatch, getState) => {
    const { wabaOpenid, phoneOpenid } = getState().marketWhatsapp;
    await Whatsapp.addChannel({ ...params, wabaOpenid, phoneOpenid });
    dispatch(fetchChannels());
  };
};

export const updateChannel = (params) => {
  return async (dispatch) => {
    await Whatsapp.updateChannel(params);
    dispatch(fetchChannels());
  };
};

export const deleteChannel = (id) => {
  return async (dispatch) => {
    await Whatsapp.deleteChannel(id);
    dispatch(fetchChannels());
  };
};

export const fetchConversations = (params = {}) => {
  return async (dispatch, getState) => {
    const { conversationPagination } = getState().marketWhatsapp;

    const searchParams = {
      channelId: params?.channelId,
      'pagination.pageIndex': params.pageIndex || conversationPagination.pageIndex,
      'pagination.pageSize': params.pageSize || conversationPagination.pageSize,
      'pagination.orderBy': params.orderBy || conversationPagination.orderBy,
    };
    const { items, total } = await Whatsapp.fetchConversations(searchParams);
    dispatch(setState({
      conversations: items,
      conversationTotal: total,
      conversationPagination: {
        pageIndex: searchParams['pagination.pageIndex'],
        pageSize: searchParams['pagination.pageSize'],
        orderBy: searchParams['pagination.orderBy'],
      },
    }));
  };
};

export const fetchMessages = (params) => {
  return async () => {
    const result = await Whatsapp.fetchMessages(params);
    return result;
  };
};

const _getInitState = () => {
  return {
    account: {},
    phones: [],
    list: [],
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    conversations: [],
    conversationTotal: 0,
    conversationPagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
