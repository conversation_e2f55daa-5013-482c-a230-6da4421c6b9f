import { FilterBar, PaginationTable, Toast } from '~/components';
import Configs from '~/consts';
import { ChatBot } from '~/engine';
import { Button, Divider, Drawer, Form, Input, Popconfirm, Select, Switch, Tabs } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import AccountDrawer from './components/AccountDrawer';
import ConversationDrawer from './components/ConversationDrawer';
import reducer, * as actions from './state';

const DEFAULT_CHANNEL = {
  status: 'active',
  maxTokensPreUser: 2000,
  maxTokensPreUserDay: 20000,
};
@connect(
  (state) => {
    return state.marketWhatsapp;
  },
  actions,
)
export default class Whatsapp extends Component {
  static propTypes = {
    account: PropTypes.object,
    phoneOpenid: PropTypes.string,
    phones: PropTypes.array,
    list: PropTypes.array,
    total: PropTypes.number,
    conversations: PropTypes.array,
    conversationsTotal: PropTypes.number,
    conversationsPagination: PropTypes.object,
    pagination: PropTypes.object,
    updateAccount: PropTypes.func.isRequired,
    updatePhone: PropTypes.func.isRequired,
    fetchAccounts: PropTypes.func.isRequired,
    fetchChannels: PropTypes.func.isRequired,
    addChannel: PropTypes.func.isRequired,
    updateChannel: PropTypes.func.isRequired,
    deleteChannel: PropTypes.func.isRequired,
    fetchConversations: PropTypes.func.isRequired,
    fetchMessages: PropTypes.func.isRequired,
    setState: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    activeKey: 'channel',
    openDetail: false,
    detail: { ...DEFAULT_CHANNEL },
    workflows: [],
  }

  componentDidMount = async () => {
    const stateParams = {};
    const { items } = await ChatBot.fetchChatbotWorkflows(Configs.ALL_PAGE_PARAMS);
    const workflows = items.map((x) => { return { uuid: x.uuid, name: x.name }; });
    stateParams.workflows = workflows;

    await this.props.fetchAccounts();
    this.props.fetchChannels();
    this.setState(stateParams);
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const detail = { ...this.state.detail };
    detail[key] = value;
    this.setState({ detail });
  }

  onChangeRelatedConfig = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const detail = { ...this.state.detail };
    if (!detail.relatedConfig) {
      detail.relatedConfig = {};
    }
    detail.relatedConfig[key] = value;
    this.setState({ detail });
  }

  onOpenConversation = async (row) => {
    await this.props.fetchConversations({ channelId: row.id });
    this.setState({ activeKey: 'conversation' });
  }

  onSubmit = async () => {
    const { id, name, keywords, relatedConfig } = this.state.detail;
    if (_.isEmpty(name) || _.isEmpty(keywords) || _.isEmpty(relatedConfig)) {
      Toast.show('请完善信息', Toast.Type.WARNING);
      return;
    }

    if (!relatedConfig.relatedFlowUuid) {
      Toast.show('请选择工作流', Toast.Type.WARNING);
      return;
    }

    if (!relatedConfig.welcomeMessages || relatedConfig.welcomeMessages.length === 0) {
      Toast.show('请输入欢迎语', Toast.Type.WARNING);
      return;
    }

    if (!_.isUndefined(id)) {
      await this.props.updateChannel(this.state.detail);
    } else {
      await this.props.addChannel(this.state.detail);
    }

    this.setState({ openDetail: false, detail: {} });
  }

  renderSelects = () => {
    return (
      <Select
        allowClear
        value={this.props.phoneOpenid}
        style={{ width: 320, marginBottom: 16 }}
        placeholder="请选择账号"
        onChange={async (phoneOpenid) => {
          this.props.setState({ phoneOpenid });
          await this.props.fetchChannels();
        }}
      >
        {
          this.props.phones.map((x) => {
            return (<Select.Option value={x.phoneOpenid}>{x.verifiedName}({x.phoneNumber})</Select.Option>);
          })
        }
      </Select>
    );
  }

  renderDetailDrawer = () => {
    const { openDetail, detail, workflows } = this.state;

    return (
      <Drawer
        title="渠道"
        placement="right"
        width="50vw"
        visible={openDetail}
        onClose={() => { return this.setState({ openDetail: false }); }}
        extra={<Button type="primary" onClick={this.onSubmit}>保存</Button>}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} className="common-form" >
          <Form.Item label="渠道名称">
            <Input
              value={detail.name}
              onChange={(e) => { return this.onChangeValue(e, 'name'); }}
            />
          </Form.Item>
          <Form.Item label="状态">
            <Switch
              checked={detail.status === 'active'}
              onChange={(checked) => {
                const status = checked ? 'active' : 'inactive';
                this.onChangeValue(status, 'status');
              }}
            />
          </Form.Item>
          <Form.Item label="关键词">
            <Select
              mode="tags"
              value={detail.keywords}
              open={false}
              onChange={(value) => { return this.onChangeValue(value, 'keywords'); }}
            />
          </Form.Item>
          <Form.Item label="Token用量" help="最小值：2000">
            <Input
              type="number"
              min={2000}
              value={detail?.maxTokensPreUser || 2000}
              onChange={(e) => { return this.onChangeValue(e, 'maxTokensPreUser'); }}
            />
          </Form.Item>
          <Form.Item label="每日Token用量">
            <Input
              min={0}
              type="number"
              value={_.isUndefined(detail?.maxTokensPreUserDay) ? 20000 : detail?.maxTokensPreUserDay}
              onChange={(e) => { return this.onChangeValue(e, 'maxTokensPreUserDay'); }}
            />
          </Form.Item>
          <Form.Item label="被限制文案">
            <Input.TextArea
              value={detail?.tokenLimitTip}
              autoSize={{ minRows: 5 }}
              onChange={(e) => { return this.onChangeValue(e, 'tokenLimitTip'); }}
            />
          </Form.Item>
          <Form.Item label="工作流ID">
            <div style={{ display: 'flex' }}>
              <Select
                showSearch
                filterOption={(input, option) => { return option.children.includes(input); }}
                value={detail?.relatedConfig?.relatedFlowUuid}
                onChange={(e) => { return this.onChangeRelatedConfig(e, 'relatedFlowUuid'); }}
              >
                {(workflows || []).map((x) => { return <Select.Option value={x.uuid}>{x.name}</Select.Option>; })}
              </Select>

              <Button
                style={{ marginLeft: 10 }}
                onClick={() => {
                  window.open(`${window.location.origin}/workflow/${detail?.relatedConfig?.relatedFlowUuid}`);
                }}
              >打开工作流
              </Button>
            </div>
          </Form.Item>
          <Form.Item label="欢迎语">
            <Input.TextArea
              autoSize={{ minRows: 3 }}
              value={(detail?.relatedConfig?.welcomeMessages || [])[0]}
              onChange={(e) => { return this.onChangeRelatedConfig([e.target.value], 'welcomeMessages'); }}
            />
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderWelcomeDrawer = () => {
    return (
      <Drawer
        title="全局欢迎语"
        placement="right"
        width="50vw"
        onClose={() => { return this.setState({ openWelcome: false }); }}
        open={this.state.openWelcome}
        extra={
          <Button
            type="primary"
            onClick={() => {
              this.props.updateAccount({ ...this.props.account });
              this.setState({ openWelcome: false });
            }}
          >保存
          </Button>
        }
      >
        <Form.Item label="欢迎语">
          <Input.TextArea
            autoSize={{ minRows: 5 }}
            value={this.props.account.welcomeMessage}
            onChange={(e) => {
              return this.props.setState({ account: { ...this.props.account, welcomeMessage: e.target.value } });
            }}
          />
        </Form.Item>
      </Drawer>
    );
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '渠道名称', dataIndex: 'name', key: 'name', align: 'center' },
      { title: '状态', dataIndex: 'status', key: 'status', align: 'center' },
      {
        title: '关键词',
        dataIndex: 'keywords',
        key: 'keywords',
        align: 'center',
        render: (t) => { return t.join(','); },
      },
      {
        title: '会话',
        dataIndex: 'conversation',
        key: 'conversation',
        align: 'center',
        render: (t, row) => {
          return (
            <Button type="link" onClick={() => { return this.onOpenConversation(row); }}>
              查看
            </Button>
          );
        },
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (t) => {
          return t ? moment(t).format('YYYY-MM-DD HH:mm:ss') : '';
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        render: (x, row) => {
          return (
            <div>
              <a onClick={() => { return this.setState({ openDetail: true, detail: row }); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm
                title="确定删除吗？"
                onConfirm={() => { this.props.deleteChannel(row.id); }}
              >
                <a>删除</a>
              </Popconfirm>
            </div>
          );
        },
      },
    ];
  }

  renderConversationColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '用户昵称', dataIndex: 'nickname', key: 'nickname', align: 'center' },
      { title: '消息数', dataIndex: 'messageCount', key: 'messageCount', align: 'center', sorter: true },
      { title: '输入Token', dataIndex: 'promptTokens', key: 'promptTokens', align: 'center', sorter: true },
      { title: '输出Token', dataIndex: 'completionTokens', key: 'completionTokens', align: 'center', sorter: true },
      {
        title: '会话开始时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (t) => { return t ? moment(t).format('YYYY-MM-DD HH:mm:ss') : ''; },
      },
      {
        title: '最后消息时间',
        dataIndex: 'lastMessageAt',
        key: 'lastMessageAt',
        align: 'center',
        render: (t) => { return t ? moment(t).format('YYYY-MM-DD HH:mm:ss') : ''; },
      },
      {
        title: '会话记录',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        render: (txt, row) => {
          return (
            <a onClick={() => { this.setState({ openConversation: true, conversation: row }); }}>
              查看
            </a>
          );
        },
      },
    ];
  }

  render = () => {
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <Tabs
          activeKey={this.state.activeKey}
          onChange={(activeKey) => { return this.setState({ activeKey }); }}
          tabBarExtraContent={{
            right: (
              <>
                <Button type="primary" onClick={() => { return this.setState({ openWelcome: true }); }}>
                  全局欢迎语
                </Button>
                <Divider type="vertical" />
                <Button type="primary" onClick={() => { return this.setState({ openAccount: true }); }}>
                  账号欢迎语
                </Button>
              </>
            ),
          }}
        >
          <Tabs.TabPane tab="渠道" key="channel">
            <FilterBar
              canAdd
              addText="新增渠道"
              renderSelects={this.renderSelects}
              onAdd={() => { return this.setState({ openDetail: true, detail: { ...DEFAULT_CHANNEL } }); }}
            />

            <PaginationTable
              dataSource={this.props.list}
              totalDataCount={this.props.total}
              pagination={this.props.pagination}
              columns={this.renderColumns()}
              onPaginationChange={() => { }}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="会话" key="conversation">
            <Select
              style={{ width: 160, marginBottom: 16 }}
              placeholder="请选择"
              onChange={
                async (e) => {
                  const staff = _.find(this.props.list, { id: e });
                  this.onOpenConversation(staff);
                }
              }
            >
              {(this.props.list || []).map((x) => { return <Select.Option value={x.id}>{x.name}</Select.Option>; })}
            </Select>
            <PaginationTable
              dataSource={this.props.conversations}
              totalDataCount={this.props.conversationsTotal}
              pagination={this.props.conversationsPagination}
              columns={this.renderConversationColumns()}
              onPaginationChange={() => { }}

            />
          </Tabs.TabPane>
        </Tabs>


        {this.state.openDetail && this.renderDetailDrawer()}
        {
          this.state.openAccount &&
          <AccountDrawer
            open={this.state.openAccount}
            phoneOpenid={this.props.phoneOpenid}
            phones={this.props.phones}
            onSubmit={this.props.updatePhone}
            onClose={() => { return this.setState({ openAccount: false }); }}
          />
        }
        {this.state.openWelcome && this.renderWelcomeDrawer()}
        {
          this.state.openConversation &&
          <ConversationDrawer
            open={this.state.openConversation}
            conversation={this.state.conversation}
            fetchMessages={this.props.fetchMessages}
            onClose={() => { return this.setState({ openConversation: false }); }}
          />
        }
      </div>
    );
  }
}

export {
  reducer,
};
