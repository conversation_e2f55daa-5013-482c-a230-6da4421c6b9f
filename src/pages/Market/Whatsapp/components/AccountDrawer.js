import { Toast } from '~/components';
import { <PERSON><PERSON>, Drawer, Form, Input, Select } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class AccountDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    phoneOpenid: PropTypes.string,
    phones: PropTypes.array,
    onClose: PropTypes.func,
    onSubmit: PropTypes.func,
  }

  state = {
    accountId: '',
    phoneOpenid: '',
  }

  componentDidMount = () => {
    const { id, verifiedName, welcomeMessage } = this.props.phones.find((x) => {
      return x.phoneOpenid === this.props.phoneOpenid;
    });
    this.setState({
      accountId: id,
      verifiedName,
      phoneOpenid: this.props.phoneOpenid,
      welcomeMessage,
    });
  }

  onChangePhone = (phoneOpenid) => {
    const { id, verifiedName, welcomeMessage } = this.props.phones.find((x) => {
      return x.phoneOpenid === phoneOpenid;
    });
    this.setState({ accountId: id, verifiedName, phoneOpenid, welcomeMessage });
  }

  onSubmit = async () => {
    const { accountId, verifiedName, welcomeMessage } = this.state;
    await this.props.onSubmit({ id: accountId, verifiedName, welcomeMessage });
    this.props.onClose();
    Toast.show('保存成功', Toast.Type.SUCCESS);
  }

  render = () => {
    return (
      <Drawer
        title="账号欢迎语"
        placement="right"
        width="50vw"
        onClose={this.props.onClose}
        open={this.props.open}
        extra={<Button type="primary" onClick={() => { return this.onSubmit(); }}>保存</Button>}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} className="common-form">
          <Form.Item label="账号">
            <Select value={this.state.phoneOpenid} onChange={(e) => { return this.onChangePhone(e); }} >
              {
                this.props.phones.map((x) => {
                  return (<Select.Option value={x.phoneOpenid}>{x.verifiedName}({x.phoneNumber})</Select.Option>);
                })
              }
            </Select>
          </Form.Item>
          <Form.Item label="欢迎语">
            <Input.TextArea
              autoSize={{ minRows: 5 }}
              value={this.state.welcomeMessage}
              onChange={(e) => { return this.setState({ welcomeMessage: e.target.value }); }}
            />
          </Form.Item>
        </Form>
      </Drawer>
    );
  }
}
