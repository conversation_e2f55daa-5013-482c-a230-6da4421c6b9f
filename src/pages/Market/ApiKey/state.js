
const SET_STATE = 'mpArticle/SET_STATE';
const CLEAR_STATE = 'mpArticle/CLEAR_STATE';

import { Apikey } from '~/engine';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchApikeys = (params = {}) => {
  return async (dispatch) => {
    const { apiKeys } = await Apikey.fetchApikeys(params);
    dispatch(setState({ apiKeys }));
  };
};

export const createApikey = (params = {}) => {
  return async (dispatch) => {
    const data = await Apikey.createApikey(params);
    dispatch(fetchApikeys());
    return data;
  };
};

export const updateApikey = (params = {}) => {
  return async (dispatch) => {
    await Apikey.updateApikey(params);
    dispatch(fetchApikeys());
  };
};

export const updateApikeyStatus = (params = {}) => {
  return async (dispatch) => {
    await Apikey.updateApikeyStatus(params);
    dispatch(fetchApikeys());
  };
};

export const deleteApikey = (id) => {
  return async (dispatch) => {
    await Apikey.deleteApikey(id);
    dispatch(fetchApikeys());
  };
};

const _getInitState = () => {
  return {
    apiKeys: [],
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
