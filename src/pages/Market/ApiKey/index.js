import { CopyOutlined } from '@ant-design/icons';
import { PaginationTable, Toast } from '~/components';
import { Button, DatePicker, Divider, Form, Input, Modal, Popconfirm, Switch, message } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.marketApikey;
  },
  actions,
)
export default class MpArticle extends Component {
  static propTypes = {
    apiKeys: PropTypes.array.isRequired,
    createApikey: PropTypes.func.isRequired,
    fetchApikeys: PropTypes.func.isRequired,
    updateApikey: PropTypes.func.isRequired,
    deleteApikey: PropTypes.func.isRequired,
    updateApikeyStatus: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    apiKey: {},
    isGenerate: false,
  }

  componentDidMount = async () => {
    await this.props.fetchApikeys();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onAdd = async () => {
    this.setState({ visible: true, apiKey: {}, isGenerate: false });
  }

  onChangeValue = (e, key) => {
    this.setState({ apiKey: { ...this.state.apiKey, [key]: e?.target?.value || e } });
  }

  onSubmit = async () => {
    const { apiKey } = this.state;
    if (!apiKey.name) {
      message.error('请输入名称');
      return;
    }
    if (!apiKey.expiredAt) {
      message.error('请选择过期时间');
      return;
    }

    apiKey.expiredAt = moment(apiKey.expiredAt).format('YYYY-MM-DDTHH:mm:ss.SSSSSS');

    if (apiKey.id) {
      await this.props.updateApikey(apiKey);
      this.setState({ visible: false, apiKey: {}, isGenerate: false });
    } else {
      const obj = await this.props.createApikey(apiKey);
      this.setState({ isGenerate: true, apiKey: { ...obj, expiredAt: moment(obj.expiredAt) } });
    }
    Toast.show('操作成功', Toast.Type.SUCCESS);
  }

  renderModal = () => {
    const { visible, apiKey, isGenerate } = this.state;

    return (
      <Modal
        title="Apikey"
        visible={visible}
        onOk={this.onSubmit}
        okButtonProps={{ disabled: isGenerate }}
        onCancel={() => { return this.setState({ visible: false, apiKey: {} }); }}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 14 }} >

          <Form.Item label="名称">
            <Input
              value={apiKey.name}
              onChange={(e) => { return this.onChangeValue(e, 'name'); }}
            />
          </Form.Item>
          <Form.Item label="过期时间">
            <DatePicker
              value={apiKey.expiredAt}
              onChange={(e) => { return this.onChangeValue(e, 'expiredAt'); }}
            />
          </Form.Item>
          <Form.Item label="Key">
            <Input
              value={apiKey.sensitiveId}
              disabled
              addonAfter={
                <CopyOutlined
                  onClick={() => {
                    if (!_.isEmpty(apiKey.sensitiveId)) {
                      navigator.clipboard.writeText(apiKey.sensitiveId).then(() => {
                        message.success('复制成功');
                      });
                    }
                  }}
                />
              }
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  renderColumns = () => {
    return [
      { title: '名称', dataIndex: 'name', key: 'name', align: 'center' },
      {
        title: '过期时间',
        dataIndex: 'expiredAt',
        key: 'expiredAt',
        align: 'center',
        render: (text) => {
          return moment(text).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      { title: 'Key', dataIndex: 'sensitiveId', key: 'sensitiveId', align: 'center' },
      {
        title: '状态',
        dataIndex: 'enabled',
        key: 'enabled',
        align: 'center',
        render: (enabled, row) => {
          return (
            <Switch
              checked={enabled}
              onChange={(e) => { return this.props.updateApikeyStatus({ id: row.id, enabled: e }); }}
            />
          );
        },
      },
      {
        title: '操作',
        key: 'action',
        align: 'center',
        render: (text, row) => {
          return (
            <>
              <Button
                type="link"
                onClick={() => {
                  return this.setState({
                    visible: true,
                    isGenerate: false,
                    apiKey: { ...row, expiredAt: moment(row.expiredAt) },
                  });
                }}
              >
                编辑
              </Button>
              <Divider type="vertical" />
              <Popconfirm title="确定删除吗？" onConfirm={() => { return this.props.deleteApikey(row.id); }}>
                <Button type="link">删除</Button>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  render = () => {
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <Button
          type="primary"
          style={{ float: 'right', marginBottom: 20 }}
          onClick={() => { return this.onAdd(); }}
        >新增
        </Button>
        <PaginationTable
          needPagination={false}
          dataSource={this.props.apiKeys}
          columns={this.renderColumns()}
        />
        {this.state.visible && this.renderModal()}
      </div>
    );
  }
}

export {
  reducer,
};
