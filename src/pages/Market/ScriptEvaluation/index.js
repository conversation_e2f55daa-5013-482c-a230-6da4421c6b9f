/* eslint-disable max-lines */
import './index.less';

import { DownloadOutlined, InboxOutlined } from '@ant-design/icons';
import Engine, { Sessions } from '~/engine';
import { EVENT_TYPE } from '~/pages/Playground/Configs';
import ReconnectingWebSocket from '~/pages/Playground/WebSocket';
import { OSSFileHelper, Platform, StringExtension } from '~/plugins';
import { Button, Checkbox, Drawer, Form, Input, Modal, Space, Table, Upload, message } from 'antd';
import * as JSONC from 'jsonc-parser';
import qs from 'qs';
import React, { Component } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import * as XLSX from 'xlsx';

import TableColumnHelper from './utils/tableColumnHelper';
import Utils from './utils/utils';

const ACCEPT_TYPES = '.xlsx,.xls';
const CONCURRENT_LIMIT = 10;
window.JSONC = JSONC; // 方便调试
const FLOW_MAP = {
  stg: 'MW24mDbmQCtjavB80KjGvB',
  prod: '4bmA061WXThqj9qeJ1Sv3c',
};

// 修正工作流map
const FIX_FLOW_MAP = {
  stg: '7pWPF6H6Es2nEyJ2XL5jQ0',
  prod: '3m6GJup7AAAbQtCSipXsAz',
};

export default class ScriptEvaluation extends Component {
  state = {
    promptText: '你是一位资深的知识类短视频脚本质检专家。你的任务是判断AI生成的最终文稿是否符合项目规范。' +
      '请根据以下输入评估两个方面并给出明确结论。\n\n' +
      '输入内容：\n\n' +
      'AI生成的最终文稿： {generated_script}\n' +
      '语言风格参考： {style_reference}\n' +
      '结构要求： {structure_rules}\n' +
      '评估维度与标准：\n\n' +
      '结构要求符合度（刚性框架一致性）\n' +
      '核心原则： 逐条比对是否符合 结构要求 中所有强制规则，包括：\n' +
      '分段数量与功能（如"开场钩子-痛点阐述-解决方案"三幕式）\n' +
      '特定内容的位置（如"品牌口号必须在结尾10秒内出现"）\n' +
      '单句/单段时长限制（如"每信息点≤15字"）\n' +
      '结论判定： 需100%满足所有条款，任意一项违规即判"不符合"。\n' +
      '语言风格一致性（表达基因延续性）\n' +
      '核心原则： 分析 语言基因特征 是否与 语言风格参考 一致：\n' +
      '必须匹配的特征： 主语人称（如"你"VS"用户"）、术语颗粒度（如"神经网络"VS"AI大脑"）、' +
      '句式结构（如设问句占比）\n' +
      '允许变化的特征： 具体案例词汇、修辞手法（若未在要求中限定）\n' +
      '不合格表现： 专业稿出现网络俚语、科普稿使用学术化长句、人称体系混乱\n' +
      '评估结论：\n\n' +
      '合格： 同时满足 "结构要求符合度 + 语言风格一致性"\n' +
      '不合格： 任意一项未达标\n' +
      '输出格式（严格遵循）：\n\n' +
      '结构要求符合度结论：\n' +
      '[符合 / 不符合]\n' +
      '(如不符合，用1句话说明违规条款，例：结尾未植入品牌口号)\n' +
      '语言风格一致性结论：\n' +
      '[一致 / 不一致]\n' +
      '(如不一致，用1句话指出基因偏差，例：学术化术语占比超阈值)\n' +
      '最终裁决：\n' +
      '[合格 / 不合格]\n' +
      '返回的内容为 JSON，确保能被 python 的 `json.loads` 解析，JSON 字段定义如下：\n{"内容一致性":"","语言风格一致性":"","结构要求符合度":"","最终裁决":""}',

    // 从 Redux 迁移过来的状态
    fileName: '',
    excelData: [],
    headers: [],
    evaluationResults: [],
    isUploading: false,
    isProcessing: false,
    totalItems: 0,
    completedItems: 0,
    allResults: [],
    // 新增状态：专家评判字段和对比统计
    expertFields: [],
    comparisonStatistics: null,
    showStatistics: false,
    // 去缓存选项，默认选中
    disableCache: true,
    // 提示词修正功能相关状态
    isOptimizing: false,
    optimizedPrompt: '',
    optimizeReason: '',
    showOptimizeModal: false,
    // 提示词历史版本管理
    promptHistory: [], // 存储提示词历史版本
    evaluationHistory: [], // 存储每个版本对应的AI判定结果
    currentPromptVersion: 0, // 当前提示词版本号
    showHistoryModal: false, // 显示历史版本弹窗
  }

  // 组件挂载时从 localStorage 读取数据并获取OSS历史数据
  componentDidMount = async () => {
    const storageFields = ['promptText'];
    const updates = storageFields.reduce((acc, field) => {
      const savedValue = this.getStorageData(field);
      if (savedValue) acc[field] = savedValue;
      return acc;
    }, {});

    if (Object.keys(updates).length > 0) {
      this.setState(updates);
    }

    // 获取OSS历史修正提示词数据
    await this.initializeHistoryFromOSS();
  }

  // 缓存处理后的数据，避免重复计算
  processedTextsCache = null;

  // WebSocket连接池管理
  wsConnections = new Map();

  // 清除缓存数据
  clearCache = () => {
    this.processedTextsCache = null;
  }

  // 清理WebSocket连接
  cleanupConnection = (itemIndex) => {
    const ws = this.wsConnections.get(itemIndex);
    if (ws) {
      try {
        ws.close();
      } catch (error) {
        console.warn(`关闭数据项 ${itemIndex + 1} 连接时出错:`, error); // eslint-disable-line no-console
      }
      this.wsConnections.delete(itemIndex);
    }
  }

  // 从 localStorage 获取数据
  getStorageData = (key) => {
    try {
      const data = localStorage.getItem(`scriptEvaluation_${key}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Error reading ${key} from localStorage:`, error);
      return null;
    }
  }

  // 保存数据到 localStorage
  setStorageData = (key, value) => {
    try {
      localStorage.setItem(`scriptEvaluation_${key}`, JSON.stringify(value));
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Error saving ${key} to localStorage:`, error);
    }
  }

  // 读取Excel文件
  readExcelFile = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          // 保持原始数据类型，不进行自动转换
          const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
            raw: true, // 保持原始数据类型
            defval: undefined, // 空单元格使用undefined而不是空字符串
          });

          if (jsonData.length === 0) {
            reject(new Error('Excel文件为空'));
            return;
          }

          const headers = jsonData[0];
          const rows = jsonData.slice(1).filter((row) => {
            return row.some((cell) => {
              return cell !== undefined && cell !== null && cell !== '';
            });
          });

          resolve({ headers, rows });
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = () => { return reject(new Error('文件读取失败')); };
      reader.readAsArrayBuffer(file);
    });
  }

  // 处理Excel数据
  processExcelData = (data, headers) => {
    // 查找 内容参考 风格参考 结构要求 最终输出
    const contentRefIndex = headers.findIndex((header) => { return header === '内容参考'; });
    const styleRefIndex = headers.findIndex((header) => { return header === '风格参考'; });
    const structureIndex = headers.findIndex((header) => { return header === '结构要求'; });
    const outputIndex = headers.findIndex((header) => { return header === '最终输出'; });

    if (contentRefIndex === -1) {
      throw new Error('Excel文件中未找到"内容参考"列，请检查文件格式');
    }
    if (styleRefIndex === -1) {
      throw new Error('Excel文件中未找到"风格参考"列，请检查文件格式');
    }
    if (structureIndex === -1) {
      throw new Error('Excel文件中未找到"结构要求"列，请检查文件格式');
    }
    if (outputIndex === -1) {
      throw new Error('Excel文件中未找到"最终输出"列，请检查文件格式');
    }

    return data.map((row, index) => {
      const contentRefValue = row[contentRefIndex] || '';
      const styleRefValue = row[styleRefIndex] || '';
      const structureValue = row[structureIndex] || '';
      const outputValue = row[outputIndex] || '';

      // 验证必需字段不为空
      if (!contentRefValue.trim()) {
        throw new Error(`第${index + 2}行的"内容参考"字段为空，请检查数据完整性`);
      }
      if (!styleRefValue.trim()) {
        throw new Error(`第${index + 2}行的"风格参考"字段为空，请检查数据完整性`);
      }
      if (!structureValue.trim()) {
        throw new Error(`第${index + 2}行的"结构要求"字段为空，请检查数据完整性`);
      }
      if (!outputValue.trim()) {
        throw new Error(`第${index + 2}行的"最终输出"字段为空，请检查数据完整性`);
      }

      // 构建完整的原始数据对象，包含所有Excel列
      const originalData = {};
      headers.forEach((header, headerIndex) => {
        // 保持原始数据值，不强制转换为空字符串
        const cellValue = row[headerIndex];
        originalData[header] = cellValue !== undefined && cellValue !== null ? cellValue : '';
      });

      return {
        id: index + 1,
        contentRef: contentRefValue.trim(), // 内容参考
        styleRef: styleRefValue.trim(), // 风格参考
        structure: structureValue.trim(), // 结构要求
        output: outputValue.trim(), // 最终输出
        originalData, // 包含所有原始Excel列的完整数据
      };
    });
  }

  // 并发处理所有数据项
  processItemsConcurrently = async (items, promptText) => {
    const semaphore = new Array(CONCURRENT_LIMIT).fill(null);
    let itemIndex = 0;

    const processItem = async () => {
      while (itemIndex < items.length) {
        const currentItemIndex = itemIndex++;
        const item = items[currentItemIndex];

        try {
          // eslint-disable-next-line no-await-in-loop
          await this.processSingleItem(
            item, currentItemIndex, promptText,
          );
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error(`数据项 ${currentItemIndex} 处理失败:`, error);
        }
      }
    };

    await Promise.all(semaphore.map(() => { return processItem(); }));
  }

  // 处理单个数据项
  processSingleItem = async (item, itemIndex, promptText) => {
    return new Promise((resolve, reject) => {
      try {
        const flowId = FLOW_MAP[Platform.isProd() ? 'prod' : 'stg'];
        const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflow-v2/run/${flowId}`;
        const query = { access_token: Sessions.getToken() };

        const ws = new ReconnectingWebSocket(
          `${path}?${qs.stringify(query)}`,
          [],
          (e) => { this.onReceiveMsg(e, itemIndex, resolve, reject); },
          () => {
            // 为单个数据项发送WebSocket消息
            ws.send(JSON.stringify({
              text: JSON.stringify({
                content: item.contentRef, // 内容参考
                style_reference: item.styleRef, // 风格参考
                structure_format: item.structure, // 结构要求
                article: item.output, // 最终输出
                prompt: promptText,
              }),
              type: 'message',
              is_beta: false,
              item_id: item.id,
              item_index: itemIndex,
            }));
          },
        );

        this.wsConnections.set(itemIndex, ws);

        // 设置超时处理
        setTimeout(() => {
          if (this.wsConnections.has(itemIndex)) {
            this.cleanupConnection(itemIndex);
            reject(new Error(`数据项 ${itemIndex + 1} 处理超时`));
          }
        }, 30 * 60 * 1000);
      } catch (error) {
        reject(error);
      }
    });
  }

  // 处理单个数据项结果
  handleItemResult = (content, itemIndex) => {
    try {
      // 尝试解析JSON结果
      const result = JSONC.parse(content || '{}');

      // 使用函数式状态更新，确保正确累积结果
      this.setState((prevState) => {
        // 检查是否已经存在相同 itemIndex 的结果，避免重复添加
        const existingIndex = prevState.allResults.findIndex((item) => { return item.itemIndex === itemIndex; });
        let newResults;

        if (existingIndex >= 0) {
          // 如果已存在，则更新该项
          newResults = [...prevState.allResults];
          newResults[existingIndex] = { ...result, itemIndex };
        } else {
          // 如果不存在，则添加新项
          newResults = [...prevState.allResults, { ...result, itemIndex }];
        }

        return {
          allResults: newResults,
          evaluationResults: newResults,
        };
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('结果解析失败:', error, content);
      // 如果JSON解析失败，将原始内容作为结果保存
      this.setState((prevState) => {
        // 检查是否已经存在相同 itemIndex 的结果，避免重复添加
        const existingIndex = prevState.allResults.findIndex((item) => { return item.itemIndex === itemIndex; });
        let newResults;

        if (existingIndex >= 0) {
          // 如果已存在，则更新该项
          newResults = [...prevState.allResults];
          newResults[existingIndex] = { itemIndex, content };
        } else {
          // 如果不存在，则添加新项
          newResults = [...prevState.allResults, { itemIndex, content }];
        }

        return {
          allResults: newResults,
          evaluationResults: newResults,
        };
      });
    }
  }

  // 从promptText解析动态字段
  parseFieldsFromPrompt = () => {
    const { promptText } = this.state;

    try {
      // 查找最后一行的JSON字段定义
      const lines = promptText.split('\n');
      const lastLine = lines[lines.length - 1].trim();

      // 尝试解析JSON
      const fieldsObj = JSON.parse(lastLine);
      return Object.keys(fieldsObj);
    } catch (error) {
      // 降级到默认字段
      return ['内容一致性', '语言风格一致性', '结构要求符合度', '最终裁决'];
    }
  }

  // 初始化提示词历史版本
  initializePromptHistory = () => {
    const { promptText, promptHistory } = this.state;

    // 如果历史为空或者当前提示词不在历史中，则初始化
    if (promptHistory.length === 0 || !promptHistory.some((item) => { return item.prompt === promptText; })) {
      const initialVersion = {
        version: promptHistory.length + 1, // 基于现有历史长度确定版本号
        prompt: promptText,
        timestamp: new Date().toISOString(),
        isOriginal: promptHistory.length === 0, // 只有在没有历史记录时才标记为原始版本
        description: promptHistory.length === 0 ? '原始版本' : '当前版本',
      };

      this.setState({
        promptHistory: [...promptHistory, initialVersion],
        currentPromptVersion: initialVersion.version,
      });
    }
  }

  // 添加新版本到历史
  addPromptToHistory = (newPrompt, description = '优化版本') => {
    const { promptHistory } = this.state;
    const newVersion = {
      version: promptHistory.length + 1,
      prompt: newPrompt,
      timestamp: new Date().toISOString(),
      isOriginal: false,
      description,
    };

    this.setState({
      promptHistory: [...promptHistory, newVersion],
      currentPromptVersion: newVersion.version,
    });
  }

  // 更新指定版本的AI判定结果
  updateEvaluationHistory = (version, results) => {
    const { evaluationHistory } = this.state;
    const existingIndex = evaluationHistory.findIndex((item) => { return item.version === version; });

    const evaluationData = {
      version,
      results,
      timestamp: new Date().toISOString(),
      statistics: this.generateEvaluationStatistics(results),
    };

    if (existingIndex >= 0) {
      // 更新现有版本
      const newHistory = [...evaluationHistory];
      newHistory[existingIndex] = evaluationData;
      this.setState({ evaluationHistory: newHistory });
    } else {
      // 添加新版本
      this.setState({
        evaluationHistory: [...evaluationHistory, evaluationData],
      });
    }
  }

  // 生成AI判定结果统计
  generateEvaluationStatistics = (results) => {
    if (!results || results.length === 0) {
      return { total: 0, qualified: 0, qualifiedRate: 0 };
    }

    const total = results.length;
    const qualified = results.filter((result) => {
      return result['最终裁决'] === '合格' || result['最终裁决'] === '通过';
    }).length;

    return {
      total,
      qualified,
      qualifiedRate: Math.round((qualified / total) * 100),
    };
  }

  // 提取专家评判字段
  extractExpertFields = (headers) => {
    const requiredFields = ['内容参考', '风格参考', '结构要求', '最终输出'];

    // 过滤掉必需字段，剩余的就是专家评判字段
    const expertFields = headers.filter((header) => {
      return !requiredFields.includes(header) && header && header.trim() !== '';
    });

    // 验证字段名称安全性
    const validExpertFields = expertFields.filter((field) => {
      // 简单的字段名验证，防止注入
      return typeof field === 'string' && field.length > 0 && field.length < 100;
    });

    return validExpertFields;
  }

  // AI与专家结果对比分析
  compareAIWithExpert = (aiResults, expertData, expertFields) => {
    if (!aiResults || !expertData || !expertFields || expertFields.length === 0) {
      return [];
    }

    const comparisons = [];
    // 获取AI评估字段用于映射
    const aiFields = this.parseFieldsFromPrompt();

    // 遍历专家数据，确保按正确的索引对应
    expertData.forEach((expertResult) => {
      // 根据itemIndex查找对应的AI评估结果，确保数据正确对应
      // expertResult.id 对应的是 Excel 行号（从1开始），需要找到对应的AI结果
      const aiResult = aiResults.find((result) => { return result.itemIndex === (expertResult.id - 1); });
      if (!aiResult) return;

      const comparison = {
        id: expertResult.id,
        itemIndex: expertResult.id - 1, // 转换为0基索引
        comparisons: {},
      };

      // 对每个专家字段进行对比
      expertFields.forEach((expertField) => {
        // 建立专家字段与AI字段的映射关系
        const aiFieldName = this.mapExpertFieldToAIField(expertField, aiFields);
        const aiValue = aiFieldName ? (aiResult[aiFieldName] || '') : '';

        // 统一专家数据访问路径，优先使用originalData确保数据一致性
        let expertValue = '';
        if (expertResult.originalData && expertResult.originalData[expertField] !== undefined) {
          expertValue = expertResult.originalData[expertField];
        } else if (expertResult[expertField] !== undefined) {
          expertValue = expertResult[expertField];
        }

        // 确保值为字符串类型
        const normalizedAiValue = (aiValue || '').toString().trim();
        const normalizedExpertValue = (expertValue || '').toString().trim();

        // 简单的一致性判断（可以根据需要扩展更复杂的对比逻辑）
        const isConsistent = this.compareValues(normalizedAiValue, normalizedExpertValue);

        comparison.comparisons[expertField] = {
          aiValue: normalizedAiValue,
          expertValue: normalizedExpertValue,
          isConsistent,
          aiFieldName, // 添加AI字段名用于调试
        };
      });

      comparisons.push(comparison);
    });

    return comparisons;
  }

  // 专家字段与AI字段映射
  mapExpertFieldToAIField = (expertField, aiFields) => {
    // 移除专家字段中的前缀（如"专家-"），获取核心字段名
    const coreFieldName = expertField.replace(/^专家-?/, '').trim();

    // 在AI字段中查找匹配的字段
    const matchedAIField = aiFields.find((aiField) => {
      // 完全匹配
      if (aiField === coreFieldName) return true;

      // 模糊匹配：检查是否包含核心关键词（修正正则表达式语法）
      const coreKeywords = coreFieldName.replace(/一致性|符合度|裁决/g, '').trim();
      if (coreKeywords && aiField.includes(coreKeywords)) return true;

      return false;
    });

    return matchedAIField || null;
  }

  // 值对比逻辑
  compareValues = (aiValue, expertValue) => {
    // 处理空值情况
    if (!aiValue && !expertValue) return true; // 都为空认为一致
    if (!aiValue || !expertValue) return false; // 一个为空一个不为空认为不一致

    const aiStr = aiValue.toString().trim().toLowerCase();
    const expertStr = expertValue.toString().trim().toLowerCase();

    // 直接进行字符串完全匹配比较（忽略大小写和前后空格）
    const isConsistent = aiStr === expertStr;

    return isConsistent;
  }

  // 生成对比统计分析
  generateComparisonStatistics = (comparisons, expertFields) => {
    if (!comparisons || comparisons.length === 0 || !expertFields || expertFields.length === 0) {
      return null;
    }

    const statistics = {
      totalItems: comparisons.length,
      fieldStatistics: {},
      overallAccuracy: 0,
      detailedAnalysis: [],
    };

    // 为每个专家字段生成统计
    expertFields.forEach((field) => {
      let consistentCount = 0;
      let totalCount = 0;
      const fieldDetails = [];

      comparisons.forEach((comparison) => {
        const fieldComparison = comparison.comparisons[field];
        if (fieldComparison) {
          totalCount++;
          if (fieldComparison.isConsistent) {
            consistentCount++;
          }

          fieldDetails.push({
            itemId: comparison.id,
            aiValue: fieldComparison.aiValue,
            expertValue: fieldComparison.expertValue,
            isConsistent: fieldComparison.isConsistent,
          });
        }
      });

      const accuracy = totalCount > 0 ? (consistentCount / totalCount * 100).toFixed(2) : 0;

      statistics.fieldStatistics[field] = {
        consistentCount,
        totalCount,
        accuracy: parseFloat(accuracy),
        details: fieldDetails,
      };
    });

    // 修正整体准确率计算：按样本维度计算，确保每个样本在所有维度上的权重相等
    const sampleAccuracies = [];
    comparisons.forEach((comparison) => {
      const fieldResults = expertFields.map((field) => {
        const fieldComparison = comparison.comparisons[field];
        return fieldComparison ? fieldComparison.isConsistent : false;
      });

      // 计算该样本在所有维度上的准确率
      const validResults = fieldResults.filter((result) => { return result !== null && result !== undefined; });
      if (validResults.length > 0) {
        const sampleAccuracy = fieldResults.filter(Boolean).length / validResults.length;
        sampleAccuracies.push(sampleAccuracy);
      }
    });

    // 计算所有样本的平均准确率作为整体一致性
    if (sampleAccuracies.length > 0) {
      const totalAccuracy = sampleAccuracies.reduce((sum, acc) => { return sum + acc; }, 0);
      const averageAccuracy = totalAccuracy / sampleAccuracies.length * 100;
      statistics.overallAccuracy = parseFloat(averageAccuracy.toFixed(2));
    } else {
      statistics.overallAccuracy = 0;
    }

    return statistics;
  }

  // 执行对比分析
  performComparisonAnalysis = () => {
    const { evaluationResults, excelData, headers, expertFields } = this.state;

    // 检查是否有专家字段和评估结果
    if (!expertFields || expertFields.length === 0) {
      return;
    }

    if (!evaluationResults || evaluationResults.length === 0) {
      return;
    }

    try {
      // 处理Excel数据获取专家评判结果
      const processedData = this.processExcelData(excelData, headers);

      // 进行AI与专家结果对比
      const comparisons = this.compareAIWithExpert(evaluationResults, processedData, expertFields);

      // 生成统计分析
      const statistics = this.generateComparisonStatistics(comparisons, expertFields);

      // 更新状态
      this.setState({
        comparisonStatistics: statistics,
        showStatistics: statistics !== null,
      });

      if (statistics) {
        message.success(`对比分析完成！整体一致性: ${statistics.overallAccuracy}%`);
      }
    } catch (error) {
      console.error('对比分析失败:', error); // eslint-disable-line no-console
      message.error('对比分析失败，请检查数据格式');
    }
  }

  // 获取合并后的数据
  getMergedData = () => {
    const { evaluationResults, excelData, headers } = this.state;

    if (!evaluationResults || evaluationResults.length === 0 || !excelData || excelData.length === 0) {
      return [];
    }

    try {
      // 处理Excel数据，获取原始格式
      const processedData = this.processExcelData(excelData, headers);

      // 获取动态字段
      const dynamicFields = this.parseFieldsFromPrompt();

      // 合并数据
      const mergedData = processedData.map((item) => {
        // 根据itemIndex查找对应的评估结果，确保数据正确对应
        // item.id 对应的是 Excel 行号（从1开始），itemIndex 是从0开始的索引
        const evaluationResult = evaluationResults.find((result) => {
          return result.itemIndex === (item.id - 1);
        }) || {};

        // 构建合并后的数据对象，包含所有原始列 + 评估结果
        const mergedItem = {
          key: item.id,
          id: item.id,
          // 展开所有原始数据（专家数据保持原始字段名）
          ...item.originalData,
        };

        // 动态追加AI评估结果字段（添加ai_前缀避免与专家字段冲突）
        dynamicFields.forEach((field) => {
          const aiFieldName = `ai_${field}`;
          mergedItem[aiFieldName] = evaluationResult[field] || '';
        });

        return mergedItem;
      });

      return mergedData;
    } catch (error) {
      console.error('数据合并失败:', error); // eslint-disable-line no-console
      return [];
    }
  }

  // 将数据转换为 markdown 格式字符串
  convertDataToMarkdown = (dataArray) => {
    if (!dataArray || dataArray.length === 0) {
      return '暂无数据';
    }

    let markdown = '# 评估结果数据\n\n';

    dataArray.forEach((item, index) => {
      markdown += `## 数据项 ${index + 1}\n\n`;

      // 将对象的每个字段转换为 markdown 列表
      Object.keys(item).forEach((key) => {
        const value = item[key] || '';
        // 对于长文本内容，使用代码块格式
        if (typeof value === 'string' && value.length > 100) {
          markdown += `**${key}:**\n\`\`\`\n${value}\n\`\`\`\n\n`;
        } else {
          markdown += `**${key}:** ${value}\n\n`;
        }
      });

      markdown += '---\n\n';
    });

    return markdown;
  }

  // 将原始数据转换为 markdown 表格格式字符串（排除AI判定结果）
  convertOriginalDataToMarkdown = (dataArray) => {
    if (!dataArray || dataArray.length === 0) {
      return '暂无数据';
    }

    // 获取表头信息（排除内部字段和AI评估结果字段）
    const firstItem = dataArray[0];
    const headers = ['ID'];

    // 添加原始Excel表头，排除内部字段和AI字段
    Object.keys(firstItem).forEach((key) => {
      if (key !== 'key' && key !== 'id' && !key.startsWith('ai_')) {
        headers.push(key);
      }
    });

    // 辅助函数：处理表格单元格内容
    const formatCellContent = (value) => {
      if (value === null || value === undefined || value === '') {
        return '-';
      }

      const stringValue = String(value);

      // 转义markdown特殊字符，保持完整文本内容
      return stringValue
        .replace(/\|/g, '\\|')
        .replace(/\n/g, ' ')
        .replace(/\r/g, ' ')
        .trim();
    };

    // 构建markdown表格
    let markdown = '# 原始数据\n\n';

    // 表格头部
    markdown += `| ${headers.join(' | ')} |\n`;
    markdown += `|${headers.map(() => { return '---'; }).join('|')}|\n`;

    // 表格数据行
    dataArray.forEach((item, index) => {
      const row = [index + 1]; // ID列

      // 添加其他列的数据
      headers.slice(1).forEach((header) => {
        const value = item[header];
        row.push(formatCellContent(value));
      });

      markdown += `| ${row.join(' | ')} |\n`;
    });

    return markdown;
  }

  // 生成包含所有历史版本的提示词数据
  generateHistoricalPromptData = () => {
    const { promptHistory, evaluationHistory } = this.state;

    if (promptHistory.length === 0) {
      return '';
    }

    let historicalData = '# 提示词优化历史\n\n';

    promptHistory.forEach((promptVersion) => {
      const { version, prompt, description } = promptVersion;

      historicalData += `## 版本${version} (${description})\n`;

      // 去除格式字段，只发送纯净的提示词内容给优化工作流
      const { cleanPrompt } = this.extractFormatFields(prompt);
      historicalData += `**提示词内容：**\n\`\`\`\n${cleanPrompt}\n\`\`\`\n\n`;

      // 查找对应版本的AI判定结果
      const evaluationData = evaluationHistory.find((item) => { return item.version === version; });

      if (evaluationData && evaluationData.results) {
        historicalData += '**AI判定结果：**\n\n';

        // 生成表格格式的AI判定结果
        const aiFields = this.parseFieldsFromPrompt();

        // 表格头部
        historicalData += `| ID | ${aiFields.join(' | ')} |\n`;
        historicalData += `|${Array(aiFields.length + 1).fill('---').join('|')}|\n`;

        // 表格数据
        evaluationData.results.forEach((result, index) => {
          const row = [index + 1];
          aiFields.forEach((field) => {
            row.push(result[field] || '');
          });
          historicalData += `| ${row.join(' | ')} |\n`;
        });

        historicalData += '\n';
      } else {
        historicalData += '**AI判定结果：** 暂无评估结果\n\n';
      }

      historicalData += '---\n\n';
    });

    return historicalData;
  }

  // 从OSS初始化历史数据到组件状态
  initializeHistoryFromOSS = async () => {
    try {
      const historyData = await this.fetchPromptHistoryFromOSS();

      if (historyData && historyData.sessions && historyData.sessions.length > 0) {
        // 获取最新的会话数据
        const latestSession = historyData.sessions[historyData.sessions.length - 1];

        if (latestSession.promptHistory && latestSession.promptHistory.length > 0) {
          // 设置历史提示词和评估数据到组件状态
          this.setState({
            promptHistory: latestSession.promptHistory,
            evaluationHistory: latestSession.evaluationHistory || [],
            currentPromptVersion: latestSession.promptHistory.length,
          });

          console.log(`已加载历史提示词数据，共 ${latestSession.promptHistory.length} 个版本`); // eslint-disable-line no-console
        }
      }
    } catch (error) {
      console.error('初始化OSS历史数据失败:', error); // eslint-disable-line no-console
      // 初始化失败时不影响正常使用，保持默认状态
    }
  }

  // 从OSS读取提示词历史数据
  fetchPromptHistoryFromOSS = async () => {
    try {
      const existingData = await OSSFileHelper.fetchData('prompt-evaluation', 'history');

      if (existingData && typeof existingData === 'object') {
        return existingData;
      }

      // 如果文件不存在或数据格式错误，返回初始结构
      return {
        sessions: [],
        metadata: {
          totalSessions: 0,
          lastUpdated: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error('读取OSS历史数据失败:', error); // eslint-disable-line no-console
      // 返回初始结构
      return {
        sessions: [],
        metadata: {
          totalSessions: 0,
          lastUpdated: new Date().toISOString(),
        },
      };
    }
  }

  // 上传历史信息到OSS
  uploadHistoryToOSS = async () => {
    try {
      const { promptHistory, evaluationHistory } = this.state;

      if (promptHistory.length === 0) {
        return;
      }

      // 读取现有的历史数据
      const existingData = await this.fetchPromptHistoryFromOSS();

      // 生成当前会话的唯一ID
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 构建当前会话数据
      const currentSession = {
        sessionId,
        timestamp: new Date().toISOString(),
        promptHistory,
        evaluationHistory,
        metadata: {
          totalVersions: promptHistory.length,
          userAgent: navigator.userAgent,
        },
      };

      // 将当前会话添加到历史数据中
      const updatedData = {
        sessions: [...existingData.sessions, currentSession],
        metadata: {
          totalSessions: existingData.sessions.length + 1,
          lastUpdated: new Date().toISOString(),
        },
      };

      // 上传更新后的数据到OSS
      await OSSFileHelper.updateData(
        JSON.stringify(updatedData, null, 2),
        'prompt-evaluation',
        'history',
      );

      message.success('历史信息已保存到OSS');
    } catch (error) {
      console.error('上传历史信息失败:', error); // eslint-disable-line no-console
      message.warning('历史信息保存失败，但不影响评估功能');
      // 不阻断主流程，只记录错误
    }
  }

  cleanupOptimizeConnection = () => {
    if (this.optimizeWs) {
      try {
        this.optimizeWs.close();
      } catch (error) {
        console.warn('关闭优化连接时出错:', error); // eslint-disable-line no-console
      }
      this.optimizeWs = null;
    }
    // 清理临时保存的格式字段
    this.savedFormatFields = null;
  }

  // 提取提示词末尾的格式处理字段
  extractFormatFields = (promptText) => {
    try {
      const lines = promptText.split('\n');
      if (lines.length < 2) return { formatFields: '', cleanPrompt: promptText };

      // 查找最后两行：JSON说明行和JSON字段定义行
      const lastLine = lines[lines.length - 1].trim();
      const secondLastLine = lines[lines.length - 2].trim();

      // 检查是否包含JSON格式定义
      if (lastLine.startsWith('{') && lastLine.endsWith('}') &&
        secondLastLine.includes('JSON') && secondLastLine.includes('json.loads')) {
        // 提取最后两行作为格式字段
        const formatFields = `${secondLastLine}\n${lastLine}`;
        // 移除最后两行，保留其余内容
        const cleanPrompt = lines.slice(0, -2).join('\n');
        return { formatFields, cleanPrompt };
      }

      return { formatFields: '', cleanPrompt: promptText };
    } catch (error) {
      console.warn('提取格式字段失败:', error); // eslint-disable-line no-console
      return { formatFields: '', cleanPrompt: promptText };
    }
  }

  // 从提示词中移除格式处理字段
  removeFormatFields = (promptText) => {
    const { cleanPrompt } = this.extractFormatFields(promptText);
    return cleanPrompt;
  }

  // 将格式处理字段重新添加到提示词末尾
  addFormatFields = (promptText, formatFields) => {
    if (!formatFields) return promptText;

    // 确保提示词末尾有换行符，然后添加格式字段
    const trimmedPrompt = promptText.trim();
    return `${trimmedPrompt}\n${formatFields}`;
  }

  // 计算文本差异
  calculateTextDiff = (oldText, newText) => {
    const oldLines = oldText.split('\n');
    const newLines = newText.split('\n');
    const diffResult = [];

    let oldIndex = 0;
    let newIndex = 0;

    while (oldIndex < oldLines.length || newIndex < newLines.length) {
      const oldLine = oldLines[oldIndex];
      const newLine = newLines[newIndex];

      if (oldIndex >= oldLines.length) {
        // 只剩新行，标记为新增
        diffResult.push({ type: 'added', content: newLine, lineNumber: newIndex + 1 });
        newIndex++;
      } else if (newIndex >= newLines.length) {
        // 只剩旧行，标记为删除
        diffResult.push({ type: 'deleted', content: oldLine, lineNumber: oldIndex + 1 });
        oldIndex++;
      } else if (oldLine === newLine) {
        // 行相同，标记为未变更
        diffResult.push({ type: 'unchanged', content: oldLine, lineNumber: oldIndex + 1 });
        oldIndex++;
        newIndex++;
      } else {
        // 行不同，检查是否是修改还是删除/新增
        const nextOldLine = oldLines[oldIndex + 1];
        const nextNewLine = newLines[newIndex + 1];

        if (nextOldLine === newLine) {
          // 旧行被删除
          diffResult.push({ type: 'deleted', content: oldLine, lineNumber: oldIndex + 1 });
          oldIndex++;
        } else if (nextNewLine === oldLine) {
          // 新行被插入
          diffResult.push({ type: 'added', content: newLine, lineNumber: newIndex + 1 });
          newIndex++;
        } else {
          // 行被修改
          diffResult.push({ type: 'modified', content: newLine, oldContent: oldLine, lineNumber: oldIndex + 1 });
          oldIndex++;
          newIndex++;
        }
      }
    }

    return diffResult;
  }

  // 调用优化工作流
  callOptimizeWorkflow = async (promptText, evaluationResults) => { // eslint-disable-line no-unused-vars
    return new Promise((resolve, reject) => {
      try {
        const flowId = FIX_FLOW_MAP[Platform.isProd() ? 'prod' : 'stg'];
        const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflow-v2/run/${flowId}`;
        const query = { access_token: Sessions.getToken() };

        // 提取并保存格式字段，发送纯净的提示词给优化工作流
        const { formatFields } = this.extractFormatFields(promptText);
        this.savedFormatFields = formatFields;

        // 生成包含所有历史版本的提示词数据（使用纯净的提示词）
        const historicalPromptData = this.generateHistoricalPromptData();

        // 获取原始数据（排除AI判定结果）
        const mergedData = this.getMergedData();
        const originalData = mergedData.map((item) => {
          const originalItem = {};
          Object.keys(item).forEach((key) => {
            // 只保留原始Excel数据，排除内部字段和AI评估结果字段
            if (key !== 'key' && key !== 'id' && !key.startsWith('ai_')) {
              originalItem[key] = item[key] !== undefined && item[key] !== null ? String(item[key]) : '';
            }
          });
          return originalItem;
        });

        // 将原始数据转换为 markdown 格式
        const originalMarkdownContent = this.convertOriginalDataToMarkdown(originalData);

        const ws = new ReconnectingWebSocket(
          `${path}?${qs.stringify(query)}`,
          [],
          (e) => { this.onReceiveOptimizeMsg(e, resolve, reject); },
          () => {
            ws.send(JSON.stringify({
              text: JSON.stringify({
                old_prompt: historicalPromptData, // 包含所有历史版本的提示词和AI判定结果
                content: originalMarkdownContent, // 只包含原始Excel数据
              }),
              type: 'message',
              is_beta: false,
            }));
          },
        );

        // 保存WebSocket连接用于清理
        this.optimizeWs = ws;

        // 设置超时处理
        setTimeout(() => {
          if (this.optimizeWs) {
            this.cleanupOptimizeConnection();
            reject(new Error('提示词优化超时，请重试'));
          }
        }, 30 * 60 * 1000); // 30分钟超时
      } catch (error) {
        reject(error);
      }
    });
  }

  // 优化提示词
  onOptimizePrompt = async () => {
    const { promptText, evaluationResults, isOptimizing } = this.state;

    if (isOptimizing) {
      message.warning('正在优化中，请等待当前任务完成');
      return;
    }

    if (!promptText.trim()) {
      message.warning('请先输入提示词！');
      return;
    }

    if (!evaluationResults || evaluationResults.length === 0) {
      message.warning('请先进行评估，获得AI结果后再进行优化！');
      return;
    }

    try {
      this.setState({ isOptimizing: true });
      message.info('开始分析AI评估结果并优化提示词...');

      // 调用修正工作流
      const optimizedPrompt = await this.callOptimizeWorkflow(promptText, evaluationResults);

      this.setState({
        optimizedPrompt,
        showOptimizeModal: true,
        isOptimizing: false,
      });

      message.success('提示词优化完成！');
    } catch (error) {
      console.error('提示词优化失败:', error); // eslint-disable-line no-console
      message.error(error.message || '提示词优化失败，请重试！');
      this.setState({ isOptimizing: false });
    }
  }

  // 处理优化工作流消息
  onReceiveOptimizeMsg = (event, resolve, reject) => {
    if (event?.data !== 'pong') {
      try {
        const originData = JSON.parse(event.data);
        const { type, data } = StringExtension.snakeToCamelObj(originData);

        if (type === EVENT_TYPE.FINAL_RESULT) {
          try {
            const { output } = JSON.parse(data?.output);
            const str = output.split('```')[1] || output; // 获取代码块内容
            const { new_prompt, reason } = JSONC.parse(str || '{}'); // eslint-disable-line

            // 保存优化原因到状态
            this.setState({ optimizeReason: reason || '' });

            // 重新添加之前保存的格式处理字段
            const finalOutput = this.savedFormatFields
              ? this.addFormatFields(new_prompt || '', this.savedFormatFields) // eslint-disable-line
              : (new_prompt || '');// eslint-disable-line

            // 清理连接和临时变量
            this.cleanupOptimizeConnection();
            this.savedFormatFields = null;

            // 解析Promise，返回包含格式字段的完整提示词
            resolve(finalOutput);
          } catch (error) {
            console.error('优化结果解析失败:', error); // eslint-disable-line no-console
            this.cleanupOptimizeConnection();
            this.savedFormatFields = null;
            reject(new Error('优化结果解析失败'));
          }
        } else if (type === EVENT_TYPE.EXEC_FAILED) {
          console.error('优化工作流执行失败:', data); // eslint-disable-line no-console
          this.cleanupOptimizeConnection();
          this.savedFormatFields = null;
          reject(new Error('优化工作流执行失败'));
        }
      } catch (error) {
        console.error('优化消息解析失败:', error); // eslint-disable-line no-console
        this.cleanupOptimizeConnection();
        this.savedFormatFields = null;
        reject(new Error('优化消息解析失败'));
      }
    }
  }

  // 接收WebSocket消息
  onReceiveMsg = (event, itemIndex, resolve, reject) => {
    if (event?.data !== 'pong') {
      try {
        const originData = JSON.parse(event.data);
        const { type, data } = StringExtension.snakeToCamelObj(originData);

        if (type === EVENT_TYPE.FINAL_RESULT) {
          try {
            const { output } = JSON.parse(data?.output);

            // 处理AI返回的结果
            this.handleItemResult(output, itemIndex);

            // 更新完成的数据项数量
            this.setState((prevState) => {
              return {
                completedItems: prevState.completedItems + 1,
              };
            });

            message.success(`数据项 ${itemIndex + 1} 处理完成`);

            // 清理连接
            this.cleanupConnection(itemIndex);

            // 解析Promise
            if (resolve) {
              resolve({ itemIndex, result: output });
            }
          } catch (error) {
            message.error(`数据项 ${itemIndex + 1} 结果解析失败`);
            // 清理连接
            this.cleanupConnection(itemIndex);

            // 拒绝Promise
            if (reject) {
              reject(error);
            }
          }
        }
      } catch (error) {
        console.error(`数据项 ${itemIndex + 1} 消息解析失败:`, error); // eslint-disable-line no-console

        // 清理连接
        this.cleanupConnection(itemIndex);

        if (reject) {
          reject(error);
        }
      }
    }
  }

  // 上传Excel文件
  onUploadExcel = async (option) => {
    this.setState({ isUploading: true, fileName: option.file.name });

    try {
      const data = await this.readExcelFile(option.file);
      // 验证必需的列是否存在
      const requiredColumns = ['内容参考', '风格参考', '结构要求', '最终输出'];
      const missingColumns = requiredColumns.filter((col) => { return !data.headers.includes(col); });

      if (missingColumns.length > 0) {
        throw new Error(`Excel文件缺少必需的列：${missingColumns.join('、')}。请确保文件包含"内容参考"、"风格参考"、"结构要求"、"最终输出"列。`);
      }

      // 验证数据完整性（在这里进行初步验证）
      try {
        this.processExcelData(data.rows, data.headers);
      } catch (validationError) {
        throw validationError;
      }

      // 提取专家评判字段
      const expertFields = this.extractExpertFields(data.headers);

      this.setState({
        excelData: data.rows,
        headers: data.headers,
        expertFields,
        isUploading: false,
        fileName: option.file.name,
        // 重置统计数据
        comparisonStatistics: null,
        showStatistics: false,
      });
      this.clearCache();
      option.onSuccess();

      const expertFieldsText = expertFields.length > 0
        ? `，检测到专家评判字段：${expertFields.join('、')}`
        : '';
      message.success(`Excel文件上传成功！共${data.rows.length}行数据${expertFieldsText}`);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Excel文件解析失败:', error);
      this.setState({ isUploading: false, fileName: '' });
      option.onError();
      message.error(error.message || 'Excel文件解析失败，请检查文件格式！');
    }
  }

  // 打开工作流页面
  onOpenWorkflow = (isFix = false) => {
    const flowMap = isFix ? FIX_FLOW_MAP : FLOW_MAP;
    const flowId = flowMap[Platform.isProd() ? 'prod' : 'stg'];
    window.open(`/workflow-v2/${flowId}`, '_blank');
  }

  // 应用优化后的提示词
  onApplyOptimizedPrompt = () => {
    const { optimizedPrompt, promptHistory } = this.state;

    // 添加新版本到历史
    const description = `第${promptHistory.length}次优化`;
    this.addPromptToHistory(optimizedPrompt, description);

    this.setState({
      promptText: optimizedPrompt,
      showOptimizeModal: false,
    });

    // 保存到localStorage
    this.setStorageData('promptText', optimizedPrompt);

    message.success('提示词已更新！请重新评估以获取新版本的AI判定结果。');
  }

  // 关闭优化弹窗
  onCloseOptimizeModal = () => {
    this.setState({
      showOptimizeModal: false,
      optimizedPrompt: '',
      optimizeReason: '',
    });
  }

  // 开始评估处理
  onEvaluate = async () => {
    const { excelData, headers, isProcessing, promptText, currentPromptVersion } = this.state;

    if (isProcessing) {
      message.warning('正在处理中，请等待当前任务完成');
      return;
    }

    if (!excelData || excelData.length === 0) {
      message.warning('请先上传Excel文件！');
      return;
    }

    if (!promptText.trim()) {
      message.warning('请输入评估提示词！');
      return;
    }

    try {
      // 初始化提示词历史版本
      this.initializePromptHistory();

      this.setState({
        isProcessing: true,
        completedItems: 0,
        allResults: [],
        evaluationResults: [],
      });

      // 处理Excel数据，转换为文本格式
      const processedTexts = this.processExcelData(excelData, headers);

      // 设置总数据项数量
      this.setState({ totalItems: processedTexts.length, completedItems: 0 });

      message.info(`开始并发处理，共 ${processedTexts.length} 条数据，并发数: ${CONCURRENT_LIMIT}`);

      // 根据去缓存选项决定是否为 promptText 添加随机盐值
      const saltedPromptText = this.state.disableCache
        ? Utils.addRandomSalt(promptText)
        : promptText;

      // 并发处理所有数据项
      await this.processItemsConcurrently(
        processedTexts, saltedPromptText,
      );

      // 评估完成后进行对比分析
      this.performComparisonAnalysis();

      // 评估完成后，更新当前版本的AI判定结果
      const { evaluationResults } = this.state;
      if (evaluationResults && evaluationResults.length > 0) {
        this.updateEvaluationHistory(currentPromptVersion || 1, evaluationResults);
        // 上传历史信息到OSS
        this.uploadHistoryToOSS();
      }

      message.success('文稿评估完成！');
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('评估处理失败:', error);
      message.error(error.message || '评估处理失败，请重试！');
    } finally {
      // 清理所有剩余的WebSocket连接
      this.wsConnections.forEach((ws, itemIndex) => {
        this.cleanupConnection(itemIndex);
      });

      this.setState({ isProcessing: false });
    }
  }

  // 导出结果
  onExportResults = () => {
    const mergedData = this.getMergedData();
    const { comparisonStatistics, expertFields } = this.state;

    if (!mergedData || mergedData.length === 0) {
      message.warning('暂无评估结果可导出！');
      return;
    }

    try {
      const { headers } = this.state;

      // 创建工作簿
      const wb = XLSX.utils.book_new();

      // 获取动态评估结果字段
      const evaluationFields = this.parseFieldsFromPrompt();

      // 转换数据格式 - 保持原始Excel列的顺序，然后追加评估结果
      const exportData = mergedData.map((item) => {
        const exportItem = {};

        // 首先添加所有原始Excel列（按原始顺序）
        headers.forEach((header) => {
          exportItem[header] = item[header] || '';
        });

        // 然后追加动态评估结果字段（使用ai_前缀访问AI数据）
        evaluationFields.forEach((field) => {
          const aiFieldName = `ai_${field}`;
          exportItem[field] = item[aiFieldName] || '';
        });

        return exportItem;
      });

      // 创建主数据工作表
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 动态设置列宽 - 为所有列设置合适的宽度
      const colWidths = [];

      // 为原始列设置宽度
      headers.forEach((header) => {
        if (header === '内容参考' || header === '风格参考' || header === '结构要求') {
          colWidths.push({ wch: 30 });
        } else if (header === '最终输出') {
          colWidths.push({ wch: 50 });
        } else {
          colWidths.push({ wch: 15 });
        }
      });

      // 为动态评估结果列设置宽度
      evaluationFields.forEach(() => {
        colWidths.push({ wch: 20 });
      });

      ws['!cols'] = colWidths;

      // 添加主数据工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '文稿评估结果');

      // 如果有对比统计数据，添加统计分析工作表
      if (comparisonStatistics && expertFields.length > 0) {
        const statisticsData = [
          { 项目: '整体分析', 值: `整体一致性: ${comparisonStatistics.overallAccuracy}%` },
          { 项目: '样本数量', 值: comparisonStatistics.totalItems },
          { 项目: '评判维度', 值: expertFields.length },
          { 项目: '', 值: '' }, // 空行分隔
          { 项目: '各维度详细分析', 值: '' },
        ];

        // 添加各维度统计
        expertFields.forEach((field) => {
          const fieldStat = comparisonStatistics.fieldStatistics[field];
          if (fieldStat) {
            statisticsData.push({
              项目: field,
              值: `${fieldStat.accuracy}% (${fieldStat.consistentCount}/${fieldStat.totalCount})`,
            });
          }
        });

        const statsWs = XLSX.utils.json_to_sheet(statisticsData);
        statsWs['!cols'] = [{ wch: 20 }, { wch: 40 }];
        XLSX.utils.book_append_sheet(wb, statsWs, '对比统计分析');
      }

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const fileName = `文稿评估结果_${timestamp}.xlsx`;

      // 导出文件
      XLSX.writeFile(wb, fileName);

      const exportMessage = comparisonStatistics
        ? `成功导出 ${mergedData.length} 条评估结果和对比统计分析`
        : `成功导出 ${mergedData.length} 条评估结果`;
      message.success(exportMessage);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('导出失败:', error);
      message.error('导出失败，请重试！');
    }
  }

  // 处理值变更
  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ [key]: value });
    this.setStorageData(key, value);
  }

  // 渲染差异对比组件
  renderDiffComparison = (oldText, newText) => {
    const diffResult = this.calculateTextDiff(oldText, newText);

    return (
      <div className="diff-container">
        {diffResult.map((line, index) => {
          const key = `diff-line-${index}`;

          switch (line.type) {
            case 'deleted':
              return (
                <div key={key} className="diff-line diff-deleted">
                  <span className="diff-line-number">-{line.lineNumber}</span>
                  <span className="diff-content">{line.content}</span>
                </div>
              );
            case 'added':
              return (
                <div key={key} className="diff-line diff-added">
                  <span className="diff-line-number">+{line.lineNumber}</span>
                  <span className="diff-content">{line.content}</span>
                </div>
              );
            case 'modified':
              return (
                <div key={key}>
                  <div className="diff-line diff-deleted">
                    <span className="diff-line-number">-{line.lineNumber}</span>
                    <span className="diff-content">{line.oldContent}</span>
                  </div>
                  <div className="diff-line diff-added">
                    <span className="diff-line-number">+{line.lineNumber}</span>
                    <span className="diff-content">{line.content}</span>
                  </div>
                </div>
              );
            case 'unchanged':
              return (
                <div key={key} className="diff-line diff-unchanged">
                  <span className="diff-line-number">{line.lineNumber}</span>
                  <span className="diff-content">{line.content}</span>
                </div>
              );
            default:
              return null;
          }
        })}
      </div>
    );
  }

  // 渲染结果表格列
  renderResultColumns = () => {
    const { expertFields } = this.state;

    // ID列单独配置（特殊宽度和对齐方式）
    const idColumn = TableColumnHelper.createTableColumn(
      { title: 'ID', dataIndex: 'id', key: 'id', width: 80, align: 'center' },
    );

    // 基础文本列配置
    const baseTextColumnConfigs = [
      { title: '内容参考', dataIndex: '内容参考', key: 'contentRef' },
      { title: '风格参考', dataIndex: '风格参考', key: 'styleRef' },
      { title: '结构要求', dataIndex: '结构要求', key: 'structure' },
      { title: '最终输出', dataIndex: '最终输出', key: 'output' },
    ];

    // 使用工具函数创建标准化的基础文本列
    const baseTextColumns = TableColumnHelper.createBaseTextColumns(baseTextColumnConfigs);

    // 使用通用函数创建专家评判字段列
    const expertColumns = TableColumnHelper.createColumnsFromFields(expertFields, '专家', 'expert');

    // 获取动态AI评估字段并创建列
    const dynamicFields = this.parseFieldsFromPrompt();
    const aiColumns = TableColumnHelper.createColumnsFromFields(dynamicFields, 'AI', 'ai', {
      dataIndexPrefix: 'ai_', // 使用ai_前缀访问AI评估数据
    });

    return [idColumn, ...baseTextColumns, ...expertColumns, ...aiColumns];
  }

  // 渲染AI判定结果表格列
  renderAIResultColumns = () => {
    const dynamicFields = this.parseFieldsFromPrompt();

    const idColumn = {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      fixed: 'left',
    };

    const aiColumns = dynamicFields.map((field) => {
      return {
        title: field,
        dataIndex: field,
        key: field,
        width: 120,
        ellipsis: true,
        render: (text) => {
          return (
            <span title={text} style={{ fontSize: 12 }}>
              {text || '-'}
            </span>
          );
        },
      };
    });

    return [idColumn, ...aiColumns];
  }

  // 渲染历史版本抽屉
  renderHistoryDrawer = () => {
    const { showHistoryModal, promptHistory, evaluationHistory, currentPromptVersion } = this.state;

    return (
      <Drawer
        title="提示词历史版本"
        open={showHistoryModal}
        onClose={() => { this.setState({ showHistoryModal: false }); }}
        width="60vw"
        placement="right"
      >
        <div style={{ maxHeight: '100%', overflow: 'auto' }}>
          {promptHistory.map((version) => {
            const evaluationData = evaluationHistory.find((item) => { return item.version === version.version; });
            const isCurrent = version.version === currentPromptVersion;

            return (
              <div
                key={version.version}
                style={{
                  marginBottom: 24,
                  padding: 16,
                  border: isCurrent ? '2px solid #1890ff' : '1px solid #d9d9d9',
                  borderRadius: 8,
                  backgroundColor: isCurrent ? '#f6ffed' : '#fafafa',
                }}
              >
                <div style={{ marginBottom: 12 }}>
                  <Space>
                    <span style={{ fontWeight: 'bold', fontSize: 16 }}>
                      版本{version.version} ({version.description})
                    </span>
                    {isCurrent && (
                      <span style={{ color: '#52c41a', fontSize: 12 }}>当前使用</span>
                    )}
                    <span style={{ color: '#666', fontSize: 12 }}>
                      {new Date(version.timestamp).toLocaleString()}
                    </span>
                  </Space>
                </div>

                <div style={{ marginBottom: 12 }}>
                  <strong>提示词内容：</strong>
                  <div style={{
                    marginTop: 8,
                    padding: 12,
                    backgroundColor: '#fff',
                    border: '1px solid #d9d9d9',
                    borderRadius: 4,
                    maxHeight: 200,
                    overflow: 'auto',
                    fontSize: 12,
                    lineHeight: 1.5,
                  }}
                  >
                    {version.prompt}
                  </div>
                </div>

                {evaluationData && evaluationData.results && (
                  <div>
                    <strong>AI判定结果：</strong>
                    <div style={{ marginTop: 8, marginBottom: 8 }}>
                      <Space>
                        <span>总数：{evaluationData.statistics.total}</span>
                        <span>合格：{evaluationData.statistics.qualified}</span>
                        <span>合格率：{evaluationData.statistics.qualifiedRate}%</span>
                        <span style={{ color: '#666', fontSize: 12 }}>
                          更新时间：{new Date(evaluationData.timestamp).toLocaleString()}
                        </span>
                      </Space>
                    </div>

                    {/* 展示完整的AI判定结果 */}
                    <div style={{
                      maxHeight: 300,
                      overflow: 'auto',
                      border: '1px solid #d9d9d9',
                      borderRadius: 4,
                      backgroundColor: '#fafafa',
                    }}
                    >
                      <Table
                        size="small"
                        dataSource={evaluationData.results.map((result, index) => {
                          return {
                            key: index,
                            id: index + 1,
                            ...result,
                          };
                        })}
                        columns={this.renderAIResultColumns()}
                        pagination={false}
                        scroll={{ x: 'max-content' }}
                        style={{ margin: 0 }}
                      />
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </Drawer>
    );
  }

  // 渲染优化提示词弹窗
  renderOptimizeModal = () => {
    const { showOptimizeModal, optimizedPrompt, promptText, optimizeReason } = this.state;

    return (
      <Modal
        title="提示词优化结果"
        open={showOptimizeModal}
        onCancel={this.onCloseOptimizeModal}
        width={1200}
        footer={[
          <Button key="cancel" onClick={this.onCloseOptimizeModal}>
            取消
          </Button>,
          <Button key="apply" type="primary" onClick={this.onApplyOptimizedPrompt}>
            应用修正
          </Button>,
        ]}
        className="optimize-prompt-modal"
      >
        {/* 优化原因展示 */}
        {optimizeReason && (
          <div style={{ marginBottom: 20 }}>
            <h4 style={{ marginBottom: 8, color: '#1890ff', fontSize: 16 }}>
              <span style={{ marginRight: 8 }} role="img" aria-label="灯泡">💡</span>
              优化原因：
            </h4>
            <div style={{
              background: '#e6f7ff',
              padding: 16,
              borderRadius: 8,
              border: '1px solid #91d5ff',
              fontSize: 14,
              lineHeight: 1.6,
              color: '#0050b3',
            }}
            >
              <ReactMarkdown remarkPlugins={[remarkGfm]}>{optimizeReason}</ReactMarkdown>
            </div>
          </div>
        )}

        {/* 差异对比展示 */}
        <div style={{ marginBottom: 16 }}>
          <h4 style={{ marginBottom: 12, color: '#333', fontSize: 16 }}>
            <span style={{ marginRight: 8 }} role="img" aria-label="对比">🔄</span>
            提示词对比：
          </h4>
          <div style={{
            border: '1px solid #d9d9d9',
            borderRadius: 8,
            maxHeight: 400,
            overflow: 'auto',
            background: '#fafafa',
          }}
          >
            {this.renderDiffComparison(promptText, optimizedPrompt)}
          </div>
        </div>

        <div style={{
          marginTop: 16,
          padding: 12,
          background: '#fff7e6',
          borderRadius: 6,
          border: '1px solid #ffd591',
        }}
        >
          <p style={{ margin: 0, fontSize: 13, color: '#d46b08' }}>
            <strong>提示：</strong>点击&quot;应用修正&quot;将使用优化后的提示词替换当前提示词。红色表示删除，绿色表示新增，黄色表示修改。
          </p>
        </div>
      </Modal>
    );
  }

  render = () => {
    const {
      promptText, fileName, isUploading, isProcessing, completedItems, totalItems,
      comparisonStatistics, showStatistics, expertFields,
    } = this.state;

    // 获取合并后的数据用于展示
    const mergedData = this.getMergedData();

    return (
      <div className="chat-knowledge script-evaluation-container" style={{ padding: 30, background: '#fff' }}>
        <Form layout="vertical">
          <Form.Item label="Excel文件上传">
            <Upload.Dragger
              accept={ACCEPT_TYPES}
              customRequest={this.onUploadExcel}
              showUploadList={false}
              disabled={isUploading}
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">
                {isUploading ? '正在解析Excel文件...' : '点击或拖拽Excel文件到此区域上传'}
              </p>
              <p className="ant-upload-hint">
                支持.xlsx和.xls格式的Excel文件
              </p>
              {fileName && (
                <p style={{ color: '#52c41a', marginTop: 8 }}>
                  已上传: {fileName}
                </p>
              )}
            </Upload.Dragger>
          </Form.Item>

          <Form.Item
            label={
              <Space>
                <span>评估提示词</span>
                <Button
                  type="link"
                  size="small"
                  onClick={() => { return this.onOpenWorkflow(); }}
                  style={{ padding: 0, height: 'auto' }}
                >
                  打开工作流 [文稿判定]
                </Button>
                <Button
                  type="link"
                  size="small"
                  onClick={() => { return this.onOpenWorkflow(true); }}
                  style={{ padding: 0, height: 'auto' }}
                >
                  打开工作流 [修正提示词]
                </Button>
                <Button
                  type="link"
                  size="small"
                  onClick={() => { this.setState({ showHistoryModal: true }); }}
                  style={{ padding: 0, height: 'auto' }}
                  disabled={this.state.promptHistory.length === 0}
                >
                  查看历史版本 ({this.state.promptHistory.length})
                </Button>
                <Checkbox
                  checked={this.state.disableCache}
                  onChange={(e) => { return this.setState({ disableCache: e.target.checked }); }}
                >
                  去缓存
                </Checkbox>
              </Space>
            }
            className="grid-item"
          >
            <Input.TextArea
              rows={4}
              value={promptText}
              placeholder="请输入文稿评估的提示词..."
              onChange={(e) => { return this.onChangeValue(e, 'promptText'); }}
            />
          </Form.Item>
          <Form.Item className="evaluation-button">
            <Space size="large">
              <Button
                type="primary"
                size="large"
                onClick={this.onEvaluate}
                loading={isProcessing}
                disabled={isProcessing || !fileName}
              >
                {isProcessing ? `处理中 (${completedItems}/${totalItems})` : '开始评估'}
              </Button>
              <Button
                type="default"
                size="large"
                onClick={this.onOptimizePrompt}
                loading={this.state.isOptimizing}
                disabled={this.state.isOptimizing || isProcessing || !this.state.evaluationResults.length}
              >
                {this.state.isOptimizing ? '优化中...' : '优化提示词'}
              </Button>
            </Space>
            {isProcessing && (
              <div style={{ marginTop: 8, color: '#1890ff' }}>
                正在处理文稿评估，已完成 {completedItems} 条，共 {totalItems} 条数据...
              </div>
            )}
            {this.state.isOptimizing && (
              <div style={{ marginTop: 8, color: '#52c41a' }}>
                正在分析AI评估结果并优化提示词...
              </div>
            )}
          </Form.Item>
        </Form>

        {/* 统计分析报告 */}
        {
          showStatistics && comparisonStatistics && expertFields.length > 0 && (
            <div className="statistics-section" style={{ marginTop: 24, marginBottom: 24 }}>
              <h3>AI与专家对比分析报告</h3>
              <div style={{ background: '#f5f5f5', padding: 16, borderRadius: 6, marginBottom: 16 }}>
                <div style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>
                  整体一致性: {comparisonStatistics.overallAccuracy}%
                </div>
                <div style={{ color: '#666' }}>
                  共分析 {comparisonStatistics.totalItems} 个样本，
                  涉及 {expertFields.length} 个评判维度
                </div>
              </div>

              <div className="field-statistics" style={{ marginBottom: 16 }}>
                <h4>各维度一致性分析</h4>
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: 16 }}>
                  {expertFields.map((field) => {
                    const fieldStat = comparisonStatistics.fieldStatistics[field];
                    if (!fieldStat) return null;

                    const getColor = (accuracy) => {
                      if (accuracy >= 90) return '#52c41a';
                      if (accuracy >= 80) return '#1890ff';
                      if (accuracy >= 70) return '#faad14';
                      return '#f5222d';
                    };

                    return (
                      <div
                        key={field}
                        style={{
                          background: '#fff',
                          border: '1px solid #d9d9d9',
                          borderRadius: 6,
                          padding: 12,
                          minWidth: 200,
                        }}
                      >
                        <div style={{ fontWeight: 'bold', marginBottom: 4 }}>{field}</div>
                        <div style={{ color: getColor(fieldStat.accuracy), fontSize: 18, fontWeight: 'bold' }}>
                          {fieldStat.accuracy}%
                        </div>
                        <div style={{ color: '#666', fontSize: 12 }}>
                          {fieldStat.consistentCount}/{fieldStat.totalCount} 一致
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )
        }

        {/* 评估结果展示 */}
        {
          mergedData && mergedData.length > 0 && (
            <div className="results-section">
              <div className="results-header">
                <h3>评估结果 ({mergedData.length} 条)</h3>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={this.onExportResults}
                  className="export-btn"
                >
                  导出结果
                </Button>
              </div>
              <Table
                className="results-table"
                columns={this.renderResultColumns()}
                dataSource={mergedData}
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => { return `第 ${range[0]}-${range[1]} 条，共 ${total} 条`; },
                }}
                scroll={{ x: 1200 }}
              />
            </div>
          )
        }

        {/* 提示词优化弹窗 */}
        {this.renderOptimizeModal()}

        {/* 历史版本抽屉 */}
        {this.renderHistoryDrawer()}
      </div>
    );
  }
}
