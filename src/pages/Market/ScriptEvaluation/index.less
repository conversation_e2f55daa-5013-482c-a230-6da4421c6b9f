.script-evaluation-container {
  .evaluation-button {
    margin-top: 20px;
    text-align: center;

    .ant-btn {
      min-width: 200px;
      height: 50px;
      font-size: 16px;
      font-weight: 500;
    }

    .ant-space {
      display: flex;
      justify-content: center;
    }
  }

  .ant-upload-drag {
    border: 2px dashed #d9d9d9;
    background: #fafafa;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      background: #f0f8ff;
      border-color: #1890ff;
    }

    .ant-upload-drag-icon {
      margin-bottom: 16px;
      font-size: 48px;
      color: #1890ff;
    }

    .ant-upload-text {
      margin-bottom: 8px;
      font-size: 16px;
      color: #666;
    }

    .ant-upload-hint {
      font-size: 14px;
      color: #999;
    }
  }

  .ant-form-item-label > label {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  .ant-input {
    border: 1px solid #d9d9d9;
    border-radius: 6px;

    &:focus,
    &:hover {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  .ant-form-item {
    margin-bottom: 24px;
  }

  // 网格布局样式
  .textarea-grid {
    display: grid;
    margin-bottom: 24px;
    gap: 20px;
    grid-template-columns: 1fr 1fr;

    .grid-item {
      margin-bottom: 0;
    }

    .grid-item-full {
      grid-column: 1 / -1;
    }

    // 响应式设计 - 小屏幕时改为单列
    @media (max-width: 768px) {
      gap: 16px;
      grid-template-columns: 1fr;
    }
  }

  // 全宽度表单项
  .full-width-item {
    margin-bottom: 24px;
  }

  .processing-info {
    margin-top: 16px;
    padding: 12px 16px;
    border: 1px solid #b7eb8f;
    background: #f6ffed;
    font-size: 14px;
    color: #52c41a;
    border-radius: 6px;
  }

  .results-section {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;

    .results-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .export-btn {
        background: #52c41a;
        border-color: #52c41a;

        &:hover {
          background: #73d13d;
          border-color: #73d13d;
        }
      }
    }

    .results-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
        color: #333;
      }

      .score-cell {
        text-align: center;
        font-weight: 500;

        &.high-score {
          color: #52c41a;
        }

        &.medium-score {
          color: #faad14;
        }

        &.low-score {
          color: #ff4d4f;
        }
      }
    }
  }
}

// 优化提示词弹窗样式
.optimize-prompt-modal {
  .ant-modal-body {
    padding: 24px;
  }

  .ant-modal-footer {
    padding: 16px 24px;
    text-align: center;

    .ant-btn {
      min-width: 100px;
      height: 40px;
      margin: 0 8px;
      font-size: 14px;
    }
  }

  h4 {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 600;
  }

  pre {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    color: #333;
  }

  // Diff 对比样式
  .diff-container {
    background: #fff;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;

    .diff-line {
      display: flex;
      align-items: flex-start;
      min-height: 20px;
      padding: 2px 8px;
      border-left: 3px solid transparent;

      .diff-line-number {
        display: inline-block;
        flex-shrink: 0;
        width: 50px;
        margin-right: 12px;
        text-align: right;
        font-weight: 500;
        color: #666;
        user-select: none;
      }

      .diff-content {
        flex: 1;
        white-space: pre-wrap;
        word-break: break-word;
      }

      &.diff-deleted {
        background-color: #ffebee;
        border-left-color: #f44336;

        .diff-line-number {
          color: #c62828;
        }

        .diff-content {
          color: #c62828;
          text-decoration: line-through;
        }
      }

      &.diff-added {
        background-color: #e8f5e8;
        border-left-color: #4caf50;

        .diff-line-number {
          color: #2e7d32;
        }

        .diff-content {
          color: #2e7d32;
        }
      }

      &.diff-modified {
        background-color: #fff3e0;
        border-left-color: #ff9800;

        .diff-line-number {
          color: #ef6c00;
        }

        .diff-content {
          color: #ef6c00;
        }
      }

      &.diff-unchanged {
        background-color: #fff;
        border-left-color: transparent;

        .diff-line-number {
          color: #999;
        }

        .diff-content {
          color: #333;
        }
      }

      &:hover {
        background-color: rgba(0, 0, 0, 0.02);
      }
    }
  }
}
