/**
 * 数据处理和转换工具模块
 * 提供 JSON 解析、数据映射、字段提取和综合转换功能
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

/**
 * 安全的 JSON 解析函数
 * @param {string} jsonString - 要解析的 JSON 字符串
 * @param {*} defaultValue - 解析失败时返回的默认值，默认为 null
 * @returns {*} 解析后的对象或默认值
 *
 * @example
 * const result = parseJSON('{"name": "test"}', {});
 * // 返回: {name: "test"}
 *
 * const invalid = parseJSON('invalid json', []);
 * // 返回: []
 */
export const parseJSON = (jsonString, defaultValue = null) => {
  // 输入验证
  if (typeof jsonString !== 'string') { // eslint-disable-next-line
    console.warn('parseJSON: 输入参数必须是字符串类型', { input: jsonString });
    return defaultValue;
  }

  // 空字符串处理
  if (!jsonString.trim()) {
    return defaultValue;
  }

  try {
    // 使用安全的 JSON.parse 进行解析
    const parsed = JSON.parse(jsonString);

    // 防止原型污染检查
    if (parsed && typeof parsed === 'object' && parsed.constructor !== Object && parsed.constructor !== Array) {
      console.warn('parseJSON: 检测到可能的原型污染，返回默认值'); // eslint-disable-line
      return defaultValue;
    }

    return parsed;
  } catch (error) { // eslint-disable-next-line
    console.warn('parseJSON: JSON 解析失败', {
      error: error.message,
      input: jsonString.substring(0, 100), // 只记录前100个字符
      defaultValue,
    });
    return defaultValue;
  }
};

/**
 * 安全的深层对象属性访问
 * @param {Object} obj - 目标对象
 * @param {string} path - 属性路径，支持点号分隔和数组索引
 * @param {*} defaultValue - 访问失败时的默认值
 * @returns {*} 属性值或默认值
 *
 * @example
 * const obj = { user: { profile: { name: 'John' }, tags: ['admin', 'user'] } };
 * getNestedValue(obj, 'user.profile.name'); // 返回: 'John'
 * getNestedValue(obj, 'user.tags.0'); // 返回: 'admin'
 * getNestedValue(obj, 'user.invalid.path', 'default'); // 返回: 'default'
 */
// eslint-disable-next-line no-unused-vars
const getNestedValue = (obj, path, defaultValue = undefined) => {
  if (!obj || typeof obj !== 'object' || typeof path !== 'string') {
    return defaultValue;
  }

  try {
    // 分割路径并处理数组索引
    const keys = path.split('.').filter((key) => { return key !== ''; });
    let current = obj;

    for (const key of keys) {
      // 检查是否为数组索引
      if (/^\d+$/.test(key)) {
        const index = parseInt(key, 10);
        if (Array.isArray(current) && index >= 0 && index < current.length) {
          current = current[index];
        } else {
          return defaultValue;
        }
      } else {
        // 安全的对象属性访问
        // eslint-disable-next-line
        if (current && typeof current === 'object' && Object.prototype.hasOwnProperty.call(current, key)) {
          current = current[key];
        } else {
          return defaultValue;
        }
      }
    }

    return current;
  } catch (error) { // eslint-disable-next-line
    console.warn('getNestedValue: 属性访问失败', { path, error: error.message });
    return defaultValue;
  }
};

/**
 * 安全的类型转换函数
 * @param {*} value - 要转换的值
 * @param {string} targetType - 目标类型 ('string', 'number', 'boolean', 'array', 'object')
 * @param {*} defaultValue - 转换失败时的默认值
 * @returns {*} 转换后的值或默认值
 */
// eslint-disable-next-line no-unused-vars
const convertType = (value, targetType, defaultValue = null) => {
  if (value === null || value === undefined) {
    return defaultValue;
  }

  try {
    switch (targetType.toLowerCase()) {
      case 'string':
        return String(value);

      case 'number': {
        const num = Number(value);
        return Number.isNaN(num) ? defaultValue : num;
      }

      case 'boolean':
        if (typeof value === 'boolean') return value;
        if (typeof value === 'string') {
          const lower = value.toLowerCase();
          if (lower === 'true' || lower === '1' || lower === 'yes') return true;
          if (lower === 'false' || lower === '0' || lower === 'no') return false;
        }
        return Boolean(value);

      case 'array':
        if (Array.isArray(value)) return value;
        if (typeof value === 'string') {
          try {
            const parsed = JSON.parse(value);
            return Array.isArray(parsed) ? parsed : [value];
          } catch {
            return [value];
          }
        }
        return [value];

      case 'object':
        if (value && typeof value === 'object' && !Array.isArray(value)) return value;
        if (typeof value === 'string') {
          try {
            const parsed = JSON.parse(value);
            return (parsed && typeof parsed === 'object' && !Array.isArray(parsed)) ? parsed : defaultValue;
          } catch {
            return defaultValue;
          }
        }
        return defaultValue;

      default:
        return value;
    }
  } catch (error) { // eslint-disable-next-line
    console.warn('convertType: 类型转换失败', { value, targetType, error: error.message });
    return defaultValue;
  }
};
