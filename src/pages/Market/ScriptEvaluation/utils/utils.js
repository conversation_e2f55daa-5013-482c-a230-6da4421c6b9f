/**
 * 通用工具类
 * 提供文本处理、防缓存等实用功能
 */
export default class Utils {
  /**
   * 为文本随机添加空格盐值，防止 LLM 缓存
   * @param {string} text - 原始文本
   * @param {object} options - 配置选项
   * @param {number} options.minSpaces - 最小空格数，默认1
   * @param {number} options.maxSpaces - 最大空格数，默认3
   * @param {number} options.probability - 在每个插入点添加空格的概率，默认0.3
   * @returns {string} 添加随机空格后的文本
   */
  static addRandomSalt(text, options = {}) {
    // 输入验证
    if (typeof text !== 'string') {
      return '';
    }

    if (!text.trim()) {
      return text;
    }

    const {
      minSpaces = 1,
      maxSpaces = 3,
      probability = 0.3,
    } = options;

    // 定义安全的插入点：句号、换行符、逗号、冒号、分号后
    const safeInsertPoints = /([。\n，：；])/g;

    let result = text;
    let offset = 0;

    // 查找所有安全插入点
    const matches = [...text.matchAll(safeInsertPoints)];

    matches.forEach((match) => {
      // 根据概率决定是否在此位置插入空格
      if (Math.random() < probability) {
        const insertPosition = match.index + 1 + offset;

        // 随机生成1-3个空格
        const spaceCount = Math.floor(Math.random() * (maxSpaces - minSpaces + 1)) + minSpaces;
        const spaces = ' '.repeat(spaceCount);

        // 插入空格
        result = result.slice(0, insertPosition) + spaces + result.slice(insertPosition);
        offset += spaceCount;
      }
    });

    return result;
  }

  /**
   * 为文本添加时间戳盐值（预留扩展方法）
   * @param {string} text - 原始文本
   * @returns {string} 添加时间戳后的文本
   */
  static addTimestampSalt(text) {
    if (typeof text !== 'string') {
      return '';
    }

    const timestamp = Date.now();
    return `${text}\n<!-- timestamp: ${timestamp} -->`;
  }

  /**
   * 生成随机字符串（预留扩展方法）
   * @param {number} length - 字符串长度
   * @returns {string} 随机字符串
   */
  static generateRandomString(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}
