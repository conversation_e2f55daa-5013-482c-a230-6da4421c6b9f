/* eslint-disable react/prop-types */

import { UploadOutlined } from '@ant-design/icons';
import { Button, Select, Upload } from 'antd';
import React, { useState } from 'react';

const AudioUploader = ({ onUpload }) => {
  const [asrProvider, setAsrProvider] = useState(null);

  const handleUpload = (file) => {
    if (asrProvider) {
      onUpload(file, asrProvider);
    }
    return false; // 阻止默认上传行为，用于手动处理上传
  };

  return (
    <div>
      <Select
        style={{ width: 200 }}
        placeholder="选择ASR服务"
        onChange={setAsrProvider}
      >
        <Select.Option value="dash-scope">灵积</Select.Option>
        <Select.Option value="aliyun">阿里云</Select.Option>
        <Select.Option value="bytedance">火山</Select.Option>
      </Select>
      <Upload
        beforeUpload={handleUpload}
        showUploadList={false}
        disabled={asrProvider == null}
        accept="audio/*"
      >
        <Button disabled={asrProvider == null} icon={<UploadOutlined />}>上传音频</Button>
      </Upload>
    </div>
  );
};

export default AudioUploader;
