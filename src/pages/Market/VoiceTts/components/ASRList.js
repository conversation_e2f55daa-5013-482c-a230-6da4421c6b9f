/* eslint-disable react/prop-types */
import { List } from 'antd';
import React from 'react';

import ListItem from './ListItem';

const ASRList = ({ items, speakers, onTextChange, onGenerateAudio }) => {
  return (
    <List
      dataSource={items}
      renderItem={(item, index) => {
 return (
   <ListItem
     key={item.id}
     item={item}
     speakers={speakers}
     onTextChange={(text) => { return onTextChange(index, text); }}
     onGenerateAudio={(service) => { return onGenerateAudio(index, service); }}
   />
      );
}}
    />
  );
};

export default ASRList;
