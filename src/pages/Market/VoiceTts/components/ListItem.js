/* eslint-disable react/prop-types */
import { Button, Input, Select } from 'antd';
import React, { useState } from 'react';

const ListItem = ({ item, speakers, onTextChange, onGenerateAudio }) => {
  const [text, setText] = useState(item.text);
  const [selectedService, setSelectedService] = useState(null);
  const [generating, setGenerating] = useState(false);

  return (
    <div>
      <div style={{ display: 'flex', marginBottom: 10 }}>
        <Input
          style={{ flex: 1 }}
          value={text}
          onChange={(e) => {
            setText(e.target.value);
            onTextChange(e.target.value);
          }}
        />
        <Select
          style={{ width: 250, marginLeft: 10 }}
          placeholder="选择TTS服务"
          options={speakers}
          onChange={setSelectedService}
        />
        <Button
          type="primary"
          loading={generating}
          onClick={async () => {
            setGenerating(true);
            const selected = speakers.find((option) => { return option.value === selectedService; });
            await onGenerateAudio(selected);
            setGenerating(false);
          }}
          disabled={!selectedService}
          style={{ marginLeft: 10 }}
        >
          生成音频
        </Button>
      </div>
      {item.audioUrl && (
        <audio controls src={item.audioUrl} style={{ marginLeft: 10 }} />
      )}
    </div>
  );
};

export default ListItem;
