/* eslint-disable react/prop-types */
import { Accounts, AliyunHelper, Market } from '~/engine';
import { Button, Card, Input, Select } from 'antd';
import React, { useEffect, useState } from 'react';

// import Azure from '../VoiceClone/Detail/azure';
import ASRList from './components/ASRList';
import AudioUploader from './components/AudioUploader';

export default () => {
  const [sentences, setSentences] = useState([]);
  const [speakers, setSpeakers] = useState([]);
  const [generating, setGenerating] = useState(false);

  const [selectedService, setSelectedService] = useState(null);

  const loadData = async () => {
    const tmpSpeakers = await Accounts.fetchTtsSpeakers();

    const cloneSpeakers = Object.values(await Market.fetchVoiceCloneSpeakers());
    for (const s of Object.values(cloneSpeakers)) {
      s.label = s.speaker;
      s.value = s.speaker;
      s.tts_settings = { provider: 'gpt-sovits', voice: s.speaker, ref: s.refs[0] };
      tmpSpeakers.push(s);
    }

    tmpSpeakers.push({
      label: '火山-汤山老王',
      value: 'S_DPrVCAF41',
      tts_settings: { provider: 'bytedance-re', voice: 'S_DPrVCAF41' },
    });

    setSpeakers(tmpSpeakers);
  };

  useEffect(() => {
    loadData();
  }, []);

  const handleUpload = async (file, asrProvider) => {
    const url = await AliyunHelper.uploadMp3(file);
    const result = await Market.audioToText({ file: url, provider: asrProvider });
    let startIndex = 0;
    if (sentences.length > 0) {
      startIndex = sentences[sentences.length - 1].id + 1;
    }
    for (let i = 0; i < result.sentences.length; i++) {
      result.sentences[i].id = startIndex + i;
    }
    setSentences(result.sentences);
  };

  const handleTextChange = (index, newText) => {
    const updatedSentences = [...sentences];
    updatedSentences[index].text = newText;
    setSentences(updatedSentences);
  };

  const handleGenerateAudio = async (index, service) => {
    // 调用文本转音频服务，并更新音频URL（模拟）
    const updatedSentences = [...sentences];

    const s = sentences[index];
    const { fileUrl } = await Market.textToAudio({ text: s.text, tts_settings: service.tts_settings });
    updatedSentences[index].audioUrl = fileUrl;
    setSentences(updatedSentences);
  };

  const handleExport = async () => {
    let text = '';
    for (const s of sentences) {
      text += `${s.text}`;
    }
    const service = speakers.find((option) => { return option.value === selectedService; });
    setGenerating(true);
    const { fileUrl } = await Market.textToAudio({ text, tts_settings: service.tts_settings });
    setGenerating(false);
    window.open(fileUrl);
  };

  return (
    <Card
      title="ASR 和 文字转音频对比"
      extra={
        sentences.length > 0 ?
          <Input.Group compact>
            <Select
              style={{ width: 250 }}
              placeholder="选择TTS服务"
              options={speakers}
              onChange={setSelectedService}
            />
            <Button loading={generating} disabled={!selectedService} type="primary" onClick={handleExport}>导出</Button>
          </Input.Group> : null
      }
      style={{ marginTop: '20px' }}
    >
      <AudioUploader onUpload={handleUpload} />
      <br />
      <ASRList
        items={sentences}
        speakers={speakers}
        onTextChange={handleTextChange}
        onGenerateAudio={handleGenerateAudio}
      />
    </Card>
  );
};
