.gpt-image-container {
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  padding: 20px;

  .conversation-group:hover {
    background: #e2e2e2;
  }

  .chat-area {
    flex: 1;
    margin-bottom: 20px;
    overflow: hidden;

    .messages-container {
      height: 100%;
      overflow-y: auto;
      padding: 20px;
      background: #f5f5f5;
      border-radius: 8px;

      .message {
        margin-bottom: 20px;
        display: flex;
        flex-direction: column;

        &.user-message {
          align-items: flex-end;

          .message-content {
            white-space: pre-wrap;
            background: #1890ff;
            color: white;
            padding: 12px 16px;
            border-radius: 18px 18px 4px 18px;
            max-width: 70%;

            .ref-images {
              margin-top: 8px;
              display: flex;
              gap: 8px;
              flex-wrap: wrap;

              .ref-image {
                width: 60px;
                height: 60px;
                object-fit: cover;
                border-radius: 4px;
                border: 2px solid rgba(255, 255, 255, 0.3);
              }
            }

            .user-input-details {
              margin-top: 8px;
              padding-top: 8px;
              border-top: 1px solid rgba(255, 255, 255, 0.2);

              .detail-item {
                font-size: 12px;
                opacity: 0.8;
                margin-bottom: 2px;
              }
            }

            .apply-btn {
              margin-top: 8px;
              padding: 0;
              height: auto;
              color: rgba(255, 255, 255, 0.8);

              &:hover {
                color: white;
              }
            }
          }
        }

        &.ai-message {
          align-items: flex-start;

          .message-content {
            background: white;
            color: #333;
            padding: 12px 16px;
            border-radius: 18px 18px 18px 4px;
            max-width: 70%;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            .generated-image {
              margin-top: 8px;

              img {
                max-width: 300px;
                max-height: 300px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              }
            }
          }

          &.text-loading {
            align-items: center;

            .message-content {
              display: flex;
              align-items: center;
              gap: 8px;
            }
          }
        }

        .message-time {
          font-size: 12px;
          color: #999;
          margin-top: 4px;
          padding: 0 16px;
        }
      }
    }
  }

  .input-area {
    .setting-item {
      margin-bottom: 16px;

      .ant-typography {
        display: block;
        margin-bottom: 4px;
        font-weight: 500;
      }
    }

    .ref-images-section {
      margin: 16px 0;

      .ant-typography {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .ref-images-list {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        .ref-image-item {
          position: relative;
          width: 80px;
          height: 80px;

          .ref-image-thumb {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
            border: 1px solid #d9d9d9;
          }

          .delete-btn {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff4d4f;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;

            &:hover {
              background: #ff7875;
            }
          }
        }

        .upload-btn {
          .upload-placeholder {
            width: 80px;
            height: 80px;
            border: 2px dashed #d9d9d9;
            border-radius: 4px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #999;
            font-size: 12px;
            text-align: center;

            &:hover {
              border-color: #1890ff;
              color: #1890ff;
            }
          }
        }
      }
    }

    .prompt-input-section {
      display: flex;
      gap: 12px;
      align-items: flex-end;

      .ant-input {
        flex: 1;
      }

      .send-btn {
        height: 40px;
      }
    }
  }
}
