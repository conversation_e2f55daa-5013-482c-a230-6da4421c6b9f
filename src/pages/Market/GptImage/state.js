import { Accounts, ChatBot, Market } from '~/engine';
import _ from 'lodash';

// import Azure from '../VoiceClone/Detail/azure';

const SET_STATE = 'MARKET_GPT_IMAGE/SET_STATE';
const CLEAR_STATE = 'MARKET_GPT_IMAGE/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

const _getInitState = () => {
  return {
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
