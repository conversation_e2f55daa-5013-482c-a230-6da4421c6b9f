import './index.less';

import { <PERSON><PERSON>, Card, Col, Input, List, Row, Select, Spin, Tag, Typography, message, Upload, Image } from 'antd';
import { PlusOutlined, SendOutlined, DeleteOutlined } from '@ant-design/icons';
import _ from 'lodash';
import { PropTypes } from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';

import { InputUpload } from '~/components';
import { AliyunHelper, Market } from '~/engine';
import reducer, * as actions from './state';

const { TextArea } = Input;
const { Title, Text } = Typography;

@connect(
  (state) => {
    return state.marketGptImage;
  },
  actions,
)
export default class GptImage extends Component {
  static propTypes = {
  };

  constructor(props) {
    super(props);
    this.state = {
      prompt: '',
      refImages: [],
      size: 'auto',
      quality: 'low',
      transparent: true,
      loading: false,
      messages: [],
      uploadFileList: [], // 添加文件列表状态
      // 新增状态
      conversations: [],
      conversationLoading: false,
      hasMore: true,
      pageIndex: 1,
      pageSize: 20,
    };
  }

  async componentDidMount() {
    await this.fetchConversations();
  }

  // 获取聊天记录
  fetchConversations = async (isLoadMore = false) => {
    const { pageIndex, pageSize, conversations } = this.state;

    this.setState({ conversationLoading: true });

    try {
      // 记录加载前的滚动高度
      let preScrollHeight = 0;
      if (isLoadMore) {
        const container = document.querySelector('.messages-container');
        if (container) {
          preScrollHeight = container.scrollHeight;
        }
      }

      const data = await Market.fetchAiConversations({
        'pagination.pageIndex': pageIndex,
        'pagination.pageSize': pageSize,
      });

      const newConversations = data.items.map(item => ({
        ...item,
        userInput: JSON.parse(item.userInput || '{}'),
        aiReply: JSON.parse(item.aiReply || '{}'),
      })).reverse(); // 倒序排列

      this.setState({
        conversations: isLoadMore ? [...newConversations, ...conversations] : newConversations,
        hasMore: pageIndex * pageSize < data.total,
        pageIndex: pageIndex + 1,
      }, () => {
        if (isLoadMore) {
          // 加载更多时，保持用户当前的阅读位置
          setTimeout(() => {
            const container = document.querySelector('.messages-container');
            if (container) {
              container.scrollTop = container.scrollHeight - preScrollHeight;
            }
          }, 100);
        } else {
          // 首次加载时，滚动到底部
          this.scrollToBottom();
        }
      });
    } catch (error) {
      console.log(error);
      message.error('获取聊天记录失败');
    } finally {
      this.setState({ conversationLoading: false });
    }
  }

  // 滚动到底部
  scrollToBottom = () => {
    setTimeout(() => {
      const container = document.querySelector('.messages-container');
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }, 100);
  }

  // 处理滚动事件
  handleScroll = (e) => {
    const { scrollTop } = e.target;
    const { hasMore, conversationLoading } = this.state;

    if (scrollTop === 0 && hasMore && !conversationLoading) {
      this.fetchConversations(true);
    }
  }

  onPromptChange = (e) => {
    this.setState({ prompt: e.target.value });
  }

  onAddRefImages = async (files) => {
    const { refImages } = this.state;
    const uploadPromises = files.map(file =>
      AliyunHelper.clipsUploadImage(file)
    );

    try {
      const urls = await Promise.all(uploadPromises);
      this.setState({ refImages: [...refImages, ...urls] });
      message.success(`成功上传 ${urls.length} 张图片`);
    } catch (error) {
      message.error('图片上传失败，请重试');
    }
  }

  onUploadChange = (info) => {
    const { uploadFileList } = this.state;
    const newFiles = info.fileList.filter(file =>
      !uploadFileList.find(existingFile => existingFile.uid === file.uid)
    );

    if (newFiles.length > 0) {
      const files = newFiles.map(file => file.originFileObj || file);
      this.onAddRefImages(files);
    }

    this.setState({ uploadFileList: info.fileList });
  }

  onRemoveRefImage = (index) => {
    const { refImages } = this.state;
    const newImages = refImages.filter((_, i) => i !== index);
    this.setState({ refImages: newImages });
  }

  onGenerate = async () => {
    const { prompt, refImages, size, quality, transparent } = this.state;

    if (!prompt.trim()) {
      message.error('请输入提示词');
      return;
    }

    this.setState({ loading: true });

    // 添加用户消息，包含参数信息
    const userMessage = {
      type: 'user',
      content: prompt,
      refImages: [...refImages],
      timestamp: moment().format('YYYY-MM-DD HH:mm:ss'),
      // 添加参数信息
      params: {
        size,
        quality,
        transparent,
        model: 'gpt-image-1'
      }
    };

    this.setState({
      messages: [...this.state.messages, userMessage],
      prompt: '',
      refImages: [],
    });
    this.scrollToBottom();

    try {
      const result = await Market.generateGptImage({
        prompt,
        size,
        quality,
        transparent,
        refImageUrls: refImages.length > 0 ? refImages : undefined,
      });

      const aiMessage = {
        type: 'ai',
        content: result.message,
        imageUrl: result.imageUrl,
        timestamp: moment().format('YYYY-MM-DD HH:mm:ss'),
      };
      this.setState({
        messages: [...this.state.messages, aiMessage],
      });
    } catch (error) {
      const aiMessage = {
        type: 'ai',
        content: error?.response?.data?.detail,
        imageUrl: '',
        timestamp: moment().format('YYYY-MM-DD HH:mm:ss'),
      };
      this.setState({
        messages: [...this.state.messages, aiMessage],
      });
      message.error('生成失败，请重试');
    } finally {
      this.setState({ loading: false });
    }
  }

  // 参数值映射
  getParamDisplayValue = (key, value) => {
    const mappings = {
      size: {
        'auto': '自动',
        '256x256': '256x256',
        '512x512': '512x512',
        '1024x1024': '1024x1024',
        '1536x1024': '1536x1024',
        '1024x1536': '1024x1536'
      },
      quality: {
        'low': '低',
        'medium': '中',
        'high': '高',
        'standard': '标准',
        'auto': '自动'
      },
      background: {
        null: '不透明',
        'transparent': '透明'
      }
    };

    return mappings[key]?.[value] || value;
  }

  // 应用历史参数
  onApplyParams = (params, refImages = [], prompt = '') => {
    console.log(params, !!(params.transparent || params.background === 'transparent'));
    this.setState({
      size: params.size || 'auto',
      quality: params.quality || 'low',
      transparent: !!(params.transparent || params.background === 'transparent'),
      refImages: refImages || [],
      prompt: prompt || '',
    });
    message.success('参数已应用到输入区域');
  }

  renderMessage = (msg, index) => {
    if (msg.type === 'user') {
      return (
        <div key={index} className="message user-message">
          <div className="message-content">
            <Text>{msg.content}</Text>
            {msg.refImages && msg.refImages.length > 0 && (
              <div className="ref-images">
                <Image.PreviewGroup>
                  {msg.refImages.map((url, i) => (
                    <Image
                      key={i}
                      src={url}
                      alt="参考图"
                      className="ref-image"
                      preview={{
                        mask: '点击放大'
                      }}
                    />
                  ))}
                </Image.PreviewGroup>
              </div>
            )}
            {/* 显示其他字段 */}
            <div className="user-input-details">
              <div className="detail-item">尺寸: {this.getParamDisplayValue('size', msg.params.size)}</div>
              <div className="detail-item">质量: {this.getParamDisplayValue('quality', msg.params.quality)}</div>
              <div className="detail-item">背景: {msg.params.transparent ? '透明' : '不透明'}</div>
            </div>
            <Button
              type="link"
              size="small"
              onClick={() => this.onApplyParams(msg.params, msg.refImages, msg.content)}
              className="apply-btn"
            >
              应用
            </Button>
          </div>
          <div className="message-time">{msg.timestamp}</div>
        </div>
      );
    } else {
      return (
        <div key={index} className="message ai-message">
          <div className="message-content">
            {msg.imageUrl ? (
              <div className="generated-image">
                <Image
                  src={msg.imageUrl}
                  alt="生成的图片"
                  preview={{
                    mask: '点击放大'
                  }}
                />
              </div>
            ) : (
              <Text>{msg.content}</Text>
            )}
          </div>
          <div className="message-time">{msg.timestamp}</div>
        </div>
      );
    }
  }

  // 渲染历史对话
  renderConversation = (conversation, index) => {
    const { userInput, aiReply, createdAt, updatedAt } = conversation;

    return (
      <div key={conversation.id} className="conversation-group">
        {/* 用户消息 */}
        <div className="message user-message">
          <div className="message-content">
            <Text>{userInput.prompt}</Text>
            {userInput.ref_image_urls && userInput.ref_image_urls.length > 0 && (
              <div className="ref-images">
                <Image.PreviewGroup>
                  {userInput.ref_image_urls.map((url, i) => (
                    <Image
                      key={i}
                      src={url}
                      alt="参考图"
                      className="ref-image"
                      preview={{
                        mask: '点击放大'
                      }}
                    />
                  ))}
                </Image.PreviewGroup>
              </div>
            )}
            {/* 显示其他字段 */}
            <div className="user-input-details">
              <div className="detail-item">尺寸: {this.getParamDisplayValue('size', userInput.size)}</div>
              <div className="detail-item">质量: {this.getParamDisplayValue('quality', userInput.quality)}</div>
              <div className="detail-item">背景: {this.getParamDisplayValue('background', userInput.background)}</div>
            </div>
            <Button
              type="link"
              size="small"
              onClick={() => this.onApplyParams(userInput, userInput.ref_image_urls, userInput.prompt)}
              className="apply-btn"
            >
              应用
            </Button>
          </div>
          <div className="message-time">{moment(createdAt).format('YYYY-MM-DD HH:mm:ss')}</div>
        </div>

        {/* AI回复 */}
        {
          (aiReply.image_url || aiReply.message || aiReply.error) && (
            <div className="message ai-message">
              <div className="message-content">
                {aiReply.image_url ? (
                  <div className="generated-image">
                    <Image
                      src={aiReply.image_url}
                      alt="生成的图片"
                      preview={{ mask: '点击放大' }}
                    />
                  </div>
                ) : (
                  <Text>{aiReply.message || aiReply.error}</Text>
                )}
              </div>
              <div className="message-time">{moment(updatedAt).format('YYYY-MM-DD HH:mm:ss')}</div>
            </div>
          )
        }
      </div>
    );
  }

  render() {
    const { prompt, refImages, size, quality, transparent, loading, messages, conversations, conversationLoading } = this.state;

    return (
      <div className="gpt-image-container">
        <Title level={3}>GPT图像生成</Title>

        <div className="chat-area">
          <div
            className="messages-container"
            onScroll={this.handleScroll}
          >
            {conversationLoading && (
              <div className="loading-more">
                <Spin size="small" /> 加载更多...
              </div>
            )}

            {/* 历史对话 */}
            {conversations.map((conversation, index) => this.renderConversation(conversation, index))}

            {/* 当前会话消息 */}
            {messages.map((msg, index) => this.renderMessage(msg, index))}

            {loading && (
              <div className="message ai-message text-loading">
                <Spin /> 正在生成图片...
              </div>
            )}
          </div>
        </div>

        <Card className="input-area">
          <Row gutter={16}>
            <Col span={6}>
              <div className="setting-item">
                <Text>尺寸:</Text>
                <Select
                  value={size}
                  onChange={(value) => this.setState({ size: value })}
                  style={{ width: '100%' }}
                >
                  <Select.Option value="auto">自动</Select.Option>
                  <Select.Option value="1024x1024">1024x1024</Select.Option>
                  <Select.Option value="1536x1024">1536x1024</Select.Option>
                  <Select.Option value="1024x1536">1024x1536</Select.Option>
                </Select>
              </div>
            </Col>
            <Col span={6}>
              <div className="setting-item">
                <Text>质量:</Text>
                <Select
                  value={quality}
                  onChange={(value) => this.setState({ quality: value })}
                  style={{ width: '100%' }}
                >
                  <Select.Option value="low">低</Select.Option>
                  <Select.Option value="medium">中</Select.Option>
                  <Select.Option value="high">高</Select.Option>
                  <Select.Option value="auto">自动</Select.Option>
                </Select>
              </div>
            </Col>
            <Col span={6}>
              <div className="setting-item">
                <Text>透明背景:</Text>
                <Select
                  value={transparent}
                  onChange={(value) => this.setState({ transparent: value })}
                  style={{ width: '100%' }}
                >
                  <Select.Option value={true}>是</Select.Option>
                  <Select.Option value={false}>否</Select.Option>
                </Select>
              </div>
            </Col>
          </Row>

          <div className="ref-images-section">
            <Text>参考图片:</Text>
            <div className="ref-images-list">
              {refImages.map((url, index) => (
                <div key={index} className="ref-image-item">
                  <Image
                    src={url}
                    alt="参考图"
                    className="ref-image-thumb"
                    preview={{
                      mask: '点击放大'
                    }}
                  />
                  <Button
                    type="text"
                    icon={<DeleteOutlined />}
                    onClick={() => this.onRemoveRefImage(index)}
                    className="delete-btn"
                  />
                </div>
              ))}
              <Upload
                accept="image/*"
                multiple
                showUploadList={false}
                beforeUpload={() => false}
                onChange={this.onUploadChange}
                className="upload-btn"
              >
                <div className="upload-placeholder">
                  <PlusOutlined />
                  <div>添加参考图</div>
                  <div style={{ fontSize: '10px', color: '#999' }}>支持多选</div>
                </div>
              </Upload>
            </div>
          </div>

          <div className="prompt-input-section">
            <TextArea
              value={prompt}
              onChange={this.onPromptChange}
              placeholder="请输入图片描述..."
              rows={3}
              onPressEnter={(e) => {
                if (e.ctrlKey || e.metaKey) {
                  this.onGenerate();
                }
              }}
            />
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={this.onGenerate}
              loading={loading}
              disabled={!prompt.trim()}
              className="send-btn"
            >
              生成图片
            </Button>
          </div>
        </Card>
      </div>
    );
  }
}

export {
  reducer,
};
