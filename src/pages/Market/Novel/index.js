import './index.less';

import { DeleteFilled } from '@ant-design/icons';
import { Sessions } from '~/engine';
import { Platform } from '~/plugins';
import { <PERSON><PERSON>, Col, Collapse, Divider, Drawer, Form, Input, Popconfirm, Row, Tabs, Typography } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import KeywordDrawer from './components/KeywordDrawer';
import PromptDrawer from './components/PromptDrawer';
import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.marketNovel;
  },
  actions,
)
export default class MarketNovel extends Component {
  static propTypes = {
    promptData: PropTypes.object.isRequired,
    keyword: PropTypes.object.isRequired,
    detail: PropTypes.object.isRequired,
    match: PropTypes.object.isRequired,
    fetchPrompt: PropTypes.func.isRequired,
    savePrompt: PropTypes.func.isRequired,
    fetchKeywords: PropTypes.func.isRequired,
    saveKeywords: PropTypes.func.isRequired,
    fetchDetail: PropTypes.func.isRequired,
    saveChapter: PropTypes.func.isRequired,
    snippetAction: PropTypes.func.isRequired,
    setState: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    storyName: '',
    fileName: '',
    hasOriginalText: false,
    openOriginalText: false,
    pId: Sessions.getPartner()?.partnerId,
  }

  componentDidMount = async () => {
    const { name } = this.props.match?.params;
    const [storyName, fileName] = name.split('_');
    await this.props.fetchDetail({ pId: this.state.pId, name: storyName, file: fileName });
    await this.props.fetchPrompt({ pId: this.state.pId, name: storyName });
    await this.props.fetchKeywords({ pId: this.state.pId, name: storyName });
    const { content } = this.props.detail;
    this.setState({ storyName, fileName, hasOriginalText: !_.isEmpty(content) });
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.props.setState({ detail: { ...this.props.detail, [key]: value } });
  }

  onSaveChapter = async () => {
    const { detail } = this.props;
    const { pId, storyName, fileName } = this.state;
    await this.props.saveChapter({ pId, type: 'save', name: storyName, file: fileName, ...detail });
  }

  onSetupChapter = async () => {
    const { pId, storyName, fileName } = this.state;
    await this.props.saveChapter({ pId, type: 'setup', name: storyName, file: fileName });
  }

  onSaveKeyword = async (data) => {
    const { pId, storyName } = this.state;
    await this.props.saveKeywords({ pId, name: storyName, ...data });
    this.setState({ openKeywordDrawer: false });
  }

  onChangeSnippetValue = (e, key, idx) => {
    const value = e?.target ? e.target.value : e;
    const snippets = _.cloneDeep(this.props.detail.snippets);
    snippets[idx][key] = value;
    this.props.setState({ detail: { ...this.props.detail, snippets } });
  }

  onChangeSettingValue = (e, key, idx, topKey, topIdx) => {
    const value = e?.target ? e.target.value : e;
    const snippets = _.cloneDeep(this.props.detail.snippets);
    snippets[topIdx][topKey][idx][key] = value;
    this.props.setState({ detail: { ...this.props.detail, snippets } });
  }

  onDelSettingItem = (idx, topKey, topIdx) => {
    const snippets = _.cloneDeep(this.props.detail.snippets);
    snippets[topIdx][topKey].splice(idx, 1);
    this.props.setState({ detail: { ...this.props.detail, snippets } });
  }

  onSavePrompt = async (data) => {
    const { pId, storyName } = this.state;
    await this.props.savePrompt({ pId, name: storyName, ...data });
    this.setState({ openPromptDrawer: false });
  }

  onSaveSnippet = async () => {
    await this.onSaveChapter();
    const { storyName, fileName, pId } = this.state;
    await this.props.fetchDetail({ pId, name: storyName, file: fileName });
  }

  onActionSnippet = async (content, action, idx) => {
    const { pId, storyName, fileName } = this.state;
    const data = await this.props.snippetAction({
      idx,
      pId,
      content,
      fileName,
      name: storyName,
      action: _.snakeCase(action),
      env: Platform.isProd() ? '' : 'staging-',
    });
    const snippets = _.cloneDeep(this.props.detail.snippets);
    snippets[idx][action] = data[action];
    this.props.setState({ detail: { ...this.props.detail, snippets } });
  }

  renderOriginalTextDrawer = () => {
    const { detail } = this.props;
    const { openOriginalText } = this.state;

    return (
      <Drawer
        open={openOriginalText}
        width="50vw"
        title="原文"
        onClose={() => { return this.setState({ openOriginalText: false }); }}
      >
        <div
          style={{
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <Input.TextArea
            value={detail.content}
            style={{ height: 'calc(100% - 50px)' }}
            onChange={(e) => { return this.onChangeValue(e, 'content'); }}
          />
          <Button type="primary" onClick={() => { return this.onSaveChapter(); }}>保存</Button>
        </div>
      </Drawer>
    );
  }

  renderExtractButton = (content, idx, key, btnTxt = '提取') => {
    return (
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <Button
          type="link"
          size="small"
          style={{ top: -10 }}
          onClick={() => { return this.onActionSnippet(content, key, idx); }}
        >
          {_.isEmpty(content) ? btnTxt : `重新${btnTxt}`}
        </Button>
        {/* new btn for add name of 新增 show btn with key includes Settings */}
        {
          key.includes('Settings') && (
            <Button
              type="link"
              size="small"
              style={{ top: -10 }}
              onClick={() => {
                const snippets = _.cloneDeep(this.props.detail.snippets);
                snippets[idx][key].push({ category: '', word: '', newWord: '' });
                this.props.setState({ detail: { ...this.props.detail, snippets } });
              }}
            >
              新增
            </Button>
          )}
      </div>
    );
  }

  renderSnippetItem = (data, idx) => {
    return (
      <Collapse.Panel
        header={`第${idx + 1}段`}
        key={idx}
        extra={<Button type="link" size="small" onClick={() => { return this.onSaveSnippet(); }}>保存</Button>}
      >
        <Tabs>
          <Tabs.TabPane tab="原文" key="content">
            <Input.TextArea
              autoSize
              value={data?.content}
              onChange={(e) => { return this.onChangeSnippetValue(e, 'content', idx); }}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="提取指南" key="extractGuide">
            {this.renderExtractButton(data?.extractGuide, idx, 'extractGuide')}
            <Input.TextArea
              autoSize
              value={data?.extractGuide}
              onChange={(e) => { return this.onChangeSnippetValue(e, 'extractGuide', idx); }}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="提取设定" key="extractedSettings">
            {this.renderExtractButton(data?.extractedSettings, idx, 'extractedSettings')}
            <Row gutter={[48, 8]}>
              {
                (data?.extractedSettings || []).map((x, sIdx) => {
                  return (
                    <Col span={12}>
                      <Input.Group compact style={{ display: 'flex' }}>
                        <Input
                          addonBefore="分类"
                          value={x.category}
                          onChange={(e) => {
                            return this.onChangeSettingValue(e, 'category', sIdx, 'extractedSettings', idx);
                          }}
                        />
                        <Input
                          addonBefore="名称"
                          value={x.word}
                          onChange={(e) => {
                            return this.onChangeSettingValue(e, 'word', sIdx, 'extractedSettings', idx);
                          }}
                        />
                        <Popconfirm
                          title="确认删除?"
                          onConfirm={() => { return this.onDelSettingItem(sIdx, 'extractedSettings', idx); }}
                        >
                          <Button icon={<DeleteFilled />} />
                        </Popconfirm>
                      </Input.Group>
                    </Col>
                  );
                })
              }

            </Row>
          </Tabs.TabPane>
          <Tabs.TabPane tab="更新设定" key="updatedSettings">
            {this.renderExtractButton(data?.updatedSettings, idx, 'updatedSettings')}
            <Row gutter={[48, 8]}>
              {
                (data?.updatedSettings || []).map((x, sIdx) => {
                  return (
                    <Col span={12}>
                      <Input.Group compact style={{ display: 'flex' }}>
                        <Input
                          addonBefore="分类"
                          value={x.category}
                          onChange={(e) => {
                            return this.onChangeSettingValue(e, 'category', sIdx, 'updatedSettings', idx);
                          }}
                        />
                        <Input
                          addonBefore="名称"
                          value={x.word}
                          onChange={(e) => {
                            return this.onChangeSettingValue(e, 'word', sIdx, 'updatedSettings', idx);
                          }}
                        />
                        <Input
                          addonBefore="新词"
                          value={x.newWord}
                          onChange={(e) => {
                            return this.onChangeSettingValue(e, 'newWord', sIdx, 'updatedSettings', idx);
                          }}
                        />
                        <Popconfirm
                          title="确认删除?"
                          onConfirm={() => { return this.onDelSettingItem(sIdx, 'updatedSettings', idx); }}
                        >
                          <Button icon={<DeleteFilled />} />
                        </Popconfirm>
                      </Input.Group>
                    </Col>
                  );
                })
              }
            </Row>
          </Tabs.TabPane>
          <Tabs.TabPane tab="最终内容" key="finalContent">
            {this.renderExtractButton(data?.finalContent, idx, 'finalContent')}
            <Input.TextArea
              autoSize
              value={data?.finalContent}
              onChange={(e) => { return this.onChangeSnippetValue(e, 'finalContent', idx); }}
            />
          </Tabs.TabPane>
        </Tabs>
      </Collapse.Panel >
    );
  }

  render = () => {
    const { storyName, fileName, hasOriginalText, openOriginalText } = this.state;
    const { snippets, words, tokens } = this.props?.detail || {};
    return (
      <div className="market-novel-container">
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography.Title level={3}>{storyName} / {fileName}</Typography.Title>
          <div>
            <Button
              style={{ marginRight: 30 }}
              onClick={() => { return this.setState({ openKeywordDrawer: true }); }}
            >
              关键词
            </Button>
            <Button onClick={() => { return this.setState({ openPromptDrawer: true }); }}>
              提示词
            </Button>
          </div>
        </div>
        <Divider style={{ margin: '10px 0' }} />
        <Form>
          <Form.Item label="原文">
            <div>
              <Button size="small" type="primary" onClick={() => { return this.setState({ openOriginalText: true }); }}>
                {hasOriginalText ? '查看原文' : '新增'}
              </Button>

              <Button
                size="small"
                type="primary"
                style={{ marginLeft: 30 }}
                onClick={() => { return this.onSetupChapter(); }}
              >
                {_.isEmpty(snippets) ? '处理章节' : '重新处理章节'}
              </Button>
            </div>
          </Form.Item>

          {
            !_.isEmpty(snippets) &&
            <>
              <div style={{ marginBottom: 10 }}>
                总字数: {words},&nbsp;
                总token数: {tokens},&nbsp;
                分解为 {snippets?.length} 段文字
              </div>
              <Collapse>
                {(snippets || []).map((x, idx) => { return this.renderSnippetItem(x, idx); })}
              </Collapse>
            </>
          }
        </Form>

        {openOriginalText && this.renderOriginalTextDrawer()}
        {this.state.openKeywordDrawer &&
          <KeywordDrawer
            open={this.state.openKeywordDrawer}
            data={this.props.keyword}
            onSave={this.onSaveKeyword}
            onClose={() => { return this.setState({ openKeywordDrawer: false }); }}
          />
        }
        {
          this.state.openPromptDrawer &&
          <PromptDrawer
            open={this.state.openPromptDrawer}
            data={this.props.promptData}
            onSave={this.onSavePrompt}
            onClose={() => { return this.setState({ openPromptDrawer: false }); }}
          />
        }
      </div>
    );
  }
}

export {
  reducer,
};
