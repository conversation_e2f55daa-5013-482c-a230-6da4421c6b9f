import { Button, Drawer, Form, Input } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

const PROMPT_ENUMS = {
  requirement: '需求',
  requirementUpdateSetting: '需求更新设置',
  requirementRegenSetting: '需求重置设置',
  systemAuthor: '系统作者',
  analyzeSettingCot: 'COT分析设置',
  analyzeSetting: '分析设置',
  generateNewSettings: '生成新设置',
  sameWordSetting: '同义词设置',
  rewriteContentSetting: '重写内容设置',
};

export default class PromptDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    data: PropTypes.object,
    onSave: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    data: {
      requirement: '',
      requirementUpdateSetting: '',
      requirementRegenSetting: '',
      systemAuthor: '',
      analyzeSettingCot: '',
      analyzeSetting: '',
      generateNewSettings: '',
      sameWordSetting: '',
      rewriteContentSetting: '',
    },
  }

  componentDidMount = () => {
    this.setState({ data: this.props.data });
  }

  onChangValue = (e, type) => {
    this.setState({ data: { ...this.state.data, [type]: e.target.value } });
  }

  onSave = () => {
    const { data } = this.state;
    this.props.onSave(data);
  }

  onUseTemplate = async () => { // eslint-disable-next-line
    const resp = await fetch('https://video-clip.oss-cn-shanghai.aliyuncs.com/faas/frontend/novels/prompt_template.json');
    const result = await resp.json();
    const data = {};
    _.forEach(result, (v, k) => { data[_.camelCase(k)] = v; });
    this.setState({ data });
  }

  render = () => {
    return (
      <Drawer
        title="提示词"
        placement="right"
        width="50vw"
        open={this.props.open}
        extra={<Button type="primary" onClick={() => { return this.onSave(); }}>保存</Button>}
        onClose={() => { return this.props.onClose(); }}
      >
        <Button type="link" size="small" onClick={() => { return this.onUseTemplate(); }}>使用预设模版</Button>
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          {
            _.map(PROMPT_ENUMS, (v, k) => {
              return (
                <Form.Item label={v} key={k}>
                  <Input.TextArea
                    autoSize
                    value={this.state.data[k]}
                    onChange={(e) => { return this.onChangValue(e, k); }}
                  />
                </Form.Item>
              );
            })
          }
        </Form>
      </Drawer>
    );
  };
}
