import { DeleteOutlined, PlusCircleFilled } from '@ant-design/icons';
import { <PERSON><PERSON>, Drawer, Form, Input } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class KeywordDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    data: PropTypes.object,
    onSave: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
  }

  componentDidMount = () => {
    const { includeWords, excludeWords } = this.props.data;
    const includes = _.isEmpty(includeWords) ? [''] : includeWords;
    const excludes = _.isEmpty(excludeWords) ? [''] : excludeWords;
    this.setState({ includes, excludes });
  }

  onChangValue = (e, type, index) => {
    const data = _.cloneDeep(this.state[type]);
    data[index] = e.target.value;
    this.setState({ [type]: data });
  }

  onAddKeyword = (type, index) => {
    const data = _.cloneDeep(this.state[type]);
    data.splice(index + 1, 0, '');
    this.setState({ [type]: data });
  }

  onDelKeyword = (type, index) => {
    const data = _.cloneDeep(this.state[type]);
    data.splice(index, 1);
    this.setState({ [type]: data });
  }

  onSave = () => {
    const { includes, excludes } = this.state;
    const data = {
      includeWords: _.isEmpty(includes) ? [] : includes,
      excludeWords: _.isEmpty(excludes) ? [] : excludes,
    };
    this.props.onSave(data);
  }

  render = () => {
    const { includes, excludes } = this.state;
    return (
      <Drawer
        title="人工干预关键词"
        placement="right"
        width="50vw"
        open={this.props.open}
        extra={<Button type="primary" onClick={() => { return this.onSave(); }}>保存</Button>}
        onClose={() => { return this.props.onClose(); }}
      >
        <Form labelCol={{ span: 2 }}>
          <Form.Item label="包含">
            {
              includes?.map((x, idx) => {
                return (
                  <Input.Group compact style={{ display: 'flex', marginBottom: 5 }}>
                    <Input
                      value={x}
                      style={{ width: '100%' }}
                      onChange={(e) => { return this.onChangValue(e, 'includes', idx); }}
                    />
                    <div style={{ display: 'flex' }}>
                      <Button
                        icon={<PlusCircleFilled />}
                        onClick={() => { return this.onAddKeyword('includes', idx); }}
                      />
                      <Button
                        icon={<DeleteOutlined />}
                        onClick={() => { return this.onDelKeyword('includes', idx); }}
                      />
                    </div>
                  </Input.Group>
                );
              })
            }
          </Form.Item>
          <Form.Item label="排除">
            {
              excludes?.map((x, idx) => {
                return (
                  <Input.Group compact style={{ display: 'flex', marginBottom: 5 }}>
                    <Input
                      value={x}
                      style={{ width: '100%' }}
                      onChange={(e) => { return this.onChangValue(e, 'excludes', idx); }}
                    />
                    <div style={{ display: 'flex' }}>
                      <Button
                        icon={<PlusCircleFilled />}
                        onClick={() => { return this.onAddKeyword('excludes', idx); }}
                      />
                      <Button
                        icon={<DeleteOutlined />}
                        onClick={() => { return this.onDelKeyword('excludes', idx); }}
                      />
                    </div>
                  </Input.Group>
                );
              })
            }
          </Form.Item>
        </Form>
      </Drawer>
    );
  }
}
