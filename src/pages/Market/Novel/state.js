import { Market } from '~/engine';

const SET_STATE = 'MARKET_NOVEL/SET_STATE';
const CLEAR_STATE = 'MARKET_NOVEL/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchDetail = (params) => {
  return async (dispatch) => {
    const detail = await Market.fetchNovelChapter(params);
    dispatch(setState({ detail }));
  };
};

export const saveChapter = (params) => {
  return async (dispatch) => {
    const detail = await Market.saveNovelChapter(params);
    dispatch(setState({ detail }));
  };
};

export const fetchKeywords = (params) => {
  return async (dispatch) => {
    const keyword = await Market.fetchNovelKeywords(params);
    dispatch(setState({ keyword }));
  };
};

export const saveKeywords = (params) => {
  return async (dispatch) => {
    await Market.saveNovelKeywords(params);
    const { includeWords, excludeWords } = params;
    dispatch(setState({ keyword: { includeWords, excludeWords } }));
  };
};

export const fetchPrompt = (params) => {
  return async (dispatch) => {
    const promptData = await Market.fetchNovelPrompts(params);
    dispatch(setState({ promptData }));
  };
};

export const savePrompt = (params) => {
  return async (dispatch, getState) => {
    const { promptData } = getState().marketNovel;
    await Market.saveNovelPrompts(params);
    dispatch(setState({ promptData: { ...promptData, ...params } }));
  };
};

export const snippetAction = (params) => {
  return async () => {
    const data = await Market.getNovelSnippet(params);
    return data;
  };
};

const _getInitState = () => {
  return {
    detail: {},
    keyword: {
      includeWords: [],
      excludeWords: [],
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
