import { Market } from '~/engine';

const SET_STATE = 'MARKET_MEME/SET_STATE';
const CLEAR_STATE = 'MARKET_MEME/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchNovels = (params) => {
  return async () => {
    const { items } = await Market.fetchNovels(params);
    return items;
  };
};

export const fetchKeywords = (params) => {
  return async () => {
    const keyword = await Market.fetchNovelKeywords(params);
    return keyword;
  };
};

export const saveKeywords = (params) => {
  return async () => {
    await Market.saveNovelKeywords(params);
  };
};

export const addNovel = (params) => {
  return async () => {
    const { items } = await Market.addNovel(params);
    return items;
  };
};

const _getInitState = () => {
  return {
    novels: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
