import { FilterBar, Toast } from '~/components';
import { Sessions } from '~/engine';
import { Divider, Input, Modal, Table } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import KeywordDrawer from '../components/KeywordDrawer';
import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.marketNovelList;
  },
  actions,
)
export default class MarketNovelList extends Component {
  static propTypes = {
    novels: PropTypes.array,
    addNovel: PropTypes.func.isRequired,
    fetchNovels: PropTypes.func.isRequired,
    fetchKeywords: PropTypes.func.isRequired,
    saveKeywords: PropTypes.func.isRequired,
    setState: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    keyword: {},
    pId: <PERSON>.getPartner()?.partnerId,
  }

  componentDidMount = async () => {
    await this.onFetchNovels();
  }


  componentWillUnmount = () => {
    this.props.clearState();
  }

  onFetchNovels = async () => {
    const novels = await this.props.fetchNovels({ pId: this.state.pId });
    this.props.setState({ novels });
  }

  onShowKeywordDrawer = async (name) => {
    const keyword = await this.props.fetchKeywords({ pId: this.state.pId, name });
    this.setState({ keywordOpen: true, keyword, storyName: name });
  }

  onSaveKeyword = async (data) => {
    await this.props.saveKeywords({ pId: this.state.pId, name: this.state.storyName, ...data });
    this.setState({ keywordOpen: false });
  }

  onShowChapterModal = async (name) => {
    let chapters = await this.props.fetchNovels({ pId: this.state.pId, name });
    chapters = chapters.filter((x) => {
      return !(x.name.indexOf('keyword') > -1) && !(x.name.indexOf('prompt') > -1);
    });
    this.setState({ chapterOpen: true, chapters: _.sortBy(chapters), storyName: name });
  }

  onAddNovel = async () => {
    const { name } = this.state;
    const storyName = _.trim(name);
    if (_.isEmpty(storyName)) {
      Toast.show('请输入小说名称!', Toast.Type.WARNING);
      return;
    }

    await this.props.addNovel({ pId: this.state.pId, storyName });
    await this.onFetchNovels();
    this.setState({ addOpen: false, name: '' });
  }

  onAddChapter = async (e) => {
    const { pId, storyName } = this.state;
    const chapterName = _.trim(e);
    if (_.isEmpty(chapterName)) {
      Toast.show('请输入章节名称!', Toast.Type.WARNING);
      return;
    }
    const chapters = await this.props.addNovel({ pId, storyName, chapterName });
    this.setState({ chapters: _.orderBy(chapters, 'createdAt') });
  }

  renderCreateModal = () => {
    const { addOpen, name } = this.state;
    return (
      <Modal
        open={addOpen}
        title="新增小说"
        onCancel={() => { return this.setState({ addOpen: false, name: '' }); }}
        onOk={this.onAddNovel}
      >
        <Input
          placeholder="请输小说名称"
          value={name}
          onChange={(e) => { return this.setState({ name: e.target.value }); }}
        />
      </Modal>
    );
  }

  renderChapterModal = () => {
    const { chapterOpen, chapters, storyName } = this.state;
    return (
      <Modal
        open={chapterOpen}
        title={`${storyName} - 章节列表`}
        footer={null}
        onCancel={() => { return this.setState({ chapterOpen: false, chapters: [] }); }}
      >
        <Input.Search
          enterButton="新增章节"
          onSearch={(e) => { return this.onAddChapter(e); }}
          style={{ marginTop: -10 }}
        />
        <Table dataSource={chapters} size="small" pagination={false} columns={this.renderColumns(true)} />
      </Modal>
    );
  }

  renderColumns = (isChapter = false) => {
    return [
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
        render: (x) => { return isChapter ? _.trimEnd(x, '.json') : x; },
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (x) => { return moment(x).format('YYYY-MM-DD HH:mm'); },
      },
      {
        title: '操作',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
        render: (x) => {
          return isChapter ?
            <a onClick={() => {
              return this.$push(`market-novel/${this.state.storyName}_${_.trimEnd(x, '.json')}`);
            }}
            >编辑
            </a> :
            (
              <>
                <a onClick={() => { return this.onShowChapterModal(x); }}>章节列表</a>
                <Divider type="vertical" />
                <a onClick={() => { return this.onShowKeywordDrawer(x); }}>关键词</a>
              </>
            );
        },
      },
    ];
  }

  render = () => {
    const { novels } = this.props;
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <FilterBar
          canAdd
          shouldShowSearchInput={false}
          onAdd={() => { return this.setState({ addOpen: true, name: '' }); }}
        />
        <Table
          dataSource={novels}
          pagination={false}
          columns={this.renderColumns()}
        />

        {this.state.addOpen && this.renderCreateModal()}
        {this.state.chapterOpen && this.renderChapterModal()}
        {this.state.keywordOpen &&
          <KeywordDrawer
            open={this.state.keywordOpen}
            data={this.state.keyword}
            onSave={this.onSaveKeyword}
            onClose={() => { return this.setState({ keywordOpen: false }); }}
          />
        }
      </div>
    );
  }
}

export {
  reducer,
};
