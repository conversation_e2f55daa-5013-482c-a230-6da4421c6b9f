import { <PERSON><PERSON>, Drawer, List } from 'antd';
import classNames from 'classnames';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class ConversationDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    staff: PropTypes.object,
    conversation: PropTypes.object,
    fetchStaffMessages: PropTypes.func,
    onClose: PropTypes.func,
  }
  state = {
    messages: [],
    messagesTotal: 0,
    messagesPagination: {
      pageIndex: 1,
      pageSize: 20,
    },
  }

  componentDidMount = async () => {
    const pagination = { pageIndex: 1, pageSize: 20 };
    const { items, total } = await this.props.fetchStaffMessages({
      openKfid: this.props.staff.openKfid,
      userId: this.props.conversation.userId,
      'pagination.pageIndex': pagination.pageIndex,
      'pagination.pageSize': pagination.pageSize,
    });
    setTimeout(() => {
      if (this.msgRef) {
        this.msgRef.scrollTop = this.msgRef.scrollHeight;
      }
    }, 0);

    this.setState({ messages: items.reverse(), messagesTotal: total, messagesPagination: pagination });
  }

  render = () => {
    const { staff, conversation } = this.props;
    const { messages, messagesTotal, messagesPagination } = this.state;
    return (
      <Drawer
        className="wework-kf-messages"
        title="会话详情"
        placement="right"
        width="30vw"
        onClose={this.props.onClose}
        open={this.props.open}
      >
        <div
          style={{
            height: '100%',
            overflowY: 'auto',
            margin: '0 -24px',
            padding: '0 24px',
          }}
          ref={(el) => { this.msgRef = el; }}
        >
          <List>
            {
              (messages || []).length < messagesTotal && (
                <div style={{ textAlign: 'center' }}>
                  <Button
                    size="mini"
                    onClick={async () => {
                      messagesPagination.pageIndex += 1;
                      const { items, total } = await this.props.fetchStaffMessages({
                        openKfid: staff.openKfid,
                        userId: conversation.userId,
                        'pagination.pageIndex': messagesPagination.pageIndex,
                        'pagination.pageSize': messagesPagination.pageSize,
                      });
                      const preScrollHeight = this.msgRef.scrollHeight;
                      setTimeout(() => {
                        if (this.msgRef) {
                          this.msgRef.scrollTop = this.msgRef.scrollHeight - preScrollHeight;
                        }
                      }, 0);
                      return this.setState({
                        messages: [...items.reverse(), ...messages],
                        messagesTotal: total,
                        messagesPagination,
                      });
                    }}
                  >
                    加载更多
                  </Button>
                </div>
              )
            }
            {
              (messages || []).map((message) => {
                const isFromKf = message?.sender === message?.openKfid;
                let content;
                switch (message?.content?.msgtype) {
                  case 'text':
                    content = (
                      <span className="content">{message?.content?.text?.content}</span>
                    );
                    break;
                  case 'file':
                    content = (
                      <span className="content">
                        <a href={message?.content?.file?.fileUrl} target="_blank" rel="noreferrer">【文件】</a>
                      </span>
                    );
                    break;
                  case 'image':
                    content = (
                      <span className="content">
                        <img src={message?.content?.image?.fileUrl} />
                      </span>
                    );
                    break;
                  case 'link':
                    content = (
                      <a className="link" href={message?.content?.link?.url} target="_blank" rel="noreferrer">
                        {message?.content?.link?.title}
                        <div className="link-content">
                          <span className="link-desc">{message?.content?.link?.desc}</span>
                          <img src={message?.content?.link?.picUrl} />
                        </div>
                      </a>
                    );
                    break;
                  case 'miniprogram':
                    content = (
                      <span className="content">{message?.content?.miniprogram?.title}【小程序】</span>
                    );
                    break;
                  case 'event':
                    content = (
                      <span className="content">【事件信息】</span>
                    );
                    break;
                  default:
                    content = (
                      <span className="content">【该条信息为素材，无法展示】</span>
                    );
                }
                return (
                  <List.Item className={classNames('msg-item', { kf: isFromKf })}>
                    <span className="nickname">{isFromKf ? staff.name : conversation.userNickname}</span>
                    {content}
                    <span className="date">{moment(message.createdAt).format('YYYY-MM-DD HH:mm:ss')}</span>
                  </List.Item>
                );
              })
            }
          </List>
        </div>
      </Drawer>
    );
  }
}
