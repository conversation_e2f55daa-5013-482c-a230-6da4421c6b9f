@import 'app.less';

.wework-kf-messages {
  .msg-item {
    flex-direction: column;
    align-items: flex-end;
    border-bottom: 0;

    .content {
      display: inline-block;
      max-width: 90%;
      padding: 7px 12px;
      font-size: 14px;
      line-height: 20px;
      background-color: #1afa25;
      border-radius: 4px;
      white-space: pre-wrap;
      word-break: break-all;

      img {
        width: 100%;
      }
    }

    &.kf {
      align-items: flex-start;

      .content {
        background-color: #efefef;
      }
    }

    .nickname {
      display: block;
      margin-bottom: 6px;
      font-size: 14px;
      line-height: 20px;
    }

    .date {
      display: block;
      font-size: 12px;
      color: #ccc;
    }

    .link {
      padding: 7px;
      font-size: 14px;
      line-height: 20px;
      color: #000;
      background-color: #f7f6f6;
      border-radius: 4px;

      .link-content {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-top: 5px;

        .link-desc {
          font-size: 12px;
          color: #999;
        }

        img {
          width: 50px;
          height: 50px;
          margin-left: 10px;
        }
      }
    }
  }
}
