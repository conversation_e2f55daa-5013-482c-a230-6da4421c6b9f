import './index.less';

import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { PaginationTable, Toast } from '~/components';
import { <PERSON><PERSON><PERSON>elper } from '~/engine';
import TTSSettings from '~/pages/Market/CourseMaterials/components/TTSSetting';
import { Avatar, Button, Divider, Drawer, Form, Input, List, Popconfirm, Radio, Select, Upload } from 'antd';
import classNames from 'classnames';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class CustomerDrawer extends PureComponent {
  static propTypes = {
    speakers: PropTypes.array,
    corpId: PropTypes.string,
    open: PropTypes.bool,
    delStaff: PropTypes.func,
    // eslint-disable-next-line react/no-unused-prop-types
    updateStaff: PropTypes.func,
    // eslint-disable-next-line react/no-unused-prop-types
    createStaff: PropTypes.func,
    fetchStaffs: PropTypes.func,
    createStaffChannel: PropTypes.func,
    delStaffChannel: PropTypes.func,
    updateStaffChannel: PropTypes.func,
    fetchStaffChannels: PropTypes.func,
    fetchStaffConversations: PropTypes.func,
    fetchStaffMessages: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    staff: {},
    conversation: {},
    staffs: [],
    channels: [],
    conversations: [],
    messages: [],
    messagesTotal: 0,
    messagesPagination: {},
    conversationsTotal: 0,
    conversationsPagination: {},
  }

  componentDidMount = async () => {
    const { items } = await this.props.fetchStaffs({ corpId: this.props.corpId });
    this.setState({ staffs: items });
  }

  convertTextToMenu = (txt) => {
    const result = { headContent: '', list: [] };
    const parts = txt.split('|');
    const headContent = parts[1].trim().startsWith('[') ? '' : parts[1].trim();
    const menuItems = headContent ? parts.slice(1) : parts;
    result.headContent = headContent;
    const regex = /\[(.*?)\]\((.*?)\)/;

    for (const item of menuItems) {
      const match = item.trim().match(regex);
      if (match) {
        const text = match[1];
        const idOrUrl = match[2];
        if (idOrUrl.startsWith('http')) {
          result.list.push({ type: 'view', view: { url: idOrUrl, content: text } });
        } else if (idOrUrl) {
          result.list.push({ type: 'click', click: { id: idOrUrl, content: text } });
        } else {
          result.list.push({ type: 'text', text: { content: text } });
        }
      }
    }

    result.list = result.list.slice(0, 10);
    return result;
  }
  onChangeStaffValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ staff: { ...this.state.staff, [key]: value } });
  }

  onUpload = (key) => {
    return async (option) => {
      try {
        const url = await AliyunHelper.clipsUploadImage(option.file, (progress) => {
          const percent = Math.round((progress.loaded / progress.total) * 100);
          option.onProgress({ percent });
        });
        option.onSuccess();
        await this.onChangeStaffValue(url, key);
      } catch (e) {
        option.onError();
      }
    };
  };

  onDelStaff = async (staff) => {
    await this.props.delStaff(staff.id);
    const { items } = await this.props.fetchStaffs({ corpId: this.props.corpId });
    this.setState({ staffs: items });
  }

  onSubmitStaff = async () => {
    if (_.isEmpty(this.state.staff.name)) { Toast.show('客服名称不能为空'); return; }
    if (_.isEmpty(this.state.staff.avatar)) { Toast.show('客服头像不能为空'); return; }

    const { maxTokensPreUser: token } = this.state.staff;
    await this.props[this.state.staff.id ? 'updateStaff' : 'createStaff']({
      ...this.state.staff,
      corpId: this.props.corpId,
      maxTokensPreUser: (!token || (token < 2000)) ? 2000 : token,
    });

    const { items } = await this.props.fetchStaffs({ corpId: this.props.corpId });
    this.setState({ staffs: items, open: false });
  }

  onOpenChannel = async (staff) => {
    const { items } = await this.props.fetchStaffChannels({ staffId: staff.id });
    this.setState({ openChannel: true, staff, channels: items });
  }

  onOpenConversation = async (staff) => {
    const pagination = {
      pageIndex: 1,
      pageSize: 20,
    };

    const { items, total } = await this.props.fetchStaffConversations({
      openKfid: staff.openKfid,
      'pagination.pageIndex': pagination.pageIndex,
      'pagination.pageSize': pagination.pageSize,
    });
    this.setState({
      openConversation: true,
      staff,
      conversations: items,
      conversationsTotal: total,
      conversationsPagination: pagination,
    });
  }

  onDelStaffChannel = async (channel) => {
    await this.props.delStaffChannel(channel.id);
    const { items } = await this.props.fetchStaffChannels({ staffId: this.state.staff.id });
    this.setState({ channels: items });
  }

  onSubmitChannel = async () => {
    const staffId = this.state.staff.id;
    const { welcomeType, menus, scene, relatedConfig } = this.state.channel;
    if (_.isEmpty(scene)) { Toast.show('场景不能为空'); return; }
    if (_.isEmpty(relatedConfig)) { Toast.show('请完善信息'); return; }
    if (welcomeType === 'menu') {
      if (_.isUndefined(menus.headContent) && _.isEmpty(menus.headContent)) {
        Toast.show('请完善菜单信息');
      }

      let welcomeMsg = `menu:|${menus.headContent}`;
      menus.list.forEach((item) => {
        if (item.type === 'text') {
          welcomeMsg += `|[${item.text.content}]()`;
        } else {
          welcomeMsg += `|[${item[item.type].content}](${item[item.type].id || item[item.type].url})`;
        }
      });

      relatedConfig.welcomeMessages = [welcomeMsg];
    }

    const params = { ...this.state.channel, relatedConfig, staffId };
    if (this.state.channel.id) {
      await this.props.updateStaffChannel(params);
    } else {
      await this.props.createStaffChannel(params);
    }

    const { items } = await this.props.fetchStaffChannels({ staffId });
    this.setState({ channels: items, openChannelDetail: false, channel: {} });
  }

  onChangeChannelValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ channel: { ...this.state.channel, [key]: value } });
  }

  onChangeRelatedConfig = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const relatedConfig = this.state.channel.relatedConfig || {};
    relatedConfig[key] = value;
    this.setState({ channel: { ...this.state.channel, relatedConfig } });
  }

  onChangeChannelMenu = (e, key, index) => {
    const value = e?.target ? e.target.value : e;
    const menus = this.state.channel.menus || [];
    if (_.isUndefined(index)) {
      menus[key] = value;
    } else {
      if (key === 'type') { // eslint-disable-line
        const idOrUrl = key === 'click' ? 'id' : 'url';
        menus.list[index] = { type: value, [value]: { content: '', [idOrUrl]: '' } };
      } else {
        menus.list[index][menus.list[index].type][key] = value;
      }
    }
    this.setState({ channel: { ...this.state.channel, menus } });
  }

  renderDetail = () => {
    const { open, staff } = this.state;
    return (
      <Drawer
        title="客服详情"
        placement="right"
        width="30vw"
        onClose={() => { return this.setState({ open: false }); }}
        open={open}
        extra={<Button type="primary" onClick={() => { return this.onSubmitStaff(); }}>保存</Button>}
      >
        <Form labelCol={{ span: 4 }}>
          <Form.Item label="名称">
            <Input value={staff?.name} onChange={(e) => { return this.onChangeStaffValue(e, 'name'); }} />
          </Form.Item>
          <Form.Item label="Token用量" help="最小值：2000">
            <Input
              type="number"
              min={2000}
              value={staff?.maxTokensPreUser || 2000}
              onChange={(e) => { return this.onChangeStaffValue(e, 'maxTokensPreUser'); }}
            />
          </Form.Item>
          <Form.Item label="每日Token用量">
            <Input
              min={0}
              type="number"
              value={_.isUndefined(staff?.maxTokensPreUserDay) ? 20000 : staff?.maxTokensPreUserDay}
              onChange={(e) => { return this.onChangeStaffValue(e, 'maxTokensPreUserDay'); }}
            />
          </Form.Item>
          <Form.Item label="被限制文案">
            <Input.TextArea
              value={staff?.tokenLimitTip}
              autoSize={{ minRows: 5 }}
              onChange={(e) => { return this.onChangeStaffValue(e, 'tokenLimitTip'); }}
            />
          </Form.Item>
          <Form.Item label="头像">
            <Upload
              accept="image/*"
              listType="picture-card"
              showUploadList={false}
              customRequest={this.onUpload('avatar')}
            >
              {
                staff?.avatar ?
                  <img style={{ maxWidth: 100 }} src={`${staff?.avatar}?x-oss-process=image/resize,h_100,m_lfit`} /> :
                  <div>
                    <PlusOutlined />
                    <div style={{ marginTop: 8 }}>上传</div>
                  </div>
              }
            </Upload>
          </Form.Item>
          <Form.Item label="背景">
            <Upload
              accept="image/*"
              listType="picture-card"
              showUploadList={false}
              customRequest={this.onUpload('banner')}
              style={{ alignItems: 'flex-start' }}
            >
              {
                staff?.banner ?
                  <img style={{ maxWidth: 100 }} src={staff?.banner} /> :
                  <div>
                    <PlusOutlined />
                    <div style={{ marginTop: 8 }}>上传</div>
                  </div>
              }
            </Upload>
          </Form.Item>
          <TTSSettings
            course={staff}
            speakers={this.props.speakers}
            onChange={(e, key) => { return this.onChangeStaffValue(e, key); }}
          />
        </Form>
      </Drawer>
    );
  }

  renderWelcomeMessage = (channel) => {
    return (
      <Form.Item label="欢迎语">
        <Radio.Group
          value={channel.welcomeType}
          style={{ marginBottom: 10 }}
          onChange={(e) => { return this.onChangeChannelValue(e, 'welcomeType'); }}
        >
          <Radio value="text">文本</Radio>
          <Radio value="menu">菜单</Radio>
        </Radio.Group>

        {
          channel.welcomeType === 'text' &&
          <Input.TextArea
            autoSize={{ minRows: 5 }}
            value={(channel?.relatedConfig?.welcomeMessages || [])[0]}
            onChange={(e) => { return this.onChangeRelatedConfig([e.target.value], 'welcomeMessages'); }}
          />
        }
        {
          channel.welcomeType === 'menu' &&
          <Form labelCol={{ span: 2 }} wrapperCol={{ span: 22 }} className="common-form">
            <Form.Item label="起始">
              <Input.TextArea
                value={channel.menus.headContent}
                onChange={(e) => { return this.onChangeChannelMenu(e, 'headContent'); }}
              />
            </Form.Item>
            {
              channel.menus.list.map((item, index) => {
                return (
                  <Form.Item label={`菜单${index + 1}`} className="common-form">
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Select
                        value={item.type}
                        style={{ width: 220 }}
                        onChange={(e) => { return this.onChangeChannelMenu(e, 'type', index); }}
                      >
                        <Select.Option value="text">文本</Select.Option>
                        <Select.Option value="click">回复菜单</Select.Option>
                        <Select.Option value="view">超链菜单</Select.Option>
                      </Select>
                      <span>
                        <PlusOutlined
                          onClick={() => {
                            const menus = _.cloneDeep(channel.menus);
                            if (menus.list.length >= 10) { return; }
                            menus.list.push({ type: 'text', text: { content: '' } });
                            this.setState({ channel: { ...channel, menus } });
                          }}
                        />
                        <Divider type="vertical" />
                        <DeleteOutlined
                          onClick={() => {
                            const menus = _.cloneDeep(channel.menus);
                            if (menus.list.length === 1) { return; }
                            menus.list.splice(index, 1);
                            this.setState({ channel: { ...channel, menus } });
                          }}
                        />
                      </span>
                    </div>
                    <Input
                      style={{ margin: '5px 0' }}
                      addonBefore={item.type !== 'text' ? '菜单' : '内容'}
                      value={item[item.type]?.content}
                      onChange={(e) => { return this.onChangeChannelMenu(e, 'content', index); }}
                    />
                    {
                      item.type !== 'text' &&
                      <Input
                        addonBefore="内容"
                        value={item.click?.id || item.view?.url}
                        onChange={(e) => {
                          return this.onChangeChannelMenu(e, item.type === 'click' ? 'id' : 'url', index);
                        }}
                      />
                    }
                  </Form.Item>
                );
              })
            }
          </Form>
        }
      </Form.Item>
    );
  }

  renderChannelDetail = () => {
    const { channel, openChannelDetail } = this.state;

    return (
      <Drawer
        title="渠道详情"
        placement="right"
        width="40vw"
        onClose={() => { return this.setState({ openChannelDetail: false, channel: {} }); }}
        open={openChannelDetail}
        extra={<Button type="primary" onClick={() => { return this.onSubmitChannel(); }}>保存</Button>}
      >
        <Form labelCol={{ span: 4 }} className="common-form">
          <Form.Item label="场景">
            <Input
              value={channel?.scene}
              onChange={(e) => { return this.onChangeChannelValue(e, 'scene'); }}
            />
          </Form.Item>
          <Form.Item label="工作流ID">
            <Input
              value={channel?.relatedConfig?.relatedFlowUuid}
              onChange={(e) => { return this.onChangeRelatedConfig(e, 'relatedFlowUuid'); }}
            />
          </Form.Item>
          <Form.Item label="SessionID">
            <Input
              value={channel?.relatedConfig?.relatedSessionId}
              onChange={(e) => { return this.onChangeRelatedConfig(e, 'relatedSessionId'); }}
            />
          </Form.Item>
          <Form.Item label="企微员工ID">
            <Input
              value={channel?.relatedConfig?.relatedEmployeeId}
              onChange={(e) => { return this.onChangeRelatedConfig(e, 'relatedEmployeeId'); }}
            />
          </Form.Item>
          {this.renderWelcomeMessage(channel)}
        </Form>
      </Drawer>
    );
  }

  renderConversationDetail = () => {
    const { openConversationDetail, staff, conversation, messages, messagesTotal, messagesPagination } = this.state;
    return (
      <Drawer
        className="wework-kf-messages"
        title="会话详情"
        placement="right"
        width="30vw"
        onClose={() => { return this.setState({ openConversationDetail: false }); }}
        open={openConversationDetail}
      >
        <div
          style={{
            height: '100%',
            overflowY: 'auto',
            margin: '0 -24px',
            padding: '0 24px',
          }}
          ref={(el) => { this.msgRef = el; }}
        >
          <List>
            {
              (messages || []).length < messagesTotal && (
                <div style={{ textAlign: 'center' }}>
                  <Button
                    size="mini"
                    onClick={async () => {
                      messagesPagination.pageIndex += 1;
                      const { items, total } = await this.props.fetchStaffMessages({
                        openKfid: staff.openKfid,
                        userId: conversation.userId,
                        'pagination.pageIndex': messagesPagination.pageIndex,
                        'pagination.pageSize': messagesPagination.pageSize,
                        // 'pagination.orderBy': pagination.orderBy,
                      });
                      const preScrollHeight = this.msgRef.scrollHeight;
                      setTimeout(() => {
                        if (this.msgRef) {
                          this.msgRef.scrollTop = this.msgRef.scrollHeight - preScrollHeight;
                        }
                      }, 0);
                      return this.setState({
                        messages: [...items.reverse(), ...messages],
                        messagesTotal: total,
                        messagesPagination,
                      });
                    }}
                  >
                    加载更多
                  </Button>
                </div>
              )
            }
            {
              (messages || []).map((message) => {
                const isFromKf = message?.sender === message?.openKfid;
                let content;
                switch (message?.content?.msgtype) {
                  case 'text':
                    content = (
                      <span className="content">{message?.content?.text?.content}</span>
                    );
                    break;
                  case 'file':
                    content = (
                      <span className="content">
                        <a href={message?.content?.file?.fileUrl} target="_blank" rel="noreferrer">【文件】</a>
                      </span>
                    );
                    break;
                  case 'image':
                    content = (
                      <span className="content">
                        <img src={message?.content?.image?.fileUrl} />
                      </span>
                    );
                    break;
                  case 'link':
                    content = (
                      <a className="link" href={message?.content?.link?.url} target="_blank" rel="noreferrer">
                        {message?.content?.link?.title}
                        <div className="link-content">
                          <span className="link-desc">{message?.content?.link?.desc}</span>
                          <img src={message?.content?.link?.picUrl} />
                        </div>
                      </a>
                    );
                    break;
                  case 'miniprogram':
                    content = (
                      <span className="content">{message?.content?.miniprogram?.title}【小程序】</span>
                    );
                    break;
                  case 'event':
                    content = (
                      <span className="content">【事件信息】</span>
                    );
                    break;
                  default:
                    content = (
                      <span className="content">【该条信息为素材，无法展示】</span>
                    );
                }
                return (
                  <List.Item className={classNames('msg-item', { kf: isFromKf })}>
                    <span className="nickname">{isFromKf ? staff.name : conversation.userNickname}</span>
                    {content}
                    <span className="date">{moment(message.createdAt).format('YYYY-MM-DD HH:mm:ss')}</span>
                  </List.Item>
                );
              })
            }
          </List>
        </div>
      </Drawer>
    );
  }

  renderChannelColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '场景', dataIndex: 'scene', key: 'scene', align: 'center' },
      { title: '关联ID', dataIndex: 'workflowId', key: 'workflowId', align: 'center' },
      { title: '企业微信员工ID', dataIndex: 'staffId', key: 'staffId', align: 'center' },
      {
        title: '渠道链接',
        dataIndex: 'channelUrl',
        key: 'channelUrl',
        align: 'center',
        render: (url) => {
          return (
            <a onClick={async () => {
              await navigator.clipboard.writeText(url);
              Toast.show('复制成功', Toast.Type.SUCCESS);
            }}
            >
              复制
            </a>
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        render: (t, row) => {
          return (
            <div>
              <a onClick={() => {
                const welcomeMessages = row?.relatedConfig?.welcomeMessages || [];
                let welcomeType = 'text';
                let menus = { headContent: '', list: [{ type: 'text', text: { content: '' } }] };
                const isMenu = (_.head(welcomeMessages) || '').startsWith('menu:|');
                if (isMenu) {
                  welcomeType = 'menu';
                  menus = this.convertTextToMenu(_.head(welcomeMessages));
                }
                const channel = { ...row, welcomeType, menus };
                return this.setState({ openChannelDetail: true, channel });
              }}
              >
                编辑
              </a>
              <Divider type="vertical" />
              <Popconfirm title="确定删除吗？" onConfirm={() => { return this.onDelStaffChannel(row); }}>
                <a>删除</a>
              </Popconfirm>
            </div>
          );
        },
      },
    ];
  }

  renderConversationColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '用户昵称', dataIndex: 'userNickname', key: 'userNickname', align: 'center' },
      {
        title: '会话开始时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (t) => {
          return moment(t).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '会话记录',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        render: (t, row) => {
          return (
            <div>
              <a
                onClick={async () => {
                  const { staff } = this.state;
                  const pagination = {
                    pageIndex: 1,
                    pageSize: 20,
                    // orderBy: 'createdAt asc',
                  };
                  const { items, total } = await this.props.fetchStaffMessages({
                    openKfid: staff.openKfid,
                    userId: row.userId,
                    'pagination.pageIndex': pagination.pageIndex,
                    'pagination.pageSize': pagination.pageSize,
                    // 'pagination.orderBy': pagination.orderBy,
                  });
                  setTimeout(() => {
                    if (this.msgRef) {
                      this.msgRef.scrollTop = this.msgRef.scrollHeight;
                    }
                  }, 0);
                  return this.setState({
                    openConversationDetail: true,
                    conversation: row,
                    messages: items.reverse(),
                    messagesTotal: total,
                    messagesPagination: pagination,
                  });
                }}
              >
                查看
              </a>
            </div>
          );
        },
      },
    ];
  }

  renderChannelDrawer = () => {
    return (
      <Drawer
        title="渠道列表"
        placement="right"
        width="50vw"
        onClose={() => { return this.setState({ openChannel: false }); }}
        open={this.state.openChannel}
        extra={
          <Button
            type="primary"
            onClick={() => { return this.setState({ openChannelDetail: true, channel: {} }); }}
          >
            新增
          </Button>
        }
      >
        <PaginationTable
          dataSource={this.state.channels}
          columns={this.renderChannelColumns()}
        />
      </Drawer>
    );
  }

  renderConversationDrawer = () => {
    const { openConversation, conversations, conversationsPagination, conversationsTotal, staff } = this.state;
    return (
      <Drawer
        title="会话列表"
        placement="right"
        width="50vw"
        onClose={() => { return this.setState({ openConversation: false }); }}
        open={openConversation}
      >
        <PaginationTable
          dataSource={conversations}
          columns={this.renderConversationColumns()}
          totalDataCount={conversationsTotal}
          pagination={conversationsPagination}
          onPaginationChange={async (e) => {
            const { items, total } = await this.props.fetchStaffConversations({
              openKfid: staff.openKfid,
              'pagination.pageIndex': e.pageIndex,
              'pagination.pageSize': e.pageSize,
            });
            this.setState({
              conversations: items,
              conversationsTotal: total,
              conversationsPagination: e,
            });
          }}
        />
      </Drawer>
    );
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '客服名称', dataIndex: 'name', key: 'name', align: 'center' },
      {
        title: '客服头像',
        dataIndex: 'avatar',
        key: 'avatar',
        align: 'center',
        render: (avatar) => {
          const url = _.isEmpty(avatar) ? '/static/ai-avatar.jpg' : avatar;
          return <Avatar src={url} />;
        },
      },
      {
        title: '渠道',
        dataIndex: 'channel',
        key: 'channel',
        align: 'center',
        render: (t, row) => {
          return (
            <Button type="link" onClick={() => { return this.onOpenChannel(row); }}>
              查看
            </Button>
          );
        },
      },
      {
        title: '会话',
        dataIndex: 'conversation',
        key: 'conversation',
        align: 'center',
        render: (t, row) => {
          return (
            <Button type="link" onClick={() => { return this.onOpenConversation(row); }}>
              查看
            </Button>
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        render: (t, row) => {
          return (
            <div>
              <a onClick={() => { return this.setState({ open: true, staff: row }); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm
                title="确定删除吗？"
                onConfirm={() => { return this.onDelStaff(row); }}
              >
                <a>删除</a>
              </Popconfirm>
            </div>
          );
        },
      },
    ];
  }

  render = () => {
    return (
      <Drawer
        title="客服列表"
        placement="right"
        width="60vw"
        closable={false}
        onClose={() => { return this.props.onClose(); }}
        open={this.props.open}
        extra={<Button type="primary" onClick={() => { return this.setState({ open: true }); }}>新增</Button>}
      >
        <PaginationTable
          needPagination={false}
          dataSource={this.state.staffs}
          columns={this.renderColumns()}
        />

        {this.state.open && this.renderDetail()}
        {this.state.openChannel && this.renderChannelDrawer()}
        {this.state.openConversation && this.renderConversationDrawer()}
        {this.state.openChannelDetail && this.renderChannelDetail()}
        {this.state.openConversationDetail && this.renderConversationDetail()}
      </Drawer>
    );
  }
}
