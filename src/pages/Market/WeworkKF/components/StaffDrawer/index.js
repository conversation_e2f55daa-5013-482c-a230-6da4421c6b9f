import { PlusOutlined } from '@ant-design/icons';
import { KFWelcomeMessage, Toast } from '~/components';
import { <PERSON>yunHelper } from '~/engine';
import TTSSettings from '~/pages/Market/CourseMaterials/components/TTSSetting';
import { Button, Drawer, Form, Input, Upload } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class StaffDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    staff: PropTypes.object,
    speakers: PropTypes.array,
    onClose: PropTypes.func,
    onSubmit: PropTypes.func,
  }

  state = {
    staff: {},
  }

  componentDidMount = () => {
    this.setState({ staff: this.props.staff });
  }

  onUpload = (key) => {
    return async (option) => {
      try {
        const url = await AliyunHelper.clipsUploadImage(option.file, (progress) => {
          const percent = Math.round((progress.loaded / progress.total) * 100);
          option.onProgress({ percent });
        });
        option.onSuccess();
        await this.onChangeValue(url, key);
      } catch (e) {
        option.onError();
      }
    };
  };

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ staff: { ...this.state.staff, [key]: value } });
  }

  onSubmitStaff = async () => {
    if (_.isEmpty(this.state.staff.name)) { Toast.show('客服名称不能为空'); return; }
    if (_.isEmpty(this.state.staff.avatar)) { Toast.show('客服头像不能为空'); return; }

    const { maxTokensPreUser: token } = this.state.staff;
    const params = { ...this.state.staff, maxTokensPreUser: (!token || (token < 2000)) ? 2000 : token };
    await this.props.onSubmit(params);
    Toast.show('操作成功!', Toast.Type.SUCCESS);
    this.props.onClose();
  }


  render = () => {
    const { staff } = this.state;
    return (
      <Drawer
        title="客服详情"
        placement="right"
        width="50vw"
        onClose={this.props.onClose}
        open={this.props.open}
        extra={<Button type="primary" onClick={() => { return this.onSubmitStaff(); }}>保存</Button>}
      >
        <Form labelCol={{ span: 4 }}>
          <Form.Item label="名称">
            <Input value={staff?.name} onChange={(e) => { return this.onChangeValue(e, 'name'); }} />
          </Form.Item>
          <Form.Item label="Token用量" help="最小值：2000">
            <Input
              type="number"
              min={2000}
              value={staff?.maxTokensPreUser || 2000}
              onChange={(e) => { return this.onChangeValue(e, 'maxTokensPreUser'); }}
            />
          </Form.Item>
          <Form.Item label="每日Token用量">
            <Input
              min={0}
              type="number"
              value={_.isUndefined(staff?.maxTokensPreUserDay) ? 20000 : staff?.maxTokensPreUserDay}
              onChange={(e) => { return this.onChangeValue(e, 'maxTokensPreUserDay'); }}
            />
          </Form.Item>
          <Form.Item label="被限制文案">
            <KFWelcomeMessage
              text={staff?.tokenLimitTip}
              onChange={(e) => { return this.onChangeValue(e, 'tokenLimitTip'); }}
            />
          </Form.Item>
          <Form.Item label="头像">
            <Upload
              accept="image/*"
              listType="picture-card"
              showUploadList={false}
              customRequest={this.onUpload('avatar')}
            >
              {
                staff?.avatar ?
                  <img style={{ maxWidth: 100 }} src={`${staff?.avatar}?x-oss-process=image/resize,h_100,m_lfit`} /> :
                  <div>
                    <PlusOutlined />
                    <div style={{ marginTop: 8 }}>上传</div>
                  </div>
              }
            </Upload>
          </Form.Item>
          <Form.Item label="背景">
            <Upload
              accept="image/*"
              listType="picture-card"
              showUploadList={false}
              customRequest={this.onUpload('banner')}
              style={{ alignItems: 'flex-start' }}
            >
              {
                staff?.banner ?
                  <img style={{ maxWidth: 100 }} src={staff?.banner} /> :
                  <div>
                    <PlusOutlined />
                    <div style={{ marginTop: 8 }}>上传</div>
                  </div>
              }
            </Upload>
          </Form.Item>
          <TTSSettings
            course={staff}
            speakers={this.props.speakers}
            onChange={(e, key) => { return this.onChangeValue(e, key); }}
          />
        </Form>
      </Drawer>
    );
  }
}
