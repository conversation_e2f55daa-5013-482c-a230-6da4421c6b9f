import { Toast } from '~/components';
import { Button, Drawer, Form, Input } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class SaveFuncDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    onClose: PropTypes.func,
    onSubmit: PropTypes.func,
  }

  state = {
    data: {
    },
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onSubmit = async () => {
    if (_.isEmpty(this.state.data.name)) { Toast.show('函数名不能为空'); return; }
    if (_.isEmpty(this.state.data.displayName)) { Toast.show('别名不能为空'); return; }
    if (_.isEmpty(this.state.data.description)) { Toast.show('描述不能为空'); return; }

    await this.props.onSubmit(this.state.data);
    Toast.show('操作成功!', Toast.Type.SUCCESS);
    this.props.onClose();
  }


  render = () => {
    const { data } = this.state;
    return (
      <Drawer
        title="另存为函数"
        width={500}
        onClose={this.props.onClose}
        visible={this.props.open}
        extra={<Button type="primary" onClick={this.onSubmit}>保存</Button>}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} >
          <Form.Item label="函数名" help="仅支持字母、下划线, eg.: A_b_c" required>
            <Input value={data?.name} onChange={(e) => { return this.onChangeValue(e, 'name'); }} />
          </Form.Item>
          <Form.Item label="别名" help="支持中文" required>
            <Input value={data?.displayName} onChange={(e) => { return this.onChangeValue(e, 'displayName'); }} />
          </Form.Item>
          <Form.Item label="描述" required>
            <Input value={data?.description} onChange={(e) => { return this.onChangeValue(e, 'description'); }} />
          </Form.Item>
        </Form>
      </Drawer>
    );
  }
}
