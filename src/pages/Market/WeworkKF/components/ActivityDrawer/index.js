import { K<PERSON><PERSON><PERSON>comeMessage, Poster, ReplyMessages } from '~/components';
import { <PERSON><PERSON>, Drawer, Form, Input, InputNumber, Select } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class ActivityDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    employees: PropTypes.array,
    data: PropTypes.object,
    onClose: PropTypes.func,
    onSubmit: PropTypes.func,
  }

  state = {
    data: {},
  }

  componentDidMount = () => {
    this.setState({ data: this.props.data });
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  render = () => {
    const { data } = this.state;
    return (
      <Drawer
        title="活动详情"
        placement="right"
        width="50vw"
        onClose={this.props.onClose}
        open={this.props.open}
        extra={<Button type="primary" onClick={() => { return this.props.onSubmit(data); }}>保存</Button>}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} className="common-form">
          <Form.Item label="活动名称">
            <Input
              value={data.name}
              onChange={(e) => { return this.onChangeValue(e, 'name'); }}
            />
          </Form.Item>
          <Form.Item label="企微员工">
            <Select value={data.employee} onChange={(e) => { return this.onChangeValue(e, 'employee'); }}>
              {
                (this.props.employees || []).map((item) => {
                  return <Select.Option value={item.wxUserId}>{item.name}({item.wxUserId})</Select.Option>;
                })
              }
            </Select>
          </Form.Item>
          <Form.Item label="奖励">
            <InputNumber
              min={0}
              max={9999}
              addonBefore="每邀请 1 位 奖励"
              addonAfter="Token"
              value={data.rewardsPerUser}
              onChange={(e) => { return this.onChangeValue(e, 'rewardsPerUser'); }}
            />
          </Form.Item>
          <Form.Item label="裂变海报">
            <Poster
              background={data?.posterBackground || {}}
              onChange={(e) => { return this.onChangeValue(e, 'posterBackground'); }}
            />
          </Form.Item>
          <Form.Item label="邀请成功通知">
            <KFWelcomeMessage
              text={data?.notification}
              onChange={(e) => { return this.onChangeValue(e, 'notification'); }}
            />
          </Form.Item>
          <Form.Item label="欢迎语">
            <ReplyMessages
              datas={data?.welcomes}
              onChange={(e) => { return this.onChangeValue(e, 'welcomes'); }}
            />
          </Form.Item>
        </Form>
      </Drawer>
    );
  }
}
