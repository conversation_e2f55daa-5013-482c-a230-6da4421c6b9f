import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
/* eslint-disable react/no-array-index-key */
import { InputUpload, Toast, ToolEditor } from '~/components';
import { Wecom } from '~/engine';
import { SubmitParams } from '~/plugins';
import { Button, Divider, Drawer, Form, Image, Input, Popconfirm, Radio, Select, Spin, Switch } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

const ChannelTypes = {
  SINGLE: { value: 1, name: '单人' },
  MUlTI: { value: 2, name: '多人' },
};

export default class EmployeeQrCode extends PureComponent {
  static propTypes = {
    channel: PropTypes.object,
    employees: PropTypes.array,
    open: PropTypes.bool,
    onClose: PropTypes.func,
    onSubmit: PropTypes.func,
  }

  state = {
    loading: false,
    detail: {
      type: 1,
      name: '',
      desc: '',
      partner: 'xinzhixinxi',
      tags: [],
      employees: [],
      welcomeTips: [{ type: 'TEXT', text: {}, image: {}, card: {} }],
      enableSkipVerify: true,
    },
  }

  componentDidMount = async () => {
    await this.setState({ loading: true });
    const topicUrlMap = {};
    const { channelUrl, relatedConfig } = this.props.channel;
    const keys = _.keys(relatedConfig?.sceneWelcomeMessages).filter((x) => { return x !== 'shortUrl'; });
    for (let index = 0; index < keys.length; index++) {
      const fullUrl = encodeURIComponent(`${channelUrl}&scene_param=${encodeURIComponent(keys[index])}`);
      const resp = await fetch(`https://fn.bzy.ai/v2/shorten?url=${fullUrl}`); // eslint-disable-line
      topicUrlMap[keys[index]] = await resp.text(); // eslint-disable-line
    }
    const resp = await fetch(`https://fn.bzy.ai/v2/shorten?url=${encodeURIComponent(channelUrl)}`);
    topicUrlMap['原链接'] = await resp.text();
    const stateParams = { topicUrlMap };
    const qrcodeId = relatedConfig?.relatedQrcodeId;
    if (!_.isUndefined(qrcodeId)) {
      const detail = await Wecom.getPersonQrcode({ id: qrcodeId });
      if (detail?.uuid?.length) {
        stateParams.detail = detail;
      }
    }

    stateParams.loading = false;
    this.setState(stateParams);
  }

  onChangeValue = (e, type) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ detail: { ...this.state.detail, [type]: value } });
  }

  onChangeWelcomeTips = (e, type, index, objKey) => {
    const value = e?.target ? e.target.value : e;
    const welcomeTips = this.state.detail.welcomeTips || [];
    if (objKey) {
      welcomeTips[index][objKey][type] = value;
    } else {
      welcomeTips[index][type] = value;
    }

    this.setState({ detail: { ...this.state.detail, welcomeTips } });
  }

  onAddWelcomeTips = (index) => {
    const welcomeTips = _.cloneDeep(this.state.detail.welcomeTips);
    welcomeTips.splice(index + 1, 0, { type: 'TEXT', text: {}, image: {}, card: {} });
    this.setState({ detail: { ...this.state.detail, welcomeTips } });
  }

  onDelWelcomeTips = (index) => {
    if (!index) return;
    const welcomeTips = _.cloneDeep(this.state.detail.welcomeTips);
    if (welcomeTips.length === 1) { return; }
    welcomeTips.splice(index, 1);
    this.setState({ detail: { ...this.state.detail, welcomeTips } });
  }

  onDel = async () => {
    const { id, type, name, desc, partner, tags, welcomeTips, enableSkipVerify } = this.state.detail;
    await Wecom.delPersonQrcode({ id });
    const relatedConfig = _.cloneDeep(this.props.channel.relatedConfig);
    relatedConfig.relatedQrcodeId = undefined;
    this.props.onSubmit({ ...this.props.channel, relatedConfig });
    this.setState({ detail: { type, name, desc, partner, tags, employees: [], welcomeTips, enableSkipVerify } });
  }

  onSubmit = async () => {
    const { detail } = this.state;
    if (detail.type === ChannelTypes.SINGLE.value && detail.employees.length !== 1) {
      Toast.show('单人活码只能选择一个员工', Toast.Type.ERROR);
      return;
    }

    const welcomeTips = [];

    for (const item of detail.welcomeTips) {
      welcomeTips.push({
        type: item.type,
        [item.type.toLowerCase()]: item[item.type.toLowerCase()],
      });
    }

    if (!SubmitParams.check(detail.name, detail.employees)) return;
    await this.setState({ loading: true });
    let qrCodeId = detail.id;
    if (_.isUndefined(detail.id)) {
      const data = await Wecom.addPersonQrcode({ ...detail, welcomeTips });
      const relatedConfig = _.cloneDeep(this.props.channel.relatedConfig);
      relatedConfig.relatedQrcodeId = data.id;
      qrCodeId = data.id;
      this.props.onSubmit({ ...this.props.channel, relatedConfig });
    } else {
      await Wecom.updatePersonQrcode({ ...detail, welcomeTips });
    }

    const result = await Wecom.getPersonQrcode({ id: qrCodeId });
    this.setState({ detail: result, loading: false });
    Toast.show('保存成功', Toast.Type.SUCCESS);
  }

  renderWelcomeMsgs = (welcomeTips = [], uid) => {
    return (
      <Form labelCol={{ span: 2 }} wrapperCol={{ span: 22 }} className="common-form">
        {
          (welcomeTips || []).map((x, idx) => {
            return (
              <>
                <Form.Item label="类型">
                  <div style={{ display: 'flex' }}>
                    <Select
                      disabled={idx === 0}
                      value={x?.type}
                      onChange={(e) => { return this.onChangeWelcomeTips(e, 'type', idx); }}
                    >
                      <Select.Option value="TEXT">文本</Select.Option>
                      <Select.Option value="CARD">图文</Select.Option>
                      <Select.Option value="IMAGE">图片</Select.Option>
                    </Select>

                    <span style={{ display: 'flex', width: 120, alignItems: 'center', justifyContent: 'flex-end' }}>
                      <PlusOutlined onClick={() => { return this.onAddWelcomeTips(idx); }} />
                      <Divider type="vertical" />
                      <DeleteOutlined onClick={() => { return this.onDelWelcomeTips(idx); }} />
                    </span>
                  </div>
                </Form.Item>
                {
                  x.type === 'TEXT' &&
                  <Form.Item label="文本">
                    <ToolEditor
                      noEscaped
                      types={[
                        { type: 'message-user', name: '用户昵称', value: '#user#' },
                        { type: 'message-employee', name: '员工昵称', value: '#employee#' },
                      ]}
                      key={`${uid || 0}-id-${idx}`}
                      value={x.text?.text}
                      editorId={`${uid || 0}-id-${idx}`}
                      barKeys={[]}
                      onChange={(value) => { return this.onChangeWelcomeTips(value, 'text', idx, 'text'); }}
                    />
                  </Form.Item>
                }
                {
                  x.type === 'IMAGE' &&
                  <Form.Item label="图片">
                    <InputUpload
                      accept="image/*"
                      url={x.image?.fileUrl}
                      onChange={(e) => { return this.onChangeWelcomeTips(e, 'fileUrl', idx, 'image'); }}
                    />
                  </Form.Item>
                }
                {
                  x.type === 'CARD' &&
                  <Form.Item label="图文">
                    <div className="card-item">
                      <InputUpload
                        accept="image/*"
                        placeholder="封面"
                        url={(x.card || {})?.thumbnail}
                        onChange={(e) => { return this.onChangeWelcomeTips(e, 'thumbnail', idx, 'card'); }}
                      />
                      <Input
                        addonBefore="标题"
                        style={{ margin: '5px 0' }}
                        value={(x.card || {}).title}
                        onChange={(e) => { return this.onChangeWelcomeTips(e, 'title', idx, 'card'); }}
                      />
                      <Input
                        addonBefore="描述"
                        value={(x.card || {}).description}
                        onChange={(e) => { return this.onChangeWelcomeTips(e, 'description', idx, 'card'); }}
                      />
                      <Input
                        addonBefore="链接"
                        style={{ margin: '5px 0' }}
                        value={(x.card || {}).url}
                        onChange={(e) => { return this.onChangeWelcomeTips(e, 'url', idx, 'card'); }}
                      />
                    </div>
                  </Form.Item>

                }
              </>
            );
          })
        }

      </Form>
    );
  }

  render() {
    const { open, employees } = this.props;
    const { detail, topicUrlMap } = this.state;
    const isEdit = !_.isUndefined(detail.uuid);
    return (
      <Drawer
        title="活码"
        placement="right"
        width="40vw"
        onClose={this.props.onClose}
        open={open}
        extra={<Button type="primary" onClick={() => { return this.onSubmit(); }}>保存</Button>}
      >
        <Spin spinning={this.state.loading}>
          <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} className="common-form">
            {isEdit && <Form.Item label="活码"><Image src={detail.qrCode} width={150} /></Form.Item>}
            <Form.Item label="话题短链">
              {
                _.map(topicUrlMap, (v, k) => {
                  return (
                    <Button
                      type="link"
                      size="small"
                      onClick={async () => {
                        await navigator.clipboard.writeText(v);
                        Toast.show('复制成功', Toast.Type.SUCCESS);
                      }}
                    >{k}
                    </Button>
                  );
                })
              }
            </Form.Item>
            <Form.Item label="新用户回调">
              <Input
                value={detail.newContactCallback}
                placeholder="请输入新用户回调"
                onChange={(e) => { return this.onChangeValue(e, 'newContactCallback'); }}
              />
            </Form.Item>
            <Form.Item label="名称" required>
              <Input
                value={detail.name}
                className="short-input"
                placeholder="请输入活码名称"
                onChange={(e) => { return this.onChangeValue(e, 'name'); }}
              />
            </Form.Item>
            <Form.Item label="员工" required>
              <div style={{ display: 'flex' }}>
                <Select
                  disabled={isEdit}
                  style={{ width: '100%' }}
                  placeholder="请选择员工"
                  value={detail?.employees || []}
                  mode="multiple"
                  optionFilterProp="children"
                  onChange={(e) => { return this.onChangeValue(e, 'employees'); }}
                >
                  {
                    (employees || []).map((item) => {
                      return <Select.Option value={item.wxUserId}>{`${item.name}(${item.wxUserId})`}</Select.Option>;
                    })
                  }
                </Select>
                {
                  isEdit &&
                  <Popconfirm title="确定删除活码吗？" onConfirm={() => { return this.onDel(); }}>
                    <Button style={{ marginLeft: 30 }} danger>删除</Button>
                  </Popconfirm>
                }
              </div>
            </Form.Item>
            <Form.Item required label="活码类型">
              <Radio.Group
                value={detail?.type}
                onChange={(e) => { return this.onChangeValue(e, 'type'); }}
              >
                {
                  (_.values(ChannelTypes) || []).map((item) => {
                    return <Radio value={item.value}>{item.name}</Radio>;
                  })
                }
              </Radio.Group>
            </Form.Item>
            <Form.Item required label="自动添加好友">
              <Switch
                checked={detail?.enableSkipVerify}
                onChange={(e) => { return this.onChangeValue(e, 'enableSkipVerify'); }}
              />
            </Form.Item>
            <Form.Item required label="欢迎语">
              {this.renderWelcomeMsgs(detail?.welcomeTips, detail?.uuid)}
            </Form.Item>
          </Form>
        </Spin>
      </Drawer>
    );
  }
}
