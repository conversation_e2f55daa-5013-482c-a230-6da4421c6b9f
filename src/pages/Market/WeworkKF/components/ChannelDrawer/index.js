import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { PaginationTable, Toast } from '~/components';
import Configs from '~/consts';
import { ChatBot, Workflow } from '~/engine';
import { <PERSON><PERSON>, Divider, Drawer, Form, Input, Popconfirm, Radio, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import EmployeeQrCode from './EmployeeQrCode';
import WelcomeMessage from './WelcomeMessage';

export default class ChannelDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    staff: PropTypes.object,
    channels: PropTypes.array,
    employees: PropTypes.array,
    onClose: PropTypes.func,
    onDel: PropTypes.func,
    onSubmit: PropTypes.func,
  }

  state = {
    channel: {},
    openDetail: false,
    openChannel: false,
    openEmployeeQrCode: false,
    sceneWelcomeMessages: [],
    workflows: [],
  }

  componentDidMount = async () => {
    const { items } = await ChatBot.fetchChatbotWorkflows(Configs.ALL_PAGE_PARAMS);
    const V2 = await Workflow.fetchWorkflows(Configs.ALL_PAGE_PARAMS);
    const workflows = items.map((x) => { return { value: x.uuid, label: x.name }; });
    this.setState({
      workflows: [
        { label: 'V2', options: V2.items.map((x) => { return { value: x.uuid, label: `${x.name} [v2]` }; }) },
        { label: 'V1', options: workflows },
      ],
    });
  }

  convertTextToMenu = (txt) => {
    const result = { headContent: '', list: [] };
    const parts = txt.split('|');
    const headContent = parts[1].trim().startsWith('[') ? '' : parts[1].trim();
    const menuItems = headContent ? parts.slice(1) : parts;
    result.headContent = headContent;
    const regex = /\[(.*?)\]\((.*?)\)/;

    for (const item of menuItems) {
      const match = item.trim().match(regex);
      if (match) {
        const text = match[1];
        const idOrUrl = match[2];
        if (idOrUrl.startsWith('http')) {
          result.list.push({ type: 'view', view: { url: idOrUrl, content: text } });
        } else if (idOrUrl) {
          result.list.push({ type: 'click', click: { id: idOrUrl, content: text } });
        } else {
          result.list.push({ type: 'text', text: { content: text } });
        }
      }
    }

    result.list = result.list.slice(0, 10);
    return result;
  }

  formatMenu = (items = []) => {
    let welcomeType = 'text';
    const msg = items[0];
    let menus = { headContent: '', list: [{ type: 'text', text: { content: '' } }] };
    const isMenu = (_.head(items) || '').startsWith('menu:|');
    if (isMenu) {
      welcomeType = 'menu';
      menus = this.convertTextToMenu(_.head(items));
    }
    return { welcomeType, menus, msg };
  }

  onChangeChannelValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const channel = _.cloneDeep(this.state.channel);
    channel[key] = value;
    if (key === 'welcomeType' && _.isUndefined(channel.menus)) {
      channel.menus = { headContent: '', list: [{ type: 'text', text: { content: '' } }] };
    }
    this.setState({ channel });
  }

  onChangeRelatedConfig = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const relatedConfig = this.state.channel.relatedConfig || {};
    relatedConfig[key] = value;
    this.setState({ channel: { ...this.state.channel, relatedConfig } });
  }

  onChangeChannelMenu = (e, key, index) => {
    const value = e?.target ? e.target.value : e;
    const menus = this.state.channel.menus || [];
    if (_.isUndefined(index)) {
      menus[key] = value;
    } else {
      if (key === 'type') { // eslint-disable-line
        const idOrUrl = key === 'click' ? 'id' : 'url';
        menus.list[index] = { type: value, [value]: { content: '', [idOrUrl]: '' } };
      } else {
        menus.list[index][menus.list[index].type][key] = value;
      }
    }
    this.setState({ channel: { ...this.state.channel, menus } });
  }

  onSubmitChannel = async () => {
    const staffId = this.props.staff.id;
    const { welcomeType, menus, scene, relatedConfig } = this.state.channel;
    if (_.isEmpty(scene)) { Toast.show('场景不能为空'); return; }
    if (_.isEmpty(relatedConfig)) { Toast.show('请完善信息'); return; }
    if (welcomeType === 'menu') {
      if (_.isUndefined(menus.headContent) && _.isEmpty(menus.headContent)) {
        Toast.show('请完善菜单信息');
      }

      let welcomeMsg = `menu:|${menus.headContent}`;
      menus.list.forEach((item) => {
        if (item.type === 'text') {
          welcomeMsg += `|[${item.text.content}]()`;
        } else {
          welcomeMsg += `|[${item[item.type].content}](${item[item.type].id || item[item.type].url})`;
        }
      });

      relatedConfig.welcomeMessages = [welcomeMsg];
    }

    const params = { ...this.state.channel, relatedConfig, staffId };
    this.props.onSubmit(params);
    this.setState({ openDetail: false, channel: {} });
  }

  onChangeSceneValue = (e, key, idx) => {
    const value = e?.target ? e.target.value : e;
    const sceneWelcomeMessages = _.cloneDeep(this.state.sceneWelcomeMessages);
    sceneWelcomeMessages[idx][key] = value;
    this.setState({ sceneWelcomeMessages });
  }

  onAddScene = (idx) => {
    const sceneWelcomeMessages = _.cloneDeep(this.state.sceneWelcomeMessages);
    sceneWelcomeMessages.splice(
      idx + 1, 0,
      {
        scene: '',
        msg: '',
        welcomeType: 'menu',
        menus: { headContent: '', list: [{ type: 'text', text: { content: '' } }] },
      },
    );
    this.setState({ sceneWelcomeMessages });
  }

  onDelScene = (idx) => {
    const sceneWelcomeMessages = _.cloneDeep(this.state.sceneWelcomeMessages);
    if (sceneWelcomeMessages.length === 1) { return; }
    sceneWelcomeMessages.splice(idx, 1);
    this.setState({ sceneWelcomeMessages });
  }

  onCopySceneUrl = async (channel, item) => {
    let url = '';
    if ((channel?.shortUrl || {})[item.scene]) {
      url = channel?.shortUrl[item.scene];
    } else {
      const fullUrl = encodeURIComponent(`${channel.channelUrl}&scene_param=${encodeURIComponent(item.scene)}`);

      const resp = await fetch(`https://fn.bzy.ai/v2/shorten?url=${fullUrl}`);
      url = await resp.text();
      this.setState({ channel: { ...channel, shortUrl: { ...channel.shortUrl, [item.scene]: url } } });
      this.onSubmitScene(true);
    }
    await navigator.clipboard.writeText(url);
    Toast.show('复制成功', Toast.Type.SUCCESS);
  }

  onSubmitScene = (openChannel = false) => {
    const { sceneWelcomeMessages: msgs, channel } = this.state;
    const scenes = _.map(msgs, 'scene');
    if (_.uniq(scenes).length !== scenes.length) {
      Toast.show('话题不能重复', Toast.Type.WARNING);
      return;
    }
    const sceneWelcomeMessages = {};

    msgs.forEach((msg) => {
      if (msg?.welcomeType === 'menu') {
        if (_.isUndefined(msg?.menus.headContent) && _.isEmpty(msg?.menus.headContent)) {
          Toast.show('请完善菜单信息');
        }
        let welcomeMsg = `menu:|${msg?.menus.headContent}`;
        (msg?.menus?.list || []).forEach((item) => {
          if (item.type === 'text') {
            welcomeMsg += `|[${item.text.content}]()`;
          } else {
            welcomeMsg += `|[${item[item.type].content}](${item[item.type].id || item[item.type].url})`;
          }
        });
        sceneWelcomeMessages[msg.scene] = [welcomeMsg];
      } else {
        sceneWelcomeMessages[msg.scene] = [msg.msg];
      }
    });
    sceneWelcomeMessages.shortUrl = channel?.shortUrl || {};
    const staffId = this.props.staff.id;
    const relatedConfig = _.cloneDeep(this.state.channel.relatedConfig);
    relatedConfig.sceneWelcomeMessages = sceneWelcomeMessages;
    const params = { ...this.state.channel, relatedConfig, staffId };
    this.props.onSubmit(params);

    if (!openChannel) {
      this.setState({ openChannel, channel: {}, sceneWelcomeMessages: [] });
    }
  }

  onOpenWorkflow = (uuid) => {
    const option = this.state.workflows[0].options.find((x) => { return x.value === uuid; });
    if (option) {
      window.open(`${window.location.origin}/workflow/${uuid}`);
    } else {
      window.open(`${window.location.origin}/workflow-v2/${uuid}`);
    }
  }

  onSubmitQrCode = async (channel) => {
    const staffId = this.props.staff.id;
    this.setState({ channel });
    const params = { ...this.state.channel, staffId };
    this.props.onSubmit(params);
  }

  renderWelcomeMessage = (channel) => {
    return (
      <Form.Item label="欢迎语">
        <Radio.Group
          value={channel.welcomeType}
          style={{ marginBottom: 10 }}
          onChange={(e) => { return this.onChangeChannelValue(e, 'welcomeType'); }}
        >
          <Radio value="text">文本</Radio>
          <Radio value="menu">菜单</Radio>
        </Radio.Group>

        {
          channel.welcomeType === 'text' &&
          <Input.TextArea
            autoSize={{ minRows: 5 }}
            value={(channel?.relatedConfig?.welcomeMessages || [])[0]}
            onChange={(e) => { return this.onChangeRelatedConfig([e.target.value], 'welcomeMessages'); }}
          />
        }
        {
          channel.welcomeType === 'menu' &&
          <Form labelCol={{ span: 2 }} wrapperCol={{ span: 22 }} className="common-form">
            <Form.Item label="起始">
              <Input.TextArea
                value={channel.menus.headContent}
                onChange={(e) => { return this.onChangeChannelMenu(e, 'headContent'); }}
              />
            </Form.Item>
            {
              channel.menus.list.map((item, index) => {
                return (
                  <Form.Item label={`菜单${index + 1}`} className="common-form">
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Select
                        value={item.type}
                        style={{ width: 220 }}
                        onChange={(e) => { return this.onChangeChannelMenu(e, 'type', index); }}
                      >
                        <Select.Option value="text">文本</Select.Option>
                        <Select.Option value="click">回复菜单</Select.Option>
                        <Select.Option value="view">超链菜单</Select.Option>
                      </Select>
                      <span>
                        <PlusOutlined
                          onClick={() => {
                            const menus = _.cloneDeep(channel.menus);
                            if (menus.list.length >= 10) { return; }
                            menus.list.push({ type: 'text', text: { content: '' } });
                            this.setState({ channel: { ...channel, menus } });
                          }}
                        />
                        <Divider type="vertical" />
                        <DeleteOutlined
                          onClick={() => {
                            const menus = _.cloneDeep(channel.menus);
                            if (menus.list.length === 1) { return; }
                            menus.list.splice(index, 1);
                            this.setState({ channel: { ...channel, menus } });
                          }}
                        />
                      </span>
                    </div>
                    <Input
                      style={{ margin: '5px 0' }}
                      addonBefore={item.type !== 'text' ? '菜单' : '内容'}
                      value={item[item.type]?.content}
                      onChange={(e) => { return this.onChangeChannelMenu(e, 'content', index); }}
                    />
                    {
                      item.type !== 'text' &&
                      <Input
                        addonBefore="内容"
                        value={item.click?.id || item.view?.url}
                        onChange={(e) => {
                          return this.onChangeChannelMenu(e, item.type === 'click' ? 'id' : 'url', index);
                        }}
                      />
                    }
                  </Form.Item>
                );
              })
            }
          </Form>
        }
      </Form.Item>
    );
  }

  renderDetailDrawer = () => {
    const { channel, workflows, openDetail } = this.state;

    return (
      <Drawer
        title="渠道详情"
        placement="right"
        width="40vw"
        onClose={() => { return this.setState({ openDetail: false, channel: {} }); }}
        open={openDetail}
        extra={<Button type="primary" onClick={() => { return this.onSubmitChannel(); }}>保存</Button>}
      >
        <Form labelCol={{ span: 4 }} className="common-form">
          <Form.Item label="场景">
            <Input
              value={channel?.scene}
              onChange={(e) => { return this.onChangeChannelValue(e, 'scene'); }}
            />
          </Form.Item>
          <Form.Item label="工作流ID">
            <div style={{ display: 'flex' }}>
              <Select
                showSearch
                filterOption={(input, option) => { return option.children.includes(input); }}
                value={channel?.relatedConfig?.relatedFlowUuid}
                onChange={(e) => { return this.onChangeRelatedConfig(e, 'relatedFlowUuid'); }}
                options={workflows}
              />

              <Button
                style={{ marginLeft: 10 }}
                onClick={() => { return this.onOpenWorkflow(channel?.relatedConfig?.relatedFlowUuid); }}
              >打开工作流
              </Button>
            </div>
          </Form.Item>
          <Form.Item label="SessionID">
            <Input
              value={channel?.relatedConfig?.relatedSessionId}
              onChange={(e) => { return this.onChangeRelatedConfig(e, 'relatedSessionId'); }}
            />
          </Form.Item>
          <Form.Item label="企微员工ID">
            <Input
              value={channel?.relatedConfig?.relatedEmployeeId}
              onChange={(e) => { return this.onChangeRelatedConfig(e, 'relatedEmployeeId'); }}
            />
          </Form.Item>
          {this.renderWelcomeMessage(channel)}
        </Form>
      </Drawer>
    );
  }

  renderChannelDrawer = () => {
    const { openChannel, sceneWelcomeMessages, channel } = this.state;
    return (
      <Drawer
        title="话题"
        placement="right"
        width="40vw"
        onClose={() => { return this.setState({ openChannel: false }); }}
        open={openChannel}
        extra={<Button type="primary" onClick={() => { return this.onSubmitScene(); }}>保存</Button>}
      >
        <Form labelCol={{ span: 2 }} className="common-form">
          {
            sceneWelcomeMessages.map((item, idx) => {
              return (
                <>
                  <Form.Item label={`话题${idx + 1}`} help="输入用户的聊天话题文字，支持中文">
                    <div style={{ display: 'flex' }}>
                      <Input
                        value={item.scene}
                        addonAfter={
                          <span>
                            <PlusOutlined onClick={() => { return this.onAddScene(idx); }} />
                            <Divider type="vertical" />
                            <DeleteOutlined onClick={() => { return this.onDelScene(idx); }} />
                          </span>
                        }
                        onChange={(e) => { return this.onChangeSceneValue(e, 'scene', idx); }}
                      />
                      <Button
                        style={{ marginLeft: 10 }}
                        onClick={() => { return this.onCopySceneUrl(channel, item); }}
                      >
                        复制
                      </Button>
                    </div>
                  </Form.Item>
                  <WelcomeMessage
                    data={item}
                    onChange={(e, key) => { return this.onChangeSceneValue(e, key, idx); }}
                  />
                </>
              );
            })
          }
        </Form>
      </Drawer>
    );
  }

  renderChannelColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '场景', dataIndex: 'scene', key: 'scene', align: 'center' },
      { title: '关联ID', dataIndex: 'workflowId', key: 'workflowId', align: 'center' },
      { title: '企业微信员工ID', dataIndex: 'staffId', key: 'staffId', align: 'center' },
      {
        title: '链接',
        dataIndex: 'channelUrl',
        key: 'channelUrl',
        align: 'center',
        render: (url, row) => {
          return (
            <a onClick={() => {
              const sceneWelcomeMessages = [];
              const { shortUrl, ...msgs } = row?.relatedConfig?.sceneWelcomeMessages || {};
              _.map(_.isEmpty(msgs) ? { '': [''] } : msgs, (v, k) => {
                const obj = this.formatMenu(v);
                sceneWelcomeMessages.push({ scene: k, ...obj });
              });
              this.setState({ openChannel: true, channel: { ...row, shortUrl: shortUrl || {} }, sceneWelcomeMessages });
            }}
            >
              查看
            </a>
          );
        },
      }, {
        title: '活码',
        dataIndex: 'code',
        key: 'code',
        align: 'center',
        render: (url, row) => {
          return <a onClick={() => { return this.setState({ openEmployeeQrCode: true, channel: row }); }}>查看</a>;
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        render: (t, row) => {
          return (
            <div>
              <a onClick={() => {
                const welcomeMessages = row?.relatedConfig?.welcomeMessages || [];
                const { welcomeType, menus } = this.formatMenu(welcomeMessages);
                const channel = { ...row, welcomeType, menus };
                return this.setState({ openDetail: true, channel });
              }}
              >
                编辑
              </a>
              <Divider type="vertical" />
              <Popconfirm title="确定删除吗？" onConfirm={() => { return this.props.onDel(row); }}>
                <a>删除</a>
              </Popconfirm>
            </div>
          );
        },
      },
    ];
  }

  render = () => {
    return (
      <Drawer
        title="渠道列表"
        placement="right"
        width="50vw"
        onClose={this.props.onClose}
        open={this.props.open}
        extra={
          <Button type="primary" onClick={() => { return this.setState({ openDetail: true, channel: {} }); }} >
            新增
          </Button>
        }
      >
        <PaginationTable
          needPagination={false}
          dataSource={this.props.channels}
          columns={this.renderChannelColumns()}
        />

        {this.state.openDetail && this.renderDetailDrawer()}
        {this.state.openChannel && this.renderChannelDrawer()}
        {
          this.state.openEmployeeQrCode &&
          <EmployeeQrCode
            channel={this.state.channel}
            employees={this.props.employees}
            open={this.state.openEmployeeQrCode}
            onSubmit={this.onSubmitQrCode}
            onClose={() => { return this.setState({ openEmployeeQrCode: false }); }}
          />
        }
      </Drawer>
    );
  }
}
