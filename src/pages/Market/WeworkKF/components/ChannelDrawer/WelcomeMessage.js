import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Divider, Form, Input, Radio, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class WelcomeMessage extends PureComponent {
  static propTypes = {
    data: PropTypes.object,
    onChange: PropTypes.func,
  }

  onChangeMenu = (e, key, index) => {
    const value = e?.target ? e.target.value : e;
    const menus = this.props.data.menus || [];
    if (_.isUndefined(index)) {
      menus[key] = value;
    } else {
      if (key === 'type') { // eslint-disable-line
        const idOrUrl = key === 'click' ? 'id' : 'url';
        menus.list[index] = { type: value, [value]: { content: '', [idOrUrl]: '' } };
      } else {
        menus.list[index][menus.list[index].type][key] = value;
      }
    }

    this.props.onChange(menus, 'menus');
  }

  onAddMenu = (index) => {
    const menus = _.cloneDeep(this.props.data.menus);
    menus.list.splice(index + 1, 0, { type: 'text', text: { content: '' } });
    this.props.onChange(menus, 'menus');
  }

  onDelMenu = (index) => {
    const menus = _.cloneDeep(this.props.data.menus);
    if (menus.list.length === 1) { return; }
    menus.list.splice(index, 1);
    this.props.onChange(menus, 'menus');
  }

  render() {
    const { data } = this.props;
    return (
      <Form.Item label="欢迎语">
        <Radio.Group
          value={data.welcomeType}
          style={{ marginBottom: 10 }}
          onChange={(e) => { return this.props.onChange(e, 'welcomeType'); }}
        >
          <Radio value="text">文本</Radio>
          <Radio value="menu">菜单</Radio>
        </Radio.Group>

        {
          data.welcomeType === 'text' &&
          <Input.TextArea
            autoSize={{ minRows: 5 }}
            value={data?.msg || ''}
            onChange={(e) => { return this.props.onChange(e, 'msg'); }}
          />
        }
        {
          data.welcomeType === 'menu' &&
          <Form labelCol={{ span: 2 }} wrapperCol={{ span: 22 }} className="common-form">
            <Form.Item label="起始">
              <Input.TextArea
                value={data.menus.headContent}
                onChange={(e) => { return this.onChangeMenu(e, 'headContent'); }}
              />
            </Form.Item>
            {
              data.menus.list.map((item, index) => {
                return (
                  <Form.Item label={`菜单${index + 1}`} className="common-form">
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Select
                        value={item.type}
                        style={{ width: 220 }}
                        onChange={(e) => { return this.onChangeMenu(e, 'type', index); }}
                      >
                        <Select.Option value="text">文本</Select.Option>
                        <Select.Option value="click">回复菜单</Select.Option>
                        <Select.Option value="view">超链菜单</Select.Option>
                      </Select>
                      <span>
                        <PlusOutlined onClick={() => { return this.onAddMenu(index); }} />
                        <Divider type="vertical" />
                        <DeleteOutlined
                          onClick={() => { return this.onDelMenu(index); }}
                        />
                      </span>
                    </div>
                    <Input
                      style={{ margin: '5px 0' }}
                      addonBefore={item.type !== 'text' ? '菜单' : '内容'}
                      value={item[item.type]?.content}
                      onChange={(e) => { return this.onChangeMenu(e, 'content', index); }}
                    />
                    {
                      item.type !== 'text' &&
                      <Input
                        addonBefore="内容"
                        value={item.click?.id || item.view?.url}
                        onChange={(e) => {
                          return this.onChangeMenu(e, item.type === 'click' ? 'id' : 'url', index);
                        }}
                      />
                    }
                  </Form.Item>
                );
              })
            }
          </Form>
        }
      </Form.Item>
    );
  }
}
