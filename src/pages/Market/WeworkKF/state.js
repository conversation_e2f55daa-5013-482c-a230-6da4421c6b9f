import Configs from '~/consts';
import { Market, Wecom, WeworkKF } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'WEWORK_KF/SET_STATE';
const CLEAR_STATE = 'WEWORK_KF/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchCorps = () => {
  return async (dispatch) => {
    const { items } = await WeworkKF.fetchCorps({ ...Configs.ALL_PAGE_PARAMS });
    dispatch(setState({ corps: items }));
  };
};

export const fetchStaffs = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().marketWeworkKF;
    const searchParams = {
      name: params?.name,
      corpId: params?.corpId,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const { items, total } = await WeworkKF.fetchStaffs(params);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const delStaff = (id) => {
  return async (dispatch) => {
    await WeworkKF.deleteStaff(id);
    dispatch(fetchStaffs());
  };
};

export const createStaff = (params) => {
  return async (dispatch) => {
    await WeworkKF.createStaff(params);
    dispatch(fetchStaffs());
  };
};

export const updateStaff = (params) => {
  return async () => {
    await WeworkKF.updateStaff(params);
  };
};

export const createStaffChannel = (params) => {
  return async () => {
    await WeworkKF.createStaffChannel(params);
  };
};

export const delStaffChannel = (id) => {
  return async () => {
    await WeworkKF.deleteStaffChannel(id);
  };
};

export const updateStaffChannel = (params) => {
  return async () => {
    await WeworkKF.updateStaffChannel(params);
  };
};

export const fetchStaffChannels = (params = {}) => {
  return async () => {
    const result = await WeworkKF.fetchStaffChannels(params);
    return result;
  };
};

export const exportConversations = (params) => {
  return async () => {
    const blob = await WeworkKF.exportConversations(params);
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '会话记录.xlsx';
    a.click();
    window.URL.revokeObjectURL(url);
  };
};

export const fetchStaffConversations = (params = {}) => {
  return async (dispatch, getState) => {
    const { conversationsPagination } = getState().marketWeworkKF;
    const searchParams = {
      openKfid: params.openKfid,
      orderBy: params.orderBy,
      'pagination.pageIndex': params.pageIndex || conversationsPagination.pageIndex,
      'pagination.pageSize': params.pageSize || conversationsPagination.pageSize,
    };
    const { items, total } = await WeworkKF.fetchStaffConversations(searchParams);
    dispatch(
      setState({
        conversations: items,
        conversationsTotal: total,
        conversationsPagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
        },
      }),
    );
  };
};

export const fetchStaffMessages = (params = {}) => {
  return async () => {
    const result = await WeworkKF.fetchStaffMessages(params);
    return result;
  };
};

export const fetchVoiceCloneSpeakers = () => {
  return async (dispatch) => {
    const result = await Market.fetchVoiceCloneSpeakers();
    dispatch(setState({ speakers: _.values(result) }));
  };
};

export const fetchEmployees = () => {
  return async (dispatch) => {
    const { items } = await Wecom.fetchEmployees();
    dispatch(setState({ employees: items }));
  };
};

export const fetchActivities = (params = {}) => {
  return async (dispatch, getState) => {
    const { activityPagination } = getState().marketWeworkKF;
    const searchParams = {
      openKfid: params.openKfid,
      orderBy: params.orderBy,
      'pagination.pageIndex': params.pageIndex || activityPagination.pageIndex,
      'pagination.pageSize': params.pageSize || activityPagination.pageSize,
    };
    const { items, total } = await WeworkKF.fetchInvitationConfigs(searchParams);
    dispatch(setState({
      activities: items,
      activitiesTotal: total,
      activityPagination: {
        pageIndex: searchParams['pagination.pageIndex'],
        pageSize: searchParams['pagination.pageSize'],
      },
    }));
  };
};

export const createActivity = (params) => {
  return async (dispatch) => {
    await WeworkKF.createInvitationConfig(params);
    dispatch(fetchActivities());
  };
};

export const updateActivity = (params) => {
  return async (dispatch) => {
    await WeworkKF.updateInvitationConfig(params);
    dispatch(fetchActivities());
  };
};

export const deleteActivity = (id) => {
  return async (dispatch) => {
    await WeworkKF.deleteInvitationConfig(id);
    dispatch(fetchActivities());
  };
};

export const createActivityFunc = (params = {}) => {
  return async () => {
    await WeworkKF.createActivityFunc(params);
  };
};

const _getInitState = () => {
  return {
    corps: [],
    speakers: [],
    employees: [],
    list: [],
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    conversations: [],
    conversationsTotal: 0,
    conversationsPagination: {
      pageIndex: 1,
      pageSize: 20,
    },
    activities: [],
    activitiesTotal: 0,
    activityPagination: {
      pageIndex: 1,
      pageSize: 20,
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
