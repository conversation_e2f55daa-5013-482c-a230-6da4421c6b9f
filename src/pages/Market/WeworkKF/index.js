import './components/CustomerDrawer/index.less';

import { FilterBar, PaginationTable, Toast } from '~/components';
import Configs from '~/consts';
import Engine, { Sessions } from '~/engine';
import { Avatar, Button, Divider, Image, Input, Modal, Popconfirm, Select, Tabs } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import QRCode from 'qrcode.react';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import ActivityDrawer from './components/ActivityDrawer';
import ChannelDrawer from './components/ChannelDrawer';
import ConversationDrawer from './components/ConversationDrawer';
import SaveFuncDrawer from './components/SaveFuncDrawer';
import StaffDrawer from './components/StaffDrawer';
import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.marketWeworkKF;
  },
  actions,
)
export default class WeworkKF extends Component {
  static propTypes = {
    speakers: PropTypes.array,
    corps: PropTypes.array,
    employees: PropTypes.array,
    list: PropTypes.array,
    total: PropTypes.number,
    pagination: PropTypes.object,
    conversations: PropTypes.array,
    conversationsTotal: PropTypes.number,
    conversationsPagination: PropTypes.object,
    activities: PropTypes.array,
    activitiesTotal: PropTypes.number,
    activitiesPagination: PropTypes.object,
    fetchCorps: PropTypes.func.isRequired,
    delStaff: PropTypes.func.isRequired,
    updateStaff: PropTypes.func.isRequired,
    createStaff: PropTypes.func.isRequired,
    createStaffChannel: PropTypes.func.isRequired,
    delStaffChannel: PropTypes.func.isRequired,
    updateStaffChannel: PropTypes.func.isRequired,
    fetchStaffChannels: PropTypes.func.isRequired,
    fetchStaffConversations: PropTypes.func.isRequired,
    exportConversations: PropTypes.func.isRequired,
    fetchStaffMessages: PropTypes.func.isRequired,
    fetchStaffs: PropTypes.func.isRequired,
    fetchEmployees: PropTypes.func.isRequired,
    fetchVoiceCloneSpeakers: PropTypes.func.isRequired,
    fetchActivities: PropTypes.func.isRequired,
    createActivity: PropTypes.func.isRequired,
    updateActivity: PropTypes.func.isRequired,
    deleteActivity: PropTypes.func.isRequired,
    createActivityFunc: PropTypes.func.isRequired,
    location: PropTypes.object,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    activeKey: 'customer',
    corpId: undefined,
    channels: [],
    openStaff: false,
    openChannel: false,
  }

  componentDidMount = async () => {
    await this.props.fetchCorps();
    await this.props.fetchEmployees();
    await this.props.fetchActivities();
    const { corpId } = _.head(this.props.corps);
    this.setState({ corpId });
    await this.props.fetchStaffs({ corpId });
    const { activeKey } = this.props.location.query;
    if (activeKey) {
      this.setState({ activeKey });
    }
    this.props.fetchVoiceCloneSpeakers();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onOpenChannel = async (staff) => {
    const { items } = await this.props.fetchStaffChannels({ staffId: staff.id, ...Configs.ALL_PAGE_PARAMS });
    this.setState({ openChannel: true, staff, channels: items });
  }

  onDelStaffChannel = async (channel) => {
    await this.props.delStaffChannel(channel.id);
    await this.onOpenChannel(this.state.staff);
  }

  onSubmitChannel = async (params) => {
    if (params.id) {
      await this.props.updateStaffChannel(params);
    } else {
      await this.props.createStaffChannel(params);
    }
    await this.onOpenChannel(this.state.staff);
  }

  onOpenConversation = async (staff) => {
    await this.props.fetchStaffConversations({ openKfid: staff.openKfid });
    this.setState({ staff, activeKey: 'conversation' });
  }

  onSearch = async (params) => {
    await this.props.fetchStaffs({ ...params, corpId: this.state.corpId });
  }

  onSubmitStaff = async (params) => {
    params.corpId = this.state.corpId; // eslint-disable-line
    if (params.id) {
      await this.props.updateStaff(params);
    } else {
      await this.props.createStaff(params);
    }
    await this.onSearch();
    this.setState({ open: false });
  }

  onExportConversations = async () => {
    const params = {
      openKfid: this.state.staff.openKfid,
    };
    await this.props.exportConversations(params);
  }

  onSubmitActivity = async (params) => {
    if (params?.id) {
      await this.props.updateActivity(params);
    } else {
      await this.props.createActivity(params);
    }
    this.setState({ openActivity: false, activity: {} });
    Toast.show('操作成功', Toast.Type.SUCCESS);
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '客服名称', dataIndex: 'name', key: 'name', align: 'center' },
      {
        title: '客服头像',
        dataIndex: 'avatar',
        key: 'avatar',
        align: 'center',
        render: (avatar) => {
          const url = _.isEmpty(avatar) ? '/static/ai-avatar.jpg' : avatar;
          return <Avatar src={url} />;
        },
      },
      {
        title: '渠道',
        dataIndex: 'channel',
        key: 'channel',
        align: 'center',
        render: (t, row) => {
          return (
            <Button type="link" onClick={() => { return this.onOpenChannel(row); }}>
              查看
            </Button>
          );
        },
      },
      {
        title: '会话',
        dataIndex: 'conversation',
        key: 'conversation',
        align: 'center',
        render: (t, row) => {
          return (
            <Button type="link" onClick={() => { return this.onOpenConversation(row); }}>
              查看
            </Button>
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        render: (t, row) => {
          return (
            <div>
              <a onClick={() => { return this.setState({ openStaff: true, staff: row }); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm
                title="确定删除吗？"
                onConfirm={() => { return this.props.delStaff(row.id); }}
              >
                <a>删除</a>
              </Popconfirm>
            </div>
          );
        },
      },
    ];
  }

  renderConversationColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: 'UID', dataIndex: 'userId', key: 'userId' },
      { title: '用户昵称', dataIndex: 'userNickname', key: 'userNickname', align: 'center' },
      {
        title: '会话开始时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (t) => {
          return moment(t).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '会话数', dataIndex: 'enterCount', key: 'enterCount', align: 'center', sorter: true,
      },
      {
        title: '消息数', dataIndex: 'messageCount', key: 'messageCount', align: 'center', sorter: true,
      },
      {
        title: '输入Token', dataIndex: 'promptTokens', key: 'promptTokens', align: 'center', sorter: true,
      },
      {
        title: '输出Token', dataIndex: 'completionTokens', key: 'completionTokens', align: 'center', sorter: true,
      },
      {
        title: '最后消息时间',
        dataIndex: 'lastMessageAt',
        key: 'lastMessageAt',
        align: 'center',
        render: (t) => {
          return moment(t).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '会话记录',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        render: (t, row) => {
          return (
            <a onClick={async () => { this.setState({ openChat: true, conversation: row }); }}>
              查看
            </a>
          );
        },
      },
    ];
  }

  renderActivityColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '活动名称', dataIndex: 'name', key: 'name', align: 'center' },
      {
        title: '活动海报',
        dataIndex: 'posterBackground',
        key: 'posterBackground',
        align: 'center',
        render: (poster) => {
          return <Image width={40} height={40} src={poster?.bgUrl} shape="square" />;
        },
      },
      {
        title: '员工',
        dataIndex: 'employee',
        key: 'employee',
        align: 'center',
        render: (employeeId) => {
          const employee = _.find(this.props.employees, { wxUserId: employeeId });
          return employee?.name;
        },
      },
      {
        title: '创建时间',
        dataIndex: 'time',
        key: 'time',
        align: 'center',
        render: (t) => {
          return moment(t).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        render: (t, row) => {
          return (
            <>
              <a onClick={() => { return this.setState({ openSaveFunc: true, activity: row }); }} >另存为函数</a>
              <Divider type="vertical" />
              <a onClick={() => { this.setState({ openActivity: true, activity: row }); }}>
                编辑
              </a>
              <Divider type="vertical" />
              <Popconfirm title="确定删除吗？" onConfirm={() => { this.props.deleteActivity(row.id); }}>
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  renderModal = () => {
    const { partnerId } = Sessions.getProfile();
    const domain = Engine.getApiEndpoint();
    const url = `${domain}/v2/wework/kf/auth?pid=${partnerId}`;
    return (
      <Modal
        title="授权二维码"
        visible={this.state.open}
        onCancel={() => { this.setState({ open: false }); }}
        footer={null}
      >
        <div style={{ textAlign: 'center' }}>
          <QRCode value={url} />
          <Input
            value={url}
            addonAfter={
              <span
                onClick={async () => {
                  await navigator.clipboard.writeText(url);
                  Toast.show('复制成功', Toast.Type.SUCCESS);
                }}
              >复制
              </span>
            }
          />
        </div>
      </Modal>
    );
  }

  renderStaffDrawer = () => {
    return (
      <StaffDrawer
        open={this.state.openStaff}
        staff={this.state.staff}
        speakers={this.props.speakers}
        onClose={() => { return this.setState({ openStaff: false }); }}
        onSubmit={this.onSubmitStaff}
      />
    );
  }

  renderChannelDrawer = () => {
    return (
      <ChannelDrawer
        open={this.state.openChannel}
        staff={this.state.staff}
        channels={this.state.channels}
        employees={this.props.employees}
        onClose={() => { return this.setState({ openChannel: false }); }}
        onDel={this.onDelStaffChannel}
        onSubmit={this.onSubmitChannel}
      />
    );
  }

  renderConversationDrawer = () => {
    return (
      <ConversationDrawer
        open={this.state.openChat}
        staff={this.state.staff}
        conversation={this.state.conversation}
        onClose={() => { return this.setState({ openChat: false }); }}
        fetchStaffMessages={this.props.fetchStaffMessages}
      />
    );
  }

  renderSelects = () => {
    return (
      <Select
        allowClear
        value={this.state.corpId}
        style={{ width: 160, marginBottom: 16 }}
        placeholder="请选择企业"
        onChange={async (e) => {
          await this.setState({ corpId: e });
          this.onSearch();
        }}
      >
        {
          this.props.corps.map((x) => {
            return (<Select.Option value={x.corpId}>{x.corpName}</Select.Option>);
          })
        }
      </Select>
    );
  }

  render = () => {
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <Tabs
          activeKey={this.state.activeKey}
          onChange={(activeKey) => {
            this.$replace('/market-wework-kf', { activeKey });
            return this.setState({ activeKey });
          }}
          tabBarExtraContent={{
            right: (
              <Button type="primary" onClick={() => { return this.setState({ open: true }); }}>
                授权
              </Button>
            ),
          }}
        >
          <Tabs.TabPane tab="企业客服" key="customer">
            <FilterBar
              canAdd
              addText="新增客服"
              renderSelects={this.renderSelects}
              onAdd={() => { return this.setState({ openStaff: true, staff: {} }); }}
            />

            <PaginationTable
              dataSource={this.props.list}
              totalDataCount={this.props.total}
              pagination={this.props.pagination}
              columns={this.renderColumns()}
              onPaginationChange={(e) => {
                return this.props.fetchStaffs({ ...e, corpId: this.state.corpId });
              }}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="客服会话" key="conversation">
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Select
                style={{ width: 160, marginBottom: 16 }}
                placeholder="请选择客服"
                value={this.state.staff?.id}
                onChange={
                  async (e) => {
                    const staff = _.find(this.props.list, { id: e });
                    this.onOpenConversation(staff);
                  }
                }
              >
                {
                  (this.props.list || []).map((x) => {
                    return (<Select.Option value={x.id}>{x.name}</Select.Option>);
                  })
                }
              </Select>

              <Button
                disabled={_.isEmpty(this.props.conversations)}
                onClick={() => { return this.onExportConversations(); }}
              >导出
              </Button>
            </div>
            <PaginationTable
              dataSource={this.props.conversations}
              totalDataCount={this.props.conversationsTotal}
              pagination={this.props.conversationsPagination}
              columns={this.renderConversationColumns()}
              onPaginationChange={(e) => {
                return this.props.fetchStaffConversations({ ...e, openKfid: this.state.staff.openKfid });
              }}
              onTableChange={({ current, pageSize }, filters, { order, field }, { action }) => {
                if (action === 'sort') {
                  const sortParams = {
                    pageIndex: current,
                    pageSize,
                    orderBy: order ? `${_.snakeCase(field)} ${_.trimEnd(order, 'end')}` : 'pub_date desc',
                  };
                  return this.props.fetchStaffConversations({ ...sortParams, openKfid: this.state.staff.openKfid });
                }
                return null;
              }}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="客服裂变" key="activity">
            <FilterBar
              canAdd
              shouldShowSearchInput={false}
              addText="新增裂变"
              onAdd={() => { return this.setState({ openActivity: true, activity: {} }); }}
            />

            <PaginationTable
              dataSource={this.props.activities}
              totalDataCount={this.props.activitiesTotal}
              pagination={this.props.activitiesPagination}
              columns={this.renderActivityColumns()}
              onPaginationChange={() => { }}
            />
          </Tabs.TabPane>
        </Tabs>

        {this.state.open && this.renderModal()}
        {this.state.openStaff && this.renderStaffDrawer()}
        {this.state.openChannel && this.renderChannelDrawer()}
        {this.state.openChat && this.renderConversationDrawer()}
        {
          this.state.openActivity &&
          <ActivityDrawer
            open={this.state.openActivity}
            data={this.state.activity}
            employees={this.props.employees}
            onClose={() => { return this.setState({ openActivity: false }); }}
            onSubmit={(e) => { return this.onSubmitActivity(e); }}
          />
        }
        {
          this.state.openSaveFunc &&
          <SaveFuncDrawer
            open={this.state.openSaveFunc}
            onClose={() => { return this.setState({ openSaveFunc: false }); }}
            onSubmit={async (e) => {
              const { activity } = this.state;
              const params = { ...e, invitationConfigId: activity.id };
              this.props.createActivityFunc(params);
              this.setState({ openSaveFunc: false, activity: {} });
              Toast.show('操作成功', Toast.Type.SUCCESS);
            }}
          />
        }
      </div>
    );
  }
}

export {
  reducer,
};
