
const SET_STATE = 'mpArticle/SET_STATE';
const CLEAR_STATE = 'mpArticle/CLEAR_STATE';

import { Accounts } from '~/engine';
import _ from 'lodash';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchRooms = () => {
  return async (dispatch) => {
    const { items } = await Accounts.fetchDouyinDataRooms({});
    const uids = _.uniq(_.map(items, 'originUid'));
    const anthors = {};
    for (const uid of uids) {
      const res = items.filter((x) => { return x.originUid === uid; });
      anthors[uid] = { list: res, name: res[0].userName };
    }

    dispatch(setState({ anthors }));
  };
};

export const exportDouyinRoomDatas = (params = {}) => {
  return async () => {
    const ds = await Accounts.exportDouyinRoomDatas(params);
    return ds;
  };
};

const _getInitState = () => {
  return {
    anthors: {},
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
