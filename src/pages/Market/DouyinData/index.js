import { But<PERSON>, Checkbox, Col, DatePicker, Divider, Form, Row, Select, Typography } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.marketDouyinData;
  },
  actions,
)
export default class MpArticle extends Component {
  static propTypes = {
    anthors: PropTypes.object,
    fetchRooms: PropTypes.func.isRequired,
    exportDouyinRoomDatas: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    indeterminate: false,
    checkAll: false,
    startTime: null,
    endTime: null,
    selectUids: [],
    selectRooms: [],
    roomList: {},
  }

  componentDidMount = async () => {
    await this.props.fetchRooms();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  formatDate = (dateString) => {
    const date = new Date(dateString);
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = date.getHours();
    const period = hours < 12 ? '上午' : '下午';
    return `${month}-${day}  ${period} 场`;
  }

  onChangeUids = (e = []) => {
    const { selectRooms, startTime, endTime } = this.state;
    const newRoomList = {};
    e.forEach((uid) => {
      const rooms = this.props.anthors[uid].list.filter((room) => {
        const roomTime = new Date(room.createTime).getTime();
        return (!startTime || roomTime >= startTime) && (!endTime || roomTime <= endTime);
      });
      newRoomList[uid] = rooms;
    });


    const newSelectRooms = selectRooms.filter((roomId) => {
      return e.some((uid) => {
        return newRoomList[uid].list.some((room) => { return room.roomId === roomId; });
      });
    });

    this.setState({ selectUids: e, selectRooms: newSelectRooms, roomList: newRoomList });
  }

  onChangeTime = (e = []) => {
    const { selectUids } = this.state;
    const { anthors } = this.props;
    const startTime = e && e[0] ? e[0].startOf('day').valueOf() : null;
    const endTime = e && e[1] ? e[1].endOf('day').valueOf() : null;

    const newRoomList = {};
    let newSelectRooms = [];

    selectUids.forEach((uid) => {
      const rooms = anthors[uid].list.filter((room) => {
        const roomTime = new Date(room.createTime).getTime();
        return (!startTime || roomTime >= startTime) && (!endTime || roomTime <= endTime);
      });
      newRoomList[uid] = rooms;
    });

    newSelectRooms = this.state.selectRooms.filter((roomId) => {
      return selectUids.some((uid) => {
        return newRoomList[uid].some((room) => { return room.roomId === roomId; });
      });
    });

    this.setState({ startTime, endTime, roomList: newRoomList, selectRooms: newSelectRooms });
  }

  onCheckAllChange = (e) => {
    const { selectUids, roomList } = this.state;
    const newSelectRooms = [];
    selectUids.forEach((uid) => {
      roomList[uid].forEach((room) => {
        if (e.target.checked) {
          newSelectRooms.push(room.roomId);
        }
      });
    });

    this.setState({ selectRooms: newSelectRooms, checkAll: e.target.checked, indeterminate: false });
  }

  onSumit = async () => {
    const { selectRooms } = this.state;
    const blob = await this.props.exportDouyinRoomDatas({
      originUids: [],
      roomIds: selectRooms,
      create_time_start: 0,
      create_time_end: 0,
    });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '百应数据.xlsx';
    a.click();
    window.URL.revokeObjectURL(url);
  }

  render = () => {
    const { selectUids, selectRooms, roomList } = this.state;

    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item label="主播">
            <Select
              mode="multiple"
              placeholder="请选择主播"
              style={{ width: '100%' }}
              value={selectUids}
              onChange={(e) => { return this.onChangeUids(e); }}
            >
              {
                Object.keys(this.props.anthors).map((x) => {
                  return <Select.Option key={x} value={x}>{this.props.anthors[x].name}</Select.Option>;
                })
              }
            </Select>
          </Form.Item>
          <Form.Item label="起止时间">
            <DatePicker.RangePicker
              format="YYYY-MM-DD"
              onChange={(e) => { this.onChangeTime(e); }}
            />
          </Form.Item>
          <Form.Item label="场次">
            <Checkbox
              indeterminate={this.state.indeterminate}
              checked={this.state.checkAll}
              onChange={this.onCheckAllChange}
            >
              全选
            </Checkbox>
            <Checkbox.Group
              value={selectRooms}
              style={{ width: '100%' }}
              onChange={(e) => { return this.setState({ indeterminate: false, selectRooms: e }); }}
            >
              {
                _.map(selectUids, (x) => {
                  const obj = this.props.anthors[x];
                  return (
                    <>
                      <Typography.Title level={4}>{obj.name}</Typography.Title>
                      <Row>
                        {
                          (roomList[x] || []).map((y) => {
                            return (
                              <Col span={8}>
                                <Checkbox key={y.roomId} value={y.roomId}>
                                  [{this.formatDate(y.createTime)}]
                                </Checkbox>
                              </Col>
                            );
                          })
                        }
                      </Row>
                      <Divider />
                    </>
                  );
                })
              }
            </Checkbox.Group>
          </Form.Item>

          {
            !_.isEmpty(selectRooms) && !_.isEmpty(selectUids) &&
            <Form.Item label="数据">
              <Button type="primary" onClick={this.onSumit}>下载</Button>
            </Form.Item>
          }
        </Form>
      </div>
    );
  }
}

export {
  reducer,
};
