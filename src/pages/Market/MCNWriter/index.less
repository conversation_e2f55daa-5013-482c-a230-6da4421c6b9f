.mcnWriterContainer {
  max-height: calc(100vh - 100px);
  overflow: auto;
  padding: 20px;
}

.mainTextAreaContainer {
  margin-bottom: 24px;
}

.mainTextArea {
  width: 100%;
  font-size: 16px;
  border-radius: 4px;
  resize: none;
}

.extraTextAreaContainer {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.extraTextArea {
  width: 100%;
  border-radius: 4px;
  resize: none;
}

.addButtonContainer {
  margin: 16px 0 24px;
}

.actionButtonsContainer {
  display: flex;
  justify-content: flex-start;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
