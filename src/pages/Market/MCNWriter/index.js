import './index.less';

import { PlusOutlined } from '@ant-design/icons';
import Engine, { Sessions } from '~/engine';
import ReconnectingWebSocket from '~/pages/Playground/WebSocket';
import { Timer } from '~/plugins';
import { Button, Card, Col, Input, Popconfirm, Row, Typography, message } from 'antd';
import _ from 'lodash';
import qs from 'qs';
import React, { Component } from 'react';

const { TextArea } = Input;

// 定义三个工作流ID
const WORKFLOW_IDS = {
  CLONE: '0YXtzJUnyMtHFMJRyDUEtm',
  WRITE: 'FVru2zmbsUoBUWDLt5ocSQ',
  TRAIN: 'MNRePWrF5NiTSW13Y3MTk2',
};
const STORAGE_KEYS = {
  MAIN_CONTENT: 'mcn_prompt',
  EXTRA_TEXT_AREAS: 'mcn_refs',
};

export default class MCNWriter extends Component {
  // 定义 localStorage 键名常量

  state = {
    mainContent: '',
    extraTextAreas: [],
    loading: {
      clone: false,
      write: false,
      train: false,
    },
    currentWorkflow: null,
    newPrompt: '',
    currentWriteIndex: 0,
    writeResults: [],
    trainResult: '',
  }

  componentDidMount() {
    // 从 localStorage 加载数据
    this.loadFromLocalStorage();
  }

  componentWillUnmount() {
    this.closeWebSocket();
  }

  // 从 localStorage 加载数据
  loadFromLocalStorage = () => {
    try {
      // 获取保存的主内容
      const savedMainContent = localStorage.getItem(STORAGE_KEYS.MAIN_CONTENT);
      if (savedMainContent) {
        this.setState({ mainContent: savedMainContent });
      }

      // 获取保存的额外文本区域
      const savedExtraTextAreas = localStorage.getItem(STORAGE_KEYS.EXTRA_TEXT_AREAS);
      if (savedExtraTextAreas) {
        this.setState({ extraTextAreas: JSON.parse(savedExtraTextAreas) });
      }
    } catch (error) {
      console.error('从本地存储加载数据失败:', error); // eslint-disable-line
    }
  }

  // 保存数据到 localStorage
  saveToLocalStorage = () => {
    try {
      localStorage.setItem(STORAGE_KEYS.MAIN_CONTENT, this.state.mainContent);
      localStorage.setItem(STORAGE_KEYS.EXTRA_TEXT_AREAS, JSON.stringify(this.state.extraTextAreas));
    } catch (error) {
      console.error('保存数据到本地存储失败:', error); // eslint-disable-line
    }
  }

  // 创建并打开WebSocket连接
  openWebSocket = (workflowId) => {
    // 关闭之前的连接
    this.closeWebSocket();

    const uri = Engine.getWssEndpoint();
    const path = `${uri}/v2/chatbot/workflow-v2/run/${workflowId}`;
    const query = { access_token: Sessions.getToken() };

    // 创建新的WebSocket连接
    this.ws = new ReconnectingWebSocket(`${path}?${qs.stringify(query)} `, [], this.onReceiveMsg);
    this.pingTimer = Timer.setInterval(() => { this.ws.send(JSON.stringify({ type: 'ping' })); }, 1000 * 30);
  }

  // 关闭WebSocket连接
  closeWebSocket = () => {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  // 设置加载状态
  setLoadingState = (isLoading) => {
    const { currentWorkflow } = this.state;
    if (currentWorkflow) {
      this.setState({
        loading: {
          ...this.state.loading,
          [currentWorkflow]: isLoading,
        },
      });
    }
  }

  // 执行工作流的通用方法
  executeWorkflow = (workflowType) => {
    const workflowId = WORKFLOW_IDS[workflowType];
    if (!workflowId) {
      message.error('工作流ID未定义');
      return;
    }

    // 设置当前工作流和加载状态
    this.setState({
      currentWorkflow: workflowType.toLowerCase(),
      loading: {
        ...this.state.loading,
        [workflowType.toLowerCase()]: true,
      },
    });

    // 打开WebSocket连接
    this.openWebSocket(workflowId);

    // 准备发送的数据
    let inputData = {};
    if (workflowType === 'CLONE') {
      // 只处理选中的extraTextAreas
      const selectedAreas = this.state.extraTextAreas.filter((area) => { return area.selected !== false; });
      inputData = {
        prompt: this.state.mainContent,
        ref_articles: _.map(selectedAreas, (area, idx) => {
          // 格式化参考文章，包含话题和内容
          return `目标生成文稿${idx + 1}\n话题：${area.topic}\n主体内容：${area.content}`;
        }).filter((content) => {
          return content.trim() !== '';
        }).join('\n\n'),
      };
    } else if (workflowType === 'WRITE') {
      this.setState({
        currentWriteIndex: 0,
        writeResults: [],
      }, () => {
        this.sendWriteRequest(0);
      });
      return;
    } else if (workflowType === 'TRAIN') {
      // 只处理选中的extraTextAreas
      const selectedAreas = this.state.extraTextAreas.filter((area) => { return area.selected !== false; });
      inputData = {
        prompt: this.state.mainContent,
        refs: _.map(selectedAreas, (area, idx) => {
          return `目标生成文稿${idx + 1}\n话题：${area.topic}\n主体内容：${area.content}`;
        }).filter((content) => {
          return content.trim() !== '';
        }).join('\n\n'),
        cloned_prompt: this.state.newPrompt,
        new_articles: _.map(this.state.writeResults, (area, idx) => {
          return `生成的文稿${idx + 1}\n${area.result}`;
        }).filter((content) => {
          return content.trim() !== '';
        }).join('\n\n'),
      };
    }

    // 延迟发送数据，确保WebSocket已连接
    setTimeout(() => {
      if (this.ws) {
        this.ws.send(JSON.stringify({
          text: JSON.stringify(inputData),
          type: 'message',
          is_beta: false,
        }));
      } else {
        message.error('WebSocket连接失败，请重试');
        this.setLoadingState(false);
      }
    }, 2 * 1000);
  }

  // 发送写稿请求
  sendWriteRequest = (index) => {
    // 过滤出选中的extraTextAreas
    const selectedAreas = this.state.extraTextAreas.filter((area) => { return area.selected !== false; });

    if (index >= selectedAreas.length) {
      this.setLoadingState(false);
      message.success('所有写稿任务已完成');
      return;
    }

    const area = selectedAreas[index];
    const inputData = { prompt: this.state.newPrompt, topic: area.topic };

    // 延迟发送数据，确保WebSocket已连接
    setTimeout(() => {
      if (this.ws) {
        this.ws.send(JSON.stringify({
          text: JSON.stringify(inputData),
          type: 'message',
          is_beta: false,
        }));
      } else {
        message.error('WebSocket连接失败，请重试');
        this.setLoadingState(false);
      }
    }, 2 * 1000);
  }

  // 添加新的文本区域
  addTextArea = () => {
    const newId = Date.now(); // 使用时间戳作为唯一id
    this.setState({
      extraTextAreas: [...this.state.extraTextAreas, { id: newId, topic: '', content: '', selected: true }],
    }, this.saveToLocalStorage);
  };

  // 更新额外文本区域的内容
  updateExtraContent = (id, field, value) => {
    const updated = this.state.extraTextAreas.map((area) => {
      return area.id === id ? { ...area, [field]: value } : area;
    });
    this.setState({ extraTextAreas: updated }, this.saveToLocalStorage);
  };

  // 删除额外文本区域
  removeTextArea = (id) => {
    const filtered = this.state.extraTextAreas.filter((area) => {
      return area.id !== id;
    });
    this.setState({ extraTextAreas: filtered }, this.saveToLocalStorage);
  };

  // 切换额外文本区域的选中状态
  toggleSelection = (id) => {
    const updated = this.state.extraTextAreas.map((area) => {
      return area.id === id ? { ...area, selected: !area.selected } : area;
    });
    this.setState({ extraTextAreas: updated }, this.saveToLocalStorage);
  };

  // 处理复刻按钮点击
  handleClone = () => {
    this.executeWorkflow('CLONE');
  };

  // 处理写稿按钮点击
  handleWrite = () => {
    this.executeWorkflow('WRITE');
  };

  // 处理训练按钮点击
  handleTrain = () => {
    this.executeWorkflow('TRAIN');
  };

  // 替换原始提示词
  replaceWithTrainResult = () => {
    this.setState({ mainContent: this.state.trainResult }, this.saveToLocalStorage);
    message.success('原始提示词已替换为训练结果');
    this.setState({
      trainResult: '',
      newPrompt: '',
      currentWriteIndex: 0,
      writeResults: [],
    });
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // 处理接收到的WebSocket消息
  onReceiveMsg = (event) => {
    if (event.data === 'pong') {
      return;
    }

    try {
      const data = JSON.parse(event.data);
      if (data.type === 'final_result') {
        if (this.state.loading.clone) {
          // 处理复刻结果
          this.setState({
            newPrompt: data.data.output,
            loading: { ...this.state.loading, clone: false },
          });
          message.success('复刻成功');
        } else if (this.state.loading.write) {
          // 处理写稿结果
          // 保存当前结果
          const updatedResults = [...this.state.writeResults];
          updatedResults[this.state.currentWriteIndex] = {
            topic: this.state.extraTextAreas[this.state.currentWriteIndex].topic,
            result: data.data.output,
          };

          // 更新索引和结果
          const nextIndex = this.state.currentWriteIndex + 1;

          this.setState({
            writeResults: updatedResults,
            currentWriteIndex: nextIndex,
          }, () => {
            if (nextIndex < this.state.extraTextAreas.length) {
              this.sendWriteRequest(nextIndex);
            } else {
              this.setLoadingState(false);
              message.success('所有写稿任务已完成');
            }
          });
        } else if (this.state.loading.train) {
          // 处理训练结果
          this.setState({
            trainResult: data.data.output,
            loading: { ...this.state.loading, train: false },
          });
          message.success('训练成功');
        }
        const container = document.querySelector('.mcnWriterContainer');
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      }
    } catch (error) {
      console.error('处理消息出错:', error); // eslint-disable-line
    }
  }

  render() {
    const { mainContent, extraTextAreas, loading } = this.state;

    return (
      <div className="mcnWriterContainer">
        <Card title="" bordered={false}>
          {/* 主文本区域 */}
          <div className="mainTextAreaContainer">
            <Typography.Title level={5}>原始提示词:</Typography.Title>
            <TextArea
              value={mainContent}
              onChange={(e) => { return this.setState({ mainContent: e.target.value }, this.saveToLocalStorage); }}
              placeholder="请在这里输入提示词"
              autoSize={{ minRows: 5, maxRows: 10 }}
              className="mainTextArea"
            />
          </div>

          {/* 额外文本区域列表 */}
          {extraTextAreas.map((area, idx) => {
            return (
              <div key={area.id} className="extraTextAreaContainer">
                <Row gutter={16} align="middle">
                  <Col flex="auto">
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                      <Input
                        addonBefore={
                          <span style={{ display: 'flex', alignItems: 'center' }}>
                            <input
                              type="checkbox"
                              checked={area.selected !== false}
                              onChange={() => { return this.toggleSelection(area.id); }}
                              style={{ marginRight: '8px' }}
                            />
                            参考话题 {idx + 1}
                          </span>
                        }
                        value={area.topic}
                        onChange={(e) => { return this.updateExtraContent(area.id, 'topic', e.target.value); }}
                        placeholder="请输入参考话题"
                        autoSize={{ minRows: 2, maxRows: 3 }}
                        className="extraTextArea"
                      />
                    </div>
                    <Typography.Text level={5} style={{ marginBottom: 0 }}>
                      {`参考内容 ${idx + 1}`}:
                    </Typography.Text>
                    <TextArea
                      value={area.content}
                      onChange={(e) => { return this.updateExtraContent(area.id, 'content', e.target.value); }}
                      placeholder="请输入参考内容"
                      autoSize={{ minRows: 3, maxRows: 5 }}
                      className="extraTextArea"
                    />
                  </Col>
                  <Col>
                    <Button
                      type="text"
                      danger
                      onClick={() => { return this.removeTextArea(area.id); }}
                    >
                      删除
                    </Button>
                  </Col>
                </Row>
              </div>
            );
          })}

          {/* 添加文本区域按钮 */}
          <div className="addButtonContainer">
            <Row gutter={16} align="middle" justify="space-between">
              <Col span={18}>
                <Button
                  type="dashed"
                  onClick={this.addTextArea}
                  icon={<PlusOutlined />}
                  block
                >
                  添加参考文章
                </Button>
              </Col>
              <Col span={6}>
                <Button
                  type="primary"
                  onClick={() => { return this.handleClone(); }}
                  loading={loading.clone}
                  disabled={loading.write || loading.train}
                >
                  复刻
                </Button>
              </Col>
            </Row>
          </div>

          {
            !this.state.loading.clone && !_.isEmpty(this.state.newPrompt) &&
            <div className="newPromptContainer" style={{ marginTop: '20px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography.Title level={5}>新提示词:</Typography.Title>
                <Button
                  type="primary"
                  onClick={() => { return this.handleWrite(); }}
                  loading={loading.write}
                  disabled={loading.clone || loading.train || _.isEmpty(this.state.newPrompt)}
                >
                  写稿
                </Button>
              </div>
              <TextArea
                value={this.state.newPrompt}
                onChange={(e) => { return this.setState({ newPrompt: e.target.value }); }}
                placeholder="这里将显示生成的新提示词"
                autoSize={{ minRows: 5, maxRows: 10 }}
                className="newPromptTextArea"
              />
            </div>
          }

          {/* 展示写稿内容 */}
          {this.state.writeResults.length > 0 && (
            <div className="writeResultsContainer" style={{ marginTop: '20px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography.Title level={5}>写稿结果对比：</Typography.Title>
                <Button
                  type="primary"
                  onClick={() => { return this.handleTrain(); }}
                  loading={loading.train}
                  disabled={loading.clone || loading.write || _.isEmpty(this.state.newPrompt)}
                >
                  训练
                </Button>
              </div>
              {this.state.writeResults.map((result, index) => {
                const originalItem = this.state.extraTextAreas.find((item) => {
                  return item.topic === result.topic;
                }) || {};

                return (
                  <Card
                    key={`result-${index}`} // eslint-disable-line
                    title={`话题: ${result.topic}`}
                    style={{ marginBottom: '20px' }}
                    bordered
                  >
                    <Row gutter={16}>
                      <Col span={12}>
                        <Card type="inner" title="原始内容">
                          <Typography.Paragraph>
                            {originalItem.content || '无原始内容'}
                          </Typography.Paragraph>
                        </Card>
                      </Col>
                      <Col span={12}>
                        <Card type="inner" title="生成结果">
                          <Typography.Paragraph>
                            {result.result || '暂无结果'}
                          </Typography.Paragraph>
                        </Card>
                      </Col>
                    </Row>
                  </Card>
                );
              })}
            </div>
          )}

          {/* 展示训练内容 */}
          {this.state.trainResult && (
            <div className="trainResultContainer" style={{ marginTop: '20px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography.Title level={5}>训练结果:</Typography.Title>
                <Popconfirm title="确定替换原始提示词吗？" onConfirm={this.replaceWithTrainResult} okText="是" cancelText="否">
                  <Button type="primary">
                    替换原始提示词
                  </Button>
                </Popconfirm>
              </div>
              <TextArea
                value={this.state.trainResult}
                onChange={(e) => { return this.setState({ trainResult: e.target.value }); }}
                placeholder="这里将显示训练结果"
                autoSize={{ minRows: 5, maxRows: 10 }}
                className="trainResultTextArea"
              />
            </div>
          )}
        </Card>
      </div>
    );
  }
}
