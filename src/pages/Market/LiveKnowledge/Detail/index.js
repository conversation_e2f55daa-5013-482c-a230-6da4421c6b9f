import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Divider, Drawer, Dropdown, Form, Input, List, Modal, Popconfirm, Tree, Typography } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.marketLiveKnowledgeDetail;
  },
  actions,
)
export default class MarketLiveKnowledgeDetail extends Component {
  static propTypes = {
    addHelperKnowledge: PropTypes.func.isRequired,
    delHelperKnowledge: PropTypes.func.isRequired,
    updateHelperKnowledge: PropTypes.func.isRequired,
    fetchHelperLibDetail: PropTypes.func.isRequired,
    items: PropTypes.array.isRequired,
    clearState: PropTypes.func.isRequired,
    match: PropTypes.object.isRequired,
  }

  state = {
    detail: {},
    parent: null,
  }

  componentDidMount = async () => {
    const { id } = this.props.match.params;
    await this.props.fetchHelperLibDetail(id);
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  findParent = (tree, key) => {
    let parentKey = null;

    function traverse(node, parent) {
      if (node.knowledgeId === key) {
        parentKey = parent ? parent.knowledgeId : null;
        return true;
      }

      if (node.knowledges) {
        for (let i = 0; i < node.knowledges.length; i++) {
          if (traverse(node.knowledges[i], node)) return true;
        }
      }

      return false;
    }

    for (let i = 0; i < tree.length; i++) {
      if (traverse(tree[i], null)) break;
    }

    return parentKey;
  }

  onClickMenu = (key, e) => {
    const { id } = this.props.match.params;
    if (key === 'del') {
      Modal.confirm({
        title: '是否删除?',
        onOk: () => {
          return this.props.delHelperKnowledge({ libId: id, knowledgeId: e.knowledgeId });
        },
      });
      return;
    }

    if (key === 'add') {
      const preKnowledgeId = _.last(e?.knowledges)?.knowledgeId || 0;
      this.setState({
        parent: e,
        detail: { parentId: e.knowledgeId, preKnowledgeId, question: '', answer: '' },
        openQADrawer: true,
      });
    }

    if (key === 'edit') {
      const parentId = this.findParent(this.props.items, e.knowledgeId);
      this.setState({ detail: { ...e, parentId, preKnowledgeId: e.seq - 1 }, openQADrawer: true });
    }
  }

  onSubmit = async () => {
    const libId = this.props.match.params?.id;
    const { knowledgeId, question, answer, parentId, preKnowledgeId } = this.state.detail;
    const params = { question, answer, id: libId, parentId: parentId || 0, preKnowledgeId: preKnowledgeId || 0 };

    if (_.isUndefined(knowledgeId)) {
      await this.props.addHelperKnowledge(params);
    } else {
      await this.props.updateHelperKnowledge({ ...params, knowledgeId, libId });
    }
    this.setState({ openQADrawer: false, detail: {}, parent: null });
  }

  renderQADrawer = () => {
    const { openQADrawer, parent, detail } = this.state;
    return (
      <Drawer
        title={parent ? `[${parent.question}] - 新增子集` : '新增干货'}
        visible={openQADrawer}
        onClose={() => { this.setState({ openQADrawer: false }); }}
        width="40vw"
        extra={<Button type="primary" onClick={() => { return this.onSubmit(); }} >提交</Button>}
      >
        <Form layout="vertical">
          <Form.Item label="大纲">
            <Input
              value={detail.question}
              onChange={(e) => { return this.setState({ detail: { ...detail, question: e.target.value } }); }}
            />
          </Form.Item>
          <Form.Item label="干货">
            <Input.TextArea
              autoSize={{ minRows: 3 }}
              value={detail.answer}
              onChange={(e) => { return this.setState({ detail: { ...detail, answer: e.target.value } }); }}
            />
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderTree = () => {
    return (
      <div style={{ width: '50vw' }} >
        <div style={{ marginBottom: 10 }}>
          <Button type="link" onClick={() => { return this.setState({ openQADrawer: true }); }}>新增</Button>
        </div>
        <Tree
          showLine
          defaultExpandAll
          fieldNames={{ title: 'question', key: 'knowledgeId', children: 'knowledges' }}
          treeData={this.props.items}
          titleRender={(e) => {
            return (
              <Dropdown
                menu={{
                  items: [{ label: '删除', key: 'del' }, { label: '新增', key: 'add' }, { label: '编辑', key: 'edit' }],
                  onClick: ({ key }) => { return this.onClickMenu(key, e); },
                }}
                placement="bottomLeft"
                trigger={['contextMenu']}
              >
                <Typography.Title level={5} style={{ marginBottom: 0 }}>
                  {e.question}
                </Typography.Title>
              </Dropdown>
            );
          }}
        />
      </div>
    );
  }


  renderV2 = () => {
    return (
      <div style={{ background: '#fff', padding: 30, display: 'flex' }}>
        <List
          bordered
          header={
            <div style={{ width: '100%', textAlign: 'end' }}>
              <a onClick={() => { return this.setState({ openQADrawer: true }); }} >新增</a>
            </div>
          }
          style={{ width: '50vw' }}
          dataSource={this.props.items}
          renderItem={(item, idx) => {
            return (
              <List.Item extra={
                <span style={{ float: 'right', cursor: 'pointer' }}>
                  <EditOutlined onClick={() => { return this.setState({ detail: item, openQADrawer: true }); }} />
                  <Divider type="vertical" />
                  <Popconfirm
                    title="是否删除?"
                    onConfirm={() => {
                      const { id } = this.props.match.params;
                      return this.props.delHelperKnowledge({ libId: id, knowledgeId: item.knowledgeId });
                    }}
                  >
                    <DeleteOutlined />
                  </Popconfirm>
                </span>
              }
              >
                [{idx + 1}] {item.question}
              </List.Item>
            );
          }}
        />
        {this.state.openQADrawer && this.renderQADrawer()}
      </div>
    );
  }

  render = () => {
    return (
      <div style={{ background: '#fff', padding: 30, display: 'flex' }}>
        {this.renderTree()}
        {this.state.openQADrawer && this.renderQADrawer()}
      </div>
    );
  }
}

export {
  reducer,
};
