import Configs from '~/consts';
import { Market } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'mpArticle/SET_STATE';
const CLEAR_STATE = 'mpArticle/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchHelperLibDetail = (id) => {
  return async (dispatch) => {
    const items = await Market.fetchHelperLibDetail({ id, ...Configs.ALL_PAGE_PARAMS });
    dispatch(setState({ items: _.values(items) }));
  };
};

export const addHelperKnowledge = (params) => {
  return async (dispatch) => {
    await Market.addHelperKnowledge(params);
    dispatch(fetchHelperLibDetail(params.id));
  };
};

export const delHelperKnowledge = (params) => {
  return async (dispatch) => {
    await Market.delHelperKnowledge(params);
    dispatch(fetchHelperLibDetail(params.libId));
  };
};

export const updateHelperKnowledge = (params) => {
  return async (dispatch) => {
    await Market.updateHelperKnowledge(params);
    dispatch(fetchHelperLibDetail(params.libId));
  };
};

const _getInitState = () => {
  return {
    items: [],
  };
};


export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
