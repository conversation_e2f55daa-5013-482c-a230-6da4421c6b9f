import { PaginationTable, Toast } from '~/components';
import { Button, Divider, Form, Input, Modal, Popconfirm, Select, Tree } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.marketLiveKnowledge;
  },
  actions,
)
export default class MarketLiveKnowledge extends Component {
  static propTypes = {
    fetchPartners: PropTypes.func.isRequired,
    addHelperLib: PropTypes.func.isRequired,
    delHelperLib: PropTypes.func.isRequired,
    updateHelperLib: PropTypes.func.isRequired,
    fetchHelperLibs: PropTypes.func.isRequired,
    fetchLibLabels: PropTypes.func.isRequired,
    fetchLibsByLabel: PropTypes.func.isRequired,
    list: PropTypes.array.isRequired,
    labels: PropTypes.array.isRequired,
    partners: PropTypes.array.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    detail: {},
    script: {},
    openAssignModal: false,
    tree: [],
  }

  componentDidMount = async () => {
    this.props.fetchPartners();
    this.props.fetchLibLabels();
    await this.props.fetchHelperLibs();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ detail: { ...this.state.detail, [key]: value } });
  }

  onSearchLibsByLabel = async (e) => {
    const tree = await this.props.fetchLibsByLabel({ labels: [e] });
    this.setState({ tree });
  }

  onCheckTree = (selectedKeys, e) => {
    const { tree } = this.state;
    const genKnowledges = [];
    selectedKeys.forEach((x) => {
      const item = tree.find((t) => { return t.key === x; });
      if (!_.isUndefined(item)) {
        genKnowledges.push({
          libId: item.key,
          knowledges: item.children.map((i) => { return { knowledgeId: i.key, knowledges: [] }; }),
        });
      }
    });
    if (!_.isEmpty(e.halfCheckedKeys)) {
      e.halfCheckedKeys.forEach((x) => {
        const item = tree.find((t) => { return t.key === x; });
        if (!_.isUndefined(item)) {
          const subItems = item.children.filter((i) => { return selectedKeys.includes(i.key); });
          genKnowledges.push({
            libId: item.key,
            knowledges: subItems.map((i) => { return { knowledgeId: i.key, knowledges: [] }; }),
          });
        }
      });
    }
    this.onChangeScript(genKnowledges, 'genKnowledges');
  }

  onChangeScript = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ script: { ...this.state.script, [key]: value } });
  }

  onSubmit = async () => {
    const { detail } = this.state;
    if (_.isEmpty(detail.libName) || _.isEmpty(detail.label)) {
      Toast.show('请输入名称和标签', Toast.Type.WARNING);
      return;
    }

    if (_.isUndefined(detail?.id)) {
      await this.props.addHelperLib(detail);
    } else {
      await this.props.updateHelperLib(detail);
    }
    this.setState({ openLibNameModal: false, detail: {} });
    Toast.show('操作成功', Toast.Type.SUCCESS);
  }

  renderLibNameModal = () => {
    const { detail, openLibNameModal } = this.state;
    return (
      <Modal
        title="资料库"
        visible={openLibNameModal}
        onOk={() => { return this.onSubmit(); }}
        onCancel={() => { return this.setState({ openLibNameModal: false, detail: {} }); }}
      >
        <Form.Item label="名称">
          <Input
            placeholder="请输入名称"
            value={detail?.libName}
            onChange={(e) => { return this.onChangeValue(e, 'libName'); }}
          />
        </Form.Item>
        <Form.Item label="标签">
          <Select
            mode="tags"
            style={{ width: '100%' }}
            placeholder="请输入标签"
            value={_.isUndefined(detail?.label) ? [] : [detail?.label]}
            filterOption={(input, option) => { return option?.children?.includes(input); }}
            onChange={(e) => { return this.onChangeValue(_.last(e), 'label'); }}
          >
            {
              (this.props.labels || []).map((x) => {
                return <Select.Option value={x}>{x}</Select.Option>;
              })
            }
          </Select>
        </Form.Item>
      </Modal>
    );
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', align: 'center', width: 80 },
      { title: '名称', dataIndex: 'libName', align: 'center' },
      { title: '标签', dataIndex: 'label', align: 'label' },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        align: 'center',
        render: (t) => { return moment(t).format('YYYY-MM-DD HH:mm:ss'); },
      },
      {
        title: '更新时间',
        dataIndex: 'updatedAt',
        align: 'center',
        render: (t) => { return moment(t).format('YYYY-MM-DD HH:mm:ss'); },
      },
      {
        title: '操作',
        dataIndex: 'id',
        align: 'center',
        render: (t, row) => {
          return (
            <span>
              <a onClick={() => { return this.setState({ openLibNameModal: true, detail: row }); }}>编辑</a>
              <Divider type="vertical" />
              <a onClick={() => { return this.$push(`/market-live-knowledge/${row.id}`); }}>干货</a>
              <Divider type="vertical" />
              <Popconfirm
                title="确定删除？"
                onConfirm={() => { return this.props.delHelperLib(row.id); }}
              >
                <a>删除</a>
              </Popconfirm>
            </span>
          );
        },
      },

    ];
  }

  renderAssignModal = () => {
    const { tree, script, openAssignModal } = this.state;
    return (
      <Modal
        title="分配"
        visible={openAssignModal}
        onOk={() => { return this.setState({ openAssignModal: false }); }}
        onCancel={() => { return this.setState({ openAssignModal: false }); }}
      >
        <Form className="common-form">
          <Form.Item label="名称">
            <Input
              value={script?.newHelperName}
              onChange={(e) => { return this.onChangeScript(e, 'newHelperName'); }}
            />
          </Form.Item>
          <Form.Item label="标签">
            <Select onChange={(e) => { return this.onSearchLibsByLabel(e); }}>
              {
                (this.props.labels || []).map((x) => {
                  return <Select.Option value={x}>{x}</Select.Option>;
                })
              }
            </Select>
          </Form.Item>
          <Form.Item label="主播">
            <Select
              style={{ width: '100%' }}
              mode="multiple"
              placeholder="请选择主播"
              value={script?.genPartnerIds}
              onChange={(e) => { return this.onChangeScript(e, 'genPartnerIds'); }}
            >
              {this.props.partners.map((x) => {
                return <Select.Option value={x.id}>{x.name}</Select.Option>;
              })}
            </Select>
          </Form.Item>
          <Form.Item label="大纲" >
            <Tree
              checkable
              defaultExpandAll
              onCheck={(keys, e) => { return this.onCheckTree(keys, e); }}
              treeData={tree}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  render = () => {
    return (
      <div style={{ padding: 30, background: '#fff' }}>
        <div style={{ float: 'right', marginBottom: 15 }}>
          <Button
            onClick={() => { return this.setState({ openAssignModal: true }); }}
          >
            分配
          </Button>
          <Divider type="vertical" />
          <Button
            type="primary"
            onClick={() => { return this.setState({ openLibNameModal: true, detail: '' }); }}
          >
            新增
          </Button>
        </div>
        <PaginationTable
          columns={this.renderColumns()}
          dataSource={this.props.list}
        />

        {this.state.openAssignModal && this.renderAssignModal()}
        {this.state.openLibNameModal && this.renderLibNameModal()}
      </div>
    );
  }
}

export {
  reducer,
};
