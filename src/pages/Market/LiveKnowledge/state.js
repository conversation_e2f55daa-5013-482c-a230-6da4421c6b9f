import Configs from '~/consts';
import { Market } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'MARKET_LIVE_KNOWLEDGE/SET_STATE';
const CLEAR_STATE = 'MARKET_LIVE_KNOWLEDGE/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchPartners = () => {
  return async (dispatch) => {
    const searchParams = { partner_type: 'live_helper_user', ...Configs.ALL_PAGE_PARAMS };
    const { items } = await Market.fetchPartners(searchParams);
    dispatch(setState({ partners: items }));
  };
};

export const fetchHelperLibs = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().marketLiveKnowledge;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const { items, total } = await Market.fetchHelperLibs(searchParams);

    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const fetchLibLabels = () => {
  return async (dispatch) => {
    const items = await Market.fetchLibLabels();
    dispatch(setState({ labels: _.values(items) }));
  };
};

export const addHelperLib = (params) => {
  return async (dispatch) => {
    await Market.addHelperLib(params);
    dispatch(fetchHelperLibs());
  };
};

export const updateHelperLib = (params) => {
  return async (dispatch) => {
    await Market.updateHelperLib(params);
    dispatch(fetchHelperLibs());
  };
};

export const delHelperLib = (id) => {
  return async (dispatch) => {
    await Market.delHelperLib(id);
    dispatch(fetchHelperLibs());
  };
};

export const fetchLibsByLabel = (label) => {
  return async () => {
    const items = await Market.fetchLibsByLabel(label);
    const { knowledges } = _.head(_.values(items));
    const tree = [];
    _.map(_.groupBy(knowledges, 'libName'), ((v, k) => {
      const children = v.map((x) => { return { title: x.question, key: x.knowledgeId }; });
      tree.push({ title: k, key: _.head(v).libId, children });
    }));

    return tree;
  };
};

const _getInitState = () => {
  return {
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    list: [],
    labels: [],
    partners: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
