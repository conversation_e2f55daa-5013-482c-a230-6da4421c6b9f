import { Toast } from '~/components';
import { <PERSON><PERSON>, Drawer, Form, Modal, Popconfirm, Select, Table } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class MemberDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    botId: PropTypes.string,
    workflows: PropTypes.array,
    fetchAllMembers: PropTypes.func,
    fetchMemberWorkflows: PropTypes.func,
    addBotMemberWorkflow: PropTypes.func,
    delBotMemberWorkflow: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    data: {},
    members: [],
    items: [],
  }

  componentDidMount = async () => {
    const { items } = await this.props.fetchMemberWorkflows(this.props.botId);
    this.setState({ items });
  }

  onSearch = async (e) => {
    if (!_.isEmpty(e)) {
      const items = await this.props.fetchAllMembers({ name: e });
      this.setState({ members: items.map((x) => { return { label: x.nickname, value: x.unionId }; }) });
    }
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e?.target.value : e;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onDel = async (id) => {
    await this.props.delBotMemberWorkflow(id);
    const { items } = await this.props.fetchMemberWorkflows(this.props.botId);
    this.setState({ items });
  }

  onSubmit = async () => {
    const { botId } = this.props;
    const { unionId, workflowUuid } = this.state?.data;
    if (_.isEmpty(unionId) || _.isEmpty(workflowUuid)) {
      Toast.show('请完善信息!', Toast.Type.WARNING);
      return;
    }

    await this.props.addBotMemberWorkflow({ botId, unionId, workflowUuid });
    const { items } = await this.props.fetchMemberWorkflows(this.props.botId);
    this.setState({ items, open: false, data: {} });
  }

  renderModal = () => {
    const { open, data } = this.state;
    return (
      <Modal
        size="large"
        open={open}
        title="用户工作流"
        onCancel={() => { return this.setState({ open: false, data: {} }); }}
        onOk={this.onSubmit}
      >
        <Form>
          <Form.Item label=" 用 户 ">
            <Select
              showSearch
              value={data?.unionId}
              defaultActiveFirstOption={false}
              showArrow={false}
              filterOption={false}
              onSearch={this.onSearch}
              onChange={(e) => { return this.onChangeValue(e, 'unionId'); }}
              notFoundContent={null}
              options={this.state.members}
            />
          </Form.Item>
          <Form.Item label="工作流">
            <Select
              showSearch
              value={data?.workflowUuid}
              filterOption={(input, option) => { return option.children.includes(input); }}
              onChange={(e) => { return this.onChangeValue(e, 'workflowUuid'); }}
            >
              {
                (this.props.workflows || []).map((x) => {
                  return <Select.Option value={x.uuid}>{x.name}</Select.Option>;
                })
              }
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    );
  };

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '昵称', dataIndex: 'userNickname', key: 'userNickname', align: 'center' },
      { title: '工作流', dataIndex: 'workflowName', key: 'workflowName', align: 'center' },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <Popconfirm title="是否删除?!" onConfirm={() => { return this.onDel(row.id); }}>
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }


  render = () => {
    return (
      <Drawer
        title="用户工作流"
        open={this.props.open}
        placement="right"
        contentWrapperStyle={{ width: '40vw' }}
        onClose={this.props.onClose}
      >
        <Button
          type="primary"
          onClick={() => { return this.setState({ open: true }); }}
          style={{ float: 'right' }}
        >新增
        </Button>
        <Table dataSource={this.state.items} pagination={false} columns={this.renderColumns()} />

        {this.state.open && this.renderModal()}
      </Drawer>
    );
  }
}
