import { InfoCircleOutlined } from '@ant-design/icons';
import { PaginationTable, Toast } from '~/components';
import Utils from '~/pages/Market/Article/Utils';
import {
  Button,
  Checkbox,
  DatePicker,
  Divider,
  Drawer,
  Form,
  Input,
  Modal,
  Popconfirm,
  Popover,
  Select,
  Switch,
  Table,
  Tabs,
  Tag,
} from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

const PLATFORMS = [
  { value: '今日头条', name: '今日头条' },
  { value: 'B站', name: 'B站' },
  { value: '抖音', name: '抖音' },
  { value: '抖音直播', name: '抖音直播' },
  { value: 'YouTube', name: 'YouTube' },
];
const JOBS_STATUS = { pending: '初始化', running: '执行中', done: '成功', failed: '失败' };
const ANALYSIS_MAP = { diggCount: '点赞(阅读)数', commentCount: '评论数', shareCount: '分享数', collectCount: '收藏数' };
const PASSED_MAP = { null: '全部', true: '合格', false: '不合格' };
export default class MaterialDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    materialSources: PropTypes.array,
    usageAssociationId: PropTypes.number,
    createMaterialSource: PropTypes.func,
    delMaterialSource: PropTypes.func,
    fetchMaterials: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    materialSource: {},
    materialSources: [],
    total: 0,
    materials: [],
    filters: {
      fetchStatus: 'done',
      optimizeStatus: 'done',
      passed: 'true',
    },
    pagination: {
      pageIndex: 1,
      pageSize: 10,
      orderBy: 'created_at asc',
    },
  }

  componentDidMount = async () => {
    const { materialSources } = this.props;
    await this.setState({ materialSources });
    await this.fetchMaterials();
  }

  fetchMaterials = async (params = {}) => {
    const { filters, pagination } = this.state;
    const page = {
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const searchParams = {
      ...filters,
      groupId: this.props.usageAssociationId,
      orderBy: page['pagination.orderBy'],
      pagination: {
        skip: (page['pagination.pageIndex'] - 1) * page['pagination.pageSize'],
        limit: page['pagination.pageSize'],
      },
    };

    const { total, items: materials } = await this.props.fetchMaterials(searchParams);
    const newPagination = {
      pageIndex: page['pagination.pageIndex'],
      pageSize: page['pagination.pageSize'],
      orderBy: page['pagination.orderBy'],
    };
    await this.setState({ total, materials, pagination: newPagination });
  }

  handleNumber = (key) => {
    return () => {
      const value = `${this.state.materialSource[key]}`;
      let valueTemp = value;
      if (value.charAt(value.length - 1) === '.' || value === '-') {
        valueTemp = value.slice(0, -1);
      }
      this.onChangeValue(valueTemp.replace(/0*(\d+)/, '$1'), 'materialSource', key);
    };
  }

  onChangeValue = (e, key) => {
    let value;
    if (e?.target) {
      value = e.target.value || e.target.checked;
    } else {
      value = e;
    }
    const materialSource = { ...this.state.materialSource, [key]: value };
    this.setState({ materialSource });
  }

  onChangeFilter = async (e, key) => {
    const value = e?.target ? e?.target?.value : e;
    const filters = { ...this.state.filters, [key]: value };
    await this.setState({ filters });
    await this.fetchMaterials();
  }

  onAnalyzeUrl = async (url) => {
    const materialSource = await Utils.analyzeDyUrl(url);
    this.setState({ materialSource });
  }

  onCreateMaterialSource = async () => {
    const materialSources = _.cloneDeep(this.state.materialSources);
    const data = this.state.materialSource;
    const materialSource = {
      ...data,
      source_url: data.sourceUrl,
      setting: {
        fetch_setting: {
          enable_fetch_history: data.enableFetchHistory,
          history_limit: data.historyLimit,
          enable_fetch_new: data.enableFetchNew,
        },
        review_setting: {
          review_type: data.reviewType,
        },
        optimize_setting: {
          optimize_limit: data.optimizeLimit,
        },
        partition_setting: {
          partition_type: data.partitionType,
          character_id: data.characterId,
        },
      },
      usage: 'chatroom',
      usageAssociationId: this.props.usageAssociationId,
    };
    const item = await this.props.createMaterialSource(materialSource);
    const index = materialSources.findIndex((w) => { return w.id === item.id; });
    if (index > -1) {
      materialSources[index] = item;
    } else {
      materialSources.push(item);
    }
    this.setState({ materialSources, openMaterialSourceModal: false, materialSource: {} });
  }

  onShowMaterialSourceModal = (data) => {
    const materialSource = {
      ...data,
      ...data.setting.optimizeSetting,
      ...data.setting.fetchSetting,
      ...data.setting.reviewSetting,
      ...data.setting.partitionSetting,
    };
    return this.setState({ openMaterialSourceModal: true, materialSource });
  }

  onUpdateMaterialSource = async (data, value, key) => {
    const materialSource = {
      ...data,
      ...data.setting.optimizeSetting,
      ...data.setting.fetchSetting,
      ...data.setting.reviewSetting,
      ...data.setting.partitionSetting,
      [key]: value,
    };
    await this.setState({ materialSource });
    this.onCreateMaterialSource();
  }

  onDelMaterialSource = async (id) => {
    const materialSources = _.cloneDeep(this.props.materialSources);
    await this.props.delMaterialSource(id);
    const index = materialSources.findIndex((w) => { return w.id === id; });
    if (index > -1) {
      materialSources.splice(index, 1);
    }
    this.setState({ materialSources });
  }

  renderStatus = (title, key) => {
    return {
      title,
      dataIndex: key,
      key,
      align: 'center',
      render: (txt) => {
        let tag;
        switch (txt) {
          case 'done':
            tag = <Tag color="success">成功</Tag>;
            break;
          case 'failed':
            tag = <Tag color="error">失败</Tag>;
            break;
          case 'pending':
            tag = <Tag color="warning">初始化</Tag>;
            break;
          case 'running':
            tag = <Tag color="processing">执行中</Tag>;
            break;
          default:
            tag = <Tag color="default">未知状态</Tag>;
        }
        return tag;
      },
    };
  }

  renderMaterialSourceModal = () => {
    const { openMaterialSourceModal, materialSource } = this.state;

    return (
      <Modal
        width={800}
        open={openMaterialSourceModal}
        title={materialSource.id ? '编辑' : '创建'}
        onCancel={() => { this.setState({ openMaterialSourceModal: false, materialSource: {} }); }}
        onOk={this.onCreateMaterialSource}
      >
        <Form labelCol={{ span: 4 }} className="meme-form">
          <Form.Item label="来源链接">
            <Input.Search
              enterButton="解析"
              value={materialSource.sourceUrl}
              onChange={(e) => { return this.onChangeValue(e, 'sourceUrl'); }}
              onSearch={(e) => { return this.onAnalyzeUrl(e); }}
            />
          </Form.Item>
          <Form.Item label="作者">
            <Input
              value={materialSource.author}
              onChange={(e) => { return this.onChangeValue(e, 'author'); }}
            />
          </Form.Item>
          <Form.Item label="平台">
            <Select
              value={materialSource.platform}
              onChange={(e) => { return this.onChangeValue(e, 'platform'); }}
            >
              {PLATFORMS.map((w) => { return <Select.Option value={w.value}>{w?.name}</Select.Option>; })}
            </Select>
          </Form.Item>
          <Form.Item label="简短说明">
            <Input.TextArea
              value={materialSource.description}
              onChange={(e) => { return this.onChangeValue(e, 'description'); }}
            />
          </Form.Item>
          <Form.Item label="优先级">
            <Select
              value={materialSource.priority}
              onChange={(e) => { return this.onChangeValue(e, 'priority'); }}
            >
              <Select.Option value={0}>高</Select.Option>
              <Select.Option value={1}>普通</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item label="状态">
            <Switch
              checkedChildren="启用"
              unCheckedChildren="禁用"
              checked={materialSource.enabled}
              onChange={(e) => { return this.onChangeValue(e, 'enabled'); }}
            />
          </Form.Item>
          <Form.Item label="入库设定">
            <Checkbox
              checked={materialSource.enableFetchHistory}
              onChange={(e) => { return this.onChangeValue(e, 'enableFetchHistory'); }}
            >
              抓取最新的
              <Input
                value={materialSource.historyLimit}
                style={{ width: 100, margin: '0 10px 10px 10px' }}
                onChange={(e) => {
                  const { value: inputValue } = e.target;
                  const reg = /^-?\d*(\.\d*)?$/;
                  if (reg.test(inputValue) || inputValue === '' || inputValue === '-') {
                    this.onChangeValue(inputValue, 'historyLimit');
                  }
                }}
                onBlur={this.handleNumber('historyLimit')}
                maxLength={5}
              />
              条文章
            </Checkbox>
            <Checkbox
              checked={materialSource.enableFetchNew}
              style={{ marginLeft: 0 }}
              onChange={(e) => { return this.onChangeValue(e, 'enableFetchNew'); }}
            >
              新文章监控并自动入库
            </Checkbox>
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  renderMaterialSourcesColumns = () => {
    return [
      { title: '作者', dataIndex: 'author', key: 'author', align: 'center' },
      { title: '平台', dataIndex: 'platform', key: 'platform', align: 'center' },
      { title: '简短说明', dataIndex: 'description', key: 'description', ellipsis: true, width: '30%' },
      {
        title: '来源链接',
        dataIndex: 'sourceUrl',
        key: 'sourceUrl',
        align: 'center',
        render: (txt) => { return <a href={txt} target="_blank" rel="noreferrer">查看</a>; },
      },
      {
        title: '待入库',
        dataIndex: 'pendingMaterialCount',
        key: 'pendingMaterialCount',
        align: 'center',
        render: (txt) => { return txt || '-'; },
      },
      {
        title: '最新文章时间',
        dataIndex: 'lastMaterialPubdate',
        key: 'lastMaterialPubdate',
        align: 'center',
        render: (txt) => {
          return txt ? moment(txt).format('YYYY-MM-DD HH:mm:ss') : '-';
        },
      },
      {
        title: '优先级',
        dataIndex: 'priority',
        key: 'priority',
        align: 'center',
        render: (txt, row) => {
          return (
            <Select
              value={txt}
              style={{ width: 100 }}
              onChange={(e) => { return this.onUpdateMaterialSource(row, e, 'priority'); }}
            >
              <Select.Option value={0}>高</Select.Option>
              <Select.Option value={1}>普通</Select.Option>
            </Select>
          );
        },
      },
      {
        title: '状态',
        dataIndex: 'enabled',
        key: 'enabled',
        align: 'center',
        render: (txt, row) => {
          return (
            <Switch
              checked={txt}
              checkedChildren="启用"
              unCheckedChildren="禁用"
              onChange={(e) => { return this.onUpdateMaterialSource(row, e, 'enabled'); }}
            />
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <a onClick={() => { return this.onShowMaterialSourceModal(row); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm title="是否删除?!" onConfirm={() => { return this.onDelMaterialSource(row.id); }}>
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  renderMaterialColumns = () => {
    const { materialSources } = this.props;
    return [
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        align: 'center',
        ellipsis: true,
        width: '20%',
      },
      {
        title: '作者',
        dataIndex: 'sourceId',
        key: 'sourceId',
        align: 'center',
        render: (txt) => {
          const author = materialSources.find((x) => { return +x.sourceId === +txt; });
          return <a target="_blank" href={author?.sourceUrl} rel="noreferrer">{author?.author}</a>;
        },
      },
      ..._.map(ANALYSIS_MAP, (v, k) => {
        return { title: v, dataIndex: k, key: k, align: 'center', width: 140, sorter: true };
      }),
      {
        title: '发布时间',
        dataIndex: 'pubDate',
        key: 'pubDate',
        align: 'center',
        render: ((txt) => { return moment(txt).format('YYYY-MM-DD HH:mm'); }),
      },
      this.renderStatus('抓取状态', 'fetchStatus'),
      {
        title: '是否合格',
        dataIndex: 'passed',
        key: 'passed',
        align: 'center',
        render: (passed, row) => {
          return (
            <Popover
              content={<div style={{ maxWidth: 220 }}>{row.reviewComment}</div>}
              title="LLM审核意见"
              trigger="click"
            >
              {passed ? <Tag color="success">合格</Tag> : <Tag color="error">不合格</Tag>}
              <InfoCircleOutlined style={{ verticalAlign: 'middle' }} />
            </Popover>
          );
        },
      },
      {
        title: '预览',
        dataIndex: 'passed',
        key: 'passed',
        align: 'center',
        render: (txt, row) => {
          return (
            <Button
              type="link"
              onClick={() => { return this.setState({ openMaterial: true, material: row }); }}
            >查看
            </Button>
          );
        },
      },
    ];
  }

  renderMaterialDrawer = () => {
    const { openMaterial, material } = this.state;
    return (
      <Drawer
        width="40vw"
        open={openMaterial}
        title="详情"
        onClose={() => { this.setState({ openMaterial: false }); }}
        placement="right"
        closable={false}
      >
        <Form labelCol={{ span: 4 }} className="meme-form">
          <Form.Item label="标题">
            {material.title}
          </Form.Item>
          <Form.Item label="生成时间">
            {material.createdAt}
          </Form.Item>
          <Form.Item label="文稿源">
            <a target="_blank" href={material.sourceUrl} rel="noreferrer">{material.sourceUrl}</a>
          </Form.Item>
          <Form.Item label="是否合格">
            {material.passed ? <Tag color="success">合格</Tag> : <Tag color="error">不合格</Tag>}
          </Form.Item>
          <Form.Item label="正文">
            <p style={{ whiteSpace: 'pre-line', maxHeight: '60vh', overflow: 'auto', position: 'relative' }}>
              <Button
                type="link"
                style={{ position: 'absolute', right: 0, top: 0 }}
                onClick={async () => {
                  await navigator.clipboard.writeText(material.content);
                  Toast.show('复制成功', Toast.Type.SUCCESS);
                }}
              >
                复制正文
              </Button>
              {material.content}
            </p>
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderFilter = () => {
    const { startAt, endAt, sourceId } = this.state.filters;
    return (
      <div>
        <DatePicker.RangePicker
          style={{ marginRight: 16 }}
          value={_.isUndefined(startAt) ? [] : [moment(startAt), moment(endAt)]}
          onChange={async (e) => {
            const [startTime, endTime] = e || [];
            await this.onChangeFilter(startTime?.format('YYYY-MM-DDT00:00:00.SSSSSS') || undefined, 'startAt');
            await this.onChangeFilter(endTime?.format('YYYY-MM-DDT23:59:59.SSSSSS') || undefined, 'endAt');
          }}
        />
        <Select
          style={{ width: 260, marginBottom: 16, marginRight: 16 }}
          allowClear
          showSearch
          filterOption={(input, option) => { return option.children.includes(input); }}
          value={sourceId}
          placeholder="请选择文稿源"
          onChange={async (e) => { return this.onChangeFilter(e, 'sourceId'); }}
        >
          {this.props.materialSources.map((w) => {
            return <Select.Option value={w.id}>{w?.author}</Select.Option>;
          })}
        </Select>
        {
          [
            { name: '抓取状态', value: 'fetchStatus', opts: JOBS_STATUS },
            { name: '是否合格', value: 'passed', opts: PASSED_MAP },
          ].map((x) => {
            return (
              <Select
                style={{ width: 120, marginBottom: 16, marginRight: 16 }}
                allowClear
                showSearch={x.showSearch}
                filterOption={(input, option) => { return option.children.includes(input); }}
                value={this.state.filters[x.value]}
                placeholder={x.name}
                onChange={async (e) => { return this.onChangeFilter(e, x.value); }}
              >
                {_.map(x.opts, (v, k) => { return <Select.Option value={k}>{v}</Select.Option>; })}
              </Select>
            );
          })
        }
      </div>
    );
  }

  render = () => {
    const { activeKey, materialSources, openMaterialSourceModal,
      openMaterial, materials, total, pagination } = this.state;
    return (
      <Drawer
        title="内容源"
        open={this.props.open}
        placement="right"
        contentWrapperStyle={{ width: '66vw' }}
        onClose={this.props.onClose}
      >
        <Tabs
          activeKey={activeKey || 'materialSourceGroups'}
          onChange={(e) => { return this.setState({ activeKey: e }); }}
        >
          <Tabs.TabPane tab="文稿源" key="materialSourceGroups">
            <Button
              type="primary"
              onClick={() => { return this.setState({ openMaterialSourceModal: true }); }}
              style={{ float: 'right', marginBottom: 10 }}
            >添加
            </Button>

            <Table columns={this.renderMaterialSourcesColumns()} dataSource={materialSources} />
          </Tabs.TabPane>
          <Tabs.TabPane tab="文稿" key="articles">
            {this.renderFilter()}
            <PaginationTable
              totalDataCount={total}
              dataSource={materials}
              pagination={pagination}
              columns={this.renderMaterialColumns()}
              onPaginationChange={(e) => { return this.fetchMaterials(e); }}
              onTableChange={({ current, pageSize }, filters, { order, field }, { action }) => {
                if (action === 'sort') {
                  const sortParams = {
                    pageIndex: current,
                    pageSize,
                    orderBy: order ? `${_.snakeCase(field)} ${_.trimEnd(order, 'end')}` : 'pub_date desc',
                  };
                  return this.fetchMaterials(sortParams);
                }
                return null;
              }}
            />
          </Tabs.TabPane>
        </Tabs>

        {openMaterialSourceModal && this.renderMaterialSourceModal()}
        {openMaterial && this.renderMaterialDrawer()}
      </Drawer>
    );
  }
}
