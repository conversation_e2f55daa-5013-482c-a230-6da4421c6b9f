import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Toast } from '~/components';
import { But<PERSON>, Divider, Drawer, Form, Input, InputNumber, Popconfirm, Select, TimePicker } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

const DEFAULT_RULE = {
  materialSourceIds: [],
  dayTime: '',
  recentlyHours: '',
  rankType: '',
  count: '',
  filters: [
    { field: 'title', operator: 'include', keywords: [] },
    { field: 'title', operator: 'not_include', keywords: [] },
  ],
};
const ANALYSIS_MAP = {
  ai: 'AI自动选择', digg_count: '点赞(阅读)数', comment_count: '评论数', share_count: '分享数', collect_count: '收藏数',
};

export default class PublishRuleDrawer extends PureComponent {
  static propTypes = {
    rules: PropTypes.array,
    materialSources: PropTypes.array,
    chatroomGroupUuid: PropTypes.string,
    open: PropTypes.bool,
    addRule: PropTypes.func,
    delRule: PropTypes.func,
    updateRule: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    rules: [],
  }

  componentDidMount = () => {
    let { rules } = this.props;
    if (_.isEmpty(rules)) {
      rules = [_.cloneDeep(DEFAULT_RULE)];
    }
    this.setState({ rules });
  }

  onRuleChange = (index, key, value) => {
    const rules = [...this.state.rules];
    rules[index][key] = value;
    this.setState({ rules });
  }

  onRuleFilterChange = (index, e, key) => {
    const rules = [...this.state.rules];
    let filters = rules[index].filters || [];
    if (_.isEmpty(filters)) {
      filters = _.cloneDeep(DEFAULT_RULE.filters);
    }

    const sIndex = filters.findIndex((x) => { return x.operator === key; });
    filters[sIndex].keywords = e;
    this.onRuleChange(index, 'filters', filters);
  }

  onAddRule = (index) => {
    const rules = [...this.state.rules];
    rules.splice(index + 1, 0, _.cloneDeep(DEFAULT_RULE));
    this.setState({ rules });
  }

  onDelRule = async (index) => {
    const rules = [...this.state.rules];
    await this.props.delRule(rules[index].id);
    rules.splice(index, 1);
    this.setState({ rules });
  }

  onSave = async () => {
    const { rules } = this.state;
    const promises = [];
    for (let index = 0; index < rules.length; index++) {
      const rule = rules[index];
      if (_.isUndefined(rule.id)) {
        promises.push(this.props.addRule({ ...rule, chatroomGroupUuid: this.props.chatroomGroupUuid }));
      } else {
        promises.push(this.props.updateRule(rule));
      }
    }

    await Promise.all(promises);
    Toast.show('保存成功', Toast.Type.SUCCESS);
  }

  renderRule = (rule, index) => {
    return (
      <>
        <Form labelCol={{ span: 4 }} >
          <Form.Item label="定时" style={{ marginBottom: 5 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Input.Group compact >
                <Button type="text">每天</Button>
                <TimePicker
                  showNow={false}
                  minuteStep={15}
                  format="HH:mm"
                  value={rule.dayTime ? moment(rule.dayTime, 'HH:mm') : null}
                  onChange={(w, value) => { this.onRuleChange(index, 'dayTime', value); }}
                />
                <Button type="text">发送</Button>
              </Input.Group>
              <div style={{ display: 'flex' }}>
                <Button
                  icon={<PlusOutlined />}
                  shape="circle"
                  style={{ marginRight: 20 }}
                  onClick={() => { this.onAddRule(index); }}
                />
                <Popconfirm title="确定删除吗?" onConfirm={() => { this.onDelRule(index); }}>
                  <Button icon={<DeleteOutlined />} shape="circle" />
                </Popconfirm>
              </div>
            </div>
          </Form.Item>
          <Form.Item label="内容源" style={{ marginBottom: 5 }}>
            <Select
              mode="multiple"
              style={{ width: '100%' }}
              value={rule.materialSourceIds}
              onChange={(value) => { this.onRuleChange(index, 'materialSourceIds', value); }}
            >
              {(this.props.materialSources || []).map((x) => {
                return <Select.Option value={x.id}>{x.author}</Select.Option>;
              })}
            </Select>
          </Form.Item>
          <Form.Item label="文稿">
            <Input.Group compact>
              <Button type="text">最近</Button>
              <InputNumber
                style={{ width: 60 }}
                value={rule.recentlyHours}
                onChange={(value) => { this.onRuleChange(index, 'recentlyHours', value); }}
              />
              <Button type="text">小时内</Button>
              <Select
                style={{ width: 160 }}
                value={rule.rankType}
                onChange={(value) => { this.onRuleChange(index, 'rankType', value); }}
              >
                {
                  _.map(ANALYSIS_MAP, (v, k) => {
                    return <Select.Option value={k}>{v}</Select.Option>;
                  })
                }
              </Select>
              <Button type="text">数最高的</Button>
              <InputNumber
                style={{ width: 60 }}
                value={rule.count}
                onChange={(value) => { this.onRuleChange(index, 'count', value); }}
              />
              <Button type="text">篇文章</Button>
            </Input.Group>
            <Input.Group compact style={{ display: 'flex', margin: '10px 0' }}>
              <div style={{ width: 140, display: 'flex', alignItems: 'center', justifyContent: 'end' }}>
                标题包含关键字:&nbsp;
              </div>
              <Select
                mode="tags"
                open={false}
                value={rule.filters[0]?.keywords || []}
                style={{ width: '100%' }}
                onChange={(e) => { return this.onRuleFilterChange(index, e, 'include'); }}
              />
            </Input.Group>
            <Input.Group compact style={{ display: 'flex' }}>
              <div style={{ width: 140, display: 'flex', alignItems: 'center', justifyContent: 'end' }}>
                标题不包含关键字:&nbsp;
              </div>
              <Select
                mode="tags"
                open={false}
                value={rule.filters[1]?.keywords || []}
                style={{ width: '100%' }}
                onChange={(e) => { return this.onRuleFilterChange(index, e, 'not_include'); }}
              />
            </Input.Group>
          </Form.Item>
        </Form>
        <Divider />
      </>
    );
  }

  render = () => {
    return (
      <Drawer
        title="发布规则"
        open={this.props.open}
        placement="right"
        contentWrapperStyle={{ width: '40vw' }}
        onClose={this.props.onClose}
        extra={<Button type="primary" onClick={this.onSave}>保存</Button>}
      >
        {this.state.rules.map((x, i) => { return this.renderRule(x, i); })}
      </Drawer>
    );
  }
}
