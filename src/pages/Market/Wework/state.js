import Configs from '~/consts';
import { ChatBot, Market, Materials } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'MARKET_PARTNER/SET_STATE';
const CLEAR_STATE = 'MARKET_PARTNER/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchChatbotWorkflows = () => {
  return async (dispatch) => {
    const { items } = await ChatBot.fetchChatbotWorkflows(Configs.ALL_PAGE_PARAMS);
    dispatch(setState({ workflows: items }));
  };
};

export const fetchChatbots = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().marketWework?.chatbot;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const { items, total } = await Market.fetchChatbots(searchParams);
    dispatch(
      setState({
        chatbot: {
          total,
          list: items,
          pagination: {
            pageIndex: searchParams['pagination.pageIndex'],
            pageSize: searchParams['pagination.pageSize'],
            orderBy: searchParams['pagination.orderBy'],
          },
        },
      }),
    );
  };
};

export const delChatbot = (id) => {
  return async (dispatch) => {
    await Market.delChatbot(id);
    dispatch(fetchChatbots());
  };
};

export const addChatbot = (params) => {
  return async (dispatch) => {
    await Market.addChatbot(params);
    dispatch(fetchChatbots());
  };
};

export const updateChatbot = (params) => {
  return async (dispatch) => {
    await Market.updateChatbot(params);
    dispatch(fetchChatbots());
  };
};

export const fetchChatrooms = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().marketWework?.chatroom;
    const searchParams = {
      name: params?.name || undefined,
      groupId: params?.groupId || undefined,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const { items, total } = await Market.fetchChatrooms(searchParams);
    dispatch(
      setState({
        chatroom: {
          total,
          list: items,
          pagination: {
            pageIndex: searchParams['pagination.pageIndex'],
            pageSize: searchParams['pagination.pageSize'],
            orderBy: searchParams['pagination.orderBy'],
          },
        },
      }),
    );
  };
};

export const updateChatroom = (params) => {
  return async (dispatch) => {
    await Market.updateChatroom(params);
    dispatch(fetchChatrooms());
  };
};

export const delChatroom = (id) => {
  return async (dispatch) => {
    await Market.delChatroom(id);
    dispatch(fetchChatrooms());
  };
};

export const fetchBotsInRoom = (params) => {
  return async () => {
    const items = await Market.fetchBotsInRoom(params);
    return _.map(items, ((v) => { return v; }));
  };
};

export const fetchAllMembers = (params) => {
  return async () => {
    const { items } = await Market.fetchAllMembers({ ...params, platform: 'meme' });
    return items;
  };
};

export const fetchMemberWorkflows = (botId) => {
  return async () => {
    const items = await Market.fetchBotMemberWorkflows({ botId, ...Configs.ALL_PAGE_PARAMS });
    return items;
  };
};

export const addBotMemberWorkflow = (params) => {
  return async () => {
    await Market.addBotMemberWorkflow(params);
  };
};

export const delBotMemberWorkflow = (id) => {
  return async () => {
    await Market.delBotMemberWorkflow(id);
  };
};

export const fetchChatroomGroups = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().marketWework?.group;
    const searchParams = {
      name: params?.name || undefined,
      groupId: params?.name || undefined,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const { items, total } = await Market.fetchChatroomGroups(searchParams);
    dispatch(
      setState({
        group: {
          total,
          list: items,
          pagination: {
            pageIndex: searchParams['pagination.pageIndex'],
            pageSize: searchParams['pagination.pageSize'],
            orderBy: searchParams['pagination.orderBy'],
          },
        },
      }),
    );
  };
};

export const addChatroomGroup = (params) => {
  return async (dispatch) => {
    await Market.addChatroomGroup(params);
    dispatch(fetchChatroomGroups());
  };
};

export const updateChatroomGroup = (params) => {
  return async (dispatch) => {
    await Market.updateChatroomGroup(params);
    dispatch(fetchChatroomGroups());
  };
};

export const delChatroomGroup = (id) => {
  return async (dispatch) => {
    await Market.delChatroomGroup(id);
    dispatch(fetchChatroomGroups());
  };
};

export const fetchChatroomGroupPublishRules = (id) => {
  return async () => {
    const items = await Market.fetchChatroomGroupPublishRules(id);
    return _.values(items);
  };
};

export const addChatroomGroupPublishRule = (params) => {
  return async () => {
    await Market.addChatroomGroupPublishRule(params);
  };
};

export const updateChatroomGroupPublishRule = (params) => {
  return async () => {
    await Market.updateChatroomGroupPublishRule(params);
  };
};

export const delChatroomGroupPublishRule = (id) => {
  return async () => {
    await Market.delChatroomGroupPublishRule(id);
  };
};

export const fetchMaterialSources = (groupId) => {
  return async () => {
    const { items } = await Materials.fetchmaterialSources({
      ...Configs.ALL_PAGE_PARAMS,
      usage: 'chatroom',
      usageAssociationId: groupId,
    });
    return items;
  };
};

export const createMaterialSource = (params) => {
  return async () => {
    const item = await Materials.creatematerialSource(params);
    return item;
  };
};

export const delMaterialSource = (id) => {
  return async () => {
    await Materials.deletematerialSource(id);
  };
};

export const fetchMaterials = (params = {}) => {
  return async () => {
    const { items, total } = await Materials.searchMaterialsByGroup(params);
    return { items, total };
  };
};

const _getInitState = () => {
  return {
    chatrooms: [],
    chatroom: {
      total: 0,
      pagination: {
        pageIndex: 1,
        pageSize: 20,
        orderBy: 'createdAt asc',
      },
      list: [],
    },
    chatbot: {
      total: 0,
      pagination: {
        pageIndex: 1,
        pageSize: 20,
        orderBy: 'createdAt asc',
      },
      list: [],
    },
    group: {
      total: 0,
      pagination: {
        pageIndex: 1,
        pageSize: 20,
        orderBy: 'createdAt asc',
      },
      list: [],
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
