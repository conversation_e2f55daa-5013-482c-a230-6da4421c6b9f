import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { FilterBar, PaginationTable } from '~/components';
import ModelSelect from '~/pages/Playground/Workflow/components/ModelSelect';
import {
  Button,
  Divider,
  Drawer,
  Form,
  Image,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Select,
  Switch,
  Table,
  Tabs,
  Tag,
  TimePicker,
} from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import MaterialDrawer from './components/MaterialDrawer';
import MemberDrawer from './components/MemberDrawer';
import PublishRuleDrawer from './components/PublishRuleDrawer';
import reducer, * as actions from './state';

const ARTICLE_SETTING_MAP = {
  defaultSigmoidWeight: '合适的文章字数',
  defaultSigmoidScale: '越大变化越平缓',
  defaultTimeDecay: '越大时间影响越大',
  recencyWeight: '时间相关性权重',
  articleScoreWeight: '文章分数权重',
  articleCommentScoreWeight: '文章评论权重',
  contentLenWeight: '文章长度权重',
};

const DEFAULT_ARTICLE_SETTING = {
  defaultSigmoidWeight: 2000,
  defaultSigmoidScale: 200,
  defaultTimeDecay: 0.005,
  recencyWeight: 0.5,
  articleScoreWeight: 0.5,
  articleCommentScoreWeight: 0.5,
  contentLenWeight: 0.3,
  // eslint-disable-next-line max-len
  systemMessage: '命令：你是一个专业的文章审核员。\n\n你需要判断是否选取文章，你选取的标准是：\n1. 国际宏观经济资讯\n2. 国内宏观经济资讯\n3. 国内外货币政策和宏观调控政策\n4. 你所在的国家是中国大陆，国内指中国国内，国际指中国之外的全球\n5. 不要出现广告内容\n\n你会非常多疑，尽可能排除更多不需要选取的文章。\n\n请判断是否选取上文给定的文章。\n输出选取/否，然后结束', // eslint-disable-next-line max-len
  userMessage: '例子：\n1. 美联储官员放鸽，美股盘中转涨，中东冲突震动，原油涨超4%创半年最大涨幅\n输出：选取\n2. 速冻食品惊呼：“糟了，我成替身了”\n输出：否\n3. 预制菜风波对配餐企业和速冻食品市场的影响\n输出：否\n4. 巨震！人民币 扛住了\n输出：选取\n\n文章摘要：{title}\n{content}\n输出：\n',
  modelName: 'gpt-3.5-turbo-0613',
};
@connect(
  (state) => {
    return state.marketWework;
  },
  actions,
)
export default class MarketWework extends Component {
  static propTypes = {
    group: PropTypes.object,
    chatbot: PropTypes.object,
    chatroom: PropTypes.object,
    workflows: PropTypes.array,
    fetchChatroomGroups: PropTypes.func.isRequired,
    addChatroomGroup: PropTypes.func.isRequired,
    updateChatroomGroup: PropTypes.func.isRequired,
    delChatroomGroup: PropTypes.func.isRequired,
    fetchChatbots: PropTypes.func.isRequired,
    delChatbot: PropTypes.func.isRequired,
    addChatbot: PropTypes.func.isRequired,
    updateChatbot: PropTypes.func.isRequired,
    fetchChatrooms: PropTypes.func.isRequired,
    updateChatroom: PropTypes.func.isRequired,
    delChatroom: PropTypes.func.isRequired,
    fetchBotsInRoom: PropTypes.func.isRequired,
    fetchAllMembers: PropTypes.func.isRequired,
    fetchChatbotWorkflows: PropTypes.func.isRequired,
    fetchMemberWorkflows: PropTypes.func.isRequired,
    addBotMemberWorkflow: PropTypes.func.isRequired,
    delBotMemberWorkflow: PropTypes.func.isRequired,
    fetchChatroomGroupPublishRules: PropTypes.func.isRequired,
    addChatroomGroupPublishRule: PropTypes.func.isRequired,
    updateChatroomGroupPublishRule: PropTypes.func.isRequired,
    delChatroomGroupPublishRule: PropTypes.func.isRequired,
    fetchMaterialSources: PropTypes.func.isRequired,
    createMaterialSource: PropTypes.func.isRequired,
    delMaterialSource: PropTypes.func.isRequired,
    fetchMaterials: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    rules: [],
    roomBots: [],
    chatbot: {},
    chatgroup: {},
    openMaterial: false,
  }


  constructor(props) {
    super(props);
    this.DEFAULT_ARTICLE_SETTING = _.cloneDeep(DEFAULT_ARTICLE_SETTING);
  }

  componentDidMount = async () => {
    this.props.fetchChatbotWorkflows();
    await this.props.fetchChatbots();
    await this.props.fetchChatrooms();
    await this.props.fetchChatroomGroups();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeChatroomValue = (e, key) => {
    const value = e?.target ? e?.target.value : e;
    this.setState({ chatroom: { ...this.state.chatroom, [key]: value } });
  }

  onChangeChatbotValue = (e, key) => {
    const value = e?.target ? e?.target.value : e;
    this.setState({ chatbot: { ...this.state.chatbot, [key]: value } });
  }

  onChangeChatgroupValue = (e, key, subKey = undefined) => {
    const value = e?.target ? e?.target.value : e;
    const chatgroup = _.cloneDeep(this.state.chatgroup);
    if (_.isUndefined(subKey)) {
      if (key === 'name') {
        chatgroup[key] = value;
      } else {
        chatgroup.setting = chatgroup.setting || {};
        chatgroup.setting[key] = value;
      }
    } else {
      chatgroup[subKey] = chatgroup[subKey] || this[`DEFAULT_${_.toUpper(_.snakeCase(subKey))}`];
      chatgroup[subKey][key] = value;
    }

    this.setState({ chatgroup });
  }

  onShowMember = async (row) => {
    this.setState({ memberObj: { open: true, botId: row.botId } });
  }

  onShowChatroomModal = async (row) => {
    const bots = await this.props.fetchBotsInRoom({ roomId: row.roomId });
    this.setState({ chatroom: row, roomBots: bots, openChatroom: true });
  }

  onShowChatbotModal = (row) => {
    let params = row;
    if (_.startsWith(row?.usage, 'workflow')) {
      const [usage, flowId] = (row?.usage || '').split('@');
      params = { ...params, usage, flowId };
    }

    this.setState({ openChatbot: true, chatbot: params });
  }

  onSubmitChatroom = async () => {
    await this.props.updateChatroom(this.state.chatroom);
    this.setState({ chatroom: {}, roomBots: [], openChatroom: false });
  }

  onSubmitChatbot = async () => {
    if (_.isUndefined(this.state.chatbot?.id)) {
      await this.props.addChatbot(this.state.chatbot);
    } else {
      await this.props.updateChatbot(this.state.chatbot);
    }
    this.setState({ chatbot: {}, openChatbot: false });
  }

  onSubmitGroup = async () => {
    const { chatgroup } = this.state;
    if (_.isEmpty(chatgroup?.articleSetting)) {
      chatgroup.articleSetting = _.cloneDeep(DEFAULT_ARTICLE_SETTING);
    }
    if (_.isUndefined(chatgroup?.id)) {
      await this.props.addChatroomGroup(chatgroup);
    } else {
      await this.props.updateChatroomGroup(chatgroup);
    }
    this.setState({ chatgroup: {}, openGroup: false });
  }

  onShowRuleDrawer = async (row) => {
    let rules = await this.props.fetchChatroomGroupPublishRules(row.uuid);
    const materialSources = await this.props.fetchMaterialSources(row.id);
    rules = _.orderBy(rules, ['dayTime'], ['asc']);
    this.setState({ openGroupRule: true, rules, materialSources, chatroomGroupUuid: row.uuid });
  }

  onShowMaterialDrawer = async (row) => {
    const materialSources = await this.props.fetchMaterialSources(row.id);
    this.setState({ openMaterial: true, materialSources, usageAssociationId: row.id });
  }

  renderChatroomModal = () => {
    const { openChatroom, chatroom, roomBots } = this.state;

    return (
      <Modal
        open={openChatroom}
        title={`编辑-${chatroom?.name}`}
        onCancel={() => { return this.setState({ openChatroom: false, chatroom: {} }); }}
        onOk={this.onSubmitChatroom}
      >
        <Form labelCol={{ span: 4 }}>
          <Form.Item label="群名">{chatroom?.name || '-'}</Form.Item>
          <Form.Item label="是否可入群">
            <Switch
              checked={chatroom?.openToJoin}
              onChange={(e) => { return this.onChangeChatroomValue(e, 'openToJoin'); }}
            />
          </Form.Item>
          <Form.Item label="群机器人">
            <Select
              value={chatroom?.rpaBotId}
              onChange={(e) => { return this.onChangeChatroomValue(e, 'rpaBotId'); }}
            >
              {
                (roomBots || []).map((x) => {
                  return <Select.Option value={x.botId}>{x.botName}({x.botWeixin})</Select.Option>;
                })
              }
            </Select>
          </Form.Item>
          <Form.Item label="工作流">
            <Select
              showSearch
              filterOption={(input, option) => { return option.children.includes(input); }}
              value={chatroom?.workflowUuid}
              onChange={(e) => { return this.onChangeChatroomValue(e, 'workflowUuid'); }}
            >
              {
                (this.props.workflows || []).map((x) => {
                  return <Select.Option value={x.uuid}>{x.name}</Select.Option>;
                })
              }
            </Select>
          </Form.Item>
          <Form.Item label="分组">
            <Select
              value={chatroom?.groupId}
              placeholder="请选择分组"
              onChange={(e) => { return this.onChangeChatroomValue(e, 'groupId'); }}
            >
              {
                (this.props.group?.list || []).map((x) => {
                  return <Select.Option value={x.id}>{x.name}</Select.Option>;
                })
              }
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  renderChatbotModal = () => {
    const { openChatbot, chatbot } = this.state;
    const keys = {
      botId: 'BOT_ID',
      botWxid: 'BOT_WXID',
      botWeixin: 'BOT_WEIXIN',
      botName: 'BOT_NAME',
    };

    return (
      <Modal
        open={openChatbot}
        title="新增Bot"
        onCancel={() => { return this.setState({ openChatbot: false, chatbot: {} }); }}
        onOk={this.onSubmitChatbot}
      >
        <Form labelCol={{ span: 5 }}>
          {
            _.map(keys, (v, k) => {
              return (
                <Form.Item label={v}>
                  <Input value={chatbot[k]} onChange={(e) => { return this.onChangeChatbotValue(e, k); }} />
                </Form.Item>
              );
            })
          }
          <Form.Item label="用途">
            <Select value={chatbot?.usage} onChange={(e) => { return this.onChangeChatbotValue(e, 'usage'); }}>
              <Select.Option value="customer_service">客服</Select.Option>
              <Select.Option value="premise_course">反馈总结</Select.Option>
              <Select.Option value="workflow">工作流</Select.Option>
            </Select>
          </Form.Item>
          {
            chatbot?.usage === 'workflow' &&
            <Form.Item label="工作流">
              <Select
                showSearch
                filterOption={(input, option) => { return option.children.includes(input); }}
                value={chatbot?.flowId}
                onChange={(e) => { return this.onChangeChatbotValue(e, 'flowId'); }}
              >
                {
                  (this.props.workflows || []).map((x) => {
                    return <Select.Option value={x.uuid}>{x.name}</Select.Option>;
                  })
                }
              </Select>
            </Form.Item>
          }
        </Form>
      </Modal>
    );
  }

  renderGroupModal = () => {
    const { workflows } = this.props;
    const { chatgroup, openGroup } = this.state;
    const { name, setting, articleSetting } = chatgroup;

    return (
      <Drawer
        open={openGroup}
        title={`${_.isEmpty(chatgroup) ? '新增' : '编辑'}分组`}
        width={800}
        className="article-drawer"
        onClose={() => { return this.setState({ openGroup: false }); }}
        extra={<Button type="primary" onClick={() => { this.onSubmitGroup(); }}>保存</Button>}
      >
        <Form labelCol={{ span: 4 }} className="article-drawer-form">
          <Form.Item label="名称">
            <Input value={name} onChange={(e) => { return this.onChangeChatgroupValue(e, 'name'); }} />
          </Form.Item>
          {
            _.map(ARTICLE_SETTING_MAP, (v, k) => {
              return (
                <Form.Item label={v}>
                  <InputNumber
                    value={articleSetting?.[k] || DEFAULT_ARTICLE_SETTING[k]}
                    onChange={(e) => { return this.onChangeChatgroupValue(e, k, 'articleSetting'); }}
                  />
                </Form.Item>
              );
            })
          }
          {
            _.map({ systemMessage: '系统提示语', userMessage: '用户提示语' }, (v, k) => {
              return (
                <Form.Item label={v}>
                  <Input.TextArea
                    autoSize={{ minRows: 5 }}
                    value={articleSetting?.[k] || DEFAULT_ARTICLE_SETTING[k]}
                    onChange={(e) => { return this.onChangeChatgroupValue(e, k, 'articleSetting'); }}
                  />
                </Form.Item>
              );
            })
          }
          <Form.Item label="模型">
            <ModelSelect
              canAdd={false}
              value={articleSetting?.modelName || DEFAULT_ARTICLE_SETTING.modelName}
              onChange={(e) => { return this.onChangeChatgroupValue(e, 'modelName', 'articleSetting'); }}
            />
          </Form.Item>
          <Form.Item label="总结工作流">
            <Select
              value={setting?.summaryWorkflowUuid}
              onChange={(e) => { return this.onChangeChatgroupValue(e, 'summaryWorkflowUuid'); }}
            >
              {(workflows || []).map((x) => { return <Select.Option value={x.uuid}>{x.name}</Select.Option>; })}
            </Select>
          </Form.Item>
          <Form.Item label="备注">
            该工作流可以使用的工作流常量：<br />
            material_id: 文章的ID<br />
            material_title：文章的标题<br />
            material_content：文章的正文<br />
            published_material_uuid：该文章发布后的唯一ID，H5可以通过该ID加载总结的文字
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderGroupRuleModal = () => {
    return (
      <Modal
        width={720}
        title="发布规则"
        open={this.state.openGroupRule}
        onCancel={() => { return this.setState({ openGroupRule: false }); }}
        onOk={this.onSubmitGroupRule}
      >
        <Form labelCol={{ span: 4 }}>
          <Form.Item label="定时" style={{ marginBottom: 5 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Input.Group compact >
                <Button size="small" type="text">每天</Button>
                <TimePicker showNow={false} minuteStep={15} format="HH:mm" size="small" />
                <Button size="small" type="text">发送</Button>
              </Input.Group>
              <div style={{ display: 'flex' }}>
                <Button size="small" icon={<PlusOutlined />} shape="circle" style={{ marginRight: 20 }} />
                <Button size="small" icon={<DeleteOutlined />} shape="circle" />
              </div>
            </div>
          </Form.Item>
          <Form.Item label="内容源" style={{ marginBottom: 5 }}>
            <Select size="small" />
          </Form.Item>
          <Form.Item label="文稿">
            <Input.Group compact >
              <Button size="small" type="text">最近</Button>
              <InputNumber size="small" style={{ width: 60 }} />
              <Button size="small" type="text">小时内</Button>
              <Select size="small" style={{ width: 80 }}>
                <Select.Option>点赞</Select.Option>
                <Select.Option>阅读</Select.Option>
              </Select>
              <Button size="small" type="text">数最高的</Button>
              <InputNumber size="small" style={{ width: 60 }} />
              <Button size="small" type="text">篇文章</Button>
            </Input.Group>
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '群名', dataIndex: 'name', key: 'name', align: 'center' },
      {
        title: '分组',
        dataIndex: 'groupId',
        key: 'groupId',
        align: 'center',
        render: () => { return '-'; },
      },
      { title: '群主', dataIndex: 'owner', key: 'owner', align: 'center' },
      {
        title: '是否可入群',
        dataIndex: 'openToJoin',
        key: 'openToJoin',
        align: 'center',
        render: (txt) => { return txt ? <Tag color="green">是</Tag> : <Tag color="red">否</Tag>; },
      },
      {
        title: '群二维码',
        dataIndex: 'qrCode',
        key: 'qrCode',
        align: 'center',
        render: (txt) => { return txt ? <Image width={40} height={40} src={txt} /> : '-'; },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <Popconfirm
                title="是否删除?!"
                onConfirm={() => { return this.props.delChatroom(row.id); }}
              >
                <a>删除</a>
              </Popconfirm>
              <Divider type="vertical" />
              <a onClick={() => { return this.onShowChatroomModal(row); }}>编辑</a>
            </>
          );
        },
      },
    ];
  };

  renderBotColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '昵称', dataIndex: 'botName', key: 'botName', align: 'center' },
      { title: 'WXID', dataIndex: 'botWeixin', key: 'botWeixin', align: 'center' },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <a onClick={() => { return this.onShowChatbotModal(row); }}>编辑</a>
              <Divider type="vertical" />
              <a onClick={() => { return this.onShowMember(row); }}>用户工作流</a>
              <Divider type="vertical" />
              <Popconfirm title="是否删除?!" onConfirm={() => { return this.props.delChatbot(row.id); }}>
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  renderGroupColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '名称', dataIndex: 'name', key: 'name', align: 'center' },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <a onClick={() => { return this.onShowMaterialDrawer(row); }}>内容源</a>
              <Divider type="vertical" />
              <a onClick={() => { return this.onShowRuleDrawer(row); }}>发布规则</a>
              <Divider type="vertical" />
              {/* <a onClick={() => { }} disabled>发布记录</a>
              <Divider type="vertical" /> */}
              <a onClick={() => { return this.setState({ openGroup: true, chatgroup: row }); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm title="是否删除?!" onConfirm={() => { return this.props.delChatroomGroup(row.id); }}>
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  renderChatroomSelects = () => {
    const { list } = this.props.group;
    return (
      <Select
        allowClear
        value={this.state.groupId}
        style={{ width: 160, marginBottom: 16 }}
        placeholder="请选择"
        onChange={(e) => { return this.setState({ groupId: e }); }}
      >
        {(list || []).map((x) => { return <Select.Option value={x.id}>{x.name}</Select.Option>; })}
      </Select>
    );
  }

  render = () => {
    const { group, chatroom, chatbot } = this.props;
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <Tabs>
          <Tabs.TabPane key="chatroom-group" tab="社群分组">
            <Button
              style={{ float: 'right' }}
              type="primary"
              onClick={() => { return this.setState({ openGroup: true }); }}
            >
              新增分组
            </Button>
            <PaginationTable
              dataSource={group.list}
              pagination={group?.pagination}
              totalDataCount={group?.total}
              columns={this.renderGroupColumns()}
              onPaginationChange={(e) => { return this.props.fetchChatrooms(e); }}
            />
          </Tabs.TabPane>
          <Tabs.TabPane key="chatroom" tab="社群">
            <FilterBar
              canAdd={false}
              placeholder="请输入关键字"
              searchKeyWords={this.state.name}
              renderSelects={this.renderChatroomSelects}
              onChange={(e) => { return this.setState({ name: e }); }}
              onSearch={() => {
                return this.props.fetchChatrooms({
                  name: this.state.name, groupId: this.state.groupId, pageIndex: 1,
                });
              }}
            />
            <PaginationTable
              size="small"
              dataSource={chatroom.list}
              pagination={chatroom?.pagination}
              totalDataCount={chatroom?.total}
              columns={this.renderColumns()}
              onPaginationChange={(e) => { return this.props.fetchChatrooms(e); }}
            />
          </Tabs.TabPane>
          <Tabs.TabPane key="chatbot" tab="机器人">
            <Button
              type="primary"
              style={{ float: 'right', marginBottom: 10 }}
              onClick={() => { return this.setState({ openChatbot: true }); }}
            >
              新增
            </Button>
            <Table dataSource={chatbot.list} columns={this.renderBotColumns()} />
          </Tabs.TabPane>
        </Tabs>

        {this.state.openGroup && this.renderGroupModal()}
        {/* {this.state.openGroupRule && this.renderGroupRuleModal()} */}
        {this.state.openChatroom && this.renderChatroomModal()}
        {this.state.openChatbot && this.renderChatbotModal()}
        {
          this.state.memberObj?.open &&
          <MemberDrawer
            {...this.state.memberObj}
            workflows={this.props.workflows}
            fetchAllMembers={this.props.fetchAllMembers}
            fetchMemberWorkflows={this.props.fetchMemberWorkflows}
            addBotMemberWorkflow={this.props.addBotMemberWorkflow}
            delBotMemberWorkflow={this.props.delBotMemberWorkflow}
            onClose={() => { return this.setState({ memberObj: {} }); }}
          />
        }
        {
          this.state.openGroupRule &&
          <PublishRuleDrawer
            open={this.state.openGroupRule}
            rules={this.state.rules}
            materialSources={this.state.materialSources}
            chatroomGroupUuid={this.state.chatroomGroupUuid}
            addRule={this.props.addChatroomGroupPublishRule}
            delRule={this.props.delChatroomGroupPublishRule}
            updateRule={this.props.updateChatroomGroupPublishRule}
            onClose={() => { return this.setState({ openGroupRule: false }); }}
          />
        }
        {
          this.state.openMaterial &&
          <MaterialDrawer
            open={this.state.openMaterial}
            materialSources={this.state.materialSources}
            usageAssociationId={this.state.usageAssociationId}
            createMaterialSource={this.props.createMaterialSource}
            delMaterialSource={this.props.delMaterialSource}
            fetchMaterials={this.props.fetchMaterials}
            onClose={() => { return this.setState({ openMaterial: false }); }}
          />
        }
      </div>
    );
  };
}

export {
  reducer,
};
