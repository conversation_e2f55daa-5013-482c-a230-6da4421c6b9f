import { Button, Drawer, Form, Input, InputNumber, Radio, Typography } from 'antd';
import PropTypes from 'prop-types';
import React, { useState } from 'react';

import { minuteToSecond, secondToMinute } from '../Utils';

export default function CreateInviteCodeDrawer({ open, onClose, onCreate }) {
  const [title, setTitle] = useState('');
  const [limitsSetting, setLimitsSetting] = useState({ llmToken: 0, digitalPersonDuration: 0, autoClipDuration: 0 });
  const [expiredDaysLimit, setExpiredDayLimit] = useState(true);
  const [expiredDays, setExpiredDay] = useState(30);
  const [accountCountLimit, setAccountCountLimit] = useState(true);
  const [maxAccountCount, setMaxAccountCount] = useState(10);


  const handleTitleChange = (e) => {
    const value = e?.target ? e.target.value : e;
    setTitle(value);
  };

  const handleAccountCountRadioChange = (e) => {
    const value = e?.target ? e.target.value : e;
    setAccountCountLimit(value === 'limited');
  };

  const handleMaxAccountCountChange = (e) => {
    const value = e?.target ? e.target.value : e;
    setMaxAccountCount(value);
  };

  const handleExpiredDayRadioChange = (e) => {
    const value = e?.target ? e.target.value : e;
    setExpiredDayLimit(value === 'limited');
  };

  const handleExpiredDayChange = (e) => {
    const value = e?.target ? e.target.value : e;
    setExpiredDay(value);
  };

  const handleInitialTokenChange = (e) => {
    const value = e?.target ? e.target.value : e;
    setLimitsSetting({ ...limitsSetting, llmToken: value * 10000 });
  };

  const handleDigitalPersonDurationChange = (e) => {
    const value = e?.target ? e.target.value : e;
    setLimitsSetting({ ...limitsSetting, digitalPersonDuration: minuteToSecond(value) });
  };

  const handleAutoClipDurationChange = (e) => {
    const value = e?.target ? e.target.value : e;
    setLimitsSetting({ ...limitsSetting, autoClipDuration: minuteToSecond(value) });
  };

  const onSubmit = async () => {
    await onCreate({
      title,
      limitsSetting,
      expiredDays: expiredDaysLimit ? expiredDays : -1,
      maxAccountCount: accountCountLimit ? maxAccountCount : -1,
    });
    onClose();
  };

  return (
    <Drawer
      open={open}
      title="创建邀请码"
      onClose={onClose}
      width="40vw"
      extra={<Button type="primary" onClick={() => { return onSubmit(); }}>保存</Button>}
    >
      <Form>
        <Typography.Title level={5}>基本信息</Typography.Title>
        <Form.Item label="邀请码标题" name="title">
          <Input value={title} onChange={handleTitleChange} />
        </Form.Item>
        <Form.Item label="成员数量" name="maxAccountCount">
          <Radio.Group
            value={accountCountLimit ? 'limited' : 'unlimited'}
            onChange={handleAccountCountRadioChange}
          >
            <Radio value="limited">
              自定义：<InputNumber min={1} value={maxAccountCount} onChange={handleMaxAccountCountChange} />
            </Radio>
            <Radio value="unlimited">无限制</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="有效期" name="expiredDays">
          <Radio.Group value={expiredDaysLimit ? 'limited' : 'unlimited'} onChange={handleExpiredDayRadioChange}>
            <Radio value="limited">
              自定义：<InputNumber min={1} value={expiredDays} onChange={handleExpiredDayChange} addonAfter="天" />
            </Radio>
            <Radio value="unlimited">无限制</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="初始Token" name="initialToken">
          <InputNumber
            min={0}
            defaultValue={0}
            value={limitsSetting.llmToken / 10000}
            onChange={handleInitialTokenChange}
            addonAfter="万"
          />
        </Form.Item>
        <Typography.Title level={5}>权益</Typography.Title>
        <Form.Item label="数字人时长" name="digitalPersonDuration">
          <InputNumber
            min={0}
            defaultValue={0}
            value={secondToMinute(limitsSetting.digitalPersonDuration)}
            onChange={handleDigitalPersonDurationChange}
            addonAfter="分钟"
          />
        </Form.Item>
        <Form.Item label="自动剪辑时长" name="autoClipDuration">
          <InputNumber
            min={0}
            defaultValue={0}
            value={secondToMinute(limitsSetting.autoClipDuration)}
            onChange={handleAutoClipDurationChange}
            addonAfter="分钟"
          />
        </Form.Item>
      </Form>
    </Drawer>
  );
}

CreateInviteCodeDrawer.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onCreate: PropTypes.func.isRequired,
};
