import { CopyOutlined } from '@ant-design/icons';
import { FilterBar, PaginationTable, Toast } from '~/components';
import { Button, Drawer, Form, Input, InputNumber, Popconfirm, Space, Tag, Tooltip, Typography } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';

import { minuteToSecond, secondToMinute } from '../Utils';

function CreateRedeemCodeDrawer({ open, onClose, onCreate }) {
  const [title, setTitle] = useState('');
  const [quotaData, setQuotaData] = useState({ llmToken: 0, digitalPersonDuration: 0, autoClipDuration: 0 });


  const handleTitleChange = (e) => {
    const value = e?.target ? e.target.value : e;
    setTitle(value);
  };


  const handleInitialTokenChange = (e) => {
    const value = e?.target ? e.target.value : e;
    setQuotaData({ ...quotaData, llmToken: value * 10000 });
  };

  const handleDigitalPersonDurationChange = (e) => {
    const value = e?.target ? e.target.value : e;
    setQuotaData({ ...quotaData, digitalPersonDuration: minuteToSecond(value) });
  };

  const handleAutoClipDurationChange = (e) => {
    const value = e?.target ? e.target.value : e;
    setQuotaData({ ...quotaData, autoClipDuration: minuteToSecond(value) });
  };

  const onSubmit = async () => {
    await onCreate({ title, quotaData });
    onClose();
  };

  return (
    <Drawer
      open={open}
      title="创建兑换码"
      onClose={onClose}
      width="40vw"
      extra={<Button type="primary" onClick={() => { return onSubmit(); }}>保存</Button>}
    >
      <Form>
        <Typography.Title level={5}>基本信息</Typography.Title>
        <Form.Item label="兑换码标题" name="title">
          <Input value={title} onChange={handleTitleChange} />
        </Form.Item>
        <Form.Item label="Token" name="initialToken">
          <InputNumber
            min={0}
            defaultValue={0}
            value={quotaData.llmToken / 10000}
            onChange={handleInitialTokenChange}
            addonAfter="万"
          />
        </Form.Item>
        <Typography.Title level={5}>权益</Typography.Title>
        <Form.Item label="数字人时长" name="digitalPersonDuration">
          <InputNumber
            min={0}
            defaultValue={0}
            value={secondToMinute(quotaData.digitalPersonDuration)}
            onChange={handleDigitalPersonDurationChange}
            addonAfter="分钟"
          />
        </Form.Item>
        <Form.Item label="自动剪辑时长" name="autoClipDuration">
          <InputNumber
            min={0}
            defaultValue={0}
            value={secondToMinute(quotaData.autoClipDuration)}
            onChange={handleAutoClipDurationChange}
            addonAfter="分钟"
          />
        </Form.Item>
      </Form>
    </Drawer>
  );
}

export default function RedeemCodeDrawer({ partner, open, onClose, fetchItems, createItem, deleteItem }) {
  const [keywords, setKeywords] = useState('');
  const [total, setTotal] = useState(0);
  const [items, setItems] = useState([]);
  const [pagination, setPagination] = useState({ pageIndex: 1, pageSize: 20 });
  const [createOpen, setCreateOpen] = useState(false);


  const onFetchItems = async () => {
    const result = await fetchItems(partner.partnerId, {
      title: keywords,
      'pagination.pageIndex': pagination.pageIndex,
      'pagination.pageSize': pagination.pageSize,
    });
    setItems(result.items);
    setTotal(result.total);
  };

  const onSearch = async () => {
    await setPagination({ ...pagination, pageIndex: 1 });
  };

  useEffect(() => {
    onFetchItems();
  }, [pagination]);

  const onPaginationChange = async ({ pageIndex, pageSize }) => {
    await setPagination({ pageIndex, pageSize });
  };

  const onDeleteItem = async (id) => {
    await deleteItem(id);
    onFetchItems();
  };

  const onCreateItem = async (item) => {
    await createItem(partner.partnerId, item);
    onFetchItems();
  };

  const renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '标题', dataIndex: 'title', key: 'title', width: 70 },
      {
        title: '兑换码',
        dataIndex: 'code',
        key: 'code',
        align: 'center',
        render: (txt) => {
          return (
            <Input
              value={txt}
              size="small"
              addonAfter={<CopyOutlined onClick={
                async () => {
                  await navigator.clipboard.writeText(txt);
                  Toast.show('复制成功', Toast.Type.SUCCESS);
                }}
              />}
            />
          );
        },
      },
      {
        title: '权益',
        dataIndex: 'quotaData',
        key: 'quotaData',
        align: 'center',
        render: (__, row) => {
          const { llmToken, digitalPersonDuration, autoClipDuration } = row.quotaData;
          return (
            <Space direction="vertical">
              <Typography.Text>LLM Token: {(llmToken || 0) / 10000}万</Typography.Text>
              <Typography.Text>数字人时长: {secondToMinute(digitalPersonDuration || 0)}分钟</Typography.Text>
              <Typography.Text>自动剪辑时长: {secondToMinute(autoClipDuration || 0)}分钟</Typography.Text>
            </Space>
          );
        },
      },
      {
        title: '是否兑换',
        dataIndex: 'isRedeemed',
        key: 'isRedeemed',
        render: (text, row) => {
          return (
            _.isEmpty(row.redeemedTime) ? <Tag color="green">未兑换</Tag> :
            <Tooltip title={moment(row.redeemedTime).format('YYYY-MM-DD HH:mm:ss')}>
              <Tag color="red">已兑换</Tag>
            </Tooltip>
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (__, row) => {
          return (
            <Popconfirm
              title="是否删除?!"
              onConfirm={() => { onDeleteItem(row.id); }}
            >
              <a>删除</a>
            </Popconfirm>
          );
        },
      },
    ];
  };

  return (
    <Drawer
      title="权益兑换码"
      open={open}
      onClose={onClose}
      width="60vw"
    >
      <FilterBar
        shouldShowSearchInput
        searchKeyWords={keywords}
        onChange={(value) => { setKeywords(value); }}
        onSearch={() => { onSearch(); }}
        placeholder="请输入标题"
        canAdd
        onAdd={() => { return setCreateOpen(true); }}
      />
      <PaginationTable
        totalDataCount={total}
        dataSource={items}
        pagination={pagination}
        columns={renderColumns()}
        onPaginationChange={onPaginationChange}
      />
      {createOpen && <CreateRedeemCodeDrawer
        open={createOpen}
        onClose={() => { setCreateOpen(false); }}
        onCreate={onCreateItem}
      />}
    </Drawer>
  );
}

RedeemCodeDrawer.propTypes = {
  partner: PropTypes.object.isRequired,
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  fetchItems: PropTypes.func.isRequired,
  createItem: PropTypes.func.isRequired,
  deleteItem: PropTypes.func.isRequired,
};


CreateRedeemCodeDrawer.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onCreate: PropTypes.func.isRequired,
};
