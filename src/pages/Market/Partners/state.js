import Configs from '~/consts';
import { Market } from '~/engine';
import Chanjing from '~/engine/Chanjing';
import PartnerInviteCode from '~/engine/PartnerInviteCode';
import QuotaRedeemCode from '~/engine/QuotaRedeemCode';

const SET_STATE = 'MARKET_PARTNER/SET_STATE';
const CLEAR_STATE = 'MARKET_PARTNER/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchPartners = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().marketPartner;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const { items, total } = await Market.fetchPartners(searchParams);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const getPartner = (id) => {
  return async () => {
    const result = await Market.getPartner(id);
    return result;
  };
};

export const addPartner = (params) => {
  return async (dispatch) => {
    await Market.addPartner(params);
    dispatch(fetchPartners());
  };
};

export const updatePartner = (params) => {
  return async (dispatch) => {
    await Market.updatePartner(params);
    dispatch(fetchPartners());
  };
};

export const fetchAllMembers = (params) => {
  return async () => {
    const { items } = await Market.fetchAllMembers(params);
    return items;
  };
};

export const bindMember = (params) => {
  return async (dispatch) => {
    await Market.bindMember(params);
    dispatch(fetchPartners());
  };
};

export const fetchMembers = (id) => {
  return async () => {
    const { items } = await Market.fetchPartnerMembers({ id, ...Configs.ALL_PAGE_PARAMS });
    return items;
  };
};

export const delMember = (id) => {
  return async () => {
    await Market.delPartnerMember(id);
  };
};

export const fetchPermissions = (id) => {
  return async () => {
    const result = await Market.fetchPartnerPermissions(id);
    return result;
  };
};

export const addPermissions = (params) => {
  return async () => {
    await Market.addPartnerPermissions(params);
  };
};

export const updatePermissions = (params) => {
  return async () => {
    await Market.updatePartnerPermissions(params);
  };
};

export const fetchModelQuotas = (id) => {
  return async () => {
    const result = await Market.fetchModelQuotas(id);
    return result;
  };
};

export const addModelQuotas = (params) => {
  return async () => {
    await Market.addModelQuotas(params);
  };
};

export const updateModelQuotas = (params) => {
  return async () => {
    await Market.updateModelQuotas(params);
  };
};

export const getChanjingAuth = (partnerId) => {
  return async () => {
    const result = await Chanjing.getChanjingAuth(partnerId);
    return result;
  };
};

export const upsertChanjingAuth = (partnerId, params) => {
  return async () => {
    await Chanjing.upsertChanjingAuth(partnerId, params);
  };
};

export const fetchInviteCodes = (partnerId) => {
  return async (dispatch) => {
    const { items } = await PartnerInviteCode.fetchInviteCodes(partnerId, Configs.ALL_PAGE_PARAMS);
    dispatch(
      setState({ inviteCodes: items }),
    );
  };
};

export const createInviteCode = (partnerId, params) => {
  return async (dispatch) => {
    await PartnerInviteCode.createInviteCode(partnerId, params);
    dispatch(fetchInviteCodes(partnerId));
  };
};

export const deleteInviteCode = (partnerId, itemId) => {
  return async (dispatch) => {
    await PartnerInviteCode.deleteInviteCode(itemId);
    dispatch(fetchInviteCodes(partnerId));
  };
};

export const fetchQuotaRedeemCodes = (partnerId, params) => {
  return async () => {
    const result = await QuotaRedeemCode.fetchQuotaRedeemCodes(partnerId, params);
    return result;
  };
};

export const createQuotaRedeemCode = (partnerId, params) => {
  return async () => {
    await QuotaRedeemCode.createQuotaRedeemCode(partnerId, params);
  };
};

export const deleteQuotaRedeemCode = (itemId) => {
  return async () => {
    await QuotaRedeemCode.deleteQuotaRedeemCode(itemId);
  };
};

const _getInitState = () => {
  return {
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    inviteCodes: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
