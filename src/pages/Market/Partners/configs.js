export const MARKET_MENU = {
  icon: 'icon-template',
  name: '应用',
  needAuth: true,
  routeKey: '/market-meme',
};

export const PERMISSION_LIST = [
  {
    icon: 'icon-knowledge',
    name: '因为所以',
    routeKey: '/market-meme',
  },
  {
    icon: 'icon-partner',
    name: '企微社群',
    routeKey: '/market-wework',
  },
  {
    icon: 'icon-feedback',
    name: '发布设置',
    routeKey: '/market-publish',
  },
  {
    icon: 'icon-invitation',
    name: '邀请设置',
    routeKey: '/market-invitation',
  },
  {
    icon: 'icon-partner',
    name: '合作方管理',
    routeKey: '/market-partner',
  },
  {
    icon: 'icon-article',
    name: '文稿中心',
    routeKey: '/market-article',
  },
  {
    icon: 'icon-novel',
    name: '小说中心',
    routeKey: '/market-novel',
  },
  {
    icon: 'icon-novel',
    name: '成员管理',
    routeKey: '/market-partner/member',
  },
  {
    icon: 'icon-course-material',
    name: '课程讲义',
    routeKey: '/market-course-materials',
  },
  {
    icon: 'icon-tts-white',
    name: '声音克隆',
    routeKey: '/market-voice-clone',
  },
  {
    icon: 'icon-tts-white',
    name: '声音测试',
    routeKey: '/market-voice-tts',
  },
  {
    icon: 'icon-live-room',
    name: '直播助手',
    routeKey: '/market-live-script',
  },
  {
    icon: 'icon-live-room',
    name: '直播资料库',
    routeKey: '/market-live-knowledge',
  },
  {
    icon: 'icon-comment',
    name: '企微客服',
    routeKey: '/market-wework-kf',
  },
  {
    icon: 'icon-whatapp',
    name: 'WhatsApp',
    routeKey: '/market-whatsapp',
  },
  {
    icon: 'icon-douyin-white',
    name: '抖音私信',
    routeKey: '/market-douyin-private',
  },
  {
    icon: 'icon-douyin-white',
    name: '百应数据',
    routeKey: '/market-douyin-data',
  },
  {
    icon: 'icon-client-white',
    name: 'ApiKey',
    routeKey: '/market-apikey',
  },
  {
    icon: 'icon-client-white',
    name: '智播激活码',
    routeKey: '/market-zhibo',
  },
  {
    icon: 'icon-client-white',
    name: '课件',
    routeKey: '/market-textbook',
  },
  {
    name: 'MCN写手',
    icon: 'icon-meme',
    routeKey: '/market-mcnwriter',
  },
  {
    name: '主播定位',
    icon: 'icon-meme',
    routeKey: '/market-sku',
  },
  {
    name: '文稿评估',
    icon: 'icon-meme',
    routeKey: '/market-script-evaluation',
  },
  {
    name: '播客对话',
    icon: 'icon-meme',
    routeKey: '/market-podcast-chat',
  },
  {
    name: 'GPT-image',
    icon: 'icon-meme',
    routeKey: '/market-gpt-image',
  },
];

export const LLM_MODELS = {
  OpenAI: ['gpt-3.5-turbo', 'gpt-4-turbo', 'gpt-4o', 'gpt-4o-mini'],
  Azure: ['msgpt-35', 'msgpt-4o', 'msgpt-4'],
  Claude: [
    'claude-3-opus-20240229',
    'claude-3-sonnet-20240229',
    'claude-3-haiku-20240307',
  ],
  百度文心: [
    'ernie-bot',
    'ernie-bot-turbo',
    'ernie-bot-4',
  ],
  ChatGLM: [
    'chatglm_turbo',
    'chatglm2-6b-32k',
  ],
  阿里千问: [
    'qwen-turbo',
    'qwen-plus',
    'qwen-vl-v1',
    'qwen-vl-chat-v1',
  ],
  千问开源: [
    'qwen-7b-chat',
    'qwen-14b-chat',
  ],
  Llama2: [
    'llama2-7b',
    'llama2-13b',
    'llama2-70b',
    'chinese-llama2-7b',
  ],
  腾讯混元: ['hunyuan'],
  讯飞星火: [
    'xfyun_general',
    'xfyun_generalv2',
  ],
  百川智能: [
    'Baichuan2-Turbo',
    'Baichuan2-Turbo-192k',
  ],
};

export const LLM_PRICE_ENUM = {
  'ernie-bot': 'MaxToken: 2000, ￥0.012(千/token)',
  'ernie-bot-4': 'MaxToken: 2000, ￥0.12(千/token)',
  'ernie-bot-turbo': 'MaxToken: 11200, ￥0.008(千/token)',
  'llama2-7b': 'MaxToken: 2000, ￥0.006(千/token)',
  'chinese-llama2-7b': 'MaxToken: 2000, ￥0.06(千/token)',
  'llama2-13b': 'MaxToken: 2000, ￥0.008(千/token)',
  'llama2-70b': 'MaxToken: 2000, ￥0.008(千/token)',
  chatglm_turbo: 'MaxToken: 32768, ￥0.005(千/token)',
  'chatglm2-6b-32k': 'MaxToken: 32768, ￥0.06(千/token)',
  'qwen-turbo': 'MaxToken: 6656, ￥0.008(千/token)',
  'qwen-plus': 'MaxToken: 6656, ￥0.02(千/token)',
  'qwen-7b-chat': 'MaxToken: 6656, ￥0.012(千/token)',
  'qwen-14b-chat': 'MaxToken: 6656, ￥0.012(千/token)',
  'qwen-vl-v1': 'MaxToken: 1536, ￥0.014(千/token)',
  'qwen-vl-chat-v1': 'MaxToken: 1536, ￥0.014(千/token)',
  'gpt-3.5-turbo': 'MaxToken: 4096, ￥0.0113/￥0.015(千/token)',
  'gpt-3.5-turbo-1106': 'MaxToken: 16384, ￥0.008/￥0.016(千/token)',
  'gpt-4': 'MaxToken: 8192, ￥0.24/￥0.48(千/token)',
  'gpt-4-turbo-preview': 'MaxToken: 131072, ￥0.08/￥0.24(千/token)',
  'gpt-4-vision-preview': 'MaxToken: 131072, ￥0.08/￥0.24(千/token)',
  'msgpt-35-16k': 'MaxToken: 16384, ￥0.008/￥0.016(千/token)',
  'msgpt-4-8k': 'MaxToken: 8192, ￥0.08/￥0.24(千/token)',
  'msgpt-4-32k': 'MaxToken: 32768, ￥0.08/￥0.24(千/token)',
  'msgpt-4-128k': 'MaxToken: 131072, ￥0.08/￥0.24(千/token)',
  hunyuan: 'MaxToken: 3000, ￥0.1(千/token)',
  'Baichuan2-Turbo': 'MaxToken: 3000, ￥0.008(千/token)',
  'Baichuan2-Turbo-192k': 'MaxToken: 192K, ￥0.016(千/token)',
  xfyun_general: 'MaxToken: 4096, ￥0.018(千/token)',
  xfyun_generalv2: 'MaxToken: 8192, ￥0.036(千/token)',
  'claude-3-opus-20240229': 'MaxToken: 200K, ¥0.108/¥0.54(千/token)',
  'claude-3-sonnet-20240229': 'MaxToken: 200K, ¥0.0216/¥0.108(千/token)',
  'claude-3-haiku-20240307': 'MaxToken: 32K, ¥0.0018/¥0.009(千/token)',
};
