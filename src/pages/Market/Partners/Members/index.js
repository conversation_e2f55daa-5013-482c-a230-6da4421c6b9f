import { PaginationTable } from '~/components';
import { Avatar, Popconfirm, Typography } from 'antd';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.marketPartnerMember;
  },
  actions,
)
export default class MarketPartnerMembers extends Component {
  static propTypes = {
    list: PropTypes.array,
    total: PropTypes.number,
    fetchMembers: PropTypes.func.isRequired,
    delMember: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
  }

  componentDidMount = async () => {
    await this.props.fetchMembers();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  renderColumns = () => {
    return [
      {
        title: '头像',
        dataIndex: 'avatar',
        key: 'avatar',
        align: 'center',
        render: (url) => { return <Avatar src={url} />; },
      },
      { title: '昵称', dataIndex: 'nickname', key: 'nickname', align: 'center' },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (url, row) => {
          return (
            <Popconfirm
              title="是否删除?!"
              onConfirm={async () => {
                await this.props.delMember({ id: row.uuid, partnerId: 0 });
                const items = this.state.members.filter((x) => { return x.uuid !== row.uuid; });
                this.setState({ members: items });
              }}
            >
              <a>删除</a>
            </Popconfirm>
          );
        },
      },
    ];
  };

  render = () => {
    const { total, list } = this.props;

    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <Typography.Title level={4}>成员列表</Typography.Title>
        <PaginationTable
          totalDataCount={total}
          dataSource={list}
          pagination={false}
          columns={this.renderColumns()}
        />
      </div>
    );
  };
}

export {
  reducer,
};
