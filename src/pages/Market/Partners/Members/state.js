import Configs from '~/consts';
import { Market } from '~/engine';

const SET_STATE = 'MARKET_PARTNER_MEMBER/SET_STATE';
const CLEAR_STATE = 'MARKET_PARTNER_MEMBER/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchMembers = () => {
  return async (dispatch) => {
    const searchParams = { platform: 'playground', ...Configs.ALL_PAGE_PARAMS };
    const { items, total } = await Market.fetchMembers(searchParams);
    dispatch(setState({ total, list: items }));
  };
};

export const delMember = (id) => {
  return async (dispatch) => {
    await Market.delPartnerMember(id);
    dispatch(fetchMembers());
  };
};

const _getInitState = () => {
  return {
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    list: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
