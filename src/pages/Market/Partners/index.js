import { CopyOutlined } from '@ant-design/icons';
import { FilterBar, PaginationTable, Toast } from '~/components';
import {
  Avatar,
  Button,
  Checkbox,
  Col,
  Divider,
  Drawer,
  Form,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import CreateInviteCodeDrawer from './components/CreateInviteCodeDrawer';
import RedeemCodeDrawer from './components/RedeemCodeDrawer';
import { LLM_MODELS, MARKET_MENU, PERMISSION_LIST } from './configs';
import reducer, * as actions from './state';
import { secondToMinute } from './Utils';

@connect(
  (state) => {
    return state.marketPartner;
  },
  actions,
)
export default class MarketPartner extends Component {
  static propTypes = {
    list: PropTypes.array,
    total: PropTypes.number,
    pagination: PropTypes.object,
    inviteCodes: PropTypes.array,
    getPartner: PropTypes.func.isRequired,
    fetchPartners: PropTypes.func.isRequired,
    addPartner: PropTypes.func.isRequired,
    updatePartner: PropTypes.func.isRequired,
    fetchAllMembers: PropTypes.func.isRequired,
    fetchMembers: PropTypes.func.isRequired,
    delMember: PropTypes.func.isRequired,
    fetchPermissions: PropTypes.func.isRequired,
    addPermissions: PropTypes.func.isRequired,
    updatePermissions: PropTypes.func.isRequired,
    fetchModelQuotas: PropTypes.func.isRequired,
    addModelQuotas: PropTypes.func.isRequired,
    updateModelQuotas: PropTypes.func.isRequired,
    bindMember: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
    getChanjingAuth: PropTypes.func.isRequired,
    upsertChanjingAuth: PropTypes.func.isRequired,
    fetchInviteCodes: PropTypes.func.isRequired,
    createInviteCode: PropTypes.func.isRequired,
    deleteInviteCode: PropTypes.func.isRequired,
    fetchQuotaRedeemCodes: PropTypes.func.isRequired,
    createQuotaRedeemCode: PropTypes.func.isRequired,
    deleteQuotaRedeemCode: PropTypes.func.isRequired,
  }

  state = {
    open: false,
    data: {},
    llmObj: {},
  }

  componentDidMount = async () => {
    await this.props.fetchPartners();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e?.target.value : e;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onSearch = async (e) => {
    await this.props.fetchPartners(e);
  };

  onSearchMembers = async (e) => {
    const list = await this.props.fetchAllMembers({ platform: 'playground', name: e });
    this.setState({ list: _.uniqBy(list, 'uuid') });
  }

  onShowPermissionModal = async (row) => {
    const { id, permissions } = await this.props.fetchPermissions(row.partnerId);
    const menuKeys = _.map((_.head(permissions)?.subMenus || []), 'routeKey');
    this.setState({
      permissionOpen: true,
      permissionObj: { id, partnerId: row.partnerId, menuKeys, permissions: _.head(permissions) },
    });
  }

  onShowChanjingModal = async (row) => {
    const chanjingAuth = await this.props.getChanjingAuth(row.partnerId);
    this.setState({
      chanjingOpen: true,
      chanjingObj: { ...chanjingAuth, partnerId: row.partnerId },
    });
  }

  onShowInviteCodeDrawer = async (row) => {
    await this.props.fetchInviteCodes(row.partnerId);
    this.setState({ inviteCodeOpen: true, data: row });
  }

  onSubmitPermission = async () => {
    const { id, partnerId, menuKeys, permissions } = this.state.permissionObj;
    const subMenus = PERMISSION_LIST.filter((x) => { return menuKeys.includes(x.routeKey); });
    if (_.isUndefined(id) && _.isEmpty(menuKeys)) {
      Toast.show('无配置改动!', Toast.Type.WARNING);
      return;
    }

    const data = { ...(permissions || MARKET_MENU), subMenus, routeKey: _.head(menuKeys) };
    const params = { partnerId, permissions: [data] };
    if (_.isUndefined(id)) {
      await this.props.addPermissions(params);
    } else {
      await this.props.updatePermissions(params);
    }

    Toast.show('操作成功!', Toast.Type.SUCCESS);
    this.setState({ permissionOpen: false, permissionObj: {} });
  }

  onShowLLMModal = async (row) => {
    const { id, enabledModels, tokenLimitsPerAccount } = await this.props.fetchModelQuotas(row.partnerId);
    this.setState({
      llmOpen: true,
      llmObj: { id, partnerId: row.partnerId, enabledModels, tokenLimitsPerAccount },
    });
  }

  onSubmitLLM = async () => {
    const { id, partnerId, enabledModels, tokenLimitsPerAccount } = this.state.llmObj;

    if (_.isUndefined(id) && _.isUndefined(tokenLimitsPerAccount)) {
      Toast.show('无配置改动!', Toast.Type.WARNING);
      return;
    }

    const params = { partnerId, enabledModels, tokenLimitsPerAccount };
    if (_.isUndefined(id)) {
      await this.props.addModelQuotas(params);
    } else {
      await this.props.updateModelQuotas(params);
    }

    Toast.show('操作成功!', Toast.Type.SUCCESS);
    this.setState({ llmOpen: false, llmObj: {} });
  }

  onSubmitChanjing = async () => {
    const { partnerId, apiId, apiSecret, durationLimitsPerAccount } = this.state.chanjingObj;
    await this.props.upsertChanjingAuth(partnerId, { apiId, apiSecret, durationLimitsPerAccount });
    Toast.show('操作成功!', Toast.Type.SUCCESS);
    this.setState({ chanjingOpen: false, chanjingObj: {} });
  }

  onSubmit = async () => {
    const { partnerId, name, partnerStyle, partnerType } = this.state.data;
    if (_.isEmpty(name)) {
      Toast.show('请完善信息!', Toast.Type.WARNING);
      return;
    }

    if (_.isUndefined(partnerId)) {
      await this.props.addPartner({ name, partnerStyle, partnerType });
    } else {
      await this.props.updatePartner({ id: partnerId, partnerId, name, partnerStyle, partnerType });
    }
    await this.onSubmitLLM();
    this.setState({ open: false, data: {}, llmObj: {} });
    Toast.show('操作成功!', Toast.Type.SUCCESS);
  }

  onShowMember = async (row) => {
    const members = await this.props.fetchMembers(row.partnerId);
    this.setState({ memberOpen: true, members, data: row });
  }

  renderModal = () => {
    const { open, data, llmObj } = this.state;
    return (
      <Drawer
        open={open}
        width="40vw"
        title={_.isEmpty(data) ? '新增' : '编辑'}
        onClose={() => { return this.setState({ open: false, data: {} }); }}
        extra={<Button type="primary" onClick={() => { return this.onSubmit(); }}>保存</Button>}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} className="common-form">
          <Form.Item label="合作方">
            <Input
              placeholder="合作方名称"
              value={data?.name}
              onChange={(e) => { return this.onChangeValue(e, 'name'); }}
            />
          </Form.Item>
          <Form.Item label="Token限制">
            <InputNumber
              min={0}
              step={1000}
              value={llmObj?.tokenLimitsPerAccount}
              onChange={(e) => { return this.setState({ llmObj: { ...llmObj, tokenLimitsPerAccount: e } }); }}
            />
          </Form.Item>
          <Form.Item label="类型">
            <Select
              placeholder="合作方类型"
              value={data?.partnerType}
              onChange={(e) => { return this.onChangeValue(e, 'partnerType'); }}
            >
              <Select.Option value="playground">运营</Select.Option>
              <Select.Option value="live_helper_user">主播</Select.Option>
              <Select.Option value="tts_user">TTS</Select.Option>
            </Select>
          </Form.Item>
          {
            (data?.partnerType === 'live_helper_user') &&
            <Form.Item label="主播人设">
              <Input.TextArea
                placeholder="主播人设"
                value={data?.partnerStyle}
                onChange={(e) => { return this.onChangeValue(e, 'partnerStyle'); }}
                autoSize={{ minRows: 3 }}
              />
            </Form.Item>
          }
        </Form>
      </Drawer>
    );
  };

  renderMemberModal = () => {
    const { memberOpen, members, data } = this.state;

    return (
      <Modal
        open={memberOpen}
        title="关联用户"
        onCancel={() => { return this.setState({ memberOpen: false, members: [] }); }}
        footer={null}
      >
        <Table
          size="small"
          dataSource={members}
          columns={[
            {
              title: '头像',
              dataIndex: 'avatar',
              key: 'avatar',
              align: 'center',
              render: (url) => { return <Avatar src={url} />; },
            },
            { title: '昵称', dataIndex: 'nickname', key: 'nickname', align: 'center' },
            {
              title: '操作',
              dataIndex: 'opt',
              key: 'opt',
              align: 'center',
              render: (url, row) => {
                return (
                  <Popconfirm
                    title="是否删除?!"
                    onConfirm={async () => {
                      await this.props.delMember({ id: row.uuid, partnerId: data.partnerId });
                      const items = this.state.members.filter((x) => { return x.uuid !== row.uuid; });
                      this.setState({ members: items });
                    }}
                  >
                    <a>删除</a>
                  </Popconfirm>
                );
              },
            },
          ]}
        />
      </Modal>
    );
  }

  renderBindModal = () => {
    const { bindOpen, list, data } = this.state;

    return (
      <Modal
        open={bindOpen}
        title="关联用户"
        onCancel={() => { return this.setState({ bindOpen: false, data: {}, list: [] }); }}
        footer={null}
      >
        <Input.Search enterButton="搜索" onSearch={this.onSearchMembers} />
        {
          !_.isEmpty(list) &&
          <>
            <Divider />
            <Table
              size="small"
              dataSource={list}
              columns={[
                {
                  title: '头像',
                  dataIndex: 'avatar',
                  key: 'avatar',
                  align: 'center',
                  render: (url) => { return <Avatar src={url} />; },
                },
                { title: '昵称', dataIndex: 'nickname', key: 'nickname', align: 'center' },
                {
                  title: '操作',
                  dataIndex: 'opt',
                  key: 'opt',
                  align: 'center',
                  render: (url, row) => {
                    return (
                      <Popconfirm
                        title="是否绑定?!"
                        onConfirm={() => {
                          return this.props.bindMember({ partnerId: data.partnerId, userIds: [row.uuid] });
                        }}
                      >
                        <a>绑定</a>
                      </Popconfirm>
                    );
                  },
                },
              ]}
            />
          </>
        }
      </Modal>
    );
  }

  renderPermissionModal = () => {
    const { permissionOpen, permissionObj } = this.state;
    return (
      <Modal
        open={permissionOpen}
        title="权限设置"
        onCancel={() => { return this.setState({ permissionOpen: false, permissionObj: {} }); }}
        onOk={this.onSubmitPermission}
      >
        <Checkbox.Group
          value={permissionObj?.menuKeys}
          onChange={(e) => { return this.setState({ permissionObj: { ...permissionObj, menuKeys: e } }); }}
        >
          <Row>
            {
              PERMISSION_LIST.map((x) => {
                return (
                  <Col span={12}>
                    <Checkbox value={x.routeKey}>{x.name}</Checkbox>
                  </Col>
                );
              })
            }
          </Row>
        </Checkbox.Group>
      </Modal>
    );
  }

  renderLLMModal = () => {
    const { llmOpen, llmObj } = this.state;
    return (
      <Modal
        width={800}
        open={llmOpen}
        title="模型设置"
        onCancel={() => { return this.setState({ llmOpen: false, llmObj: {} }); }}
        onOk={this.onSubmitLLM}
      >
        <Checkbox.Group
          style={{ width: '100%' }}
          value={llmObj?.enabledModels}
          onChange={(e) => { return this.setState({ llmObj: { ...llmObj, enabledModels: e } }); }}
        >
          {
            _.map(LLM_MODELS, (v, k) => {
              return (
                <div style={{ marginBottom: 20 }}>
                  <Typography.Title level={5}>{k}</Typography.Title>
                  <Row>
                    {v.map((x) => { return (<Col span={8}><Checkbox value={x}>{x}</Checkbox></Col>); })}
                  </Row>
                </div>
              );
            })
          }
        </Checkbox.Group>
      </Modal>
    );
  }

  renderChanjingModal = () => {
    const { chanjingOpen, chanjingObj } = this.state;
    return (
      <Modal
        width={800}
        open={chanjingOpen}
        title="蝉镜设置"
        onCancel={() => { return this.setState({ chanjingOpen: false, chanjingObj: {} }); }}
        onOk={this.onSubmitChanjing}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} className="common-form">
          <Form.Item label="API ID">
            <Input
              value={chanjingObj?.apiId}
              onChange={(e) => { return this.setState({ chanjingObj: { ...chanjingObj, apiId: e.target.value } }); }}
            />
          </Form.Item>
          <Form.Item label="API Secret">
            <Input
              placeholder={'*'.repeat(16)}
              value={chanjingObj?.apiSecret}
              onChange={(e) => {
                return this.setState({ chanjingObj: { ...chanjingObj, apiSecret: e.target.value } });
              }}
            />
          </Form.Item>
          <Form.Item label="时长限制">
            <InputNumber
              min={0}
              value={chanjingObj?.durationLimitsPerAccount}
              onChange={(e) => {
                return this.setState({ chanjingObj: { ...chanjingObj, durationLimitsPerAccount: e } });
              }}
              addonAfter="秒"
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  renderInviteCodeDrawer = () => {
    const { inviteCodes } = this.props;
    const { inviteCodeOpen, data } = this.state;
    return (
      <Drawer
        open={inviteCodeOpen}
        title="邀请码"
        onClose={() => { return this.setState({ inviteCodeOpen: false, data: {} }); }}
        extra={
          <Button type="primary" onClick={() => { return this.setState({ createInviteCodeOpen: true }); }}>新增</Button>
        }
        width="60vw"
      >
        <Table
          size="small"
          dataSource={inviteCodes}
          columns={[
            { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
            { title: '标题', dataIndex: 'title', key: 'title', align: 'center', width: 70 },
            {
              title: '邀请码',
              dataIndex: 'code',
              key: 'code',
              align: 'center',
              render: (txt) => {
                return (
                  <Input
                    value={txt}
                    size="small"
                    addonAfter={<CopyOutlined onClick={
                      async () => {
                        await navigator.clipboard.writeText(txt);
                        Toast.show('复制成功', Toast.Type.SUCCESS);
                      }}
                    />}
                  />
                );
              },
            },
            {
              title: '状态',
              dataIndex: 'expired',
              key: 'expired',
              align: 'center',
              render: (expired, row) => {
                const tag = expired ? <Tag color="red">已过期</Tag> : <Tag color="green">未过期</Tag>;
                return (
                  <Tooltip
                    title={row.expiredAt ? `过期时间: ${moment(row.expiredAt).format('YYYY-MM-DD HH:mm:ss')}` : '永不过期'}
                  >
                    {tag}
                  </Tooltip>
                );
              },
            },
            {
              title: '账号数量',
              dataIndex: 'accountCount',
              key: 'accountCount',
              align: 'center',
              render: (txt, row) => {
                const { maxAccountCount } = row;
                return `${txt}/${maxAccountCount > 0 ? maxAccountCount : '无限制'}`;
              },
            },
            {
              title: '权益',
              dataIndex: 'limitsSetting',
              key: 'limitsSetting',
              align: 'center',
              render: (__, row) => {
                const { llmToken, digitalPersonDuration, autoClipDuration } = row.limitsSetting;
                return (
                  <Space direction="vertical">
                    <Typography.Text>LLM Token: {(llmToken || 0) / 10000}万</Typography.Text>
                    <Typography.Text>数字人时长: {secondToMinute(digitalPersonDuration || 0)}分钟</Typography.Text>
                    <Typography.Text>自动剪辑时长: {secondToMinute(autoClipDuration || 0)}分钟</Typography.Text>
                  </Space>
                );
              },
            },
            {
              title: '操作',
              dataIndex: 'opt',
              key: 'opt',
              align: 'center',
              render: (__, row) => {
                return (
                  <Popconfirm
                    title="是否删除?!"
                    onConfirm={() => { this.props.deleteInviteCode(row.partnerId, row.id); }}
                  >
                    <a>删除</a>
                  </Popconfirm>
                );
              },
            },
          ]}
        />
        {this.state.createInviteCodeOpen && (
          <CreateInviteCodeDrawer
            open={this.state.createInviteCodeOpen}
            onClose={() => { return this.setState({ createInviteCodeOpen: false }); }}
            onCreate={(params) => { return this.props.createInviteCode(data.partnerId, params); }}
          />
        )}
      </Drawer>
    );
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'partnerId', key: 'partnerId', align: 'center' },
      { title: '合作方', dataIndex: 'name', key: 'name', align: 'center' },
      {
        title: '账号数量',
        dataIndex: 'memberCount',
        key: 'memberCount',
        align: 'center',
        render: (txt, row) => {
          return txt > 0 ? <a onClick={() => { return this.onShowMember(row); }}>{txt}</a> : '-';
        },
      },
      { title: '创建人', dataIndex: 'creator', key: 'creator', align: 'center' },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <a onClick={async () => {
                const data = await this.props.getPartner(row.partnerId);
                const { id, enabledModels, tokenLimitsPerAccount } = await this.props.fetchModelQuotas(row.partnerId);
                return this.setState({
                  open: true,
                  data,
                  llmObj: { id, partnerId: row.partnerId, enabledModels, tokenLimitsPerAccount },
                });
              }}
              >编辑
              </a>
              <Divider type="vertical" />
              <a onClick={() => { return this.setState({ bindOpen: true, data: row }); }}>关联用户</a>
              <Divider type="vertical" />
              <a onClick={() => { return this.onShowLLMModal(row); }}>模型</a>
              <Divider type="vertical" />
              <a onClick={() => { return this.onShowPermissionModal(row); }}>权限</a>
              <Divider type="vertical" />
              <a onClick={() => { return this.onShowChanjingModal(row); }}>蝉镜</a>
              <Divider type="vertical" />
              <a onClick={() => { return this.onShowInviteCodeDrawer(row); }}>邀请码</a>
              <Divider type="vertical" />
              <a onClick={() => { return this.setState({ redeemCodeOpen: true, data: row }); }}>兑换码</a>
            </>
          );
        },
      },
    ];
  };

  render = () => {
    const { total, list, pagination } = this.props;

    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <FilterBar
          canAdd
          shouldShowSearchInput={false}
          onAdd={() => { return this.setState({ open: true, data: {} }); }}
        />
        <PaginationTable
          totalDataCount={total}
          dataSource={list}
          pagination={pagination}
          columns={this.renderColumns()}
          onPaginationChange={(e) => { return this.onSearch(e); }}
        />

        {this.state.open && this.renderModal()}
        {this.state.bindOpen && this.renderBindModal()}
        {this.state.llmOpen && this.renderLLMModal()}
        {this.state.permissionOpen && this.renderPermissionModal()}
        {this.state.memberOpen && this.renderMemberModal()}
        {this.state.chanjingOpen && this.renderChanjingModal()}
        {this.state.inviteCodeOpen && this.renderInviteCodeDrawer()}
        {this.state.redeemCodeOpen &&
          <RedeemCodeDrawer
            partner={this.state.data}
            open={this.state.redeemCodeOpen}
            onClose={() => { return this.setState({ redeemCodeOpen: false, data: {} }); }}
            fetchItems={this.props.fetchQuotaRedeemCodes}
            createItem={this.props.createQuotaRedeemCode}
            deleteItem={this.props.deleteQuotaRedeemCode}
          />
        }
      </div>
    );
  };
}

export {
  reducer,
};
