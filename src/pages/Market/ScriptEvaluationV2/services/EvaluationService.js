import Engine, { Sessions } from '~/engine';
import { EVENT_TYPE } from '~/pages/Playground/Configs';
import ReconnectingWebSocket from '~/pages/Playground/WebSocket';
import { Platform, StringExtension } from '~/plugins';
import { message } from 'antd';
import * as JSONC from 'jsonc-parser';
import _ from 'lodash';
import qs from 'qs';

// 并发限制常量
const CONCURRENT_LIMIT = 10;

// 工作流配置
const FLOW_MAP = {
  stg: 'MW24mDbmQCtjavB80KjGvB',
  prod: '4bmA061WXThqj9qeJ1Sv3c',
};

/**
 * 评估服务类
 * 封装ScriptEvaluation的核心评估逻辑，提供标准化的评估服务API
 */
export default class EvaluationService {
  constructor() {
    // WebSocket连接池
    this.wsConnections = new Map();
    // 评估状态
    this.isProcessing = false;
    // 进度回调函数
    this.onProgressUpdate = null;
    // 结果回调函数
    this.onResultUpdate = null;
    // 完成回调函数
    this.onComplete = null;
    // 错误回调函数
    this.onError = null;
  }

  /**
   * 设置回调函数
   * @param {Object} callbacks - 回调函数集合
   */
  setCallbacks(callbacks = {}) {
    this.onProgressUpdate = callbacks.onProgressUpdate || null;
    this.onResultUpdate = callbacks.onResultUpdate || null;
    this.onComplete = callbacks.onComplete || null;
    this.onError = callbacks.onError || null;
  }

  /**
   * 开始评估
   * @param {Object} params - 评估参数
   * @param {Array} params.excelData - Excel数据
   * @param {Array} params.headers - Excel表头
   * @param {string} params.promptText - 提示词文本
   * @param {boolean} params.disableCache - 是否禁用缓存
   * @param {Object} params.promptInfo - 提示词信息
   * @param {Object} params.fileInfo - 文件信息
   */
  async startEvaluation(params) {
    const { excelData, headers, promptText, disableCache = true, promptInfo, fileInfo } = params;

    if (this.isProcessing) {
      message.warning('正在处理中，请等待当前任务完成');
      return;
    }

    if (!excelData || excelData.length === 0) {
      message.warning('请先上传Excel文件！');
      return;
    }

    if (!promptText.trim()) {
      message.warning('请输入评估提示词！');
      return;
    }

    try {
      this.isProcessing = true;

      // 处理Excel数据，转换为文本格式
      const processedTexts = this.processExcelData(excelData, headers);

      // 验证处理结果
      if (!processedTexts || processedTexts.length === 0) {
        throw new Error('Excel数据处理后为空，请检查文件内容和格式');
      }

      // 通知开始处理
      if (this.onProgressUpdate) {
        this.onProgressUpdate({
          totalItems: processedTexts.length,
          completedItems: 0,
          isProcessing: true,
        });
      }

      message.info(`开始并发处理，共 ${processedTexts.length} 条数据，并发数: ${CONCURRENT_LIMIT}`);

      // 根据去缓存选项决定是否为 promptText 添加随机盐值
      const saltedPromptText = disableCache
        ? this.addRandomSalt(promptText)
        : promptText;

      // 存储评估结果
      const allResults = [];
      let completedItems = 0;

      // 并发处理所有数据项
      await this.processItemsConcurrently(
        processedTexts,
        saltedPromptText,
        (result, itemIndex) => {
          // 单个项目完成回调
          allResults[itemIndex] = result;
          completedItems++;

          if (this.onProgressUpdate) {
            this.onProgressUpdate({
              totalItems: processedTexts.length,
              completedItems,
              isProcessing: true,
            });
          }

          if (this.onResultUpdate) {
            this.onResultUpdate(result, itemIndex);
          }

          message.success(`数据项 ${itemIndex + 1} 处理完成`);
        },
      );

      // 评估完成
      message.success('文稿评估完成！');

      if (this.onComplete) {
        // 对评估结果进行标准化处理
        const normalizedResults = this.normalizeEvaluationResults(
          allResults.filter((item) => { return item !== undefined; }),
        );

        this.onComplete({
          results: normalizedResults,
          promptInfo,
          fileInfo,
        });
      }
    } catch (error) {
      console.error('评估处理失败:', error); // eslint-disable-line no-console
      message.error(error.message || '评估处理失败，请重试！');

      if (this.onError) {
        this.onError(error);
      }
    } finally {
      // 清理所有剩余的WebSocket连接
      this.wsConnections.forEach((ws, itemIndex) => {
        this.cleanupConnection(itemIndex);
      });

      this.isProcessing = false;

      if (this.onProgressUpdate) {
        this.onProgressUpdate({
          totalItems: 0,
          completedItems: 0,
          isProcessing: false,
        });
      }
    }
  }

  /**
   * 处理Excel数据
   * @param {Array} excelData - Excel原始数据
   * @param {Array} headers - Excel表头
   * @returns {Array} 处理后的数据
   */
  processExcelData = (excelData, headers) => {
    // 验证输入参数
    if (!excelData || !Array.isArray(excelData)) {
      throw new Error('Excel数据无效：excelData 必须是数组');
    }

    if (!headers || !Array.isArray(headers)) {
      throw new Error('Excel表头无效：headers 必须是数组');
    }

    if (excelData.length === 0) {
      throw new Error('Excel文件为空，请检查文件内容');
    }

    if (headers.length === 0) {
      throw new Error('Excel表头为空，请检查文件格式');
    }

    // 查找必需的列
    const contentRefIndex = headers.findIndex((header) => { return header === '内容参考'; });
    const styleRefIndex = headers.findIndex((header) => { return header === '风格参考'; });
    const structureIndex = headers.findIndex((header) => { return header === '结构要求'; });
    const outputIndex = headers.findIndex((header) => { return header === '最终输出'; });

    if (contentRefIndex === -1) {
      throw new Error(`Excel文件中未找到"内容参考"列，请检查文件格式。当前表头：${headers.join('、')}`);
    }
    if (styleRefIndex === -1) {
      throw new Error(`Excel文件中未找到"风格参考"列，请检查文件格式。当前表头：${headers.join('、')}`);
    }
    if (structureIndex === -1) {
      throw new Error(`Excel文件中未找到"结构要求"列，请检查文件格式。当前表头：${headers.join('、')}`);
    }
    if (outputIndex === -1) {
      throw new Error(`Excel文件中未找到"最终输出"列，请检查文件格式。当前表头：${headers.join('、')}`);
    }

    // 处理数据
    const processedData = excelData.map((row, index) => {
      const contentRef = row[contentRefIndex] || '';
      const styleRef = row[styleRefIndex] || '';
      const structure = row[structureIndex] || '';
      const output = row[outputIndex] || '';

      // 转换为字符串并去除空白
      const contentRefStr = String(contentRef).trim();
      const styleRefStr = String(styleRef).trim();
      const structureStr = String(structure).trim();
      const outputStr = String(output).trim();

      // 验证必需字段
      if (!contentRefStr || !styleRefStr || !structureStr || !outputStr) {
        const errorMsg = `第 ${index + 2} 行数据不完整，请检查"内容参考"、"风格参考"、"结构要求"、"最终输出"列是否都有内容。` +
          `当前值：内容参考="${contentRefStr}"，风格参考="${styleRefStr}"，结构要求="${structureStr}"，最终输出="${outputStr}"`;
        throw new Error(errorMsg);
      }

      return {
        id: index + 1,
        contentRef: contentRefStr,
        styleRef: styleRefStr,
        structure: structureStr,
        output: outputStr,
        originalData: row, // 保留原始数据
      };
    });

    return processedData;
  }

  /**
   * 并发处理所有数据项
   * @param {Array} items - 数据项列表
   * @param {string} promptText - 提示词
   * @param {Function} onItemComplete - 单项完成回调
   */
  async processItemsConcurrently(items, promptText, onItemComplete) {
    const semaphore = new Array(CONCURRENT_LIMIT).fill(null);
    let itemIndex = 0;

    const processItem = async () => {
      while (itemIndex < items.length) {
        const currentItemIndex = itemIndex++;
        const item = items[currentItemIndex];

        try { // eslint-disable-next-line no-await-in-loop
          const result = await this.processSingleItem(item, currentItemIndex, promptText);
          if (onItemComplete) {
            onItemComplete(result, currentItemIndex);
          }
        } catch (error) {
          console.error(`数据项 ${currentItemIndex} 处理失败:`, error); // eslint-disable-line no-console
          // 即使单个项目失败，也要通知完成（带错误信息）
          if (onItemComplete) {
            onItemComplete({ error: error.message, itemIndex: currentItemIndex }, currentItemIndex);
          }
        }
      }
    };

    await Promise.all(semaphore.map(() => { return processItem(); }));
  }

  /**
 * 处理单个数据项
 * @param {Object} item - 数据项
 * @param {number} itemIndex - 项目索引
 * @param {string} promptText - 提示词
 * @returns {Promise} 处理结果
 */
  processSingleItem(item, itemIndex, promptText) {
    return new Promise((resolve, reject) => {
      try {
        const flowId = FLOW_MAP[Platform.isProd() ? 'prod' : 'stg'];
        const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflow-v2/run/${flowId}`;
        const query = { access_token: Sessions.getToken() };

        const ws = new ReconnectingWebSocket(
          `${path}?${qs.stringify(query)}`,
          [],
          (e) => { return this.onReceiveMsg(e, itemIndex, resolve, reject); },
          () => {
            // 为单个数据项发送WebSocket消息
            ws.send(JSON.stringify({
              text: JSON.stringify({
                content: item.contentRef, // 内容参考
                style_reference: item.styleRef, // 风格参考
                structure_format: item.structure, // 结构要求
                article: item.output, // 最终输出
                prompt: promptText,
              }),
              type: 'message',
              is_beta: false,
              item_id: item.id,
              item_index: itemIndex,
            }));
          },
        );

        this.wsConnections.set(itemIndex, ws);

        // 设置超时处理
        setTimeout(() => {
          if (this.wsConnections.has(itemIndex)) {
            this.cleanupConnection(itemIndex);
            reject(new Error(`数据项 ${itemIndex + 1} 处理超时`));
          }
        }, 30 * 60 * 1000); // 30分钟超时
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
 * 接收WebSocket消息
 * @param {Event} event - WebSocket事件
 * @param {number} itemIndex - 项目索引
 * @param {Function} resolve - Promise resolve
 * @param {Function} reject - Promise reject
 */
  onReceiveMsg(event, itemIndex, resolve, reject) {
    if (event?.data !== 'pong') {
      try {
        const originData = JSON.parse(event.data);
        const { type, data } = StringExtension.snakeToCamelObj(originData);

        if (type === EVENT_TYPE.FINAL_RESULT) {
          try {
            const { output } = JSON.parse(data?.output);

            // 解析结果
            const result = this.parseEvaluationResult(output, itemIndex);

            // 清理连接
            this.cleanupConnection(itemIndex);

            // 解析Promise
            resolve(result);
          } catch (error) {
            // 清理连接
            this.cleanupConnection(itemIndex);
            reject(error);
          }
        }
      } catch (error) {
        console.error(`数据项 ${itemIndex + 1} 消息解析失败:`, error); // eslint-disable-line no-console
        this.cleanupConnection(itemIndex);
        reject(error);
      }
    }
  }

  /**
 * 解析评估结果
 * @param {string} content - 原始结果内容
 * @param {number} itemIndex - 项目索引
 * @returns {Object} 解析后的结果
 */
  parseEvaluationResult = (content, itemIndex) => {
    try {
      // 尝试解析JSON结果
      let result = JSONC.parse(content || '{}');
      if (_.isUndefined(result) || result === 1) {
        result = JSONC.parse(content.split('```')[1]); // 处理特殊情况
      }

      return { ...result, itemIndex };
    } catch (error) {
      console.error('结果解析失败:', error, content); // eslint-disable-line no-console
      // 如果JSON解析失败，将原始内容作为结果保存
      return { itemIndex, content, parseError: true };
    }
  }

  /**
 * 清理WebSocket连接
 * @param {number} itemIndex - 项目索引
 */
  cleanupConnection(itemIndex) {
    const ws = this.wsConnections.get(itemIndex);
    if (ws) {
      try {
        ws.close();
      } catch (error) {
        console.warn(`关闭数据项 ${itemIndex + 1} 连接时出错:`, error); // eslint-disable-line no-console
      }
      this.wsConnections.delete(itemIndex);
    }
  }

  /**
 * 为文本添加随机盐值，防止缓存
 * @param {string} text - 原始文本
 * @returns {string} 添加盐值后的文本
 */
  addRandomSalt = (text) => {
    if (typeof text !== 'string' || !text.trim()) {
      return text;
    }

    const minSpaces = 1;
    const maxSpaces = 3;
    const probability = 0.3;

    // 在随机位置插入随机数量的空格
    const words = text.split(' ');
    const result = [];

    for (let i = 0; i < words.length; i++) {
      result.push(words[i]);

      // 在词之间随机插入额外空格
      if (i < words.length - 1 && Math.random() < probability) {
        const spaceCount = Math.floor(Math.random() * (maxSpaces - minSpaces + 1)) + minSpaces;
        result.push(' '.repeat(spaceCount));
      }
    }

    return result.join(' ');
  }

  /**
 * 停止当前评估
 */
  stopEvaluation() {
    if (this.isProcessing) {
      // 清理所有WebSocket连接
      this.wsConnections.forEach((ws, itemIndex) => {
        this.cleanupConnection(itemIndex);
      });

      this.isProcessing = false;
      message.info('评估已停止');

      if (this.onProgressUpdate) {
        this.onProgressUpdate({
          totalItems: 0,
          completedItems: 0,
          isProcessing: false,
        });
      }
    }
  }

  /**
 * 获取当前处理状态
 * @returns {boolean} 是否正在处理
 */
  getProcessingStatus() {
    return this.isProcessing;
  }

  /**
   * 标准化评估结果
   * 将特定的评估值转换为标准格式
   * @param {Array} results - 评估结果数组
   * @returns {Array} 标准化后的结果数组
   */
  normalizeEvaluationResults(results) {
    if (!Array.isArray(results)) {
      return results;
    }

    try {
      // 深拷贝结果数组，避免修改原始数据
      const normalizedResults = JSON.parse(JSON.stringify(results));

      return normalizedResults.map((result) => {
        return this.normalizeObjectValues(result);
      });
    } catch (error) {
      console.error('标准化评估结果失败:', error); // eslint-disable-line no-console
      return results; // 如果处理失败，返回原始结果
    }
  }

  /**
   * 递归标准化对象中的值
   * @param {any} obj - 要处理的对象或值
   * @returns {any} 标准化后的对象或值
   */
  normalizeObjectValues(obj) {
    // 值转换映射表
    const valueMap = {
      合格: '是',
      符合: '是',
      确定: '是',
      不合格: '否',
      不符合: '否',
      不确定: '否',
    };

    // 如果是字符串，进行转换
    if (typeof obj === 'string') {
      return valueMap[obj] || obj;
    }

    // 如果是数组，递归处理每个元素
    if (Array.isArray(obj)) {
      return obj.map((item) => { return this.normalizeObjectValues(item); });
    }

    // 如果是对象，递归处理每个属性
    if (obj !== null && typeof obj === 'object') {
      const normalizedObj = {};
      Object.keys(obj).forEach((key) => {
        normalizedObj[key] = this.normalizeObjectValues(obj[key]);
      });
      return normalizedObj;
    }

    // 其他类型直接返回
    return obj;
  }
}
