/**
 * 提示词版本管理工具类
 * 为后续扩展提示词修改功能预留接口
 */
export default class PromptVersionManager {
  /**
   * 创建提示词版本差异对比
   * @param {string} originalContent - 原始内容
   * @param {string} modifiedContent - 修改后内容
   * @returns {Object} 差异对比结果
   */
  static createDiff(originalContent, modifiedContent) {
    // TODO: 实现详细的文本差异对比算法
    // 可以使用 diff 库或自定义算法

    const originalLines = originalContent.split('\n');
    const modifiedLines = modifiedContent.split('\n');

    const changes = [];
    const maxLength = Math.max(originalLines.length, modifiedLines.length);

    for (let i = 0; i < maxLength; i++) {
      const originalLine = originalLines[i] || '';
      const modifiedLine = modifiedLines[i] || '';

      if (originalLine !== modifiedLine) {
        changes.push({
          lineNumber: i + 1, // eslint-disable-next-line
          type: originalLine === '' ? 'added' : modifiedLine === '' ? 'deleted' : 'modified',
          original: originalLine,
          modified: modifiedLine,
        });
      }
    }

    return {
      hasChanges: changes.length > 0,
      changes,
      statistics: {
        totalLines: maxLength,
        changedLines: changes.length,
        addedLines: changes.filter((c) => { return c.type === 'added'; }).length,
        deletedLines: changes.filter((c) => { return c.type === 'deleted'; }).length,
        modifiedLines: changes.filter((c) => { return c.type === 'modified'; }).length,
      },
    };
  }

  /**
   * 生成提示词版本信息
   * @param {string} content - 提示词内容
   * @param {string} description - 版本描述
   * @param {string} baseVersionId - 基础版本ID（可选）
   * @returns {Object} 版本信息
   */
  static createVersionInfo(content, description = '新版本', baseVersionId = null) {
    const timestamp = new Date().toISOString();
    const hash = this.generateContentHash(content);

    return {
      id: `version_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content,
      description,
      hash,
      baseVersionId,
      createdAt: timestamp,
      metadata: {
        contentLength: content.length,
        lineCount: content.split('\n').length,
        wordCount: content.split(/\s+/).filter((word) => { return word.length > 0; }).length,
      },
    };
  }

  /**
   * 生成内容哈希值
   * @param {string} content - 内容
   * @returns {string} 哈希值
   */
  static generateContentHash(content) {
    let hash = 0;
    if (content.length === 0) return hash.toString();

    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      // eslint-disable-next-line no-bitwise
      hash = ((hash << 5) - hash) + char;
      // eslint-disable-next-line no-bitwise
      hash &= hash; // Convert to 32bit integer
    }

    return Math.abs(hash).toString(36);
  }

  /**
   * 验证提示词内容
   * @param {string} content - 提示词内容
   * @returns {Object} 验证结果
   */
  static validatePromptContent(content) {
    const errors = [];
    const warnings = [];

    // 基本验证
    if (!content || content.trim().length === 0) {
      errors.push('提示词内容不能为空');
    }

    if (content.length < 10) {
      warnings.push('提示词内容过短，可能影响评估效果');
    }

    if (content.length > 5000) {
      warnings.push('提示词内容过长，可能影响处理性能');
    }

    // 检查必要的占位符
    const requiredPlaceholders = ['{generated_script}'];
    const missingPlaceholders = requiredPlaceholders.filter((placeholder) => {
      return !content.includes(placeholder);
    });

    if (missingPlaceholders.length > 0) {
      warnings.push(`缺少必要的占位符: ${missingPlaceholders.join(', ')}`);
    }

    // 检查JSON格式要求
    if (!content.includes('JSON') && !content.includes('json')) {
      warnings.push('建议在提示词中明确要求返回JSON格式');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: this.calculatePromptScore(content),
    };
  }

  /**
   * 计算提示词质量评分
   * @param {string} content - 提示词内容
   * @returns {number} 评分（0-100）
   */
  static calculatePromptScore(content) {
    let score = 0;

    // 长度评分 (20分)
    const { length } = content;
    if (length >= 100 && length <= 2000) {
      score += 20;
    } else if (length >= 50 && length <= 3000) {
      score += 15;
    } else if (length >= 20) {
      score += 10;
    }

    // 结构评分 (30分)
    const hasInstructions = /指示|任务|要求|评估/.test(content);
    const hasExamples = /例如|示例|比如/.test(content);
    const hasFormat = /格式|JSON|输出/.test(content);

    if (hasInstructions) score += 10;
    if (hasExamples) score += 10;
    if (hasFormat) score += 10;

    // 占位符评分 (20分)
    const placeholders = content.match(/\{[^}]+\}/g) || [];
    if (placeholders.length >= 1) score += 10;
    if (placeholders.length >= 3) score += 10;

    // 清晰度评分 (30分)
    const sentences = content.split(/[。！？.!?]/).filter((s) => { return s.trim().length > 0; });
    const avgSentenceLength = sentences.reduce((sum, s) => { return sum + s.length; }, 0) / sentences.length;

    if (avgSentenceLength >= 10 && avgSentenceLength <= 50) {
      score += 15;
    } else if (avgSentenceLength >= 5 && avgSentenceLength <= 80) {
      score += 10;
    }

    const hasSpecificTerms = /符合|一致|判断|分析/.test(content);
    if (hasSpecificTerms) score += 15;

    return Math.min(100, Math.max(0, score));
  }

  /**
   * 为后续扩展预留：提示词优化建议
   * @param {string} content - 提示词内容
   * @param {Array} evaluationResults - 评估结果（可选）
   * @returns {Array} 优化建议列表
   */
  static generateOptimizationSuggestions(content, evaluationResults = []) {
    const suggestions = [];
    const validation = this.validatePromptContent(content);

    // 基于验证结果的建议
    validation.warnings.forEach((warning) => {
      suggestions.push({
        type: 'warning',
        category: 'structure',
        message: warning,
        priority: 'medium',
      });
    });

    // 基于评估结果的建议
    if (evaluationResults.length > 0) {
      const successRate = evaluationResults.filter((r) => { return r.success; }).length / evaluationResults.length;

      if (successRate < 0.7) {
        suggestions.push({
          type: 'improvement',
          category: 'performance',
          message: '成功率较低，建议增加更具体的评估标准和示例',
          priority: 'high',
        });
      }

      if (successRate < 0.5) {
        suggestions.push({
          type: 'improvement',
          category: 'performance',
          message: '成功率过低，建议重新设计提示词结构',
          priority: 'critical',
        });
      }
    }

    // 基于内容分析的建议
    if (validation.score < 60) {
      suggestions.push({
        type: 'improvement',
        category: 'quality',
        message: '提示词质量评分较低，建议参考最佳实践进行优化',
        priority: 'high',
      });
    }

    return suggestions;
  }

  /**
   * 为后续扩展预留：提示词模板管理
   * @returns {Array} 预定义模板列表
   */
  static getPromptTemplates() {
    return [
      {
        id: 'basic_evaluation',
        name: '基础评估模板',
        description: '适用于一般文稿评估场景',
        category: 'evaluation',
        content: `你是一位资深的内容评估专家。请根据以下标准评估文稿质量：

输入内容：
文稿内容：{generated_script}
评估标准：{evaluation_criteria}

评估维度：
1. 内容质量
2. 语言表达
3. 结构完整性

请返回JSON格式的评估结果：
{"质量评分": "", "语言评分": "", "结构评分": "", "综合评价": "", "改进建议": ""}`,
      },
      {
        id: 'style_consistency',
        name: '风格一致性模板',
        description: '专注于评估内容风格一致性',
        category: 'style',
        content: `你是一位专业的内容风格分析师。请评估文稿的风格一致性：

输入内容：
待评估文稿：{generated_script}
风格参考：{style_reference}

评估重点：
- 语言风格是否与参考保持一致
- 表达方式是否符合预期
- 术语使用是否规范

返回JSON格式：
{"风格一致性": "", "语言特征": "", "改进建议": ""}`,
      },
    ];
  }

  /**
   * 为后续扩展预留：版本回滚功能
   * @param {string} targetVersionId - 目标版本ID
   * @param {Array} versionHistory - 版本历史
   * @returns {Object} 回滚结果
   */
  static rollbackToVersion(targetVersionId, versionHistory) {
    const targetVersion = versionHistory.find((v) => { return v.id === targetVersionId; });

    if (!targetVersion) {
      return {
        success: false,
        error: '目标版本不存在',
      };
    }

    return {
      success: true,
      version: targetVersion,
      rollbackInfo: {
        targetVersionId,
        rollbackTime: new Date().toISOString(),
        description: `回滚到版本: ${targetVersion.description}`,
      },
    };
  }
}
