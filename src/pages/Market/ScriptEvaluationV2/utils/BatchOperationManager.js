import { message } from 'antd';

/**
 * 批量操作管理器
 * 负责批量评估优化流程的编排和状态跟踪
 *
 * 设计原则：
 * 1. 零侵入性 - 不修改任何现有的评估、优化、存储逻辑
 * 2. 流程复用 - 完全复用现有的单次操作流程
 * 3. 状态隔离 - 批量操作状态独立管理
 * 4. 数据一致性 - 每次迭代都按照现有的单次操作模式处理数据
 */
export default class BatchOperationManager {
  constructor() {
    // 批量操作状态
    this.isRunning = false;
    this.isCancelled = false;
    this.isPaused = false;
    this.currentIteration = 0;
    this.totalIterations = 0;
    this.currentStep = '';
    this.currentStepProgress = 0;

    // 批量操作历史记录
    this.operationHistory = [];
    this.currentOperationId = null;

    // 当前迭代数据
    this.currentPromptText = '';
    this.iterationResults = [];
    this.startTime = null;
    this.endTime = null;

    // 回调函数
    this.onProgressUpdate = null;
    this.onIterationComplete = null;
    this.onComplete = null;
    this.onError = null;
    this.onStepChange = null;

    // 操作超时设置（每个步骤最大30分钟）
    this.stepTimeout = 30 * 60 * 1000;
    this.currentStepTimer = null;
  }

  /**
   * 设置回调函数
   * @param {Object} callbacks - 回调函数集合
   */
  setCallbacks(callbacks = {}) {
    this.onProgressUpdate = callbacks.onProgressUpdate || null;
    this.onIterationComplete = callbacks.onIterationComplete || null;
    this.onComplete = callbacks.onComplete || null;
    this.onError = callbacks.onError || null;
    this.onStepChange = callbacks.onStepChange || null;
  }

  /**
   * 开始批量操作
   * @param {Object} params - 批量操作参数
   * @param {number} params.iterations - 迭代次数
   * @param {Function} params.evaluateFunction - 评估函数
   * @param {Function} params.optimizeFunction - 优化函数
   * @param {Object} params.initialData - 初始数据
   * @param {string} params.initialPromptText - 初始提示词
   */
  async startBatchOperation(params) {
    const { iterations, evaluateFunction, optimizeFunction, initialData, initialPromptText } = params;

    // 验证参数
    if (!iterations || iterations < 1 || iterations > 10) {
      throw new Error('批量次数必须在1-10之间');
    }

    if (this.isRunning) {
      throw new Error('批量操作正在进行中，请等待完成或取消当前操作');
    }

    if (typeof evaluateFunction !== 'function' || typeof optimizeFunction !== 'function') {
      throw new Error('评估函数和优化函数必须是有效的函数');
    }

    // 初始化批量操作
    this.isRunning = true;
    this.isCancelled = false;
    this.isPaused = false;
    this.currentIteration = 0;
    this.totalIterations = iterations;
    this.currentPromptText = initialPromptText;
    this.iterationResults = [];
    this.startTime = new Date().toISOString();
    this.endTime = null;

    // 生成操作ID
    this.currentOperationId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 创建操作记录
    const operationRecord = {
      id: this.currentOperationId,
      startTime: this.startTime,
      endTime: null,
      totalIterations: iterations,
      completedIterations: 0,
      status: 'running',
      initialData,
      initialPromptText,
      iterations: [],
      summary: null,
    };

    this.operationHistory.push(operationRecord);

    try {
      // 更新进度
      this.updateProgress();

      // 执行批量迭代
      for (let i = 1; i <= iterations; i++) {
        if (this.isCancelled) {
          operationRecord.status = 'cancelled';
          operationRecord.endTime = new Date().toISOString();
          break;
        }

        // 处理暂停状态
        while (this.isPaused && !this.isCancelled) {
          await this.sleep(1000);  // eslint-disable-line
        }

        if (this.isCancelled) {
          operationRecord.status = 'cancelled';
          operationRecord.endTime = new Date().toISOString();
          break;
        }

        this.currentIteration = i;
        this.currentStep = `第${i}次迭代`;

        // 执行单次迭代
        const iterationResult = await this.executeIteration(i, evaluateFunction, optimizeFunction); // eslint-disable-line

        // 记录迭代结果
        operationRecord.iterations.push(iterationResult);
        operationRecord.completedIterations = i;
        this.iterationResults.push(iterationResult);

        // 更新当前提示词为优化后的版本
        if (iterationResult.optimizedPrompt) {
          this.currentPromptText = iterationResult.optimizedPrompt;
        }

        // 通知迭代完成
        if (this.onIterationComplete) {
          this.onIterationComplete(iterationResult, i, iterations);
        }

        // 更新进度
        this.updateProgress();
      }

      // 完成批量操作
      if (!this.isCancelled) {
        operationRecord.status = 'completed';
        operationRecord.endTime = new Date().toISOString();
        operationRecord.summary = this.generateOperationSummary(operationRecord);

        this.endTime = operationRecord.endTime;

        if (this.onComplete) {
          this.onComplete(operationRecord);
        }

        message.success(`批量操作完成！共执行${iterations}次迭代`);
      } else {
        message.info('批量操作已取消');
      }
    } catch (error) {
      operationRecord.status = 'failed';
      operationRecord.endTime = new Date().toISOString();
      operationRecord.error = error.message;

      if (this.onError) {
        this.onError(error, operationRecord);
      }

      message.error(`批量操作失败: ${error.message}`);
    } finally {
      // 清理状态
      this.isRunning = false;
      this.isCancelled = false;
      this.isPaused = false;
      this.currentIteration = 0;
      this.currentStep = '';
      this.currentStepProgress = 0;
      this.clearStepTimer();
    }
  }

  /**
   * 执行单次迭代
   * @param {number} iterationIndex - 迭代索引
   * @param {Function} evaluateFunction - 评估函数
   * @param {Function} optimizeFunction - 优化函数
   * @returns {Object} 迭代结果
   */
  async executeIteration(iterationIndex, evaluateFunction, optimizeFunction) {
    const iterationStartTime = new Date().toISOString();

    const iterationResult = {
      iteration: iterationIndex,
      startTime: iterationStartTime,
      endTime: null,
      promptText: this.currentPromptText,
      evaluationResults: null,
      optimizedPrompt: null,
      optimizeReason: null,
      status: 'running',
      error: null,
      duration: 0,
    };

    try {
      // 步骤1: 执行评估
      this.currentStep = `第${iterationIndex}次迭代 - 评估中`;
      this.currentStepProgress = 0;
      this.updateProgress();
      this.notifyStepChange('evaluation', iterationIndex);

      const evaluationResults = await this.executeWithTimeout(
        () => { return evaluateFunction(this.currentPromptText); },
        '评估超时',
      );

      iterationResult.evaluationResults = evaluationResults;
      this.currentStepProgress = 50;
      this.updateProgress();

      // 步骤2: 执行优化
      this.currentStep = `第${iterationIndex}次迭代 - 优化中`;
      this.currentStepProgress = 50;
      this.updateProgress();
      this.notifyStepChange('optimization', iterationIndex);

      const optimizationResult = await this.executeWithTimeout(
        () => { return optimizeFunction(this.currentPromptText, evaluationResults); },
        '优化超时',
      );

      iterationResult.optimizedPrompt = optimizationResult.optimizedPrompt;
      iterationResult.optimizeReason = optimizationResult.optimizeReason;
      iterationResult.status = 'completed';
      this.currentStepProgress = 100;
      this.updateProgress();

      message.success(`第${iterationIndex}次迭代完成`);
    } catch (error) {
      iterationResult.status = 'failed';
      iterationResult.error = error.message;
      message.error(`第${iterationIndex}次迭代失败: ${error.message}`);
    } finally {
      iterationResult.endTime = new Date().toISOString();
      iterationResult.duration = new Date(iterationResult.endTime) - new Date(iterationResult.startTime);
    }

    return iterationResult;
  }

  /**
   * 带超时的执行函数
   * @param {Function} fn - 要执行的函数
   * @param {string} timeoutMessage - 超时消息
   * @returns {Promise} 执行结果
   */
  async executeWithTimeout(fn, timeoutMessage) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(timeoutMessage));
      }, this.stepTimeout);

      this.currentStepTimer = timer;

      Promise.resolve(fn())
        .then((result) => {
          clearTimeout(timer);
          this.currentStepTimer = null;
          resolve(result);
        })
        .catch((error) => {
          clearTimeout(timer);
          this.currentStepTimer = null;
          reject(error);
        });
    });
  }

  /**
   * 更新进度
   */
  updateProgress() {
    if (this.onProgressUpdate) {
      const overallProgress = this.totalIterations > 0 // eslint-disable-next-line
        ? Math.round(((this.currentIteration - 1) / this.totalIterations * 100) + (this.currentStepProgress / this.totalIterations))
        : 0;

      this.onProgressUpdate({
        isRunning: this.isRunning,
        isPaused: this.isPaused,
        currentIteration: this.currentIteration,
        totalIterations: this.totalIterations,
        currentStep: this.currentStep,
        currentStepProgress: this.currentStepProgress,
        overallProgress,
        iterationResults: this.iterationResults,
      });
    }
  }

  /**
   * 通知步骤变化
   * @param {string} stepType - 步骤类型
   * @param {number} iteration - 迭代次数
   */
  notifyStepChange(stepType, iteration) {
    if (this.onStepChange) {
      this.onStepChange({
        stepType,
        iteration,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * 生成操作摘要
   * @param {Object} operationRecord - 操作记录
   * @returns {Object} 操作摘要
   */
  generateOperationSummary(operationRecord) {
    const completedIterations = operationRecord.iterations.filter((iter) => { return iter.status === 'completed'; });
    const failedIterations = operationRecord.iterations.filter((iter) => { return iter.status === 'failed'; });

    const totalDuration = new Date(operationRecord.endTime) - new Date(operationRecord.startTime);
    const avgIterationDuration = completedIterations.length > 0
      ? completedIterations.reduce((sum, iter) => { return sum + iter.duration; }, 0) / completedIterations.length
      : 0;

    return {
      totalIterations: operationRecord.totalIterations,
      completedIterations: completedIterations.length,
      failedIterations: failedIterations.length,
      successRate: operationRecord.totalIterations > 0
        ? Math.round((completedIterations.length / operationRecord.totalIterations) * 100)
        : 0,
      totalDuration,
      avgIterationDuration,
      finalPromptText: this.currentPromptText,
      promptEvolution: operationRecord.iterations.map((iter) => {
        return {
          iteration: iter.iteration,
          promptLength: iter.promptText ? iter.promptText.length : 0,
          optimizedLength: iter.optimizedPrompt ? iter.optimizedPrompt.length : 0,
          hasOptimization: !!iter.optimizedPrompt,
        };
      }),
    };
  }

  /**
   * 取消批量操作
   */
  cancelOperation() {
    if (this.isRunning) {
      this.isCancelled = true;
      this.clearStepTimer();
      message.info('正在取消批量操作...');
    }
  }

  /**
   * 暂停批量操作
   */
  pauseOperation() {
    if (this.isRunning && !this.isPaused) {
      this.isPaused = true;
      message.info('批量操作已暂停');
      this.updateProgress();
    }
  }

  /**
   * 恢复批量操作
   */
  resumeOperation() {
    if (this.isRunning && this.isPaused) {
      this.isPaused = false;
      message.info('批量操作已恢复');
      this.updateProgress();
    }
  }

  /**
   * 清理步骤计时器
   */
  clearStepTimer() {
    if (this.currentStepTimer) {
      clearTimeout(this.currentStepTimer);
      this.currentStepTimer = null;
    }
  }

  /**
   * 获取当前状态
   * @returns {Object} 当前状态
   */
  getCurrentStatus() {
    return {
      isRunning: this.isRunning,
      isCancelled: this.isCancelled,
      isPaused: this.isPaused,
      currentIteration: this.currentIteration,
      totalIterations: this.totalIterations,
      currentStep: this.currentStep,
      currentStepProgress: this.currentStepProgress,
      currentPromptText: this.currentPromptText,
      iterationResults: this.iterationResults,
      operationHistory: this.operationHistory,
    };
  }

  /**
   * 获取操作历史
   * @returns {Array} 操作历史列表
   */
  getOperationHistory() {
    return this.operationHistory;
  }

  /**
   * 清理操作历史
   */
  clearHistory() {
    this.operationHistory = [];
  }

  /**
   * 睡眠函数
   * @param {number} ms - 毫秒数
   * @returns {Promise} Promise对象
   */
  sleep = (ms) => {
    return new Promise((resolve) => { return setTimeout(resolve, ms); });
  }
}
