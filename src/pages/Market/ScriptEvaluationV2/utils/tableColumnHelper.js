import React from 'react';

/**
 * 表格列配置工具类
 * 提供通用的表格列创建和配置方法
 */
export default class TableColumnHelper {
  /**
   * 通用的单列创建函数
   * @param {Object} config - 列配置对象
   * @param {string} config.title - 列标题
   * @param {string} config.dataIndex - 数据索引
   * @param {string} config.key - 列键值
   * @param {number} config.width - 列宽度，默认150
   * @param {string} config.align - 对齐方式，默认'left'
   * @param {boolean} config.ellipsis - 是否显示省略号，默认false
   * @param {Function} config.render - 自定义渲染函数
   * @param {boolean} config.customRender - 是否使用自定义渲染，默认false
   * @returns {Object} 表格列配置对象
   */
  static createTableColumn = (config) => {
    const {
      title,
      dataIndex,
      key,
      width = 150,
      align = 'left',
      ellipsis = false,
      render = null,
      customRender = false,
    } = config;

    const column = {
      title,
      dataIndex,
      key,
      width,
      align,
    };

    if (ellipsis) {
      column.ellipsis = true;
    }

    if (customRender && render) {
      column.render = render;
    } else if (!customRender) {
      column.render = (text) => { return text || '-'; };
    }

    return column;
  }

  /**
   * 批量字段列创建函数
   * @param {Array} fields - 字段数组
   * @param {string} prefix - 标题前缀，默认为空
   * @param {string} keyPrefix - 键值前缀，默认为空
   * @param {Object} options - 选项配置
   * @param {number} options.width - 列宽度，默认150
   * @param {string} options.align - 对齐方式，默认'center'
   * @param {Function} options.render - 渲染函数
   * @param {string} options.dataIndexPrefix - dataIndex前缀，用于字段名映射
   * @returns {Array} 表格列配置数组
   */
  static createColumnsFromFields = (fields, prefix = '', keyPrefix = '', options = {}) => {
    const defaultOptions = {
      width: 150,
      align: 'center',
      dataIndexPrefix: '', // 新增：dataIndex前缀选项
      render: (text) => {
        // 改进渲染逻辑，保持原始数据显示
        if (text === null || text === undefined) {
          return '-';
        }
        // 保持原始数据类型和值
        const displayValue = String(text).trim();
        return displayValue || '-';
      },
    };

    const finalOptions = { ...defaultOptions, ...options };

    return fields.map((field, index) => {
      const title = prefix ? `${prefix}-${field}` : field;
      const key = keyPrefix ? `${keyPrefix}_${index}` : `field_${index}`;
      // 根据dataIndexPrefix决定实际的数据索引字段名
      const dataIndex = finalOptions.dataIndexPrefix ? `${finalOptions.dataIndexPrefix}${field}` : field;

      return TableColumnHelper.createTableColumn({
        title,
        dataIndex,
        key,
        width: field.includes('裁决') ? 120 : finalOptions.width,
        align: finalOptions.align,
        render: finalOptions.render,
      });
    });
  }

  /**
   * 创建带提示的文本列渲染函数
   * @returns {Function} 渲染函数
   */
  static createTextColumnRender = () => {
    return (text) => {
      // 改进文本渲染，保持原始数据显示
      if (text === null || text === undefined) {
        return <span title="空值" style={{ cursor: 'pointer', color: '#ccc' }}>-</span>;
      }

      const displayText = String(text);
      return (
        <span title={displayText} style={{ cursor: 'pointer' }}>
          {displayText}
        </span>
      );
    };
  }

  /**
   * 批量创建标准化的基础文本列
   * @param {Array} columnConfigs - 列配置数组
   * @param {Object} options - 全局选项
   * @param {number} options.width - 统一列宽，默认200
   * @returns {Array} 标准化的列配置数组
   */
  static createBaseTextColumns = (columnConfigs, options = {}) => {
    const { width = 200 } = options;

    return columnConfigs.map((config) => {
      return TableColumnHelper.createTableColumn({
        title: config.title,
        dataIndex: config.dataIndex,
        key: config.key,
        width,
        ellipsis: true,
        render: TableColumnHelper.createTextColumnRender(),
      });
    });
  }
}
