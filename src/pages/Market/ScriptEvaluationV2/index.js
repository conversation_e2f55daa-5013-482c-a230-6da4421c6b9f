/* eslint-disable max-lines */
import './index.less';

import Engine, { Sessions } from '~/engine';
import { EVENT_TYPE } from '~/pages/Playground/Configs';
import ReconnectingWebSocket from '~/pages/Playground/WebSocket';
import { Platform, StringExtension } from '~/plugins';
import { <PERSON><PERSON>, Divider, Drawer, Modal, Spin, Tabs, message } from 'antd';
import * as JSONC from 'jsonc-parser';
import _ from 'lodash';
import qs from 'qs';
import React, { Component } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

import EvaluationPanel from './components/EvaluationPanel';
import FileManagementPanel from './components/FileManagementPanel';
import EvaluationService from './services/EvaluationService';

// 评估工作流配置
const FLOW_MAP = {
  stg: 'MW24mDbmQCtjavB80KjGvB',
  prod: '4bmA061WXThqj9qeJ1Sv3c',
};

// 修正工作流配置
const FIX_FLOW_MAP = {
  stg: '7pWPF6H6Es2nEyJ2XL5jQ0',
  prod: '3m6GJup7AAAbQtCSipXsAz',
};

// eslint-disable-next-line
const OUTPUT_FORMAT = '{"内容一致性":"[是 / 否]","语言风格一致性":"[是 / 否]","结构要求符合度":"[是 / 否]","表达是否合格":"[是 / 否]","最终裁决":"[是 / 否]","置信度":"[百分比]"}';
/**
 * ScriptEvaluationV2 组件
 * 包含两个独立的面板，每个面板有清晰的视觉分隔
 */
export default class ScriptEvaluationV2 extends Component {
  state = {
    // 组件状态初始化
    activeTabKey: '1', // 当前激活的标签页，默认为第一个
    // 评估相关状态
    isProcessing: false,
    totalItems: 0,
    completedItems: 0,
    evaluationResults: [],
    // 优化提示词相关状态
    isOptimizing: false,
    optimizedPrompt: '',
    optimizeReason: '',
    showOptimizeModal: false,
    originalPromptText: '', // 保存原始提示词用于对比
    // 预览优化数据相关状态
    showPreviewDrawer: false, // 显示预览抽屉
    isLoadingPreviewData: false, // 加载预览数据状态
    previewData: null, // 预览数据
  }

  /**
   * 组件挂载后执行
   */
  componentDidMount() {
    // 初始化评估服务
    this.evaluationService = new EvaluationService();
    this.setupEvaluationCallbacks();
  }

  /**
   * 组件卸载前执行
   */
  componentWillUnmount() {
    // 清理评估服务
    if (this.evaluationService) {
      this.evaluationService.stopEvaluation();
    }

    // 清理优化WebSocket连接
    this.cleanupOptimizeConnection();
  }
  // WebSocket连接管理
  optimizeWs = null;
  savedFormatFields = null;

  /**
   * 设置评估服务回调函数
   */
  setupEvaluationCallbacks() {
    if (this.evaluationService) {
      this.evaluationService.setCallbacks({
        onProgressUpdate: this.handleProgressUpdate,
        onResultUpdate: this.handleResultUpdate,
        onComplete: this.handleEvaluationComplete,
        onError: this.handleEvaluationError,
      });
    }
  }

  /**
   * 处理文件变化回调
   * @param {Array} files - 文件列表
   */
  handleFileChange = (files) => {
    // 如果需要在主组件中处理文件变化，可以在这里添加逻辑
    console.log('文件列表已更新:', files); // eslint-disable-line no-console
  }

  /**
   * 处理评估面板文件变化
   * @param {File} file - 选择的文件
   */
  handleEvaluationFileChange = (file) => {
    // 记录文件选择，可用于后续处理
    console.log('选择的文件:', file); // eslint-disable-line no-console
  }

  /**
   * 处理评估请求
   * @param {Object} params - 评估参数
   */
  handleEvaluate = (params) => {
    const { onComplete, promptInfo, fileInfo, excelData, headers, promptText } = params;

    // 如果已经在处理中，则返回
    if (this.state.isProcessing) {
      message.warning('正在处理中，请等待当前任务完成');
      return;
    }

    // 验证必要参数
    if (!excelData || excelData.length === 0) {
      message.warning('请先上传Excel文件！');
      return;
    }

    if (!promptText || !promptText.trim()) {
      message.warning('请输入评估提示词！');
      return;
    }

    // 保存 EvaluationPanel 的回调函数
    this.panelOnComplete = onComplete;

    // 开始评估
    message.info('开始评估文件，这可能需要一些时间...');

    // 调用评估服务，使用主组件的完成回调
    this.evaluationService.startEvaluation({
      excelData,
      headers,
      promptText,
      promptInfo,
      fileInfo,
      disableCache: Platform.isProd(), // 生产环境禁用缓存，确保结果准确性
    });
  }

  /**
   * 处理进度更新
   * @param {Object} progress - 进度信息
   */
  handleProgressUpdate = (progress) => {
    const { totalItems, completedItems, isProcessing } = progress;
    this.setState({
      totalItems,
      completedItems,
      isProcessing,
    });
  }

  /**
   * 处理结果更新
   * @param {Object} result - 单个评估结果
   * @param {number} itemIndex - 项目索引
   */
  handleResultUpdate = (result, itemIndex) => {
    this.setState((prevState) => {
      // 检查是否已经存在相同 itemIndex 的结果，避免重复添加
      const existingIndex = prevState.evaluationResults.findIndex((item) => { return item.itemIndex === itemIndex; });
      let newResults;

      if (existingIndex >= 0) {
        // 如果已存在，则更新该项
        newResults = [...prevState.evaluationResults];
        newResults[existingIndex] = { ...result, itemIndex };
      } else {
        // 如果不存在，则添加新项
        newResults = [...prevState.evaluationResults, { ...result, itemIndex }];
      }

      return {
        evaluationResults: newResults,
      };
    });
  }

  /**
   * 处理评估完成
   * @param {Object} data - 评估结果数据
   */
  handleEvaluationComplete = async (data) => {
    const { results, promptInfo, fileInfo } = data || {};
    console.log(results, promptInfo, fileInfo); // eslint-disable-line no-console
    // 更新主组件状态
    this.setState({
      evaluationResults: results || [],
      isProcessing: false,
    });

    // 调用 EvaluationPanel 的完成回调
    if (this.panelOnComplete && typeof this.panelOnComplete === 'function') {
      try {
        await this.panelOnComplete(data);
      } catch (error) {
        console.error('调用面板完成回调失败:', error); // eslint-disable-line no-console
      }
    }

    // 如果有结果且提供了云端信息，显示成功消息
    if (results && results.length > 0 && promptInfo && fileInfo) {
      message.success(`评估完成！共处理 ${results.length} 条数据，结果已保存到云端`);
    } else if (results && results.length > 0) {
      message.success(`评估完成！共处理 ${results.length} 条数据`);
    } else {
      message.success('评估完成！');
    }
  }

  /**
   * 处理评估错误
   * @param {Error} error - 错误信息
   */
  handleEvaluationError = (error) => {
    console.error('评估错误:', error); // eslint-disable-line no-console
    message.error(error.message || '评估处理失败，请重试！');
  }


  /**
   * 处理预览优化数据请求
   * @param {Object} params - 预览参数
   */
  handlePreviewOptimizeData = async (params) => {
    const { promptText, onOptimizeComplete } = params;

    if (!promptText.trim()) {
      message.warning('请先输入提示词！');
      return;
    }

    // 如果有回调函数，说明是批量操作调用，直接执行优化
    if (onOptimizeComplete) {
      try {
        // 生成预览数据
        const previewData = await this.generateFileGroupedHistoryData(promptText);

        // 直接执行优化
        const optimizedPrompt = await this.callOptimizeWorkflowFromPreview(previewData);

        // 调用回调函数
        onOptimizeComplete(optimizedPrompt, '批量操作优化');
      } catch (error) {
        console.error('批量优化失败:', error); // eslint-disable-line no-console
        throw error;
      }
      return;
    }

    // 注意：不再检查当前评估结果，因为优化基于历史数据
    // 检查是否有评估历史数据将在EvaluationPanel中进行

    this.setState({
      showPreviewDrawer: true,
      isLoadingPreviewData: true,
      previewData: null,
    });

    try {
      // 生成按文件分组的历史数据
      // 注意：现在只基于历史数据，不需要当前评估结果
      const previewData = await this.generateFileGroupedHistoryData(promptText);

      this.setState({
        previewData,
        isLoadingPreviewData: false,
      });

      message.success('预览数据生成完成！');
    } catch (error) {
      console.error('生成预览数据失败:', error); // eslint-disable-line no-console
      message.error('生成预览数据失败，请重试！');
      this.setState({
        isLoadingPreviewData: false,
        showPreviewDrawer: false,
      });
    }
  }

  /**
   * 关闭预览抽屉
   */
  closePreviewDrawer = () => {
    this.setState({
      showPreviewDrawer: false,
      previewData: null,
    });
  }

  /**
   * 从预览抽屉触发优化
   */
  handleOptimizeFromPreview = async () => {
    const { previewData } = this.state;

    if (!previewData || !previewData.markdownContent) {
      message.error('预览数据不完整，请重新生成预览数据');
      return;
    }

    if (this.state.isOptimizing) {
      message.warning('正在优化中，请等待当前任务完成');
      return;
    }

    try {
      // 从预览数据中提取当前提示词用于对比
      this.setState({
        isOptimizing: true,
        originalPromptText: previewData.currentPromptText, // 保存原始提示词用于对比
      });

      message.info('开始分析历史评估数据并优化提示词...');

      // 调用优化工作流，使用预览数据中的markdown内容
      const optimizedPrompt = await this.callOptimizeWorkflowFromPreview(previewData);

      // 关闭预览抽屉并显示优化结果
      this.setState({
        showPreviewDrawer: false,
        previewData: null,
        optimizedPrompt,
        showOptimizeModal: true,
        isOptimizing: false,
      });

      // 设置优化完成回调，用于更新EvaluationPanel中的提示词
      this.pendingOptimizeComplete = this.updateEvaluationPanelPrompt;

      message.success('提示词优化完成！');
    } catch (error) {
      console.error('提示词优化失败:', error); // eslint-disable-line no-console
      message.error(error.message || '提示词优化失败，请重试！');
      this.setState({ isOptimizing: false });
    }
  }

  /**
   * 更新EvaluationPanel中的提示词
   * @param {string} optimizedPrompt - 优化后的提示词
   */
  updateEvaluationPanelPrompt = async (optimizedPrompt) => {
    if (!optimizedPrompt) {
      return;
    }

    try {
      // 获取EvaluationPanel的引用并调用其handleOptimizeComplete方法
      if (this.evaluationPanelRef && this.evaluationPanelRef.handleOptimizeComplete) {
        await this.evaluationPanelRef.handleOptimizeComplete(optimizedPrompt);
      }
    } catch (error) {
      console.error('更新EvaluationPanel提示词失败:', error); // eslint-disable-line no-console
      message.warning('提示词优化完成，但更新界面失败');
    }
  }

  /**
   * 从预览数据调用优化工作流
   * @param {Object} previewData - 预览数据对象
   * @returns {Promise} 优化结果Promise
   */
  callOptimizeWorkflowFromPreview = async (previewData) => {
    return new Promise((resolve, reject) => {
      try {
        const flowId = FIX_FLOW_MAP[Platform.isProd() ? 'prod' : 'stg'];
        const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflow-v2/run/${flowId}`;
        const query = { access_token: Sessions.getToken() };

        const { formatFields } = this.extractFormatFields(previewData.currentPromptText || '');
        this.savedFormatFields = formatFields; // 保存格式处理字段以便后续使用

        const ws = new ReconnectingWebSocket(
          `${path}?${qs.stringify(query)}`,
          [],
          (e) => { this.onReceiveOptimizeMsg(e, resolve, reject); },
          () => {
            ws.send(JSON.stringify({
              text: JSON.stringify({
                old_prompt: previewData.currentPromptText,
                content: previewData.markdownContent,
              }),
              type: 'message',
              is_beta: false,
            }));
          },
        );

        // 保存WebSocket连接用于清理
        this.optimizeWs = ws;

        // 设置超时处理
        setTimeout(() => {
          if (this.optimizeWs) {
            this.cleanupOptimizeConnection();
            reject(new Error('提示词优化超时，请重试'));
          }
        }, 30 * 60 * 1000); // 30分钟超时
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 提取提示词末尾的格式处理字段
   * @param {string} promptText - 提示词文本
   * @returns {Object} 包含格式字段和纯净提示词的对象
   */
  extractFormatFields = (promptText) => {
    try {
      const lines = promptText.split('\n');
      if (lines.length < 2) return { formatFields: '', cleanPrompt: promptText };

      // 查找最后两行：JSON说明行和JSON字段定义行
      const lastLine = lines[lines.length - 1].trim();
      const secondLastLine = lines[lines.length - 2].trim();

      // 检查是否包含JSON格式定义
      if (lastLine.startsWith('{') && lastLine.endsWith('}') &&
        secondLastLine.includes('JSON') && secondLastLine.includes('json.loads')) {
        // 提取最后两行作为格式字段
        const formatFields = `${secondLastLine}\n${lastLine}`;
        // 移除最后两行，保留其余内容
        const cleanPrompt = lines.slice(0, -2).join('\n');
        return { formatFields, cleanPrompt };
      }

      return { formatFields: '', cleanPrompt: promptText };
    } catch (error) {
      console.warn('提取格式字段失败:', error); // eslint-disable-line no-console
      return { formatFields: '', cleanPrompt: promptText };
    }
  }

  /**
   * 将格式处理字段重新添加到提示词末尾
   * @param {string} promptText - 提示词文本
   * @param {string} formatFields - 格式字段
   * @returns {string} 包含格式字段的完整提示词
   */
  addFormatFields = (promptText, formatFields) => {
    if (!formatFields) return promptText;

    // 确保提示词末尾有换行符，然后添加格式字段
    const trimmedPrompt = promptText.trim();
    return `${trimmedPrompt}\n${formatFields}`;
  }


  /**
   * 生成按文件分组的历史数据（用于预览和优化）
   * @param {string} currentPromptText - 当前提示词
   * @returns {Object} 包含分组数据和markdown的对象
   */
  generateFileGroupedHistoryData = async (currentPromptText) => {
    try {
      // 1. 获取所有评估历史记录
      const EvaluationPanelModule = await import('./components/EvaluationPanel/index.js');
      const { CloudDataManager } = EvaluationPanelModule;
      const allRecords = await CloudDataManager.fetchEvaluationHistory();

      // 2. 按文件分组
      const fileGroups = this.groupRecordsByFile(allRecords);

      // 3. 生成markdown格式的数据
      const markdownContent = await this.generateFileGroupedMarkdown(
        fileGroups,
        currentPromptText,
      );

      return {
        fileGroups,
        markdownContent,
        totalFiles: Object.keys(fileGroups).length,
        totalRecords: allRecords.length,
        generatedAt: new Date().toISOString(),
        currentPromptText,
      };
    } catch (error) {
      console.error('生成文件分组历史数据失败:', error); // eslint-disable-line no-console
      throw new Error('获取历史数据失败，请检查网络连接');
    }
  }

  /**
   * 按文件对评估记录进行分组
   * @param {Array} records - 评估记录数组
   * @returns {Object} 按文件ID分组的记录
   */
  groupRecordsByFile = (records) => {
    const fileGroups = {};

    records.forEach((record) => {
      const fileName = record.file?.name || '未知文件';
      const fileId = record.file?.id || 'unknown';

      // 使用文件名作为主要分组键，避免同一文件因ID不同而重复
      const groupKey = fileName;

      if (!fileGroups[groupKey]) {
        fileGroups[groupKey] = {
          fileInfo: {
            id: fileId,
            name: fileName,
            url: record.file?.url,
          },
          records: [],
        };
      }

      fileGroups[groupKey].records.push(record);
    });

    // 按时间排序每个文件组内的记录
    Object.values(fileGroups).forEach((group) => {
      group.records.sort((a, b) => { return new Date(a.timestamp) - new Date(b.timestamp); });
    });

    return fileGroups;
  }

  /**
   * 生成按文件分组的markdown内容
   * @param {Object} fileGroups - 按文件分组的数据
   * @param {string} currentPromptText - 当前提示词
   * @returns {string} markdown格式的内容
   */
  generateFileGroupedMarkdown = async (fileGroups) => {
    let markdown = '';
    console.log('开始生成按文件分组的markdown内容', fileGroups); // eslint-disable-line no-console
    // 按文件生成历史数据
    const fileIds = Object.keys(fileGroups);
    if (fileIds.length === 0) {
      markdown += '## 历史数据\n\n暂无历史评估记录\n\n';
      return markdown;
    }

    markdown += '## 历史评估记录\n\n';

    // 处理每个文件
    for (let i = 0; i < fileIds.length; i++) {
      const fileId = fileIds[i];
      const group = fileGroups[fileId];
      const { fileInfo, records } = group;

      markdown += `## 文件 ${i + 1}:内容如下 \n\n`;

      // 1. 首先获取并显示Excel数据的markdown table（每个文件只显示一次）
      try {
        const excelMarkdown = await this.getExcelDataAsMarkdown(fileInfo); // eslint-disable-line no-await-in-loop
        markdown += `#### 数据内容与专家判定结果\n\n${excelMarkdown}\n\n`;
      } catch (error) {
        console.error('获取Excel数据失败:', error); // eslint-disable-line no-console
        markdown += `#### Excel数据内容\n\n无法获取Excel数据: ${error.message}\n\n`;
      }

      // 2. 然后按版本显示提示词和AI判定结果
      records.forEach((record, recordIndex) => { // eslint-disable-line no-loop-func
        const { prompt, evaluation } = record;

        markdown += `#### 提示词版本${recordIndex + 1}\n\n`;

        // 提示词内容
        if (prompt && prompt.content) {
          markdown += `**提示词内容：**\n\`\`\`\n${prompt.content}\n\`\`\`\n\n`;
        }

        // AI评估结果表格
        if (evaluation && evaluation.results && evaluation.results.length > 0) {
          const resultsMarkdown = this.convertEvaluationResultsToMarkdown(evaluation.results);
          markdown += `**AI判定结果：**\n\n${resultsMarkdown}\n\n`;
        }

        markdown += '---\n\n';
      });
    }

    return markdown;
  }

  /**
   * 获取Excel数据并转换为markdown表格
   * @param {Object} fileInfo - 文件信息
   * @returns {string} Excel数据的markdown表格
   */
  getExcelDataAsMarkdown = async (fileInfo) => {
    try {
      // 从文件URL获取Excel数据
      const EvaluationPanelModule = await import('./components/EvaluationPanel/index.js');
      const EvaluationPanelClass = EvaluationPanelModule.default;

      // 创建临时实例来使用readExcelFromUrl方法
      const tempInstance = new EvaluationPanelClass({});
      const excelData = await tempInstance.readExcelFromUrl(fileInfo.url);

      if (!excelData || !excelData.headers || !excelData.rows) {
        return '无法读取Excel数据';
      }

      return this.convertExcelToMarkdownTable(excelData.headers, excelData.rows);
    } catch (error) {
      throw new Error(`读取Excel文件失败: ${error.message}`);
    }
  }

  /**
   * 将Excel数据转换为markdown表格
   * @param {Array} headers - 表头
   * @param {Array} rows - 数据行
   * @returns {string} markdown表格
   */
  convertExcelToMarkdownTable = (headers, rows) => {
    if (!headers || headers.length === 0) {
      return '表头为空';
    }

    if (!rows || rows.length === 0) {
      return '数据为空';
    }

    // 构建表头
    let markdown = `| ${headers.map((header) => { return header || ''; }).join(' | ')} |\n`;
    markdown += `| ${headers.map(() => { return '---'; }).join(' | ')} |\n`;

    // 构建数据行
    rows.forEach((row) => {
      const cells = headers.map((_a, index) => {
        const cellValue = row[index];
        if (cellValue === null || cellValue === undefined || cellValue === '') {
          return '-';
        }
        // 转义markdown特殊字符
        return String(cellValue).replace(/\|/g, '\\|').replace(/\n/g, ' ').replace(/\r/g, ' ')
          .trim();
      });
      markdown += `| ${cells.join(' | ')} |\n`;
    });

    return markdown;
  }

  /**
   * 删除结果数组中所有对象类型的key
   * @param {Array} results - 原始结果数组
   * @returns {Array} 清理后的结果数组
   */
  removeObjectKeysFromResults = (results) => {
    if (!results || !Array.isArray(results)) {
      return results;
    }

    return results.map((result) => {
      if (!result || typeof result !== 'object') {
        return result;
      }

      const cleanedResult = {};
      Object.keys(result).forEach((key) => {
        const value = result[key];
        // 只保留非对象类型的值（包括字符串、数字、布尔值、null、undefined）
        if (!_.isObject(value) && !_.isObjectLike(value)) {
          cleanedResult[key] = value;
        }
        if (value.length < 10) {
          cleanedResult[key] = value;
        }
      });

      return cleanedResult;
    });
  }


  /**
   * 将AI评估结果转换为markdown表格
   * @param {Array} results - 评估结果数组
   * @returns {string} markdown表格
   */
  convertEvaluationResultsToMarkdown = (results) => {
    if (!results || results.length === 0) {
      return '无评估结果';
    }
    const cleanedResult = this.removeObjectKeysFromResults(results);
    // 获取所有可能的字段
    const allFields = new Set();
    cleanedResult.forEach((result) => {
      Object.keys(result).forEach((key) => {
        if (key !== 'itemIndex' && key !== 'id'
          && !_.isObject(result[key]) && !_.isObjectLike(result[key])
          && result[key].length < 10
        ) {
          allFields.add(key);
        }
      });
    });

    const fields = Array.from(allFields);
    if (fields.length === 0) {
      return '无有效评估字段';
    }

    // 构建表头
    let markdown = `| 序号 | ${fields.join(' | ')} |\n`;
    markdown += `| --- | ${fields.map(() => { return '---'; }).join(' | ')} |\n`;

    // 构建数据行
    results.forEach((result, index) => {
      const cells = fields.map((field) => {
        const value = result[field];
        if (value === null || value === undefined || value === '') {
          return '-';
        }
        // 转义markdown特殊字符并限制长度
        const stringValue = String(value).replace(/\|/g, '\\|').replace(/\n/g, ' ').replace(/\r/g, ' ')
          .trim();
        return stringValue.length > 50 ? `${stringValue.substring(0, 47)}...` : stringValue;
      });
      markdown += `| ${index + 1} | ${cells.join(' | ')} |\n`;
    });

    return markdown;
  }

  /**
   * 将评估结果转换为markdown格式
   * @param {Array} evaluationResults - 评估结果数组
   * @returns {string} markdown格式的数据
   */
  convertOriginalDataToMarkdown = (evaluationResults) => {
    if (!evaluationResults || evaluationResults.length === 0) {
      return '暂无评估数据';
    }

    let markdown = '# 评估结果数据\n\n';

    evaluationResults.forEach((result, index) => {
      markdown += `## 数据项 ${index + 1}\n\n`;

      // 基本信息
      markdown += `**评估结果：** ${result.success ? '✅ 成功' : '❌ 失败'}\n\n`;

      if (result.reason) {
        markdown += `**原因：** ${result.reason}\n\n`;
      }

      // 如果有详细的评估字段，添加它们
      Object.keys(result).forEach((key) => {
        if (key !== 'success' && key !== 'reason' && key !== 'id') {
          const value = result[key] || '';
          if (typeof value === 'string' && value.length > 100) {
            markdown += `**${key}:**\n\`\`\`\n${value}\n\`\`\`\n\n`;
          } else {
            markdown += `**${key}:** ${value}\n\n`;
          }
        }
      });

      markdown += '---\n\n';
    });

    return markdown;
  }

  /**
   * 清理优化WebSocket连接
   */
  cleanupOptimizeConnection = () => {
    if (this.optimizeWs) {
      try {
        this.optimizeWs.close();
      } catch (error) {
        console.warn('关闭优化连接时出错:', error); // eslint-disable-line no-console
      }
      this.optimizeWs = null;
    }
    // 清理临时保存的格式字段
    this.savedFormatFields = null;
  }

  /**
   * 计算文本差异
   * @param {string} oldText - 原始文本
   * @param {string} newText - 新文本
   * @returns {Array} 差异结果数组
   */
  calculateTextDiff = (oldText, newText) => {
    const oldLines = oldText.split('\n');
    const newLines = newText.split('\n');
    const diffResult = [];

    let oldIndex = 0;
    let newIndex = 0;

    while (oldIndex < oldLines.length || newIndex < newLines.length) {
      const oldLine = oldLines[oldIndex];
      const newLine = newLines[newIndex];

      if (oldIndex >= oldLines.length) {
        // 只剩新行，标记为新增
        diffResult.push({ type: 'added', content: newLine, lineNumber: newIndex + 1 });
        newIndex++;
      } else if (newIndex >= newLines.length) {
        // 只剩旧行，标记为删除
        diffResult.push({ type: 'deleted', content: oldLine, lineNumber: oldIndex + 1 });
        oldIndex++;
      } else if (oldLine === newLine) {
        // 行相同，标记为未变更
        diffResult.push({ type: 'unchanged', content: oldLine, lineNumber: oldIndex + 1 });
        oldIndex++;
        newIndex++;
      } else {
        // 行不同，检查是否是修改还是删除/新增
        const nextOldLine = oldLines[oldIndex + 1];
        const nextNewLine = newLines[newIndex + 1];

        if (nextOldLine === newLine) {
          // 旧行被删除
          diffResult.push({ type: 'deleted', content: oldLine, lineNumber: oldIndex + 1 });
          oldIndex++;
        } else if (nextNewLine === oldLine) {
          // 新行被插入
          diffResult.push({ type: 'added', content: newLine, lineNumber: newIndex + 1 });
          newIndex++;
        } else {
          // 行被修改
          diffResult.push({ type: 'modified', content: newLine, oldContent: oldLine, lineNumber: oldIndex + 1 });
          oldIndex++;
          newIndex++;
        }
      }
    }

    return diffResult;
  }

  /**
   * 处理标签页切换
   * @param {string} key - 标签页的key
   */
  handleTabChange = (key) => {
    this.setState({ activeTabKey: key });
  }

  /**
   * 处理优化工作流消息
   * @param {Event} event - WebSocket事件
   * @param {Function} resolve - Promise resolve函数
   * @param {Function} reject - Promise reject函数
   */
  onReceiveOptimizeMsg = (event, resolve, reject) => {
    if (event?.data !== 'pong') {
      try {
        const originData = JSON.parse(event.data);
        const { type, data } = StringExtension.snakeToCamelObj(originData);

        if (type === EVENT_TYPE.FINAL_RESULT) {
          try {
            const { output } = JSON.parse(data?.output);
            let str = output; // 获取代码块内容
            if (output.split('```')[1]) {
              str = output.split('```').join('```'); // 去除代码块标记
            }
            if (_.isUndefined(JSONC.parse(str)) || JSONC.parse(str) === 1) {
              str = output.split('```')[1];
            }

            const { new_prompt, reason } = JSONC.parse(str || '{}'); // eslint-disable-line
            const lastLine = new_prompt?.split('\n').pop() || '';
            let finalOutput = new_prompt || ''; // eslint-disable-line
            if (lastLine.startsWith('{') && lastLine.endsWith('}')) {
              // 如果最后一行是JSON格式，移除它
              finalOutput = new_prompt.slice(0, -lastLine.length).trim();
            }
            finalOutput += OUTPUT_FORMAT;


            // // 重新添加之前保存的格式处理字段
            // const finalOutput = this.savedFormatFields
            //   ? this.addFormatFields(new_prompt || '', this.savedFormatFields) // eslint-disable-line
            //   : (new_prompt || '');// eslint-disable-line

            // 保存优化原因到状态，并在状态更
            // 新完成后resolve Promise
            this.setState({ optimizeReason: reason || '' }, () => {
              // 清理连接和临时变量
              this.cleanupOptimizeConnection();

              // 解析Promise，返回包含格式字段的完整提示词
              resolve(finalOutput);
            });
          } catch (error) {
            console.error('优化结果解析失败:', error); // eslint-disable-line no-console
            this.cleanupOptimizeConnection();
            reject(new Error('优化结果解析失败'));
          }
        } else if (type === EVENT_TYPE.EXEC_FAILED) {
          console.error('优化工作流执行失败:', data); // eslint-disable-line no-console
          this.cleanupOptimizeConnection();
          reject(new Error('优化工作流执行失败'));
        }
      } catch (error) {
        console.error('优化消息解析失败:', error); // eslint-disable-line no-console
        this.cleanupOptimizeConnection();
        reject(new Error('优化消息解析失败'));
      }
    }
  }

  /**
   * 应用优化后的提示词
   */
  onApplyOptimizedPrompt = async () => {
    const { optimizedPrompt } = this.state;

    // 关闭modal
    this.setState({
      showOptimizeModal: false,
    });

    // 调用保存的完成回调
    if (this.pendingOptimizeComplete && typeof this.pendingOptimizeComplete === 'function') {
      try {
        await this.pendingOptimizeComplete(optimizedPrompt);
      } catch (error) {
        console.error('调用优化完成回调失败:', error); // eslint-disable-line no-console
      }
    }

    // 清理回调引用
    this.pendingOptimizeComplete = null;

    message.success('提示词已更新！请重新评估以获取新版本的AI判定结果。');
  }

  /**
   * 关闭优化弹窗
   */
  onCloseOptimizeModal = () => {
    this.setState({
      showOptimizeModal: false,
      optimizedPrompt: '',
      optimizeReason: '',
      originalPromptText: '',
    });

    // 清理回调引用
    this.pendingOptimizeComplete = null;
  }

  /**
   * 打开工作流页面
   * @param {boolean} isFix - 是否为修正工作流，默认为false（评估工作流）
   */
  onOpenWorkflow = (isFix = false) => {
    const flowMap = isFix ? FIX_FLOW_MAP : FLOW_MAP;
    const flowId = flowMap[Platform.isProd() ? 'prod' : 'stg'];
    window.open(`/workflow-v2/${flowId}`, '_blank');
  }

  /**
   * 渲染差异对比组件
   * @param {string} oldText - 原始文本
   * @param {string} newText - 新文本
   * @returns {JSX.Element} 差异对比组件
   */
  renderDiffComparison = (oldText, newText) => {
    const diffResult = this.calculateTextDiff(oldText, newText);

    return (
      <div className="diff-container">
        {diffResult.map((line, index) => {
          const key = `diff-line-${index}`;

          switch (line.type) {
            case 'deleted':
              return (
                <div key={key} className="diff-line diff-deleted">
                  <span className="diff-line-number">-{line.lineNumber}</span>
                  <span className="diff-content">{line.content}</span>
                </div>
              );
            case 'added':
              return (
                <div key={key} className="diff-line diff-added">
                  <span className="diff-line-number">+{line.lineNumber}</span>
                  <span className="diff-content">{line.content}</span>
                </div>
              );
            case 'modified':
              return (
                <div key={key}>
                  <div className="diff-line diff-deleted">
                    <span className="diff-line-number">-{line.lineNumber}</span>
                    <span className="diff-content">{line.oldContent}</span>
                  </div>
                  <div className="diff-line diff-added">
                    <span className="diff-line-number">+{line.lineNumber}</span>
                    <span className="diff-content">{line.content}</span>
                  </div>
                </div>
              );
            case 'unchanged':
              return (
                <div key={key} className="diff-line diff-unchanged">
                  <span className="diff-line-number">{line.lineNumber}</span>
                  <span className="diff-content">{line.content}</span>
                </div>
              );
            default:
              return null;
          }
        })}
      </div>
    );
  }

  /**
   * 渲染优化提示词弹窗
   * @returns {JSX.Element} 优化modal组件
   */
  renderOptimizeModal = () => {
    const { showOptimizeModal, optimizedPrompt, originalPromptText, optimizeReason } = this.state;

    return (
      <Modal
        title="提示词优化结果"
        open={showOptimizeModal}
        onCancel={this.onCloseOptimizeModal}
        width={1200}
        footer={[
          <Button key="cancel" onClick={this.onCloseOptimizeModal}>
            取消
          </Button>,
          <Button key="apply" type="primary" onClick={this.onApplyOptimizedPrompt}>
            应用修正
          </Button>,
        ]}
        className="optimize-prompt-modal"
      >
        {/* 优化原因展示 */}
        {optimizeReason && (
          <div style={{ marginBottom: 20 }}>
            <h4 style={{ marginBottom: 8, color: '#1890ff', fontSize: 16 }}>
              <span style={{ marginRight: 8 }} role="img" aria-label="灯泡">💡</span>
              优化原因：
            </h4>
            <div style={{
              background: '#e6f7ff',
              padding: 16,
              borderRadius: 8,
              border: '1px solid #91d5ff',
              fontSize: 14,
              lineHeight: 1.6,
              color: '#0050b3',
            }}
            >
              <ReactMarkdown remarkPlugins={[remarkGfm]}>{optimizeReason}</ReactMarkdown>
            </div>
          </div>
        )}

        {/* 差异对比展示 */}
        <div style={{ marginBottom: 16 }}>
          <h4 style={{ marginBottom: 12, color: '#333', fontSize: 16 }}>
            <span style={{ marginRight: 8 }} role="img" aria-label="对比">🔄</span>
            提示词对比：
          </h4>
          <div style={{
            border: '1px solid #d9d9d9',
            borderRadius: 8,
            maxHeight: 400,
            overflow: 'auto',
            background: '#fafafa',
          }}
          >
            {this.renderDiffComparison(originalPromptText, optimizedPrompt)}
          </div>
        </div>

        <div style={{
          marginTop: 16,
          padding: 12,
          background: '#fff7e6',
          borderRadius: 6,
          border: '1px solid #ffd591',
        }}
        >
          <p style={{ margin: 0, fontSize: 13, color: '#d46b08' }}>
            <strong>提示：</strong>点击&quot;应用修正&quot;将使用优化后的提示词替换当前提示词。红色表示删除，绿色表示新增，黄色表示修改。
          </p>
        </div>
      </Modal>
    );
  }

  /**
   * 渲染预览优化数据抽屉
   * @returns {JSX.Element} 预览抽屉组件
   */
  renderPreviewDrawer = () => {
    const { showPreviewDrawer, isLoadingPreviewData, previewData } = this.state;

    const renderContent = () => {
      if (isLoadingPreviewData) {
        return (
          <div style={{ textAlign: 'center', padding: '40px 20px' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16, fontSize: '16px', color: '#666' }}>
              正在生成预览数据...
            </div>
          </div>
        );
      }

      if (!previewData) {
        return (
          <div style={{ textAlign: 'center', padding: '40px 20px', color: '#999' }}>
            暂无预览数据
          </div>
        );
      }

      return (
        <div style={{ flex: 1, overflow: 'auto' }}>
          {/* 数据概览 */}
          <div style={{ marginBottom: 24, padding: 16, background: '#f5f5f5', borderRadius: 6 }}>
            <h3 style={{ margin: 0, marginBottom: 12 }}>数据概览</h3>
            <div style={{ display: 'flex', gap: 24 }}>
              <div>总文件数: <strong>{previewData.totalFiles}</strong></div>
              <div>总记录数: <strong>{previewData.totalRecords}</strong></div>
              <div>生成时间: <strong>{new Date(previewData.generatedAt).toLocaleString('zh-CN')}</strong></div>
            </div>
          </div>

          {/* Markdown预览 */}
          <div style={{ flex: 1 }}>
            <h3>将发送给AI的Markdown数据</h3>
            <pre style={{
              background: '#f5f5f5',
              padding: 16,
              borderRadius: 6,
              fontSize: 12,
              whiteSpace: 'pre-wrap',
              maxHeight: '65vh',
              overflow: 'auto',
            }}
            >
              <ReactMarkdown remarkPlugins={[remarkGfm]}>{previewData.markdownContent}</ReactMarkdown>
            </pre>
          </div>

          {/* 操作按钮区域 */}
          <div style={{
            marginTop: 24,
            padding: '16px 0',
            borderTop: '1px solid #f0f0f0',
            display: 'flex',
            justifyContent: 'flex-end',
            gap: 12,
          }}
          >
            <Button onClick={this.closePreviewDrawer}>
              取消
            </Button>
            <Button
              type="primary"
              onClick={this.handleOptimizeFromPreview}
              loading={this.state.isOptimizing}
              disabled={this.state.isOptimizing}
            >
              {this.state.isOptimizing ? '优化中...' : '开始优化'}
            </Button>
          </div>
        </div>
      );
    };

    return (
      <Drawer
        title="预览优化数据"
        placement="right"
        width="80vw"
        open={showPreviewDrawer}
        onClose={this.closePreviewDrawer}
        className="preview-optimize-data-drawer"
      >
        <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          {renderContent()}
        </div>
      </Drawer>
    );
  }

  /**
 * 渲染组件
 * @returns {JSX.Element} 组件的JSX
 */
  render() {
    const { activeTabKey, isProcessing, isOptimizing, totalItems, completedItems, evaluationResults } = this.state;

    return (
      <div className="script-evaluation-v2-container" style={{ padding: '30px', background: '#fff' }}>
        <Tabs
          activeKey={activeTabKey}
          onChange={this.handleTabChange}
          type="card"
          style={{ height: '100%' }}
        >
          <Tabs.TabPane tab="文稿评估" key="1">
            <EvaluationPanel
              ref={(ref) => { this.evaluationPanelRef = ref; }}
              onFileChange={this.handleEvaluationFileChange}
              onEvaluate={this.handleEvaluate}
              onPreviewOptimizeData={this.handlePreviewOptimizeData}
              onOpenWorkflow={this.onOpenWorkflow}
              isProcessing={isProcessing}
              isOptimizing={isOptimizing}
              totalItems={totalItems}
              completedItems={completedItems}
              evaluationResults={evaluationResults}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="文件管理" key="2">
            <FileManagementPanel
              onFileChange={this.handleFileChange}
            />
          </Tabs.TabPane>
        </Tabs>
        <Divider />

        {/* 提示词优化弹窗 */}
        {this.renderOptimizeModal()}

        {/* 预览优化数据抽屉 */}
        {this.renderPreviewDrawer()}
      </div>
    );
  }
}
