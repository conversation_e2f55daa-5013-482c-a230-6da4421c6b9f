.script-evaluation-v2-container {
  .panel-card {
    height: 100%;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      background-color: #fafafa;
    }
  }

  .evaluation-panel {
    padding: 20px;

    .file-selector {
      margin-bottom: 20px;

      .ant-upload-drag {
        border: 2px dashed #d9d9d9;
        background: #fafafa;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: #f0f8ff;
          border-color: #1890ff;
        }

        .ant-upload-drag-icon {
          margin-bottom: 16px;
          font-size: 48px;
          color: #1890ff;
        }

        .ant-upload-text {
          margin-bottom: 8px;
          font-size: 16px;
          color: #666;
        }

        .ant-upload-hint {
          font-size: 14px;
          color: #999;
        }
      }

      .file-info {
        margin-top: 8px;
        color: #52c41a;
      }
    }

    .prompt-input {
      margin-bottom: 20px;

      .ant-form-item-label > label {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .ant-input {
        border: 1px solid #d9d9d9;
        border-radius: 6px;

        &:focus,
        &:hover {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }

    .action-buttons {
      margin-bottom: 20px;
      text-align: center;

      .ant-btn {
        min-width: 150px;
        height: 40px;
        margin: 0 10px;
        font-size: 16px;
        font-weight: 500;
      }

      .processing-info {
        margin-top: 10px;
        color: #1890ff;
      }
    }

    .results-table {
      margin-top: 20px;

      .results-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }
      }

      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
        color: #333;
      }
    }
  }

  // 优化提示词弹窗样式
  .optimize-prompt-modal {
    .ant-modal-body {
      padding: 24px;
    }

    .ant-modal-footer {
      padding: 16px 24px;
      border-top: 1px solid #f0f0f0;
    }
  }

  // Diff 对比样式
  .diff-container {
    background: #fff;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;

    .diff-line {
      display: flex;
      align-items: flex-start;
      min-height: 20px;
      padding: 2px 8px;
      border-left: 3px solid transparent;

      .diff-line-number {
        display: inline-block;
        flex-shrink: 0;
        width: 50px;
        margin-right: 12px;
        text-align: right;
        font-weight: 500;
        color: #666;
        user-select: none;
      }

      .diff-content {
        flex: 1;
        white-space: pre-wrap;
        word-break: break-word;
      }

      &.diff-deleted {
        background-color: #ffebee;
        border-left-color: #f44336;

        .diff-line-number {
          color: #c62828;
        }

        .diff-content {
          color: #c62828;
          text-decoration: line-through;
        }
      }

      &.diff-added {
        background-color: #e8f5e8;
        border-left-color: #4caf50;

        .diff-line-number {
          color: #2e7d32;
        }

        .diff-content {
          color: #2e7d32;
        }
      }

      &.diff-modified {
        background-color: #fff3e0;
        border-left-color: #ff9800;

        .diff-line-number {
          color: #ef6c00;
        }

        .diff-content {
          color: #ef6c00;
        }
      }

      &.diff-unchanged {
        background-color: #fff;
        border-left-color: transparent;

        .diff-line-number {
          color: #999;
        }

        .diff-content {
          color: #333;
        }
      }

      &:hover {
        background-color: rgba(0, 0, 0, 0.02);
      }
    }
  }
}
