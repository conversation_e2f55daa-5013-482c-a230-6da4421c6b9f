.evaluation-history-drawer {
  .ant-drawer-body {
    padding: 24px;
  }

  .ant-list-item {
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .ant-list-item-meta-title {
      margin-bottom: 8px;

      .ant-tag {
        margin-left: 8px;
      }
    }

    .ant-list-item-meta-description {
      .ant-typography {
        margin-bottom: 8px;
      }
    }

    .ant-list-item-action {
      margin-left: 16px;
    }
  }

  .ant-card {
    .ant-card-head {
      min-height: 40px;
      padding: 0 16px;

      .ant-card-head-title {
        font-size: 14px;
        font-weight: 500;
      }
    }

    .ant-card-body {
      padding: 16px;
    }
  }

  .ant-empty {
    padding: 40px 20px;
  }

  // 分析报告样式
  .analysis-report-section {
    // 统计卡片网格布局
    .analysis-grid {
      display: grid;
      margin-top: 8px;
      gap: 8px;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

      .analysis-card {
        padding: 8px;
        border: 1px solid #d9d9d9;
        background: #fff;
        font-size: 12px;
        border-radius: 4px;
        transition: box-shadow 0.2s;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .field-name {
          margin-bottom: 4px;
          font-size: 12px;
          font-weight: bold;
          color: #262626;
        }

        .field-accuracy {
          margin-bottom: 2px;

          .accuracy-value {
            margin-left: 4px;
            font-weight: bold;
          }
        }

        .field-details {
          font-size: 11px;
          color: #666;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .ant-drawer-content-wrapper {
      width: 90vw !important;
    }

    .ant-list-item {
      .ant-list-item-action {
        margin-left: 8px;

        .ant-btn {
          padding: 4px 8px;
          font-size: 12px;
        }
      }
    }

    .ant-card .ant-card-body {
      padding: 12px;

      .ant-space {
        flex-direction: column;
        align-items: flex-start;

        .ant-space-item {
          margin-bottom: 8px;
        }
      }
    }
  }
}
