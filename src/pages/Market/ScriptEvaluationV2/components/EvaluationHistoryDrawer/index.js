import './index.less';

import { CopyOutlined, DeleteOutlined } from '@ant-design/icons';
import { Button, Drawer, List, Popconfirm, Space, Typography, message } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import * as XLSX from 'xlsx';

const { Text } = Typography;

/**
 * 评估历史记录抽屉组件
 * 展示用户的评估历史记录，支持删除操作
 */
export default class EvaluationHistoryDrawer extends Component {
  static propTypes = {
    visible: PropTypes.bool,
    onClose: PropTypes.func,
    onDeleteRecord: PropTypes.func,
    records: PropTypes.array,
    loading: PropTypes.bool,
  }

  static defaultProps = {
    visible: false,
    onClose: () => { },
    onDeleteRecord: () => { },
    records: [],
    loading: false,
  }

  constructor(props) {
    super(props);
    this.state = {
      // Excel 数据缓存，以 filename 为键
      excelDataCache: {},
      // 正在加载的文件名集合
      loadingFiles: new Set(),
      // 统计数据缓存，以 recordId 为键
      statisticsCache: {},
      // 正在加载统计的记录ID集合
      loadingStatistics: new Set(),
    };
  }

  /**
   * 组件卸载时清理缓存，防止内存泄漏
   */
  componentWillUnmount() {
    this.setState({
      excelDataCache: {},
      loadingFiles: new Set(),
      statisticsCache: {},
      loadingStatistics: new Set(),
    });
  }

  /**
   * 格式化时间显示
   * @param {string} timestamp - 时间戳
   * @returns {string} 格式化后的时间
   */
  formatTime = (timestamp) => {
    return moment(timestamp).format('YYYY-MM-DD HH:mm');
  }

  /**
   * 复制提示词内容到剪贴板
   * @param {string} content - 提示词内容
   */
  copyPromptContent = (content) => {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(content).then(() => {
        message.success('提示词已复制到剪贴板');
      }).catch(() => {
        this.fallbackCopyTextToClipboard(content);
      });
    } else {
      this.fallbackCopyTextToClipboard(content);
    }
  }

  /**
   * 兼容性复制方法
   * @param {string} text - 要复制的文本
   */
  fallbackCopyTextToClipboard = (text) => {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
      const successful = document.execCommand('copy');
      if (successful) {
        message.success('提示词已复制到剪贴板');
      } else {
        message.error('复制失败，请手动复制');
      }
    } catch (err) {
      message.error('复制失败，请手动复制');
    }
    document.body.removeChild(textArea);
  }

  /**
   * 获取成功率颜色
   * @param {number} successRate - 成功率
   * @returns {string} 颜色值
   */
  getSuccessRateColor = (successRate) => {
    if (successRate >= 90) return '#52c41a';
    if (successRate >= 80) return '#1890ff';
    if (successRate >= 70) return '#faad14';
    return '#f5222d';
  }

  /**
   * 从URL下载并读取Excel文件
   * @param {string} url - 文件URL
   * @returns {Promise<Object>} Excel数据
   */
  readExcelFromUrl = async (url) => {
    return new Promise((resolve, reject) => {
      fetch(url)
        .then((response) => {
          if (!response.ok) {
            throw new Error('文件下载失败');
          }
          return response.arrayBuffer();
        })
        .then((arrayBuffer) => {
          try {
            const data = new Uint8Array(arrayBuffer);
            const workbook = XLSX.read(data, { type: 'array' });
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];

            // 保持原始数据类型，不进行自动转换
            const jsonData = XLSX.utils.sheet_to_json(worksheet, {
              header: 1,
              raw: true, // 保持原始数据类型
              defval: undefined, // 空单元格使用undefined而不是空字符串
            });

            if (jsonData.length === 0) {
              reject(new Error('Excel文件为空'));
              return;
            }

            const headers = jsonData[0];
            const rows = jsonData.slice(1).filter((row) => {
              return row.some((cell) => {
                return cell !== undefined && cell !== null && cell !== '';
              });
            });

            resolve({
              headers,
              rows,
            });
          } catch (parseError) {
            reject(new Error('Excel文件解析失败'));
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * 处理Excel数据，转换为标准格式
   * @param {Array} excelData - Excel原始数据
   * @param {Array} headers - 表头
   * @returns {Array} 处理后的数据
   */
  processExcelData = (excelData, headers) => {
    if (!excelData || !headers) {
      return [];
    }

    try {
      return excelData.map((row, index) => {
        const item = {
          id: index + 1,
          originalData: {},
        };

        // 将行数据映射到对应的表头
        headers.forEach((header, headerIndex) => {
          if (header && header.trim()) {
            const cellValue = row[headerIndex];
            // 保持原始数据类型和格式
            item.originalData[header] = cellValue !== undefined ? cellValue : '';
            item[header] = cellValue !== undefined ? cellValue : '';
          }
        });

        return item;
      });
    } catch (error) {
      console.error('处理Excel数据失败:', error); // eslint-disable-line no-console
      return [];
    }
  }

  /**
   * 提取专家评判字段
   * @param {Array} headers - Excel表头
   * @returns {Array} 专家评判字段列表
   */
  extractExpertFields = (headers) => {
    const requiredFields = ['内容参考', '风格参考', '结构要求', '最终输出'];

    // 过滤掉必需字段，剩余的就是专家评判字段
    const expertFields = headers.filter((header) => {
      return !requiredFields.includes(header) && header && header.trim() !== '';
    });

    // 验证字段名称安全性
    const validExpertFields = expertFields.filter((field) => {
      // 简单的字段名验证，防止注入
      return typeof field === 'string' && field.length > 0 && field.length < 100;
    });

    return validExpertFields;
  }

  /**
   * 获取评估结果的动态字段
   * @param {Array} results - 评估结果数组
   * @returns {Array} 字段名称数组
   */
  getEvaluationFields = (results) => {
    if (!results || results.length === 0) {
      return [];
    }

    // 从第一个结果中提取字段，排除系统字段
    const firstResult = results[0];
    const excludeFields = ['itemIndex', 'timestamp', 'success', 'details', 'parseError', 'content'];
    const keys = Object.keys(firstResult).filter((field) => { return !excludeFields.includes(field); });
    keys.forEach((field, index) => {
      if (_.isObject(firstResult[field])) {
        delete keys[index];
      }
    });

    return keys.filter(Boolean);
  }

  /**
   * 获取或加载Excel数据缓存
   * @param {string} fileName - 文件名
   * @param {string} fileUrl - 文件URL
   * @returns {Promise<Object>} 缓存的Excel数据
   */
  getOrLoadExcelData = async (fileName, fileUrl) => {
    const { excelDataCache, loadingFiles } = this.state;

    // 检查缓存是否存在
    if (excelDataCache[fileName]) {
      return excelDataCache[fileName];
    }

    // 检查是否正在加载
    if (loadingFiles.has(fileName)) {
      // 等待加载完成
      return new Promise((resolve) => {
        const checkCache = () => {
          if (excelDataCache[fileName]) {
            resolve(excelDataCache[fileName]);
          } else {
            setTimeout(checkCache, 100);
          }
        };
        checkCache();
      });
    }

    try {
      // 标记为正在加载
      this.setState((prevState) => {
        return {
          loadingFiles: new Set([...prevState.loadingFiles, fileName]),
        };
      });

      // 读取Excel文件
      const excelData = await this.readExcelFromUrl(fileUrl);
      const processedData = this.processExcelData(excelData.rows, excelData.headers);
      const expertFields = this.extractExpertFields(excelData.headers);

      const cacheData = {
        headers: excelData.headers,
        rows: excelData.rows,
        processedData,
        expertFields,
      };

      // 更新缓存
      this.setState((prevState) => {
        return {
          excelDataCache: {
            ...prevState.excelDataCache,
            [fileName]: cacheData,
          },
          loadingFiles: new Set([...prevState.loadingFiles].filter((f) => { return f !== fileName; })),
        };
      });

      return cacheData;
    } catch (error) {
      // 移除加载标记
      this.setState((prevState) => {
        return {
          loadingFiles: new Set([...prevState.loadingFiles].filter((f) => { return f !== fileName; })),
        };
      });
      throw error;
    }
  }

  /**
   * 值对比逻辑
   * @param {any} aiValue - AI评估值
   * @param {any} expertValue - 专家评估值
   * @returns {boolean} 是否一致
   */
  compareValues = (aiValue, expertValue) => {
    // 处理空值情况
    if (!aiValue && !expertValue) return true; // 都为空认为一致
    if (!aiValue || !expertValue) return false; // 一个为空一个不为空认为不一致

    const aiStr = aiValue.toString().trim().toLowerCase();
    const expertStr = expertValue.toString().trim().toLowerCase();

    // 直接进行字符串完全匹配比较（忽略大小写和前后空格）
    const isConsistent = aiStr === expertStr;

    return isConsistent;
  }

  /**
   * 专家字段与AI字段映射
   * @param {string} expertField - 专家字段名
   * @param {Array} aiFields - AI字段列表
   * @returns {string|null} 匹配的AI字段名
   */
  mapExpertFieldToAIField = (expertField, aiFields) => {
    // 移除专家字段中的前缀（如"专家-"），获取核心字段名
    const coreFieldName = expertField.replace(/^专家-?/, '').trim();

    // 在AI字段中查找匹配的字段
    const matchedAIField = aiFields.find((aiField) => {
      // 完全匹配
      if (aiField === coreFieldName) return true;

      // 模糊匹配：检查是否包含核心关键词
      const coreKeywords = coreFieldName.replace(/一致性|符合度|裁决/g, '').trim();
      if (coreKeywords && aiField.includes(coreKeywords)) return true;

      return false;
    });

    return matchedAIField || null;
  }

  /**
   * 生成评估统计信息（支持专家数据对比）
   * @param {Array} results - 评估结果
   * @param {Object} excelCacheData - Excel缓存数据（可选）
   * @returns {Promise<Object>} 统计信息
   */
  generateEvaluationStatistics = async (results, excelCacheData = null) => {
    if (!results || results.length === 0) {
      return {
        totalItems: 0,
        fieldStatistics: {},
        overallAccuracy: 0,
        hasExpertComparison: false,
      };
    }

    const statistics = {
      totalItems: results.length,
      fieldStatistics: {},
      overallAccuracy: 0,
      hasExpertComparison: false,
    };

    // 获取AI评估字段
    const aiFields = this.getEvaluationFields(results);

    // 如果有Excel缓存数据，进行专家对比分析
    if (excelCacheData && excelCacheData.expertFields && excelCacheData.expertFields.length > 0) {
      statistics.hasExpertComparison = true;

      // 进行AI与专家结果对比
      const comparisons = this.compareAIWithExpert(results, excelCacheData.processedData, excelCacheData.expertFields);

      // 生成对比统计
      const comparisonStats = this.generateComparisonStatistics(comparisons, excelCacheData.expertFields);

      if (comparisonStats) {
        statistics.fieldStatistics = comparisonStats.fieldStatistics;
        statistics.overallAccuracy = comparisonStats.overallAccuracy;
        return statistics;
      }
    }

    // 如果没有专家数据，只计算完成率
    aiFields.forEach((field) => {
      const fieldValues = results
        .map((result) => { return result[field]; })
        .filter((value) => { return value !== undefined && value !== null && value !== ''; });
      const totalCount = results.length;
      const validCount = fieldValues.length;

      statistics.fieldStatistics[field] = {
        totalCount,
        validCount,
        consistentCount: validCount, // 没有专家数据时，有效值即为"一致"
        accuracy: totalCount > 0 ? parseFloat(((validCount / totalCount) * 100).toFixed(2)) : 0,
        fieldType: 'completion_rate',
      };
    });

    // 计算整体完成率
    const totalValidFields = Object.values(statistics.fieldStatistics)
      .reduce((sum, stat) => { return sum + stat.validCount; }, 0);
    const totalPossibleFields = aiFields.length * results.length;
    statistics.overallAccuracy = totalPossibleFields > 0
      ? parseFloat(((totalValidFields / totalPossibleFields) * 100).toFixed(2)) : 0;

    return statistics;
  }

  /**
   * 生成对比统计分析
   * @param {Array} comparisons - 对比结果
   * @param {Array} expertFields - 专家字段列表
   * @returns {Object} 统计分析结果
   */
  generateComparisonStatistics = (comparisons, expertFields) => {
    if (!comparisons || comparisons.length === 0 || !expertFields || expertFields.length === 0) {
      return null;
    }

    const statistics = {
      totalItems: comparisons.length,
      fieldStatistics: {},
      overallAccuracy: 0,
    };

    // 为每个专家字段生成统计
    expertFields.forEach((field) => {
      let consistentCount = 0;
      let totalCount = 0;

      comparisons.forEach((comparison) => {
        const fieldComparison = comparison.comparisons[field];
        if (fieldComparison) {
          totalCount++;
          if (fieldComparison.isConsistent) {
            consistentCount++;
          }
        }
      });

      const accuracy = totalCount > 0 ? parseFloat(((consistentCount / totalCount) * 100).toFixed(2)) : 0;

      statistics.fieldStatistics[field] = {
        consistentCount,
        totalCount,
        accuracy,
        fieldType: 'expert_comparison',
      };
    });

    // 计算整体准确率
    const sampleAccuracies = [];
    comparisons.forEach((comparison) => {
      let sampleConsistentCount = 0;
      let sampleTotalCount = 0;

      expertFields.forEach((field) => {
        const fieldComparison = comparison.comparisons[field];
        if (fieldComparison) {
          sampleTotalCount++;
          if (fieldComparison.isConsistent) {
            sampleConsistentCount++;
          }
        }
      });

      if (sampleTotalCount > 0) {
        const sampleAccuracy = sampleConsistentCount / sampleTotalCount;
        sampleAccuracies.push(sampleAccuracy);
      }
    });

    // 计算所有样本的平均准确率作为整体一致性
    if (sampleAccuracies.length > 0) {
      const totalAccuracy = sampleAccuracies.reduce((sum, acc) => { return sum + acc; }, 0);
      const averageAccuracy = totalAccuracy / sampleAccuracies.length * 100;
      statistics.overallAccuracy = parseFloat(averageAccuracy.toFixed(2));
    } else {
      statistics.overallAccuracy = 0;
    }

    return statistics;
  }

  /**
   * AI与专家结果对比分析
   * @param {Array} aiResults - AI评估结果
   * @param {Array} expertData - 专家数据
   * @param {Array} expertFields - 专家字段列表
   * @returns {Array} 对比结果
   */
  compareAIWithExpert = (aiResults, expertData, expertFields) => {
    if (!aiResults || !expertData || !expertFields || expertFields.length === 0) {
      return [];
    }

    const comparisons = [];
    // 获取AI评估字段用于映射
    const aiFields = this.getEvaluationFields(aiResults);

    // 遍历专家数据，确保按正确的索引对应
    expertData.forEach((expertResult) => {
      // 根据itemIndex查找对应的AI评估结果，确保数据正确对应
      // expertResult.id 对应的是 Excel 行号（从1开始），需要找到对应的AI结果
      const aiResult = aiResults.find((result) => { return result.itemIndex === (expertResult.id - 1); });
      if (!aiResult) return;

      const comparison = {
        id: expertResult.id,
        itemIndex: expertResult.id - 1, // 转换为0基索引
        comparisons: {},
      };

      // 对每个专家字段进行对比
      expertFields.forEach((expertField) => {
        // 建立专家字段与AI字段的映射关系
        const aiFieldName = this.mapExpertFieldToAIField(expertField, aiFields);
        const aiValue = aiFieldName ? (aiResult[aiFieldName] || '') : '';

        // 统一专家数据访问路径，优先使用originalData确保数据一致性
        let expertValue = '';
        if (expertResult.originalData && expertResult.originalData[expertField] !== undefined) {
          expertValue = expertResult.originalData[expertField];
        } else if (expertResult[expertField] !== undefined) {
          expertValue = expertResult[expertField];
        }

        // 进行值对比
        const isConsistent = this.compareValues(aiValue, expertValue);

        comparison.comparisons[expertField] = {
          aiValue,
          expertValue,
          isConsistent,
        };
      });

      comparisons.push(comparison);
    });

    return comparisons;
  }

  /**
   * 加载统计数据
   * @param {string} recordId - 记录ID
   * @param {Array} results - 评估结果
   * @param {string} fileName - 文件名
   * @param {string} fileUrl - 文件URL
   */
  loadStatisticsForRecord = async (recordId, results, fileName, fileUrl) => {
    const { statisticsCache, loadingStatistics } = this.state;

    // 检查缓存
    if (statisticsCache[recordId]) {
      return;
    }

    // 检查是否正在加载
    if (loadingStatistics.has(recordId)) {
      return;
    }

    try {
      // 标记为正在加载
      this.setState((prevState) => {
        return {
          loadingStatistics: new Set([...prevState.loadingStatistics, recordId]),
        };
      });

      // 尝试获取Excel缓存数据
      let excelCacheData = null;
      try {
        excelCacheData = await this.getOrLoadExcelData(fileName, fileUrl);
      } catch (error) {
        console.warn('无法加载Excel数据，将使用基础统计:', error); // eslint-disable-line no-console
      }

      // 生成统计信息
      const statistics = await this.generateEvaluationStatistics(results, excelCacheData);

      // 更新缓存
      this.setState((prevState) => {
        return {
          statisticsCache: {
            ...prevState.statisticsCache,
            [recordId]: statistics,
          },
          loadingStatistics: new Set([...prevState.loadingStatistics].filter((id) => { return id !== recordId; })),
        };
      });
    } catch (error) {
      console.error('生成统计信息失败:', error); // eslint-disable-line no-console

      // 降级到基础统计
      try {
        const basicStats = await this.generateEvaluationStatistics(results);
        this.setState((prevState) => {
          return {
            statisticsCache: {
              ...prevState.statisticsCache,
              [recordId]: basicStats,
            },
            loadingStatistics: new Set([...prevState.loadingStatistics].filter((id) => { return id !== recordId; })),
          };
        });
      } catch (basicError) {
        // 移除加载标记
        this.setState((prevState) => {
          return {
            loadingStatistics: new Set([...prevState.loadingStatistics].filter((id) => { return id !== recordId; })),
          };
        });
      }
    }
  }

  /**
   * 渲染分析报告
   * @param {Object} evaluation - 评估数据
   * @param {string} fileName - 文件名
   * @param {string} fileUrl - 文件URL
   * @param {string} recordId - 记录ID
   * @returns {JSX.Element} 分析报告组件
   */
  renderAnalysisReport = (evaluation, fileName, fileUrl, recordId) => {
    const { results } = evaluation;
    const { statisticsCache, loadingStatistics } = this.state;

    if (!results || results.length === 0) {
      return (
        <div style={{ marginTop: 8 }}>
          <Text type="secondary">评估结果：暂无数据</Text>
        </div>
      );
    }

    // 检查是否需要加载统计数据
    const statistics = statisticsCache[recordId];
    const isLoading = loadingStatistics.has(recordId);

    if (!statistics && !isLoading) {
      // 异步加载统计数据
      this.loadStatisticsForRecord(recordId, results, fileName, fileUrl);
    }

    if (isLoading || !statistics) {
      return (
        <div style={{ marginTop: 8 }}>
          <Text type="secondary">正在分析评估结果...</Text>
        </div>
      );
    }

    if (statistics.totalItems === 0) {
      return (
        <div style={{ marginTop: 8 }}>
          <Text type="secondary">评估结果：无法生成统计信息</Text>
        </div>
      );
    }

    const aiFields = this.getEvaluationFields(results);
    const displayFields = statistics.hasExpertComparison
      ? Object.keys(statistics.fieldStatistics)
      : aiFields;

    return (
      <div style={{ marginTop: 8 }}>
        <Text type="secondary">评估结果分析：</Text>

        <div className="analysis-report-section" style={{ marginTop: 12, marginBottom: 16 }}>
          {/* 整体统计概览 */}
          <div style={{ background: '#f5f5f5', padding: 12, borderRadius: 6, marginBottom: 12 }}>
            <div style={{ fontSize: 14, fontWeight: 'bold', marginBottom: 6 }}>
              {statistics.hasExpertComparison ? '整体一致性' : '整体完成率'}: {statistics.overallAccuracy}%
            </div>
            <div style={{ color: '#666', fontSize: 12 }}>
              共分析 {statistics.totalItems} 个样本，
              涉及 {displayFields.length} 个评估维度
              {statistics.hasExpertComparison && <span style={{ color: '#1890ff' }}> (已与专家数据对比)</span>}
            </div>
          </div>

          {/* 各字段详细统计 */}
          {displayFields.length > 0 && (
            <div style={{ marginBottom: 12 }}>
              <div style={{ fontSize: 13, fontWeight: 'bold', marginBottom: 8 }}>
                {statistics.hasExpertComparison ? '各维度一致性分析' : '各维度完成情况'}
              </div>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: 8,
              }}
              >
                {displayFields.map((field) => {
                  const fieldStat = statistics.fieldStatistics[field];
                  if (!fieldStat) return null;

                  let statusColor = '#52c41a'; // 绿色 - 优秀
                  let statusText = '优秀';
                  if (fieldStat.accuracy < 60) {
                    statusColor = '#ff4d4f'; // 红色 - 需改进
                    statusText = '需改进';
                  } else if (fieldStat.accuracy < 80) {
                    statusColor = '#faad14'; // 橙色 - 良好
                    statusText = '良好';
                  }

                  return (
                    <div
                      key={field}
                      style={{
                        border: '1px solid #d9d9d9',
                        borderRadius: 4,
                        padding: 8,
                        background: '#fff',
                        fontSize: 12,
                      }}
                    >
                      <div style={{ fontWeight: 'bold', marginBottom: 4, fontSize: 12 }}>{field}</div>
                      <div style={{ marginBottom: 4 }}>
                        {statistics.hasExpertComparison ? '准确率' : '完成率'}:
                        <span style={{ color: statusColor, fontWeight: 'bold', marginLeft: 4 }}>
                          {fieldStat.accuracy}%
                        </span>
                      </div>
                      <div style={{ marginBottom: 4, fontSize: 12, color: '#666' }}>
                        {statistics.hasExpertComparison ? (
                          <>一致: {fieldStat.consistentCount} / 总计: {fieldStat.totalCount}</>
                        ) : (
                          <>有效: {fieldStat.validCount} / 总计: {fieldStat.totalCount}</>
                        )}
                      </div>
                      {statistics.hasExpertComparison && (
                        <div style={{ fontSize: 12, color: statusColor }}>
                          评级: {statusText}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  /**
   * 渲染记录项
   * @param {Object} record - 评估记录
   * @returns {JSX.Element} 记录项组件
   */
  renderRecordItem = (record) => {
    const { evaluation, prompt, file, timestamp } = record;

    return (
      <List.Item
        key={record.recordId}
        actions={[
          <Popconfirm
            key="delete"
            title="确定要删除这条评估记录吗？"
            onConfirm={() => { this.props.onDeleteRecord(record.recordId); }}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>,
        ]}
      >
        <List.Item.Meta
          title={
            <Space>
              <Text strong>{file.name}</Text>
              <Text type="secondary">{this.formatTime(timestamp)}</Text>
            </Space>
          }
          description={
            <div>
              <div style={{ marginBottom: 8 }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: 4,
                }}
                >
                  <Text type="secondary">提示词：</Text>
                  <Button
                    type="text"
                    size="small"
                    icon={<CopyOutlined />}
                    onClick={() => { this.copyPromptContent(prompt.content); }}
                    style={{ padding: '0 4px', height: 'auto' }}
                    title="复制提示词"
                  >
                    复制
                  </Button>
                </div>
                <div style={{
                  marginTop: 4,
                  padding: 8,
                  background: '#f5f5f5',
                  borderRadius: 4,
                  border: '1px solid #d9d9d9',
                  maxHeight: 200,
                  overflow: 'auto',
                }}
                >
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {prompt.content}
                  </ReactMarkdown>
                </div>
              </div>
              {this.renderAnalysisReport(evaluation, file.name, file.url, record.recordId)}
            </div>
          }
        />
      </List.Item>
    );
  }

  /**
   * 渲染空状态
   * @returns {JSX.Element} 空状态组件
   */
  renderEmptyState = () => {
    return (
      <div style={{ textAlign: 'center', padding: '40px 20px' }}>
        <Text type="secondary">暂无评估历史记录</Text>
        <br />
        <Text type="secondary">完成评估后，记录将显示在这里</Text>
      </div>
    );
  }


  render() {
    const { visible, onClose, records, loading } = this.props;

    return (
      <Drawer
        title="评估历史记录"
        placement="right"
        width="66vw"
        open={visible}
        onClose={onClose}
        className="evaluation-history-drawer"
      >
        <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          <div style={{ flex: 1, overflow: 'auto' }}>
            <List
              loading={loading}
              dataSource={records}
              renderItem={this.renderRecordItem}
              locale={{ emptyText: this.renderEmptyState() }}
              pagination={records.length > 10 ? {
                pageSize: 10,
                showSizeChanger: false,
                showQuickJumper: true,
                showTotal: (total, range) => {
                  return `第 ${range[0]}-${range[1]} 条，共 ${total} 条`;
                },
              } : false}
            />
          </div>
        </div>
      </Drawer>
    );
  }
}
