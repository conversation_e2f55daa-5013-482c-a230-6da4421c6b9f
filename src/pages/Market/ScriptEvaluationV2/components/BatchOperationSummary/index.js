import {
  BarChartOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  DownloadOutlined,
  TrophyOutlined,
} from '@ant-design/icons';
import { Button, Card, Col, Divider, Modal, Row, Statistic, Timeline, Typography, message } from 'antd';
import PropTypes from 'prop-types';
import React, { Component } from 'react';

const { Text, Paragraph } = Typography;

/**
 * 批量操作结果汇总组件
 * 显示批量操作完成后的详细统计和分析
 */
export default class BatchOperationSummary extends Component {
  static propTypes = {
    visible: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    operationRecord: PropTypes.object,
    onExportBatchResults: PropTypes.func, // 新增：导出批量结果回调
  }

  static defaultProps = {
    operationRecord: null,
    onExportBatchResults: () => { },
  }

  /**
   * 格式化持续时间
   * @param {number} duration - 持续时间（毫秒）
   * @returns {string} 格式化后的时间字符串
   */
  formatDuration = (duration) => {
    if (!duration) return '0秒';

    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    }
    return `${seconds}秒`;
  }

  /**
   * 处理导出批量结果
   */
  handleExportBatchResults = () => {
    const { operationRecord, onExportBatchResults } = this.props;

    if (!operationRecord) {
      message.warning('暂无批量操作记录可导出');
      return;
    }

    // 检查是否有评估结果
    const hasEvaluationResults = operationRecord.iterations.some((iter) => {
      return iter.evaluationResults && iter.evaluationResults.length > 0;
    });

    if (!hasEvaluationResults) {
      message.warning('批量操作中没有评估结果可导出');
      return;
    }

    onExportBatchResults(operationRecord);
  }

  /**
   * 渲染整体统计
   */
  renderOverallStatistics = () => {
    const { operationRecord } = this.props;

    if (!operationRecord || !operationRecord.summary) {
      return null;
    }

    const { summary } = operationRecord;
    const {
      totalIterations,
      completedIterations,
      failedIterations,
      successRate,
      totalDuration,
      avgIterationDuration,
    } = summary;

    return (
      <Card title="整体统计" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="总迭代次数"
              value={totalIterations}
              prefix={<BarChartOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="成功次数"
              value={completedIterations}
              valueStyle={{ color: '#3f8600' }}
              prefix={<CheckCircleOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="失败次数"
              value={failedIterations}
              valueStyle={{ color: '#cf1322' }}
              prefix={<CloseCircleOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="成功率"
              value={successRate}
              suffix="%"
              valueStyle={{ color: successRate >= 80 ? '#3f8600' : '#cf1322' }}
              prefix={<TrophyOutlined />}
            />
          </Col>
        </Row>

        <Divider />

        <Row gutter={16}>
          <Col span={12}>
            <Statistic
              title="总耗时"
              value={this.formatDuration(totalDuration)}
              prefix={<ClockCircleOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="平均每次耗时"
              value={this.formatDuration(avgIterationDuration)}
              prefix={<ClockCircleOutlined />}
            />
          </Col>
        </Row>
      </Card>
    );
  }

  /**
   * 渲染提示词演进历史
   */
  renderPromptEvolution = () => {
    const { operationRecord } = this.props;

    if (!operationRecord || !operationRecord.summary || !operationRecord.summary.promptEvolution) {
      return null;
    }

    const { promptEvolution } = operationRecord.summary;

    return (
      <Card title="提示词演进历史" size="small" style={{ marginBottom: 16 }}>
        <Timeline mode="left">
          <Timeline.Item
            dot={<CheckCircleOutlined style={{ color: '#1890ff' }} />}
            color="blue"
          >
            <div>
              <Text strong>初始提示词</Text>
              <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                长度: {operationRecord.initialPromptText ? operationRecord.initialPromptText.length : 0} 字符
              </div>
            </div>
          </Timeline.Item>

          {promptEvolution.map((evolution) => {
            const { iteration, promptLength, optimizedLength, hasOptimization } = evolution;

            return (
              <Timeline.Item
                key={iteration}
                dot={hasOptimization ?
                  <CheckCircleOutlined style={{ color: '#52c41a' }} /> :
                  <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
                }
                color={hasOptimization ? 'green' : 'red'}
              >
                <div>
                  <Text strong>第{iteration}次迭代</Text>
                  <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                    {hasOptimization ? (
                      <>
                        优化前: {promptLength} 字符 → 优化后: {optimizedLength} 字符
                        <br />
                        变化: {optimizedLength > promptLength ? '+' : ''}{optimizedLength - promptLength} 字符
                      </>
                    ) : (
                      '优化失败'
                    )}
                  </div>
                </div>
              </Timeline.Item>
            );
          })}
        </Timeline>
      </Card>
    );
  }

  /**
   * 渲染迭代详情
   */
  renderIterationDetails = () => {
    const { operationRecord } = this.props;

    if (!operationRecord || !operationRecord.iterations) {
      return null;
    }

    const { iterations } = operationRecord;

    return (
      <Card title="迭代详情" size="small" style={{ marginBottom: 16 }}>
        <div style={{ maxHeight: 300, overflowY: 'auto' }}>
          {iterations.map((iteration) => {
            const {
              iteration: iterIndex,
              status,
              startTime,
              endTime,
              duration,
              error,
            } = iteration;

            const statusColor = status === 'completed' ? '#52c41a' : '#ff4d4f';
            const statusText = status === 'completed' ? '成功' : '失败';

            return (
              <Card
                key={iterIndex}
                size="small"
                style={{ marginBottom: 8 }}
                title={`第${iterIndex}次迭代`}
                extra={
                  <Text style={{ color: statusColor, fontWeight: 'bold' }}>
                    {statusText}
                  </Text>
                }
              >
                <Row gutter={16}>
                  <Col span={8}>
                    <Text type="secondary">开始时间:</Text>
                    <br />
                    <Text>{new Date(startTime).toLocaleString()}</Text>
                  </Col>
                  <Col span={8}>
                    <Text type="secondary">结束时间:</Text>
                    <br />
                    <Text>{endTime ? new Date(endTime).toLocaleString() : '-'}</Text>
                  </Col>
                  <Col span={8}>
                    <Text type="secondary">耗时:</Text>
                    <br />
                    <Text>{this.formatDuration(duration)}</Text>
                  </Col>
                </Row>

                {error && (
                  <div style={{ marginTop: 8 }}>
                    <Text type="secondary">错误信息:</Text>
                    <br />
                    <Text type="danger">{error}</Text>
                  </div>
                )}
              </Card>
            );
          })}
        </div>
      </Card>
    );
  }

  /**
   * 渲染最终结果
   */
  renderFinalResult = () => {
    const { operationRecord } = this.props;

    if (!operationRecord || !operationRecord.summary) {
      return null;
    }

    const { finalPromptText } = operationRecord.summary;

    return (
      <Card title="最终结果" size="small">
        <Paragraph>
          <Text strong>最终提示词长度:</Text> {finalPromptText ? finalPromptText.length : 0} 字符
        </Paragraph>

        <Paragraph>
          <Text strong>优化建议:</Text>
        </Paragraph>
        <ul>
          <li>建议定期进行批量优化以保持提示词的有效性</li>
          <li>关注成功率较低的迭代，分析失败原因</li>
          <li>根据业务需求调整批量操作的迭代次数</li>
        </ul>
      </Card>
    );
  }

  /**
   * 渲染组件
   */
  render() {
    const { visible, onClose, operationRecord } = this.props;

    if (!operationRecord) {
      return null;
    }

    // 检查是否有可导出的评估结果
    const hasEvaluationResults = operationRecord.iterations.some((iter) => {
      return iter.evaluationResults && iter.evaluationResults.length > 0;
    });

    return (
      <Modal
        title="批量操作结果汇总"
        open={visible}
        onCancel={onClose}
        footer={[
          hasEvaluationResults && (
            <Button
              key="export"
              icon={<DownloadOutlined />}
              onClick={this.handleExportBatchResults}
              style={{ marginRight: 8 }}
            >
              导出所有迭代结果
            </Button>
          ),
          <Button key="close" type="primary" onClick={onClose}>
            关闭
          </Button>,
        ].filter(Boolean)}
        width={800}
        style={{ top: 20 }}
      >
        {/* 整体统计 */}
        {this.renderOverallStatistics()}

        {/* 提示词演进历史 */}
        {this.renderPromptEvolution()}

        {/* 迭代详情 */}
        {this.renderIterationDetails()}

        {/* 最终结果 */}
        {this.renderFinalResult()}
      </Modal>
    );
  }
}
