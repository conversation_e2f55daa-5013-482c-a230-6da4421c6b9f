import './index.less';

import { InboxOutlined } from '@ant-design/icons';
import { AliyunHelper } from '~/engine';
import { OSSFileHelper } from '~/plugins';
import { Button, Card, Popconfirm, Table, Upload, message } from 'antd';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';

/**
 * 文件管理面板组件
 * 提供文件上传、列表展示和管理功能
 */
export default class FileManagementPanel extends Component {
  static propTypes = {
    onFileChange: PropTypes.func,
  }

  static defaultProps = {
    onFileChange: () => { },
  }
  state = {
    fileList: [], // 文件列表
    loading: false, // 加载状态
    uploading: false, // 上传状态
  }

  /**
   * 组件挂载后执行
   */
  componentDidMount() {
    this.fetchFileListFromOSS();
  }

  /**
   * 从OSS获取文件列表
   */
  fetchFileListFromOSS = async () => {
    try {
      this.setState({ loading: true });
      const fileData = await OSSFileHelper.fetchData('script-evaluation-v2', 'file-management');

      const files = fileData && fileData.files && Array.isArray(fileData.files)
        ? fileData.files
        : [];

      this.setState({ fileList: files }, () => {
        // 调用父组件的回调函数
        this.props.onFileChange(files);
      });
    } catch (error) {
      console.error('获取文件列表失败:', error); // eslint-disable-line no-console
      message.error('获取文件列表失败');
    } finally {
      this.setState({ loading: false });
    }
  }

  /**
   * 更新文件列表到OSS
   * @param {Array} files - 文件列表
   */
  updateFileListToOSS = async (files) => {
    try {
      const fileData = {
        files,
        metadata: {
          totalFiles: files.length,
          lastUpdated: new Date().toISOString(),
        },
      };

      await OSSFileHelper.updateData(
        JSON.stringify(fileData, null, 2),
        'script-evaluation-v2',
        'file-management',
      );

      message.success('文件列表已更新');
    } catch (error) {
      console.error('更新文件列表失败:', error); // eslint-disable-line no-console
      message.error('更新文件列表失败');
    }
  }

  /**
   * 验证文件大小和类型
   * @param {File} file - 文件对象
   * @returns {boolean} 是否通过验证
   */
  validateFile = (file) => {
    // 文件大小限制：50MB
    const isLessThan50M = file.size / 1024 / 1024 < 50;
    if (!isLessThan50M) {
      message.error('文件大小不能超过50MB');
      return false;
    }

    // 文件类型检查 - 仅支持Excel表格类型
    const allowedTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ];

    // 检查文件类型是否为Excel
    const isAllowedType = allowedTypes.includes(file.type) ||
      file.name.endsWith('.xlsx') ||
      file.name.endsWith('.xls');
    if (!isAllowedType) {
      message.error('仅支持Excel表格文件(.xlsx或.xls格式)');
      return false;
    }

    return true;
  }

  /**
   * 处理文件上传
   * @param {Object} option - 上传选项
   */
  handleFileUpload = async (option) => {
    const { file } = option;
    let aborted = false;

    // 验证文件
    if (!this.validateFile(file)) {
      option.onError(new Error('文件验证失败'));
      return { abort: () => { } };
    }

    try {
      this.setState({ uploading: true });

      // 上传文件到OSS
      const fileUrl = await AliyunHelper.clipsUploadImage(file, (progress) => {
        const percent = Math.round((progress.loaded / progress.total) * 100);
        option.onProgress({ percent });
      });

      if (aborted) {
        return {
          abort() {
            aborted = true;
          },
        };
      }

      // 上传成功
      option.onSuccess();

      // 构建文件信息
      const newFile = {
        id: `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: file.name,
        url: fileUrl,
        size: file.size,
        type: file.type,
        uploadTime: new Date().toISOString(),
        status: 'success',
      };

      // 更新状态
      const updatedFileList = [...this.state.fileList, newFile];
      this.setState({ fileList: updatedFileList }, () => {
        // 调用父组件的回调函数
        this.props.onFileChange(updatedFileList);
      });

      // 更新到OSS
      await this.updateFileListToOSS(updatedFileList);

      message.success(`${file.name} 上传成功`);
    } catch (error) {
      console.error('文件上传失败:', error); // eslint-disable-line no-console
      option.onError();
      message.error(`${file.name} 上传失败`);
    } finally {
      this.setState({ uploading: false });
    }

    return {
      abort() {
        aborted = true;
      },
    };
  }

  /**
   * 处理文件删除
   * @param {String} fileId - 文件ID
   */
  handleFileRemove = async (fileId) => {
    try {
      this.setState({ loading: true });

      // 过滤掉要删除的文件
      const updatedFileList = this.state.fileList.filter((file) => { return file.id !== fileId; });

      // 更新状态
      this.setState({ fileList: updatedFileList }, () => {
        // 调用父组件的回调函数
        this.props.onFileChange(updatedFileList);
      });

      // 更新到OSS
      await this.updateFileListToOSS(updatedFileList);

      message.success('文件已删除');
    } catch (error) {
      console.error('删除文件失败:', error); // eslint-disable-line no-console
      message.error('删除文件失败');
    } finally {
      this.setState({ loading: false });
    }
  }

  /**
   * 刷新文件列表
   */
  refreshFileList = () => {
    this.fetchFileListFromOSS();
  }

  /**
   * 渲染文件列表表格
   * @returns {JSX.Element} 表格组件
   */
  renderFileTable = () => {
    const { fileList, loading } = this.state;

    const columns = [
      {
        title: '文件名',
        dataIndex: 'name',
        key: 'name',
        render: (text, record) => {
          return (
            <a href={record.url} target="_blank" rel="noreferrer">{text}</a>
          );
        },
      },
      {
        title: '文件大小',
        dataIndex: 'size',
        key: 'size',
        align: 'center',
        render: (size) => {
          const kb = size / 1024;
          if (kb < 1024) {
            return `${kb.toFixed(2)} KB`;
          }
          return `${(kb / 1024).toFixed(2)} MB`;
        },
      },
      {
        title: '上传时间',
        dataIndex: 'uploadTime',
        key: 'uploadTime',
        align: 'center',
        render: (time) => { return moment(time).format('YYYY-MM-DD HH:mm:ss'); },
      },
      {
        title: '操作',
        key: 'action',
        align: 'center',
        render: (_, record) => {
          return (
            <Popconfirm
              title="确定要删除此文件吗?"
              onConfirm={() => { return this.handleFileRemove(record.id); }}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger>删除</Button>
            </Popconfirm>
          );
        },
      },
    ];

    return (
      <Table
        rowKey="id"
        columns={columns}
        dataSource={fileList}
        loading={loading}
        pagination={{ pageSize: 5 }}
      />
    );
  }

  /**
   * 渲染组件
   */
  render() {
    const { uploading } = this.state;

    return (
      <Card
        title="文件管理"
        bordered
        className="file-management-panel"
        style={{
          height: '100%',
          boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
        }}
        extra={
          <Button onClick={this.refreshFileList}>刷新</Button>
        }
      >
        <div style={{ padding: '20px' }}>
          <Upload.Dragger
            multiple
            accept=".xlsx,.xls,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" // eslint-disable-line max-len
            customRequest={this.handleFileUpload}
            showUploadList={false}
            disabled={uploading}
          >
            <p className="ant-upload-drag-icon"><InboxOutlined /></p>
            <p className="ant-upload-text">点击或拖动Excel文件到此区域上传</p>
            <p className="ant-upload-hint file-upload-hint">仅支持Excel表格文件(.xlsx或.xls格式)</p>
          </Upload.Dragger>

          <div className="file-table-container">
            {this.renderFileTable()}
          </div>
        </div>
      </Card>
    );
  }
}
