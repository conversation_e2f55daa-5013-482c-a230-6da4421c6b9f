/* eslint-disable max-lines, no-bitwise */
import { DownloadOutlined, HistoryOutlined, ThunderboltOutlined } from '@ant-design/icons';
import { Sessions } from '~/engine';
import { OSSFileHelper } from '~/plugins';
import { Button, Drawer, Form, Input, List, Select, Space, Table, message } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import * as XLSX from 'xlsx';

import BatchOperationManager from '../../utils/BatchOperationManager';
import PromptVersionManager from '../../utils/PromptVersionManager';
import TableColumnHelper from '../../utils/tableColumnHelper';
import BatchOperationModal from '../BatchOperationModal';
import BatchOperationProgress from '../BatchOperationProgress';
import BatchOperationSummary from '../BatchOperationSummary';
import EvaluationHistoryDrawer from '../EvaluationHistoryDrawer';

/**
 * 云端数据管理类
 * 负责提示词和评估记录的云端同步
 */
class CloudDataManager {
  /**
   * 生成内容哈希值
   * @param {string} content - 内容
   * @returns {string} 哈希值
   */
  static generateHash(content) {
    let hash = 0;
    if (content.length === 0) return hash.toString();
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      // eslint-disable-next-line no-bitwise
      hash = ((hash << 5) - hash) + char;
      // eslint-disable-next-line no-bitwise
      hash &= hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 获取用户提示词库
   * @returns {Array} 提示词列表
   */
  static async fetchUserPrompts() {
    try {
      const data = await OSSFileHelper.fetchData('script-evaluation-v2', 'prompts');
      return data?.prompts || [];
    } catch (error) {
      console.error('获取提示词库失败:', error); // eslint-disable-line no-console
      return [];
    }
  }

  /**
   * 同步提示词到云端
   * @param {string} promptContent - 提示词内容
   * @param {string} description - 描述
   * @returns {Object} 提示词信息
   */
  static async syncPromptToCloud(promptContent, description = '用户输入') {
    const promptHash = this.generateHash(promptContent);
    const existingPrompts = await this.fetchUserPrompts();

    // 检查是否已存在相同内容的提示词
    const existingPrompt = existingPrompts.find((p) => { return p.hash === promptHash; });
    if (existingPrompt) {
      // 更新使用次数
      existingPrompt.usageCount = (existingPrompt.usageCount || 0) + 1;
      existingPrompt.lastUsed = new Date().toISOString();

      // 更新云端数据
      await this.updatePromptsToCloud(existingPrompts);
      return existingPrompt;
    }

    // 创建新的提示词记录
    const newPrompt = {
      id: `prompt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content: promptContent,
      hash: promptHash,
      description,
      createdAt: new Date().toISOString(),
      lastUsed: new Date().toISOString(),
      usageCount: 1,
    };

    // 添加到提示词库并更新云端
    const updatedPrompts = [...existingPrompts, newPrompt];
    await this.updatePromptsToCloud(updatedPrompts);

    return newPrompt;
  }

  /**
   * 更新提示词库到云端
   * @param {Array} prompts - 提示词列表
   */
  static async updatePromptsToCloud(prompts) {
    const promptData = {
      prompts,
      metadata: {
        totalPrompts: prompts.length,
        lastUpdated: new Date().toISOString(),
        userId: Sessions.getPartner().openId,
      },
    };

    await OSSFileHelper.updateData(
      JSON.stringify(promptData, null, 2),
      'script-evaluation-v2',
      'prompts',
    );
  }

  /**
   * 获取用户评估记录
   * @returns {Array} 评估记录列表
   */
  static async fetchUserRecords() {
    try {
      const data = await OSSFileHelper.fetchData('script-evaluation-v2', 'records');
      return data?.records || [];
    } catch (error) {
      console.error('获取评估记录失败:', error); // eslint-disable-line no-console
      return [];
    }
  }

  /**
   * 创建评估记录
   * @param {Object} promptInfo - 提示词信息
   * @param {Object} fileInfo - 文件信息
   * @param {Array} evaluationResults - 评估结果
   * @returns {Object} 评估记录
   */
  static async createEvaluationRecord(promptInfo, fileInfo, evaluationResults) {
    const record = {
      recordId: `eval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      prompt: {
        content: promptInfo.content,
        hash: promptInfo.hash,
      },
      file: {
        id: fileInfo.id,
        name: fileInfo.name,
        url: fileInfo.url,
      },
      evaluation: {
        results: evaluationResults,
        statistics: this.generateStatistics(evaluationResults),
      },
      metadata: {
        userId: Sessions.getPartner().openId,
        platform: process.env.NODE_ENV === 'production' ? 'prod' : 'stg',
      },
    };

    // 保存到云端
    await this.saveRecordToCloud(record);
    return record;
  }

  /**
   * 保存评估记录到云端
   * @param {Object} record - 评估记录
   */
  static async saveRecordToCloud(record) {
    const existingRecords = await this.fetchUserRecords();
    const updatedRecords = [...existingRecords, record];

    const recordData = {
      records: updatedRecords,
      metadata: {
        totalRecords: updatedRecords.length,
        lastUpdated: new Date().toISOString(),
        userId: Sessions.getPartner().openId,
      },
    };

    await OSSFileHelper.updateData(
      JSON.stringify(recordData, null, 2),
      'script-evaluation-v2',
      'records',
    );
  }

  /**
   * 生成评估统计信息
   * @param {Array} results - 评估结果
   * @returns {Object} 统计信息
   */
  static generateStatistics(results) {
    if (!results || results.length === 0) {
      return { total: 0, success: 0, failure: 0, successRate: 0 };
    }

    const total = results.length;
    const success = results.filter((r) => { return r.success !== false; }).length;
    const failure = total - success;
    const successRate = Math.round((success / total) * 100);

    return {
      total,
      success,
      failure,
      successRate,
    };
  }

  /**
   * 获取用户评估历史记录（按时间排序）
   * @param {number} limit - 限制返回数量，默认50
   * @returns {Array} 历史记录列表
   */
  static async fetchEvaluationHistory(limit = 1000) {
    try {
      const records = await this.fetchUserRecords();
      // 按时间倒序排列，最新的在前
      return records
        .sort((a, b) => { return new Date(b.timestamp) - new Date(a.timestamp); })
        .slice(0, limit);
    } catch (error) {
      console.error('获取评估历史失败:', error); // eslint-disable-line no-console
      return [];
    }
  }

  /**
   * 根据提示词内容获取相关的评估记录
   * @param {string} promptContent - 提示词内容
   * @returns {Array} 相关评估记录
   */
  static async fetchRecordsByPrompt(promptContent) {
    try {
      const promptHash = this.generateHash(promptContent);
      const records = await this.fetchUserRecords();

      return records
        .filter((record) => { return record.prompt.hash === promptHash; })
        .sort((a, b) => { return new Date(b.timestamp) - new Date(a.timestamp); });
    } catch (error) {
      console.error('获取提示词相关记录失败:', error); // eslint-disable-line no-console
      return [];
    }
  }

  /**
   * 获取评估记录的详细统计信息
   * @param {Array} records - 评估记录列表
   * @returns {Object} 详细统计信息
   */
  static generateDetailedStatistics(records) {
    if (!records || records.length === 0) {
      return {
        totalRecords: 0,
        totalEvaluations: 0,
        averageSuccessRate: 0,
        dateRange: null,
        promptUsage: {},
        fileUsage: {},
      };
    }

    const totalRecords = records.length;
    let totalEvaluations = 0;
    let totalSuccessRate = 0;
    const promptUsage = {};
    const fileUsage = {};
    const dates = [];

    records.forEach((record) => {
      // 统计评估数量和成功率
      const stats = record.evaluation.statistics;
      totalEvaluations += stats.total;
      totalSuccessRate += stats.successRate;

      // 统计提示词使用情况
      const promptHash = record.prompt.hash;
      if (!promptUsage[promptHash]) {
        promptUsage[promptHash] = {
          content: `${record.prompt.content.substring(0, 100)}...`,
          count: 0,
          successRate: 0,
        };
      }
      promptUsage[promptHash].count++;
      promptUsage[promptHash].successRate += stats.successRate;

      // 统计文件使用情况
      const fileName = record.file.name;
      if (!fileUsage[fileName]) {
        fileUsage[fileName] = {
          count: 0,
          successRate: 0,
        };
      }
      fileUsage[fileName].count++;
      fileUsage[fileName].successRate += stats.successRate;

      // 收集日期
      dates.push(new Date(record.timestamp));
    });

    // 计算平均成功率
    const averageSuccessRate = Math.round(totalSuccessRate / totalRecords);

    // 计算各项平均值
    Object.keys(promptUsage).forEach((hash) => {
      promptUsage[hash].successRate = Math.round(
        promptUsage[hash].successRate / promptUsage[hash].count,
      );
    });

    Object.keys(fileUsage).forEach((fileName) => {
      fileUsage[fileName].successRate = Math.round(
        fileUsage[fileName].successRate / fileUsage[fileName].count,
      );
    });

    // 计算日期范围
    const sortedDates = dates.sort((a, b) => { return a - b; });
    const dateRange = dates.length > 0 ? {
      start: sortedDates[0].toISOString(),
      end: sortedDates[sortedDates.length - 1].toISOString(),
    } : null;

    return {
      totalRecords,
      totalEvaluations,
      averageSuccessRate,
      dateRange,
      promptUsage,
      fileUsage,
    };
  }

  /**
   * 删除评估记录
   * @param {string} recordId - 记录ID
   * @returns {boolean} 删除是否成功
   */
  static async deleteEvaluationRecord(recordId) {
    try {
      const records = await this.fetchUserRecords();
      const filteredRecords = records.filter((record) => { return record.recordId !== recordId; });

      if (filteredRecords.length === records.length) {
        // 没有找到要删除的记录
        return false;
      }

      const recordData = {
        records: filteredRecords,
        metadata: {
          totalRecords: filteredRecords.length,
          lastUpdated: new Date().toISOString(),
          userId: Sessions.getPartner().openId,
        },
      };

      await OSSFileHelper.updateData(
        JSON.stringify(recordData, null, 2),
        'script-evaluation-v2',
        'records',
      );

      return true;
    } catch (error) {
      console.error('删除评估记录失败:', error); // eslint-disable-line no-console
      return false;
    }
  }

  /**
   * 为后续扩展预留：提示词版本管理接口
   * 创建提示词版本
   * @param {string} basePromptId - 基础提示词ID
   * @param {string} newContent - 新内容
   * @param {string} description - 版本描述
   * @returns {Object} 新版本信息
   */
  static async createPromptVersion(basePromptId, newContent, description = '修改版本') {
    // 使用 PromptVersionManager 创建版本信息
    const versionInfo = PromptVersionManager.createVersionInfo(newContent, description, basePromptId);

    // 验证提示词内容
    const validation = PromptVersionManager.validatePromptContent(newContent);
    if (!validation.isValid) {
      throw new Error(`提示词验证失败: ${validation.errors.join(', ')}`);
    }

    // 同步到云端
    const newPrompt = await this.syncPromptToCloud(newContent, description);

    // 添加版本管理信息
    newPrompt.basePromptId = basePromptId;
    newPrompt.isVersion = true;
    newPrompt.versionInfo = versionInfo;
    newPrompt.validation = validation;

    return newPrompt;
  }

  /**
   * 为后续扩展预留：获取提示词版本历史
   * @param {string} promptId - 提示词ID
   * @returns {Array} 版本历史列表
   */
  static async getPromptVersionHistory(promptId) {
    const prompts = await this.fetchUserPrompts();
    const versionChain = prompts.filter((p) => {
      return p.basePromptId === promptId || p.id === promptId;
    });

    // 按创建时间排序
    return versionChain.sort((a, b) => {
      return new Date(b.createdAt) - new Date(a.createdAt);
    });
  }

  /**
   * 为后续扩展预留：比较两个提示词版本
   * @param {string} originalContent - 原始内容
   * @param {string} modifiedContent - 修改后内容
   * @returns {Object} 差异对比结果
   */
  static comparePromptVersions(originalContent, modifiedContent) {
    return PromptVersionManager.createDiff(originalContent, modifiedContent);
  }

  /**
   * 为后续扩展预留：获取提示词优化建议
   * @param {string} promptContent - 提示词内容
   * @param {Array} evaluationResults - 评估结果
   * @returns {Array} 优化建议列表
   */
  static getPromptOptimizationSuggestions(promptContent, evaluationResults = []) {
    return PromptVersionManager.generateOptimizationSuggestions(promptContent, evaluationResults);
  }

  /**
   * 为后续扩展预留：获取提示词模板
   * @returns {Array} 模板列表
   */
  static getPromptTemplates() {
    return PromptVersionManager.getPromptTemplates();
  }

  /**
   * 为后续扩展预留：回滚到指定版本
   * @param {string} targetVersionId - 目标版本ID
   * @returns {Object} 回滚结果
   */
  static async rollbackToPromptVersion(targetVersionId) {
    const prompts = await this.fetchUserPrompts();
    const targetPrompt = prompts.find((p) => { return p.id === targetVersionId; });

    if (!targetPrompt) {
      return {
        success: false,
        error: '目标版本不存在',
      };
    }

    // 创建回滚版本
    const rollbackPrompt = await this.syncPromptToCloud(
      targetPrompt.content,
      `回滚到版本: ${targetPrompt.description}`,
    );

    return {
      success: true,
      prompt: rollbackPrompt,
      rollbackInfo: {
        targetVersionId,
        rollbackTime: new Date().toISOString(),
        originalDescription: targetPrompt.description,
      },
    };
  }
}

/**
 * 评估面板组件
 * 包含文件选择器、提示词输入、操作按钮和结果表格
 */
export default class EvaluationPanel extends Component {
  static propTypes = {
    onFileChange: PropTypes.func,
    onEvaluate: PropTypes.func,
    onPreviewOptimizeData: PropTypes.func,
    onOpenWorkflow: PropTypes.func, // 新增：打开工作流回调
    // 新增：评估状态相关props
    totalItems: PropTypes.number,
    completedItems: PropTypes.number,
    isProcessing: PropTypes.bool,
    evaluationResults: PropTypes.array,
  }

  static defaultProps = {
    onFileChange: () => { },
    onEvaluate: () => { },
    onPreviewOptimizeData: () => { },
    onOpenWorkflow: () => { }, // 新增：打开工作流默认回调
    totalItems: 0,
    completedItems: 0,
    isProcessing: false,
    evaluationResults: [],
  }

  state = {
    selectedFile: null, // 选中的文件
    fileName: '', // 文件名
    promptText: '', // 提示词文本
    isProcessing: false, // 处理状态
    evaluationResults: [], // 评估结果
    availableFiles: [], // 可用文件列表
    selectedFileFromList: null, // 从列表选择的文件ID
    isLoadingFiles: false, // 加载文件列表状态
    isFileProcessing: false, // 文件处理状态
    excelData: null, // Excel数据
    headers: null, // Excel表头
    expertFields: [], // 专家评判字段
    // 云端数据相关状态
    cloudPrompts: [], // 云端提示词库
    evaluationRecords: [], // 评估记录历史
    isCloudSyncing: false, // 云端同步状态
    currentPromptInfo: null, // 当前提示词信息
    showPromptHistory: false, // 显示提示词历史
    isLoadingPrompts: false, // 加载提示词状态
    // AI与专家对比分析相关状态
    comparisonStatistics: null, // 对比统计数据
    showStatistics: false, // 显示统计报告
    // 历史记录相关状态
    showHistoryDrawer: false, // 显示历史记录抽屉
    isLoadingHistory: false, // 加载历史记录状态
    // 批量操作相关状态
    showBatchModal: false, // 显示批量操作配置弹窗
    showBatchProgress: false, // 显示批量操作进度弹窗
    showBatchSummary: false, // 显示批量操作结果汇总
    batchProgressData: {}, // 批量操作进度数据
    batchOperationRecord: null, // 批量操作记录
    isBatchRunning: false, // 批量操作运行状态
  }

  /**
   * 组件挂载后执行
   */
  componentDidMount() {
    // 获取可用文件列表
    this.fetchAvailableFiles();
    // 加载云端提示词和评估记录
    this.loadCloudData();
    // 初始化批量操作管理器
    this.initializeBatchOperationManager();
  }

  /**
   * 组件卸载前执行
   */
  componentWillUnmount() {
    // 清理批量操作管理器
    if (this.batchOperationManager) {
      this.batchOperationManager.cancelOperation();
    }
  }

  /**
   * 加载云端数据
   */
  loadCloudData = async () => {
    this.setState({ isLoadingPrompts: true });

    try {
      // 并行加载云端提示词库和评估记录
      const [prompts, records] = await Promise.all([
        CloudDataManager.fetchUserPrompts(),
        CloudDataManager.fetchEvaluationHistory(1000), // 只加载最近20条记录
      ]);

      this.setState({
        cloudPrompts: prompts,
        evaluationRecords: records,
        isLoadingPrompts: false,
      });

      // 如果有提示词，使用最近使用的提示词
      if (prompts.length > 0) {
        // 按最后使用时间排序
        const sortedPrompts = [...prompts].sort((a, b) => {
          return new Date(b.lastUsed) - new Date(a.lastUsed);
        });

        // 使用最近的提示词
        const latestPrompt = sortedPrompts[0];
        this.setState({
          promptText: latestPrompt.content,
          currentPromptInfo: latestPrompt,
        });
      }

      console.log(`已加载 ${prompts.length} 个提示词和 ${records.length} 条评估记录`); // eslint-disable-line no-console
    } catch (error) {
      console.error('加载云端数据失败:', error); // eslint-disable-line no-console
      message.error('加载云端数据失败，请刷新页面重试');
      this.setState({ isLoadingPrompts: false });
    }
  }

  /**
   * 显示历史记录抽屉
   */
  showHistoryDrawer = async () => {
    this.setState({
      showHistoryDrawer: true,
      isLoadingHistory: true,
    });

    try {
      // 重新加载最新的评估记录
      const records = await CloudDataManager.fetchEvaluationHistory();
      this.setState({
        evaluationRecords: records,
        isLoadingHistory: false,
      });
    } catch (error) {
      console.error('加载历史记录失败:', error); // eslint-disable-line no-console
      message.error('加载历史记录失败');
      this.setState({ isLoadingHistory: false });
    }
  }

  /**
   * 关闭历史记录抽屉
   */
  closeHistoryDrawer = () => {
    this.setState({ showHistoryDrawer: false });
  }


  /**
   * 删除评估记录
   * @param {string} recordId - 记录ID
   */
  deleteRecord = async (recordId) => {
    try {
      const success = await CloudDataManager.deleteEvaluationRecord(recordId);
      if (success) {
        message.success('删除成功');
        // 重新加载记录列表
        const records = await CloudDataManager.fetchEvaluationHistory();
        this.setState({ evaluationRecords: records });
      } else {
        message.error('删除失败，记录不存在');
      }
    } catch (error) {
      console.error('删除记录失败:', error); // eslint-disable-line no-console
      message.error('删除失败，请重试');
    }
  }

  /**
   * 处理提示词变化
   * @param {Event} e - 输入事件
   */
  handlePromptChange = (e) => {
    const { value } = e.target;
    this.setState({ promptText: value });

    // 实时同步到云端（防抖处理）
    this.debouncedSyncPrompt(value);
  }

  /**
   * 防抖同步提示词到云端
   */
  debouncedSyncPrompt = _.debounce(async (promptText) => {
    if (!promptText.trim()) return;

    try {
      this.setState({ isCloudSyncing: true });
      const promptInfo = await CloudDataManager.syncPromptToCloud(promptText, '用户编辑');
      this.setState({
        currentPromptInfo: promptInfo,
        isCloudSyncing: false,
      });
    } catch (error) {
      console.error('同步提示词失败:', error); // eslint-disable-line no-console
      this.setState({ isCloudSyncing: false });
    }
  }, 1000)

  /**
   * 开始评估
   */
  handleEvaluate = async () => {
    const { selectedFile, promptText, excelData, headers, expertFields, currentPromptInfo } = this.state;

    if (!selectedFile) {
      message.warning('请先选择Excel文件');
      return;
    }

    if (!promptText.trim()) {
      message.warning('请输入评估提示词');
      return;
    }

    if (!excelData || !headers) {
      message.warning('Excel文件数据未加载完成，请重新选择文件');
      return;
    }

    this.setState({ isProcessing: true });

    try {
      // 1. 确保提示词已同步到云端
      let promptInfo = currentPromptInfo;
      if (!promptInfo || promptInfo.content !== promptText.trim()) {
        promptInfo = await CloudDataManager.syncPromptToCloud(promptText.trim(), '评估使用');
        this.setState({ currentPromptInfo: promptInfo });
      }

      // 2. 准备文件信息
      const fileInfo = {
        id: selectedFile.id || `file_${Date.now()}`,
        name: selectedFile.name,
        url: selectedFile.url,
      };

      // 3. 调用父组件的评估方法，传递额外的云端信息
      this.props.onEvaluate({
        file: selectedFile,
        promptText: promptText.trim(),
        excelData,
        headers,
        expertFields,
        // 新增：云端数据信息
        promptInfo,
        fileInfo,
        onComplete: this.handleEvaluateComplete,
      });
    } catch (error) {
      console.error('评估准备失败:', error); // eslint-disable-line no-console
      message.error('评估准备失败，请重试');
      this.setState({ isProcessing: false });
    }
  }

  /**
   * 评估完成回调
   * @param {Object} data - 评估结果数据
   */
  handleEvaluateComplete = async (data) => {
    const { results, promptInfo, fileInfo } = data || {};
    const evaluationResults = results || [];

    this.setState({
      isProcessing: false,
      evaluationResults,
    });

    // 如果有结果且提供了云端信息，创建评估记录
    if (evaluationResults.length > 0 && promptInfo && fileInfo) {
      try {
        // 创建评估记录
        const record = await CloudDataManager.createEvaluationRecord(
          promptInfo,
          fileInfo,
          evaluationResults,
        );

        // 更新评估记录列表（将新记录添加到列表开头，保持时间倒序）
        this.setState((prevState) => {
          return {
            evaluationRecords: [record, ...prevState.evaluationRecords],
          };
        });

        // 如果有专家评判字段，可以进行对比分析
        if (this.state.expertFields && this.state.expertFields.length > 0) {
          this.performComparisonAnalysis(evaluationResults);
        }

        message.success(`评估完成！共处理 ${evaluationResults.length} 条数据，记录已保存到云端`);
      } catch (error) {
        console.error('保存评估记录失败:', error); // eslint-disable-line no-console
        message.warning('评估记录保存失败，但不影响当前结果');
      }
    } else {
      message.success(`评估完成！共处理 ${evaluationResults.length} 条数据`);
    }
  }

  /**
   * 提取专家评判字段
   * @param {Array} headers - Excel表头
   * @returns {Array} 专家评判字段列表
   */
  extractExpertFields = (headers) => {
    const requiredFields = ['内容参考', '风格参考', '结构要求', '最终输出'];

    // 过滤掉必需字段，剩余的就是专家评判字段
    const expertFields = headers.filter((header) => {
      return !requiredFields.includes(header) && header && header.trim() !== '';
    });

    // 验证字段名称安全性
    const validExpertFields = expertFields.filter((field) => {
      // 简单的字段名验证，防止注入
      return typeof field === 'string' && field.length > 0 && field.length < 100;
    });

    return validExpertFields;
  }

  /**
   * 专家字段与AI字段映射
   * @param {string} expertField - 专家字段名
   * @param {Array} aiFields - AI字段列表
   * @returns {string|null} 匹配的AI字段名
   */
  mapExpertFieldToAIField = (expertField, aiFields) => {
    // 移除专家字段中的前缀（如"专家-"），获取核心字段名
    const coreFieldName = expertField.replace(/^专家-?/, '').trim();

    // 在AI字段中查找匹配的字段
    const matchedAIField = aiFields.find((aiField) => {
      // 完全匹配
      if (aiField === coreFieldName) return true;

      // 模糊匹配：检查是否包含核心关键词
      const coreKeywords = coreFieldName.replace(/一致性|符合度|裁决/g, '').trim();
      if (coreKeywords && aiField.includes(coreKeywords)) return true;

      return false;
    });

    return matchedAIField || null;
  }

  /**
   * 值对比逻辑
   * @param {any} aiValue - AI评估值
   * @param {any} expertValue - 专家评估值
   * @returns {boolean} 是否一致
   */
  compareValues = (aiValue, expertValue) => {
    // 处理空值情况
    if (!aiValue && !expertValue) return true; // 都为空认为一致
    if (!aiValue || !expertValue) return false; // 一个为空一个不为空认为不一致

    const aiStr = aiValue.toString().trim().toLowerCase();
    const expertStr = expertValue.toString().trim().toLowerCase();

    // 直接进行字符串完全匹配比较（忽略大小写和前后空格）
    const isConsistent = aiStr === expertStr;

    return isConsistent;
  }

  removeObjectKeysFromResults = (results) => {
    if (!results || !Array.isArray(results)) {
      return results;
    }

    return results.map((result) => {
      if (!result || typeof result !== 'object') {
        return result;
      }

      const cleanedResult = {};
      Object.keys(result).forEach((key) => {
        const value = result[key];
        // 只保留非对象类型的值（包括字符串、数字、布尔值、null、undefined）
        if (!_.isObject(value) && !_.isObjectLike(value)) {
          cleanedResult[key] = value;
        }
        if (value.length < 10) {
          cleanedResult[key] = value;
        }
      });

      return cleanedResult;
    });
  }

  /**
   * 从提示词中解析字段
   * @returns {Array} 解析出的字段列表
   */
  parseFieldsFromPrompt = () => {
    const { promptText } = this.state;

    if (!promptText) return [];

    try {
      // 按行分割提示词
      const lines = promptText.split('\n');

      // 查找最后一行包含JSON格式的行
      for (let i = lines.length - 1; i >= 0; i--) {
        const line = lines[i].trim();
        if (line.startsWith('{') && line.endsWith('}')) {
          try {
            const jsonObj = JSON.parse(line);
            return Object.keys(jsonObj);
          } catch (parseError) {
            // 如果解析失败，继续查找上一行
            continue; // eslint-disable-line no-continue
          }
        }
      }

      // 如果没有找到JSON格式，返回默认字段
      return ['结构要求符合度', '语言风格一致性', '评估结论'];
    } catch (error) {
      console.error('解析提示词字段失败:', error); // eslint-disable-line no-console
      return ['结构要求符合度', '语言风格一致性', '评估结论'];
    }
  }

  /**
   * AI与专家结果对比分析
   * @param {Array} aiResults - AI评估结果
   * @param {Array} expertData - 专家数据
   * @param {Array} expertFields - 专家字段列表
   * @returns {Array} 对比结果
   */
  compareAIWithExpert = (aiResults, expertData, expertFields) => {
    if (!aiResults || !expertData || !expertFields || expertFields.length === 0) {
      return [];
    }

    const comparisons = [];
    // 获取AI评估字段用于映射
    const aiFields = this.parseFieldsFromPrompt();

    // 遍历专家数据，确保按正确的索引对应
    expertData.forEach((expertResult) => {
      // 根据itemIndex查找对应的AI评估结果，确保数据正确对应
      // expertResult.id 对应的是 Excel 行号（从1开始），需要找到对应的AI结果
      const aiResult = aiResults.find((result) => { return result.itemIndex === (expertResult.id - 1); });
      if (!aiResult) return;

      const comparison = {
        id: expertResult.id,
        itemIndex: expertResult.id - 1, // 转换为0基索引
        comparisons: {},
      };

      // 对每个专家字段进行对比
      expertFields.forEach((expertField) => {
        // 建立专家字段与AI字段的映射关系
        const aiFieldName = this.mapExpertFieldToAIField(expertField, aiFields);
        const aiValue = aiFieldName ? (aiResult[aiFieldName] || '') : '';

        // 统一专家数据访问路径，优先使用originalData确保数据一致性
        let expertValue = '';
        if (expertResult.originalData && expertResult.originalData[expertField] !== undefined) {
          expertValue = expertResult.originalData[expertField];
        } else if (expertResult[expertField] !== undefined) {
          expertValue = expertResult[expertField];
        }

        // 进行值对比
        const isConsistent = this.compareValues(aiValue, expertValue);

        comparison.comparisons[expertField] = {
          aiValue,
          expertValue,
          isConsistent,
        };
      });

      comparisons.push(comparison);
    });

    return comparisons;
  }

  /**
   * 生成对比统计分析
   * @param {Array} comparisons - 对比结果
   * @param {Array} expertFields - 专家字段列表
   * @returns {Object} 统计分析结果
   */
  generateComparisonStatistics = (comparisons, expertFields) => {
    if (!comparisons || comparisons.length === 0 || !expertFields || expertFields.length === 0) {
      return null;
    }

    const statistics = {
      totalItems: comparisons.length,
      fieldStatistics: {},
      overallAccuracy: 0,
      detailedAnalysis: [],
    };

    // 为每个专家字段生成统计
    expertFields.forEach((field) => {
      let consistentCount = 0;
      let totalCount = 0;
      const fieldDetails = [];

      comparisons.forEach((comparison) => {
        const fieldComparison = comparison.comparisons[field];
        if (fieldComparison) {
          totalCount++;
          if (fieldComparison.isConsistent) {
            consistentCount++;
          }

          fieldDetails.push({
            itemId: comparison.id,
            aiValue: fieldComparison.aiValue,
            expertValue: fieldComparison.expertValue,
            isConsistent: fieldComparison.isConsistent,
          });
        }
      });

      const accuracy = totalCount > 0 ? (consistentCount / totalCount * 100).toFixed(2) : 0;

      statistics.fieldStatistics[field] = {
        consistentCount,
        totalCount,
        accuracy: parseFloat(accuracy),
        details: fieldDetails,
      };
    });

    // 计算整体准确率
    const sampleAccuracies = [];
    comparisons.forEach((comparison) => {
      let sampleConsistentCount = 0;
      let sampleTotalCount = 0;

      expertFields.forEach((field) => {
        const fieldComparison = comparison.comparisons[field];
        if (fieldComparison) {
          sampleTotalCount++;
          if (fieldComparison.isConsistent) {
            sampleConsistentCount++;
          }
        }
      });

      if (sampleTotalCount > 0) {
        const sampleAccuracy = sampleConsistentCount / sampleTotalCount;
        sampleAccuracies.push(sampleAccuracy);
      }
    });

    // 计算所有样本的平均准确率作为整体一致性
    if (sampleAccuracies.length > 0) {
      const totalAccuracy = sampleAccuracies.reduce((sum, acc) => { return sum + acc; }, 0);
      const averageAccuracy = totalAccuracy / sampleAccuracies.length * 100;
      statistics.overallAccuracy = parseFloat(averageAccuracy.toFixed(2));
    } else {
      statistics.overallAccuracy = 0;
    }

    // 生成详细分析报告
    statistics.detailedAnalysis = this.generateDetailedAnalysis(statistics, expertFields);

    return statistics;
  }

  /**
   * 生成详细分析报告
   * @param {Object} statistics - 统计数据
   * @param {Array} expertFields - 专家字段列表
   * @returns {Array} 详细分析报告
   */
  generateDetailedAnalysis = (statistics, expertFields) => {
    const analysis = [];

    // 整体分析
    analysis.push({
      type: 'overall',
      title: '整体分析',
      content: `共分析 ${statistics.totalItems} 个样本，整体一致性为 ${statistics.overallAccuracy}%`,
    });

    // 各字段分析
    expertFields.forEach((field) => {
      const fieldStat = statistics.fieldStatistics[field];
      if (fieldStat) {
        let level = '优秀';
        if (fieldStat.accuracy < 60) {
          level = '需改进';
        } else if (fieldStat.accuracy < 80) {
          level = '良好';
        }

        analysis.push({
          type: 'field',
          title: `${field} 分析`,
          content: `准确率: ${fieldStat.accuracy}% (${fieldStat.consistentCount}/${fieldStat.totalCount}) - ${level}`,
          level,
          field,
        });
      }
    });

    return analysis;
  }

  /**
   * 执行对比分析
   * @param {Array} evaluationResults - 评估结果
   */
  performComparisonAnalysis = (evaluationResults) => {
    const { excelData, headers, expertFields } = this.state;

    if (!excelData || !headers || !expertFields || expertFields.length === 0) {
      return;
    }

    try {
      // 处理Excel数据获取专家评判结果
      const processedData = this.processExcelData(excelData, headers);

      // 进行AI与专家结果对比
      const cleanedResults = this.removeObjectKeysFromResults(evaluationResults);
      const comparisons = this.compareAIWithExpert(cleanedResults, processedData, expertFields);

      // 生成统计分析
      const statistics = this.generateComparisonStatistics(comparisons, expertFields);

      // 更新状态
      this.setState({
        comparisonStatistics: statistics,
        showStatistics: statistics !== null,
      });

      if (statistics) {
        message.success(`对比分析完成！整体一致性: ${statistics.overallAccuracy}%`);
      }
    } catch (error) {
      console.error('对比分析失败:', error); // eslint-disable-line no-console
      message.error('对比分析失败，请检查数据格式');
    }
  }

  /**
   * 预览优化数据
   */
  handlePreviewOptimizeData = () => {
    const { promptText, evaluationRecords } = this.state;
    const evaluationResults = this.props.evaluationResults || this.state.evaluationResults;

    // 检查是否有评估历史数据
    if (!evaluationRecords.length) {
      message.warning('暂无评估历史数据，请先进行评估以生成历史记录');
      return;
    }

    // 调用父组件的预览方法
    this.props.onPreviewOptimizeData({
      promptText,
      evaluationResults: evaluationResults.length > 0 ? evaluationResults : [], // 当前评估结果可以为空
    });
  }

  /**
   * 优化提示词（通过预览数据）
   * 注意：这个方法现在只是触发预览，实际优化在预览抽屉中进行
   */
  handleOptimizePrompt = () => {
    const { evaluationRecords } = this.state;

    // 检查是否有评估历史数据
    if (!evaluationRecords.length) {
      message.warning('暂无评估历史数据，请先进行评估以生成历史记录');
      return;
    }

    // 直接调用预览优化数据，因为优化必须先预览
    this.handlePreviewOptimizeData();
  }

  /**
   * 优化完成回调
   * @param {string} optimizedPrompt - 优化后的提示词
   */
  handleOptimizeComplete = async (optimizedPrompt) => {
    if (!optimizedPrompt) {
      // 不需要设置isOptimizing状态，由父组件管理
      return;
    }

    try {
      // 同步优化后的提示词到云端
      const promptInfo = await CloudDataManager.syncPromptToCloud(
        optimizedPrompt,
        '优化版本',
      );

      this.setState({
        promptText: optimizedPrompt,
        currentPromptInfo: promptInfo,
      });

      message.success('优化后的提示词已同步到云端');
    } catch (error) {
      console.error('同步优化提示词失败:', error); // eslint-disable-line no-console

      // 即使同步失败，也更新本地状态
      this.setState({
        promptText: optimizedPrompt,
      });

      message.warning('提示词已更新，但同步到云端失败');
    }
  }

  /**
   * 初始化批量操作管理器
   */
  initializeBatchOperationManager = () => {
    this.batchOperationManager = new BatchOperationManager();

    // 设置回调函数
    this.batchOperationManager.setCallbacks({
      onProgressUpdate: this.handleBatchProgressUpdate,
      onIterationComplete: this.handleBatchIterationComplete,
      onComplete: this.handleBatchComplete,
      onError: this.handleBatchError,
      onStepChange: this.handleBatchStepChange,
    });
  }

  /**
   * 显示批量操作配置弹窗
   */
  showBatchOperationModal = () => {
    const { promptText, selectedFile, evaluationRecords } = this.state;

    if (!promptText.trim()) {
      message.warning('请先输入评估提示词');
      return;
    }

    if (!selectedFile) {
      message.warning('请先选择要评估的文件');
      return;
    }

    if (!evaluationRecords.length) {
      message.warning('暂无评估历史数据，请先进行评估以生成历史记录');
      return;
    }

    this.setState({ showBatchModal: true });
  }

  /**
   * 隐藏批量操作配置弹窗
   */
  hideBatchOperationModal = () => {
    this.setState({ showBatchModal: false });
  }

  /**
   * 开始批量操作
   * @param {Object} config - 批量操作配置
   */
  startBatchOperation = async (config) => {
    const { iterations } = config;
    const { promptText, selectedFile, excelData, headers, expertFields, currentPromptInfo } = this.state;

    try {
      // 隐藏配置弹窗，显示进度弹窗
      this.setState({
        showBatchModal: false,
        showBatchProgress: true,
        isBatchRunning: true,
      });

      // 准备初始数据
      const initialData = {
        selectedFile,
        excelData,
        headers,
        expertFields,
        currentPromptInfo,
      };

      // 开始批量操作
      await this.batchOperationManager.startBatchOperation({
        iterations,
        evaluateFunction: this.executeBatchEvaluation,
        optimizeFunction: this.executeBatchOptimization,
        initialData,
        initialPromptText: promptText,
      });
    } catch (error) {
      message.error(`批量操作启动失败: ${error.message}`);
      this.setState({
        showBatchModal: false,
        showBatchProgress: false,
        isBatchRunning: false,
      });
    }
  }

  /**
   * 执行批量评估
   * @param {string} promptText - 提示词
   * @returns {Promise} 评估结果
   */
  executeBatchEvaluation = (promptText) => {
    return new Promise((resolve) => {
      const { selectedFile, excelData, headers, expertFields, currentPromptInfo } = this.state;

      // 准备文件信息
      const fileInfo = {
        id: selectedFile.id || `file_${Date.now()}`,
        name: selectedFile.name,
        url: selectedFile.url,
      };

      // 为批量操作中的每次迭代创建独特的提示词信息
      const batchPromptInfo = {
        ...currentPromptInfo,
        content: promptText.trim(),
        hash: this.generatePromptHash(promptText.trim()),
        // 添加批量操作标识
        batchOperationId: this.batchOperationManager?.currentOperationId,
        iterationIndex: this.batchOperationManager?.currentIteration,
      };

      // 调用父组件的评估方法
      this.props.onEvaluate({
        file: selectedFile,
        promptText: promptText.trim(),
        excelData,
        headers,
        expertFields,
        promptInfo: batchPromptInfo,
        fileInfo,
        onComplete: async (result) => {
          try {
            // 为批量操作中的每次迭代保存评估记录（与手动评估相同的方式）
            if (result.results && result.results.length > 0) {
              const record = await CloudDataManager.createEvaluationRecord(
                batchPromptInfo,
                fileInfo,
                result.results,
              );

              // 更新评估记录列表（将新记录添加到列表开头）
              this.setState((prevState) => {
                return {
                  evaluationRecords: [record, ...prevState.evaluationRecords],
                };
              });
            }
          } catch (error) {
            console.error('批量操作评估记录保存失败:', error); // eslint-disable-line no-console
            // 不阻断批量操作流程，只记录错误
          }

          resolve(result.results);
        },
      });
    });
  }

  /**
   * 执行批量优化
   * @param {string} promptText - 当前提示词
   * @param {Array} evaluationResults - 评估结果
   * @returns {Promise} 优化结果
   */
  executeBatchOptimization = (promptText, evaluationResults) => {
    return new Promise((resolve) => {
      // 调用父组件的优化预览方法
      this.props.onPreviewOptimizeData({
        promptText,
        evaluationResults,
        onOptimizeComplete: (optimizedPrompt, optimizeReason) => {
          resolve({
            optimizedPrompt,
            optimizeReason,
          });
        },
      });
    });
  }

  /**
   * 处理批量操作进度更新
   * @param {Object} progressData - 进度数据
   */
  handleBatchProgressUpdate = (progressData) => {
    this.setState({
      batchProgressData: progressData,
    });
  }

  /**
   * 生成提示词哈希值
   * @param {string} promptText - 提示词文本
   * @returns {string} 哈希值
   */
  generatePromptHash = (promptText) => {
    // 简单的哈希算法，用于标识提示词的唯一性
    let hash = 0;
    if (promptText.length === 0) return hash.toString();

    for (let i = 0; i < promptText.length; i++) {
      const char = promptText.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash &= hash; // 转换为32位整数
    }

    return Math.abs(hash).toString(36);
  }

  /**
   * 处理批量操作迭代完成
   * @param {Object} iterationResult - 迭代结果
   * @param {number} iteration - 迭代次数
   * @param {number} totalIterations - 总迭代次数
   */
  handleBatchIterationComplete = (iterationResult, iteration, totalIterations) => {
    // 更新当前提示词
    if (iterationResult.optimizedPrompt) {
      this.setState({
        promptText: iterationResult.optimizedPrompt,
      });
    }

    message.success(`第${iteration}/${totalIterations}次迭代完成，评估记录已保存`);
  }

  /**
   * 处理批量操作完成
   * @param {Object} operationRecord - 操作记录
   */
  handleBatchComplete = (operationRecord) => {
    this.setState({
      isBatchRunning: false,
      showBatchProgress: false,
      showBatchSummary: true,
      batchOperationRecord: operationRecord,
    });

    const { summary } = operationRecord;

    // 统计批量操作中保存的评估记录数量
    const batchRecordsCount = operationRecord.iterations.filter((iter) => { return iter.evaluationResults; }).length;

    message.success(
      `批量操作完成！成功率: ${summary.successRate}%，共${summary.completedIterations}次成功迭代，已保存${batchRecordsCount}条评估记录`,
    );

    // 刷新评估记录列表以显示最新的批量操作记录
    this.fetchEvaluationRecords();
  }

  /**
   * 处理批量操作错误
   * @param {Error} error - 错误对象
   * @param {Object} operationRecord - 操作记录
   */
  handleBatchError = (error) => {
    this.setState({
      isBatchRunning: false,
    });

    message.error(`批量操作失败: ${error.message}`);
  }

  /**
   * 处理批量操作步骤变化
   * @param {Object} stepInfo - 步骤信息
   */
  handleBatchStepChange = (stepInfo) => {
    console.log('批量操作步骤变化:', stepInfo); // eslint-disable-line no-console
  }

  /**
   * 暂停批量操作
   */
  pauseBatchOperation = () => {
    if (this.batchOperationManager) {
      this.batchOperationManager.pauseOperation();
    }
  }

  /**
   * 恢复批量操作
   */
  resumeBatchOperation = () => {
    if (this.batchOperationManager) {
      this.batchOperationManager.resumeOperation();
    }
  }

  /**
   * 停止批量操作
   */
  stopBatchOperation = () => {
    if (this.batchOperationManager) {
      this.batchOperationManager.cancelOperation();
    }
  }

  /**
   * 隐藏批量操作进度弹窗
   */
  hideBatchProgressModal = () => {
    const { isBatchRunning } = this.state;

    if (isBatchRunning) {
      // 如果还在运行，只是最小化
      this.setState({ showBatchProgress: false });
    } else {
      // 如果已完成，关闭弹窗
      this.setState({
        showBatchProgress: false,
        batchProgressData: {},
      });
    }
  }

  /**
   * 隐藏批量操作结果汇总弹窗
   */
  hideBatchSummaryModal = () => {
    this.setState({
      showBatchSummary: false,
      batchOperationRecord: null,
    });
  }

  /**
   * 获取可用文件列表
   */
  fetchAvailableFiles = async () => {
    try {
      this.setState({ isLoadingFiles: true });
      const fileData = await OSSFileHelper.fetchData('script-evaluation-v2', 'file-management');

      const files = fileData && fileData.files && Array.isArray(fileData.files)
        ? fileData.files
        : [];

      this.setState({
        availableFiles: files,
        isLoadingFiles: false,
      });
    } catch (error) {
      console.error('获取文件列表失败:', error); // eslint-disable-line no-console
      message.error('获取文件列表失败');
      this.setState({ isLoadingFiles: false });
    }
  }

  /**
   * 处理Excel数据，转换为标准格式
   * @param {Array} excelData - Excel原始数据
   * @param {Array} headers - 表头
   * @returns {Array} 处理后的数据
   */
  processExcelData = (excelData, headers) => {
    if (!excelData || !headers) {
      return [];
    }

    try {
      return excelData.map((row, index) => {
        const item = {
          id: index + 1,
          originalData: {},
        };

        // 将行数据映射到对应的表头
        headers.forEach((header, headerIndex) => {
          if (header && header.trim()) {
            const cellValue = row[headerIndex];
            // 保持原始数据类型和格式
            item.originalData[header] = cellValue !== undefined ? cellValue : '';
            item[header] = cellValue !== undefined ? cellValue : '';
          }
        });

        return item;
      });
    } catch (error) {
      console.error('处理Excel数据失败:', error); // eslint-disable-line no-console
      return [];
    }
  }

  /**
   * 合并评估数据（AI结果 + 专家数据）
   * @returns {Array} 合并后的数据
   */
  mergeEvaluationData = () => {
    const { evaluationResults, excelData, headers } = this.state;

    // 使用props中的评估结果，如果没有则使用state中的
    const results = this.props.evaluationResults || evaluationResults;

    if (!results || results.length === 0 || !excelData || !headers) {
      return [];
    }

    try {
      // 处理Excel数据，获取原始格式
      const processedData = this.processExcelData(excelData, headers);

      // 获取动态字段
      const dynamicFields = this.parseFieldsFromPrompt();

      // 合并数据
      const mergedData = processedData.map((item) => {
        // 根据itemIndex查找对应的评估结果，确保数据正确对应
        // item.id 对应的是 Excel 行号（从1开始），itemIndex 是从0开始的索引
        const evaluationResult = results.find((result) => { return result.itemIndex === (item.id - 1); }) || {};

        // 构建合并后的数据对象，包含所有原始列 + 评估结果
        const mergedItem = {
          key: item.id,
          id: item.id,
          // 展开所有原始数据（专家数据保持原始字段名）
          ...item.originalData,
        };

        // 动态追加AI评估结果字段（添加ai_前缀避免与专家字段冲突）
        dynamicFields.forEach((field) => {
          const aiFieldName = `ai_${field}`;
          mergedItem[aiFieldName] = evaluationResult[field] || '';
        });

        return mergedItem;
      });

      return mergedData;
    } catch (error) {
      console.error('数据合并失败:', error); // eslint-disable-line no-console
      return [];
    }
  }

  /**
   * 从URL下载并读取Excel文件
   * @param {string} url - 文件URL
   */
  readExcelFromUrl = async (url) => {
    return new Promise((resolve, reject) => {
      fetch(url)
        .then((response) => {
          if (!response.ok) {
            throw new Error('文件下载失败');
          }
          return response.arrayBuffer();
        })
        .then((arrayBuffer) => {
          try {
            const data = new Uint8Array(arrayBuffer);
            const workbook = XLSX.read(data, { type: 'array' });
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];

            // 保持原始数据类型，不进行自动转换
            const jsonData = XLSX.utils.sheet_to_json(worksheet, {
              header: 1,
              raw: true, // 保持原始数据类型
              defval: undefined, // 空单元格使用undefined而不是空字符串
            });

            if (jsonData.length === 0) {
              reject(new Error('Excel文件为空'));
              return;
            }

            const headers = jsonData[0];
            const rows = jsonData.slice(1).filter((row) => {
              return row.some((cell) => {
                return cell !== undefined && cell !== null && cell !== '';
              });
            });

            resolve({
              headers,
              rows,
            });
          } catch (parseError) {
            reject(new Error('Excel文件解析失败'));
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * 处理Excel数据
   * @param {Array} data - Excel行数据
   * @param {Array} headers - Excel表头
   */
  processExcelData = (data, headers) => {
    // 查找必需的列
    const contentRefIndex = headers.findIndex((header) => { return header === '内容参考'; });
    const styleRefIndex = headers.findIndex((header) => { return header === '风格参考'; });
    const structureIndex = headers.findIndex((header) => { return header === '结构要求'; });
    const outputIndex = headers.findIndex((header) => { return header === '最终输出'; });

    if (contentRefIndex === -1) {
      throw new Error('Excel文件中未找到"内容参考"列，请检查文件格式');
    }
    if (styleRefIndex === -1) {
      throw new Error('Excel文件中未找到"风格参考"列，请检查文件格式');
    }
    if (structureIndex === -1) {
      throw new Error('Excel文件中未找到"结构要求"列，请检查文件格式');
    }
    if (outputIndex === -1) {
      throw new Error('Excel文件中未找到"最终输出"列，请检查文件格式');
    }

    return data.map((row, index) => {
      const contentRefValue = String(row[contentRefIndex] || '');
      const styleRefValue = String(row[styleRefIndex] || '');
      const structureValue = String(row[structureIndex] || '');
      const outputValue = String(row[outputIndex] || '');

      // 构建完整的原始数据对象，包含所有Excel列
      const originalData = {};
      headers.forEach((header, headerIndex) => {
        const cellValue = row[headerIndex];
        originalData[header] = cellValue !== undefined && cellValue !== null ? cellValue : '';
      });

      return {
        id: index + 1,
        contentRef: contentRefValue.trim(),
        styleRef: styleRefValue.trim(),
        structure: structureValue.trim(),
        output: outputValue.trim(),
        originalData,
      };
    });
  }

  /**
   * 提取专家评判字段
   * @param {Array} headers - Excel表头
   */
  extractExpertFields = (headers) => {
    const excludeFields = ['内容参考', '风格参考', '结构要求', '最终输出'];
    return headers.filter((header) => { return !excludeFields.includes(header); });
  }

  /**
   * 处理文件选择
   * @param {string} fileId - 选中的文件ID
   */
  handleFileSelect = async (fileId) => {
    const { availableFiles } = this.state;
    const selectedFile = availableFiles.find((file) => { return file.id === fileId; });

    if (selectedFile) {
      this.setState({
        selectedFile: {
          name: selectedFile.name,
          url: selectedFile.url,
          type: selectedFile.type,
          size: selectedFile.size,
        },
        fileName: selectedFile.name,
        selectedFileFromList: fileId,
        isFileProcessing: true,
      });

      try {
        // 下载并读取Excel文件
        const data = await this.readExcelFromUrl(selectedFile.url);

        // 验证必需的列是否存在
        const requiredColumns = ['内容参考', '风格参考', '结构要求', '最终输出'];
        const missingColumns = requiredColumns.filter((col) => { return !data.headers.includes(col); });

        if (missingColumns.length > 0) {
          throw new Error(`Excel文件缺少必需的列：${missingColumns.join('、')}。请确保文件包含"内容参考"、"风格参考"、"结构要求"、"最终输出"列。`);
        }

        // 验证数据完整性
        try {
          this.processExcelData(data.rows, data.headers);
        } catch (validationError) {
          throw validationError;
        }

        // 提取专家评判字段
        const expertFields = this.extractExpertFields(data.headers);

        this.setState({
          excelData: data.rows,
          headers: data.headers,
          expertFields,
          isFileProcessing: false,
        });

        // 通知父组件文件变化
        this.props.onFileChange({
          name: selectedFile.name,
          url: selectedFile.url,
          type: selectedFile.type,
          size: selectedFile.size,
          excelData: data.rows,
          headers: data.headers,
          expertFields,
        });

        const expertFieldsText = expertFields.length > 0
          ? `，检测到专家评判字段：${expertFields.join('、')}`
          : '';
        message.success(`文件 ${selectedFile.name} 选择成功！共${data.rows.length}行数据${expertFieldsText}`);
      } catch (error) {
        console.error('文件处理失败:', error); // eslint-disable-line no-console
        this.setState({
          isFileProcessing: false,
          selectedFile: null,
          fileName: '',
          selectedFileFromList: null,
          excelData: null,
          headers: null,
          expertFields: [],
        });
        message.error(error.message || '文件处理失败，请检查文件格式！');
      }
    }
  }

  /**
   * 导出评估结果到Excel
   */
  exportEvaluationResults = () => {
    const mergedData = this.mergeEvaluationData();

    if (!mergedData || mergedData.length === 0) {
      message.warning('暂无评估结果可导出');
      return;
    }

    try {
      // 创建工作簿
      const wb = XLSX.utils.book_new();

      // 准备导出数据
      const exportData = mergedData.map((item) => {
        const exportItem = { ...item };
        delete exportItem.key; // 移除内部字段
        return exportItem;
      });

      // 创建工作表
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      const colWidths = [];
      const headers = Object.keys(exportData[0] || {});

      headers.forEach((header) => {
        if (header === '内容参考' || header === '风格参考' || header === '结构要求') {
          colWidths.push({ wch: 30 });
        } else if (header === '最终输出') {
          colWidths.push({ wch: 50 });
        } else if (header.startsWith('ai_')) {
          colWidths.push({ wch: 20 });
        } else {
          colWidths.push({ wch: 15 });
        }
      });

      ws['!cols'] = colWidths;

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '评估结果');

      // 生成文件名
      const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
      const fileName = `脚本评估结果_${timestamp}.xlsx`;

      // 导出文件
      XLSX.writeFile(wb, fileName);

      message.success(`评估结果已导出到 ${fileName}`);
    } catch (error) {
      console.error('导出失败:', error); // eslint-disable-line no-console
      message.error('导出失败，请重试');
    }
  }

  /**
   * 导出批量操作的所有迭代评估结果
   * @param {Object} operationRecord - 批量操作记录
   */
  exportBatchEvaluationResults = async (operationRecord) => {
    if (!operationRecord || !operationRecord.iterations || operationRecord.iterations.length === 0) {
      message.warning('暂无批量操作结果可导出');
      return;
    }

    try {
      // 创建工作簿
      const wb = XLSX.utils.book_new();

      // 为每次迭代创建一个工作表
      for (let i = 0; i < operationRecord.iterations.length; i++) {
        const iteration = operationRecord.iterations[i];

        if (iteration.evaluationResults && iteration.evaluationResults.length > 0) {
          // 合并原始数据和评估结果
          const { excelData, headers } = operationRecord.initialData;
          const mergedData = this.mergeIterationEvaluationData(
            excelData,
            headers,
            iteration.evaluationResults,
          );

          // 创建工作表
          const ws = XLSX.utils.json_to_sheet(mergedData);

          // 设置列宽
          const colWidths = [];
          const sheetHeaders = Object.keys(mergedData[0] || {});

          sheetHeaders.forEach((header) => {
            if (header === '内容参考' || header === '风格参考' || header === '结构要求') {
              colWidths.push({ wch: 30 });
            } else if (header === '最终输出') {
              colWidths.push({ wch: 50 });
            } else if (header.startsWith('ai_')) {
              colWidths.push({ wch: 20 });
            } else {
              colWidths.push({ wch: 15 });
            }
          });

          ws['!cols'] = colWidths;

          // 添加工作表到工作簿
          XLSX.utils.book_append_sheet(wb, ws, `第${iteration.iteration}次迭代`);
        }
      }

      // 添加批量操作摘要工作表
      const summaryData = [
        { 项目: '批量操作ID', 值: operationRecord.id },
        { 项目: '开始时间', 值: moment(operationRecord.startTime).format('YYYY-MM-DD HH:mm:ss') },
        { 项目: '结束时间', 值: moment(operationRecord.endTime).format('YYYY-MM-DD HH:mm:ss') },
        { 项目: '总迭代次数', 值: operationRecord.totalIterations },
        { 项目: '完成迭代次数', 值: operationRecord.completedIterations },
        { 项目: '成功率', 值: `${operationRecord.summary?.successRate || 0}%` },
        { 项目: '初始提示词', 值: operationRecord.initialPromptText },
        { 项目: '最终提示词', 值: operationRecord.summary?.finalPromptText || '' },
      ];

      const summaryWs = XLSX.utils.json_to_sheet(summaryData);
      summaryWs['!cols'] = [{ wch: 20 }, { wch: 80 }];
      XLSX.utils.book_append_sheet(wb, summaryWs, '批量操作摘要');

      // 生成文件名
      const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
      const fileName = `批量评估结果_${operationRecord.completedIterations}次迭代_${timestamp}.xlsx`;

      // 导出文件
      XLSX.writeFile(wb, fileName);

      message.success(`批量评估结果已导出到 ${fileName}`);
    } catch (error) {
      console.error('批量结果导出失败:', error); // eslint-disable-line no-console
      message.error('批量结果导出失败，请重试');
    }
  }

  /**
   * 合并单次迭代的评估数据
   * @param {Array} excelData - 原始Excel数据
   * @param {Array} headers - Excel表头
   * @param {Array} evaluationResults - 评估结果
   * @returns {Array} 合并后的数据
   */
  mergeIterationEvaluationData = (excelData, headers, evaluationResults) => {
    const dynamicFields = this.parseFieldsFromPrompt();

    return excelData.map((row, index) => {
      // 根据索引查找对应的评估结果
      const evaluationResult = evaluationResults.find((result) => {
        return result.itemIndex === index;
      }) || {};

      // 构建合并后的数据对象
      const mergedItem = {
        序号: index + 1,
        // 展开所有原始数据
        ...row,
      };

      // 动态追加AI评估结果字段
      dynamicFields.forEach((field) => {
        const aiFieldName = `ai_${field}`;
        mergedItem[aiFieldName] = evaluationResult[field] || '';
      });

      return mergedItem;
    });
  }

  /**
   * 渲染结果表格列
   */
  renderResultColumns = () => {
    const { expertFields } = this.state;

    // ID列配置（使用TableColumnHelper，添加排序功能）
    const idColumn = {
      ...TableColumnHelper.createTableColumn({
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 80,
        align: 'center',
      }),
      sorter: (a, b) => { return a.id - b.id; },
      defaultSortOrder: 'ascend',
    };

    // 基础Excel列配置
    const baseTextColumnConfigs = [
      { title: '内容参考', dataIndex: '内容参考', key: 'contentRef' },
      { title: '风格参考', dataIndex: '风格参考', key: 'styleRef' },
      { title: '结构要求', dataIndex: '结构要求', key: 'structure' },
      { title: '最终输出', dataIndex: '最终输出', key: 'output' },
    ];

    // 使用工具函数创建标准化的基础文本列
    const baseTextColumns = TableColumnHelper.createBaseTextColumns(baseTextColumnConfigs);

    // 专家评判字段列（如果存在）
    const expertColumns = expertFields && expertFields.length > 0
      ? TableColumnHelper.createColumnsFromFields(expertFields, '专家', 'expert')
      : [];

    // AI评估字段列（动态解析，添加排序功能）
    const dynamicFields = this.parseFieldsFromPrompt();

    const aiColumns = dynamicFields && dynamicFields.length > 0
      ? TableColumnHelper.createColumnsFromFields(dynamicFields, 'AI', 'ai', {
        dataIndexPrefix: 'ai_', width: 120, align: 'center',
      })
      : [];

    return [idColumn, ...baseTextColumns, ...expertColumns, ...aiColumns];
  }
  /**
   * 渲染提示词历史抽屉
   * @returns {JSX.Element} 提示词历史抽屉
   */
  renderPromptHistoryDrawer = () => {
    const { showPromptHistory, cloudPrompts } = this.state;

    return (
      <Drawer
        title="提示词历史"
        open={showPromptHistory}
        onClose={() => { this.setState({ showPromptHistory: false }); }}
        width="66vw"
        placement="right"
      >
        {cloudPrompts.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px 20px' }}>
            <div style={{ fontSize: '16px', color: '#999' }}>
              暂无提示词历史记录
            </div>
          </div>
        ) : (
          <List
            dataSource={cloudPrompts}
            renderItem={(prompt) => {
              return (
                <List.Item
                  key={prompt.id}
                  actions={[
                    <Button
                      key="use"
                      type="primary"
                      size="small"
                      onClick={() => {
                        this.setState({
                          promptText: prompt.content,
                          currentPromptInfo: prompt,
                          showPromptHistory: false,
                        });
                      }}
                    >
                      使用
                    </Button>,
                  ]}
                  style={{
                    padding: '16px 0',
                    borderBottom: '1px solid #f0f0f0',
                  }}
                >
                  <List.Item.Meta
                    title={
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span style={{ fontSize: '14px', color: '#666' }}>
                          创建时间: {moment(prompt.createdAt || prompt.lastUsed).format('YYYY-MM-DD HH:mm')}
                        </span>
                      </div>
                    }
                    description={
                      <div style={{ marginTop: '8px' }}>
                        <div
                          style={{
                            background: '#f8f9fa',
                            border: '1px solid #e9ecef',
                            borderRadius: '4px',
                            padding: '12px',
                            maxHeight: '200px',
                            overflow: 'auto',
                            fontSize: '13px',
                            lineHeight: '1.5',
                            whiteSpace: 'pre-wrap',
                            wordBreak: 'break-word',
                          }}
                        >
                          {prompt.content}
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              );
            }}
            pagination={{
              pageSize: 5,
              showSizeChanger: false,
              showQuickJumper: true,
              showTotal: (total, range) => { return `第 ${range[0]}-${range[1]} 条，共 ${total} 条`; },
            }}
          />
        )}
      </Drawer>
    );
  }


  /**
   * 渲染对比分析报告
   * @returns {JSX.Element} 对比分析报告组件
   */
  renderComparisonAnalysisReport = () => {
    const { showStatistics, comparisonStatistics, expertFields } = this.state;

    if (!showStatistics || !comparisonStatistics || !expertFields || expertFields.length === 0) {
      return null;
    }

    return (
      <div className="statistics-section" style={{ marginTop: 24, marginBottom: 24 }}>
        <h3>AI与专家对比分析报告</h3>

        {/* 整体统计概览 */}
        <div style={{ background: '#f5f5f5', padding: 16, borderRadius: 6, marginBottom: 16 }}>
          <div style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>
            整体一致性: {comparisonStatistics.overallAccuracy}%
          </div>
          <div style={{ color: '#666' }}>
            共分析 {comparisonStatistics.totalItems} 个样本，
            涉及 {expertFields.length} 个评判维度
          </div>
        </div>

        {/* 各字段详细统计 */}
        <div style={{ marginBottom: 16 }}>
          <h4 style={{ marginBottom: 12 }}>各维度一致性分析</h4>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: 16 }}>
            {expertFields.map((field) => {
              const fieldStat = comparisonStatistics.fieldStatistics[field];
              if (!fieldStat) return null;

              let statusColor = '#52c41a'; // 绿色 - 优秀
              let statusText = '优秀';
              if (fieldStat.accuracy < 60) {
                statusColor = '#ff4d4f'; // 红色 - 需改进
                statusText = '需改进';
              } else if (fieldStat.accuracy < 80) {
                statusColor = '#faad14'; // 橙色 - 良好
                statusText = '良好';
              }

              return (
                <div
                  key={field}
                  style={{
                    border: '1px solid #d9d9d9',
                    borderRadius: 6,
                    padding: 12,
                    background: '#fff',
                  }}
                >
                  <div style={{ fontWeight: 'bold', marginBottom: 8 }}>{field}</div>
                  <div style={{ marginBottom: 4 }}>
                    准确率:
                    <span style={{ color: statusColor, fontWeight: 'bold' }}>
                      {fieldStat.accuracy}%
                    </span>
                  </div>
                  <div style={{ marginBottom: 4, fontSize: 12, color: '#666' }}>
                    一致: {fieldStat.consistentCount} / 总计: {fieldStat.totalCount}
                  </div>
                  <div style={{ fontSize: 12, color: statusColor }}>
                    评级: {statusText}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  }

  /**
   * 渲染组件
   */
  render() {
    const {
      fileName,
      promptText,
      availableFiles,
      selectedFileFromList,
      isLoadingFiles,
      isFileProcessing,
    } = this.state;

    // 使用 props 中的状态，如果没有则使用 state 中的
    const isProcessing = this.props.isProcessing || this.state.isProcessing;

    return (
      <div className="evaluation-panel">
        {/* 文件选择器 */}
        <div className="file-selector prompt-input">
          <Form.Item label="选择文件" labelCol={{ span: 2 }} wrapperCol={{ span: 18 }}>
            <Select
              placeholder="请选择已上传的文件"
              value={selectedFileFromList}
              onChange={this.handleFileSelect}
              loading={isLoadingFiles || isFileProcessing}
              disabled={isFileProcessing}
              allowClear
              style={{ width: '100%' }}
            >
              {availableFiles.map((file) => {
                return (
                  <Select.Option key={file.id} value={file.id}>
                    {file.name}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          {isFileProcessing && (
            <div className="processing-info" style={{ marginTop: 8, color: '#1890ff' }}>
              正在处理Excel文件，请稍候...
            </div>
          )}
        </div>

        {/* 提示词输入区域 */}
        <div className="prompt-input">
          <Form.Item
            label="评估提示词"
            labelCol={{ span: 2 }}
            wrapperCol={{ span: 18 }}
            help={
              <>
                {this.state.isCloudSyncing && (
                  <span style={{ color: '#1890ff', fontSize: '12px' }}>正在同步到云端...</span>
                )}
                {this.state.currentPromptInfo && (
                  <span style={{ color: '#52c41a', fontSize: '12px' }}>已同步</span>
                )}
              </>
            }
          >
            <div>
              <Button
                type="link"
                size="small"
                onClick={() => { this.props.onOpenWorkflow(); }}
                style={{ padding: 0, height: 'auto' }}
              >
                打开工作流 [文稿判定]
              </Button>
              <Button
                type="link"
                size="small"
                onClick={() => { this.props.onOpenWorkflow(true); }}
                style={{ padding: 0, height: 'auto' }}
              >
                打开工作流 [修正提示词]
              </Button>
            </div>
            <Input.TextArea
              rows={6}
              value={promptText}
              placeholder="请输入文稿评估的提示词..."
              onChange={this.handlePromptChange}
              disabled={this.state.isLoadingPrompts}
            />
          </Form.Item>
        </div>

        {/* 操作按钮组 */}
        <div className="action-buttons">
          <Space size="large">
            <Button
              type="primary"
              onClick={this.handleEvaluate}
              loading={isProcessing}
              disabled={isProcessing || !fileName}
            >
              {isProcessing ? `评估中 (${this.props.completedItems}/${this.props.totalItems})` : '开始评估'}
            </Button>
            <Button
              type="default"
              onClick={this.handleOptimizePrompt}
              disabled={isProcessing || !this.state.evaluationRecords.length}
            >
              优化提示词
            </Button>
            <Button
              type="default"
              icon={<ThunderboltOutlined />}
              onClick={this.showBatchOperationModal}
              disabled={isProcessing || this.state.isBatchRunning || !this.state.evaluationRecords.length}
              style={{ background: '#f6ffed', borderColor: '#b7eb8f', color: '#52c41a' }}
            >
              批量评估优化
            </Button>
            <Button
              type="default"
              icon={<HistoryOutlined />}
              onClick={this.showHistoryDrawer}
              disabled={isProcessing}
            >
              评估历史 ({this.state.evaluationRecords.length})
            </Button>
          </Space>
          {isProcessing && (
            <div className="processing-info">
              正在处理评估请求，已完成 {this.props.completedItems}/{this.props.totalItems} 项...
            </div>
          )}
        </div>

        {/* AI与专家对比分析报告 */}
        {this.renderComparisonAnalysisReport()}

        {/* 结果展示表格 */}
        {
          (() => {
            const mergedData = this.mergeEvaluationData();
            return mergedData.length > 0;
          })() && (
            <div className="results-table">
              <div
                className="results-header"
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '16px',
                }}
              >
                <h3>评估结果 ({this.mergeEvaluationData().length} 条)</h3>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={this.exportEvaluationResults}
                  className="export-btn"
                >
                  导出结果
                </Button>
              </div>
              <Table
                columns={this.renderResultColumns()}
                dataSource={this.mergeEvaluationData()}
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => { return `第 ${range[0]}-${range[1]} 条，共 ${total} 条`; },
                  pageSizeOptions: ['10', '20', '50', '100'],
                }}
                scroll={{ x: 1200 }}
                size="small"
                bordered
                rowKey="id"
                sticky
                showSorterTooltip={false}
              />
            </div>
          )
        }

        {/* 提示词历史抽屉 */}
        {this.renderPromptHistoryDrawer()}

        {/* 评估历史记录抽屉 */}
        <EvaluationHistoryDrawer
          visible={this.state.showHistoryDrawer}
          onClose={this.closeHistoryDrawer}
          records={this.state.evaluationRecords}
          loading={this.state.isLoadingHistory}
          onDeleteRecord={this.deleteRecord}
        />

        {/* 批量操作配置弹窗 */}
        <BatchOperationModal
          visible={this.state.showBatchModal}
          onCancel={this.hideBatchOperationModal}
          onConfirm={this.startBatchOperation}
          loading={this.state.isBatchRunning}
          currentPromptText={this.state.promptText}
          hasEvaluationData={this.state.evaluationRecords.length > 0}
        />

        {/* 批量操作进度弹窗 */}
        <BatchOperationProgress
          visible={this.state.showBatchProgress}
          onCancel={this.hideBatchProgressModal}
          onPause={this.pauseBatchOperation}
          onResume={this.resumeBatchOperation}
          onStop={this.stopBatchOperation}
          progressData={this.state.batchProgressData}
        />

        {/* 批量操作结果汇总弹窗 */}
        <BatchOperationSummary
          visible={this.state.showBatchSummary}
          onClose={this.hideBatchSummaryModal}
          operationRecord={this.state.batchOperationRecord}
          onExportBatchResults={this.exportBatchEvaluationResults}
        />
      </div>
    );
  }
}

// 导出CloudDataManager供其他组件使用
export { CloudDataManager };
