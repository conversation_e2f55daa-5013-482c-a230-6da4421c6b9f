import { PlayCircleOutlined, SettingOutlined } from '@ant-design/icons';
import { Card, Form, InputNumber, Modal, Typography } from 'antd';
import PropTypes from 'prop-types';
import React, { Component } from 'react';

const { Text, Title } = Typography;

/**
 * 批量操作配置弹窗组件
 * 用于配置批量评估优化的参数
 */
export default class BatchOperationModal extends Component {
  static propTypes = {
    visible: PropTypes.bool.isRequired,
    onCancel: PropTypes.func.isRequired,
    onConfirm: PropTypes.func.isRequired,
    loading: PropTypes.bool,
    currentPromptText: PropTypes.string,
    hasEvaluationData: PropTypes.bool,
  }

  static defaultProps = {
    loading: false,
    currentPromptText: '',
    hasEvaluationData: false,
  }

  state = {
    iterations: 3, // 默认3次迭代
  }

  /**
   * 处理迭代次数变化
   * @param {number} value - 迭代次数
   */
  handleIterationsChange = (value) => {
    this.setState({ iterations: value || 1 });
  }

  /**
   * 处理确认操作
   */
  handleConfirm = () => {
    const { iterations } = this.state;
    const { onConfirm } = this.props;

    if (iterations < 1 || iterations > 10) {
      return;
    }

    onConfirm({
      iterations,
    });
  }

  /**
   * 处理取消操作
   */
  handleCancel = () => {
    this.props.onCancel();
  }

  /**
   * 渲染操作说明
   */
  renderOperationDescription = () => {
    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <Title level={5} style={{ margin: 0, marginBottom: 8 }}>
          <PlayCircleOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          批量操作流程
        </Title>
        <div style={{ fontSize: 13, color: '#666', lineHeight: '1.6' }}>
          <div style={{ marginBottom: 4 }}>
            <Text strong>第1步：</Text> 使用当前提示词进行文稿评估
          </div>
          <div style={{ marginBottom: 4 }}>
            <Text strong>第2步：</Text> 基于评估结果优化提示词
          </div>
          <div style={{ marginBottom: 4 }}>
            <Text strong>第3步：</Text> 使用优化后的提示词进行下一轮评估
          </div>
          <div>
            <Text strong>重复：</Text> 按设定次数重复上述流程
          </div>
        </div>
      </Card>
    );
  }

  /**
   * 渲染当前状态信息
   */
  renderCurrentStatus = () => {
    const { currentPromptText, hasEvaluationData } = this.props;

    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <Title level={5} style={{ margin: 0, marginBottom: 8 }}>
          <SettingOutlined style={{ marginRight: 8, color: '#52c41a' }} />
          当前状态
        </Title>
        <div style={{ fontSize: 13 }}>
          <div style={{ marginBottom: 8 }}>
            <Text strong>提示词长度：</Text>
            <Text>{currentPromptText ? currentPromptText.length : 0} 字符</Text>
          </div>
          <div style={{ marginBottom: 8 }}>
            <Text strong>评估数据：</Text>
            <Text style={{ color: hasEvaluationData ? '#52c41a' : '#faad14' }}>
              {hasEvaluationData ? '已准备' : '将使用历史数据'}
            </Text>
          </div>
        </div>
      </Card>
    );
  }

  /**
   * 渲染组件
   */
  render() {
    const { visible, loading } = this.props;
    const { iterations } = this.state;

    return (
      <Modal
        title="批量评估优化配置"
        open={visible}
        onCancel={this.handleCancel}
        onOk={this.handleConfirm}
        confirmLoading={loading}
        width={600}
        okText="开始批量操作"
        cancelText="取消"
        okButtonProps={{
          disabled: iterations < 1 || iterations > 10,
        }}
      >
        {/* 操作说明 */}
        {this.renderOperationDescription()}

        {/* 当前状态 */}
        {this.renderCurrentStatus()}

        {/* 配置表单 */}
        <Form layout="vertical">
          <Form.Item
            label="迭代次数"
            help="建议3-5次，过多次数可能导致提示词过度优化"
            required
          >
            <InputNumber
              min={1}
              max={10}
              value={iterations}
              onChange={this.handleIterationsChange}
              style={{ width: '100%' }}
              placeholder="请输入迭代次数"
              addonAfter="次"
            />
          </Form.Item>
        </Form>

      </Modal>
    );
  }
}
