/* eslint-disable no-nested-ternary */

import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { <PERSON><PERSON>, Button, Card, Col, Modal, Progress, Row, Space, Statistic, Timeline, Typography } from 'antd';
import PropTypes from 'prop-types';
import React, { Component } from 'react';

const { Title } = Typography;

/**
 * 批量操作进度监控组件
 * 显示批量操作的实时进度和状态
 */
export default class BatchOperationProgress extends Component {
  static propTypes = {
    visible: PropTypes.bool.isRequired,
    onCancel: PropTypes.func.isRequired,
    onPause: PropTypes.func.isRequired,
    onResume: PropTypes.func.isRequired,
    onStop: PropTypes.func.isRequired,
    progressData: PropTypes.object,
  }

  static defaultProps = {
    progressData: {
      isRunning: false,
      isPaused: false,
      currentIteration: 0,
      totalIterations: 0,
      currentStep: '',
      currentStepProgress: 0,
      overallProgress: 0,
      iterationResults: [],
    },
  }

  /**
   * 格式化持续时间
   * @param {number} duration - 持续时间（毫秒）
   * @returns {string} 格式化后的时间字符串
   */
  formatDuration = (duration) => {
    if (!duration) return '0秒';

    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    }
    return `${seconds}秒`;
  }

  /**
   * 获取迭代状态图标
   * @param {string} status - 状态
   * @returns {JSX.Element} 图标组件
   */
  getIterationStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'running':
        return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  }

  /**
   * 获取迭代状态文本
   * @param {string} status - 状态
   * @returns {string} 状态文本
   */
  getIterationStatusText = (status) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'failed':
        return '失败';
      case 'running':
        return '进行中';
      default:
        return '等待中';
    }
  }

  /**
   * 渲染整体进度
   */
  renderOverallProgress = () => {
    const { progressData } = this.props;
    const {
      overallProgress,
      currentIteration,
      totalIterations,
      currentStep,
      isRunning,
      isPaused,
    } = progressData;

    const statusText = isPaused ? '已暂停' : (isRunning ? '进行中' : '已完成');
    const statusColor = isPaused ? '#faad14' : (isRunning ? '#1890ff' : '#52c41a');

    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <Title level={5} style={{ margin: 0, marginBottom: 16 }}>整体进度</Title>

        <Progress
          percent={overallProgress}
          status={isPaused ? 'exception' : (isRunning ? 'active' : 'success')}
          strokeColor={statusColor}
          style={{ marginBottom: 16 }}
        />

        <Row gutter={16}>
          <Col span={8}>
            <Statistic
              title="当前迭代"
              value={currentIteration}
              suffix={`/ ${totalIterations}`}
              valueStyle={{ fontSize: 16 }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="状态"
              value={statusText}
              valueStyle={{ fontSize: 16, color: statusColor }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="当前步骤"
              value={currentStep || '准备中'}
              valueStyle={{ fontSize: 14 }}
            />
          </Col>
        </Row>
      </Card>
    );
  }

  /**
   * 渲染迭代历史
   */
  renderIterationHistory = () => {
    const { progressData } = this.props;
    const { iterationResults, currentIteration, totalIterations } = progressData;

    // 生成完整的迭代列表（包括未开始的）
    const allIterations = [];
    for (let i = 1; i <= totalIterations; i++) {
      const result = iterationResults.find((r) => { return r.iteration === i; });
      allIterations.push({
        iteration: i,
        status: result ? result.status : (i === currentIteration ? 'running' : 'pending'),
        startTime: result ? result.startTime : null,
        endTime: result ? result.endTime : null,
        duration: result ? result.duration : 0,
        error: result ? result.error : null,
        promptLength: result && result.promptText ? result.promptText.length : 0,
        optimizedLength: result && result.optimizedPrompt ? result.optimizedPrompt.length : 0,
      });
    }

    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <Title level={5} style={{ margin: 0, marginBottom: 16 }}>迭代历史</Title>

        <Timeline mode="left" style={{ maxHeight: 300, overflowY: 'auto' }}>
          {allIterations.map((iteration) => {
            const { iteration: index, status, startTime, endTime,
              duration, error, promptLength, optimizedLength } = iteration;

            return (
              <Timeline.Item
                key={index}
                dot={this.getIterationStatusIcon(status)}
                color={status === 'completed' ? 'green' : (status === 'failed' ? 'red' : 'blue')}
              >
                <div>
                  <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
                    第{index}次迭代 - {this.getIterationStatusText(status)}
                  </div>

                  {startTime && (
                    <div style={{ fontSize: 12, color: '#666', marginBottom: 2 }}>
                      开始时间: {new Date(startTime).toLocaleTimeString()}
                    </div>
                  )}

                  {endTime && (
                    <div style={{ fontSize: 12, color: '#666', marginBottom: 2 }}>
                      结束时间: {new Date(endTime).toLocaleTimeString()}
                    </div>
                  )}

                  {duration > 0 && (
                    <div style={{ fontSize: 12, color: '#666', marginBottom: 2 }}>
                      耗时: {this.formatDuration(duration)}
                    </div>
                  )}

                  {status === 'completed' && promptLength > 0 && (
                    <div style={{ fontSize: 12, color: '#666', marginBottom: 2 }}>
                      提示词: {promptLength} → {optimizedLength} 字符
                    </div>
                  )}

                  {error && (
                    <div style={{ fontSize: 12, color: '#ff4d4f', marginTop: 4 }}>
                      错误: {error}
                    </div>
                  )}
                </div>
              </Timeline.Item>
            );
          })}
        </Timeline>
      </Card>
    );
  }

  /**
   * 渲染操作按钮
   */
  renderActionButtons = () => {
    const { progressData, onPause, onResume, onStop } = this.props;
    const { isRunning, isPaused } = progressData;

    if (!isRunning) {
      return null;
    }

    return (
      <Space>
        {isPaused ? (
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={onResume}
          >
            恢复
          </Button>
        ) : (
          <Button
            type="default"
            icon={<PauseCircleOutlined />}
            onClick={onPause}
          >
            暂停
          </Button>
        )}

        <Button
          type="danger"
          icon={<StopOutlined />}
          onClick={onStop}
        >
          停止
        </Button>
      </Space>
    );
  }

  /**
   * 渲染警告信息
   */
  renderWarningInfo = () => {
    const { progressData } = this.props;
    const { isRunning, isPaused } = progressData;

    if (!isRunning) {
      return null;
    }

    return (
      <Alert
        type={isPaused ? 'warning' : 'info'}
        showIcon
        message={isPaused ? '操作已暂停' : '批量操作进行中'}
        description={
          isPaused
            ? '批量操作已暂停，您可以选择恢复或停止操作'
            : '请勿关闭页面或进行其他操作，以免影响批量处理进程'
        }
        style={{ marginBottom: 16 }}
      />
    );
  }

  /**
   * 渲染组件
   */
  render() {
    const { visible, onCancel, progressData } = this.props;
    const { isRunning } = progressData;

    return (
      <Modal
        title="批量操作进度"
        open={visible}
        onCancel={onCancel}
        footer={[
          this.renderActionButtons(),
          <Button key="close" onClick={onCancel} disabled={isRunning}>
            {isRunning ? '最小化' : '关闭'}
          </Button>,
        ]}
        width={700}
        closable={!isRunning}
        maskClosable={false}
      >
        {/* 警告信息 */}
        {this.renderWarningInfo()}

        {/* 整体进度 */}
        {this.renderOverallProgress()}

        {/* 迭代历史 */}
        {this.renderIterationHistory()}
      </Modal>
    );
  }
}
