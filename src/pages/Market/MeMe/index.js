import { FilterBar, PaginationTable, Toast } from '~/components';
import { But<PERSON>, Divider, Drawer, Form, Image, Input, Modal, Popconfirm, Select, Table, Tabs } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

const SCENE_ENUMS = [
  { scene: 'premise_course', name: '企微群内互动工作流' },
  { scene: 'probe', name: '网页问答提问工作流' },
  { scene: 'probe_save_feedback', name: '用户反馈收集工作流' },
];
@connect(
  (state) => {
    return state.marketMeMe;
  },
  actions,
)
export default class MarketMeMe extends Component {
  static propTypes = {
    workflows: PropTypes.array,
    list: PropTypes.array,
    total: PropTypes.number,
    pagination: PropTypes.object,
    fetchCourses: PropTypes.func.isRequired,
    delCourse: PropTypes.func.isRequired,
    fetchWorkflows: PropTypes.func.isRequired,
    linkWorkflow: PropTypes.func.isRequired,
    fetchLinkedWorkflows: PropTypes.func.isRequired,
    createCourseGroup: PropTypes.func.isRequired,
    fetchCourseGroups: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    data: {},
    columns: [
      { title: '群名', dataIndex: 'roomName', key: 'roomName' },
      {
        title: '二维码',
        dataIndex: 'qrCode',
        key: 'qrCode',
        render: (qrCode) => { return <Image src={qrCode} width={100} />; },
      },
    ],
    publishData: {
      dataSource: [],
    },
  }

  componentDidMount = async () => {
    await this.props.fetchCourses();
    await this.props.fetchWorkflows();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onOpenDrawer = async (row) => {
    const workflows = await this.props.fetchLinkedWorkflows({ courseId: row.uuid });
    const groups = await this.props.fetchCourseGroups({ courseId: row.uuid });
    this.setState({ open: true, publishData: { data: row, dataSource: groups, linkedFlow: workflows } });
  }

  onLinkFlow = async (e, scene) => {
    const { publishData } = this.state;
    const courseId = publishData?.data?.uuid;
    const flowObj = { ...publishData?.linkedFlow, [scene]: e };
    const items = [];
    _.map(flowObj, (v, k) => { items.push({ scene: k, flowId: v }); });

    await this.props.linkWorkflow({ courseId, items });
    const workflows = await this.props.fetchLinkedWorkflows({ courseId });
    this.setState({ open: true, publishData: { ...publishData, linkedFlow: workflows } });
  }

  onChangeGroup = (e, key) => {
    const { value } = e.target;
    this.setState({ group: { ...this.state.group, [key]: value } });
  }

  onCreateGroup = async () => {
    const { roomName, greeting } = this.state.group;
    if (_.isEmpty(roomName)) {
      Toast.show('请完善信息!', Toast.Type.WARNING);
      return;
    }
    const { data } = this.state.publishData;
    await this.props.createCourseGroup({ courseId: data?.uuid, roomName, greeting });
    const items = await this.props.fetchCourseGroups({ flowId: data?.uuid });
    this.setState({ addGroupOpen: false, publishData: { ...this.state.publishData, dataSource: items } });
  }

  onSearch = () => {
  }

  renderCreateModal = () => {
    const { addGroupOpen, group } = this.state;
    return (
      <Modal
        open={addGroupOpen}
        title="新增群聊"
        onCancel={() => { return this.setState({ addGroupOpen: false, group: {} }); }}
        onOk={this.onCreateGroup}
      >
        <Input
          placeholder="请输入群名称"
          value={group?.roomName}
          onChange={(e) => { return this.onChangeGroup(e, 'roomName'); }}
        />
        <Input.TextArea
          placeholder="请输入群欢迎语"
          style={{ margin: '5px 0' }}
          value={group?.greeting}
          onChange={(e) => { return this.onChangeGroup(e, 'greeting'); }}
        />
      </Modal>
    );
  }

  renderPublishModal = () => {
    const { workflows } = this.props;
    const { open, publishData, columns } = this.state;
    const { linkedFlow, dataSource } = publishData;
    return (
      <Drawer
        open={open}
        title="发布"
        placement="right"
        contentWrapperStyle={{ width: '60vw' }}
        onClose={() => { return this.setState({ open: false }); }}
      >
        <Tabs>
          <Tabs.TabPane key="workflow" tab="Workflow">
            <Form>
              {
                SCENE_ENUMS.map((x) => {
                  return (
                    <Form.Item label={x.name}>
                      <Select
                        showSearch
                        filterOption={(input, option) => { return option.children.includes(input); }}
                        value={linkedFlow[x.scene]}
                        onChange={(e) => { return this.onLinkFlow(e, x.scene); }}
                      >
                        {workflows.map((w) => { return <Select.Option value={w.uuid}>{w?.name}</Select.Option>; })}
                      </Select>
                    </Form.Item>
                  );
                })
              }
            </Form>
          </Tabs.TabPane>
          <Tabs.TabPane key="groups" tab="群聊">
            <Button
              type="primary"
              style={{ float: 'right' }}
              onClick={() => { return this.setState({ addGroupOpen: true }); }}
            >
              新增
            </Button>
            <Table size="small" pagination={false} dataSource={dataSource} columns={columns} />
          </Tabs.TabPane>
        </Tabs>
      </Drawer>
    );
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '名称', dataIndex: 'name', key: 'name', align: 'center' },
      { title: '类型', dataIndex: 'type', key: 'type', align: 'center' },
      { title: '简介', dataIndex: 'intro', key: 'intro', align: 'center', ellipsis: true, width: '30%' },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <a onClick={() => { return this.onOpenDrawer(row); }}>发布</a>
              <Divider type="vertical" />
              <a onClick={() => { return this.$push(`/market-meme/${row.id}`); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm
                title="是否删除?!"
                onConfirm={() => { return this.props.delCourse(row.id); }}
              >
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  render = () => {
    const { total, list, pagination } = this.props;

    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <FilterBar
          canAdd
          shouldShowSearchInput={false}
          onAdd={() => { return this.$push('/market-meme/add'); }}
        />
        <PaginationTable
          totalDataCount={total}
          dataSource={list}
          pagination={pagination}
          columns={this.renderColumns()}
          onPaginationChange={(e) => { return this.onSearch(e); }}
        />

        {this.state.open && this.renderPublishModal()}
        {this.state.addGroupOpen && this.renderCreateModal()}
      </div>
    );
  }
}

export {
  reducer,
};
