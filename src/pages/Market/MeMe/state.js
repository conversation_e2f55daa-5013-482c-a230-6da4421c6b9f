import Configs from '~/consts';
import { ChatBot, MeMe } from '~/engine';

const SET_STATE = 'MARKET_MEME/SET_STATE';
const CLEAR_STATE = 'MARKET_MEME/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchWorkflows = () => {
  return async (dispatch) => {
    const { items } = await ChatBot.fetchChatbotWorkflows(Configs.ALL_PAGE_PARAMS);
    dispatch(setState({ workflows: items }));
  };
};

export const fetchLinkedWorkflows = (params) => {
  return async () => {
    const { items } = await MeMe.fetchLinkedWorkflows(params);
    const obj = {};
    items.forEach((x) => { obj[x.scene] = x.flowId; });
    return obj;
  };
};

export const fetchCourseGroups = (params) => {
  return async () => {
    const { items } = await MeMe.fetchCourseGroups(params);
    return items;
  };
};

export const linkWorkflow = (params) => {
  return async () => {
    const { items } = await MeMe.linkWorkflow(params);
    return items;
  };
};

export const createCourseGroup = (params) => {
  return async () => {
    await MeMe.createCourseGroup(params);
  };
};

export const fetchCourses = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().marketMeMe;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };

    const { items, total } = await MeMe.fetchCourses(searchParams);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const delCourse = (id) => {
  return async (dispatch) => {
    await MeMe.delCourse(id);
    dispatch(fetchCourses());
  };
};

const _getInitState = () => {
  return {
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    list: [],
    workflows: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
