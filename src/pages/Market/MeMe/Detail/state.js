import Configs from '~/consts';
import { Market, MeMe } from '~/engine';

const SET_STATE = 'MARKET_MEME_DETAIL/SET_STATE';
const CLEAR_STATE = 'MARKET_MEME_DETAIL/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchChatbots = () => {
  return async (dispatch) => {
    const { items } = await Market.fetchChatbots(Configs.ALL_PAGE_PARAMS);
    dispatch(setState({ bots: items }));
  };
};

export const addCourse = (params) => {
  return async () => {
    await MeMe.addCourse(params);
  };
};

export const updateCourse = (params) => {
  return async () => {
    await MeMe.updateCourse(params);
  };
};

export const fetchCourse = (id) => {
  return async (dispatch) => {
    const data = await MeMe.getCourse(id);
    dispatch(setState({ detail: data }));
  };
};

const _getInitState = () => {
  return {
    bots: [],
    detail: {
      name: '',
      logo: '',
      banner: '',
      intro: '',
      type: '',
      publishedAt: '1',
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
