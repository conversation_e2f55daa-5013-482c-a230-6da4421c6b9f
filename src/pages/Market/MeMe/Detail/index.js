import './index.less';

import { ArrowDownOutlined, ArrowUpOutlined, MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { Toast } from '~/components';
import { Accounts, AliyunHelper } from '~/engine';
import { Button, Divider, Form, Input, InputNumber, Radio, Select, Upload } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

const MESSAGE_TYPES = {
  1: '文件',
  2: '语音',
  3: '名片',
  4: '聊天历史',
  5: '表情',
  6: '图片',
  7: '文字',
  8: '位置',
  9: '小程序',
  10: '钱相关',
  12: '图文消息',
  13: '视频',
};
const ILLEGAL_LABELS = {
  profanity: '辱骂',
  negative_content: '不良',
  religion: '宗教',
  contraband: '违禁',
  violence: '暴恐',
  sexual_content: '色情内容',
  political_content: '涉政内容',
};
@connect(
  (state) => {
    return state.marketMeMeDetail;
  },
  actions,
)
export default class MarketMeMeDetail extends Component {
  static propTypes = {
    bots: PropTypes.array,
    detail: PropTypes.object,
    history: PropTypes.object,
    addCourse: PropTypes.func,
    updateCourse: PropTypes.func,
    fetchCourse: PropTypes.func,
    fetchChatbots: PropTypes.func,
    match: PropTypes.object,
    setState: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  componentDidMount = async () => {
    const { id } = this.props.match.params;
    this.props.fetchChatbots();
    if (!_.isUndefined(id)) {
      await this.props.fetchCourse(id);
    }
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeValue = (e, type) => {
    const value = e?.target ? e.target.value : e;
    this.props.setState({ detail: { ...this.props.detail, [type]: value } });
  }

  onChangeH5Value = (e, key) => {
    const h5Config = _.cloneDeep(this.props.detail?.h5Config);
    const value = e?.target ? e?.target.value : e;
    h5Config[key] = value;
    this.onChangeValue(h5Config, 'h5Config');
  }

  onUpload = async (option, type, index) => {
    try {
      const result = await Accounts.getOssSignature();
      const fileUrl = await AliyunHelper.clipsUploadImage(option.file, (progress) => {
        const percent = Math.round((progress.loaded / progress.total) * 100);
        option.onProgress({ percent });
      }, {
        filePath: result.directory,
        result: { ...result, uploadDomain: result.url },
      });
      option.onSuccess();
      if (!_.isUndefined(type)) {
        this.onChangeValue(fileUrl, type);
      } else {
        this.onChangeH5Image(fileUrl, index);
      }
    } catch (e) {
      option.onError();
    }
  }

  onDelImg = (index) => {
    const h5Config = _.cloneDeep(this.props.detail.h5Config) || [];
    if (h5Config.images?.length === 1) return;
    h5Config.images.splice(index, 1);
    this.onChangeValue(h5Config, 'h5Config');
  }

  onChangeH5Image = (url, index) => {
    const h5Config = _.cloneDeep(this.props.detail.h5Config) || [];
    if (_.isUndefined(h5Config.images)) {
      h5Config.images = ['', ''];
    }
    h5Config.images[index] = url;
    this.onChangeValue(h5Config, 'h5Config');
  }

  onChangeImgSort = (index, direction) => {
    const h5Config = _.cloneDeep(this.props.detail.h5Config) || [];
    if (direction === 'up') {
      if (index === 0) {
        return;
      }
      const temp = h5Config.images[index];
      h5Config.images[index] = h5Config.images[index - 1];
      h5Config.images[index - 1] = temp;
    } else if (direction === 'down') {
      if (index === h5Config.images.length - 1) {
        return;
      }
      const temp = h5Config.images[index];
      h5Config.images[index] = h5Config.images[index + 1];
      h5Config.images[index + 1] = temp;
    }
    this.onChangeValue(h5Config, 'h5Config');
  }

  onChangeRule = (e, key, idx) => {
    let riskControlRule = _.cloneDeep(this.props.detail?.riskControlRule);
    if (_.isEmpty(riskControlRule?.rules)) {
      riskControlRule = { rules: [{}] };
    }
    riskControlRule.rules[idx][key] = e?.target ? e?.target.value : e;
    this.onChangeValue(riskControlRule, 'riskControlRule');
  }

  onAddRule = (index) => {
    const riskControlRule = _.cloneDeep(this.props.detail?.riskControlRule);
    riskControlRule.rules.splice(index + 1, 0, {});
    this.onChangeValue(riskControlRule, 'riskControlRule');
  }

  onDelRule = (index) => {
    const riskControlRule = _.cloneDeep(this.props.detail?.riskControlRule);
    if (riskControlRule.rules.length === 1) return;
    riskControlRule.rules.splice(index, 1);
    this.onChangeValue(riskControlRule, 'riskControlRule');
  }

  onChangeReplyConfig = (e, key) => {
    const riskControlRule = _.cloneDeep(this.props.detail?.riskControlRule) || {};
    if (_.isEmpty(riskControlRule?.replyConfig)) {
      riskControlRule.replyConfig = {};
    }
    riskControlRule.replyConfig[key] = e?.target ? e?.target.value : e;
    this.onChangeValue(riskControlRule, 'riskControlRule');
  }

  onCancel = () => {
    this.props.history.goBack();
  }

  onSubmit = async () => {
    const { uuid, name } = this.props.detail;
    if (_.isEmpty(name)) {
      Toast.show('请完善信息!', Toast.Type.WARNING);
      return;
    }
    const params = { ...this.props.detail, extraLinks: [{ icon: '', name: '' }] };
    if (_.isUndefined(uuid)) {
      this.props.addCourse(params);
    } else {
      this.props.updateCourse(params);
    }
    Toast.show('操作成功!', Toast.Type.SUCCESS);
    this.onCancel();
  }

  renderUploadItem = (name, type) => {
    const { detail } = this.props;
    return (
      <Form.Item label={name}>
        <Upload
          accept="image/*"
          listType="picture-card"
          showUploadList={false}
          customRequest={(opt) => { return this.onUpload(opt, type); }}
          onRemove={() => { this.onChangeValue('', type); }}
        >
          {
            detail[type] ?
              <img style={{ maxWidth: 100 }} src={`${detail[type]}?x-oss-process=image/resize,h_100,m_lfit`} /> :
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>上传</div>
              </div>
          }
        </Upload>
      </Form.Item>
    );
  }

  renderH5Images = (images) => {
    return (
      <div className="h5-image-wrap">
        {
          (_.isEmpty(images) ? ['', ''] : [...images, '']).map((url, index) => {
            return (
              <div style={{ display: 'flex', width: 150, marginBottom: 10 }}>
                <Upload
                  accept="image/*"
                  listType="picture-card"
                  showUploadList={false}
                  customRequest={(opt) => { return this.onUpload(opt, undefined, index); }}
                  onRemove={() => { this.onDelImg(index); }}
                >
                  {
                    url ?
                      <img style={{ maxWidth: 100 }} src={`${url}?x-oss-process=image/resize,h_100,m_lfit`} /> :
                      <div>
                        <PlusOutlined />
                        <div style={{ marginTop: 8 }}>上传</div>
                      </div>
                  }
                </Upload>
                {
                  !_.isEmpty(url) &&
                  <span className="img-opt">
                    <Button icon={<ArrowUpOutlined />} onClick={() => { return this.onChangeImgSort(index, 'up'); }} />
                    <Button icon={<MinusOutlined />} onClick={() => { return this.onDelImg(index); }} />
                    <Button
                      icon={<ArrowDownOutlined />}
                      onClick={() => { return this.onChangeImgSort(index, 'down'); }}
                    />
                  </span>
                }
              </div>
            );
          })
        }
      </div>
    );
  }

  renderRuleItem = (data = {}, idx) => {
    return (
      <Input.Group>
        <Button>当用户</Button>
        <Select
          style={{ width: 150 }}
          value={data?.humanAcion}
          onChange={(e) => { return this.onChangeRule(e, 'humanAcion', idx); }}
        >
          <Select.Option value="0">发送</Select.Option>
          <Select.Option value="1">修改群名</Select.Option>
          <Select.Option value="2">发送非法内容</Select.Option>
          <Select.Option value="3">被警告</Select.Option>
        </Select>
        {
          data?.humanAcion === '0' &&
          <Select
            mode="multiple"
            maxTagCount={2}
            style={{ width: 240 }}
            value={data?.messageTypes}
            onChange={(e) => { return this.onChangeRule(e, 'messageTypes', idx); }}
          >
            {_.map(MESSAGE_TYPES, (v, k) => { return <Select.Option value={`${k}`}>{v}</Select.Option>; })}
          </Select>
        }
        {
          data?.humanAcion === '2' &&
          <Select
            mode="multiple"
            maxTagCount={2}
            style={{ width: 240 }}
            value={data?.illegalLabels}
            onChange={(e) => { return this.onChangeRule(e, 'illegalLabels', idx); }}
          >
            {_.map(ILLEGAL_LABELS, (v, k) => { return <Select.Option value={`${k}`}>{v}</Select.Option>; })}
          </Select>
        }
        {
          data?.humanAcion === '3' &&
          <InputNumber
            min={0}
            addonAfter="次以后"
            value={data?.tiggerTime}
            onChange={(e) => { return this.onChangeRule(e, 'tiggerTime', idx); }}
          />
        }
        <Button>时, 执行</Button>
        <Select
          style={{ width: 180 }}
          value={data?.systemAction}
          onChange={(e) => { return this.onChangeRule(e, 'systemAction', idx); }}
        >
          <Select.Option value="0">警告</Select.Option>
          <Select.Option value="1">踢出群</Select.Option>
          <Select.Option value="2">连带邀请人踢出群</Select.Option>
        </Select>
        {
          data?.systemAction === '0' &&
          <Input
            style={{ width: 300 }}
            addonBefore="警告语"
            value={data?.warningTemplate}
            onChange={(e) => { return this.onChangeRule(e, 'warningTemplate', idx); }}
          />
        }
        <PlusOutlined style={{ marginLeft: 20 }} onClick={() => { return this.onAddRule(idx); }} />
        <MinusOutlined style={{ marginLeft: 20 }} onClick={() => { return this.onDelRule(idx); }} />
      </Input.Group>
    );
  }

  render = () => {
    const { name, type, intro, h5Config, riskControlRule } = this.props.detail;
    return (
      <div className="meme-container">
        <Form labelCol={{ span: 4 }} className="meme-form">
          <Divider orientation="left">基础配置</Divider>
          <Form.Item label="课程名称">
            <Input value={name} onChange={(e) => { return this.onChangeValue(e, 'name'); }} />
          </Form.Item>
          <Form.Item label="课程类型">
            <Input value={type} onChange={(e) => { return this.onChangeValue(e, 'type'); }} />
          </Form.Item>
          <Form.Item label="课程简介">
            <Input.TextArea value={intro} onChange={(e) => { return this.onChangeValue(e, 'intro'); }} />
          </Form.Item>
          {this.renderUploadItem('Logo', 'logo')}
          {this.renderUploadItem('Banner', 'banner')}
          <Divider orientation="left">页面配置</Divider>
          <Form.Item label="原价">
            <InputNumber
              min={0}
              addonBefore="¥"
              addonAfter="元"
              formatter={(e) => { return e / 100; }}
              parser={(e) => { return e * 100; }}
              value={h5Config?.originPrice}
              onChange={(e) => { return this.onChangeH5Value(e, 'originPrice'); }}
            />
          </Form.Item>
          <Form.Item label="现价">
            <InputNumber
              min={0}
              addonBefore="¥"
              addonAfter="元"
              formatter={(e) => { return e / 100; }}
              parser={(e) => { return e * 100; }}
              value={h5Config?.currentPrice}
              onChange={(e) => { return this.onChangeH5Value(e, 'currentPrice'); }}
            />
          </Form.Item>
          <Form.Item label="小助手">
            <Select
              style={{ width: 300 }}
              value={h5Config?.botWx}
              onChange={(e) => { return this.onChangeH5Value(e, 'botWx'); }}
            >
              {
                this.props.bots.map((x) => {
                  return <Select.Option value={x.botWeixin}>{x.botName}({x.botWeixin})</Select.Option>;
                })
              }
            </Select>
          </Form.Item>
          <Form.Item label="页面图片">
            {this.renderH5Images(h5Config?.images)}
          </Form.Item>
          <Divider orientation="left">风控规则</Divider>
          <Form.Item label="风控规则">
            {_.map((riskControlRule?.rules || [{}]), (x, idx) => { return this.renderRuleItem(x, idx); })}
          </Form.Item>
          <Form.Item label="触发AI">
            <Radio.Group
              value={riskControlRule?.replyConfig?.condition}
              onChange={(e) => { return this.onChangeReplyConfig(e, 'condition'); }}
            >
              <Radio value="0">发任意消息</Radio>
              <Radio value="1">发指定格式的消息</Radio>
              <Radio value="2">AT消息</Radio>
            </Radio.Group>
            {
              riskControlRule?.replyConfig?.condition === '1' &&
              <Select
                mode="multiple"
                maxTagCount={2}
                style={{ width: 240 }}
                value={riskControlRule?.replyConfig?.messageTypes}
                onChange={(e) => { return this.onChangeReplyConfig(e, 'messageTypes'); }}
              >
                {_.map(MESSAGE_TYPES, (v, k) => { return <Select.Option value={`${k}`}>{v}</Select.Option>; })}
              </Select>
            }
          </Form.Item>
          <div style={{ textAlign: 'center' }}>
            <Button style={{ marginRight: 30 }} onClick={() => { return this.onCancel(); }}>取消</Button>
            <Button type="primary" onClick={() => { return this.onSubmit(); }}>确定</Button>
          </div>
        </Form>
      </div>
    );
  }
}

export {
  reducer,
};
