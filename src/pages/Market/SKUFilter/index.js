/* eslint-disable no-await-in-loop, max-lines */
import './index.less';

import { DownloadOutlined, InboxOutlined } from '@ant-design/icons';
import { PaginationTable } from '~/components';
import Engine, { AliyunHelper, Market, Sessions } from '~/engine';
import { EVENT_TYPE } from '~/pages/Playground/Configs';
import ReconnectingWebSocket from '~/pages/Playground/WebSocket';
import { Platform, StringExtension } from '~/plugins';
import { Button, Form, Input, InputNumber, Table, Tabs, Upload, message } from 'antd';
import * as JSONC from 'jsonc-parser';
import qs from 'qs';
import React, { Component } from 'react';
import * as XLSX from 'xlsx';

const FLOW_MAP = {
  prod: 'EWAt1cPCAT3NfgT1JPxjga',
  stg: '6FSG3w9fm75k7vBRmEHgUa',
};

// 常量提取，避免重复创建
const ACCEPT_TYPES = [
  '.xlsx',
  '.xls',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel',
].join(',');

const TABLE_COLUMNS = [
  { title: 'SKU ID', dataIndex: 'sku', key: 'sku', width: 120, align: 'center' },
  { title: '标题', dataIndex: '标题', key: '标题', width: '25%' },
  { title: '理由', dataIndex: '理由', key: '理由', ellipsis: true },
];

// 向量列表表格列配置
const VECTOR_LIST_COLUMNS = [
  { title: 'ID', dataIndex: 'id', key: 'id', width: 80, align: 'center' },
  { title: '名称', dataIndex: 'name', key: 'name', ellipsis: true },
  { title: '30天转化率', dataIndex: 'conversion_30d', key: 'conversion_30d', align: 'center' },
  { title: '性别分布', dataIndex: 'genderDistribution', key: 'genderDistribution', align: 'center' },
  { title: '30天销售', dataIndex: 'sales_30d', key: 'sales_30d', align: 'center' },
  { title: '30天观看量', dataIndex: 'views_30d', key: 'views_30d', align: 'center' },
  { title: '直播销售', dataIndex: 'listLiveSales', key: 'listLiveSales', align: 'center' },
  { title: '视频销售', dataIndex: 'listVideoSales', key: 'listVideoSales', align: 'center' },
  {
    title: 'KOL列表',
    dataIndex: 'kolList',
    key: 'kolList',
    ellipsis: true,
    render: (text) => { return (text ? text.split('\n')[0] : '-'); }, // 只显示第一行
  },
  {
    title: '概览',
    dataIndex: 'overview',
    key: 'overview',
    width: 150,
    ellipsis: true,
    render: (text) => { return (text ? text.split('\n')[0] : '-'); }, // 只显示第一行
  },
];

const BATCH_SIZE = 100;
const CONCURRENT_LIMIT = 5; // 并发处理批次数量限制

export default class SKUFilter extends Component {
  state = {
    activeKey: 'vector-positioning', // Tab切换状态
    fileName: '',
    excelData: [],
    headers: [],
    filterSkus: [],
    isUploading: false,
    isProcessing: false,
    totalBatches: 0,
    allResults: [],
    completedBatches: 0, // 已完成的批次数量
    originalSkuIds: null, // 保存原始SKU ID映射
    anchorInfo: `待定位主播：
晶晶博士(郭晶晶)
财经自媒体
中国社会科学院大学
中国社会科学院史学博士
中关村数字产城联盟首席研究员，上海金融与发展实验室特聘研究员
经济政策研究、金融历史知识科普🤓
最近半年计划连更一带一路国家经济史专题😎

最热门视频：
一口气带你看完韩国经济60年！一个把一手烂牌打出花的东亚小国—韩国，它是如何崛起的呢？#好书大晒 #掘金计划2024 #dou来守护钱袋子
一口气看完香港金融历史风云200年！#掘金计划2024 #金牌守护家 #香港 #人民币国际化`,
    promptText: `我们是一家mcn公司，在为被待定位主播做ip孵化的账号定位。在可选sku库中寻找适合被待定位主播卖的sku，并说明理由。我们会在选定之后为kol定制改造sku。如果没有合适的对标就直接说没有合适的对标。
注意：
1.既要考虑kol的人设，又要考虑其本身的技能及专业能力
2.人设能包装，但不能造假
3.有些技能有跨品类特征，例如：主持人的表达能力
4.技能及专业能力部分能由mcn补全，例如mcn会提供短视频逐字稿和直播话题；部分无法补全，例如kol在直播间要实时回答问题
5.整个匹配过程的本质是用户需求和解决方案的匹配，最核心是分析不同已被验证sku的人群需求是否和kol的人设或能力匹配，如果匹配，sku（也就是解决方案）可以改造定制
6.输出合适sku的编号，按匹配程度从高到底排列，不要任何其他说明性文字。

返回的内容为 JSON，确保能被 python 的 json.loads 解析，JSON 字段定义如下：
  [{sku:"", 标题:"",理由:""}]
`,
    // 向量定位相关状态
    vectorFileName: '',
    vectorAnchorInfo: `待定位主播：
晶晶博士(郭晶晶)
财经自媒体
中国社会科学院大学
中国社会科学院史学博士
中关村数字产城联盟首席研究员，上海金融与发展实验室特聘研究员
经济政策研究、金融历史知识科普🤓
最近半年计划连更一带一路国家经济史专题😎

最热门视频：
一口气带你看完韩国经济60年！一个把一手烂牌打出花的东亚小国—韩国，它是如何崛起的呢？#好书大晒 #掘金计划2024 #dou来守护钱袋子
一口气看完香港金融历史风云200年！#掘金计划2024 #金牌守护家 #香港 #人民币国际化`,
    vectorPrompt: `我们是一家mcn公司，在为被待定位主播做ip孵化的账号定位。在可选sku库中寻找适合被待定位主播卖的sku，并说明理由。我们会在选定之后为kol定制改造sku。如果没有合适的对标就直接说没有合适的对标。
注意：
1.既要考虑kol的人设，又要考虑其本身的技能及专业能力
2.人设能包装，但不能造假
3.有些技能有跨品类特征，例如：主持人的表达能力
4.技能及专业能力部分能由mcn补全，例如mcn会提供短视频逐字稿和直播话题；部分无法补全，例如kol在直播间要实时回答问题
5.整个匹配过程的本质是用户需求和解决方案的匹配，最核心是分析不同已被验证sku的人群需求是否和kol的人设或能力匹配，如果匹配，sku（也就是解决方案）可以改造定制
6.输出合适sku的编号，按匹配程度从高到底排列，不要任何其他说明性文字。

返回的内容为 JSON，确保能被 python 的 json.loads 解析，JSON 字段定义如下：
  [{sku:"", 标题:"",理由:""}]`,
    isVectorUploading: false,
    isVectorQuerying: false,
    isVectorProcessing: false,
    vectorResults: [],
    vectorTopK: 10, // 向量查询返回结果数量
    vectorThreshold: 0.5, // 向量查询相似度阈值
    // 向量列表相关状态
    vectorListData: [], // 向量列表数据
    vectorListLoading: false, // 向量列表加载状态
    vectorListPagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    }, // 向量列表分页信息
    // 向量定位结果相关状态
    vectorFilterSkus: [], // 向量定位筛选结果
    vectorAllResults: [], // 向量定位所有批次结果
    vectorCompletedBatches: 0, // 向量定位已完成批次数
    vectorTotalBatches: 0, // 向量定位总批次数
  }

  // 组件挂载时从 localStorage 读取数据
  componentDidMount = () => {
    const storageFields = ['anchorInfo', 'promptText', 'vectorAnchorInfo', 'vectorPrompt'];
    const updates = storageFields.reduce((acc, field) => {
      const savedValue = this.getStorageData(field);
      if (savedValue) acc[field] = savedValue;
      return acc;
    }, {});

    if (Object.keys(updates).length > 0) {
      this.setState(updates);
    }

    this.fetchVectorListData();
  }

  // 缓存处理后的数据，避免重复计算
  processedTextsCache = null;
  aiColumnIndexCache = null;

  // WebSocket连接池管理
  wsConnections = new Map(); // 存储每个批次的WebSocket连接
  vectorWsConnections = new Map(); // 存储向量定位每个批次的WebSocket连接

  // 获取向量列表数据
  fetchVectorListData = async (pagination = { pageIndex: 1, pageSize: 20 }) => {
    try {
      this.setState({ vectorListLoading: true });
      const searchParams = {
        'pagination.pageIndex': pagination.pageIndex,
        'pagination.pageSize': pagination.pageSize,
        'pagination.orderBy': pagination.orderBy,
      };

      const { items, total } = await Market.fetchExternalKolClasses(searchParams);

      this.setState({
        total,
        vectorListData: items,
        vectorListPagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
        },
      });
    } catch (error) {
      console.error('获取向量列表数据失败:', error); // eslint-disable-line no-console
      message.error('获取向量列表数据失败，请重试');
    } finally {
      this.setState({ vectorListLoading: false });
    }
  }

  // 清除缓存数据
  clearCache = () => {
    this.processedTextsCache = null;
    this.aiColumnIndexCache = null;
  }

  // 获取处理后的文本数据（带缓存）
  getProcessedTexts = () => {
    if (this.processedTextsCache) {
      return this.processedTextsCache;
    }

    const { excelData, headers, originalSkuIds } = this.state;

    // 缓存 AI 列索引
    if (this.aiColumnIndexCache === null) {
      this.aiColumnIndexCache = headers.findIndex((x) => { return x === 'AI 特征分析'; });
    }

    const idx = this.aiColumnIndexCache;

    if (idx === -1) {
      this.processedTextsCache = [];
      return [];
    }

    // 处理并缓存数据
    this.processedTextsCache = excelData
      .map((row, index) => {
        // 如果有原始SKU ID映射，使用原始ID；否则使用当前索引+1
        const skuId = originalSkuIds && originalSkuIds[index] ? originalSkuIds[index] : index + 1;
        return `skuId: ${skuId}\n${row[idx]}`;
      })
      .filter((text) => { return text !== null && text !== undefined && text !== ''; });

    return this.processedTextsCache;
  }

  // 获取向量定位处理后的文本数据
  getVectorProcessedTexts = () => {
    const { vectorResults } = this.state;

    if (!vectorResults || vectorResults.length === 0) {
      return [];
    }

    // 将vectorResults转换为类似excelData的格式
    return vectorResults.map((item, index) => {
      const skuId = item.id || index + 1;
      // 构建包含向量数据各字段的文本描述
      const description = [
        `名称: ${item.name || ''}`,
        `描述: ${item.overview || ''}`,
      ].filter(Boolean).join('\n');

      return `skuId: ${skuId}\n${description}`;
    }).filter((text) => { return text !== null && text !== undefined && text !== ''; });
  }

  // 将数据分批，每批100条
  createBatches = (texts, batchSize = BATCH_SIZE) => {
    const batches = [];
    for (let i = 0; i < texts.length; i += batchSize) {
      batches.push(texts.slice(i, i + batchSize));
    }
    return batches;
  }

  // 处理单个批次 - 改为返回Promise支持并发
  processBatch = (batchTexts, batchIndex) => {
    return new Promise((resolve, reject) => {
      const { anchorInfo, promptText } = this.state;

      try {
        const flowId = FLOW_MAP[Platform.isProd() ? 'prod' : 'stg'];
        const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflow-v2/run/${flowId}`;
        const query = { access_token: Sessions.getToken() };

        // 为每个批次创建独立的WebSocket连接
        const ws = new ReconnectingWebSocket(
          `${path}?${qs.stringify(query)}`,
          [],
          (e) => { this.onReceiveMsg(e, batchIndex, resolve, reject); },
          () => {
            ws.send(JSON.stringify({
              text: JSON.stringify({
                sku: `可选sku库：\n${batchTexts.join('\n')}`,
                anchor: anchorInfo,
                prompt: promptText,
              }),
              type: 'message',
              is_beta: false,
            }));
          },
        );

        // 将连接存储到连接池中
        this.wsConnections.set(batchIndex, ws);

        // 设置超时处理
        setTimeout(() => {
          if (this.wsConnections.has(batchIndex)) {
            console.warn(`批次 ${batchIndex + 1} 处理超时`); // eslint-disable-line no-console
            this.cleanupConnection(batchIndex);
            reject(new Error(`批次 ${batchIndex + 1} 处理超时`));
          }
        }, 10 * 60 * 1000); // 10分钟超时
      } catch (error) {
        console.error(`处理第${batchIndex + 1}批数据失败:`, error); // eslint-disable-line no-console
        reject(error);
      }
    });
  }

  // 清理WebSocket连接
  cleanupConnection = (batchIndex) => {
    const ws = this.wsConnections.get(batchIndex);
    if (ws) {
      try {
        ws.close();
      } catch (error) {
        console.warn(`关闭批次 ${batchIndex + 1} 连接时出错:`, error); // eslint-disable-line no-console
      }
      this.wsConnections.delete(batchIndex);
    }
  }

  // 清理向量定位WebSocket连接
  cleanupVectorConnection = (batchIndex) => {
    const ws = this.vectorWsConnections.get(batchIndex);
    if (ws) {
      try {
        ws.close();
      } catch (error) {
        console.warn(`关闭向量定位批次 ${batchIndex + 1} 连接时出错:`, error); // eslint-disable-line no-console
      }
      this.vectorWsConnections.delete(batchIndex);
    }
  }

  // 并发处理所有批次
  processBatchesConcurrently = async (batches) => {
    const results = [];

    // 分组处理，每组最多CONCURRENT_LIMIT个批次
    for (let i = 0; i < batches.length; i += CONCURRENT_LIMIT) {
      const batchGroup = batches.slice(i, i + CONCURRENT_LIMIT);
      const batchPromises = batchGroup.map((batch, index) => {
        const batchIndex = i + index;
        return this.processBatch(batch, batchIndex)
          .then((result) => { return { batchIndex, result, success: true }; })
          .catch((error) => { return { batchIndex, error, success: false }; });
      });

      // 等待当前组的所有批次完成
      const groupResults = await Promise.all(batchPromises);
      results.push(...groupResults);

      // 更新进度
      this.setState((prevState) => {
        return {
          completedBatches: prevState.completedBatches + batchGroup.length,
        };
      });

      // 如果不是最后一组，稍微延迟避免服务器压力过大
      if (i + CONCURRENT_LIMIT < batches.length) {
        await new Promise((resolve) => { return setTimeout(resolve, 500); });
      }
    }

    return results;
  }

  // 处理向量定位单个批次
  processVectorBatch = (batchTexts, batchIndex) => {
    return new Promise((resolve, reject) => {
      const { vectorAnchorInfo, vectorPrompt } = this.state;

      try {
        const flowId = FLOW_MAP[Platform.isProd() ? 'prod' : 'stg'];
        const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflow-v2/run/${flowId}`;
        const query = { access_token: Sessions.getToken() };

        // 为每个批次创建独立的WebSocket连接
        const ws = new ReconnectingWebSocket(
          `${path}?${qs.stringify(query)}`,
          [],
          (e) => { this.onReceiveVectorMsg(e, batchIndex, resolve, reject); },
          () => {
            ws.send(JSON.stringify({
              text: JSON.stringify({
                sku: `可选sku库：\n${batchTexts.join('\n')}`,
                anchor: vectorAnchorInfo,
                prompt: vectorPrompt,
              }),
              type: 'message',
              is_beta: false,
            }));
          },
        );

        // 将连接存储到向量定位连接池中
        this.vectorWsConnections.set(batchIndex, ws);

        // 设置超时处理
        setTimeout(() => {
          if (this.vectorWsConnections.has(batchIndex)) {
            console.warn(`向量定位批次 ${batchIndex + 1} 处理超时`); // eslint-disable-line no-console
            this.cleanupVectorConnection(batchIndex);
            reject(new Error(`向量定位批次 ${batchIndex + 1} 处理超时`));
          }
        }, 10 * 60 * 1000); // 10分钟超时
      } catch (error) {
        console.error(`处理向量定位第${batchIndex + 1}批数据失败:`, error); // eslint-disable-line no-console
        reject(error);
      }
    });
  }

  // 处理下一批数据 - 保留兼容性，但不再使用   此方法保留用于向后兼容，实际使用并发处理
  processNextBatch = () => {
    console.warn('processNextBatch 方法已废弃，请使用并发处理'); // eslint-disable-line no-console
  }

  // 向量定位并发处理所有批次
  processVectorBatchesConcurrently = async (batches) => {
    const results = [];

    // 分组处理，每组最多CONCURRENT_LIMIT个批次
    for (let i = 0; i < batches.length; i += CONCURRENT_LIMIT) {
      const batchGroup = batches.slice(i, i + CONCURRENT_LIMIT);
      const batchPromises = batchGroup.map((batch, index) => {
        const batchIndex = i + index;
        return this.processVectorBatch(batch, batchIndex)
          .then((result) => { return { batchIndex, result, success: true }; })
          .catch((error) => { return { batchIndex, error, success: false }; });
      });

      // 等待当前组的所有批次完成
      const groupResults = await Promise.all(batchPromises);
      results.push(...groupResults);

      // 更新向量定位进度
      this.setState((prevState) => {
        return {
          vectorCompletedBatches: prevState.vectorCompletedBatches + batchGroup.length,
        };
      });

      // 如果不是最后一组，稍微延迟避免服务器压力过大
      if (i + CONCURRENT_LIMIT < batches.length) {
        await new Promise((resolve) => { return setTimeout(resolve, 500); });
      }
    }

    return results;
  }

  // 导出筛选结果
  exportFilterResults = () => {
    const { filterSkus, excelData, headers, originalSkuIds } = this.state;

    if (!filterSkus || filterSkus.length === 0) {
      message.warning('没有筛选结果可以导出');
      return;
    }

    try {
      // 创建导出数据
      const exportData = [];

      // 添加表头，原始表头 + 理由列
      const exportHeaders = [...headers, '理由'];
      exportData.push(exportHeaders);

      // 处理每个筛选结果
      filterSkus.forEach((filterItem) => {
        const skuId = filterItem.sku;
        let rowIndex = -1;

        // 根据是否存在originalSkuIds来确定行索引计算方式
        if (originalSkuIds && originalSkuIds.length > 0) {
          // 多次"基于结果再定位"的情况：通过查找SKU ID在originalSkuIds中的索引
          rowIndex = originalSkuIds.findIndex((id) => { return String(id) === String(skuId); });
        } else {
          // 首次筛选的情况：使用简单的数字计算
          const parsedSkuId = parseInt(skuId, 10);
          if (Number.isInteger(parsedSkuId) && parsedSkuId > 0) {
            rowIndex = parsedSkuId - 1; // SKU ID从1开始，数组索引从0开始
          }
        }

        // 验证索引有效性并添加数据
        if (rowIndex >= 0 && rowIndex < excelData.length) {
          const originalRow = excelData[rowIndex];
          if (originalRow && Array.isArray(originalRow)) {
            // 原始数据 + 理由
            const exportRow = [...originalRow, filterItem.理由 || ''];
            exportData.push(exportRow);
          } else {
            console.warn(`SKU ID ${skuId} 对应的数据行无效，跳过该条记录`); // eslint-disable-line no-console
          }
        } else {
          console.warn(`无法找到SKU ID ${skuId} 对应的数据行（索引：${rowIndex}），跳过该条记录`); // eslint-disable-line no-console
        }
      });

      // 检查是否有有效的导出数据
      if (exportData.length <= 1) { // 只有表头，没有数据行
        message.error('没有找到有效的数据可以导出，请检查筛选结果');
        return;
      }

      // 创建工作簿
      const worksheet = XLSX.utils.aoa_to_sheet(exportData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '筛选结果');

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const fileName = `SKU筛选结果_${timestamp}.xlsx`;

      // 导出文件
      XLSX.writeFile(workbook, fileName);

      const actualExportCount = exportData.length - 1; // 减去表头行
      message.success(`成功导出 ${actualExportCount} 条筛选结果`);
    } catch (error) {
      console.error('导出失败:', error); // eslint-disable-line no-console
      message.error('导出失败，请重试');
    }
  }

  // 导出向量结果
  exportVectorResults = () => {
    const { vectorResults, vectorFilterSkus } = this.state;

    // 检查是否有筛选结果
    if (!vectorFilterSkus || vectorFilterSkus.length === 0) {
      message.warning('没有筛选结果可以导出');
      return;
    }

    try {
      // 定义字段映射关系
      const fieldMapping = {
        id: 'ID',
        name: '名称',
        link: '链接',
        douyinlink: '抖音链接',
        kolId: 'KOL ID',
        sales_30d: '30天销量',
        listSales_30d: '30天销售额',
        views_30d: '30天观看量',
        conversion_30d: '30天转化率',
        liveSales_30d: '直播销售30天',
        videoSales_30d: '视频销售30天',
        genderDistribution: '性别分布',
        overview: '用户概览',
        kolList: 'KOL列表详情',
        listCount_30d: '筛选SKU',
        listLiveSales: '筛选标题',
        listVideoSales: '筛选理由',
        aiFeatureAnalysis: 'AI特征分析',
      };

      // 构建表头，包含所有字段和额外的理由字段
      const headers = [
        ...Object.values(fieldMapping),
        '理由', // 额外增加的理由字段
      ];

      // 构建筛选结果数据
      const exportData = vectorFilterSkus.map((filterItem) => {
        // 根据筛选结果的sku找到对应的向量查询结果
        const vectorItem = vectorResults.find((item) => {
          return +item.id === +filterItem.sku || item.name === filterItem.sku;
        }) || {};

        // 按照字段映射顺序构建行数据
        const rowData = [
          vectorItem.id || '',
          vectorItem.name || '',
          vectorItem.link || '',
          vectorItem.douyinlink || '',
          vectorItem.kolId || '',
          vectorItem.sales_30d || '',
          vectorItem.listSales_30d || '',
          vectorItem.views_30d || '',
          vectorItem.conversion_30d || '',
          vectorItem.liveSales_30d || '',
          vectorItem.videoSales_30d || '',
          vectorItem.genderDistribution || '',
          vectorItem.overview || '',
          vectorItem.kolList || '',
          vectorItem.listCount_30d || '',
          vectorItem.listLiveSales || '',
          vectorItem.listVideoSales || '',
          vectorItem.aiFeatureAnalysis || '',
          filterItem.理由 || '', // 筛选结果中的理由字段
        ];

        return rowData;
      });

      // 创建工作表数据
      const wsData = [headers, ...exportData];
      const ws = XLSX.utils.aoa_to_sheet(wsData);

      // 设置列宽
      const colWidths = [
        { wch: 8 }, // ID
        { wch: 15 }, // 名称
        { wch: 30 }, // 链接
        { wch: 30 }, // 抖音链接
        { wch: 12 }, // 分类
        { wch: 20 }, // KOL ID
        { wch: 15 }, // 30天销量
        { wch: 15 }, // 30天销售额
        { wch: 15 }, // 30天观看量
        { wch: 15 }, // 30天转化率
        { wch: 15 }, // 直播销售30天
        { wch: 15 }, // 视频销售30天
        { wch: 15 }, // 性别分布
        { wch: 25 }, // 用户概览
        { wch: 50 }, // KOL列表详情
        { wch: 12 }, // 筛选SKU
        { wch: 20 }, // 筛选标题
        { wch: 30 }, // 筛选理由
        { wch: 20 }, // 合作伙伴ID
        { wch: 30 }, // AI特征分析
        { wch: 30 }, // 理由
      ];

      ws['!cols'] = colWidths;

      // 创建工作簿并添加筛选结果工作表
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, '筛选结果');

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const fileName = `向量查询筛选结果_${timestamp}.xlsx`;

      // 导出文件
      XLSX.writeFile(wb, fileName);

      message.success(`成功导出 ${vectorFilterSkus.length} 条筛选结果`);
    } catch (error) {
      console.error('向量结果导出失败:', error); // eslint-disable-line no-console
      message.error('导出失败，请重试');
    }
  }

  reFilterResults = () => {
    const { filterSkus, excelData, headers, isProcessing, originalSkuIds } = this.state;

    if (isProcessing) {
      message.warning('正在处理中，请等待当前任务完成');
      return;
    }

    if (!filterSkus || filterSkus.length === 0) {
      message.warning('没有筛选结果可以进行再次定位');
      return;
    }

    try {
      const newExcelData = [];
      const newOriginalSkuIds = []; // 保存原始SKU ID的映射

      filterSkus.forEach((filterItem) => {
        const skuId = filterItem.sku;

        let targetRowIndex = -1;
        let targetOriginalSkuId = skuId;

        if (originalSkuIds && originalSkuIds.length > 0) {
          targetRowIndex = originalSkuIds.findIndex((id) => { return id === skuId; });
          targetOriginalSkuId = skuId; // 保持原始SKU ID
        } else {
          targetRowIndex = parseInt(skuId, 10) - 1; // SKU ID从1开始，数组索引从0开始
          targetOriginalSkuId = skuId;
        }

        if (targetRowIndex >= 0 && targetRowIndex < excelData.length) {
          const originalRow = excelData[targetRowIndex];
          newExcelData.push([...originalRow]);
          newOriginalSkuIds.push(targetOriginalSkuId);
        } else {
          console.warn(`无法找到SKU ID ${skuId} 对应的数据行，跳过该条记录`); // eslint-disable-line no-console
        }
      });

      if (newExcelData.length === 0) {
        message.error('无法构建有效的筛选数据');
        return;
      }

      // 清除缓存，因为数据已更新
      this.clearCache();

      // 更新状态，使用筛选结果作为新的数据源
      this.setState({
        headers: [...headers], // 保持原始表头，不添加理由列
        excelData: newExcelData,
        filterSkus: [], // 重置筛选结果
        fileName: `筛选结果_${filterSkus.length}条`, // 更新文件名显示
        originalSkuIds: newOriginalSkuIds, // 保存原始SKU ID映射
      });

      message.success(`已将 ${filterSkus.length} 条筛选结果设为新的数据源，正在自动开始筛选...`);

      // 自动开始筛选，短暂延迟让用户看到成功提示
      setTimeout(() => {
        this.onFilter();
      }, 800);
    } catch (error) {
      console.error('再次筛选准备失败:', error); // eslint-disable-line no-console
      message.error('再次筛选准备失败，请重试');
    }
  }

  // localStorage 保存工具函数
  saveToStorage = (key, value) => {
    try {
      localStorage.setItem(`sku_filter_${key}`, value);
    } catch (error) {
      console.error(`保存 ${key} 失败:`, error); // eslint-disable-line no-console
    }
  }

  getStorageData = (key) => {
    try {
      return localStorage.getItem(`sku_filter_${key}`) || null;
    } catch (error) {
      console.error(`读取 ${key} 失败:`, error); // eslint-disable-line no-console
      return null;
    }
  }

  // 通用Excel解析方法
  parseExcelFile = (file, options = {}) => {
    const {
      outputFormat = 'array', // 'array' | 'object'
      includeHeaders = true,
      filterEmptyRows = true,
    } = options;

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

          if (jsonData.length === 0) {
            reject(new Error('Excel文件为空'));
            return;
          }

          let headers = [];
          let rows = jsonData;

          if (includeHeaders && jsonData.length > 0) {
            [headers, ...rows] = jsonData;
          }

          // 过滤空行
          if (filterEmptyRows) {
            rows = rows.filter((row) => {
              return row && row.length > 0 && row.some((cell) => {
                return cell !== null && cell !== undefined && cell !== '';
              });
            });
          }

          // 根据输出格式处理数据
          if (outputFormat === 'object') {
            const objectData = rows.map((row, index) => {
              const rowData = { key: index };
              headers.forEach((header, headerIndex) => {
                rowData[header] = row[headerIndex] || '';
              });
              return rowData;
            });
            resolve({ headers, data: objectData });
          } else {
            resolve({ headers, datas: rows });
          }
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };

      reader.readAsArrayBuffer(file);
    });
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ [key]: value });

    // 自动保存特定字段到 localStorage
    if (['anchorInfo', 'promptText', 'vectorAnchorInfo', 'vectorPrompt'].includes(key)) {
      this.saveToStorage(key, value);
    }
  }

  // Tab切换处理方法
  onTabChange = (activeKey) => {
    this.setState({ activeKey });
  }

  // 向量定位相关方法
  onUploadVectorExcel = async (option) => {
    try {
      this.setState({ isVectorUploading: true });

      const { file } = option;
      const fileName = file.name;

      // 上传文件到OSS
      const url = await AliyunHelper.clipsUploadImage(file, (progress) => {
        const percent = Math.round((progress.loaded / progress.total) * 100);
        option.onProgress({ percent });
      });

      await Market.importExternalKolClasses({ fileUrl: url, category: '线上课' });

      this.setState({ vectorFileName: fileName });

      option.onSuccess();
      message.success(`成功上传并解析Excel文件: ${fileName}`);
    } catch (error) {
      console.error('文件上传或解析失败:', error); // eslint-disable-line no-console
      if (error.message === 'Excel文件为空') {
        message.error('Excel文件为空');
      } else if (error.message === '文件读取失败') {
        message.error('文件读取失败');
      } else {
        message.error('文件上传失败，请重试');
      }
      option.onError();
    } finally {
      this.setState({ isVectorUploading: false });
    }
  }

  onVectorQuery = async () => {
    const { vectorAnchorInfo, vectorTopK, vectorThreshold } = this.state;

    if (!vectorAnchorInfo.trim()) {
      message.warning('请输入待定位主播信息');
      return;
    }

    try {
      this.setState({ isVectorQuerying: true });
      const data = await Market.vectorSearchExternalKolClasses({
        query: vectorAnchorInfo,
        topk: vectorTopK,
        threshold: vectorThreshold,
      });

      // 保存查询结果并显示数据条数
      this.setState({ vectorResults: data || [] });
      const resultCount = (data && data.length) || 0;
      message.success(`向量查询完成，找到 ${resultCount} 条匹配数据`);
    } catch (error) {
      console.error('向量查询失败:', error); // eslint-disable-line no-console
      message.error('向量查询失败，请重试');
    } finally {
      this.setState({ isVectorQuerying: false });
    }
  }

  onStartVectorPositioning = async () => {
    const { vectorAnchorInfo, vectorPrompt, isVectorProcessing, vectorResults } = this.state;

    if (!vectorAnchorInfo.trim()) {
      message.warning('请输入待定位主播信息');
      return;
    }

    if (!vectorPrompt.trim()) {
      message.warning('请输入提示词');
      return;
    }

    if (!vectorResults || vectorResults.length === 0) {
      message.warning('请先进行向量查询获取数据');
      return;
    }

    if (isVectorProcessing) {
      message.warning('正在处理中，请等待当前任务完成');
      return;
    }

    try {
      // 每次点击开始定位时将向量筛选结果置空
      this.setState({ vectorFilterSkus: [] });

      // 获取向量定位处理后的文本数据
      const texts = this.getVectorProcessedTexts();

      if (texts.length === 0) {
        message.error('没有找到有效的向量数据，请检查向量查询结果');
        return;
      }

      // 创建分批队列
      const batches = this.createBatches(texts);

      this.setState({
        isVectorProcessing: true,
        vectorTotalBatches: batches.length,
        vectorAllResults: [],
        vectorFilterSkus: [], // 重置向量筛选结果
        vectorCompletedBatches: 0, // 重置已完成批次数
      });

      message.info(`开始向量定位并发处理，共 ${batches.length} 个批次，每批最多 ${BATCH_SIZE} 条数据，并发数: ${CONCURRENT_LIMIT}`);

      // 使用向量定位并发处理
      const results = await this.processVectorBatchesConcurrently(batches);

      // 处理结果
      const successCount = results.filter((r) => { return r?.success !== false; }).length;
      const failCount = results.length - successCount;

      if (failCount > 0) {
        message.warning(`向量定位处理完成！成功: ${successCount} 批，失败: ${failCount} 批`);
      } else {
        message.success(`向量定位所有批次处理完成！共处理 ${successCount} 批数据`);
      }

      this.onVectorAllBatchesComplete();
    } catch (error) {
      console.error('向量定位并发处理失败:', error); // eslint-disable-line no-console
      message.error('向量定位处理过程中发生错误，请重试');
      this.setState({ isVectorProcessing: false });
    }
  }

  // 所有批次处理完成
  onAllBatchesComplete = () => {
    // 清理所有剩余的WebSocket连接
    this.wsConnections.forEach((ws, batchIndex) => {
      this.cleanupConnection(batchIndex);
    });

    this.setState({
      isProcessing: false,
      totalBatches: 0,
      completedBatches: 0,
    });
  }

  // 向量定位所有批次处理完成
  onVectorAllBatchesComplete = () => {
    // 清理所有剩余的向量定位WebSocket连接
    this.vectorWsConnections.forEach((ws, batchIndex) => {
      this.cleanupVectorConnection(batchIndex);
    });

    this.setState({
      isVectorProcessing: false,
      vectorTotalBatches: 0,
      vectorCompletedBatches: 0,
    });
  }

  // Excel文件解析方法 - 重构为使用通用解析方法
  onReadExcelFile = async (file) => {
    try {
      const result = await this.parseExcelFile(file, {
        outputFormat: 'array',
        includeHeaders: true,
        filterEmptyRows: true,
      });
      return result;
    } catch (error) {
      throw error;
    }
  }

  // 处理文件上传
  onUploadExcel = async (option) => {
    const { file } = option;

    // 检查文件类型
    const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel' ||
      file.name.endsWith('.xlsx') ||
      file.name.endsWith('.xls');

    if (!isExcel) {
      message.error('请上传Excel文件（.xlsx或.xls格式）');
      option.onError();
      return;
    }

    this.setState({ isUploading: true });

    try {
      const excelData = await this.onReadExcelFile(file);

      // 清除缓存，因为数据已更新
      this.clearCache();

      this.setState({
        fileName: file.name,
        headers: excelData.headers,
        excelData: excelData.datas,
        isUploading: false,
        filterSkus: [], // 重置筛选结果
        originalSkuIds: null, // 重置原始SKU ID映射
      });

      message.success(`成功解析Excel文件，共${excelData.datas.length}行数据`);
      option.onSuccess();
    } catch (error) {
      console.error('Excel解析失败:', error); // eslint-disable-line no-console
      message.error('Excel文件解析失败，请检查文件格式');
      this.setState({ isUploading: false });
      option.onError();
    }
  }

  onFilter = async () => {
    const { isProcessing } = this.state;

    if (isProcessing) {
      message.warning('正在处理中，请等待当前任务完成');
      return;
    }

    // 每次点击开始定位时将 filterSkus 置空
    this.setState({ filterSkus: [] });

    // 使用缓存的数据处理方法
    const texts = this.getProcessedTexts();

    if (texts.length === 0) {
      message.error('没有找到有效的AI分析数据，请检查Excel文件格式');
      return;
    }

    // 创建分批队列
    const batches = this.createBatches(texts);

    this.setState({
      isProcessing: true,
      totalBatches: batches.length,
      allResults: [],
      filterSkus: [], // 重置筛选结果
      completedBatches: 0, // 重置已完成批次数
    });

    message.info(`开始并发处理，共 ${batches.length} 个批次，每批最多 ${BATCH_SIZE} 条数据，并发数: ${CONCURRENT_LIMIT}`);

    try {
      // 使用并发处理
      const results = await this.processBatchesConcurrently(batches);

      // 处理结果
      const successCount = results.filter((r) => { return r?.success !== false; }).length;
      const failCount = results.length - successCount;

      if (failCount > 0) {
        message.warning(`处理完成！成功: ${successCount} 批，失败: ${failCount} 批`);
      } else {
        message.success(`所有批次处理完成！共处理 ${successCount} 批数据`);
      }

      this.onAllBatchesComplete();
    } catch (error) {
      console.error('并发处理失败:', error); // eslint-disable-line no-console
      message.error('处理过程中发生错误，请重试');
      this.setState({ isProcessing: false });
    }
  }

  onReceiveMsg = async (e, batchIndex, resolve, reject) => {
    if (e?.data !== 'pong') {
      const originData = JSON.parse(e.data);
      const { type, data } = StringExtension.snakeToCamelObj(originData);
      if (type === EVENT_TYPE.FINAL_RESULT) {
        try {
          const { output } = JSON.parse(data?.output);
          const { totalBatches } = this.state;
          const filterSkus = JSONC.parse(output || '[]');

          // 优化状态更新：使用函数式更新避免重复读取 state
          this.setState((prevState) => {
            // 使用 push 而不是展开操作符，提高性能
            const newResults = prevState.allResults.slice(); // 浅拷贝
            newResults.push({
              batchIndex,
              result: output,
              timestamp: new Date().toISOString(),
            });

            const newFilterSkus = prevState.filterSkus.slice(); // 浅拷贝
            newFilterSkus.push(...filterSkus);

            return {
              allResults: newResults,
              filterSkus: newFilterSkus,
            };
          });

          message.success(`第 ${batchIndex + 1}/${totalBatches} 批处理完成`);

          // 清理连接
          this.cleanupConnection(batchIndex);

          // 解析Promise
          if (resolve) {
            resolve({ batchIndex, result: output, filterSkus });
          }
        } catch (error) {
          console.error(`第${batchIndex + 1}批结果解析失败:`, error); // eslint-disable-line no-console
          message.error(`第 ${batchIndex + 1} 批结果解析失败`);

          // 清理连接
          this.cleanupConnection(batchIndex);

          // 拒绝Promise
          if (reject) {
            reject(error);
          }
        }
      }
    }
  }

  // 向量定位消息处理
  onReceiveVectorMsg = async (e, batchIndex, resolve, reject) => {
    if (e?.data !== 'pong') {
      const originData = JSON.parse(e.data);
      const { type, data } = StringExtension.snakeToCamelObj(originData);
      if (type === EVENT_TYPE.FINAL_RESULT) {
        try {
          const { output } = JSON.parse(data?.output);
          const { vectorTotalBatches } = this.state;
          const filterSkus = JSONC.parse(output || '[]');

          // 优化状态更新：使用函数式更新避免重复读取 state
          this.setState((prevState) => {
            // 使用 push 而不是展开操作符，提高性能
            const newResults = prevState.vectorAllResults.slice(); // 浅拷贝
            newResults.push({
              batchIndex,
              result: output,
              timestamp: new Date().toISOString(),
            });

            const newFilterSkus = prevState.vectorFilterSkus.slice(); // 浅拷贝
            newFilterSkus.push(...filterSkus);

            return {
              vectorAllResults: newResults,
              vectorFilterSkus: newFilterSkus,
            };
          });

          message.success(`向量定位第 ${batchIndex + 1}/${vectorTotalBatches} 批处理完成`);

          // 清理连接
          this.cleanupVectorConnection(batchIndex);

          // 解析Promise
          if (resolve) {
            resolve({ batchIndex, result: output, filterSkus });
          }
        } catch (error) {
          console.error(`向量定位第${batchIndex + 1}批结果解析失败:`, error); // eslint-disable-line no-console
          message.error(`向量定位第 ${batchIndex + 1} 批结果解析失败`);

          // 清理连接
          this.cleanupVectorConnection(batchIndex);

          // 拒绝Promise
          if (reject) {
            reject(error);
          }
        }
      }
    }
  }

  // 通用的TextArea输入框渲染方法
  renderTextAreaInputs = (configs) => {
    return configs.map((config) => {
      const { label, key, value, placeholder, rows = 6 } = config;
      return (
        <Form.Item key={key} label={label}>
          <Input.TextArea
            rows={rows}
            value={value}
            placeholder={placeholder}
            onChange={(e) => { return this.onChangeValue(e, key); }}
          />
        </Form.Item>
      );
    });
  }

  // 渲染筛选结果表格
  renderFilterTable = () => {
    const { filterSkus, isProcessing } = this.state;

    return (
      <div className="filter-results-table">
        <div className="table-header">
          <h3>筛选结果</h3>
          <div className="table-actions">
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={this.exportFilterResults}
              size="small"
              style={{ marginRight: 8 }}
            >
              导出结果
            </Button>
            <Button
              type="default"
              onClick={this.reFilterResults}
              size="small"
              disabled={isProcessing}
              loading={isProcessing}
            >
              基于结果再定位
            </Button>
          </div>
        </div>
        <Table
          columns={TABLE_COLUMNS}
          dataSource={filterSkus}
          rowKey={(record, index) => { return `${record.sku || ''}-${index}`; }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => { return `共 ${total} 条记录`; },
          }}
          locale={{ emptyText: '暂无筛选结果' }}
        />
      </div>
    );
  }

  // 渲染向量定位筛选结果表格
  renderVectorFilterTable = () => {
    const { vectorFilterSkus } = this.state;

    const columns = [
      { title: 'SKU', dataIndex: 'sku', key: 'sku', width: 100 },
      { title: '标题', dataIndex: '标题', key: '标题', ellipsis: true },
      { title: '理由', dataIndex: '理由', key: '理由', ellipsis: true },
    ];

    return (
      <div className="filter-results-table">
        <div className="table-header">
          <h3>向量定位筛选结果</h3>
          <div className="table-actions">
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={this.exportVectorResults}
              size="small"
              disabled={!vectorFilterSkus || vectorFilterSkus.length === 0}
            >
              导出Excel
            </Button>
          </div>
        </div>
        <Table
          columns={columns}
          dataSource={vectorFilterSkus.map((item, index) => { return { ...item, key: index }; })}
          rowKey={(record, index) => { return `${record.sku || ''}-${index}`; }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => { return `共 ${total} 条记录`; },
          }}
          locale={{ emptyText: '暂无筛选结果' }}
          scroll={{ y: 400 }}
        />
      </div>
    );
  }

  // 渲染大语言定位面板内容
  renderLLMPositioningPanel = () => {
    const { anchorInfo, promptText, isUploading, fileName, isProcessing,
      completedBatches, totalBatches, filterSkus } = this.state;

    const textAreaConfigs = [
      { label: '待定位主播', key: 'anchorInfo', value: anchorInfo, placeholder: '请输入内容...' },
      { label: '提示词', key: 'promptText', value: promptText, placeholder: '请输入内容...' },
    ];

    return (
      <>
        <Form layout="vertical">
          <Form.Item label="Excel文件上传">
            <Upload.Dragger
              accept={ACCEPT_TYPES}
              customRequest={this.onUploadExcel}
              showUploadList={false}
              disabled={isUploading}
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">
                {isUploading ? '正在解析Excel文件...' : '点击或拖拽Excel文件到此区域上传'}
              </p>
              <p className="ant-upload-hint">
                支持.xlsx和.xls格式的Excel文件
              </p>
              {fileName && (
                <p style={{ color: '#52c41a', marginTop: 8 }}>
                  已上传: {fileName}
                </p>
              )}
            </Upload.Dragger>
          </Form.Item>

          {this.renderTextAreaInputs(textAreaConfigs)}

          <Form.Item className="filter-button">
            <Button type="primary" size="large" onClick={this.onFilter} loading={isProcessing} disabled={isProcessing}>
              {isProcessing ? `并发处理中 (${completedBatches}/${totalBatches})` : '开始定位'}
            </Button>
            {isProcessing && (
              <div style={{ marginTop: 8, color: '#1890ff' }}>
                正在并发处理，已完成 {completedBatches} 批，共 {totalBatches} 批数据...
              </div>
            )}
          </Form.Item>
        </Form>

        {filterSkus && filterSkus.length > 0 && this.renderFilterTable()}
      </>
    );
  }

  // 渲染向量定位面板
  renderVectorPositioningPanel = () => {
    const { vectorFileName, vectorAnchorInfo, vectorPrompt, isVectorUploading,
      isVectorQuerying, isVectorProcessing, vectorResults, vectorTopK, vectorThreshold,
      vectorFilterSkus, vectorCompletedBatches, vectorTotalBatches,
    } = this.state;

    // 配置输入框参数
    const textAreaConfigs = [
      { label: '待定位主播', key: 'vectorAnchorInfo', value: vectorAnchorInfo, placeholder: '请输入待定位主播信息...' },
      { label: '提示词', key: 'vectorPrompt', value: vectorPrompt, placeholder: '请输入向量定位提示词...' },
    ];

    return (
      <>
        <Form layout="vertical">
          <Form.Item label="Excel文件上传">
            <Upload.Dragger
              accept={ACCEPT_TYPES}
              customRequest={this.onUploadVectorExcel}
              showUploadList={false}
              disabled={isVectorUploading}
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">
                {isVectorUploading ? '正在上传并解析Excel文件...' : '点击或拖拽Excel文件到此区域上传'}
              </p>
              <p className="ant-upload-hint">
                支持.xlsx和.xls格式的Excel文件，将自动上传到OSS
              </p>
              {vectorFileName && (
                <p style={{ color: '#52c41a', marginTop: 8 }}>
                  已上传: {vectorFileName}
                </p>
              )}
            </Upload.Dragger>
          </Form.Item>

          {this.renderTextAreaInputs(textAreaConfigs)}

          <Form.Item label="查询参数">
            <div style={{ display: 'flex', gap: '16px' }}>
              <div style={{ flex: 1 }}>
                <div style={{ marginBottom: 4, fontSize: 14, color: '#666' }}>Top K</div>
                <InputNumber
                  min={1}
                  max={100}
                  value={vectorTopK}
                  placeholder="返回结果数量"
                  style={{ width: '100%' }}
                  onChange={(e) => { return this.onChangeValue(e, 'vectorTopK'); }}
                />
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ marginBottom: 4, fontSize: 14, color: '#666' }}>Threshold</div>
                <InputNumber
                  min={0}
                  max={1}
                  step={0.1}
                  value={vectorThreshold}
                  placeholder="相似度阈值"
                  style={{ width: '100%' }}
                  onChange={(e) => { return this.onChangeValue(e, 'vectorThreshold'); }}
                />
              </div>
            </div>
          </Form.Item>

          <Form.Item>
            <div style={{ display: 'flex', gap: '12px' }}>
              <Button
                type="default"
                size="large"
                onClick={this.onVectorQuery}
                loading={isVectorQuerying}
                disabled={isVectorQuerying || isVectorProcessing}
              >
                {isVectorQuerying ? '向量查询中...' : '向量查询'}
              </Button>
              <Button
                type={vectorResults && vectorResults.length > 0 ? 'primary' : 'default'}
                size="large"
                onClick={this.onStartVectorPositioning}
                loading={isVectorProcessing}
                disabled={isVectorProcessing || isVectorQuerying || !vectorResults || vectorResults.length === 0}
                style={{
                  backgroundColor: vectorResults && vectorResults.length > 0 ? '#1890ff' : undefined,
                  borderColor: vectorResults && vectorResults.length > 0 ? '#1890ff' : undefined,
                  color: vectorResults && vectorResults.length > 0 ? '#fff' : undefined,
                }}
              >
                {isVectorProcessing ? `定位处理中 (${vectorCompletedBatches}/${vectorTotalBatches})` : '开始定位'}
              </Button>
            </div>
            {isVectorProcessing && (
              <div style={{ marginTop: 8, color: '#1890ff' }}>
                正在向量定位并发处理，已完成 {vectorCompletedBatches} 批，共 {vectorTotalBatches} 批数据...
              </div>
            )}
          </Form.Item>
        </Form>

        {vectorResults && vectorResults.length > 0 && (
          <div style={{ marginTop: 24 }}>
            <h3>向量查询结果</h3>
            <div style={{ padding: 16, background: '#f5f5f5', borderRadius: 4 }}>
              找到 {vectorResults.length} 条匹配结果
            </div>
          </div>
        )}

        {vectorFilterSkus && vectorFilterSkus.length > 0 && (
          <div style={{ marginTop: 24 }}>
            <div style={{ padding: 16, background: '#e6f7ff', borderRadius: 4, marginBottom: 16 }}>
              <strong>筛选完成！</strong> 从 {vectorResults?.length || 0} 条向量数据中筛选出 {vectorFilterSkus.length} 条匹配结果
            </div>
            {this.renderVectorFilterTable()}
          </div>
        )}
      </>
    );
  }

  // 渲染向量列表面板
  renderVectorListPanel = () => {
    const { vectorListData, vectorListLoading } = this.state;

    return (
      <>
        <div className="filter-results-table">
          <div className="table-header">
            <h3>向量列表</h3>
            <div className="table-actions">
              <Button
                type="default"
                onClick={() => { return this.fetchVectorListData({ pageIndex: 1, pageSize: 20 }); }}
                size="small"
                loading={vectorListLoading}
              >
                刷新数据
              </Button>
            </div>
          </div>
          <PaginationTable
            totalDataCount={this.state.total || 0}
            dataSource={vectorListData}
            pagination={this.state.vectorListPagination}
            columns={VECTOR_LIST_COLUMNS}
            onPaginationChange={this.fetchVectorListData}
          />
        </div>
      </>
    );
  }

  render = () => {
    const { activeKey } = this.state;

    return (
      <div className="chat-knowledge sku-filter-container" style={{ padding: 30, background: '#fff' }}>
        <Tabs
          activeKey={activeKey}
          onChange={this.onTabChange}
        >
          <Tabs.TabPane tab="大语言定位" key="llm-positioning">
            {this.renderLLMPositioningPanel()}
          </Tabs.TabPane>
          <Tabs.TabPane tab="向量定位" key="vector-positioning">
            {this.renderVectorPositioningPanel()}
          </Tabs.TabPane>
          <Tabs.TabPane tab="向量列表" key="vector-list">
            {this.renderVectorListPanel()}
          </Tabs.TabPane>
        </Tabs>
      </div>
    );
  }
}
