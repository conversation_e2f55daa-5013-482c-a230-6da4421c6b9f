import { CopyOutlined } from '@ant-design/icons';
import { FilterBar, PaginationTable, Toast } from '~/components';
import Consts from '~/consts';
import { <PERSON>yunHelper, Sessions } from '~/engine';
import { Platform } from '~/plugins';
import { Button, Divider, InputNumber, Popconfirm, Select, Switch, Tabs, Tag } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import BundleDrawer from './components/BundleDrawer';
import CourseDrawer from './components/CourseDrawer';
import FuncDrawer from './components/FuncDrawer';
import LiveRoomTabPane from './components/LiveRoom';
import RedeemCodeDrawer from './components/RedeemCodeDrawer';
import TagDrawer from './components/TagDrawer';
import Configs from './const';
import reducer, * as actions from './state';

const DEFAULT_COURSE = {
  ttsSettings: {
    provider: 'openai',
    model: 'tts-1',
    voice: 'alloy',
    speed: 1.25,
  },
};
const DEFAULT_BUNDLE = {
  name: '',
  price: 0,
  onSale: false,
  salePlatform: undefined,
  salePlatformId: '',
};

@connect(
  (state) => {
    return state.marketCourseMaterials;
  },
  actions,
)
export default class MarketCourseMaterials extends Component {
  static propTypes = {
    filter: PropTypes.object.isRequired,
    pagination: PropTypes.object.isRequired,
    total: PropTypes.number.isRequired,
    fetchCourse: PropTypes.func.isRequired,
    createCourse: PropTypes.func.isRequired,
    updateCourse: PropTypes.func.isRequired,
    deleteCourse: PropTypes.func.isRequired,
    fetchCourses: PropTypes.func.isRequired,
    getDefaultAssistantPrompt: PropTypes.func.isRequired,
    list: PropTypes.array.isRequired,
    clearState: PropTypes.func.isRequired,
    createBundle: PropTypes.func.isRequired,
    updateBundle: PropTypes.func.isRequired,
    fetchBundles: PropTypes.func.isRequired,
    getBundle: PropTypes.func.isRequired,
    bundles: PropTypes.array.isRequired,
    bundlePagination: PropTypes.object.isRequired,
    courses: PropTypes.array.isRequired,
    fetchAllCourses: PropTypes.func.isRequired,
    bundlesTotal: PropTypes.number.isRequired,
    deleteBundle: PropTypes.func.isRequired,
    allBundles: PropTypes.array.isRequired,
    fetchAllBundles: PropTypes.func.isRequired,
    fetchRedeemCodes: PropTypes.func.isRequired,
    generateRedeemCodes: PropTypes.func.isRequired,
    rooms: PropTypes.array.isRequired,
    roomTotal: PropTypes.number.isRequired,
    roomFilter: PropTypes.object.isRequired,
    roomPagination: PropTypes.object.isRequired,
    fetchRooms: PropTypes.func.isRequired,
    deleteRoom: PropTypes.func.isRequired,
    createRoom: PropTypes.func.isRequired,
    updateRoom: PropTypes.func.isRequired,
    addCourseFunc: PropTypes.func.isRequired,
    location: PropTypes.object.isRequired,
    setState: PropTypes.func.isRequired,
  }

  state = {
    activeKey: 'bundles',
    onSale: undefined,
    keywords: '',
    bundleUuid: undefined,
    course: { type: 'course' },
    bundle: {},
    defaultPrompts: {},
  }

  componentDidMount = async () => {
    this.props.fetchCourses();
    this.props.fetchBundles();
    this.props.fetchRooms();
    this.props.fetchAllCourses();
    this.props.fetchAllBundles();
    const tags = await this.fetchTags();
    const defaultPrompts = await this.props.getDefaultAssistantPrompt({ courseType: '' });
    const stateObj = { defaultPrompts, tags };
    const { activeKey } = this.props.location.query;
    if (!_.isUndefined(activeKey)) {
      stateObj.activeKey = activeKey;
    }


    this.setState(stateObj);
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  fetchTags = async () => {
    let tags = [];
    try {
      const resp = await fetch(`${Consts.OSS_CDN_DOMAIN}faas/html/course_tags.json?v=${Date.now()}`);
      tags = await resp.json();
    } catch (error) {
      // console.log(error);
    }
    return tags;
  }

  onSaveTags = async (tags) => {
    const blob = new Blob([JSON.stringify(tags)], { type: 'application/json' });
    await AliyunHelper.uploadImageBlob(blob, () => { }, 'faas/html/course_tags.json');
    this.setState({ tags, openTag: false });
  }

  onUpload = async (option, key) => {
    try {
      const url = await AliyunHelper.clipsUploadImage(option.file, (progress) => {
        const percent = Math.round((progress.loaded / progress.total) * 100);
        option.onProgress({ percent });
      });
      option.onSuccess();
      this.onChangeValue(url, key);
    } catch (e) {
      option.onError();
    }
  }

  onChangeFilterValue = async (e, key) => {
    const value = e?.target ? e.target.value : e;
    await this.props.setState({ filter: { ...this.props.filter, [key]: value } });
    this.props.fetchCourses({ page: 1 });
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ course: { ...this.state.course, [key]: value } });
  }

  onChangeBundleValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ bundle: { ...this.state.bundle, [key]: value } });
  }

  onShowDrawer = async (id) => {
    const course = await this.props.fetchCourse(id);
    this.setState({ openCreate: true, course });
  }

  onShowBundleDrawer = async (id) => {
    const bundle = await this.props.getBundle(id);
    this.setState({ openCreateBundle: true, bundle });
  }

  onShowRedeenCodeDrawer = async (id, scope) => {
    const redeem = { scopeId: id, scope };
    const codes = await this.props.fetchRedeemCodes(redeem);
    this.setState({ openRedeenCode: true, redeemCodes: codes, redeem });
  }

  onCopy = async (id) => {
    const { prompts, ttsSettings } = await this.props.fetchCourse(id);
    this.setState({ openCreate: true, course: { prompts, ttsSettings } });
  }

  onAdd = () => {
    this.setState({
      openCreate: true,
      course: { ...DEFAULT_COURSE, type: 'course', prompts: this.state.defaultPrompts },
    });
  }

  onAddBundle = () => {
    this.setState({ openCreateBundle: true, bundle: { ...DEFAULT_BUNDLE } });
  }

  onSubmit = async () => {
    const { course } = this.state;
    if (_.isEmpty(course.name)) {
      Toast.show('课程名称不能为空', Toast.Type.WARNING);
      return;
    }

    if (_.isUndefined(course?.id)) {
      await this.props.createCourse({ ...course, prompts: this.state.defaultPrompts });
    } else {
      await this.props.updateCourse(course);
    }
    this.setState({ openCreate: false, course: {} });
    Toast.show('保存成功', Toast.Type.SUCCESS);
  }

  onSubmitBundle = async () => {
    const { bundle } = this.state;
    if (_.isEmpty(bundle.name)) {
      Toast.show('课程分组名称不能为空', Toast.Type.WARNING);
      return;
    }

    if (_.isUndefined(bundle?.id)) {
      await this.props.createBundle(bundle);
    } else {
      await this.props.updateBundle(bundle);
    }
    this.setState({ openCreateBundle: false, bundle: {} });
    Toast.show('保存成功', Toast.Type.SUCCESS);
  }

  renderCourseDrawer = () => {
    const { course, openCreate, tags } = this.state;
    return (
      <CourseDrawer
        open={openCreate}
        course={course}
        tags={tags}
        courseList={this.props.list}
        onChangeValue={(e, key) => { return this.onChangeValue(e, key); }}
        onUpload={(option, key) => { return this.onUpload(option, key); }}
        onSave={() => { return this.onSubmit(); }}
        onClose={() => { this.setState({ openCreate: false, course: {} }); }}
      />
    );
  }

  renderBundleDrawer = () => {
    const { bundle, openCreateBundle } = this.state;
    return (
      <BundleDrawer
        bundle={bundle}
        open={openCreateBundle}
        courses={this.props.courses}
        onChangeValue={(e, key) => { return this.onChangeBundleValue(e, key); }}
        onClose={() => { this.setState({ openCreateBundle: false, bundle: {} }); }}
        onSave={() => { return this.onSubmitBundle(); }}
      />
    );
  }

  renderBundleSelects = () => {
    const { onSale } = this.state;

    return (
      <Select
        style={{ width: 120, marginBottom: 16 }}
        allowClear
        value={onSale}
        onChange={(e) => { this.setState({ onSale: e }); }}
        placeholder="售卖状态"
        options={[
          { value: true, label: '售卖中' },
          { value: false, label: '未售卖' },
        ]}
      />
    );
  }

  renderCourseSelects = () => {
    const { bundleUuid } = this.state;

    return [
      <Select
        style={{ width: 120, marginBottom: 16 }}
        allowClear
        value={bundleUuid}
        onChange={(e) => { return this.onChangeFilterValue(e, 'uuid'); }}
        placeholder="课程分组"
        options={this.props.allBundles}
      />,
      <Divider type="vertical" />,
      <Select
        style={{ width: 120, marginBottom: 16 }}
        allowClear
        value={this.props.filter?.type}
        onChange={(e) => { return this.onChangeFilterValue(e, 'type'); }}
        placeholder="课程类型"
        options={_.map(Configs.COURSE_TYPE_MAP, (v, k) => { return { label: v, value: k }; })}
      />,
    ];
  }

  renderColumns = () => {
    const columns = [
      {
        title: 'ID',
        dataIndex: 'courseUuid',
        key: 'courseUuid',
        width: 240,
        render: (t, row) => {
          if (Configs.EDIT_DISABLED.includes(row.type)) {
            return (
              <Button
                style={{ padding: 0 }}
                type="link"
                onClick={async () => {
                  const domain = Platform.isProd() ? 'https://teacher.bzy.ai' : 'https://k12-test.bzy.ai';
                  const { partnerId } = Sessions.getProfile();
                  const url = `${domain}${Configs.REDIRECT_MAP[row.type]}${t}?pid=${partnerId}`;
                  await navigator.clipboard.writeText(url);
                  Toast.show('复制成功', Toast.Type.SUCCESS);
                }}
              >{t}
              </Button>
            );
          }

          return t;
        },
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        align: 'center',
        width: 100,
        render: (txt) => {
          const t = txt || 'course';
          return <Tag color={Configs.COURSE_COLORS_MAP[t]}>{Configs.COURSE_TYPE_MAP[t]}</Tag>;
        },
      },
      {
        title: '课程全称',
        dataIndex: 'name',
        key: 'name',
        ellipsis: true,
        align: 'center',
        render: (txt, row) => {
          const { id, courseUuid } = row;
          if (Configs.EDIT_DISABLED.includes(row.type)) {
            return <a onClick={() => { return this.onShowDrawer(id); }}>{txt}</a>;
          }

          return (
            <a onClick={() => { return this.$push(`/market-course-materials/${id}`, { courseId: courseUuid }); }}>
              {txt}
            </a>
          );
        },
      },
      { title: '名称简写', dataIndex: 'abbreviation', key: 'abbreviation', align: 'center' },
      // {
      //   title: 'Icon',
      //   dataIndex: 'icon',
      //   key: 'icon',
      //   align: 'center',
      //   render: (txt) => { return <Image width={50} src={txt} />; },
      // },
      // {
      //   title: 'Copilot头像',
      //   dataIndex: 'logo',
      //   key: 'logo',
      //   align: 'center',
      //   render: (txt) => { return <Image width={50} src={txt} />; },
      // },
      // { title: '描述', dataIndex: 'description', key: 'description', align: 'center' },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (txt) => { return moment(txt).format('YYYY-MM-DD HH:mm:ss'); },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        render: (txt, row) => {
          return (
            <>
              <a onClick={() => { return this.onShowDrawer(row.id); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm title="确定删除吗？" onConfirm={() => { return this.props.deleteCourse(row.id); }}>
                <a>删除</a>
              </Popconfirm>
              <Divider type="vertical" />
              <a onClick={() => { return this.onShowRedeenCodeDrawer(row.courseUuid, 'course'); }}>
                兑换码
              </a>
              <Divider type="vertical" />
              <a onClick={() => { return this.onCopy(row.id); }}>
                复用
              </a>
              <Divider type="vertical" />
              <a onClick={() => { return this.setState({ openFunc: true, course: row }); }}>
                存函数
              </a>
            </>
          );
        },
      },
    ];

    if (!_.isUndefined(this.props.filter?.uuid)) {
      columns.unshift({
        title: '排序',
        dataIndex: 'order',
        key: 'order',
        width: 80,
        render: (txt, row) => {
          return (
            <InputNumber
              min={0}
              style={{ width: 60 }}
              value={txt}
              onChange={(e) => { return this.props.updateCourse({ ...row, order: e }, false); }}
            />
          );
        },
      });
    }

    return columns;
  }

  renderBundleColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id' },
      {
        title: '分组名称',
        dataIndex: 'name',
        key: 'name',
        ellipsis: true,
        align: 'center',
      },
      {
        title: '价格（元）',
        dataIndex: 'price',
        key: 'price',
        align: 'center',
      },
      {
        title: '售卖状态',
        dataIndex: 'onSale',
        key: 'onSale',
        align: 'center',
        render: (txt, item) => {
          return (<Switch
            checkedChildren="售卖中"
            unCheckedChildren="未售卖"
            checked={txt}
            onChange={async (e) => {
              await this.setState({ bundle: { ...item, onSale: e } });
              this.onSubmitBundle();
            }}
          />);
        },
      },
      {
        title: '兑换网址',
        dataIndex: 'redeemUrl',
        key: 'redeemUrl',
        align: 'center',
        render: (txt) => {
          return (
            <>
              {txt}&nbsp;
              <CopyOutlined
                onClick={() => { navigator.clipboard.writeText(txt); Toast.show('复制成功', Toast.Type.SUCCESS); }}
              />
            </>
          );
        },
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (txt) => { return moment(txt).format('YYYY-MM-DD HH:mm:ss'); },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        render: (txt, row) => {
          return (
            <>
              <a onClick={async () => {
                await this.setState({ activeKey: 'courses', bundleUuid: row.uuid });
                this.props.fetchCourses({
                  uuid: row.uuid,
                  page: 1,
                });
              }}
              >
                查看课程
              </a>
              <Divider type="vertical" />
              <a onClick={() => { return this.onShowBundleDrawer(row.id); }}>编辑</a>
              <Divider type="vertical" />
              <a onClick={() => { return this.onShowRedeenCodeDrawer(row.uuid, 'bundle'); }}>
                兑换码
              </a>
              <Divider type="vertical" />
              <Popconfirm title="确定删除吗？" onConfirm={() => { return this.props.deleteBundle(row.id); }}>
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  render = () => {
    const { activeKey, keywords, tags } = this.state;
    return (
      <div style={{ padding: 30, background: '#fff' }}>
        <Tabs
          activeKey={activeKey}
          tabBarExtraContent={<Button onClick={() => { return this.setState({ openTag: true }); }}>课程标签</Button>}
          onChange={(e) => {
            this.$replace(this.props.location.pathname, { activeKey: e });
            this.setState({ activeKey: e });
          }}
        >
          <Tabs.TabPane tab="课程分组" key="bundles">
            <FilterBar
              canAdd
              placeholder="请输入分组名称"
              searchKeyWords={keywords}
              onChange={(value) => { this.setState({ keywords: value }); }}
              onAdd={() => { return this.onAddBundle(); }}
              onSearch={() => {
                this.props.fetchBundles({
                  name: keywords,
                  onSale: this.state.onSale,
                  page: 1,
                });
              }}
              renderSelects={this.renderBundleSelects}
            />
            <PaginationTable
              columns={this.renderBundleColumns()}
              dataSource={this.props.bundles}
              pagination={this.props.bundlePagination}
              totalDataCount={this.props.bundlesTotal}
              onPaginationChange={this.props.fetchBundles}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="课程列表" key="courses">
            <FilterBar
              canAdd
              onAdd={() => { return this.onAdd(); }}
              placeholder="请输入课程名称"
              searchKeyWords={this.props.filter?.name}
              onChange={(value) => { return this.onChangeFilterValue(value, 'name'); }}
              onSearch={() => { this.props.fetchCourses({ page: 1 }); }}
              renderSelects={this.renderCourseSelects}
            />
            <PaginationTable
              columns={this.renderColumns()}
              dataSource={this.props.list}
              pagination={this.props.pagination}
              totalDataCount={this.props.total}
              onPaginationChange={this.props.fetchCourses}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="直播间" key="room">
            <LiveRoomTabPane
              list={this.props.rooms}
              total={this.props.roomTotal}
              filter={this.props.roomFilter}
              pagination={this.props.roomPagination}
              onChange={this.props.setState}
              fetchRooms={this.props.fetchRooms}
              deleteRoom={this.props.deleteRoom}
              createRoom={this.props.createRoom}
              updateRoom={this.props.updateRoom}
            />
          </Tabs.TabPane>
        </Tabs>

        {this.state.openCreate && this.renderCourseDrawer()}
        {this.state.openCreateBundle && this.renderBundleDrawer()}
        {this.state.openTag &&
          <TagDrawer
            open={this.state.openTag}
            tags={tags}
            onSave={(e) => { return this.onSaveTags(e); }}
            onClose={() => { this.setState({ openTag: false }); }}
          />
        }
        {
          this.state.openRedeenCode &&
          <RedeemCodeDrawer
            open={this.state.openRedeenCode}
            data={this.state.redeem}
            codes={this.state.redeemCodes}
            onGenCode={this.props.generateRedeemCodes}
            onClose={() => { this.setState({ openRedeenCode: false, redeemCodes: [], redeem: {} }); }}
          />
        }
        {
          this.state.openFunc &&
          <FuncDrawer
            open={this.state.openFunc}
            course={this.state.course}
            onSave={this.props.addCourseFunc}
            onClose={() => { this.setState({ openFunc: false }); }}
          />
        }
      </div>
    );
  }
}

export {
  reducer,
};
