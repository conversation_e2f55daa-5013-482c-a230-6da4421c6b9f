import { DeleteFilled, PlusCircleFilled } from '@ant-design/icons';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { InputUpload, Toast } from '~/components';
import { <PERSON>yunHelper, OpenAI, Sessions } from '~/engine';
import { Platform } from '~/plugins';
import { Button, Checkbox, Divider, Drawer, Form, Input, Popconfirm, Select, Spin, Modal } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import Markdown from 'react-markdown';
import ReactMde from 'react-mde';

import Configs from '../../../const';

export default class ContentDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    courseId: PropTypes.any,
    type: PropTypes.string,
    paragraph: PropTypes.object,
    previewTabs: PropTypes.object,
    onSave: PropTypes.func,
    onRefresh: PropTypes.func,
    onProcessAll: PropTypes.func,
    onClose: PropTypes.func,
    onShowTimeline: PropTypes.func,
    setState: PropTypes.func,
    fetchVideos: PropTypes.func,
    fetchMergeRecords: PropTypes.func,
    getCategoriesTree: PropTypes.func,
  }

  state = {
    spinning: false,
    typeObj: {},
    subTypeObj: {},
    areas: [],
    years: [],
    videoModalVisible: false,
    categories: [],
    videos: [],
    mergeRecords: [],
    selectedCategory: undefined,
    selectedVideo: undefined,
    currentIdx: null,
  }
  componentDidMount = () => {
    const isExercise = this.props.type === 'exercise';
    let typeObj = _.cloneDeep(Configs.TYPE_MAP);
    let subTypeObj = isExercise ? _.cloneDeep(Configs.SUB_TYPE_MAP) : { general_qa: '通用习题', essay: '作文题' };

    if (isExercise) {
      delete subTypeObj.general_qa;
      delete typeObj.knowledge;
    }
    if (this.props.type === 'follow') {
      typeObj = { follow: '跟读' };
      subTypeObj = {};
    }

    this.initEssayData();
    this.setState({ typeObj, subTypeObj });
  }

  initEssayData = async () => { // eslint-disable-next-line
    const resp = await fetch(`https://video-clip.oss-cn-shanghai.aliyuncs.com/fe_data/essay-params.json?v=${Math.random()}`);
    const data = await resp.json();
    this.setState(data);
  }

  fetchCategories = async () => {
    try {
      const data = await this.props.getCategoriesTree();
      this.setState({ categories: data || [] });
    } catch (error) {
      Toast.show('获取分类失败', Toast.Type.ERROR);
    }
  }

  onFormatContent = async (item, idx) => {
    const content = item?.content || '';
    if (_.isEmpty(content)) {
      Toast.show('内容不能为空', Toast.Type.WARNING);
      return;
    }
    await this.setState({ spinning: true });
    this.props.setState({ previewTabs: { ...this.props.previewTabs, [item.id]: 'preview' } });
    let msg = '';
    const ctrl = new AbortController();
    const url = Platform.sseUrl(OpenAI.FORMAT_PARAGRAPHS_TIMELINES_CONTENT, { sse_mode: 1 });
    const body = JSON.stringify({ course_id: this.props.courseId, paragraph_id: item.id });
    fetchEventSource(url, {
      method: 'POST',
      openWhenHidden: false,
      headers: { 'Content-Type': 'application/json', 'grpc-metadata-token': Sessions.getToken() },
      body,
      signal: ctrl.signal,
      onmessage: async (e) => {
        const obj = JSON.parse(_.isEmpty(e.data) ? '{}' : e.data);
        const paragraph = _.cloneDeep(this.props.paragraph);
        if (obj) {
          msg += obj.token || '';
          paragraph.items[idx] = { ...paragraph.items[idx], content: msg };
          this.props.setState({ paragraph });
        }
      },
      onclose: async () => {
        this.props.setState({ previewTabs: { ...this.props.previewTabs, [item.id]: 'write' } });
        await this.onChangeParagraphContent(msg, idx);
        this.setState({ spinning: false });
      },
    });
  }

  onChangeParagraphValue = (e, key, index, subKey) => {
    const value = e?.target ? e?.target.value : e;
    const paragraph = _.cloneDeep(this.props.paragraph);
    if (subKey) {
      if (!paragraph.items[index][key]) {
        paragraph.items[index][key] = {};
      }
      paragraph.items[index][key][subKey] = value;
    } else {
      paragraph.items[index][key] = value;
    }
    this.props.setState({ paragraph });
  }

  onChangeParagraphContent = (e, idx, isFillin) => {
    const paragraph = _.cloneDeep(this.props.paragraph);
    paragraph.items[idx].content = e;
    const pattern = isFillin ? /_ _ [\u2460-\u32BF] _ _/g : /「(.*?)」[\u2460-\u32BF]/g;
    const matches = (e || '').match(pattern);
    paragraph.items[idx].noteCount = matches?.length || 0;
    this.props.setState({ paragraph });
  }

  onExecuteNoteCommand = async (e, idx) => {
    if (_.isEmpty(e.initialState.selectedText)) return;
    const item = _.cloneDeep(this.props.paragraph.items[idx]);
    const modifyText = `「${e.initialState.selectedText}」${Configs.decimal[item.noteCount + 1]}`;
    await e.textApi.replaceSelection(modifyText);

    const text = `${Configs.decimal[item.noteCount + 1]}: `;

    const contentNotes = item.contentNotes ? `${item.contentNotes}\n${text}` : text;
    await this.onChangeParagraphValue(contentNotes, 'contentNotes', idx);
  }

  onExecuteFillinCommand = async (e, idx) => {
    const item = _.cloneDeep(this.props.paragraph.items[idx]);
    const modifyText = ` _ _ ${Configs.decimal[item.noteCount + 1]} _ _ `;
    await e.textApi.replaceSelection(modifyText);

    const text = `${Configs.decimal[item.noteCount + 1]}: `;
    const contentNotes = item.contentNotes ? `${item.contentNotes}\n${text}` : text;
    await this.onChangeParagraphValue(contentNotes, 'contentNotes', idx);
  }

  onAddParagraph = (index) => {
    const paragraph = _.cloneDeep(this.props.paragraph);
    paragraph.items.splice(index + 1, 0, { contentType: _.head(_.keys(this.state.typeObj)) });
    this.props.setState({ paragraph });
  }

  onDelParagraph = (index) => {
    const paragraph = _.cloneDeep(this.props.paragraph);
    paragraph.items.splice(index, 1);
    this.props.setState({ paragraph });
  }

  onChangeChoiceValue = (e, type, idx, oIdx) => {
    const value = e?.target ? e?.target.value : e;
    const paragraph = _.cloneDeep(this.props.paragraph);
    const answer = JSON.parse(paragraph.items[idx].answer || '[{},{},{},{}]');
    answer[oIdx][type] = value;
    paragraph.items[idx].answer = JSON.stringify(answer);
    this.props.setState({ paragraph });
  }

  onSelectAiVideo = (item, idx) => {
    this.setState({
      videoModalVisible: true,
      currentIdx: idx,
    });
    this.fetchCategories();
  }

  onCategoryChange = async (value) => {
    this.setState({ selectedCategory: value, videos: [], selectedVideo: undefined });
    try {
      const data = await this.props.fetchVideos({ category_id: value });
      this.setState({ videos: data?.items || [] });
    } catch (error) {
      Toast.show('获取视频列表失败', Toast.Type.ERROR);
    }
  }

  onVideoSelect = async (value) => {
    this.setState({ selectedVideo: value, mergeRecords: [] });
    try {
      const data = await this.props.fetchMergeRecords(value);
      this.setState({ mergeRecords: data?.items || [] });
    } catch (error) {
      Toast.show('获取合成记录失败', Toast.Type.ERROR);
    }
  }

  renderImageContent = (item, idx) => {
    return (
      <Form.Item label="图片">
        <div style={{ display: 'flex', width: '100%' }}>
          <div style={{ width: '80%' }}>
            <InputUpload
              url={item?.content}
              accept="image/*"
              onChange={(e) => { return this.onChangeParagraphValue(e, 'content', idx); }}
            />
          </div>
          <Divider type="vertical" />
          <Button
            disabled={_.isEmpty(item?.content)}
            onClick={() => { window.open(item?.content); }}
          >
            预览
          </Button>
        </div>
      </Form.Item>
    );
  }

  renderTextContent = (item, idx) => {
    return (
      <Form.Item label="文本">
        <ReactMde
          value={item?.content || ''}
          selectedTab={this.props.previewTabs[item.id] || 'write'}
          onTabChange={(e) => {
            return this.props.setState({ previewTabs: { ...this.props.previewTabs, [item.id]: e } });
          }}
          onChange={(e) => { return this.onChangeParagraphContent(e, idx, true); }}
          toolbarCommands={[_.dropRight(...Configs.TOOLBAR_COMMANDS)]}
          generateMarkdownPreview={(e) => { return Promise.resolve(<Markdown>{e}</Markdown>); }}
          childProps={{ writeButton: { tabIndex: -1 } }}
        />
      </Form.Item>
    );
  }

  renderFollowContent = (item, idx) => {
    return (
      <Form.Item label="文本">
        <ReactMde
          value={item?.content || ''}
          selectedTab={this.props.previewTabs[item.id] || 'write'}
          onTabChange={(e) => {
            return this.props.setState({ previewTabs: { ...this.props.previewTabs, [item.id]: e } });
          }}
          onChange={(e) => { return this.onChangeParagraphContent(e, idx, true); }}
          toolbarCommands={[_.dropRight(...Configs.TOOLBAR_COMMANDS)]}
          generateMarkdownPreview={(e) => { return Promise.resolve(<Markdown>{e}</Markdown>); }}
          childProps={{ writeButton: { tabIndex: -1 } }}
        />
      </Form.Item>
    );
  }

  renderKnowledgeContent = (item, idx) => {
    return (
      <>
        <Form.Item label={Configs.TYPE_MAP[item?.contentType]}>
          <div style={{ position: 'relative' }}>
            {
              !(item?.formatted) &&
              <Button
                type="primary"
                style={{ position: 'absolute', right: 7, top: 7 }}
                onClick={() => { return this.onFormatContent(item, idx); }}
              >
                格式化
              </Button>
            }
            <ReactMde
              value={item?.content || ''}
              selectedTab={this.props.previewTabs[item.id] || 'write'}
              onTabChange={(e) => {
                return this.props.setState({ previewTabs: { ...this.props.previewTabs, [item.id]: e } });
              }}
              onChange={(e) => { return this.onChangeParagraphContent(e, idx); }}
              commands={{
                note: {
                  icon: () => { return <>注释</>; },
                  execute: (e) => { return this.onExecuteNoteCommand(e, idx); },
                },
              }}
              paste={{
                saveImage: async function* asyncGenerator(data, blob) {
                  const url = await AliyunHelper.uploadImageBlob(blob);
                  yield url;
                  return true;
                },
              }}
              toolbarCommands={Configs.TOOLBAR_COMMANDS}
              generateMarkdownPreview={(e) => { return Promise.resolve(<Markdown>{e}</Markdown>); }}
              childProps={{ writeButton: { tabIndex: -1 } }}
            />
            <span
              style={{
                position: 'absolute', right: 5, bottom: 5, color: item?.content?.length > 2000 ? 'red' : '#000',
              }}
            >
              <b>{item?.content?.length}</b>字
            </span>
            <span style={{ position: 'absolute', right: 150, bottom: 5 }}>建议单知识点不超过<b>2000</b>字。</span>
          </div>
        </Form.Item>
        <Form.Item label="注释">
          <Input.TextArea
            autoSize={{ minRows: 2, maxRows: 5 }}
            value={item?.contentNotes}
            onChange={(e) => { return this.onChangeParagraphValue(e, 'contentNotes', idx); }}
          />
        </Form.Item>
        {
          item?.contentType === 'exercise' &&
          <Form.Item label="答案">
            <Input.TextArea
              autoSize={{ minRows: 2, maxRows: 5 }}
              value={item?.answer}
              onChange={(e) => { return this.onChangeParagraphValue(e, 'answer', idx); }}
            />
          </Form.Item>
        }
        <Form.Item label="音频">
          <Button
            type="primary"
            danger={item.jobStatus === 'failed'}
            loading={item.jobStatus === 'running'}
            onClick={() => { return this.props.onShowTimeline(item.id, idx); }}
          >查看音频稿
          </Button>
        </Form.Item>
      </>
    );
  }

  renderChoiceContent = (item, idx) => {
    let options = [{}, {}, {}, {}];
    try {
      options = JSON.parse(item?.answer || '[{},{},{},{}]');
    } catch (error) {
      // do nothing
    }

    const correctIdx = _.findIndex(options, (x) => { return x.correct; });
    return (
      <>
        <Form.Item label={Configs.TYPE_MAP[item?.contentType]}>
          <ReactMde
            value={item?.content || ''}
            selectedTab={this.props.previewTabs[item.id] || 'write'}
            onTabChange={(e) => {
              return this.props.setState({ previewTabs: { ...this.props.previewTabs, [item.id]: e } });
            }}
            onChange={(e) => { return this.onChangeParagraphContent(e, idx, true); }}
            paste={{
              saveImage: async function* asyncGenerator(data, blob) {
                const url = await AliyunHelper.uploadImageBlob(blob);
                yield url;
                return true;
              },
            }}
            toolbarCommands={[_.dropRight(...Configs.TOOLBAR_COMMANDS)]}
            generateMarkdownPreview={(e) => { return Promise.resolve(<Markdown>{e}</Markdown>); }}
            childProps={{ writeButton: { tabIndex: -1 } }}
          />
        </Form.Item>
        <Form.Item label="选项">
          {
            _.map(options, (x, i) => {
              return (
                <div style={{ display: 'flex', marginBottom: 10, alignItems: 'center' }}>
                  {
                    !x.isImg ?
                      <Input
                        value={x.content}
                        style={{ width: '80%' }}
                        addonBefore={String.fromCharCode(65 + i)}
                        onChange={(e) => { return this.onChangeChoiceValue(e, 'content', idx, i); }}
                      /> :
                      <InputUpload
                        style={{ width: '80%' }}
                        url={x?.imgUrl}
                        accept="image/*"
                        addonBefore={String.fromCharCode(65 + i)}
                        onChange={(e) => { return this.onChangeChoiceValue(e, 'imgUrl', idx, i); }}
                      />
                  }
                  <Divider type="vertical" />
                  <Checkbox
                    checked={x.correct}
                    onChange={(e) => { return this.onChangeChoiceValue(e.target.checked, 'correct', idx, i); }}
                  >正确答案
                  </Checkbox>
                  <Checkbox
                    checked={x.isImg}
                    onChange={(e) => { return this.onChangeChoiceValue(e.target.checked, 'isImg', idx, i); }}
                  >图片
                  </Checkbox>
                </div>
              );
            })
          }
        </Form.Item>
        {
          correctIdx >= 0 &&
          <Form.Item label="讲解">
            {/* <Input.TextArea
              autoSize={{ minRows: 5, maxRows: 10 }}
              value={options[correctIdx]?.note}
              onChange={(e) => { return this.onChangeChoiceValue(e, 'note', idx, correctIdx); }}
            /> */}
            <ReactMde
              value={options[correctIdx]?.note}
              onChange={(e) => { return this.onChangeChoiceValue(e, 'note', idx, correctIdx); }}
              paste={{
                saveImage: async function* asyncGenerator(data, blob) {
                  const url = await AliyunHelper.uploadImageBlob(blob);
                  yield url;
                  return true;
                },
              }}
              toolbarCommands={[_.dropRight(...Configs.TOOLBAR_COMMANDS)]}
              childProps={{ writeButton: { tabIndex: -1 } }}
            />
          </Form.Item>
        }
      </>
    );
  }

  renderFillinContent = (item, idx) => {
    return (
      <>
        <Form.Item label={Configs.TYPE_MAP[item?.contentType]}>
          <ReactMde
            id={`fillin-${idx}`}
            key={`fillin-${idx}`}
            value={item?.content || ''}
            selectedTab={this.props.previewTabs[item.id] || 'write'}
            onTabChange={(e) => {
              return this.props.setState({ previewTabs: { ...this.props.previewTabs, [item.id]: e } });
            }}
            onChange={(e) => { return this.onChangeParagraphContent(e, idx, true); }}
            commands={{
              note: {
                icon: () => { return <>填空</>; },
                execute: (e) => { return this.onExecuteFillinCommand(e, idx); },
              },
              recognition: {
                icon: () => { return <>一键识别</>; },
                execute: async (e) => {
                  const itemA = _.cloneDeep(this.props.paragraph.items[idx]);
                  const text = e.initialState.text;
                  const regex = /_{4,}/g;
                  let match;
                  let noteCount = itemA?.noteCount || 0;
                  const matches = [];

                  while ((match = regex.exec(text)) !== null) {
                    matches.push({
                      start: match.index,
                      end: match.index + match[0].length,
                      original: match[0],
                    });
                  }

                  const replacements = matches.map((match) => {
                    noteCount++;
                    return {
                      ...match,
                      replacement: ` _ _ ${Configs.decimal[noteCount]} _ _ `,
                      note: `${Configs.decimal[noteCount]}: `,
                    };
                  });

                  let newText = text;
                  const notes = [];
                  for (let i = replacements.length - 1; i >= 0; i--) {
                    const { start, end, replacement, note } = replacements[i];
                    newText = newText.substring(0, start) + replacement + newText.substring(end);
                    notes.unshift(note);
                  }

                  if (noteCount > 0) {
                    await e.textApi.replaceSelection(newText, {
                      start: 0,
                      end: text.length,
                    });

                    this.onChangeParagraphContent(newText, idx, true);

                    const contentNotes = itemA.contentNotes
                      ? `${itemA.contentNotes}\n${notes.join('\n')}`
                      : notes.join('\n');
                    this.onChangeParagraphValue(contentNotes, 'contentNotes', idx);
                  }
                },
              },
            }}
            paste={{
              saveImage: async function* asyncGenerator(data, blob) {
                const url = await AliyunHelper.uploadImageBlob(blob);
                yield url;
                return true;
              },
            }}
            toolbarCommands={Configs.TOOLBAR_COMMANDS}
            generateMarkdownPreview={(e) => { return Promise.resolve(<Markdown>{e}</Markdown>); }}
            childProps={{ writeButton: { tabIndex: -1 } }}
          />
        </Form.Item>
        <Form.Item label="答案">
          <Input.TextArea
            autoSize={{ minRows: 5, maxRows: 10 }}
            value={item?.contentNotes}
            onChange={(e) => { return this.onChangeParagraphValue(e, 'contentNotes', idx); }}
          />
        </Form.Item>
      </>
    );
  }

  renderQnaContent = (item, idx) => {
    return (
      <>
        <Form.Item label={Configs.TYPE_MAP[item?.contentType]}>
          <ReactMde
            value={item?.content || ''}
            selectedTab={this.props.previewTabs[item.id] || 'write'}
            onTabChange={(e) => {
              return this.props.setState({ previewTabs: { ...this.props.previewTabs, [item.id]: e } });
            }}
            onChange={(e) => { return this.onChangeParagraphContent(e, idx, true); }}
            paste={{
              saveImage: async function* asyncGenerator(data, blob) {
                const url = await AliyunHelper.uploadImageBlob(blob);
                yield url;
                return true;
              },
            }}
            toolbarCommands={[_.dropRight(...Configs.TOOLBAR_COMMANDS)]}
            generateMarkdownPreview={(e) => { return Promise.resolve(<Markdown>{e}</Markdown>); }}
            childProps={{ writeButton: { tabIndex: -1 } }}
          />
        </Form.Item>
        <Form.Item label="答案">
          <Input.TextArea
            autoSize={{ minRows: 2, maxRows: 5 }}
            value={item?.contentNotes}
            onChange={(e) => { return this.onChangeParagraphValue(e, 'contentNotes', idx); }}
          />
        </Form.Item>
      </>
    );
  }

  renderEssayContent = (item, idx) => {
    return (
      <>
        <Form.Item label={Configs.TYPE_MAP[item?.contentType]}>
          <ReactMde
            value={item?.content || ''}
            selectedTab={this.props.previewTabs[item.id] || 'write'}
            onTabChange={(e) => {
              return this.props.setState({ previewTabs: { ...this.props.previewTabs, [item.id]: e } });
            }}
            onChange={(e) => { return this.onChangeParagraphContent(e, idx, true); }}
            paste={{
              saveImage: async function* asyncGenerator(data, blob) {
                const url = await AliyunHelper.uploadImageBlob(blob);
                yield url;
                return true;
              },
            }}
            toolbarCommands={[_.dropRight(...Configs.TOOLBAR_COMMANDS)]}
            generateMarkdownPreview={(e) => { return Promise.resolve(<Markdown>{e}</Markdown>); }}
            childProps={{ writeButton: { tabIndex: -1 } }}
          />
        </Form.Item>
        <Form.Item label="标签">
          <Select
            mode="tags"
            style={{ width: '100%' }}
            open={false}
            value={item?.tags}
            onChange={(e) => { return this.onChangeParagraphValue(e, 'tags', idx); }}
          />
        </Form.Item>
        <Form.Item label="适用区域">
          <Select
            showSearch
            value={item?.region}
            style={{ width: 200 }}
            filterOption={(input, option) => { return option?.value?.includes(input); }}
            onChange={(e) => { return this.onChangeParagraphValue(e, 'region', idx); }}
            options={_.map(this.state.areas, (v) => { return { value: v, label: v }; })}
          />
        </Form.Item>
        <Form.Item label="出题年份">
          <Select
            showSearch
            value={item?.publishYear}
            style={{ width: 200 }}
            filterOption={(input, option) => { return option?.value?.includes(input); }}
            onChange={(e) => { return this.onChangeParagraphValue(e, 'publishYear', idx); }}
            options={_.map(this.state.years, (v) => { return { value: v, label: v }; })}
          />
        </Form.Item>
      </>
    );
  }

  renderVideoContent = (item, idx) => {
    return (
      <>
        <Form.Item label="视频">
          <div style={{ display: 'flex', width: '100%' }}>
            <div style={{ width: '80%' }}>
              <InputUpload
                url={item?.extra?.videoUrl}
                accept="video/*"
                onChange={(e) => { return this.onChangeParagraphValue(e, 'extra', idx, 'videoUrl'); }}
              />
            </div>
            <Divider type="vertical" />
            <Button
              disabled={_.isEmpty(item?.extra?.videoUrl)}
              onClick={() => { window.open(item?.extra?.videoUrl); }}
            >
              预览
            </Button>
          </div>
        </Form.Item>
        <Form.Item label="AI视频">
          <Button
            type="primary"
            onClick={() => {
              this.onSelectAiVideo(item, idx);
            }}
          >
            选择视频
          </Button>
        </Form.Item>

        <Form.Item label="内容介绍">
          <div style={{ position: 'relative' }}>
            <ReactMde
              value={item?.extra?.content || ''}
              selectedTab={this.props.previewTabs[item.id] || 'write'}
              onTabChange={(e) => {
                return this.props.setState({ previewTabs: { ...this.props.previewTabs, [item.id]: e } });
              }}
              onChange={(e) => { return this.onChangeParagraphValue(e, 'extra', idx, 'content'); }}
              paste={{
                saveImage: async function* asyncGenerator(data, blob) {
                  const url = await AliyunHelper.uploadImageBlob(blob);
                  yield url;
                  return true;
                },
              }}
              toolbarCommands={[_.dropRight(...Configs.TOOLBAR_COMMANDS)]}
              generateMarkdownPreview={(e) => { return Promise.resolve(<Markdown>{e}</Markdown>); }}
              childProps={{ writeButton: { tabIndex: -1 } }}
            />
          </div>
        </Form.Item>
      </>
    );
  }

  renderContentByType = (item, idx) => {
    let content = null;
    let type = item?.contentType;
    if (type !== 'video' && (!_.isUndefined(item.subType) && !_.isEmpty(item.subType))) {
      type = item?.subType;
    }

    switch (type) {
      case 'knowledge':
      case 'general_qa':
      case 'follow':
        content = this.renderKnowledgeContent(item, idx);
        break;
      case 'image':
        content = this.renderImageContent(item, idx);
        break;
      case 'text':
        content = this.renderTextContent(item, idx);
        break;
      case 'choice':
        content = this.renderChoiceContent(item, idx);
        break;
      case 'fillin':
        content = this.renderFillinContent(item, idx);
        break;
      case 'qna':
        content = this.renderQnaContent(item, idx);
        break;
      case 'essay':
        content = this.renderEssayContent(item, idx);
        break;
      case 'video':
        content = this.renderVideoContent(item, idx);
        break;
      default:
        break;
    }

    return content;
  }

  renderContentItem = (item, idx) => {
    const isMaterial = item?.contentType === 'material';

    return (
      <div style={{ marginBottom: 10 }}>
        <Form labelCol={{ span: 2 }}>
          <Form.Item label="类型">
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>
                <Select
                  value={item?.contentType}
                  style={{ width: 200 }}
                  onChange={(e) => {
                    this.onChangeParagraphValue(e, 'contentType', idx);
                  }}
                >
                  {_.map(this.state.typeObj, (v, k) => {
                    return (<Select.Option value={k}>{v}</Select.Option>);
                  })}
                </Select>
                {
                  item?.contentType === 'exercise' &&
                  <>
                    <Divider type="vertical" />
                    <Select
                      value={item?.subType}
                      style={{ width: 200 }}
                      onChange={(e) => { return this.onChangeParagraphValue(e, 'subType', idx); }}
                    >
                      {
                        _.map(this.state.subTypeObj, (v, k) => {
                          return (<Select.Option value={k} >{v}</Select.Option>);
                        })
                      }
                    </Select>
                  </>
                }
                {
                  item?.contentType !== 'video' &&
                  <>
                    <Divider type="vertical" />
                    {
                      !isMaterial &&
                      <Checkbox
                        value={item?.hasFormula}
                        onChange={(e) => { return this.onChangeParagraphValue(e, 'hasFormula', idx); }}
                      >
                        含有公式
                      </Checkbox>
                    }
                  </>
                }
              </div>
              <div>
                <Button
                  type="primary"
                  icon={<PlusCircleFilled />}
                  onClick={() => { return this.onAddParagraph(idx); }}
                />
                <Divider type="vertical" />
                <Button
                  type="primary"
                  danger
                  icon={<DeleteFilled />}
                  onClick={() => { return this.onDelParagraph(idx); }}
                />
              </div>
            </div>
          </Form.Item>
          {this.renderContentByType(item, idx)}
        </Form>
        <Divider />
      </div>
    );
  }

  render = () => {
    const { paragraph, open } = this.props;

    return (
      <>
        <Drawer
          width="72vw"
          open={open}
          title={paragraph.title}
          onClose={this.props.onClose}
        >
          <div style={{ position: 'relative' }}>
            <Spin
              spinning={this.state.spinning}
              tip="格式化执行中, 请停留在当前页面!"
            >
              {_.map(paragraph.items, (x, idx) => {
                return this.renderContentItem(x, idx);
              })}
            </Spin>
            <div
              style={{
                position: 'fixed',
                bottom: 0,
                width: '72vw',
                height: 50,
                background: '#fff',
                textAlign: 'center',
              }}
            >
              <Button
                type="primary"
                onClick={this.props.onSave}
              >
                保存
              </Button>
              <Button
                style={{ float: 'left' }}
                onClick={this.props.onRefresh}
              >
                刷新
              </Button>
              <Popconfirm
                title="同时生成所有音频稿，跳过已经生成过音频稿的知识点"
                onConfirm={() => {
                  return this.props.onProcessAll({
                    chapterId: paragraph.chapterId,
                    state: 'script_and_audio',
                    skipDone: true,
                  });
                }}
              >
                <Button
                  style={{ float: 'left', marginLeft: 30 }}
                >
                  全部生成
                </Button>
              </Popconfirm>
            </div>
          </div>
        </Drawer>
        <Modal
          title="选择AI视频"
          open={this.state.videoModalVisible}
          onCancel={() => {
            this.setState({ videoModalVisible: false });
          }}
          footer={null}
          width={800}
        >
          <div
            style={{
              marginBottom: 16,
            }}
          >
            <Select
              style={{ width: '100%' }}
              placeholder="请选择分类"
              value={this.state.selectedCategory}
              onChange={this.onCategoryChange}
            >
              {_.map(this.state.categories, (item) => {
                return (
                  <Select.Option value={item.id}>
                    {item.name}
                  </Select.Option>
                );
              })}
            </Select>
          </div>
          {
            this.state.selectedCategory &&
            <div style={{ marginBottom: 16 }}>
              <Select
                style={{ width: '100%' }}
                placeholder="请选择视频"
                value={this.state.selectedVideo}
                onChange={this.onVideoSelect}
              >
                {_.map(this.state.videos, (item) => {
                  return <Select.Option value={item.id}>{item.title}</Select.Option>;
                })}
              </Select>
            </div>
          }
          {
            this.state.mergeRecords.length > 0 &&
            <div>
              <div style={{ marginBottom: 8, fontWeight: 'bold' }}>合成记录：</div>
              {
                _.map(this.state.mergeRecords, (record, index) => {
                  return (
                    <div
                      key={index}
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        padding: '8px',
                        borderBottom: '1px solid #f0f0f0',
                      }}
                    >
                      <div>
                        <div>版本 {index + 1}</div>
                        <div style={{ fontSize: 12, color: '#999' }}>
                          {new Date(record.createdAt).toLocaleString()}
                        </div>
                      </div>
                      <div>
                        <Button
                          type="link"
                          onClick={() => {
                            window.open(record.videoUrl);
                          }}
                        >
                          预览
                        </Button>
                        <Button
                          type="primary"
                          onClick={() => {
                            this.onChangeParagraphValue(record.videoUrl, 'extra', this.state.currentIdx, 'videoUrl');
                            this.setState({ videoModalVisible: false });
                          }}
                        >
                          选择
                        </Button>
                      </div>
                    </div>
                  );
                })
              }
            </div>
          }
        </Modal>
      </>
    );
  }
}
