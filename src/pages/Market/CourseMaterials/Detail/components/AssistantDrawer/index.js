import { DeleteFilled, PlusCircleFilled } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer, Form, Input, InputNumber, Select, Tabs } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import Configs from '../../../const';

export default class AssistantDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    prompts: PropTypes.object,
    contentType: PropTypes.string,
    onSave: PropTypes.func,
    onClose: PropTypes.func,
    onChange: PropTypes.func,
  }

  onChangeAssistantValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const prompts = _.cloneDeep(this.props.prompts);
    prompts[key] = value;
    this.props.onChange({ prompts });
  }

  renderAssistant = (data = {}, key) => {
    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label="模型">
          <Select
            style={{ width: 200 }}
            value={data?.model}
            onChange={(e) => { return this.onChangeAssistantValue({ ...data, model: e }, key); }}
          >
            <Select.Option value="gpt-3.5-turbo">gpt-3.5</Select.Option>
            <Select.Option value="gpt-4-turbo-preview">gpt-4</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label="温度">
          <InputNumber
            value={data?.temperature}
            step={0.1}
            min={0}
            max={1}
            onChange={(e) => { return this.onChangeAssistantValue({ ...data, temperature: e }, key); }}
          />
        </Form.Item>
        <Form.Item label="响应类型">
          <Select
            style={{ width: 200 }}
            value={data?.responseFormat}
            onChange={(e) => { return this.onChangeAssistantValue({ ...data, responseFormat: e }, key); }}
          >
            <Select.Option value="json_object">JSON</Select.Option>
            <Select.Option value="text">文本</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label="系统提示">
          <Input.TextArea
            autoSize={{ minRows: 3, maxRows: 10 }}
            value={data?.systemPrompt}
            onChange={(e) => { return this.onChangeAssistantValue({ ...data, systemPrompt: e.target.value }, key); }}
          />
        </Form.Item>
        <Form.Item label="消息">
          {
            _.map(data?.messages || [], (x, i) => {
              return (
                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Select
                      value={x.role}
                      style={{ width: 200, marginBottom: 5 }}
                      options={[{ label: 'ASSISTANT', value: 'assistant' }, { label: 'USER', value: 'user' }]}
                      onChange={(e) => {
                        return this.onChangeAssistantValue({
                          ...data,
                          messages: _.map(data?.messages, (v, k) => {
                            return k === i ? { ...v, role: e } : v;
                          }),
                        }, key);
                      }}
                    />
                    <span>
                      <PlusCircleFilled
                        style={{ fontSize: 24 }}
                        onClick={() => {
                          return this.onChangeAssistantValue({
                            ...data, messages: [...data?.messages, { role: 'assistant', text: '' }],
                          }, key);
                        }}
                      />
                      <Divider type="vertical" />
                      <DeleteFilled
                        style={{ fontSize: 24 }}
                        onClick={() => {
                          return this.onChangeAssistantValue({
                            ...data, messages: _.filter(data?.messages, (v, k) => { return k !== i; }),
                          }, key);
                        }}
                      />
                    </span>
                  </div>
                  <Input.TextArea
                    value={x.content}
                    autoSize={{ minRows: 3 }}
                    onChange={(e) => {
                      return this.onChangeAssistantValue({
                        ...data,
                        messages: _.map(data?.messages, (v, k) => {
                          return k === i ? { ...v, content: e.target.value } : v;
                        }),
                      }, key);
                    }}
                  />
                </div>
              );
            })
          }
        </Form.Item>
      </Form>
    );
  }


  render = () => {
    const { prompts, open, contentType } = this.props;

    return (
      <Drawer
        width="30vw"
        open={open}
        onClose={this.props.onClose}
        title="角色"
        extra={<Button type="primary" onClick={this.props.onSave}>保存</Button>}
      >
        <Tabs>
          {
            _.map(Configs[`${_.toUpper(contentType)}_MAP`], (v, k) => {
              return (<Tabs.TabPane tab={v} key={k}>{this.renderAssistant(prompts?.[k], k)}</Tabs.TabPane>);
            })
          }
        </Tabs>
      </Drawer>
    );
  }
}
