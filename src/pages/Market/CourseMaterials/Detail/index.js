import 'react-mde/lib/styles/css/react-mde-all.css';

import { DeleteFilled, DownOutlined, EditOutlined, PlusCircleFilled, QrcodeOutlined } from '@ant-design/icons';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { Toast } from '~/components';
import Engine, { <PERSON>yunHel<PERSON>, Sessions } from '~/engine';
import { Platform, StringExtension } from '~/plugins';
import {
  Button,
  Checkbox,
  Divider,
  Drawer,
  Dropdown,
  Empty,
  Form,
  Image,
  Input,
  Modal,
  Popconfirm,
  Rate,
  Space,
  Spin,
  Tree,
  Upload,
} from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import QRCode from 'qrcode.react';
import qs from 'qs';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import Showdown from 'showdown';

import MermaidChart from '../components/MermaidChart';
import TTSSettings from '../components/TTSSetting';
import AssistantDrawer from './components/AssistantDrawer';
import ContentDrawer from './components/ContentDrawer';
import reducer, * as actions from './state';

const converter = new Showdown.Converter();
converter.setOption('tables', true);

const REGEN_PPT_TYPE = [
  { label: '摘要', key: '摘要' }, { label: '表格', key: '表格' },
  { label: '项目符号', key: '项目符号' }, { label: '随机', key: '' },
];
const COURSE_TYPE_MAP = { course: 'knowledge', exercise: 'general_qa', follow: 'follow' };

@connect(
  (state) => {
    return state.marketCourseMaterialDetail;
  },
  actions,
)
export default class MarketCourseMaterialDetail extends Component {
  static propTypes = {
    chapters: PropTypes.array.isRequired,
    fetchCourse: PropTypes.func.isRequired,
    updateCourse: PropTypes.func.isRequired,
    upsertCoursePrompt: PropTypes.func.isRequired,
    saveTimelineAudio: PropTypes.func.isRequired,
    fetchCourseChapters: PropTypes.func.isRequired,
    fetchCourseChapter: PropTypes.func.isRequired,
    upsertCourseChapter: PropTypes.func.isRequired,
    deleteCourseChapter: PropTypes.func.isRequired,
    dragCourseChapter: PropTypes.func.isRequired,
    fetchChapterParagraphs: PropTypes.func.isRequired,
    upsertChapterParagraph: PropTypes.func.isRequired,
    processChapterParagraph: PropTypes.func.isRequired,
    getChapterParagraphTimeline: PropTypes.func.isRequired,
    batchProcessChapterParagraph: PropTypes.func.isRequired,
    fetchVideos: PropTypes.func.isRequired,
    fetchMergeRecords: PropTypes.func.isRequired,
    getCategoriesTree: PropTypes.func.isRequired,
    match: PropTypes.object.isRequired,
    history: PropTypes.object.isRequired,
    location: PropTypes.object.isRequired,
    setState: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    spinning: false,
    isExercise: false,
    auditionPlaying: false,
    courseId: undefined,
    chapter: {},
    paragraph: {},
    previewTabs: {},
    prompts: {},
    courseInfo: {},
  }

  componentDidMount = async () => {
    const { id } = this.props.match.params;
    const { prompts, ...courseInfo } = await this.props.fetchCourse(id);
    await this.props.fetchCourseChapters(id);

    this.setState({ courseId: id, prompts, courseInfo, isExercise: courseInfo.type === 'exercise' });
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  findParent = (nodes, target, parent = null) => {
    for (const node of nodes) {
      if (node.key === target.key) {
        return parent;
      }
      if (node.children) {
        const result = this.findParent(node.children, target, node);
        if (result !== null) {
          return result;
        }
      }
    }
    return null;
  }

  transformData = (data, depth = 0) => {
    const result = [];
    while (data.length > 0) {
      const item = data.shift();
      if (item.depth === depth) {
        const transformedItem = {
          title: item.title,
          key: item.chapterId,
          depth: item.depth,
          children: [],
        };
        if (data.length > 0 && data[0].depth > depth) {
          transformedItem.children = this.transformData(data, depth + 1);
        }
        result.push(transformedItem);
      } else {
        data.unshift(item);
        break;
      }
    }
    return result;
  }

  onUpload = async (option, index, idx) => {
    try {
      const url = await AliyunHelper.clipsUploadImage(option.file, (progress) => {
        const percent = Math.round((progress.loaded / progress.total) * 100);
        option.onProgress({ percent });
      });
      option.onSuccess();
      const data = _.cloneDeep(this.state.paragraph);
      const { keynoteTimeline } = data.items[index];
      keynoteTimeline[idx].keypoints = { display: 'image', content: url };
      await this.setState({ paragraph: data });
      this.onSaveParagraph(true);
    } catch (e) {
      option.onError();
    }
  }

  onShowQrModal = (node) => {
    const { partnerId } = Sessions.getProfile();
    const courseId = this.props.location.query?.courseId;
    const domain = Platform.isProd() ? 'https://teacher.bzy.ai' : 'https://k12-test.bzy.ai';
    let url = `${domain}/exercise/${courseId}`;
    if (!_.isUndefined(node) && !this.state.isExercise) {
      url = `${domain}/course/${courseId}/${node.uuid}`;
    }
    if (this.state.courseInfo.type === 'follow') {
      url = `${domain}/reading/${courseId}`;
    }

    const pcurl = `${url}/auto?pid=${partnerId}&token=${Sessions.getToken()}`;
    url = `${url}?pid=${partnerId}`;
    Modal.info({
      title: '扫码预览',
      content: (
        <Form labelCol={{ span: 5 }}>
          <Form.Item label="二维码" style={{ marginBottom: 1 }}>
            <QRCode value={url} />
          </Form.Item>
          <Form.Item label="H5链接" style={{ marginBottom: 1 }}>
            <Button
              type="link"
              onClick={async () => {
                await navigator.clipboard.writeText(url);
                Toast.show('已复制到剪贴板', Toast.Type.SUCCESS);
              }}
            >复制H5链接
            </Button>
          </Form.Item>
          <Form.Item label="直播链接" style={{ marginBottom: 1 }}>
            <Button
              type="link"
              onClick={async () => {
                await navigator.clipboard.writeText(pcurl);
                Toast.show('已复制到剪贴板', Toast.Type.SUCCESS);
              }}
            >复制直播链接
            </Button>
          </Form.Item>
        </Form>
      ),
    });
  }

  onChangeChapterValue = (e, key) => {
    const value = e?.target ? e?.target.value : e;
    const chapter = { ...this.state.chapter, [key]: value };
    if (['free', 'published'].includes(key)) {
      chapter.subChapters = chapter.subChapters.map((x) => { return { ...x, [key]: value }; });
    }
    this.setState({ chapter });
  }

  onChangeSubChapterValue = (e, key, index) => {
    const value = e?.target ? e?.target.value : e;
    const chapter = _.cloneDeep(this.state.chapter);
    chapter.subChapters[index][key] = value;
    this.setState({ chapter });
  }

  onAddSubChapter = (index) => {
    const chapter = _.cloneDeep(this.state.chapter);
    chapter.subChapters.splice(index + 1, 0, {});
    this.setState({ chapter });
  }

  onDelSubChapter = (index) => {
    const chapter = _.cloneDeep(this.state.chapter);
    chapter.subChapters.splice(index, 1);
    this.setState({ chapter });
  }

  onSaveChapter = () => {
    const { id } = this.props.match.params;
    const { subChapters, ...chapter } = this.state.chapter;
    const notNullSubChapters = subChapters.filter((subChapter) => { return !_.isEmpty(subChapter); });
    this.props.upsertCourseChapter({ chapter, subChapters: notNullSubChapters, courseId: id });
    this.setState({ openChapter: false, chapter: {} });
  }

  onClickEditChapter = async (chapterId) => {
    const { chapter, subChapters } = await this.props.fetchCourseChapter(this.state.courseId, chapterId);
    const subItems = subChapters?.length ? subChapters : [];
    delete chapter.id;
    await this.setState({ openChapter: true, chapter: { ...chapter, chapterId, subChapters: subItems } });
  }

  // 查看音频稿
  onShowParagraphKeynoteTimeline = async (paragraphId, idx) => {
    const { timeline } = await this.props.getChapterParagraphTimeline(paragraphId);
    const paragraph = _.cloneDeep(this.state.paragraph);
    paragraph.items[idx].keynoteTimeline = timeline;
    await this.setState({ openCreate: true, paragraphId, paragraph });
  }

  onSelect = async (node) => {
    const { title, key } = node;
    if (!_.isEmpty(node?.children)) {
      this.onClickEditChapter(key);
      return;
    }

    let items = await this.props.fetchChapterParagraphs(key);
    const contentType = COURSE_TYPE_MAP[this.state.courseInfo?.type];
    items = items?.length ? items : [{ contentType }];
    const previewTabs = {};
    items = items.map((x) => {
      previewTabs[x.id] = 'write';
      const pattern = /「(.*?)」[\u2460-\u32BF]/g;
      const matches = (x?.content || '').match(pattern);
      return { ...x, noteCount: matches?.length || 0 };
    });
    this.setState({ open: true, previewTabs, paragraph: { chapterId: key, title, items } });
  }

  onDrop = async (info) => {
    const dropKey = info.node.key;
    const dragKey = info.dragNode.key;
    const dropPos = info.node.pos.split('-');
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);

    const loop = (data, key, callback) => { // eslint-disable-line
      for (let i = 0; i < data.length; i++) {
        if (data[i].key === key) {
          return callback(data[i], i, data);
        }
        if (data[i].children) {
          loop(data[i].children, key, callback);
        }
      }
    };
    const data = [...this.props.chapters];

    let dragObj;
    loop(data, dragKey, (item, index, arr) => { arr.splice(index, 1); dragObj = item; });
    if (!info.dropToGap) {
      loop(data, dropKey, (item) => {
        item.children = item.children || []; // eslint-disable-line
        item.children.unshift(dragObj);
      });
    } else if (
      (info.node.props.children || []).length > 0 &&
      info.node.props.expanded &&
      dropPosition === 1
    ) {
      loop(data, dropKey, (item) => {
        item.children = item.children || []; // eslint-disable-line
        item.children.unshift(dragObj);
      });
    } else {
      let ar = [];
      let i;
      loop(data, dropKey, (_item, index, arr) => { ar = arr; i = index; });
      if (dropPosition === -1) {
        ar.splice(i, 0, dragObj);
      } else {
        ar.splice(i + 1, 0, dragObj);
      }
    }

    const newParentNode = this.findParent(data, info.dragNode);
    const prevSiblingIndex = newParentNode?.children?.findIndex((x) => { return x.key === dragObj.key; });
    const params = {
      courseId: +this.state.courseId,
      chapterId: dragObj.key,
      prevSiblingId: newParentNode?.children[prevSiblingIndex - 1]?.key || 0,
      newParentId: newParentNode?.key || undefined,
    };
    this.props.setState({ chapters: data });
    await this.props.dragCourseChapter(params);
  };

  onSavePrompt = async () => {
    const { courseId, prompts } = this.state;
    await this.props.upsertCoursePrompt({ courseId, prompts });
    Toast.show('角色已保存', Toast.Type.SUCCESS);
    this.setState({ openAssistant: false, contentType: null });
  }

  onSaveParagraph = async (onlySave = false) => {
    const paragraph = _.cloneDeep(this.state.paragraph);
    const { chapterId, items } = paragraph;
    const paragraphs = items.map((x) => {
      return { ...x, keynoteTimeline: _.isUndefined(x.keynoteTimeline) ? [] : x.keynoteTimeline };
    });
    const upsertResult = await this.props.upsertChapterParagraph({ chapterId, paragraphs });
    paragraph.items = Object.values(upsertResult);
    await this.setState({ paragraph }, () => { this.onRefreshParagraph(); });
    if (!onlySave) {
      this.setState({ open: false, paragraph: {} });
    }
  }

  onRefreshParagraph = async () => {
    const { chapterId } = this.state.paragraph;
    const items = await this.props.fetchChapterParagraphs(chapterId);
    const previewTabs = {};
    const paragraph = _.cloneDeep(this.state.paragraph);
    paragraph.items = items.map((x) => {
      previewTabs[x.id] = 'write';
      const pattern = /「(.*?)」[\u2460-\u32BF]/g;
      const matches = (x?.content || '').match(pattern);
      return { ...x, noteCount: matches?.length || 0 };
    });
    await this.setState({ paragraph, previewTabs });
  }

  onProcessParagraph = async (state) => {
    await this.onSaveParagraph(true);
    await this.props.processChapterParagraph({ id: this.state.paragraphId, state });
    Toast.show('已提交处理', Toast.Type.SUCCESS);
    this.setState({ openCreate: false, paragraphId: undefined });
  }

  onRegenTranscript = async (timeline, tIdx, pIdx, type = 'transcript', keypointsType) => {
    const paragraph = _.cloneDeep(this.state.paragraph);
    const { courseId, paragraphId, prompts } = this.state;
    const { contentType, hasFormula } = paragraph.items[pIdx];
    const params = { courseId, paragraphId, prompts, timeline, hasFormula, contentType, keypointsType };
    const domain = Engine.getApiEndpoint();
    const ctrl = new AbortController();
    const query = { access_token: Sessions.getToken(), sse_mode: 1 };
    const url = type === 'transcript' ? '/v2/aiteacher/chapter-paragraphs/timelines/generate' :
      '/v2/aiteacher/chapter-paragraphs/timelines/keypoints-generate';

    fetchEventSource(`${domain}${url}?${qs.stringify(query)}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(StringExtension.camelToSnakeObj(params)),
      signal: ctrl.signal,
      onopen: () => { this.setState({ spinning: true }); },
      onclose: () => { this.setState({ spinning: false }); },
      onmessage: async (e) => {
        const obj = JSON.parse(_.isEmpty(e.data) ? '{}' : e.data);
        if (obj.steps === 'stop') {
          paragraph.items[pIdx].keynoteTimeline[tIdx] = StringExtension.snakeToCamelObj(obj.result);
          await this.setState({ paragraph });
          if (type === 'keypoint') {
            this.onSaveParagraph(true);
          }
          Toast.show('已重新生成', Toast.Type.SUCCESS);
        }
      },
    });
  }

  onAudition = async ({ transcript: text }, idx, index) => {
    const { ttsSettings } = this.state.courseInfo;

    let url = `${Engine.getWssEndpoint()}/v2/tts/audio-stream`;
    url += `?${qs.stringify({ access_token: Sessions.getToken() })}`;

    this.socket = new WebSocket(url);
    this.socket.onopen = () => {
      this.socket.send(JSON.stringify({ type: 'tts', text, tts_settings: ttsSettings }));
    };
    const vm = this;
    this.audio = new Audio();

    await this.setState({ auditionPlaying: true, stopId: `${idx}-${index}` });
    let isPlaying = false;
    const audioQueue = [];
    function playNextAudio() {
      if (audioQueue.length > 0) {
        const audioUrl = audioQueue.shift();

        vm.audio.src = audioUrl;
        vm.audio.addEventListener('ended', () => {
          vm.audio.currentTime = 0;
          playNextAudio();
        });

        vm.audio.play();
        isPlaying = true;
      } else {
        isPlaying = false;
        vm.setState({ auditionPlaying: false, stopId: undefined });
      }
    }

    this.socket.onmessage = (event) => {
      const arrayBuffer = event.data;
      const blob = new Blob([arrayBuffer], { type: 'audio/mpeg' });
      const audioUrl = URL.createObjectURL(blob);
      audioQueue.push(audioUrl);
      if (!isPlaying) {
        playNextAudio();
      }
    };
  }

  onStopAudition = () => {
    this.socket?.close();
    this.audio.pause();
    this.setState({ auditionPlaying: false, stopId: undefined });
  }

  onSaveTimelineAudio = async (timeline, pIdx) => {
    const paragraph = _.cloneDeep(this.state.paragraph);
    const { courseId, paragraphId, prompts } = this.state;
    const { contentType, hasFormula } = paragraph.items[pIdx];
    const params = { courseId, paragraphId, prompts, timeline, hasFormula, contentType };
    await this.props.saveTimelineAudio(params);
    Toast.show('音频生成任务已提交!', Toast.Type.SUCCESS);
  }

  onChangeCourseValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const courseInfo = _.cloneDeep(this.state.courseInfo);
    courseInfo[key] = value;
    this.setState({ courseInfo });
  }

  renderTtsModal = () => {
    const { openTTS, courseInfo, prompts } = this.state;
    return (
      <Modal
        title="TTS"
        open={openTTS}
        onCancel={() => { return this.setState({ openTTS: false }); }}
        onOk={async () => {
          await this.props.updateCourse({ ...courseInfo, prompts });
          Toast.show('已保存', Toast.Type.SUCCESS);
          return this.setState({ openTTS: false });
        }}
      >
        <Form labelCol={{ span: 4 }}>
          <TTSSettings
            course={courseInfo}
            onChange={(e, key) => { return this.onChangeCourseValue(e, key); }}
          />
        </Form>
      </Modal>
    );
  }

  renderKeypoints = ({ content, display }, idx) => {
    if (_.isObject(content)) {
      return <pre>{JSON.stringify(content, null, 2)}</pre>;
    }

    if (display === 'markdown') {
      return (
        <div
          style={{ marginBottom: '-1em' }}
          dangerouslySetInnerHTML={{ __html: converter.makeHtml(content) }}
        />
      );
    }

    if (display === 'image') {
      return <Image src={content} height={120} style={{ marginBottom: 10 }} />;
    }

    let chart = _.trim(content, '```');
    chart = _.trim(chart, 'mermaid');
    chart = chart.replace(/"/g, "'");
    chart = chart.replace(/\[/g, '("');
    chart = chart.replace(/\]/g, '")');

    return <MermaidChart chart={chart} data={idx} />;
  }

  renderAddDrawer = () => {
    const { paragraph, paragraphId, openCreate } = this.state;
    const index = paragraph.items?.findIndex((x) => { return +x.id === +paragraphId; });
    let timelines = index > -1 ? paragraph.items[index]?.keynoteTimeline : [{}];
    timelines = timelines?.length ? timelines : [{}];
    const contentType = paragraph.items[index]?.contentType;

    return (
      <Drawer
        width="50vw"
        open={openCreate}
        title={paragraph?.title}
        bodyStyle={{ position: 'relative' }}
        onClose={() => { this.setState({ openCreate: false }); }}
        extra={
          <>
            <Button onClick={() => { return this.setState({ openTTS: true }); }}>TTS</Button>
            <Divider type="vertical" />
            <Button
              type="primary"
              onClick={() => { return this.setState({ openAssistant: true, contentType }); }}
            >角色
            </Button>
          </>
        }
      >
        <Spin spinning={this.state.spinning}>
          <Form labelCol={{ span: 4 }}>
            {
              (timelines.map((x, idx) => {
                return (
                  <>
                    <Divider orientation="left">{x.name}</Divider>
                    <Form.Item label="原文">
                      <div
                        style={{ marginBottom: '-1em' }}
                        dangerouslySetInnerHTML={{
                          __html: !_.isUndefined(x.originText) ? converter.makeHtml(x.originText) : '',
                        }}
                      />
                    </Form.Item>
                    {
                      !_.isUndefined(x?.keypoints) &&
                      <Form.Item label="PPT记忆点">
                        <div>
                          {this.renderKeypoints(x.keypoints || {}, idx)}
                          <br />
                          <Upload
                            accept={'image/*'}
                            showUploadList={false}
                            customRequest={(opt) => { return this.onUpload(opt, index, idx); }}
                          >
                            <Button size="small">上传PPT</Button>
                          </Upload>
                        </div>
                      </Form.Item>
                    }
                    <Form.Item label="音频稿">
                      {
                        this.state.courseInfo.type !== 'follow' &&
                        <Input.TextArea
                          style={{ marginBottom: 10 }}
                          autoSize={{ minRows: 3 }}
                          value={x.transcript}
                          onChange={(e) => {
                            const data = _.cloneDeep(this.state.paragraph);
                            const { keynoteTimeline } = data.items[index];
                            keynoteTimeline[idx].transcript = e.target.value;
                            this.setState({ paragraph: data });
                          }}
                        />
                      }
                      {(x.audioUrls || []).map((s) => {
                        return <audio src={s} controls style={{ height: 30 }} />;
                      })}
                    </Form.Item>
                    {
                      x.slogan && (
                        <Form.Item label="PPT手写体">
                          <Input.TextArea
                            style={{ marginBottom: 10 }}
                            autoSize={{ minRows: 3 }}
                            value={x.slogan?.text}
                            onChange={(e) => {
                              const data = _.cloneDeep(this.state.paragraph);
                              const { keynoteTimeline } = data.items[index];
                              keynoteTimeline[idx].slogan.text = e.target.value;
                              this.setState({ paragraph: data });
                            }}
                          />
                        </Form.Item>
                      )
                    }
                    {
                      contentType === 'exercise' && (
                        <>
                          <Form.Item label="答案音频稿">
                            <Input.TextArea
                              style={{ marginBottom: 10 }}
                              autoSize={{ minRows: 3 }}
                              value={x.anwserTranscript}
                              onChange={(e) => {
                                const data = _.cloneDeep(this.state.paragraph);
                                const { keynoteTimeline } = data.items[index];
                                keynoteTimeline[idx].anwserTranscript = e.target.value;
                                this.setState({ paragraph: data });
                              }}
                            />
                            {(x.anwserAudioUrls || []).map((s) => {
                              return <audio src={s} controls style={{ height: 30 }} />;
                            })}
                          </Form.Item>
                          <Form.Item label="答案手写体">
                            <Input.TextArea
                              style={{ marginBottom: 10 }}
                              autoSize={{ minRows: 3 }}
                              value={x.anwserSlogan?.text}
                              onChange={(e) => {
                                const data = _.cloneDeep(this.state.paragraph);
                                const { keynoteTimeline } = data.items[index];
                                keynoteTimeline[idx].anwserSlogan.text = e.target.value;
                                this.setState({ paragraph: data });
                              }}
                            />
                          </Form.Item>
                        </>
                      )
                    }
                    <div
                      style={{
                        marginTop: -10,
                        marginBottom: 20,
                        float: 'right',
                        display: _.isEmpty(x.originText) ? 'none' : 'initial',
                      }}
                    >
                      {
                        this.state.stopId === `${idx}-${index}` &&
                        <>
                          <Button size="small" onClick={() => { return this.onStopAudition(x); }}>
                            停止
                          </Button>
                          <Divider type="vertical" />

                        </>
                      }
                      <Button
                        size="small"
                        loading={this.state.auditionPlaying}
                        onClick={() => { return this.onAudition(x, idx, index); }}
                      >
                        音频稿试听
                      </Button>
                      <Divider type="vertical" />
                      <Dropdown
                        disabled={this.state.courseInfo.type === 'follow'}
                        menu={{
                          items: REGEN_PPT_TYPE.map((o) => {
                            return {
                              ...o,
                              onClick: () => { return this.onRegenTranscript(x, idx, index, 'keypoint', o.key); },
                            };
                          }),
                        }}
                      >
                        <Button size="small"><Space>重新生成PPT<DownOutlined /></Space></Button>
                      </Dropdown>
                      <Divider type="vertical" />
                      <Button
                        size="small"
                        type="primary"
                        disabled={this.state.courseInfo.type === 'follow'}
                        onClick={() => { return this.onRegenTranscript(x, idx, index); }}

                      >重新生成音频稿
                      </Button>
                      <Divider type="vertical" />
                      <Button
                        size="small"
                        type="primary"
                        onClick={() => { return this.onSaveTimelineAudio(x, index); }}
                      >
                        生成音频并保存
                      </Button>
                    </div>
                  </>
                );
              }))
            }
          </Form>
          <Divider style={{ marginBottom: 50 }} />
        </Spin>
        <Form.Item
          label=" "
          colon={false}
          style={{
            position: 'fixed',
            bottom: 0,
            right: 0,
            width: '50vw',
            background: '#fff',
            height: 50,
            margin: 0,
            padding: 10,
          }}
        >
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button onClick={() => { return this.onProcessParagraph('script_and_audio'); }}>同时生成</Button>
            <span>
              <Button onClick={() => { return this.onProcessParagraph('script'); }}>生成音频稿</Button>
              <Divider type="vertical" />
              <Button onClick={() => { return this.onProcessParagraph('audio'); }} type="primary">
                生成音频
              </Button>
            </span>
          </div>
        </Form.Item>
      </Drawer>
    );
  }

  renderContentDrawer = () => {
    return (
      <ContentDrawer
        open={this.state.open}
        courseId={this.state.courseId}
        paragraph={this.state.paragraph}
        previewTabs={this.state.previewTabs}
        type={this.state.courseInfo?.type}
        onShowTimeline={this.onShowParagraphKeynoteTimeline}
        fetchVideos={this.props.fetchVideos}
        fetchMergeRecords={this.props.fetchMergeRecords}
        getCategoriesTree={this.props.getCategoriesTree}
        setState={(e) => { return this.setState(e); }}
        onClose={() => { this.setState({ open: false }); }}
        onSave={(e) => { return this.onSaveParagraph(e); }}
        onRefresh={() => { return this.onRefreshParagraph(); }}
        onProcessAll={this.props.batchProcessChapterParagraph}
      />
    );
  }

  renderAddChapterDrawer = () => {
    const { chapter, openChapter, courseInfo } = this.state;
    return (
      <Drawer
        width="50vw"
        open={openChapter}
        title="新增"
        onClose={() => { this.setState({ openChapter: false, chapter: {} }); }}
        extra={<Button type="primary" onClick={this.onSaveChapter}>保存</Button>}
      >
        <Form labelCol={{ span: 2 }}>
          <Form.Item label="章节名称">
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Input
                style={{ width: 'calc(100% - 160px)' }}
                value={chapter?.title}
                onChange={(e) => { return this.onChangeChapterValue(e, 'title'); }}
              />
              <Divider type="vertical" />
              <Checkbox
                style={{ width: 60 }}
                checked={chapter?.free}
                onChange={(e) => { return this.onChangeChapterValue(e.target.checked, 'free'); }}
              >免费
              </Checkbox>
              <Divider type="vertical" />
              <Checkbox
                style={{ width: 60 }}
                checked={chapter?.published}
                onChange={(e) => { return this.onChangeChapterValue(e.target.checked, 'published'); }}
              >上线
              </Checkbox>
            </div>
            <div style={{ display: courseInfo.type === 'follow' ? 'block' : 'none' }}>
              <span>难度: </span>&nbsp;
              <Rate
                count={3}
                value={chapter?.difficulty}
                onChange={(e) => { return this.onChangeChapterValue(e, 'difficulty'); }}
              />
            </div>
          </Form.Item>
          <Form.Item label="子章节">
            {
              _.isEmpty((chapter?.subChapters || [])) &&
              <Button onClick={() => { return this.onAddSubChapter(0); }}>新增子章节</Button>
            }
            {
              (chapter?.subChapters || []).map((x, idx) => {
                return (
                  <Input.Group compact style={{ display: 'flex', marginBottom: 10 }}>
                    <Input
                      value={x.title}
                      onChange={(e) => { return this.onChangeSubChapterValue(e, 'title', idx); }}
                      style={{ width: 'calc(100% - 100px)' }}
                    />
                    <span style={{ width: 200, display: 'flex', alignItems: 'center' }}>
                      <Divider type="vertical" />
                      <Checkbox
                        disabled={chapter?.free}
                        checked={x?.free}
                        onChange={(e) => { return this.onChangeSubChapterValue(e.target.checked, 'free', idx); }}
                      >免费
                      </Checkbox>
                      <Divider type="vertical" />
                      <Checkbox
                        disabled={chapter?.published}
                        checked={x?.published}
                        onChange={(e) => { return this.onChangeSubChapterValue(e.target.checked, 'published', idx); }}
                      >上线
                      </Checkbox>
                    </span>

                    <div style={{ display: 'flex' }}>
                      <Button
                        type="primary"
                        icon={<PlusCircleFilled />}
                        onClick={() => { return this.onAddSubChapter(idx); }}
                      />
                      <Divider type="vertical" />
                      <Button
                        type="primary"
                        danger
                        icon={<DeleteFilled />}
                        onClick={() => { return this.onDelSubChapter(idx); }}
                      />
                    </div>
                  </Input.Group>
                );
              })
            }
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderAssistantDrawer = () => {
    const { prompts, openAssistant, contentType } = this.state;

    return (
      <AssistantDrawer
        open={openAssistant}
        prompts={prompts}
        contentType={contentType}
        onChange={(e) => { return this.setState(e); }}
        onClose={() => { this.setState({ openAssistant: false, contentType: null }); }}
        onSave={this.onSavePrompt}
      />
    );
  }

  renderTreeTitle = (nodeData) => {
    return (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Popconfirm
          title="是否删除?!"
          onConfirm={() => { return this.props.deleteCourseChapter(this.state.courseId, nodeData.key); }}
        >
          <Button size="small" type="link" danger icon={<DeleteFilled />} />
        </Popconfirm>
        <Divider type="vertical" />
        <Button type="text" onClick={() => { return this.onSelect(nodeData); }}>
          {nodeData.title || '无名称'}
        </Button>
        <Divider type="vertical" />
        <Button
          size="small"
          type="link"
          icon={<EditOutlined />}
          onClick={() => { return this.onClickEditChapter(nodeData.key); }}
        />
        {
          _.isEmpty(nodeData?.children) && !this.state.isExercise &&
          <>
            <Divider type="vertical" />
            <Button
              size="small"
              type="link"
              icon={<QrcodeOutlined />}
              onClick={() => { return this.onShowQrModal(nodeData); }}
            />
          </>
        }
        {
          this.state.isExercise && !_.isEmpty(nodeData?.children) &&
          <>
            <Divider type="vertical" />
            <Button
              size="small"
              type="link"
              icon={<QrcodeOutlined />}
              onClick={() => { return this.onShowQrModal(nodeData); }}
            />
          </>
        }
      </div>
    );
  }

  renderTree = () => {
    if (_.isEmpty(this.props.chapters)) return <Empty />;

    return (
      <Tree
        defaultExpandAll
        className="draggable-tree"
        draggable
        blockNode
        onDragEnter={() => { }}
        onDrop={this.onDrop}
        treeData={this.props.chapters}
        titleRender={(e) => { return this.renderTreeTitle(e); }}
      />
    );
  }

  render = () => {
    return (
      <div style={{ padding: 30, background: '#fff', maxHeight: 'calc(100vh - 124px)', overflow: 'auto' }}>
        <div>
          <div>
            <Button
              type="primary"
              onClick={() => { return this.setState({ openChapter: true, chapter: { subChapters: [] } }); }}
            >
              新增章节
            </Button>

            {
              this.state.isExercise &&
              <Button
                type="link"
                icon={<QrcodeOutlined />}
                onClick={() => { return this.onShowQrModal(); }}
              >题库
              </Button>
            }
          </div>
          <Button
            type="primary"
            style={{ float: 'right' }}
            onClick={() => { return this.props.history.goBack(); }}
          >
            返回
          </Button>
        </div>
        <Divider />
        {this.renderTree()}

        {this.state.openTTS && this.renderTtsModal()}

        {this.state.open && this.renderContentDrawer()}
        {this.state.openCreate && this.renderAddDrawer()}
        {this.state.openChapter && this.renderAddChapterDrawer()}
        {this.state.openAssistant && this.renderAssistantDrawer()}
      </div>
    );
  }
}

export {
  reducer,
};
