import Configs from '~/consts';
import { AiTeacher } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'COURSE_MATERIALS/SET_STATE';
const CLEAR_STATE = 'COURSE_MATERIALS/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

const transformData = (data, depth = 0) => {
  const result = [];
  while (data.length > 0) {
    const item = data.shift();
    if (item.depth === depth) {
      const transformedItem = {
        title: item.title,
        key: item.chapterId,
        uuid: item.chapterUuid,
        depth: item.depth,
        children: [],
      };
      if (data.length > 0 && data[0].depth > depth) {
        transformedItem.children = transformData(data, depth + 1);
      }
      result.push(transformedItem);
    } else {
      data.unshift(item);
      break;
    }
  }
  return result;
};

export const fetchCourse = (itemId) => {
  return async () => {
    const data = await AiTeacher.fetchCourse(itemId);
    return data;
  };
};

export const updateCourse = (params) => {
  return async () => {
    await AiTeacher.updateCourse(params);
  };
};

export const upsertCoursePrompt = (params) => {
  return async () => {
    const data = await AiTeacher.upsertCoursePrompt(params);
    return data;
  };
};

export const saveTimelineAudio = (params) => {
  return async () => {
    const data = await AiTeacher.saveTimelineAudio(params);
    return data;
  };
};

export const fetchCourseChapters = (courseId) => {
  return async (dispatch) => {
    const data = await AiTeacher.fetchCourseChapters(courseId);
    const chapters = transformData(_.values(data));
    dispatch(setState({ chapters }));
  };
};

export const fetchCourseChapter = (courseId, chapterId) => {
  return async () => {
    const data = await AiTeacher.fetchCourseChapter(courseId, chapterId);
    return data;
  };
};

export const upsertCourseChapter = (params) => {
  return async (dispatch) => {
    await AiTeacher.upsertCourseChapter(params);
    dispatch(fetchCourseChapters(params.courseId));
  };
};

export const fetchChapterParagraphs = (chapterId) => {
  return async () => {
    const { items } = await AiTeacher.fetchChapterParagraphs({ chapterId, ...Configs.ALL_PAGE_PARAMS });
    return items;
  };
};

export const upsertChapterParagraph = (params) => {
  return async () => {
    const result = await AiTeacher.upsertChapterParagraph(params);
    return result;
  };
};

export const dragCourseChapter = (params) => {
  return async (dispatch) => {
    await AiTeacher.dragCourseChapter(params);
    dispatch(fetchCourseChapters(params.courseId));
  };
};

export const deleteCourseChapter = (courseId, chapterId) => {
  return async (dispatch) => {
    await AiTeacher.deleteCourseChapter(courseId, chapterId);
    dispatch(fetchCourseChapters(courseId));
  };
};

export const processChapterParagraph = (params) => {
  return async () => {
    await AiTeacher.processChapterParagraph(params);
  };
};

export const batchProcessChapterParagraph = (params) => {
  return async () => {
    await AiTeacher.batchProcessChapterParagraph(params);
  };
};

export const getChapterParagraphTimeline = (params) => {
  return async () => {
    const result = await AiTeacher.getChapterParagraphTimeline(params);
    return result;
  };
};

export const getCategoriesTree = (params) => {
  return async () => {
    const result = await AiTeacher.getCategoriesTree(params);
    return result;
  };
};

export const fetchMergeRecords = (params) => {
  return async () => {
    const result = await AiTeacher.fetchMergeRecords(params);
    return result;
  };
};

export const fetchVideos = (params) => {
  return async () => {
    const result = await AiTeacher.fetchVideos(params);
    return result;
  };
};

const _getInitState = () => {
  return {
    chapters: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
