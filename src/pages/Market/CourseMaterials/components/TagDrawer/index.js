import { Button, Drawer, Input } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class TagDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    tags: PropTypes.array,
    onSave: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    tags: [],
  }

  componentDidMount = () => {
    this.setState({ tags: this.props.tags });
  }

  onAdd = () => {
    const tags = [...this.state.tags, ''];
    this.setState({ tags });
  }

  onDelete = (tag, idx) => {
    const tags = [...this.state.tags];
    tags.splice(idx, 1);
    this.setState({ tags });
  }

  onChangeTagValue = (e, idx) => {
    const tags = [...this.state.tags];
    tags[idx] = e.target.value;
    this.setState({ tags });
  }

  onSave = () => {
    this.props.onSave(this.state.tags);
  }

  render = () => {
    return (
      <Drawer
        width="50vw"
        open={this.props.open}
        onClose={this.props.onClose}
        title="课程标签"
        extra={<Button type="primary" onClick={this.onSave}>保存</Button>}
      >
        <Button type="primary" onClick={() => { return this.onAdd(); }}>新增</Button>
        {
          this.state.tags.map((x, idx) => {
            return (
              <Input
                value={x}
                style={{ marginTop: 10 }}
                onChange={(e) => { return this.onChangeTagValue(e, idx); }}
                addonAfter={
                  <Button
                    type="text"
                    border={false}
                    onClick={() => { return this.onDelete(x, idx); }}
                  >删除
                  </Button>
                }
              />
            );
          })
        }
      </Drawer>
    );
  }
}
