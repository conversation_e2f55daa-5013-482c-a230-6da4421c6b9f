import mermaid from 'mermaid';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class Mermaid<PERSON><PERSON> extends PureComponent {
  static propTypes = {
    data: PropTypes.number,
    chart: PropTypes.string,
  }

  componentDidMount() {
    mermaid.initialize({
      startOnLoad: true,
      flowchart: {
        curve: 'monotone',
        diagramPadding: 0,
        nodeSpacing: 5,
      },
    });
    this.renderMermaid();
  }

  componentDidUpdate() {
    this.renderMermaid();
  }

  renderMermaid = () => {
    const dom = this[`ref-mermaid-${this.props.data}`];
    if (dom) {
      try {
        mermaid.mermaidAPI.render('graphDiv', this.props.chart, (svgCode) => { dom.innerHTML = svgCode; });
      } catch (error) {
        console.log(this.props.chart); // eslint-disable-line
      }
    }
  }

  render() {
    return (
      <div
        ref={(el) => { this[`ref-mermaid-${this.props.data}`] = el; }}
        className="mermaid"
      />
    );
  }
}
