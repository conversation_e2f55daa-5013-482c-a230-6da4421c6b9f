import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Checkbox, Divider, Drawer, Form, Input, InputNumber, Select, Switch, Tabs, Upload } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import Configs from '../../const';
import TTSSettings from '../TTSSetting';

const REDIRECT_MAP = {
  default: '/bundle',
  essay: '/composition',
  spoken: '/spoken/<<courseId>>',
  follow: '/reading/<<courseId>>',
  gpts: '/consultation/<<courseId>>',
};
export default class CourseDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    course: PropTypes.object,
    courseList: PropTypes.array,
    tags: PropTypes.array,
    onChangeValue: PropTypes.func,
    onUpload: PropTypes.func,
    onSave: PropTypes.func,
    onClose: PropTypes.func,
  }

  onChangeExtraValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const extra = { ...this.props.course.extra, [key]: value };
    if (key === 'serviceType') {
      const replaceKey = this.props.course?.courseUuid || ':courseId';
      extra.redirectUrl = REDIRECT_MAP[value].replace('<<courseId>>', replaceKey);
    }

    this.props.onChangeValue(extra, 'extra');
  }

  onChangeAssistantValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const prompts = { ...this.props.course.prompts, [key]: value };
    this.props.onChangeValue(prompts, 'prompts');
  }

  renderAssistant = (data = {}, key) => {
    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label="模型">
          <Select
            style={{ width: 200 }}
            value={data?.model}
            onChange={(e) => { return this.onChangeAssistantValue({ ...data, model: e }, key); }}
          >
            <Select.Option value="gpt-3.5-turbo">gpt-3.5</Select.Option>
            <Select.Option value=" gpt-4-turbo-preview">gpt-4</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label="温度">
          <InputNumber
            value={data?.temperature}
            step={0.1}
            min={0}
            max={1}
            onChange={(e) => { return this.onChangeAssistantValue({ ...data, temperature: e }, key); }}
          />
        </Form.Item>
        <Form.Item label="响应类型">
          <Select
            style={{ width: 200 }}
            value={data?.responseFormat}
            onChange={(e) => { return this.onChangeAssistantValue({ ...data, responseFormat: e }, key); }}
          >
            <Select.Option value="json_object">JSON</Select.Option>
            <Select.Option value="text">文本</Select.Option>
          </Select>
        </Form.Item>
        {
          data?.responseFormat === 'json_object' &&
          <Form.Item label="JSON提示词">
            <Input.TextArea
              autoSize={{ minRows: 3, maxRows: 10 }}
              value={data?.formatPrompt}
              onChange={(e) => { return this.onChangeAssistantValue({ ...data, formatPrompt: e.target.value }, key); }}
            />
          </Form.Item>
        }
        <Form.Item label="系统提示">
          <Input.TextArea
            autoSize={{ minRows: 3, maxRows: 10 }}
            value={data?.systemPrompt}
            onChange={(e) => { return this.onChangeAssistantValue({ ...data, systemPrompt: e.target.value }, key); }}
          />
        </Form.Item>
        <Form.Item label="消息">
          {
            _.map(data?.messages || [], (x, i) => {
              return (
                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Select
                      value={x.role}
                      style={{ width: 200, marginBottom: 5 }}
                      options={[{ label: 'ASSISTANT', value: 'assistant' }, { label: 'USER', value: 'user' }]}
                      onChange={(e) => {
                        return this.onChangeAssistantValue({
                          ...data,
                          messages: _.map(data?.messages, (v, k) => {
                            return k === i ? { ...v, role: e } : v;
                          }),
                        }, key);
                      }}
                    />
                    <span>
                      <PlusOutlined
                        style={{ fontSize: 24 }}
                        onClick={() => {
                          return this.onChangeAssistantValue({
                            ...data, messages: [...data?.messages, { role: 'assistant', text: '' }],
                          }, key);
                        }}
                      />
                      <Divider type="vertical" />
                      <DeleteOutlined
                        style={{ fontSize: 24 }}
                        onClick={() => {
                          return this.onChangeAssistantValue({
                            ...data, messages: _.filter(data?.messages, (v, k) => { return k !== i; }),
                          }, key);
                        }}
                      />
                    </span>
                  </div>
                  <Input.TextArea
                    value={x.content}
                    autoSize={{ minRows: 3 }}
                    onChange={(e) => {
                      return this.onChangeAssistantValue({
                        ...data,
                        messages: _.map(data?.messages, (v, k) => {
                          return k === i ? { ...v, content: e.target.value } : v;
                        }),
                      }, key);
                    }}
                  />
                </div>
              );
            })
          }
        </Form.Item>
      </Form>
    );
  }

  render = () => {
    const { open, course } = this.props;

    return (
      <Drawer
        width="50vw"
        open={open}
        title={!course?.uuid ? '新增' : `${course?.name}-编辑`}
        onClose={this.props.onClose}
        extra={<Button type="primary" onClick={this.props.onSave}>保存</Button>}
      >
        <Form labelCol={{ span: 3 }} className="common-form">
          <Form.Item label="上线状态">
            <Switch
              checkedChildren="已上线"
              unCheckedChildren="未上线"
              checked={course?.published}
              onChange={(e) => { return this.props.onChangeValue(e, 'published'); }}
            />
          </Form.Item>
          <Form.Item label="类型">
            <Select
              style={{ width: 120 }}
              value={course?.type || 'course'}
              onChange={(e) => { return this.props.onChangeValue(e, 'type'); }}
              options={_.map(Configs.COURSE_TYPE_MAP, (v, k) => { return { value: k, label: v }; })}
            />
          </Form.Item>
          <Form.Item label="标签">
            <Select
              value={course?.tags}
              onChange={(e) => { return this.props.onChangeValue([e], 'tags'); }}
            >
              {_.map(this.props.tags, (v) => { return <Select.Option value={v}>{v}</Select.Option>; })}
            </Select>
          </Form.Item>
          {
            course?.type === 'spoken' &&
            <Form.Item label="关联会话">
              <Input
                value={course?.extra?.relatedSessionId}
                onChange={(e) => { return this.onChangeExtraValue(e, 'relatedSessionId'); }}
              />
            </Form.Item>
          }
          {
            ['course', 'gpts'].includes(course?.type) &&
            <Form.Item label="关联工作流">
              <Input
                value={course?.extra?.relatedWorkflowId}
                onChange={(e) => { return this.onChangeExtraValue(e, 'relatedWorkflowId'); }}
              />
            </Form.Item>
          }
          {
            course?.type === 'exercise' &&
            <Form.Item label="关联课程">
              <Select
                mode="multiple"
                value={course?.relatedCourses}
                onChange={(e) => { return this.props.onChangeValue(e, 'relatedCourses'); }}
              >
                {_.map(this.props.courseList, (v) => {
                  return <Select.Option value={v.id}>{v.name}</Select.Option>;
                })}
              </Select>
            </Form.Item>
          }
          <Form.Item label="课程全称">
            <Input
              value={course.name}
              onChange={(e) => { return this.props.onChangeValue(e, 'name'); }}
            />
          </Form.Item>
          {
            course?.type === 'course' &&
            <>
              <Form.Item label="名称简写">
                <Input
                  value={course.abbreviation}
                  onChange={(e) => { return this.props.onChangeValue(e, 'abbreviation'); }}
                />
              </Form.Item>
              <Form.Item label="Icon">
                <Upload
                  accept="image/*"
                  listType="picture-card"
                  showUploadList={false}
                  customRequest={(opt) => { return this.props.onUpload(opt, 'icon'); }}
                  onRemove={() => { this.props.onChangeValue('', 'icon'); }}
                >
                  {
                    course?.icon ?
                      <img
                        style={{ maxWidth: 100 }}
                        src={`${course?.icon}?x-oss-process=image/resize,h_100,m_lfit`}
                      /> :
                      <div>
                        <PlusOutlined />
                        <div style={{ marginTop: 8 }}>上传</div>
                      </div>
                  }
                </Upload>
                {
                  course?.icon && <DeleteOutlined
                    style={{ fontSize: 20, position: 'absolute', left: 120, top: 40 }}
                    onClick={() => { this.props.onChangeValue('', 'icon'); }}
                  />
                }
              </Form.Item>
            </>
          }
          <Form.Item label="描述">
            <Input.TextArea
              autoSize={{ minRows: 3 }}
              value={course.description}
              onChange={(e) => { return this.props.onChangeValue(e, 'description'); }}
            />
          </Form.Item>
          <Form.Item label="Copilot头像">
            <Upload
              accept="image/*"
              listType="picture-card"
              showUploadList={false}
              customRequest={(opt) => { return this.props.onUpload(opt, 'logo'); }}
              onRemove={() => { this.props.onChangeValue('', 'logo'); }}
            >
              {
                course?.logo ?
                  <img style={{ maxWidth: 100 }} src={`${course?.logo}?x-oss-process=image/resize,h_100,m_lfit`} /> :
                  <div>
                    <PlusOutlined />
                    <div style={{ marginTop: 8 }}>上传</div>
                  </div>
              }
            </Upload>
            {
              course?.logo && <DeleteOutlined
                style={{ fontSize: 20, position: 'absolute', left: 120, top: 40 }}
                onClick={() => { this.props.onChangeValue('', 'logo'); }}
              />
            }
          </Form.Item>
          {
            ['course', 'spoken', 'follow', 'gpts'].includes(course?.type) &&
            <TTSSettings
              course={course}
              onChange={(value, key) => { return this.props.onChangeValue(value, key); }}
            />
          }
          <Form.Item label="含有公式">
            <Checkbox
              checked={course?.hasFormula}
              onChange={(e) => { return this.props.onChangeValue(e.target.checked, 'hasFormula'); }}
            />
          </Form.Item>
          <Form.Item label="H5跳转">
            <Select
              style={{ width: 120 }}
              value={course.extra?.serviceType}
              placeholder="必要时选择"
              onChange={(e) => { return this.onChangeExtraValue(e, 'serviceType'); }}
              options={
                [
                  { value: 'default', label: '课程' },
                  { value: 'essay', label: '作文' },
                  { value: 'follow', label: '跟读' },
                  { value: 'spoken', label: '口语' },
                  { value: 'gpts', label: 'GPTs' },
                ]
              }
            />
            {
              course.extra?.serviceType &&
              <Input
                addonBefore="跳转链接"
                style={{ marginTop: 5 }}
                value={course.extra?.redirectUrl}
                onChange={(e) => { return this.onChangeExtraValue(e, 'redirectUrl'); }}
              />
            }
          </Form.Item>
          {
            course.type === 'course' &&
            <Form.Item label=" " colon={false}>
              <Divider orientation="left" style={{ marginTop: 0 }}>角色设置</Divider>
              <Tabs>
                {
                  _.map(Configs.ASSISTANT_MAP, (v, k) => {
                    return (
                      <Tabs.TabPane tab={v} key={k}>
                        {this.renderAssistant(course?.prompts?.[k], k)}
                      </Tabs.TabPane>
                    );
                  })
                }
              </Tabs>
            </Form.Item>
          }
        </Form>
      </Drawer>

    );
  }
}
