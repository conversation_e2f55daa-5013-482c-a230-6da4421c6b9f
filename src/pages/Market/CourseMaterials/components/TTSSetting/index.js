import { Form, Input, InputNumber, Radio, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

const GPT_SOVITS = 'gpt-sovits';

export default class TTSSettings extends PureComponent {
  static propTypes = {
    course: PropTypes.object,
    onChange: PropTypes.func,
    speakers: PropTypes.array,
  }

  state = {
    tts: {},
    refs: [],
    selectedRefIndex: 0,
  }

  componentDidMount = async () => {
    const resp = await fetch(`https://video-clip.oss-cn-shanghai.aliyuncs.com/faas/html/tts_list.json?t=${Date.now()}`);
    const data = await resp.json();
    this.setState({ tts: data });

    const { course, speakers } = this.props;
    if (course?.ttsSettings?.provider === GPT_SOVITS) {
      speakers.forEach((x) => {
        if (x.speaker === course?.ttsSettings?.voice) {
          let selectedRefIndex = 0;
          x.refs.forEach(({ wavUrl }, index) => {
            if (wavUrl === course?.ttsSettings?.ref?.wavUrl) {
              selectedRefIndex = index;
            }
          });
          this.setState({ refs: x.refs, selectedRefIndex });
        }
      });
    }
  }

  onChangeProvider = (value) => {
    const { course, speakers } = this.props;
    let params = { ...course?.ttsSettings, provider: value };
    if (value !== GPT_SOVITS) {
      const tts = { voice: _.head(this.state.tts[value]).value };
      params = {
        ...params,
        ...tts,
      };
    } else {
      setTimeout(() => {
        this.onChangeSpeaker(speakers[0].speaker);
      });
    }
    return this.props.onChange(params, 'ttsSettings');
  }

  onChangeSpeaker = (value) => {
    const { course, speakers } = this.props;
    let ref = {};
    speakers.forEach((x) => {
      if (x.speaker === value) {
        this.setState({ refs: x.refs, selectedRefIndex: 0 });
        // eslint-disable-next-line prefer-destructuring
        [ref] = x.refs;
      }
    });
    return this.props.onChange({ ...course?.ttsSettings, ref, voice: value }, 'ttsSettings');
  }

  onChangeRef = (value) => {
    this.setState({ selectedRefIndex: value });
    const { course } = this.props;
    const { refs } = this.state;
    return this.props.onChange({ ...course?.ttsSettings, ref: refs[value] }, 'ttsSettings');
  }

  render = () => {
    const { course, speakers } = this.props;
    const { refs, selectedRefIndex } = this.state;
    const isOpenai = course?.ttsSettings?.provider === 'openai';
    return (
      <>
        <Form.Item label="音色模型">
          <Select
            style={{ width: 200 }}
            value={course?.ttsSettings?.provider}
            onChange={(value) => { return this.onChangeProvider(value); }}
          >
            <Select.Option value="openai">OPENAI</Select.Option>
            <Select.Option value="azure">AZURE</Select.Option>
            {
              speakers?.length > 0 && (
                <Select.Option value={GPT_SOVITS}>声音克隆</Select.Option>
              )
            }
          </Select>
        </Form.Item>
        {
          course?.ttsSettings?.provider === GPT_SOVITS ? (
            <>
              <Form.Item label="音色">
                <Select
                  style={{ width: 200 }}
                  value={course?.ttsSettings?.voice}
                  onChange={(value) => { return this.onChangeSpeaker(value); }}
                >
                  {
                    speakers.map((item) => {
                      return (
                        <Select.Option value={item.speaker}>{item.speaker}</Select.Option>
                      );
                    })
                  }
                </Select>
              </Form.Item>

              <Form.Item label="参考">
                <Radio.Group
                  value={selectedRefIndex}
                  onChange={(e) => { this.onChangeRef(e.target.value); }}
                >
                  {
                    refs.map((ref, index) => {
                      return (
                        <Radio value={index} style={{ transform: 'translateY(6px)' }}>
                          <Input value={ref.text} addonBefore={`参考${index + 1}.`} />
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            {
                              _.isUndefined(ref?.name) ?
                                '' :
                                <span style={{ fontWeight: 'bold' }}>
                                  {ref.name}&nbsp;
                                </span>
                            }
                            <audio style={{ height: 30, marginTop: 2, marginBottom: 10 }} controls src={ref.wavUrl} />
                          </div>
                        </Radio>
                      );
                    })
                  }
                </Radio.Group>
              </Form.Item>
            </>
          ) : (
            <>
              {
                isOpenai &&
                <Form.Item label="模型">
                  <Select
                    style={{ width: 200 }}
                    value={course?.ttsSettings?.model}
                    onChange={(value) => {
                      return this.props.onChange({ ...course?.ttsSettings, model: value }, 'ttsSettings');
                    }}
                  >
                    <Select.Option value="tts-1">tts-1</Select.Option>
                    <Select.Option value="tts-1-hd">tts-1-hd</Select.Option>
                  </Select>
                </Form.Item>
              }
              <Form.Item label="音色">
                <Select
                  style={{ width: 200 }}
                  value={course?.ttsSettings?.voice}
                  onChange={(value) => {
                    return this.props.onChange({ ...course?.ttsSettings, voice: value }, 'ttsSettings');
                  }}
                >
                  {(this.state.tts[course?.ttsSettings?.provider] || []).map((x) => {
                    return <Select.Option key={x.value} value={x.value}>{x.name}</Select.Option>;
                  })}
                </Select>
              </Form.Item>
              <Form.Item label="速度">
                <InputNumber
                  value={course?.ttsSettings?.speed}
                  step={0.05}
                  min={0.5}
                  max={2}
                  onChange={(value) => {
                    return this.props.onChange({ ...course?.ttsSettings, speed: value }, 'ttsSettings');
                  }}
                />
              </Form.Item>
            </>
          )
        }
      </>
    );
  }
}
