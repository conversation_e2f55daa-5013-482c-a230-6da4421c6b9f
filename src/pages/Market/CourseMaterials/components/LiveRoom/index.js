import { PlusOutlined } from '@ant-design/icons';
import { FilterBar, PaginationTable, Toast } from '~/components';
import { <PERSON><PERSON><PERSON>elper } from '~/engine';
import { But<PERSON>, Divider, Drawer, Form, Image, Input, Popconfirm, Select, Upload } from 'antd';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class LiveRoomTabPane extends PureComponent {
  static propTypes = {
    list: PropTypes.array,
    total: PropTypes.number,
    filter: PropTypes.object,
    pagination: PropTypes.object,
    onChange: PropTypes.func,
    fetchRooms: PropTypes.func,
    deleteRoom: PropTypes.func,
    createRoom: PropTypes.func,
    updateRoom: PropTypes.func,
  }

  state = {
    open: false,
    room: {},
  }

  onUpload = async (option, key) => {
    try {
      const url = await <PERSON><PERSON>H<PERSON><PERSON>.clipsUploadImage(option.file, (progress) => {
        const percent = Math.round((progress.loaded / progress.total) * 100);
        option.onProgress({ percent });
      });
      option.onSuccess();
      this.onChangeValue(url, key);
    } catch (e) {
      option.onError();
    }
  }

  onChangeFilterValue = (value, key) => {
    this.props.onChange({ roomFilter: { ...this.props.filter, [key]: value } });
  }

  onChangeValue = (e, key) => {
    const value = e.target ? e.target.value : e;
    this.setState({ room: { ...this.state.room, [key]: value } });
  }

  onSubmit = () => {
    const { room } = this.state;
    if (!room?.name) {
      Toast.error('直播间名称不能为空');
      return;
    }
    if (!room?.banner) {
      Toast.error('请上传Banner');
      return;
    }

    if (!room.uuid) {
      this.props.createRoom(this.state.room);
    } else {
      this.props.updateRoom(this.state.room);
    }
    this.setState({ open: false, room: {} });
  }

  renderDetailDrawer = () => {
    const { open, room } = this.state;
    return (
      <Drawer
        width="50vw"
        open={open}
        title={!room?.uuid ? '新增' : `${room?.name}-编辑`}
        onClose={() => { return this.setState({ open: false, room: {} }); }}
        extra={<Button type="primary" onClick={this.onSubmit}>保存</Button>}
      >
        <Form labelCol={{ span: 3 }} className="common-form">
          <Form.Item label="直播间名称">
            <Input
              value={room?.name}
              onChange={(e) => { return this.onChangeValue(e, 'name'); }}
            />
          </Form.Item>
          <Form.Item label="Banner">
            <Upload
              accept="image/*"
              listType="picture-card"
              showUploadList={false}
              customRequest={(opt) => { return this.onUpload(opt, 'banner'); }}
              onRemove={() => { this.onChangeValue('', 'banner'); }}
            >
              {
                room?.banner ?
                  <img
                    style={{ maxWidth: 100 }}
                    src={`${room?.banner}?x-oss-process=image/resize,h_100,m_lfit`}
                  /> :
                  <div>
                    <PlusOutlined />
                    <div style={{ marginTop: 8 }}>上传</div>
                  </div>
              }
            </Upload>
          </Form.Item>
          <Form.Item label="直播平台">
            <Select
              mode="multiple"
              value={room?.platforms}
              onChange={(value) => { return this.onChangeValue(value, 'platforms'); }}
            >
              <Select.Option value="douyin">抖音</Select.Option>
              <Select.Option value="shipinghao">视频号</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderColumns = () => {
    const columns = [
      { title: 'ID', dataIndex: 'uuid', key: 'uuid' },
      {
        title: '直播间',
        dataIndex: 'name',
        key: 'name',
        ellipsis: true,
        align: 'center',
      },
      {
        title: 'Banner',
        dataIndex: 'banner',
        key: 'banner',
        align: 'center',
        render: (txt) => { return <Image width={50} src={txt} />; },
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (txt) => { return moment(txt).format('YYYY-MM-DD HH:mm:ss'); },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        render: (txt, row) => {
          return (
            <>
              <a onClick={() => { return this.setState({ open: true, room: row }); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm title="确定删除吗？" onConfirm={() => { return this.props.deleteRoom(row.id); }}>
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
    return columns;
  }

  render = () => {
    return (
      <>
        <FilterBar
          canAdd
          onAdd={() => { return this.setState({ open: true, room: {} }); }}
          placeholder="请输入直播间名称"
          searchKeyWords={this.props.filter?.name}
          onChange={(value) => { return this.onChangeFilterValue(value, 'name'); }}
          onSearch={() => { this.props.fetchRooms({ page: 1 }); }}
        />
        <PaginationTable
          columns={this.renderColumns()}
          dataSource={this.props.list}
          pagination={this.props.pagination}
          totalDataCount={this.props.total}
          onPaginationChange={this.props.fetchRooms}
        />

        {this.state.open && this.renderDetailDrawer()}
      </>
    );
  }
}
