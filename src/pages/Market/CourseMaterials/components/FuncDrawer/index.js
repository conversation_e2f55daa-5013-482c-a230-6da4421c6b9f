import { Toast } from '~/components';
import { Button, Drawer, Form, Input } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class FuncDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    course: PropTypes.object,
    onSave: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    data: {},
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onSubmit = () => {
    const { data } = this.state;
    if (!data.name || !data.displayName || !data.description || !data.queryDescription || !data.countDescription) {
      Toast.show('参数不能为空', Toast.Type.WARNING);
      return;
    }


    this.props.onSave({ ...data, courseUuid: this.props.course.courseUuid });
    this.props.onClose();
  }


  render = () => {
    const { open, course } = this.props;
    const { data } = this.state;

    return (
      <Drawer
        width="50vw"
        open={open}
        title="另存为函数"
        onClose={this.props.onClose}
        extra={<Button type="primary" onClick={() => { return this.onSubmit(); }}>保存</Button>}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} className="common-form">
          <Form.Item label="课程名" >{course?.name}</Form.Item>
          <Form.Item label="函数名" help="仅支持字母、下划线, eg.: A_b_c" required>
            <Input value={data?.name} onChange={(e) => { return this.onChangeValue(e, 'name'); }} />
          </Form.Item>
          <Form.Item label="别名" help="支持中文" required>
            <Input value={data?.displayName} onChange={(e) => { return this.onChangeValue(e, 'displayName'); }} />
          </Form.Item>
          <Form.Item label="描述" required>
            <Input value={data?.description} onChange={(e) => { return this.onChangeValue(e, 'description'); }} />
          </Form.Item>
          <Form.Item label="查询描述" required>
            <Input
              value={data?.queryDescription}
              onChange={(e) => { return this.onChangeValue(e, 'queryDescription'); }}
            />
          </Form.Item>
          <Form.Item label="统计描述" required>
            <Input
              value={data?.countDescription}
              onChange={(e) => { return this.onChangeValue(e, 'countDescription'); }}
            />
          </Form.Item>
        </Form>
      </Drawer>
    );
  }
}
