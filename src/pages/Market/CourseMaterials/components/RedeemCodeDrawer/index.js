import { Toast } from '~/components';
import { <PERSON><PERSON>, Drawer, Select, Table, Tag } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import QRCode from 'qrcode.react';
import React, { PureComponent } from 'react';

export default class RedeemCodeDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    data: PropTypes.object,
    codes: PropTypes.array,
    onGenCode: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    originalData: [],
    dataSource: [],
  }

  componentDidMount = () => {
    const dataSource = this.props.codes || [];
    this.setState({ dataSource, originalData: dataSource });
  }

  onGenCode = async () => {
    const code = await this.props.onGenCode(this.props.data);
    const dataSource = [...this.state.dataSource, code];
    this.setState({ dataSource, originalData: dataSource });
  }

  onChangeFilter = (value) => {
    if (_.isUndefined(value)) {
      this.setState({ dataSource: this.state.originalData });
    }

    const dataSource = this.state.originalData.filter((x) => {
      return _.isEmpty(x.redeemedTime) !== value;
    });

    this.setState({ dataSource });
  }

  renderColumns = () => {
    return [
      { title: '序号', align: 'center', dataIndex: 'id', key: 'id' },
      { title: '兑换码', align: 'center', dataIndex: 'code', key: 'code' },
      {
        title: '兑换二维码',
        align: 'center',
        dataIndex: 'url',
        key: 'url',
        render: (txt, row) => {
          if (!_.isUndefined(row.redeemedTime)) {
            return <Tag color="red" >已使用</Tag>;
          }

          if (txt) { return <QRCode value={txt} />; }
          return '-';
        },
      },
      {
        title: '兑换链接',
        align: 'center',
        dataIndex: 'url',
        key: 'url',
        render: (txt, row) => {
          if (!_.isUndefined(row.redeemedTime)) {
            return <Tag color="red" >已使用</Tag>;
          }

          if (txt) {
            return (
              <Button
                type="link"
                onClick={async () => {
                  await navigator.clipboard.writeText(txt);
                  Toast.show('已复制到剪贴板', Toast.Type.SUCCESS);
                }}
              >复制链接
              </Button>
            );
          }
          return '-';
        },
      },
      { title: '兑换人', align: 'center', dataIndex: 'redeemedBy', key: 'redeemedBy' },
      {
        title: '兑换时间',
        dataIndex: 'redeemedTime',
        key: 'redeemedTime',
        align: 'center',
        render: (txt) => {
          return (txt || '').length ? moment(txt).format('YYYY-MM-DD HH:mm:ss') : '-';
        },
      },
    ];
  }

  render = () => {
    return (
      <Drawer
        width="50vw"
        open={this.props.open}
        onClose={this.props.onClose}
        title="兑换码"
        extra={<Button type="primary" onClick={this.onGenCode}>生成兑换码</Button>}
      >

        <Select
          allowClear
          style={{ width: 200, marginBottom: 20 }}
          placeholder="使用状态"
          onChange={(value) => { return this.onChangeFilter(value); }}
        >
          <Select.Option value={false}>未使用</Select.Option>
          <Select.Option value>已使用</Select.Option>
        </Select>

        <Table
          dataSource={this.state.dataSource}
          columns={this.renderColumns()}
          pagination={false}
        />
      </Drawer>
    );
  }
}
