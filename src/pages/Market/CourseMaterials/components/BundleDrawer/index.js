import { CopyOutlined } from '@ant-design/icons';
import { Toast } from '~/components';
import { But<PERSON>, Di<PERSON>r, Drawer, Form, Input, InputNumber, Select, Switch } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class BundleDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    bundle: PropTypes.object,
    courses: PropTypes.array,
    onChangeValue: PropTypes.func,
    onSave: PropTypes.func,
    onClose: PropTypes.func,
  }

  componentDidMount = () => {
  }

  onChangeExtraValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const extra = { ...this.props.bundle.extra, [key]: value };

    this.props.onChangeValue(extra, 'extra');
  }

  render = () => {
    const { open, bundle } = this.props;

    return (
      <Drawer
        width="50vw"
        open={open}
        title={!bundle?.uuid ? '新增' : `${bundle?.name}-编辑`}
        onClose={this.props.onClose}
        extra={<Button type="primary" onClick={this.props.onSave}>保存</Button>}
      >
        <Form labelCol={{ span: 3 }} className="common-form">
          <Form.Item label="分组名称">
            <Input
              value={bundle.name}
              onChange={(e) => { return this.props.onChangeValue(e, 'name'); }}
            />
          </Form.Item>
          <Form.Item label="价格（元）">
            <InputNumber
              value={bundle.price}
              step={0.1}
              min={0}
              max={9999}
              onChange={(e) => { return this.props.onChangeValue(e, 'price'); }}
            />
          </Form.Item>
          <Form.Item label="售卖状态">
            <Switch
              checkedChildren="售卖中"
              unCheckedChildren="未售卖"
              checked={bundle.onSale}
              onChange={(e) => { return this.props.onChangeValue(e, 'onSale'); }}
            />
          </Form.Item>
          <Form.Item label="关联课程">
            <Select
              mode="multiple"
              allowClear
              style={{ width: '100%' }}
              defaultValue={bundle.courseUuids || []}
              onChange={(e) => { return this.props.onChangeValue(e, 'courseUuids'); }}
              options={this.props.courses}
            />
          </Form.Item>
          <Form.Item label="关联平台">
            <Select
              style={{ width: 120 }}
              defaultValue={bundle.salePlatform}
              onChange={(e) => { return this.props.onChangeValue(e, 'salePlatform'); }}
              options={[{ value: 'dushu', label: '读书会' }]}
            />
          </Form.Item>
          <Form.Item label="平台ID">
            <Input
              value={bundle.salePlatformId}
              onChange={(e) => { return this.props.onChangeValue(e, 'salePlatformId'); }}
            />
          </Form.Item>
          <Divider />
          <Form.Item label="兑换链接">
            {_.map(bundle?.redeemMap || {}, (url, label) => {
              return (
                <Input
                  key={label}
                  value={url}
                  addonBefore={label}
                  addonAfter={<CopyOutlined onClick={
                    async () => {
                      await navigator.clipboard.writeText(url);
                      Toast.show('复制成功', Toast.Type.SUCCESS);
                    }}
                  />}
                />
              );
            })}
          </Form.Item>
        </Form>
      </Drawer>
    );
  }
}
