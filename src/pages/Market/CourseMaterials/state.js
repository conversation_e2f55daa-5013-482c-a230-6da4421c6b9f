import Configs from '~/consts';
import { AiTeacher, ChatBot } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'COURSE_MATERIALS/SET_STATE';
const CLEAR_STATE = 'COURSE_MATERIALS/CLEAR_STATE';
export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchCourses = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination, filter } = getState().marketCourseMaterials;
    const searchParams = {
      type: params.type || filter.type,
      name: params.name || filter.name,
      bundleUuid: params.uuid || filter.uuid,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };

    const { items, total } = await AiTeacher.fetchCourses(searchParams);
    dispatch(setState({
      list: items,
      total,
      pagination: {
        pageIndex: searchParams['pagination.pageIndex'],
        pageSize: searchParams['pagination.pageSize'],
        orderBy: searchParams['pagination.orderBy'],
      },
    }));
  };
};

export const fetchAllCourses = () => {
  return async (dispatch) => {
    const searchParams = {
      'pagination.pageIndex': 0,
      'pagination.pageSize': 1000,
    };

    const { items } = await AiTeacher.fetchCourses(searchParams);
    dispatch(setState({
      courses: items.map((item) => {
        return {
          label: item.name,
          value: item.courseUuid,
        };
      }),
    }));
  };
};

export const getDefaultAssistantPrompt = (params) => {
  return async () => {
    const data = await AiTeacher.getDefaultAssistantPrompt(params);
    return data;
  };
};

export const fetchCourse = (itemId) => {
  return async () => {
    const data = await AiTeacher.fetchCourse(itemId);
    return data;
  };
};

export const createCourse = (params) => {
  return async (dispatch) => {
    await AiTeacher.createCourse(params);
    dispatch(fetchCourses());
    dispatch(fetchAllCourses());
  };
};

export const updateCourse = (params, needFetch = true) => {
  return async (dispatch, getState) => {
    const { list } = getState().marketCourseMaterials;

    await AiTeacher.updateCourse(params);
    if (needFetch) {
      dispatch(fetchCourses());
      dispatch(fetchAllCourses());
    } else {
      const items = list.map((item) => {
        if (item.id === params.id) { return { ...item, ...params }; }
        return item;
      });
      dispatch(setState({ list: items }));
    }
  };
};

export const deleteCourse = (itemId) => {
  return async (dispatch) => {
    await AiTeacher.deleteCourse(itemId);
    dispatch(fetchCourses());
    dispatch(fetchAllCourses());
  };
};

export const fetchBundles = (params = {}) => {
  return async (dispatch, getState) => {
    const { bundlePagination } = getState().marketCourseMaterials;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || bundlePagination.pageIndex,
      'pagination.pageSize': params.pageSize || bundlePagination.pageSize,
      'pagination.orderBy': params.orderBy || bundlePagination.orderBy,
      name: params.name,
      onSale: params.onSale,
    };

    const { items, total } = await AiTeacher.fetchBundles(searchParams);
    dispatch(setState({
      bundles: items,
      bundlesTotal: total,
      bundlePagination: {
        pageIndex: searchParams['pagination.pageIndex'],
        pageSize: searchParams['pagination.pageSize'],
        orderBy: searchParams['pagination.orderBy'],
      },
    }));
  };
};

export const fetchAllBundles = () => {
  return async (dispatch) => {
    const searchParams = {
      'pagination.pageIndex': 1,
      'pagination.pageSize': 1000,
    };

    const { items } = await AiTeacher.fetchBundles(searchParams);
    dispatch(setState({
      allBundles: items.map((item) => {
        return {
          label: item.name,
          value: item.uuid,
        };
      }),
    }));
  };
};

export const createBundle = (params) => {
  return async (dispatch) => {
    const { id } = await AiTeacher.createBundle(params);
    if (params.courseUuids) {
      await AiTeacher.bundleCourses({
        id, courseUuids: params.courseUuids,
      });
    }
    dispatch(fetchBundles());
    dispatch(fetchAllBundles());
  };
};

export const updateBundle = (params) => {
  return async (dispatch) => {
    await AiTeacher.updateBundle(params);
    if (params.courseUuids) {
      await AiTeacher.bundleCourses(params);
    }
    dispatch(fetchBundles());
    dispatch(fetchAllBundles());
  };
};

export const getBundle = (itemId) => {
  return async (dispatch, getState) => {
    const { courses } = getState().marketCourseMaterials;
    const data = await AiTeacher.getBundle(itemId);
    const { courseUuids, redeemUrls } = await AiTeacher.getBundleCourses(itemId);
    const redeemMap = {};
    _.mapValues(_.keyBy(courses, 'value'), (obj) => {
      if (redeemUrls[obj.value]) {
        redeemMap[obj.label] = redeemUrls[obj.value];
      }
    });
    return { ...data, courseUuids, redeemMap };
  };
};

export const deleteBundle = (itemId) => {
  return async (dispatch) => {
    await AiTeacher.deleteBundle(itemId);
    dispatch(fetchBundles());
    dispatch(fetchAllBundles());
  };
};

export const fetchRedeemCodes = (params = {}) => {
  return async () => {
    const { items } = await AiTeacher.fetchRedeemCodes({ ...Configs.ALL_PAGE_PARAMS, ...params });
    return items;
  };
};

export const generateRedeemCodes = (params) => {
  return async () => {
    const redeem = await AiTeacher.generateRedeemCodes(params);
    return redeem;
  };
};

export const fetchRooms = (params = {}) => {
  return async (dispatch, getState) => {
    const { roomPagination, roomFilter } = getState().marketCourseMaterials;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || roomPagination.pageIndex,
      'pagination.pageSize': params.pageSize || roomPagination.pageSize,
      'pagination.orderBy': params.orderBy || roomPagination.orderBy,
      name: roomFilter.name || params.name,
    };

    const { items, total } = await AiTeacher.fetchLiveRooms(searchParams);
    dispatch(setState({
      rooms: items,
      roomTotal: total,
      roomPagination: {
        pageIndex: searchParams['pagination.pageIndex'],
        pageSize: searchParams['pagination.pageSize'],
        orderBy: searchParams['pagination.orderBy'],
      },
    }));
  };
};

export const createRoom = (params) => {
  return async (dispatch) => {
    await AiTeacher.createLiveRoom(params);
    dispatch(fetchRooms());
  };
};

export const updateRoom = (params) => {
  return async (dispatch) => {
    await AiTeacher.updateLiveRoom(params);
    dispatch(fetchRooms());
  };
};

export const deleteRoom = (itemId) => {
  return async (dispatch) => {
    await AiTeacher.deleteLiveRoom(itemId);
    dispatch(fetchRooms());
  };
};

export const addCourseFunc = (params = {}) => {
  return async () => {
    await ChatBot.addCourseFunc(params);
  };
};

const _getInitState = () => {
  return {
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    filter: { type: 'exercise' },
    list: [],
    bundlesTotal: 0,
    bundlePagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    bundles: [],
    courses: [],
    allBundles: [],
    rooms: [],
    roomTotal: 0,
    roomFilter: {},
    roomPagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
