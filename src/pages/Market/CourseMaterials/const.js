/* eslint-disable max-len */

export default class Configs {
  static decimal = {
    0: '⓪',
    1: '①',
    2: '②',
    3: '③',
    4: '④',
    5: '⑤',
    6: '⑥',
    7: '⑦',
    8: '⑧',
    9: '⑨',
    10: '⑩',
    11: '⑪',
    12: '⑫',
    13: '⑬',
    14: '⑭',
    15: '⑮',
    16: '⑯',
    17: '⑰',
    18: '⑱',
    19: '⑲',
    20: '⑳',
    21: '㉑',
    22: '㉒',
    23: '㉓',
    24: '㉔',
    25: '㉕',
    26: '㉖',
    27: '㉗',
    28: '㉘',
    29: '㉙',
    30: '㉚',
    31: '㉛',
    32: '㉜',
    33: '㉝',
    34: '㉞',
    35: '㉟',
    36: '㊱',
    37: '㊲',
    38: '㊳',
    39: '㊴',
    40: '㊵',
    41: '㊶',
    42: '㊷',
    43: '㊸',
    44: '㊹',
    45: '㊺',
    46: '㊻',
    47: '㊼',
    48: '㊽',
    49: '㊾',
    50: '㊿',
  }

  static FORMAT_ORIGINTEXT_PROMPT = `You are an expert in education.

我给你的正文是直接从PDF 复制进来的文字，可能换行和格式有误，你需要根据语义上下文重新修正格式和断句以及换行等。

切记，你需要一步一步处理，遵循规则：
1. 务必保留批注文字(用「」括起来的文字)和对应的编号(带圈的数字，如①)
2. 输出的正文符合markdown语法, no Horizontal Rule`;

  static FORMAT_ORIGINTEXT_PROMPT_EXTRA = '3. 该正文较长，需要将修正后的文字里标记成2段，每段之间不重不漏，第一段和第二段之间用<<break>>划分'

  static COURSE_TYPE_MAP = {
    course: '课程',
    exercise: '题库',
    follow: '跟读',
    gpts: 'GPTs',
    spoken: '口语',
  };

  static COURSE_COLORS_MAP = {
    course: '#FFA500',
    exercise: '#00BFFF',
    follow: 'green',
    gpts: '#FF0000',
    spoken: '#FFC0CB',
  };

  static EDIT_DISABLED = ['gpts', 'spoken'];

  static REDIRECT_MAP = { gpts: '/consultation/', spoken: '/spoken/' }

  static TYPE_MAP = {
    knowledge: '知识点',
    exercise: '练习题',
    follow: '跟读',
    text: '文本',
    image: '图片',
    video: '视频',
  };

  static SUB_TYPE_MAP = {
    general_qa: '通用习题',
    choice: '选择题',
    fillin: '填空题',
    qna: '问答题',
    essay: '作文题',
  }

  static TOOLBAR_COMMANDS = [
    ['bold', 'italic', 'strikethrough'],
    ['unordered-list', 'ordered-list'],
    ['note', 'recognition'],
  ];

  static ASSISTANT_MAP = {
    slideAssistant: '生成课件列表',
    presentationAssistant: '课件 生成讲解',
    exercisePresentationAssistant: '题目 生成讲解',
    genPresentationNoteAssistant: '课件+标注 生成讲解',
    genTranscriptAssistant: '生成音频稿',
    slideKeypointAssistant: '生成PPT',
    answerExerciseAssistant: '答题角色',
    answerQuestionAssistant: '答疑角色',
    contentFormatAssistant: '格式化角色',
    essayReviewAssistant: '作文批改',
    keywordAssistant: '课件关键词',
    sloganAssistant: '课件标语',
  };

  static KNOWLEDGE_MAP = {
    presentationAssistant: '课件 生成讲解',
    genPresentationNoteAssistant: '课件+标注 生成讲解',
    genTranscriptAssistant: '生成音频稿',
    slideKeypointAssistant: '生成PPT',
  };

  static QUESTION_MAP = {
    exercisePresentationAssistant: '题目 生成讲解',
    slideKeypointAssistant: '生成PPT',
  };

  static COURSE_TAGS = {
    course: '课程',
    exercise: '题库',
  }
}
