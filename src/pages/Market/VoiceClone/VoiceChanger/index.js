import { Toast } from '~/components';
import { Sessions } from '~/engine';
import { Platform } from '~/plugins';
import { Button, Drawer, Form, Input, Popconfirm, Select, message } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.marketVoiceChanger;
  },
  actions,
)
export default class VoiceChanger extends Component {
  static propTypes = {
    tones: PropTypes.object.isRequired,
    speakers: PropTypes.array.isRequired,
    jobTrigger: PropTypes.func.isRequired,
    fetchVoiceTones: PropTypes.func.isRequired,
    fetchVoiceCloneSpeakers: PropTypes.func.isRequired,
    history: PropTypes.object.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    podcast: {},
    trainNames: {},
    speakers: {},
    tones: {},
  }

  componentDidMount = async () => {
    await this.props.fetchVoiceTones();
    await this.props.fetchVoiceCloneSpeakers();
    this.setState({ tones: this.props.tones });
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  checkStatus = async (podcastId) => {
    const url = `https://video-clip.oss-cn-shanghai.aliyuncs.com/audios/podcast/asr/${podcastId}.json?v=${Date.now()}`;
    let response;
    return new Promise(async (resolve) => {
      do {
        response = await fetch(url); // eslint-disable-line no-await-in-loop
        if (response.ok) {
          const data = await response.json(); // eslint-disable-line no-await-in-loop
          const { title, taskResult } = data;
          const sentences = _.map(taskResult.Sentences, (s, i) => { return { ...s, idx: i }; });
          resolve({ title, sentences });
        }
        await new Promise((s) => { return setTimeout(s, 10000); }); // eslint-disable-line no-await-in-loop
      } while (!response.ok);
    });
  }

  onAnalyze = async (url) => {
    let podcastId = '';
    const qryStr = url.split('?')[1];
    if (qryStr) {
      podcastId = qs.parse(qryStr).v;
    } else {
      podcastId = _.findLast(url.split('/'), (x) => { return !_.isEmpty(x); });
    }
    const params = {
      region: 'ap-southeast-1',
      job_name: 'ytdl',
      job_type: 'asr',
      job_data: { url: `https://music.youtube.com/watch?v=${podcastId}` },
    };
    message.open({ content: '正在分析，请稍后...', duration: 0 });
    await this.props.jobTrigger(params);
    const obj = await this.checkStatus(podcastId);
    const ids = _.uniq(_.map(obj.sentences, 'SpeakerId'));
    const speakerIds = [];
    ids.forEach((s) => {
      const items = obj.sentences.filter((x) => { return x.SpeakerId === s; });
      let total = 0;
      items.forEach((i) => {
        total += (i.EndTime - i.BeginTime);
      });
      speakerIds.push({ id: s, total: _.floor(total / (1000 * 60), 2) });
    });

    this.setState({ podcast: obj, speakerIds, podcastId });
    message.destroy();
  }

  onShowAuditionDrawer = ({ id }) => {
    const { sentences } = this.state.podcast;
    const items = sentences.filter((x) => { return x.SpeakerId === id; });
    this.setState({ auditions: items, auditionVisible: true });
  }

  onDeleteAudition = (idx) => {
    const { auditions } = this.state;
    const items = auditions.filter((x) => { return x.idx !== idx; });
    this.setState({ auditions: items });
  }

  onTrain = async () => {
    const { auditions, trainName, tones } = this.state;
    if (_.isEmpty(trainName)) {
      Toast.show('请输出训练名称', Toast.Type.ERROR);
      return;
    }
    const identifier = window.pinyinPro.pinyin(trainName, { toneType: 'none' }).split(' ').join('');
    if (!_.isUndefined(tones[identifier])) {
      Toast.show('训练名称已存在', Toast.Type.ERROR);
      return;
    }
    const newTones = { ...tones, [identifier]: trainName };
    const idxs = _.map(auditions, 'idx');

    const params = {
      region: 'cn-shanghai',
      job_name: 'podcast',
      job_type: 'trainV2',
      job_data: {
        podcastId: this.state.podcastId,
        trainName,
        identifier,
        idxs,
        env: Platform.isProd() ? 'prod' : 'stg',
        token: Sessions.getToken(),
      },
    };

    await this.props.jobTrigger(params);
    this.setState({ tones: newTones, auditionVisible: false });
  }

  onCloneVoice = () => {
    const { speakerIds, podcastId, speakers, trainNames } = this.state;
    if (_.isEmpty(speakers) || _.isEmpty(trainNames) ||
      _.some(speakerIds, (x) => { return _.isEmpty(speakers[x.id]) || _.isEmpty(trainNames[x.id]); })) {
      Toast.show('请先选择音色', Toast.Type.ERROR);
      return;
    }
    const tones = {}; // cn-shanghai/functions/podcast-tone
    _.map(trainNames, (v, k) => { tones[k] = _.last(v); });
    this.props.jobTrigger({
      region: 'cn-shanghai',
      job_name: 'podcast',
      job_type: 'tone',
      job_data: {
        podcastId,
        speakers,
        tones,
        env: Platform.isProd() ? 'prod' : 'stg',
        token: Sessions.getToken(),
      },
    });
    Toast.show('正在克隆并变声，请稍后...', Toast.Type.INFO);
    this.props.history.goBack();
  }

  // 试听 Drawer
  renderAuditionDrawer = () => {
    const { podcastId, auditionVisible, auditions } = this.state;
    return (
      <Drawer
        width="30vw"
        title="试听"
        placement="right"
        closable={false}
        onClose={() => { return this.setState({ auditionVisible: false }); }}
        visible={auditionVisible}
        extra={
          <div style={{ display: 'flex' }}>
            <Input
              width={200}
              value={this.state.trainName}
              onChange={(e) => {
                return this.setState({ trainName: e.target.value });
              }}
            />
            <Popconfirm
              title="打标质量会影响训练结果 请确认是否开始训练？"
              onConfirm={() => { return this.onTrain(); }}
              okText="确定"
              cancelText="取消"
            >
              <Button type="primary">开始训练</Button>
            </Popconfirm>
          </div>
        }
      >
        {
          auditions.map((x) => {
            return (
              <div style={{ marginBottom: 5, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                {x.idx}.
                <audio
                  controls
                  style={{ height: 20 }}
                  src={`https://video-clip.oss-cn-shanghai.aliyuncs.com/audios/podcast/${podcastId}/${x.idx}.mp3`}
                />

                <Button size="small" type="link" onClick={() => { return this.onDeleteAudition(x.idx); }}>
                  删除
                </Button>
              </div>
            );
          })
        }
      </Drawer>
    );
  }

  render = () => {
    const { speakers } = this.props;
    const { podcast, speakerIds } = this.state;
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 14 }}>
          <Form.Item label="播客地址">
            <Input.Search
              defaultValue="https://music.youtube.com/podcast/X-P047aj3HE"
              enterButton="分析"
              onSearch={this.onAnalyze}
            />
          </Form.Item>
          {
            !_.isEmpty(podcast) &&
            <>
              <Form.Item label="分析结果">
                <div style={{ maxHeight: '50vh', overflow: 'auto' }}>
                  {
                    (podcast?.sentences).map((s) => {
                      return (
                        <Input
                          style={{ marginBottom: 5 }}
                          addonBefore={`${s.idx}|发言人${s.SpeakerId}`}
                          addonAfter={<div style={{ width: 80 }}>{`${(s.EndTime - s.BeginTime) / 1000} s`}</div>}
                          value={s.Text}
                        />
                      );
                    })
                  }
                </div>
              </Form.Item>
              <Form.Item label="音色" >
                {
                  (speakerIds || []).map((x) => {
                    return (
                      <Input.Group style={{ marginBottom: 5 }}>
                        <Button style={{ width: 120 }}>发言人{x.id} 使用</Button>
                        <Select
                          style={{ width: 200 }}
                          onChange={(e) => {
                            if (_.isEmpty(e)) return;
                            this.setState({ speakers: { ...this.state.speakers, [x.id]: e } });
                          }}
                        >
                          {
                            speakers.map((s) => {
                              return <Select.Option value={s.speaker}>{s.speaker}</Select.Option>;
                            })
                          }
                        </Select>
                        <Button>的声音克隆,  并变声为</Button>
                        <Select
                          mode="tags"
                          style={{ width: 200 }}
                          value={this.state.trainNames[x.id]}
                          onChange={(e) => {
                            this.setState({
                              trainNames: { ...this.state.trainNames, [x.id]: !_.isEmpty(e) ? [_.last(e)] : undefined },
                            });
                          }}
                        >
                          {_.map(this.state.tones, (v, k) => {
                            return <Select.Option value={k}>{v}</Select.Option>;
                          })}
                        </Select>
                        <Button style={{ width: 100 }}>共{x.total}分钟</Button>
                        <Button onClick={() => { return this.onShowAuditionDrawer(x); }}>打标</Button>
                      </Input.Group>
                    );
                  })
                }
              </Form.Item>
              <Button
                style={{ float: 'right', marginTop: -30 }}
                type="primary"
                onClick={this.onCloneVoice}
              >
                克隆并变声
              </Button>
            </>
          }
        </Form>
        {this.state.auditionVisible && this.renderAuditionDrawer()}
      </div>
    );
  }
}

export {
  reducer,
};
