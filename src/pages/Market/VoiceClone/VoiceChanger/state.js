import Configs from '~/consts';
import { Market } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'VOICE_CHANGER/SET_STATE';
const CLEAR_STATE = 'VOICE_CHANGER/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const jobTrigger = (params) => {
  return () => {
    return Market.jobTrigger(params);
  };
};

export const addVoiceTone = (params) => {
  return () => {
    return Market.addVoiceTone(params);
  };
};

export const importVoiceClone = (params) => {
  return async () => {
    await Market.importVoiceClone(params);
  };
};

export const fetchVoiceCloneSpeakers = () => {
  return async (dispatch) => {
    const result = await Market.fetchVoiceCloneSpeakers();
    dispatch(setState({ speakers: _.values(result) }));
  };
};

export const fetchVoiceTones = () => {
  return async (dispatch) => {
    const { items } = await Market.fetchVoiceTones({ ...Configs.ALL_PAGE_PARAMS, status: 'done' });
    const tones = {};
    items.forEach((x) => { tones[x.identifier] = x.name; });
    dispatch(setState({ tones }));
  };
};

const _getInitState = () => {
  return {
    speakers: [],
    tones: {},
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
