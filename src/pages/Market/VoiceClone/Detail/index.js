/* eslint-disable max-lines */
import {
  DeleteOutlined,
  DownCircleOutlined,
  EditOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  PlusCircleOutlined,
  RedoOutlined,
  SwapOutlined,
} from '@ant-design/icons';
import { Toast } from '~/components';
import Engine, { Accounts, <PERSON>yunHelper, ChatBot } from '~/engine';
import Sessions from '~/engine/Sessions';
import { Platform } from '~/plugins';
import {
  Button,
  Collapse,
  Divider,
  Drawer,
  Form,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Select,
  Spin,
  Switch,
  Table,
} from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { Component } from 'react';
import { connect } from 'react-redux';

// import Azure from './azure';
import reducer, * as actions from './state';

const DEFAULT_AZURE_RATE_MAP = { pitchRate: 0.85, speechRate: 1.2 };
@connect(
  (state) => {
    return state.marketVoiceCloneDetail;
  },
  actions,
)
export default class VoiceCloneDetail extends Component {
  static propTypes = {
    detail: PropTypes.object,
    tones: PropTypes.object,
    speakers: PropTypes.array,
    getVoiceClone: PropTypes.func.isRequired,
    updateVoiceTone: PropTypes.func.isRequired,
    createVoiceClone: PropTypes.func.isRequired,
    removeVoiceClone: PropTypes.func.isRequired,
    insertVoiceClone: PropTypes.func.isRequired,
    regenVoiceClone: PropTypes.func.isRequired,
    updateVoiceClone: PropTypes.func.isRequired,
    updateVoiceCloneSpeed: PropTypes.func.isRequired,
    updateVoiceCloneOffset: PropTypes.func.isRequired,
    fetchVoiceCloneSpeakers: PropTypes.func.isRequired,
    fetchVoiceTones: PropTypes.func.isRequired,
    match: PropTypes.object.isRequired,
    setState: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    sourceContent: '',
    flowId: Platform.isProd() ? '8Z2UBZKVQTQesr2X01Cw3e' : '7SfF4tZAQvME2Ku80SvxFU',
    isEdit: false,
    autoplay: true,
    rewriteLoading: false,
    usernames: [],
    audioUrls: [],
    azureSpeakers: [],
    azureSpeakersMap: [],
    playIndex: {},
    userToneMap: {},
    userVoiceMap: {},
    azureMap: {},
    userSetting: {},
    sentence: {},
    shortAudioObj: {},
  }

  constructor(props) {
    super(props);
    this.audios = [];
    this.timeouts = [];
  }

  componentDidMount = async () => {
    const { id } = this.props.match.params;

    await this.props.fetchVoiceTones();
    await this.props.fetchVoiceCloneSpeakers();

    if (!_.isUndefined(id)) {
      await this.props.getVoiceClone(id);
      this.initDetail();
    }
    this.getAbbreviations();
    this.fetchShortAudio();
    this.audio = new Audio();
    const azureSpeakers = await Accounts.fetchTtsSpeakers();
    this.setState({ azureSpeakers,
      azureSpeakersMap: azureSpeakers.map((item) => {
        return item.value;
      }) });
  }

  componentWillUnmount = () => {
    this.audio?.pause();
    this.props.clearState();
    this.onPauseAll();
  }

  fetchShortAudio = async () => { // eslint-disable-next-line
    const resp = await fetch(`https://video-clip.oss-cn-shanghai.aliyuncs.com/faas/speakers/short.json?v=${new Date().valueOf()}`);
    const data = await resp.json();
    this.setState({ shortAudioObj: data });
  }

  initDetail = () => {
    const { sentences, contentMeta } = this.props.detail;
    const { Sentences } = JSON.parse(contentMeta?.sourceContent || '{}');
    let sourceContent = '';
    (Sentences || []).forEach((s) => {
      sourceContent += `Speaker ${s.SpeakerId}: ${s.Text}\n`;
    });

    this.setState({
      isEdit: true,
      sourceContent,
      audioUrls: _.map(_.flatMap(_.map(sentences, 'shots')), 'audioUrl'),
      usernames: _.uniq(_.map(sentences, 'speaker')),
    }, this.onAnalysis);
  }

  // 判断 userVoiceMap key value 是否一致
  // 如果不一致, 需要根据 key value map关系 将 content 内容中[user]：: 替换
  // 如果一致, 直接返回 content
  // 小B: 你好，小A，谢谢你的邀请，很高兴能和大家分享我的相亲经历。
  // 小A: 小B，你是什么时候开始相亲的呢？你是怎么接触到相亲这个方式的呢？
  // let newText = text.replace(/小B[:：]/g, '替换的文本');
  // code by copilot
  formatContent = (content) => {
    const { userVoiceMap, usernames } = this.state;
    const keys = _.keys(userVoiceMap);
    const values = _.values(userVoiceMap);
    if (_.isEqual(keys, values)) return content;

    let newText = content;
    usernames.forEach((name) => {
      const speaker = userVoiceMap[name];
      newText = newText.replace(new RegExp(`${name}[：:]`, 'g'), `${speaker}：`);
    });
    return newText;
  }
  getAbbreviations = async () => { // eslint-disable-next-line
    const resp = await fetch(`https://video-clip.oss-cn-shanghai.aliyuncs.com/models/GPT-SoVITS/engdict-hot.rep?v=${new Date().valueOf()}`);
    const blob = await resp.blob();
    await new Promise((resolve, reject) => { // eslint-disable-line
      const reader = new FileReader();
      reader.onload = (e) => {
        const lines = e.target.result.split('\n');
        const abbreviations = [];
        lines.forEach((line) => {
          const [abbr, ...rest] = line.split(' ');
          abbreviations.push({ abbr, text: rest.join(' ') });
        });
        this.setState({ abbreviations });
        resolve();
      };
      reader.readAsText(blob);
    });
  }

  setAbbreviations = async (abbreviations) => {
    const blob = new Blob([abbreviations.map((x) => {
      return `${x.abbr} ${x.text}`;
    }).join('\n')], { type: 'text/plain' });
    await AliyunHelper.uploadImageBlob(blob, () => { }, 'models/GPT-SoVITS/engdict-hot.rep');
  }

  onAddAbbr = async (value) => {
    // 判断是都已经存在
    const { abbreviations } = this.state;
    if (abbreviations.find((x) => { return x.abbr === value; })) {
      Toast.show('已经存在', Toast.Type.ERROR);
      return;
    }
    Engine.showLoading();
    const { choices } = await ChatBot.directChatCompletion({
      model: 'gpt-4-turbo-preview',
      temperature: 1,
      top_p: 1,
      n: 1,
      stream: false,
      stop: '',
      max_tokens: 2048,
      presence_penalty: 0,
      frequency_penalty: 0,
      messages: [
        { role: 'system', content: '输出对应的ARPAbet, 仅输出ARPAbet.' },
        { role: 'user', content: value },
      ],
    });

    const newAbbr = { abbr: value, text: choices[0].message.content };
    await this.setState({ abbreviations: [...abbreviations, newAbbr] });
    this.searchInput.input.value = '';
    await this.setAbbreviations([...abbreviations, newAbbr]);
    Engine.hideLoading();
  }

  onPlayEnd = () => {
    if (!this.state.autoplay) {
      this.setState({});
      return;
    }

    const idx = this.state.audioUrls.indexOf(this.audio.src);
    if (idx < this.state.audioUrls.length - 1) {
      this.audio.src = this.state.audioUrls[idx + 1];
      this.audio.play();
    }
    this.setState({});
  }

  onAnalysis = () => {
    const { azureSpeakers } = this.state;
    const { content, sentences } = this.props.detail;
    let text = content.replace(/\n\n/g, '\n');
    text = text.replace(/:\n/g, ':');
    text = text.replace(/：\n/g, '：');
    const lines = text.split('\n').filter((x) => { return !_.isEmpty(x); });
    const names = lines.map((line) => { return _.trim(line.split(/[：:]/)[0] || ''); });
    const usernames = _.uniq(names).filter((name) => { return !_.isEmpty(name); });
    const speakers = _.map(this.props.speakers, 'speaker');
    const userVoiceMap = {};
    const userToneMap = {};
    const azureMap = {};

    usernames.forEach((name) => {
      const speaker = speakers.find((x) => { return x === name; });
      const item = (sentences || []).find((x) => { return x.speaker === name; });
      if (speaker) {
        userVoiceMap[name] = speaker;
      }

      if (item) {
        if (_.map(azureSpeakers, 'value').includes(item.speaker)) {
          userVoiceMap[name] = item.speaker;
          azureMap[name] = item.azureParams;
        }
        userToneMap[name] = item?.tone;
      }
    });

    this.setState({ usernames, userVoiceMap, userToneMap, azureMap });
    this.props.setState({ detail: { ...this.props.detail, content: text } });
  }

  onAnalysisSolo = async () => {
    const { content } = this.props.detail;
    const txt = (content || '')?.replace(/[\r\n]/g, '');
    await this.props.setState({ detail: { ...this.props.detail, content: `小A：${txt}` } });
    this.onAnalysis();
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.props.setState({ detail: { ...this.props.detail, [key]: value } });
  }

  onChangeShotValue = (e, sentenceIndex, shotIndex) => {
    const value = e?.target ? e.target.value : e;
    const { sentences } = this.props.detail;
    sentences[sentenceIndex].shots[shotIndex].text = value;
    this.props.setState({ detail: { ...this.props.detail, sentences } });
  }

  onShowUserSetting = (name) => {
    const userSetting = this.props.speakers.find((x) => { return x.speaker === name; });
    this.setState({ userOpen: true, userSetting });
  }

  onRegenShot = async (item, sentenceIndex, shotIndex) => {
    const cloneId = this.props.detail.id;
    const { azureParams, tone } = this.props.detail.sentences[sentenceIndex];
    const regenParams = { cloneId, sentenceIndex, shotIndex, ...item, azure: azureParams, tone };
    const detail = await this.props.regenVoiceClone(regenParams);
    await this.props.getVoiceClone(detail?.id);
    this.initDetail();
  }

  onRemoveShot = async (sentenceIndex, shotIndex) => {
    const cloneId = this.props.detail.id;
    await this.props.removeVoiceClone({ cloneId, sentenceIndex, shotIndex });
    await this.props.getVoiceClone(cloneId);
    this.initDetail();
  }

  onCreateShot = async () => {
    const cloneId = this.props.detail.id;
    const { sentenceIndex, shotIndex, speaker, text } = this.state.shotData;
    const ref = this.props.speakers.find((x) => { return x.speaker === speaker; }).refs[0];
    await this.props.insertVoiceClone({ cloneId, sentenceIndex, shotIndex, speaker, text, ref });
    await this.props.getVoiceClone(cloneId);
    this.initDetail();
    this.setState({ openShot: false, shotData: {} });
  }

  onAddShot = async (sentenceIndex, shotIndex) => {
    this.setState({ openShot: true, shotData: { sentenceIndex, shotIndex: shotIndex + 1 } });
  }

  onChangeShotData = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ shotData: { ...this.state.shotData, [key]: value } });
  }

  onChangeShotOffset = async (value, sentenceIndex, shotIndex, type) => {
    const sentences = _.cloneDeep(this.props.detail?.sentences);
    sentences[sentenceIndex].shots[shotIndex][type] = value;
    this.props.setState({ detail: { ...this.props.detail, sentences } });
    const params = { cloneId: this.props.detail.id, sentenceIndex, shotIndex, [type]: value };
    if (type === 'offset') {
      await this.props.updateVoiceCloneOffset(params);
    }

    if (type === 'speed') {
      await this.props.updateVoiceCloneSpeed(params);
    }
  }

  onStartClone = async () => {
    const { userVoiceMap, usernames, userToneMap } = this.state;
    if (_.isEmpty(userVoiceMap) || _.keys(userVoiceMap).length !== usernames.length) {
      Toast.show('请选择声音', Toast.Type.WARNING);
      return;
    }

    const { content, name } = this.props.detail;
    if (_.isEmpty(content) || _.isEmpty(name) || _.isEmpty(this.state.usernames)) {
      Toast.show('请填写标题、文本和选择声音', Toast.Type.WARNING);
      return;
    }

    const newContent = this.formatContent(content);
    const speakers = this.state.usernames.map((username) => {
      const isAzure = _.endsWith(userVoiceMap[username], 'MultilingualNeural');
      let ref = this.props.speakers[0].refs[0];
      if (!isAzure) {
        ref = this.props.speakers?.find((x) => { return x.speaker === userVoiceMap[username]; }).refs[0];
      }
      return {
        azure: isAzure ? (this.state.azureMap[username] || DEFAULT_AZURE_RATE_MAP) : undefined,
        speaker: userVoiceMap[username],
        tone: userToneMap[username],
        ref,
      };
    });

    const params = { content: newContent, name, speakers };
    // console.log(JSON.stringify(params));

    if (!_.isUndefined(this.props?.detail?.id)) {
      params.id = this.props.detail.id;
    }

    await this.props.createVoiceClone(params);
    Toast.show('克隆成功', Toast.Type.SUCCESS);
    this.$push('/market-voice-clone');
  }

  onStartTone = async () => {
    const tones = [];

    _.map(this.state.userToneMap, (tone, speaker) => { tones.push({ speaker, tone }); });
    const params = { cloneId: this.props.detail.id, tones };
    await this.props.updateVoiceTone(params);
    Toast.show('变声成功', Toast.Type.SUCCESS);
    this.$push('/market-voice-clone');
  }

  onEditRef = async (item, sentenceIndex, shotIndex) => {
    let { refs } = this.props.speakers.find((x) => { return x.speaker === item.speaker; });
    let ossRefs = [];
    try { // eslint-disable-next-line
      const resp = await fetch(`//video-clip.oss-cn-shanghai.aliyuncs.com/faas/speakers/${item.speaker}.json?v=${new Date().valueOf()}`);
      ossRefs = await resp.json();
      refs = [...refs, ...ossRefs];
    } catch (error) {
      // to do
    }

    this.setState({ openSpeaker: true, sentence: { ...item, sentenceIndex, shotIndex }, refs });
  }

  onChangeSpeaker = async () => {
    const { sentenceIndex, shotIndex, shots, ref } = this.state.sentence;
    const cloneId = this.props.detail.id;
    const refObj = (this.state.refs || []).find((x) => { return x.wavUrl === ref.wavUrl; });
    if (_.isUndefined(refObj)) {
      Toast.show('请选择声音', Toast.Type.WARNING);
      return;
    }
    const { text } = shots[shotIndex];
    await this.props.regenVoiceClone({ text, cloneId, sentenceIndex, shotIndex, ref: refObj });
    await this.props.getVoiceClone(cloneId);
    this.setState({ openSpeaker: false, sentence: {}, refs: [] });
  }

  onChangeAudioUrl = async () => {
    const { shortAudio, sentenceIndex, shotIndex } = this.state.shotObj;
    if (_.isUndefined(shortAudio)) {
      return;
    }

    const params = {
      cloneId: this.props.detail.id,
      sentenceIndex,
      shotIndex,
      audioUrl: shortAudio.url,
      duration: shortAudio.duration,
    };
    const data = await this.props.updateVoiceClone(params);
    await this.props.setState({ detail: data });
    this.setState({
      shotObj: {},
      openShort: false,
      audioUrls: _.map(_.flatMap(_.map(data?.sentences, 'shots')), 'audioUrl'),
    });
  }

  onChangeRefSpeaker = async (value) => {
    let { refs } = this.props.speakers.find((x) => { return x.speaker === value; });
    let ossRefs = [];
    try { // eslint-disable-next-line
      const resp = await fetch(`//video-clip.oss-cn-shanghai.aliyuncs.com/faas/speakers/${value}.json?v=${new Date().valueOf()}`);
      ossRefs = await resp.json();
      refs = [...refs, ...ossRefs];
    } catch (error) {
      // to do
    }
    this.setState({ sentence: { ...this.state.sentence, speaker: value }, refs });
  }

  onPlay = (item) => {
    if (this.audio.src === item.audioUrl) {
      if (this.audio.paused) {
        this.audio.play();
      } else {
        this.audio.pause();
      }
    } else {
      this.audio.src = item.audioUrl;
      if (!_.isUndefined(item.speed)) {
        this.audio.playbackRate = item.speed;
      }
      this.audio.play();
    }
    this.setState({});
  }

  onPlayAll = (startI = 0, startJ = 0, item) => {
    this.onPauseAll(item);
    if (!this.state.autoplay) {
      this.onPlay(item);
      this.setState({ playIndex: { sentenceIndex: startI, shotIndex: startJ } });
      return;
    }

    this.audios = [];
    this.timeouts = [];

    const { sentences } = this.props.detail;
    let totalDuration = 0;

    for (let i = startI; i < sentences.length; i++) {
      const { shots } = sentences[i];
      for (let j = i === startI ? startJ : 0; j < shots.length; j++) {
        const audio = new Audio(shots[j].audioUrl);
        if (!_.isUndefined(shots[j].speed)) {
          audio.playbackRate = shots[j].speed;
        }
        this.audios.push(audio);
        const delay = totalDuration * 1000;

        const timeoutId = setTimeout(() => {
          audio.play();
          this.setState({ playIndex: { sentenceIndex: i, shotIndex: j } });
        }, delay);
        this.timeouts.push(timeoutId);
        totalDuration += shots[j].duration;

        if (j <= shots.length - 1) {
          totalDuration += shots[j].offset;
        }
      }
    }
  }

  onPauseAll = (item) => {
    this.setState({ playIndex: {} });
    if (!this.state.autoplay && !this.audio.paused) {
      this.onPlay(item);
      return;
    }

    for (let i = 0; i < this.audios.length; i++) {
      this.audios[i].pause();
      clearTimeout(this.timeouts[i]);
    }
  }


  onSendWsMsg = (url, message) => {
    return new Promise((resolve, reject) => {
      const ws = new WebSocket(url);
      ws.addEventListener('open', () => {
        ws.send(message);
      });

      ws.addEventListener('message', (e) => {
        const msg = `${e.data}`;
        if (msg !== 'pong') {
          const msgObj = JSON.parse(msg);
          if (msgObj.type === 'final_result') {
            resolve(msgObj.data.output);
          }
        }
      });

      ws.addEventListener('error', (err) => {
        reject(err);
      });
    });
  }

  onRewrite = async () => {
    this.setState({ rewriteLoading: true });
    const uri = Engine.getWssEndpoint();
    const path = `${uri}/v2/chatbot/workflows/run/${this.state.flowId}`;
    const query = { access_token: Sessions.getToken() };
    const wsurl = `${path}?${qs.stringify(query)}`;
    const chunkedArray = _.chunk(this.state.sourceContent.split('\n'), 30);

    let finalText = '';
    for (let i = 0; i < chunkedArray.length; i++) {
      const text = chunkedArray[i].join('\n'); // eslint-disable-next-line
      const wText = await this.onSendWsMsg(wsurl, JSON.stringify({ text, type: 'message', is_beta: false }));
      finalText += `${wText}\n`;
      await this.setState({ rewriteText: finalText }); // eslint-disable-line
    }
    this.setState({ rewriteText: finalText, rewriteLoading: false });
    Engine.hideLoading();
  }

  onResetText = () => {
    this.props.setState({ detail: { ...this.props.detail, content: this.state.rewriteText } });
    this.setState({ openRewrite: false, rewriteText: '' });
  }

  onChangeAzure = (value, key, name) => {
    const azureMap = _.cloneDeep(this.state.azureMap);
    if (_.isUndefined(azureMap[name])) {
      azureMap[name] = {};
    }
    azureMap[name][key] = value;
    this.setState({ azureMap });
  }

  renderUserDrawer = () => {
    const { userOpen, userSetting } = this.state;

    return (
      <Drawer
        title={`${userSetting.speaker}-设置`}
        width={720}
        visible={userOpen}
        onClose={() => { return this.setState({ userOpen: false }); }}
        extra={
          <Button type="primary" onClick={() => { return this.setState({ userOpen: false }); }}>
            保存
          </Button>
        }
      >
        <Form labelCol={{ span: 3 }}>
          <Form.Item label="声音">
            <Input value={userSetting.speaker} />
          </Form.Item>
          <Form.Item label="声音设置">
            {
              (userSetting?.refs || []).map((x, i) => {
                return (
                  <div>
                    <Input.Group compact>
                      <Button>{i + 1}.</Button>
                      <Input style={{ width: '65%' }} value={x.text} />
                      {/* <Button >上传</Button> */}
                    </Input.Group>
                    <audio controls src={x.wavUrl} style={{ height: 30, margin: '5px 0' }} />
                  </div>
                );
              })
            }
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderCreateShot = () => {
    const { shotData, openShot } = this.state;
    return (
      <Modal
        open={openShot}
        title="创建Shot"
        onCancel={() => { return this.setState({ openShot: false, shotData: {} }); }}
        onOk={() => { return this.onCreateShot(); }}
      >
        <Form labelCol={{ span: 3 }}>
          <Form.Item label="声音">
            <Select
              value={shotData.speaker}
              onChange={(value) => { return this.onChangeShotData(value, 'speaker'); }}
            >
              {
                (this.props.speakers || []).map((x) => {
                  return (
                    <Select.Option key={x.speaker} value={x.speaker}>
                      {x.speaker}
                    </Select.Option>
                  );
                })
              }
            </Select>
          </Form.Item>
          <Form.Item label="文本">
            <Input.TextArea
              value={shotData.text}
              onChange={(e) => { return this.onChangeShotData(e, 'text'); }}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  renderUserName = () => {
    const { userVoiceMap, usernames, azureSpeakers, azureSpeakersMap } = this.state;
    if (_.isEmpty(usernames)) return null;

    return (
      <Form.Item label="声音设置">
        <div style={{ position: 'relative' }}>
          {
            usernames.map((name) => {
              return (
                <div key={name} style={{ marginBottom: 5, width: '100%' }}>
                  <Input.Group compact>
                    <Button style={{ width: 100 }}>{name}</Button>
                    <Select
                      style={{ width: 260 }}
                      value={userVoiceMap[name]}
                      onChange={(value) => {
                        return this.setState({ userVoiceMap: { ...userVoiceMap, [name]: value } });
                      }}
                    >
                      {
                        azureSpeakers.map((x) => {
                          return (
                            <Select.Option key={x.value} value={x.value}>
                              {x.ttsSettings.provider}-{x.label}
                            </Select.Option>
                          );
                        })
                      }
                      {
                        (this.props.speakers || []).map((x) => {
                          return (
                            <Select.Option key={x.speaker} value={x.speaker}>
                              {x.speaker}
                            </Select.Option>
                          );
                        })
                      }
                    </Select>
                    <Button
                      disabled={!userVoiceMap[name] || azureSpeakersMap.indexOf(userVoiceMap[name]) > -1}
                      onClick={() => { return this.onShowUserSetting(userVoiceMap[name]); }}
                    >设置
                    </Button>
                    {
                      azureSpeakersMap.indexOf(userVoiceMap[name]) > -1 &&
                      <>
                        <InputNumber
                          addonBefore="音高"
                          min={0}
                          step={0.1}
                          value={(this.state.azureMap[name] || DEFAULT_AZURE_RATE_MAP).pitchRate}
                          onChange={(value) => { return this.onChangeAzure(value, 'pitchRate', name); }}
                        />
                        <InputNumber
                          min={0}
                          step={0.1}
                          addonBefore="语速"
                          value={(this.state.azureMap[name] || DEFAULT_AZURE_RATE_MAP).speechRate}
                          onChange={(value) => { return this.onChangeAzure(value, 'speechRate', name); }}
                        />
                      </>
                    }
                    <Divider type="vertical" />
                    <Button>
                      变声为:
                    </Button>
                    <Select
                      style={{ width: 160 }}
                      value={this.state.userToneMap[name]}
                      onChange={(value) => {
                        return this.setState({ userToneMap: { ...this.state.userToneMap, [name]: value } });
                      }}
                    >
                      {
                        _.map(this.props.tones, (v, k) => {
                          return <Select.Option key={k} value={k}>{v}</Select.Option>;
                        })
                      }
                    </Select>
                  </Input.Group>
                </div>
              );
            })
          }
          <div style={{ position: 'absolute', bottom: 0, right: 0 }}>
            {
              this.state.isEdit &&
              <Button type="primary" onClick={() => { return this.onStartTone(); }} >
                {this.props.detail.toneStatus === 'done' ? '重新变声' : '变声'}
              </Button>
            }
            <Divider type="vertical" />
            <Button type="primary" onClick={() => { return this.onStartClone(); }}>
              {this.state.isEdit ? '重新克隆' : '开始克隆'}
            </Button>
          </div>
        </div>
      </Form.Item>
    );
  }

  renderAbbrDrawer = () => {
    return (
      <Drawer
        title="缩写读音"
        width="50vw"
        visible={this.state.openAabbr}
        onClose={() => { return this.setState({ openAabbr: false }); }}
      >
        <Input.Search
          ref={(ref) => { this.searchInput = ref; }}
          placeholder="请输入缩写"
          enterButton="新增"
          onSearch={(value) => { return this.onAddAbbr(value); }}
        />
        <Table
          dataSource={this.state.abbreviations}
          pagination={false}
          columns={
            [
              { title: '缩写', dataIndex: 'abbr', key: 'abbr' },
              { title: '全称', dataIndex: 'text', key: 'text' },
            ]
          }
        />
      </Drawer>
    );
  }

  renderSpeakerModal = () => {
    const { sentence, openSpeaker, refs } = this.state;
    return (
      <Modal
        title="声音设置"
        width="50vw"
        visible={openSpeaker}
        onCancel={() => { return this.setState({ openSpeaker: false, sentence: {} }); }}
        onOk={() => { return this.onChangeSpeaker(); }}
      >
        <Select
          style={{ width: '100%', marginTop: -10, marginBottom: 10 }}
          value={sentence.speaker}
          onChange={(value) => { return this.onChangeRefSpeaker(value); }}
        >
          {
            (this.props.speakers || []).map((x) => {
              return <Select.Option value={x.speaker}>{x.speaker}</Select.Option>;
            })
          }
        </Select>
        <Radio.Group
          value={sentence.ref.wavUrl}
          onChange={(e) => {
            return this.setState({ sentence: { ...sentence, ref: { ...sentence.ref, wavUrl: e.target.value } } });
          }}
        >
          {
            (refs || []).map((x) => {
              return (
                <Radio value={x.wavUrl} style={{ width: '100%', marginBottom: 5 }}>
                  <div style={{ width: '40vw' }}>
                    <Input value={x.text} style={{ marginBottom: 2 }} />
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <audio controls style={{ height: 30 }} src={x.wavUrl} />
                      {_.isUndefined(x.name) ? '' : <span style={{ fontWeight: 'bold' }}>{x.name}</span>}
                    </div>
                  </div>
                </Radio>
              );
            })
          }
        </Radio.Group>
      </Modal>
    );
  }

  renderShortModal = () => {
    const { shortAudioObj, shotObj } = this.state;
    return (
      <Modal
        title="声音替换"
        width="50vw"
        visible={this.state.openShort}
        onCancel={() => { return this.setState({ openShort: false }); }}
        onOk={() => { return this.onChangeAudioUrl(); }}
      >
        {_.map(shortAudioObj, (v, k) => {
          return (
            <div>
              <h3>{k}</h3>
              <Radio.Group
                value={shotObj.shortAudio}
                onChange={(e) => { return this.setState({ shotObj: { ...shotObj, shortAudio: e.target.value } }); }}
              >
                {v.map((o) => { return <Radio key={o.url} value={o}>{o.name}</Radio>; })}
              </Radio.Group>
            </div>
          );
        })}
      </Modal>
    );
  }

  renderCloneContent = () => {
    const { playIndex } = this.state;
    const { sentences } = this.props.detail;
    if (_.isEmpty(sentences)) return null;

    return (
      <>
        <Divider orientation="left">克隆结果</Divider>
        <div style={{ float: 'right', display: 'flex', alignItems: 'center' }}>
          <Switch
            style={{ float: 'right' }}
            checked={this.state.autoplay}
            checkedChildren="自动播放"
            unCheckedChildren="单句播放"
            onChange={(e) => {
              const stateObj = { autoplay: e };
              if (!e) {
                stateObj.playIndex = {};
                for (let i = 0; i < this.audios.length; i++) {
                  clearTimeout(this.timeouts[i]);
                }
              }
              return this.setState(stateObj);
            }}
          />
        </div>

        <Form.Item label=" " colon={false}>
          {
            (sentences || []).map((x, pIdx) => {
              let name = x.speaker;
              if (_.endsWith(name, 'MultilingualNeural')) {
                name = _.trimEnd(_.last(name.split('-')), 'MultilingualNeural');
              }
              return (
                <Form.Item labelCol={{ span: 2 }} label={name}>
                  {
                    (x.shots || []).map((item, idx) => {
                      const isPlay = playIndex.sentenceIndex === pIdx && playIndex.shotIndex === idx;
                      return (
                        <div key={item} style={{ margin: '5px 0', width: '100%' }}>
                          <Input
                            style={{ outline: isPlay ? '#1890ff auto 1px' : 'initial' }}
                            addonBefore={`${idx + 1}.`}
                            value={item?.text}
                            onChange={(e) => { return this.onChangeShotValue(e, pIdx, idx); }}
                            addonAfter={
                              <>
                                <DownCircleOutlined onClick={() => { window.open(item.audioUrl); }} />
                                <Divider type="vertical" />
                                <Popconfirm
                                  title="确定删除吗？"
                                  onConfirm={() => { return this.onRemoveShot(pIdx, idx); }}
                                >
                                  <DeleteOutlined />
                                </Popconfirm>
                                <Divider type="vertical" />
                                <EditOutlined onClick={() => { return this.onEditRef(x, pIdx, idx); }} />
                                <Divider type="vertical" />
                                <PlusCircleOutlined onClick={() => { return this.onAddShot(pIdx, idx); }} />
                                <Divider type="vertical" />
                                {
                                  isPlay ?
                                    <PauseCircleOutlined onClick={() => { return this.onPauseAll(item); }} /> :
                                    <PlayCircleOutlined onClick={() => { return this.onPlayAll(pIdx, idx, item); }} />
                                }
                                <Divider type="vertical" />
                                <Popconfirm
                                  title="确定重新生成吗？"
                                  onConfirm={() => { return this.onRegenShot(item, pIdx, idx); }}
                                >
                                  <RedoOutlined />
                                </Popconfirm>
                                <Divider type="vertical" />
                                <SwapOutlined
                                  onClick={() => {
                                    this.setState({
                                      openShort: true,
                                      shotObj: { ...item, sentenceIndex: pIdx, shotIndex: idx },
                                    });
                                  }}
                                />
                              </>
                            }
                          />
                          <Input.Group compact size="small" style={{ marginTop: 4 }}>
                            {
                              !!item?.duration &&
                              <>
                                时长: <span style={{ width: 60 }}>{item?.duration}秒</span>&nbsp;丨
                                <Divider type="vertical" />
                              </>
                            }
                            停顿&nbsp;
                            <InputNumber
                              size="small"
                              value={item.offset}
                              step={0.1}
                              onChange={(value) => { return this.onChangeShotOffset(value, pIdx, idx, 'offset'); }}
                            />
                            &nbsp;秒&nbsp;丨&nbsp;语速:&nbsp;
                            <Radio.Group
                              value={item.speed}
                              onChange={(e) => { return this.onChangeShotOffset(e.target.value, pIdx, idx, 'speed'); }}
                            >
                              <Radio value={0.75}>慢速</Radio>
                              <Radio value={1}>常速</Radio>
                              <Radio value={1.5}>快速</Radio>
                            </Radio.Group>
                          </Input.Group>
                        </div>
                      );
                    })
                  }
                </Form.Item>
              );
            })
          }
        </Form.Item>
      </>
    );
  }

  renderTextWrap = () => {
    const { detail } = this.props;
    return (
      <>
        <Form.Item label="标题">
          <Input
            value={detail?.name}
            onChange={(e) => { return this.onChangeValue(e, 'name'); }}
          />
        </Form.Item>
        <Form.Item label="文本">
          <Input.TextArea
            autoSize={{ minRows: 5, maxRows: 10 }}
            value={detail?.content}
            onChange={(e) => { return this.onChangeValue(e, 'content'); }}
          />
          <div style={{ marginTop: 5, float: 'right' }} >
            <Button
              size="small"
              disabled={_.isEmpty(detail?.content)}
              onClick={() => { return this.onAnalysisSolo(); }}
            >单口分析
            </Button>
            <Divider type="vertical" />
            <Button
              size="small"
              disabled={_.isEmpty(detail?.content)}
              onClick={() => { return this.onAnalysis(); }}
            >
              对话分析
            </Button>
          </div>
        </Form.Item>
        {this.renderUserName()}
      </>
    );
  }

  renderRewriteModal = () => {
    return (
      <Drawer
        title="重写"
        width="50vw"
        visible={this.state.openRewrite}
        onClose={() => { return this.setState({ openRewrite: false }); }}
      >
        <Spin spinning={this.state.rewriteLoading}>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button type="link" onClick={() => { return this.$push(`/workflow/${this.state.flowId}`); }}>
              修改Flow
            </Button>
            <Button onClick={() => { return this.onRewrite(); }} >执行Flow</Button>
          </div>
          <Input.TextArea
            style={{ margin: '10px 0' }}
            value={this.state.rewriteText}
            autoSize={{ minRows: 5, maxRows: 20 }}
          />
          <Button style={{ float: 'right' }} onClick={() => { return this.onResetText(); }}>回填文本</Button>
        </Spin>
      </Drawer>
    );
  }

  render = () => {
    const { toneStatus } = this.props.detail;
    const { isEdit, userOpen, openShot, openSpeaker, openShort } = this.state;
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <Form labelCol={{ span: 2 }} wrapperCol={{ span: 16 }} className="common-form">
          {
            isEdit ?
              <Collapse
                ghost
                expandIconPosition="end"
                expandIcon={() => {
                  return (
                    <div style={{ fontSize: 20 }}>
                      <Button
                        onClick={(e) => {
                          e.stopPropagation();
                          return this.setState({ openAabbr: true });
                        }}
                        style={{ margin: '0 16px ' }}
                      >
                        缩写读音
                      </Button>
                      当前为 <span style={{ color: 'red' }}>{toneStatus === 'done' ? '变声' : '克隆'}</span> 音色
                    </div>
                  );
                }}
              >
                <Collapse.Panel header="基本信息" key="1">
                  {this.renderTextWrap()}
                  {
                    !_.isEmpty(this.state.sourceContent) &&
                    <Form.Item label="原文">
                      <Input.TextArea
                        autoSize={{ minRows: 5, maxRows: 10 }}
                        value={this.state.sourceContent}
                      />
                      <Button
                        size="small"
                        onClick={() => { return this.setState({ openRewrite: true }); }}
                      >重写
                      </Button>
                    </Form.Item>
                  }
                </Collapse.Panel>
              </Collapse>
              : this.renderTextWrap()
          }
          {this.renderCloneContent()}
        </Form>
        {userOpen && this.renderUserDrawer()}
        {openShot && this.renderCreateShot()}
        {openSpeaker && this.renderSpeakerModal()}
        {openShort && this.renderShortModal()}
        {this.state.openRewrite && this.renderRewriteModal()}
        {this.state.openAabbr && this.renderAbbrDrawer()}
      </div>
    );
  }
}

export {
  reducer,
};
