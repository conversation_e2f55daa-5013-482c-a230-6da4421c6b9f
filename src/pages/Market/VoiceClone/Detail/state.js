import Configs from '~/consts';
import { Market } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'VOICE_CLONE_DETAIL/SET_STATE';
const CLEAR_STATE = 'VOICE_CLONE_DETAIL/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const getVoiceClone = (id) => {
  return async (dispatch) => {
    const detail = await Market.getVoiceClone(id);
    dispatch(setState({ detail }));
  };
};

export const fetchVoiceTones = () => {
  return async (dispatch) => {
    const { items } = await Market.fetchVoiceTones({ ...Configs.ALL_PAGE_PARAMS, status: 'done' });
    const tones = {};
    items.forEach((x) => { tones[x.identifier] = x.name; });
    dispatch(setState({ tones }));
  };
};

export const updateVoiceTone = (params) => {
  return async () => {
    await Market.updateVoiceTone(params);
  };
};

export const createVoiceClone = (params) => {
  return async () => {
    const data = await Market.importVoiceClone(params);
    return data;
  };
};

export const insertVoiceClone = (params) => {
  return async () => {
    const data = await Market.insertVoiceClone(params);
    return data;
  };
};

export const removeVoiceClone = (params) => {
  return async () => {
    const data = await Market.removeVoiceClone(params);
    return data;
  };
};

export const updateVoiceCloneOffset = (params) => {
  return async () => {
    const data = await Market.updateVoiceCloneOffset(params);
    return data;
  };
};

export const updateVoiceCloneSpeed = (params) => {
  return async () => {
    const data = await Market.updateVoiceCloneSpeed(params);
    return data;
  };
};

export const updateVoiceClone = (params) => {
  return async () => {
    const data = await Market.updateVoiceClone(params);
    return data;
  };
};

export const regenVoiceClone = (params) => {
  return async () => {
    const data = await Market.regenVoiceClone(params);
    return data;
  };
};

export const jobTrigger = (params) => {
  return () => {
    return Market.jobTrigger(params);
  };
};

export const fetchVoiceCloneSpeakers = () => {
  return async (dispatch) => {
    const result = await Market.fetchVoiceCloneSpeakers();
    dispatch(setState({ speakers: _.values(result) }));
  };
};

const _getInitState = () => {
  return {
    detail: {
      language: 'zh',
      speaker: 0,
    },
    tones: [],
    speakers: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
