import { RedoOutlined } from '@ant-design/icons';
import { FilterBar, InputUpload, PaginationTable, Toast } from '~/components';
import Engine, { <PERSON>yun<PERSON>elper, ChatBot, Sessions } from '~/engine';
import { Platform } from '~/plugins';
import { Button, Divider, Drawer, Form, Input, Modal, Popconfirm, Radio, Table, Tag } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.marketVoiceClone;
  },
  actions,
)
export default class VoiceClone extends Component {
  static propTypes = {
    list: PropTypes.array,
    speakers: PropTypes.array,
    total: PropTypes.number,
    pagination: PropTypes.object,
    jobTrigger: PropTypes.func.isRequired,
    getVoiceClone: PropTypes.func.isRequired,
    retryVoiceClone: PropTypes.func.isRequired,
    delVoiceClone: PropTypes.func.isRequired,
    exportVoiceClone: PropTypes.func.isRequired,
    fetchVoiceClones: PropTypes.func.isRequired,
    fetchVoiceCloneSpeakers: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    open: false,
    ref: { language: 'zh' },
    refs: [],
    ossRefs: [],
    abbreviations: [],
    partnerType: '',
  }

  componentDidMount = async () => {
    const { partnerType } = Sessions.getProfile();
    this.props.fetchVoiceCloneSpeakers();
    await this.props.fetchVoiceClones();
    this.getAbbreviations();
    this.setState({ partnerType });
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  getAbbreviations = async () => { // eslint-disable-next-line
    const resp = await fetch(`https://video-clip.oss-cn-shanghai.aliyuncs.com/models/GPT-SoVITS/engdict-hot.rep?v=${new Date().valueOf()}`);
    const blob = await resp.blob();
    await new Promise((resolve, reject) => { // eslint-disable-line
      const reader = new FileReader();
      reader.onload = (e) => {
        const lines = e.target.result.split('\n');
        const abbreviations = [];
        lines.forEach((line) => {
          const [abbr, ...rest] = line.split(' ');
          abbreviations.push({ abbr, text: rest.join(' ') });
        });
        this.setState({ abbreviations });
        resolve();
      };
      reader.readAsText(blob);
    });
  }

  setAbbreviations = async (abbreviations) => {
    const blob = new Blob([abbreviations.map((x) => {
      return `${x.abbr} ${x.text}`;
    }).join('\n')], { type: 'text/plain' });
    await AliyunHelper.uploadImageBlob(blob, () => { }, 'models/GPT-SoVITS/engdict-hot.rep');
  }

  onAddAbbr = async (value) => {
    // 判断是都已经存在
    const { abbreviations } = this.state;
    if (abbreviations.find((x) => { return x.abbr === value; })) {
      Toast.show('已经存在', Toast.Type.ERROR);
      return;
    }
    Engine.showLoading();
    const { choices } = await ChatBot.directChatCompletion({
      model: 'gpt-4-turbo-preview',
      temperature: 1,
      top_p: 1,
      n: 1,
      stream: false,
      stop: '',
      max_tokens: 2048,
      presence_penalty: 0,
      frequency_penalty: 0,
      messages: [
        { role: 'system', content: '输出对应的ARPAbet, 仅输出ARPAbet.' },
        { role: 'user', content: value },
      ],
    });

    const newAbbr = { abbr: value, text: choices[0].message.content };
    await this.setState({ abbreviations: [...abbreviations, newAbbr] });
    this.searchInput.input.value = '';
    await this.setAbbreviations([...abbreviations, newAbbr]);
    Engine.hideLoading();
  }

  onShowSpeakerRefs = async (item) => {
    let refs = [];
    let ossRefs = [];
    try { // eslint-disable-next-line
      const resp = await fetch(`//video-clip.oss-cn-shanghai.aliyuncs.com/faas/speakers/${item.speaker}.json?v=${new Date().valueOf()}`);
      ossRefs = await resp.json();
      refs = [...item.refs, ...ossRefs];
    } catch (error) {
      // eslint-disable-next-line prefer-destructuring
      refs = item.refs;
    }
    this.setState({ openRefs: true, refs, ossRefs, speaker: item });
  }

  onRery = async (row) => {
    const { id, content, name, sentences } = await this.props.getVoiceClone(row.id);
    const names = _.uniq(_.map(sentences, 'speaker'));
    const speakers = [];
    names.forEach((x) => {
      const item = sentences.find((s) => { return s.speaker === x; });
      speakers.push({ speaker: x, ref: item.ref, azure: item.azureParams, tone: x.tone });
    });

    await this.props.retryVoiceClone({ id, name, content, speakers });
  }

  onSubmit = async () => {
    const { ref, ossRefs, speaker } = this.state;
    const refs = [...(ossRefs || []), ref];
    const blob = new Blob([JSON.stringify(refs)], { type: 'application/json' });
    await AliyunHelper.uploadImageBlob(blob, () => { }, `faas/speakers/${speaker.speaker}.json`);
    await this.onShowSpeakerRefs(speaker);
    this.setState({ openRef: false, ref: {}, ossRefs: [], speaker: {} });
  }

  onSubmitPodcast = async (url) => {
    if (url) {
      const urlStrs = url.split('?')[0].split('/');
      const code = urlStrs[urlStrs.length - 1] || urlStrs[urlStrs.length - 2];
      const params = {
        region: 'cn-shanghai',
        job_name: 'podcast',
        job_type: 'clone',
        job_data: {
          url: `https://music.youtube.com/watch?v=${code}`,
          env: Platform.isProd() ? 'prod' : 'stg',
          token: Sessions.getToken(),
        },
      };
      await this.props.jobTrigger(params);
      Toast.show('操作成功', Toast.Type.SUCCESS);
      this.setState({ openPodcast: false });
    }
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '名称', dataIndex: 'name', key: 'name', align: 'center', width: '30%', ellipsis: true },
      {
        title: '变声',
        dataIndex: 'toneStatus',
        key: 'toneStatus',
        align: 'center',
        render: (t) => {
          if (_.isEmpty(t)) return '-';
          return t;
        },
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (t) => { return moment(t).format('YYYY-MM-DD HH:mm:ss'); },
      },
      {
        title: '更新时间',
        dataIndex: 'updatedAt',
        key: 'updatedAt',
        align: 'center',
        render: (t) => { return moment(t).format('YYYY-MM-DD HH:mm:ss'); },
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              {txt}
              <Popconfirm title="确定重试吗？" onConfirm={() => { return this.onRery(row); }} >
                <RedoOutlined />
              </Popconfirm>
            </>
          );
        },
      },
      {
        title: '音频',
        dataIndex: 'audioUrl',
        key: 'audioUrl',
        align: 'center',
        render: (t, row) => {
          if (_.isEmpty(t)) {
            return (
              <Button type="link" onClick={() => { return this.props.exportVoiceClone({ cloneId: row.id }); }}>
                <Tag color="blue">导出</Tag>
              </Button>
            );
          }

          return (
            <>
              <a target="_blank" href={t} rel="noreferrer"><Tag color="green">试听</Tag> </a>
              <Popconfirm
                title="确定重新导出吗？"
                onConfirm={() => { return this.props.exportVoiceClone({ cloneId: row.id }); }}
              >
                <RedoOutlined />
              </Popconfirm>
            </>
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        render: (t, row) => {
          return (
            <div>
              {/* <Button
                type="link"
                disabled={row.status !== 'done'}
                onClick={() => { return this.$push(`/market-voice-clone/topic/${row.id}`); }}
              >
                编辑V2
              </Button>
              <Divider type="vertical" /> */}

              <Button
                type="link"
                disabled={row.status !== 'done'}
                onClick={() => { return this.$push(`/market-voice-clone/${row.id}`); }}
              >
                编辑
              </Button>
              <Divider type="vertical" />
              <Popconfirm
                title="确定删除吗？"
                onConfirm={() => { return this.props.delVoiceClone(row.id); }}
              >
                <a>删除</a>
              </Popconfirm>
              {
                row.status !== 'done' &&
                <>
                  <Divider type="vertical" />
                  <Button type="link" onClick={() => { return this.props.fetchVoiceClones(); }}>
                    刷新
                  </Button>
                </>
              }
            </div>
          );
        },
      },
    ];
  }

  renderSpeakerColumns = () => {
    return [
      { title: '音色', dataIndex: 'speaker', key: 'speaker', align: 'center' },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        render: (t, row) => {
          return (
            <Button type="link" onClick={() => { return this.onShowSpeakerRefs(row); }}>
              查看
            </Button>
          );
        },
      },
    ];
  }

  renderCreateSpeakerRef = () => {
    const { ref, openRef } = this.state;
    return (
      <Modal
        title="新增音色参考"
        visible={openRef}
        onOk={() => { return this.onSubmit(); }}
        onCancel={() => { return this.setState({ openRef: false }); }}
      >
        <Form labelCol={{ span: 2 }}>
          <Form.Item label="语言">
            <Radio.Group
              value={ref.language}
              onChange={(e) => { return this.setState({ ref: { ...ref, language: e.target.value } }); }}
            >
              <Radio value="zh">中文</Radio>
              <Radio value="en">英文</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label="文本">
            <Input.TextArea
              value={ref.text}
              onChange={(e) => { return this.setState({ ref: { ...ref, text: e.target.value } }); }}
            />
          </Form.Item>
          <Form.Item label="音频">
            <InputUpload
              url={ref.wavUrl}
              accept=".wav"
              onChange={(value) => { return this.setState({ ref: { ...ref, wavUrl: value } }); }}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  renderSpeakerDrawer = () => {
    return (
      <Drawer
        title="音色"
        width="50vw"
        visible={this.state.open}
        onClose={() => { return this.setState({ open: false }); }}
      >
        <Table
          dataSource={this.props.speakers}
          pagination={false}
          columns={this.renderSpeakerColumns()}
        />
      </Drawer>
    );
  }

  renderSpeakerRefsDrawer = () => {
    return (
      <Drawer
        title="音色参考"
        width="40vw"
        visible={this.state.openRefs}
        onClose={() => { return this.setState({ openRefs: false, refs: [] }); }}
        extra={<Button type="primary" onClick={() => { return this.setState({ openRef: true }); }}>新增</Button>}
      >
        {
          (this.state.refs || []).map((x, idx) => {
            return (
              <div>
                <Input value={x.text} addonBefore={`参考${idx + 1}.`} />
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  {_.isUndefined(x?.name) ? '' : <span style={{ fontWeight: 'bold' }}>{x.name}&nbsp;</span>}
                  <audio style={{ height: 30, marginTop: 2 }} controls src={x.wavUrl} />
                </div>
              </div>
            );
          })
        }
      </Drawer>
    );
  }

  renderButtons = () => {
    let btns = [
      <Button onClick={() => { return this.setState({ openAabbr: true }); }} style={{ margin: '0 16px ' }}>
        缩写读音
      </Button>,
      <Button onClick={() => { return this.setState({ open: true }); }} >
        音色
      </Button>,
      // <Button
      //   type="primary"
      //   style={{ margin: '0 16px ' }}
      //   onClick={() => { return this.$push('/market-voice-clone/topic-add/'); }}
      // >
      //   新增V2
      // </Button>,
      <Button
        type="primary"
        style={{ margin: '0 16px ' }}
        onClick={() => { return this.$push('/market-voice-clone/add/'); }}
      >
        新增
      </Button>,
    ];

    if (this.state.partnerType === 'playground') {
      btns = [
        ...btns,
        <Button
          type="primary"
          onClick={() => { return this.setState({ openPodcast: true }); }}
        >
          新增播客
        </Button>,
        <Button
          type="primary"
          style={{ margin: '0 16px ' }}
          onClick={() => { return this.$push('/market-voice-clone/changer'); }}
        >
          播客变声
        </Button>,
      ];
    }

    return btns;
  }

  renderPodcastModal = () => {
    return (
      <Modal
        title="新增播客"
        visible={this.state.openPodcast}
        onOk={() => { return this.setState({ openPodcast: false }); }}
        onCancel={() => { return this.setState({ openPodcast: false }); }}
      >
        <Form labelCol={{ span: 2 }}>
          <Form.Item label="播客">
            <Input.Search enterButton="提交" onSearch={this.onSubmitPodcast} />
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  renderAbbrDrawer = () => {
    return (
      <Drawer
        title="缩写读音"
        width="50vw"
        visible={this.state.openAabbr}
        onClose={() => { return this.setState({ openAabbr: false }); }}
      >
        <Input.Search
          ref={(ref) => { this.searchInput = ref; }}
          placeholder="请输入缩写"
          enterButton="新增"
          onSearch={(value) => { return this.onAddAbbr(value); }}
        />
        <Table
          dataSource={this.state.abbreviations}
          pagination={false}
          columns={
            [
              { title: '缩写', dataIndex: 'abbr', key: 'abbr' },
              { title: '全称', dataIndex: 'text', key: 'text' },
            ]
          }
        />
      </Drawer>
    );
  }

  render = () => {
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <FilterBar
          shouldShowSearchInput={false}
          renderButtons={this.renderButtons}
        />
        <PaginationTable
          dataSource={this.props.list}
          totalDataCount={this.props.total}
          pagination={this.props.pagination}
          columns={this.renderColumns()}
          onPaginationChange={(e) => { return this.props.fetchVoiceClones(e); }}
        />

        {this.state.open && this.renderSpeakerDrawer()}
        {this.state.openRefs && this.renderSpeakerRefsDrawer()}
        {this.state.openRef && this.renderCreateSpeakerRef()}
        {this.state.openPodcast && this.renderPodcastModal()}
        {this.state.openAabbr && this.renderAbbrDrawer()}
      </div>
    );
  }
}

export {
  reducer,
};
