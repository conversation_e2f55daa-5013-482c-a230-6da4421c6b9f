import { Market } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'VOICE_CLONE/SET_STATE';
const CLEAR_STATE = 'VOICE_CLONE/CLEAR_STATE';
export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const getVoiceClone = (id) => {
  return async () => {
    const detail = await Market.getVoiceClone(id);
    return detail;
  };
};

export const jobTrigger = (params) => {
  return () => {
    return Market.jobTrigger(params);
  };
};

export const fetchVoiceCloneSpeakers = () => {
  return async (dispatch) => {
    const result = await Market.fetchVoiceCloneSpeakers();
    dispatch(setState({ speakers: _.values(result) }));
  };
};

export const fetchVoiceClones = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().marketVoiceClone;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const { items, total } = await Market.fetchVoiceClones(searchParams);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const retryVoiceClone = (params) => {
  return async (dispatch) => {
    await Market.importVoiceClone(params);
    dispatch(fetchVoiceClones());
  };
};

export const delVoiceClone = (id) => {
  return async (dispatch) => {
    await Market.delVoiceClone(id);
    dispatch(fetchVoiceClones());
  };
};

export const exportVoiceClone = (params) => {
  return async (dispatch) => {
    await Market.exportVoiceClone(params);
    dispatch(fetchVoiceClones());
  };
};

const _getInitState = () => {
  return {
    list: [],
    speakers: [],
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 10,
      orderBy: 'createdAt asc',
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
