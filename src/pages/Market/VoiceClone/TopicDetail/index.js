import './index.less';

import {
  AlertOutlined,
  DeleteOutlined,
  <PERSON>hOutlined,
  MessageOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  PlusSquareOutlined,
  RedoOutlined,
  RetweetOutlined,
  SettingOutlined,
  SwapOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons';
import { Toast } from '~/components';
import {
  Button,
  Checkbox,
  Divider,
  Dropdown,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Select,
  Typography,
} from 'antd';
import classNames from 'classnames';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.marketVoiceCloneTopicDetail;
  },
  actions,
)
export default class VoiceCloneTopicDetail extends Component {
  static propTypes = {
    detail: PropTypes.object.isRequired,
    speakers: PropTypes.array.isRequired,
    getVoiceClone: PropTypes.func.isRequired,
    removeVoiceClone: PropTypes.func.isRequired,
    insertVoiceClone: PropTypes.func.isRequired,
    regenVoiceClone: PropTypes.func.isRequired,
    updateVoiceClone: PropTypes.func.isRequired,
    updateVoiceCloneSpeed: PropTypes.func.isRequired,
    updateVoiceCloneOffset: PropTypes.func.isRequired,
    fetchVoiceCloneSpeakers: PropTypes.func.isRequired,
    setState: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
    match: PropTypes.object.isRequired,
  }

  state = {
    autoplay: true,
    playIndex: {},
    selectShot: {},
    shortAudioObj: {},
  }

  constructor(props) {
    super(props);
    this.audios = [];
    this.timeouts = [];
  }

  componentDidMount = async () => {
    const { id } = this.props.match.params;
    await this.props.fetchVoiceCloneSpeakers();

    if (!_.isUndefined(id)) {
      await this.props.getVoiceClone(id);
      this.initDetail();
    }
    this.fetchShortAudio();
    this.audio = new Audio();
    this.audio.addEventListener('ended', () => { this.setState({ playIndex: {} }); });
  }

  componentWillUnmount = () => {
    this.audio?.pause();
    this.props.clearState();
    this.onPauseAll();
  }

  fetchShortAudio = async () => { // eslint-disable-next-line
    const resp = await fetch(`https://video-clip.oss-cn-shanghai.aliyuncs.com/faas/speakers/short.json?v=${new Date().valueOf()}`);
    const data = await resp.json();
    this.setState({ shortAudioObj: data });
  }

  initDetail = () => {
    const { sentences } = this.props.detail;
    const durations = _.map(_.flatMap(_.map(sentences, 'shots')), 'duration');
    const totalDuration = _.reduce(durations, (total, num) => { return total + num; }, 0);

    this.setState({ totalDuration, usernames: _.uniq(_.map(sentences, 'speaker')) }, this.onAnalysis);
  }

  formatDuration = (totalSeconds = 0) => {
    let minutes = Math.floor(totalSeconds / 60);
    let seconds = _.ceil(totalSeconds % 60);

    // 如果分钟或秒钟小于10，前面添加一个'0'
    minutes = minutes < 10 ? `0${minutes}` : minutes;
    seconds = seconds < 10 ? `0${seconds}` : seconds;

    return `${minutes}:${seconds}`;
  }

  onAnalysis = () => {
    const { content } = this.props.detail;
    const lines = content.split('\n').filter((x) => { return !_.isEmpty(x); });
    const names = lines.map((line) => { return _.trim(line.split(/[：:]/)[0] || ''); });
    const usernames = _.uniq(names).filter((name) => { return !_.isEmpty(name); });
    const speakers = _.map(this.props.speakers, 'speaker');
    const userVoiceMap = {};

    usernames.forEach((name) => {
      const speaker = speakers.find((x) => { return x === name; });
      if (speaker) {
        userVoiceMap[name] = speaker;
      }
    });
    const t = content.replace(/\n\n/g, '\n');
    this.setState({ usernames });
    this.props.setState({ detail: { ...this.props.detail, content: t } });
  }

  onPlay = (item) => {
    if (this.audio.src === item.audioUrl) {
      if (this.audio.paused) {
        this.audio.play();
      } else {
        this.audio.pause();
      }
    } else {
      this.audio.src = item.audioUrl;
      if (!_.isUndefined(item.speed)) {
        this.audio.playbackRate = item.speed;
      }
      this.audio.play();
    }
    this.setState({});
  }

  onPlayAll = (startI = 0, startJ = 0, item) => {
    this.onPauseAll(item);
    if (!this.state.autoplay) {
      this.onPlay(item);
      this.setState({ playIndex: { sentenceIndex: startI, shotIndex: startJ } });
      return;
    }

    this.audios = [];
    this.timeouts = [];

    const { sentences } = this.props.detail;
    let totalDuration = 0;

    for (let i = startI; i < sentences.length; i++) {
      const { shots } = sentences[i];
      for (let j = i === startI ? startJ : 0; j < shots.length; j++) {
        const audio = new Audio(shots[j].audioUrl);
        if (!_.isUndefined(shots[j].speed)) {
          audio.playbackRate = shots[j].speed;
        }
        this.audios.push(audio);
        const delay = totalDuration * 1000;

        const timeoutId = setTimeout(() => {
          audio.play();
          this.setState({ playIndex: { sentenceIndex: i, shotIndex: j } });
        }, delay);
        this.timeouts.push(timeoutId);
        totalDuration += shots[j].duration;

        if (j <= shots.length - 1) {
          totalDuration += shots[j].offset;
        }
      }
    }
  }

  onPauseAll = (item) => {
    this.setState({ playIndex: {} });
    if (!this.state.autoplay && !this.audio.paused) {
      this.onPlay(item);
      return;
    }

    for (let i = 0; i < this.audios.length; i++) {
      this.audios[i].pause();
      clearTimeout(this.timeouts[i]);
    }
  }

  onRemoveShot = async (sentenceIndex, shotIndex) => {
    const sentences = _.cloneDeep(this.props.detail.sentences);
    const sentence = sentences[sentenceIndex];
    if (sentence.shots[shotIndex]?.isAdd) {
      sentence.shots.splice(shotIndex, 1);
      this.props.setState({ detail: { ...this.props.detail, sentences } });
      this.setState({ selectId: undefined, selectShot: {} });
      return;
    }

    const cloneId = this.props.detail.id;
    await this.props.removeVoiceClone({ cloneId, sentenceIndex, shotIndex });
    await this.props.getVoiceClone(cloneId);
    this.initDetail();
  }

  onAddShot = async (sentenceIndex, shotIndex) => {
    const sentences = _.cloneDeep(this.props.detail.sentences);
    const sentence = sentences[sentenceIndex];

    sentence.shots.splice(shotIndex + 1, 0, { isAdd: true });
    this.props.setState({ detail: { ...this.props.detail, sentences } });

    this.setState({
      selectId: `${sentenceIndex}-${shotIndex + 1}`,
      selectShot: { speaker: sentence.speaker, sentenceIndex, shotIndex: shotIndex + 1, isAdd: true },
    });
  }

  onRegenShot = async (item, sentenceIndex, shotIndex) => {
    const cloneId = this.props.detail.id;
    const detail = await this.props.regenVoiceClone({ cloneId, sentenceIndex, shotIndex, ...item });
    await this.props.getVoiceClone(detail?.id);
    this.initDetail();
  }

  onSelectShot = (sentence, sentenceIndex, shotIndex) => {
    if (this.state.selectId === `${sentenceIndex}-${shotIndex}`) {
      this.setState({ selectShot: {}, selectId: undefined });
      return;
    }

    this.setState({
      selectShot: { ...sentence.shots[shotIndex], speaker: sentence.speaker, sentenceIndex, shotIndex },
      selectId: `${sentenceIndex}-${shotIndex}`,
    });
  }

  onCreateShot = async () => {
    const cloneId = this.props.detail.id;
    const { sentenceIndex, shotIndex, speaker, text } = this.state.selectShot;
    const ref = this.props.speakers.find((x) => { return x.speaker === speaker; }).refs[0];
    await this.props.insertVoiceClone({ cloneId, sentenceIndex, shotIndex, speaker, text, ref });
    await this.props.getVoiceClone(cloneId);
    this.initDetail();
    this.setState({ selectShot: {}, selectId: undefined });
  };

  onSaveShot = async () => {
    const { selectShot, selectId } = this.state;
    if (_.isEmpty(selectShot) || _.isUndefined(selectId)) {
      return;
    }

    if (selectShot?.isAdd) {
      this.onCreateShot();
      return;
    }

    const cloneId = this.props.detail.id;
    const { speaker, sentenceIndex, shotIndex, ...item } = selectShot;
    const detail = await this.props.regenVoiceClone({ cloneId, sentenceIndex, shotIndex, ...item });
    await this.props.getVoiceClone(detail?.id);
    this.initDetail();
  }

  onChangeShotValue = async (value, type, sentenceIndex, shotIndex) => {
    const sentences = _.cloneDeep(this.props.detail?.sentences);
    sentences[sentenceIndex].shots[shotIndex][type] = value;
    this.props.setState({ detail: { ...this.props.detail, sentences } });
    const params = { cloneId: this.props.detail.id, sentenceIndex, shotIndex, [type]: value };
    if (type === 'offset') {
      await this.props.updateVoiceCloneOffset(params);
    }

    if (type === 'speed') {
      await this.props.updateVoiceCloneSpeed(params);
    }
  }

  onChangeAudioUrl = async () => {
    const { shortAudio, sentenceIndex, shotIndex } = this.state.shotObj;
    if (_.isUndefined(shortAudio)) {
      return;
    }

    const params = {
      cloneId: this.props.detail.id,
      sentenceIndex,
      shotIndex,
      audioUrl: shortAudio.url,
      duration: shortAudio.duration,
    };
    const data = await this.props.updateVoiceClone(params);
    await this.props.setState({ detail: data });
    this.setState({ shotObj: {}, openShort: false });
  }

  onEditRef = async (item, sentenceIndex, shotIndex) => {
    let { refs } = this.props.speakers.find((x) => { return x.speaker === item.speaker; });
    let ossRefs = [];
    try { // eslint-disable-next-line
      const resp = await fetch(`//video-clip.oss-cn-shanghai.aliyuncs.com/faas/speakers/${item.speaker}.json?v=${new Date().valueOf()}`);
      ossRefs = await resp.json();
      refs = [...refs, ...ossRefs];
    } catch (error) {
      // to do
    }

    this.setState({ openSpeaker: true, sentence: { ...item, sentenceIndex, shotIndex }, refs });
  }

  onChangeSpeaker = async () => {
    const { sentenceIndex, shotIndex, shots, ref } = this.state.sentence;
    const cloneId = this.props.detail.id;
    const refObj = (this.state.refs || []).find((x) => { return x.wavUrl === ref.wavUrl; });
    if (_.isUndefined(refObj)) {
      Toast.show('请选择声音', Toast.Type.WARNING);
      return;
    }

    const { text } = shots[shotIndex];
    await this.props.regenVoiceClone({ text, cloneId, sentenceIndex, shotIndex, ref: refObj });
    await this.props.getVoiceClone(cloneId);
    this.setState({ openSpeaker: false, sentence: {}, refs: [] });
  }

  onChangeRefSpeaker = async (value) => {
    let { refs } = this.props.speakers.find((x) => { return x.speaker === value; });
    let ossRefs = [];
    try { // eslint-disable-next-line
      const resp = await fetch(`//video-clip.oss-cn-shanghai.aliyuncs.com/faas/speakers/${value}.json?v=${new Date().valueOf()}`);
      ossRefs = await resp.json();
      refs = [...refs, ...ossRefs];
    } catch (error) {
      // to do
    }
    this.setState({ sentence: { ...this.state.sentence, speaker: value }, refs });
  }

  renderSpeakerModal = () => {
    const { sentence, openSpeaker, refs } = this.state;
    return (
      <Modal
        title="声音设置"
        width="50vw"
        visible={openSpeaker}
        onCancel={() => { return this.setState({ openSpeaker: false, sentence: {} }); }}
        onOk={() => { return this.onChangeSpeaker(); }}
      >
        <Select
          style={{ width: '100%', marginTop: -10, marginBottom: 10 }}
          value={sentence.speaker}
          onChange={(value) => { return this.onChangeRefSpeaker(value); }}
        >
          {
            (this.props.speakers || []).map((x) => {
              return <Select.Option value={x.speaker}>{x.speaker}</Select.Option>;
            })
          }
        </Select>
        <Radio.Group
          value={sentence.ref.wavUrl}
          onChange={(e) => {
            return this.setState({ sentence: { ...sentence, ref: { ...sentence.ref, wavUrl: e.target.value } } });
          }}
        >
          {
            (refs || []).map((x) => {
              return (
                <Radio value={x.wavUrl} style={{ width: '100%', marginBottom: 5 }}>
                  <div style={{ width: '40vw' }}>
                    <Input value={x.text} style={{ marginBottom: 2 }} />
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <audio controls style={{ height: 30 }} src={x.wavUrl} />
                      {_.isUndefined(x.name) ? '' : <span style={{ fontWeight: 'bold' }}>{x.name}</span>}
                    </div>
                  </div>
                </Radio>
              );
            })
          }
        </Radio.Group>
      </Modal>
    );
  }

  renderShortModal = () => {
    const { shortAudioObj, shotObj } = this.state;
    return (
      <Modal
        title="声音替换"
        width="50vw"
        visible={this.state.openShort}
        onCancel={() => { return this.setState({ openShort: false }); }}
        onOk={() => { return this.onChangeAudioUrl(); }}
      >
        {_.map(shortAudioObj, (v, k) => {
          return (
            <div>
              <h3>{k}</h3>
              <Radio.Group
                value={shotObj.shortAudio}
                onChange={(e) => { return this.setState({ shotObj: { ...shotObj, shortAudio: e.target.value } }); }}
              >
                {v.map((o) => { return <Radio key={o.url} value={o}>{o.name}</Radio>; })}
              </Radio.Group>
            </div>
          );
        })}
      </Modal>
    );
  }

  renderHeader = () => {
    return (
      <div style={{ display: 'flex', fontSize: 18, justifyContent: 'space-between', margin: '20px 0' }}>
        <div>
          总字数: {this.props.detail?.content?.length || 0}
          <Divider type="vertical" />
          总时长: {this.formatDuration(this.state.totalDuration)}
          <Divider type="vertical" />
          参与人数: {this.state.usernames?.length || 0}
        </div>
        <div>
          <Checkbox
            checked={this.state.autoplay}
            onChange={(e) => {
              const autoplay = e.target.checked;
              const stateObj = { autoplay };
              if (!e) {
                stateObj.playIndex = {};
                for (let i = 0; i < this.audios.length; i++) {
                  clearTimeout(this.timeouts[i]);
                }
              }
              return this.setState(stateObj);
            }}
          >
            连续播放
          </Checkbox>
        </div>
      </div>
    );
  }

  renderItem = (shot, sentence, shotIndex, sentenceIndex) => {
    const isPlay = this.state.playIndex.sentenceIndex === sentenceIndex
      && this.state.playIndex.shotIndex === shotIndex;

    return (
      <div className={classNames('chat-item',
        { 'chat-item-selected': this.state.selectId === `${sentenceIndex}-${shotIndex}` })}
      >
        <div className="chat-item-index" style={shotIndex ? { display: 'none' } : {}}>
          {sentenceIndex + 1}.
        </div>
        <div className="chat-item-content">
          <div style={{ display: 'flex' }}>
            <Select
              style={{ width: 120, height: 32 }}
              value={sentence.speaker}
            >
              {
                this.props.speakers.map((x) => {
                  return <Select.Option value={x.speaker}>{x.speaker}</Select.Option>;
                })
              }
            </Select>
            <Typography.Text
              style={{ width: 'calc(100% - 140px)', padding: '0 10px', cursor: 'pointer' }}
              onClick={() => { return this.onSelectShot(sentence, sentenceIndex, shotIndex); }}
            >
              {shot.text}
            </Typography.Text>
          </div>
          <div className="chat-item-btn">
            <div>
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'cSpeaker',
                      label: (
                        <RetweetOutlined
                          onClick={() => { return this.onEditRef(sentence, sentenceIndex, shotIndex); }}
                        />
                      ),
                    },
                    {
                      key: 'cShort',
                      label: (
                        <SwapOutlined
                          onClick={() => {
                            this.setState({ openShort: true, shotObj: { ...shot, sentenceIndex, shotIndex } });
                          }}
                        />
                      )
                      ,
                    },
                  ],
                }}
                trigger={['click']}
              >
                <Button icon={<SettingOutlined />} />
              </Dropdown>
              <Divider type="vertical" />
              <Popconfirm
                title="确定重新生成吗？"
                onConfirm={() => { return this.onRegenShot(shot, sentenceIndex, shotIndex); }}
              >
                <Button icon={<RedoOutlined />} />
              </Popconfirm>
            </div>
            <Divider type="vertical" />
            时长: <span style={{ width: 50 }}>{shot.duration}秒</span>
            <Divider type="vertical" />
            <InputNumber
              style={{ width: 160 }}
              addonBefore="语速："
              value={shot.speed}
              step={0.25}
              onChange={(value) => { this.onChangeShotValue(value, 'speed', sentenceIndex, shotIndex); }}
            />
            <Divider type="vertical" />
            <InputNumber
              style={{ width: 160 }}
              value={shot.offset}
              addonBefore="停顿："
              addonAfter="秒"
              step={0.1}
              onChange={(value) => { this.onChangeShotValue(value, 'offset', sentenceIndex, shotIndex); }}
            />
          </div>
          <Popconfirm
            title="确定删除吗？"
            onConfirm={() => { return this.onRemoveShot(sentenceIndex, shotIndex); }}
          >
            <DeleteOutlined style={{ fontSize: 18, position: 'absolute', bottom: 2 }} />
          </Popconfirm>
          <PlusSquareOutlined
            style={{ fontSize: 18, position: 'absolute', bottom: -10, left: 100 }}
            onClick={() => { return this.onAddShot(sentenceIndex, shotIndex); }}
          />
        </div>
        {
          isPlay ?
            <PauseCircleOutlined
              style={{ fontSize: 36, marginLeft: 4 }}
              onClick={() => { return this.onPauseAll(shot); }}
            />
            :
            <PlayCircleOutlined
              style={{ fontSize: 36, marginLeft: 4 }}
              onClick={() => { return this.onPlayAll(sentenceIndex, shotIndex, shot); }}
            />
        }
      </div>
    );
  }

  renderInputWrap = () => {
    const { selectShot, usernames } = this.state;
    if (_.isUndefined(usernames)) return null;

    return (
      <div className="chat-input-wrap">
        <div className="opt-item">
          <span>
            <UnorderedListOutlined style={{ fontSize: 18 }} />
            <Divider type="vertical" />
            <AlertOutlined style={{ fontSize: 18 }} />
          </span>
          <span>
            <MessageOutlined style={{ fontSize: 18 }} />
            <Divider type="vertical" />
            <MehOutlined style={{ fontSize: 18 }} />
          </span>
        </div>
        <Select
          style={{ width: 120 }}
          value={selectShot?.speaker || _.head(usernames)}
          onChange={(value) => { return this.setState({ selectShot: { ...selectShot, speaker: value } }); }}
        >
          {this.props.speakers.map((x) => { return <Select.Option value={x.speaker}>{x.speaker}</Select.Option>; })}
        </Select>
        <Input.TextArea
          placeholder="请输入内容"
          bordered={false}
          autoSize
          value={selectShot?.text}
          onChange={(e) => { return this.setState({ selectShot: { ...selectShot, text: e.target.value } }); }}
        />
        <Button onClick={() => { }} disabled>发送</Button>
        <Divider type="vertical" />
        <Button onClick={() => { return this.onSaveShot(); }}>保存</Button>
      </div>
    );
  }

  render = () => {
    const { sentences } = this.props.detail;
    return (
      <div className="chat-topic-detail">
        {this.renderHeader()}
        <div style={{ height: 'calc(100% - 150px)', overflow: 'auto' }}>
          {
            sentences.map((s, pIdx) => {
              return (
                <>
                  {s.shots.map((x, idx) => { return this.renderItem(x, s, idx, pIdx); })}
                </>
              );
            })
          }
        </div>
        {this.renderInputWrap()}

        {this.state.openSpeaker && this.renderSpeakerModal()}
        {this.state.openShort && this.renderShortModal()}
      </div>
    );
  }
}

export {
  reducer,
};
