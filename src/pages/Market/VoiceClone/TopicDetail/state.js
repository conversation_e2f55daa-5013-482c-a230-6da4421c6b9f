import { Market } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'mpArticle/SET_STATE';
const CLEAR_STATE = 'mpArticle/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const getVoiceClone = (id) => {
  return async (dispatch) => {
    const detail = await Market.getVoiceClone(id);
    dispatch(setState({ detail }));
  };
};

export const fetchVoiceCloneSpeakers = () => {
  return async (dispatch) => {
    const result = await Market.fetchVoiceCloneSpeakers();
    dispatch(setState({ speakers: _.values(result) }));
  };
};

export const createVoiceClone = (params) => {
  return async () => {
    const data = await Market.importVoiceClone(params);
    return data;
  };
};

export const insertVoiceClone = (params) => {
  return async () => {
    const data = await Market.insertVoiceClone(params);
    return data;
  };
};

export const removeVoiceClone = (params) => {
  return async () => {
    const data = await Market.removeVoiceClone(params);
    return data;
  };
};

export const updateVoiceCloneOffset = (params) => {
  return async () => {
    const data = await Market.updateVoiceCloneOffset(params);
    return data;
  };
};

export const updateVoiceCloneSpeed = (params) => {
  return async () => {
    const data = await Market.updateVoiceCloneSpeed(params);
    return data;
  };
};

export const updateVoiceClone = (params) => {
  return async () => {
    const data = await Market.updateVoiceClone(params);
    return data;
  };
};

export const regenVoiceClone = (params) => {
  return async () => {
    const data = await Market.regenVoiceClone(params);
    return data;
  };
};

const _getInitState = () => {
  return {
    detail: { sentences: [] },
    speakers: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
