import { FilterBar, PaginationTable, Toast } from '~/components';
import { But<PERSON>, Divider, Drawer, Form, Image, Input, Popconfirm, Radio, Select, Table } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

const PLATFORM_ENUM = {
  toutiao: { name: '头条', params: [{ key: 'url', name: '回调地址' }] },
  xiaoyuzhou: {
    name: '小宇宙',
    params: [
      { key: 'pid', name: 'PID' },
      { key: 'appid', name: 'APPID' },
      { key: 'refreshToken', name: 'TOKEN' },
    ],
  },
  douyin: { name: '抖音', params: [{ key: 'secUserId', name: '用户ID' }] },
  kuaishou: { name: '快手', params: [{ key: 'secUserId', name: '快手ID' }] },
};
@connect(
  (state) => {
    return state.marketPublish;
  },
  actions,
)
export default class MarketPublish extends Component {
  static propTypes = {
    list: PropTypes.array,
    total: PropTypes.number,
    pagination: PropTypes.object,
    fetchCharacters: PropTypes.func.isRequired,
    addCharacter: PropTypes.func.isRequired,
    updateCharacter: PropTypes.func.isRequired,
    delCharacter: PropTypes.func.isRequired,
    fetchAccounts: PropTypes.func.isRequired,
    addAccount: PropTypes.func.isRequired,
    updateAccount: PropTypes.func.isRequired,
    delAccount: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    open: false,
    data: {},
    accountInfo: {},
    detail: { platform: 'xiaoyuzhou' },
  }

  componentDidMount = async () => {
    await this.props.fetchCharacters();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e?.target.value : e;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onChangeDetailValue = (e, key) => {
    const value = e?.target ? e?.target.value : e;
    this.setState({ detail: { ...this.state.detail, [key]: value } });
  }

  onChangeDetailParams = (e, key) => {
    const params = _.cloneDeep(this.state.detail?.params) || {};
    const value = e?.target ? e?.target.value : e;
    params[key] = value;
    this.onChangeDetailValue(params, 'params');
  }

  onShowAccountDrawer = async (id) => {
    const accounts = await this.props.fetchAccounts({ characterId: id });
    this.setState({ accountOpen: true, accountInfo: { list: accounts, id } });
  }

  onSearch = async (e) => {
    await this.props.fetchCharacters(e);
  };

  onDelAccount = async (id) => {
    await this.props.delAccount(id);
    await this.onShowAccountDrawer(this.state.accountInfo.id);
  }

  onSubmitAccount = async () => {
    const { accountInfo, detail } = this.state;
    if (_.isEmpty(detail?.name) || _.isEmpty(detail?.params)) {
      Toast.show('请完善信息', Toast.Type.WARNING);
      return;
    }
    if (_.isUndefined(detail?.id)) {
      await this.props.addAccount({ characterId: accountInfo.id, ...detail });
    } else {
      await this.props.updateAccount({ characterId: accountInfo.id, ...detail });
    }
    await this.onShowAccountDrawer(accountInfo.id);
    this.setState({ detailOpen: false, detail: {} });
  }

  onSubmit = async () => {
    const { id, name, description, background, publishType } = this.state.data;
    if (_.isEmpty(name)) {
      Toast.show('请完善信息!', Toast.Type.WARNING);
      return;
    }

    if (_.isUndefined(id)) {
      await this.props.addCharacter({ name, description, background, publishType });
    } else {
      await this.props.updateCharacter({ id, name, description, background, publishType });
    }
    this.setState({ open: false, data: {} });
    Toast.show('操作成功!', Toast.Type.SUCCESS);
  }

  renderModal = () => {
    const { open, data } = this.state;
    return (
      <Drawer
        open={open}
        width="30vw"
        title={_.isEmpty(data) ? '新增' : '编辑'}
        onClose={() => { return this.setState({ open: false, data: {} }); }}
        extra={<Button type="primary" onClick={this.onSubmit}>保存</Button>}
      >
        <Input
          placeholder="请输入昵称"
          value={data?.name}
          onChange={(e) => { return this.onChangeValue(e, 'name'); }}
        />
        <Input.TextArea
          placeholder="请输入一句话简介"
          style={{ margin: '5px 0' }}
          value={data?.description}
          autoSize={{ minRows: 10 }}
          onChange={(e) => { return this.onChangeValue(e, 'description'); }}
        />
        <Input.TextArea
          placeholder="请输入人物背景"
          style={{ margin: '5px 0' }}
          value={data?.background}
          autoSize={{ minRows: 10 }}
          onChange={(e) => { return this.onChangeValue(e, 'background'); }}
        />
        <Input.Group>
          <Button type="text">发布设置</Button>
          <Radio.Group
            style={{ margin: '5px 0' }}
            value={data?.publishType}
            onChange={(e) => { return this.onChangeValue(e, 'publishType'); }}
          >
            <Radio value="auto">自动</Radio>
            <Radio value="manual">手动</Radio>
          </Radio.Group>
        </Input.Group>
      </Drawer>
    );
  };

  renderAccountDrawer = () => {
    const { accountOpen, accountInfo } = this.state;
    return (
      <Drawer
        open={accountOpen}
        width="50vw"
        title="发布账号"
        onClose={() => { return this.setState({ accountOpen: false, accountInfo: {} }); }}
      >
        <Button
          type="primary"
          style={{ float: 'right' }}
          onClick={() => { return this.setState({ detailOpen: true }); }}
        >
          新增账号
        </Button>
        <Table
          dataSource={accountInfo.list}
          columns={[
            { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
            {
              title: '平台',
              dataIndex: 'platform',
              key: 'platform',
              align: 'center',
              render: (txt) => { return PLATFORM_ENUM[txt]?.name; },
            },
            { title: '昵称', dataIndex: 'name', key: 'name', align: 'center' },
            {
              title: '操作',
              dataIndex: 'opt',
              key: 'opt',
              align: 'center',
              render: (txt, row) => {
                return (
                  <>
                    <a onClick={() => { return this.setState({ detailOpen: true, detail: row }); }}>编辑</a>
                    <Divider type="vertical" />
                    <Popconfirm title="是否删除?!" onConfirm={() => { return this.onDelAccount(row.id); }}>
                      <a>删除</a>
                    </Popconfirm>
                  </>
                );
              },
            },
          ]}
        />
      </Drawer>
    );
  }

  renderAccountParams = (detail) => {
    const { platform, params } = detail;
    return (
      <>
        {
          (PLATFORM_ENUM[platform]?.params || []).map((x) => {
            return (
              <Form.Item labelCol={{ span: 3 }} label={x.name}>
                <Input
                  value={(params || {})[x.key]}
                  onChange={(e) => { return this.onChangeDetailParams(e, x.key); }}
                />
              </Form.Item>
            );
          })
        }
      </>
    );
  }

  renderAccountDetailDrawer = () => {
    const { detailOpen, detail } = this.state;
    return (
      <Drawer
        open={detailOpen}
        width="40vw"
        title="账号"
        onClose={() => { return this.setState({ detailOpen: false, detail: {} }); }}
      >
        <Form labelCol={{ span: 3 }}>
          <Form.Item label="平台">
            <Select value={detail?.platform} onChange={(e) => { return this.onChangeDetailValue(e, 'platform'); }}>
              {_.map(PLATFORM_ENUM, (v, k) => { return <Select.Option value={k}>{v.name}</Select.Option>; })}
            </Select>
          </Form.Item>
          <Form.Item label="账号名称">
            <Input value={detail?.name} onChange={(e) => { return this.onChangeDetailValue(e, 'name'); }} />
          </Form.Item>
          <Form.Item>
            {this.renderAccountParams(detail)}
          </Form.Item>
        </Form>

        <Button type="primary" onClick={this.onSubmitAccount}>保存</Button>
      </Drawer>
    );
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '账号名称', dataIndex: 'name', key: 'name', align: 'center' },
      { title: '简介', dataIndex: 'description', key: 'description', align: 'center', ellipsis: true, width: '20%' },
      { title: '背景', dataIndex: 'background', key: 'background', align: 'center', ellipsis: true, width: '40%' },
      {
        title: '二维码',
        dataIndex: 'bindQr',
        key: 'bindQr',
        align: 'center',
        render: (url) => {
          if (_.isEmpty(url)) return '-';
          return <Image src={url} width={50} height={50} />;
        },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <a onClick={() => { return this.onShowAccountDrawer(row.id); }}>发布账号</a>
              <Divider type="vertical" />
              <a onClick={() => { return this.$push(`/market-publish/${row.id}/materials`); }}>文稿</a>
              <Divider type="vertical" />
              <a onClick={() => { return this.setState({ open: true, data: row }); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm title="是否删除?!" onConfirm={() => { return this.props.delCharacter(row.id); }}>
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  };

  render = () => {
    const { total, list, pagination } = this.props;

    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <FilterBar
          canAdd
          shouldShowSearchInput={false}
          onAdd={() => { return this.setState({ open: true, data: { publishType: 'auto' } }); }}
        />
        <PaginationTable
          totalDataCount={total}
          dataSource={list}
          pagination={pagination}
          columns={this.renderColumns()}
          onPaginationChange={(e) => { return this.onSearch(e); }}
        />

        {this.state.open && this.renderModal()}
        {this.state.accountOpen && this.renderAccountDrawer()}
        {this.state.detailOpen && this.renderAccountDetailDrawer()}
      </div>
    );
  };
}

export {
  reducer,
};
