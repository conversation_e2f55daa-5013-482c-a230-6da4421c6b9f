import { RedoOutlined } from '@ant-design/icons';
import { FilterBar, InputUpload, PaginationTable, Toast } from '~/components';
import { Button, DatePicker, Divider, Drawer, Form, Input, Popconfirm, Select, Tag, Tooltip } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

const PLATFORM_MAP = { douyin: '抖音', kuaishou: '快手' };
@connect(
  (state) => {
    return state.marketPublishMaterial;
  },
  actions,
)
export default class MarketPublishMaterial extends Component {
  static propTypes = {
    list: PropTypes.array,
    total: PropTypes.number,
    characterId: PropTypes.string,
    pagination: PropTypes.object,
    fetchMaterials: PropTypes.func,
    addMaterial: PropTypes.func,
    updateMaterial: PropTypes.func,
    delMaterial: PropTypes.func,
    match: PropTypes.object,
    setState: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    open: false,
    data: {},
  }

  componentDidMount = async () => {
    const { id } = this.props.match.params;
    await this.props.fetchMaterials({ characterId: id });
    this.props.setState({ characterId: id });
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e?.target.value : e;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onSearch = async (e) => {
    await this.props.fetchMaterials(e);
  };

  onSubmit = async () => {
    const { characterId } = this.props;
    const { id, title, content, tags, publishScheduledAt } = this.state.data;
    if (_.isEmpty(title) || _.isEmpty(content)) {
      Toast.show('请完善信息!', Toast.Type.WARNING);
      return;
    }
    if (_.isUndefined(id)) {
      await this.props.addMaterial({ title, content, characterId, tags, publishScheduledAt });
    } else {
      await this.props.updateMaterial(this.state.data);
    }
    this.setState({ open: false, data: {} });
    Toast.show('操作成功!', Toast.Type.SUCCESS);
  }

  renderDetailDrawer = () => {
    const { open, data } = this.state;
    return (
      <Drawer
        open={open}
        width="30vw"
        title={_.isEmpty(data) ? '新增' : '编辑'}
        onClose={() => { return this.setState({ open: false, data: {} }); }}
        extra={<Button type="primary" onClick={this.onSubmit}>保存</Button>}
      >
        <Form labelCol={{ span: 2 }}>
          <Form.Item label="标题">
            <Input
              value={data?.title}
              onChange={(e) => { return this.onChangeValue(e, 'title'); }}
            />
          </Form.Item>
          <Form.Item label="文稿">
            <Input.TextArea
              autoSize={{ minRows: 10, maxRows: 20 }}
              value={data?.content}
              onChange={(e) => { return this.onChangeValue(e, 'content'); }}
            />
          </Form.Item>
          <Form.Item label="话题">
            <Select
              mode="tags"
              open={false}
              value={data?.tags}
              onChange={(e) => { return this.onChangeValue(e, 'tags'); }}
            />
          </Form.Item>
          <Form.Item label="定时">
            <DatePicker
              showTime
              format="YYYY-MM-DD HH:mm"
              showToday={false}
              value={data?.publishScheduledAt ? moment(data?.publishScheduledAt) : undefined}
              onChange={(e) => {
                return this.onChangeValue(e.format('YYYY-MM-DDTHH:mm:ss.SSSSSS'), 'publishScheduledAt');
              }}
            />
          </Form.Item>
          <Form.Item label="音频">
            <InputUpload
              accept="audio/*"
              url={data?.audioUrl}
              onChange={(e) => { return this.onChangeValue(e, 'audioUrl'); }}
            />
          </Form.Item>
          <Form.Item label="视频">
            <InputUpload
              accept="video/*"
              url={data?.videoUrl}
              onChange={(e) => { return this.onChangeValue(e, 'videoUrl'); }}
            />
          </Form.Item>
        </Form>
      </Drawer>
    );
  };

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '标题', dataIndex: 'title', key: 'title', align: 'center' },
      { title: '内容', dataIndex: 'content', key: 'content', align: 'center', ellipsis: true },
      {
        title: '音频',
        dataIndex: 'audioUrl',
        key: 'audioUrl',
        align: 'center',
        render: (url) => { return _.isEmpty(url) ? '-' : <a target="_blank" href={url} rel="noreferrer">试听</a>; },
      },
      {
        title: '视频',
        dataIndex: 'videoUrl',
        key: 'videoUrl',
        align: 'center',
        render: (url) => { return _.isEmpty(url) ? '-' : <a target="_blank" href={url} rel="noreferrer">预览</a>; },
      },
      {
        title: '是否草稿',
        dataIndex: 'isDraft',
        key: 'isDraft',
        align: 'center',
        render: (isDraft, row) => {
          let status = isDraft;
          if (!_.isEmpty(row.publishAt)) {
            status = false;
          }
          return (
            <>
              {status ? <Tag color="red">草稿</Tag> : <Tag color="green">已发布</Tag>}
              <Popconfirm
                title="是否修改草稿状态?!"
                onConfirm={async () => {
                  await this.setState({ data: { ...row, isDraft: !isDraft } });
                  return this.onSubmit();
                }}
              >
                <RedoOutlined />
              </Popconfirm>
            </>
          );
        },
      },
      {
        title: '发布状态',
        dataIndex: 'publishStatus',
        key: 'publishStatus',
        render: (txt) => {
          if (_.isEmpty(txt)) return '-';
          const content = [];
          txt.forEach((x) => {
            let s = null;
            if (x.status === 'pending') {
              s = (<Tag color="blue">未发布</Tag>);
            } else if (x.status === 'done') {
              s = (<Tag color="green">已发布</Tag>);
            } else {
              s = (<Tooltip title={x.tatusDetail}><Tag color="red">发布失败</Tag></Tooltip>);
            }
            content.push(<div style={{ marginBottom: 5 }}>{PLATFORM_MAP[x.platform]}: {s}</div>);
          });

          return content;
        },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <a onClick={() => { return this.setState({ open: true, data: row }); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm title="是否删除?!" onConfirm={() => { return this.props.delMaterial(row.id); }}>
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  };

  renderSelects = () => {
    return (
      <Select
        allowClear
        value={this.state.isDraft}
        style={{ width: 160, marginBottom: 16 }}
        placeholder="请选择是否草稿"
        onChange={(e) => { return this.onSearch({ isDraft: e }); }}
      >
        <Select.Option value={null}>全部</Select.Option>
        <Select.Option value>草稿</Select.Option>
        <Select.Option value={false}>非草稿</Select.Option>
      </Select>
    );
  }

  render = () => {
    const { total, list, pagination } = this.props;

    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <FilterBar
          canAdd
          shouldShowSearchInput={false}
          renderSelects={this.renderSelects}
          onAdd={() => { return this.setState({ open: true, data: {} }); }}
        />
        <PaginationTable
          totalDataCount={total}
          dataSource={list}
          pagination={pagination}
          columns={this.renderColumns()}
          onPaginationChange={(e) => { return this.onSearch(e); }}
        />

        {this.state.open && this.renderDetailDrawer()}
      </div>
    );
  };
}

export {
  reducer,
};
