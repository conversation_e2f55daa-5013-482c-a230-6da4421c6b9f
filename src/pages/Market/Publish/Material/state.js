import { MeMe } from '~/engine';

const SET_STATE = 'MARKET_MEME_MATERIAL/SET_STATE';
const CLEAR_STATE = 'MARKET_MEME_MATERIAL/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchMaterials = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination, characterId } = getState().marketPublishMaterial;
    const searchParams = {
      characterId,
      ...params,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const { items, total } = await MeMe.fetchMaterials(searchParams);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const addMaterial = (params) => {
  return async (dispatch) => {
    await MeMe.addMaterial(params);
    dispatch(fetchMaterials());
  };
};

export const updateMaterial = (params) => {
  return async (dispatch) => {
    await MeMe.updateMaterial(params);
    dispatch(fetchMaterials());
  };
};

export const delMaterial = (id) => {
  return async (dispatch) => {
    await MeMe.delMaterial(id);
    dispatch(fetchMaterials());
  };
};

const _getInitState = () => {
  return {
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    list: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
