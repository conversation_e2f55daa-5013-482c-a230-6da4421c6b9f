import { Market } from '~/engine';

const SET_STATE = 'MARKET_PUBLISH/SET_STATE';
const CLEAR_STATE = 'MARKET_PUBLISH/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchCharacters = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().marketPublish;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const { items, total } = await Market.fetchPublishCharacters(searchParams);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const addCharacter = (params) => {
  return async (dispatch) => {
    await Market.addPublishCharacter(params);
    dispatch(fetchCharacters());
  };
};

export const updateCharacter = (params) => {
  return async (dispatch) => {
    await Market.updatePublishCharacter(params);
    dispatch(fetchCharacters());
  };
};

export const delCharacter = (id) => {
  return async (dispatch) => {
    await Market.delPublishCharacter(id);
    dispatch(fetchCharacters());
  };
};

export const fetchAccounts = (params = {}) => {
  return async () => {
    const { items } = await Market.fetchCharacterAccounts(params);
    return items;
  };
};

export const addAccount = (params = {}) => {
  return async () => {
    await Market.addCharacterAccount(params);
  };
};
export const updateAccount = (params = {}) => {
  return async () => {
    await Market.updateCharacterAccount(params);
  };
};

export const delAccount = (id) => {
  return async () => {
    await Market.delCharacterAccount(id);
  };
};

const _getInitState = () => {
  return {
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    list: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
