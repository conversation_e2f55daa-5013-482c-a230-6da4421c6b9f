import { CopyOutlined } from '@ant-design/icons';
import { FilterBar, PaginationTable, Toast } from '~/components';
import { Form, Input, Modal, Tag } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.marketInvitation;
  },
  actions,
)
export default class MarketInvitation extends Component {
  static propTypes = {
    list: PropTypes.array,
    total: PropTypes.number,
    pagination: PropTypes.object,
    getCodes: PropTypes.func,
    addCode: PropTypes.func,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    open: false,
    data: {},
  }

  componentDidMount = async () => {
    await this.props.getCodes();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e?.target.value : e;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onSearch = async (e) => {
    await this.props.getCodes(e);
  };

  onSubmit = async () => {
    const { invitee } = this.state.data;
    if (_.isEmpty(invitee)) {
      Toast.show('请输入受邀用户', Toast.Type.WARNING);
      return;
    }

    await this.props.addCode({ invitee });
    this.setState({ open: false, data: {} });
    Toast.show('新增成功!', Toast.Type.SUCCESS);
  }

  renderModal = () => {
    const { open, data } = this.state;
    return (
      <Modal
        open={open}
        title={_.isEmpty(data) ? '新增' : '编辑'}
        onCancel={() => { return this.setState({ open: false, data: {} }); }}
        onOk={this.onSubmit}
      >
        <Form>
          <Form.Item label="受邀用户">
            <Input
              placeholder="请输入受邀用户"
              value={data?.username}
              onChange={(e) => { return this.onChangeValue(e, 'invitee'); }}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  };

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '邀请人', dataIndex: 'inviter', key: 'inviter', align: 'center' },
      { title: '受邀人', dataIndex: 'invitee', key: 'invitee', align: 'center' },
      {
        title: '邀请链接',
        dataIndex: 'code',
        key: 'code',
        align: 'center',
        width: '30%',
        render: (code) => {
          const url = `https://${window.location.host}/account/login?invitation_code=${code}`;
          return (
            <Input
              value={url}
              size="small"
              addonAfter={<CopyOutlined onClick={
                async () => {
                  await navigator.clipboard.writeText(url);
                  Toast.show('复制成功', Toast.Type.SUCCESS);
                }}
              />}
            />
          );
        },
      },
      {
        title: '是否激活',
        dataIndex: 'redeemed',
        key: 'redeemed',
        align: 'center',
        render: (redeemed) => { return redeemed ? <Tag color="red">已激活</Tag> : <Tag color="green">未激活</Tag>; },
      },
    ];
  };

  render = () => {
    const { total, list, pagination } = this.props;

    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <FilterBar
          canAdd
          shouldShowSearchInput={false}
          onAdd={() => { return this.setState({ open: true, data: {} }); }}
        />
        <PaginationTable
          totalDataCount={total}
          dataSource={list}
          pagination={pagination}
          columns={this.renderColumns()}
          onPaginationChange={(e) => { return this.onSearch(e); }}
        />

        {this.state.open && this.renderModal()}
      </div>
    );
  };
}

export {
  reducer,
};
