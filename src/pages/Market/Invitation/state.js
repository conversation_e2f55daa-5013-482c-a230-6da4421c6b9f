import { Market } from '~/engine';

const SET_STATE = 'MARKET_INVITATION/SET_STATE';
const CLEAR_STATE = 'MARKET_INVITATION/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const getCodes = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().marketInvitation;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const { items, total } = await Market.getInviteCodes(searchParams);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const addCode = (params) => {
  return async (dispatch) => {
    await Market.addInviteCode(params);
    dispatch(getCodes());
  };
};

const _getInitState = () => {
  return {
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    list: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
