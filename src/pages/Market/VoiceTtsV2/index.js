import { Toast } from '~/components';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '~/engine';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Divider, Input, InputNumber, Select, Tooltip, Typography } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import AddVoiceButton from './components/AddVocieButton';
import ASRList from './components/ASRList';
import AudioUploader from './components/AudioUploader';
import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.marketVoiceTtsV2;
  },
  actions,
)
export default class VoiceTtsV2 extends Component {
  static propTypes = {
    article: PropTypes.string.isRequired,
    sentences: PropTypes.array.isRequired,
    tmpSpeakers: PropTypes.array,
    title: PropTypes.string,
    articleAudioUrl: PropTypes.string.isRequired,
    articleSpeaker: PropTypes.object.isRequired,
    groupedSpeakers: PropTypes.object.isRequired,
    audioSpeed: PropTypes.number.isRequired,
    audioGenerating: PropTypes.bool.isRequired,
    fetchVoiceTrain: PropTypes.func.isRequired,
    asrVoiceTest: PropTypes.func.isRequired,
    setState: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
    trainVoice: PropTypes.func.isRequired,
    addVoice: PropTypes.func.isRequired,
    getVoiceTest: PropTypes.func.isRequired,
    ttsVoiceTest: PropTypes.func.isRequired,
    ttsVoiceTestSentence: PropTypes.func.isRequired,
    match: PropTypes.object.isRequired,
  }

  state = {
    isAsrArticle: false,
    isRunning: false,
    runType: '',
    originalAudioUrl: '',
  };

  componentDidMount = async () => {
    const { id } = this.props.match.params;
    await this.props.fetchVoiceTrain();
    if (!_.isUndefined(id)) {
      await this.initVoiceTest(id);
    }
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  initVoiceTest = async (id) => {
    const data = await this.props.getVoiceTest(id);
    const { audioUrl, asrStatus, ttsStatus, ttsSettings, title, speed, sentences, ttsUrl } = data;
    const article = sentences.map((s) => { return s.text; }).join('');
    const articleSpeaker = this.props.tmpSpeakers.find((x) => {
      return x.ttsSettings.provider === ttsSettings.provider && x.ttsSettings.voice === ttsSettings.voice;
    });
    this.props.setState({
      article, articleSpeaker, title, sentences, audioSpeed: speed, articleAudioUrl: ttsUrl,
    });
    let stateParams = { isRunning: false, runType: '' };
    if (asrStatus === 'running') {
      stateParams = { isRunning: true, runType: 'asr' };
    }
    if (ttsStatus === 'running') {
      stateParams = { isRunning: true, runType: 'tts' };
    }
    this.setState({ ...stateParams, originalAudioUrl: audioUrl });
  }

  setTextBreakTime = (text, breakTime) => {
    if (breakTime) {
      return `<<BREAK=${breakTime}>>${text}`;
    }
    return text;
  }

  handleAsrUpload = async (file, asrProvider) => {
    const url = await AliyunHelper.uploadMp3(file);
    const { id } = this.props.match.params;
    await this.props.asrVoiceTest({ id, file: url, provider: asrProvider });

    Toast.show('上传成功, ASR后台运行中', Toast.Type.SUCCESS);
    this.setState({ isRunning: true, runType: 'asr' });
    // url, asrProvider
    // await this.props.audioToText(url, asrProvider);
    // this.setState({ isAsrArticle: true });
  };

  handleArticleChange = (newText) => {
    const splitSentences = newText.split(/(?<=[。！？!?\n])/).filter(Boolean)
      .filter((text) => { return text.trim() !== ''; })
      .map((text, index) => { return { index, text, ttsUrl: '' }; });
    this.props.setState({ article: newText, sentences: splitSentences });
  }

  handleSentencesChange = (index, sentence) => {
    const { originalAudioUrl } = this.state;
    const updatedSentences = [...this.props.sentences];
    updatedSentences[index] = sentence;
    if (_.isEmpty(originalAudioUrl)) {
      for (let i = 0; i < updatedSentences.length; i++) {
        updatedSentences[i].index = i;
      }
    }
    const newArticle = updatedSentences.map((s) => { return s.text; }).join('');
    this.props.setState({ sentences: updatedSentences, article: newArticle });
  };

  handleGenerateAudio = async (index, service) => {
    // 调用文本转音频服务，并更新音频URL（模拟）
    const updatedSentences = [...this.props.sentences];
    const s = updatedSentences[index];
    const { ttsUrl } = await this.props.ttsVoiceTestSentence({
      id: this.props.match.params.id,
      index,
      text: s.text,
      ttsSettings: service.ttsSettings,
      speed: s.speed,
      breakTime: s.breakTime,
    });
    updatedSentences[index].ttsUrl = ttsUrl;
    this.props.setState({ sentences: updatedSentences });
    // try {
    //   const { fileUrl } = await this.props.textToAudio(text, ttsSettings);
    //   updatedSentences[index].audioUrl = fileUrl;
    //   this.props.setState({ sentences: updatedSentences });
    // } catch (error) {
    //   message.error('生成音频失败！');
    // }
  };

  handleSelectSpeaker = (value) => {
    const { tmpSpeakers } = this.props;
    const speaker = tmpSpeakers.find((option) => { return option.value === value; });
    this.props.setState({ articleSpeaker: speaker });
  }

  handleGenerateArticleAudio = async () => {
    const { sentences, articleSpeaker, audioSpeed } = this.props;
    const ttsSettings = { ...articleSpeaker.ttsSettings, speed: audioSpeed };
    const ns = sentences.map((x) => { return { ...x, ttsSettings: { provider: '' } }; });
    await this.props.ttsVoiceTest({ id: this.props.match.params.id, sentences: ns, ttsSettings });
    Toast.show('TTS后台运行中', Toast.Type.SUCCESS);
    this.setState({ isRunning: true, runType: 'tts' });
    // try {
    //   const { fileUrl } = await this.props.textToAudio(article, ttsSettings);
    //   this.props.setState({ articleAudioUrl: fileUrl, audioGenerating: false });
    // } catch (error) {
    //   message.error('生成音频失败！');
    //   this.props.setState({ audioGenerating: false });
    // }
  }

  handleTrainVoiceTrain = async (file, provider, voiceId, name, sampleText, voiceText, isPreview) => {
    const url = await AliyunHelper.uploadMp3(file);
    const data = await this.props.trainVoice({
      voices: [url], provider, voiceId, name, sampleText, voiceText, isPreview,
    });
    await this.props.fetchVoiceTrain();
    return data;
  }

  handleAddVoice = async (provider, voiceId, name, sampleText, voiceText) => {
    await this.props.addVoice({ provider, voiceId, name, sampleText, voiceText });
    await this.props.fetchVoiceTrain();
  }

  onRefresh = async () => {
    const { id } = this.props.match.params;
    if (!_.isUndefined(id)) {
      await this.initVoiceTest(id);
    }
  }

  render = () => {
    const { isAsrArticle, isRunning, runType } = this.state;
    const { sentences, tmpSpeakers, article, articleAudioUrl, audioSpeed, title,
      audioGenerating, articleSpeaker, groupedSpeakers } = this.props;

    return (
      <div>
        <div style={{ position: 'absolute', left: '50%', transform: 'translateX(-50%)', zIndex: 10 }}>
          <Typography.Title level={2} style={{ marginBottom: 0 }}>{title}</Typography.Title>
          {
            isRunning &&
            <div>
              {_.toUpper(runType)} 后台运行中, 点击
              <Button type="link" onClick={() => { return this.onRefresh(); }}>刷新</Button>
            </div>
          }
        </div>
        <Collapse
          className="chat-knowledge"
          style={{ padding: 30, background: '#fff' }}
          defaultActiveKey={['asr', 'tts']}
          bordered={false}
        >
          <Collapse.Panel
            header="语音转文字"
            extra={<AudioUploader onUpload={this.handleAsrUpload} />}
            key="asr"
            showArrow={false}
            collapsible="header"
          >
            <Alert
              style={{ marginBottom: 10 }}
              message="当修改文本时，下方的停顿和语速会全部重置！"
              type="warning"
            />
            <Input.TextArea
              placeholder="输入文本"
              rows={15}
              value={article}
              disabled={isAsrArticle}
              style={isAsrArticle && { background: '#fff', color: 'rgba(0, 0, 0, 0.85)' }}
              onChange={(e) => { return this.handleArticleChange(e.target.value); }}
            />
            {articleAudioUrl !== '' && <audio src={articleAudioUrl} controls />}
          </Collapse.Panel>
          <Collapse.Panel
            header="文字转语音"
            extra={
              article !== '' &&
              <>
                <Tooltip title="目前仅火山支持停顿">
                  <Select
                    style={{ width: 250 }}
                    placeholder="选择语音"
                    value={articleSpeaker}
                    onChange={this.handleSelectSpeaker}
                  >
                    {Object.entries(groupedSpeakers).map(([provider, speakers]) => {
                      return (
                        <Select.OptGroup key={provider} label={provider}>
                          {speakers.map((speaker) => {
                            return (
                              <Select.Option key={speaker.value} value={speaker.value}>
                                {speaker.label}
                              </Select.Option>
                            );
                          })}
                        </Select.OptGroup>
                      );
                    })}
                  </Select>
                </Tooltip>
                <AddVoiceButton onTrain={this.handleTrainVoiceTrain} onAdd={this.handleAddVoice} />
                <Divider type="vertical" />
                <InputNumber
                  step={0.1}
                  min={0.8}
                  max={2}
                  value={audioSpeed}
                  precision={1}
                  onChange={(value) => { return this.props.setState({ audioSpeed: value }); }}
                  addonBefore="语速"
                />
                <Divider type="vertical" />
                <Button
                  type="primary"
                  disabled={sentences.length === 0 || !articleSpeaker}
                  onClick={this.handleGenerateArticleAudio}
                  loading={audioGenerating}
                >
                  生成音频
                </Button>
              </>
            }
            key="tts"
            showArrow={false}
            collapsible={sentences.length > 0 ? 'header' : 'icon'}
          >
            <div style={{ overflow: 'auto', maxHeight: 400 }}>
              {
                sentences.length > 0 &&
                <ASRList
                  items={sentences}
                  speakers={tmpSpeakers}
                  onSentencesChange={this.handleSentencesChange}
                  onGenerateAudio={this.handleGenerateAudio}
                />
              }
            </div>
          </Collapse.Panel>
        </Collapse>
      </div>
    );
  }
}

export {
  reducer,
};
