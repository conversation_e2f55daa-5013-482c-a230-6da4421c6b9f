import { Accounts, ChatBot, Market, VoiceTTS } from '~/engine';
import _ from 'lodash';

// import Azure from '../VoiceClone/Detail/azure';

const SET_STATE = 'MARKET_VOICE_TTS/SET_STATE';
const CLEAR_STATE = 'MARKET_VOICE_TTS/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchVoiceTrain = () => {
  return async (dispatch) => {
    const items = await ChatBot.fetchAllVoiceTrain();
    const speakers = await Market.fetchVoiceCloneSpeakers();
    const azureSpeakers = await Accounts.fetchTtsSpeakers();

    const tmpSpeakers = [
      ...azureSpeakers,
      ..._.values(items).map((item) => {
        return {
          label: item.name,
          value: item.id,
          ttsSettings: { provider: item.provider, voice: item.voiceId },
        };
      }),
      ..._.values(speakers).map((item) => {
        return {
          label: item.speaker,
          value: item.speaker,
          ttsSettings: { provider: 'gpt-sovits', voice: item.speaker, ref: item.refs[0] },
        };
      }),
    ];

    const groupedSpeakers = _.groupBy(tmpSpeakers, (item) => {
      return item.ttsSettings.provider;
    });

    dispatch(setState({ tmpSpeakers, groupedSpeakers }));
  };
};

export const audioToText = (url, asrProvider) => {
  return async (dispatch, getState) => {
    const { sentences } = getState().marketVoiceTtsV2;
    const result = await Market.audioToText({ file: url, provider: asrProvider });
    let startIndex = 0;
    if (sentences.length > 0) {
      startIndex = sentences[sentences.length - 1].id + 1;
    }
    let lastEndTime = 0;
    for (let i = 0; i < result.sentences.length; i++) {
      const s = result.sentences[i];
      const startTime = s.startTime || s.beginTime || 0;
      const endTime = s.endTime || 0;
      s.id = startIndex + i;
      s.breakTime = parseFloat(((startTime - lastEndTime) / 1000).toFixed(1));
      lastEndTime = endTime;
    }
    const article = result.sentences.map((s) => { return s.text; }).join('');
    dispatch(setState({ sentences: result.sentences, article }));
  };
};

export const textToAudio = (text, ttsSettings) => {
  return async () => {
    const data = await Market.textToAudio({ text, ttsSettings });
    return data;
  };
};

export const trainVoice = (params) => {
  return async () => {
    const data = await ChatBot.trainVoice(params);
    return data;
  };
};

export const addVoice = (params) => {
  return async () => {
    const data = await ChatBot.addVoice(params);
    return data;
  };
};

export const fetchList = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().marketVoiceTtsV2;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const { items, total } = await VoiceTTS.fetchVoiceTest(searchParams);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const getVoiceTest = (id) => {
  return async () => {
    const data = await VoiceTTS.getVoiceTest(id);
    return data;
  };
};

export const updateVoice = (params) => {
  return async () => {
    const data = await VoiceTTS.updateVoice(params);
    return data;
  };
};

export const deleteVoice = (id) => {
  return async (dispatch) => {
    await VoiceTTS.deleteVoiceTest(id);
    dispatch(fetchList());
  };
};

export const addVoiceTest = (params) => {
  return async () => {
    const data = await VoiceTTS.addVoiceTest(params);
    return data;
  };
};

export const asrVoiceTest = (id) => {
  return async () => {
    const data = await VoiceTTS.asrVoiceTest(id);
    return data;
  };
};

export const ttsVoiceTest = (id) => {
  return async () => {
    const data = await VoiceTTS.ttsVoiceTest(id);
    return data;
  };
};

export const ttsVoiceTestSentence = (params) => {
  return async () => {
    const data = await VoiceTTS.ttsVoiceTestSentence(params);
    return data;
  };
};

const _getInitState = () => {
  return {
    tmpSpeakers: [],
    sentences: [],
    article: '',
    articleSpeaker: null,
    articleAudioUrl: '',
    audioSpeed: 1,
    audioGenerating: false,
    list: [],
    total: 0,
    groupedSpeakers: {},
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
  };
};


export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
