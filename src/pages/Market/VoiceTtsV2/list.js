import { SaveFilled } from '@ant-design/icons';
import { PaginationTable, Toast } from '~/components';
import { Button, Divider, Input, Modal, Popconfirm } from 'antd';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.marketVoiceTtsV2;
  },
  actions,
)
export default class VoiceTTSList extends Component {
  static propTypes = {
    list: PropTypes.array.isRequired,
    total: PropTypes.number.isRequired,
    pagination: PropTypes.object.isRequired,
    fetchList: PropTypes.func.isRequired,
    updateVoice: PropTypes.func.isRequired,
    deleteVoice: PropTypes.func.isRequired,
    addVoiceTest: PropTypes.func.isRequired,
  }

  state = {
    title: '',
    open: false,
  }

  componentDidMount = async () => {
    await this.props.fetchList();
  }

  onShowModal = () => {
    this.setState({ open: true, title: '' });
  }

  onPaginationChange = async (params) => {
    await this.props.fetchList(params);
  }

  onSubmit = async () => {
    if (!this.state.title) {
      Toast.show('请输入名称', Toast.Type.WARNING);
      return;
    }

    const data = await this.props.addVoiceTest({ title: this.state.title });
    this.$push(`/market-voice-tts/edit/${data.id}`);
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      {
        title: '名称',
        dataIndex: 'title',
        key: 'title',
        align: 'center',
        render: (txt, row) => {
          return (
            <Input.Search
              size="small"
              key={row.id}
              style={{ width: 200 }}
              defaultValue={txt}
              onSearch={(value) => { return this.props.updateVoice({ id: row.id, title: value }); }}
              enterButton={<SaveFilled />}
            />
          );
        },

      },
      { title: 'ASR状态', dataIndex: 'asrStatus', key: 'asrStatus', align: 'center' },
      { title: 'TTS状态', dataIndex: 'ttsStatus', key: 'ttsStatus', align: 'center' },
      {
        title: '原音频',
        dataIndex: 'audioUrl',
        key: 'audioUrl',
        align: 'center',
        render: (txt) => {
          return <a href={txt} target="_blank" rel="noreferrer">查看</a>;
        },
      },
      {
        title: 'TTS地址',
        dataIndex: 'ttsUrl',
        key: 'ttsUrl',
        align: 'center',
        render: (txt) => {
          return <a href={txt} target="_blank" rel="noreferrer">查看</a>;
        },
      },
      {
        title: '音频时长',
        dataIndex: 'duration',
        key: 'duration',
        align: 'center',
        render: (txt) => {
          return `${txt / 1000}s`;
        },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        render: (txt, row) => {
          return (
            <div>
              <a onClick={() => { return this.$push(`/market-voice-tts/edit/${row.id}`); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm title="确定删除吗?" onConfirm={() => { return this.props.deleteVoice(row.id); }}>
                <a>删除</a>
              </Popconfirm>
            </div>
          );
        },
      },

    ];
  }

  render = () => {
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <div style={{ float: 'right' }}>
          <Button onClick={() => { return this.props.fetchList(); }}>刷新</Button>
          <Divider type="vertical" />
          <Button type="primary" onClick={() => { return this.onShowModal(); }}>
            新增
          </Button>
        </div>
        <PaginationTable
          dataSource={this.props.list}
          totalDataCount={this.props.total}
          pagination={this.props.pagination}
          columns={this.renderColumns()}
          onPaginationChange={this.onPaginationChange}
        />

        {
          this.state.open &&
          <Modal
            title="请输入名称"
            open={this.state.open}
            onOk={this.onSubmit}
            onCancel={() => { return this.setState({ open: false, title: '' }); }}
          >
            <Input value={this.state.title} onChange={(e) => { return this.setState({ title: e.target.value }); }} />
          </Modal>
        }
      </div>
    );
  }
}
export {
  reducer,
};
