import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Card, Form, Input, Modal, Select, Upload } from 'antd';
import PropTypes from 'prop-types';
import React, { Component } from 'react';

const trainTab = 'train';
const addTab = 'add';
const tabs = {
  [trainTab]: '训练',
  [addTab]: '添加',
};
const tabList = Object.keys(tabs).map((key) => {
  return {
    key,
    tab: tabs[key],
  };
});

export default class AddVoiceButton extends Component {
  static propTypes = {
    onTrain: PropTypes.func.isRequired,
    onAdd: PropTypes.func.isRequired,
  };

  state = {
    provider: '',
    voiceId: '',
    name: '',
    open: false,
    file: null,
    activeTabKey: trainTab,
    sampleText: '',
    voiceText: '',
    sampleAudioUrl: '',
  };

  onClose = () => {
    this.setState({ open: false, provider: '', voiceId: '', name: '', file: null, activeTabKey: trainTab, sampleText: '', voiceText: '', sampleAudioUrl: '' });
  }

  onOk = () => {
    const { activeTabKey } = this.state;
    if (activeTabKey === trainTab) {
      this.onTrain();
    } else {
      this.onAdd();
    }
  }

  onTrain = () => {
    const { file, provider, voiceId, name, sampleText, voiceText, sampleAudioUrl } = this.state;
    const isPreview = provider.indexOf('stepfun-api') > -1 && !sampleAudioUrl;
    this.props.onTrain(file, provider, voiceId, name, sampleText, voiceText, isPreview);
    this.onClose();
  }

  onAdd = () => {
    const { provider, voiceId, name, sampleText, voiceText } = this.state;
    this.props.onAdd(provider, voiceId, name, sampleText, voiceText);
    this.onClose();
  }

  renderUploadModal = () => {
    const { provider, voiceId, name, open, file, activeTabKey, sampleAudioUrl, sampleText, voiceText } = this.state;
    return (
      <Modal
        width={800}
        open={open}
        onCancel={this.onClose}
        onOk={this.onOk}
        okButtonProps={{
          disabled: provider.indexOf('stepfun-api') > -1 && !sampleAudioUrl && activeTabKey === 'train',
        }}
        footer={[
          <Button key="cancel" onClick={this.onClose}>取消</Button>,
          provider.indexOf('stepfun-api') > -1 && activeTabKey === 'train' && (
            <Button
              key="preview"
              type="primary"
              onClick={async () => {
              const data = await this.props.onTrain(file, provider, voiceId, name, sampleText, voiceText, true);
              this.setState({ sampleAudioUrl: data.voiceId });
            }}
            >
              试听
            </Button>
          ),
          <Button
            key="submit"
            type="primary"
            onClick={this.onOk}
            disabled={provider.indexOf('stepfun-api') > -1 && !sampleAudioUrl && activeTabKey === 'train'}
          >
            确定
          </Button>,
        ]}
      >
        <Card
          style={{ width: '100%' }}
          tabList={tabList}
          activeTabKey={activeTabKey}
          onTabChange={(key) => {
            this.setState({ activeTabKey: key });
          }}
          bordered={false}
        >
          <Form>
            <Form.Item label="提供商">
              <Select value={provider} onChange={(value) => { return this.setState({ provider: value }); }}>
                <Select.Option value="bytedance-re">火山</Select.Option>
                <Select.Option value="fish-audio">鱼音</Select.Option>
                <Select.Option value="stepfun-api">StepFun API</Select.Option>
                <Select.Option value="stepfun-api-vivid">StepFun API V2</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item label="名称">
              <Input value={name} onChange={(e) => { return this.setState({ name: e.target.value }); }} />
            </Form.Item>
            {
              (provider === 'bytedance-re' || activeTabKey === addTab) && (
                <Form.Item label="语音ID">
                  <Input value={voiceId} onChange={(e) => { return this.setState({ voiceId: e.target.value }); }} />
                </Form.Item>
              )
            }
            {
              provider.startsWith('stepfun') && (
                <Form.Item label="音频文本">
                  <Input.TextArea
                    rows={4}
                    value={voiceText}
                    onChange={(e) => { return this.setState({ voiceText: e.target.value }); }}
                    placeholder="请输入音频文本"
                  />
                </Form.Item>
              )
            }
            {
              activeTabKey === trainTab && (
                <Form.Item
                  label="文件"
                  extra={provider.indexOf('stepfun-api') > -1 ? '音频时长需要在5-9秒之间' : undefined}
                >
                  <Upload
                    fileList={file ? [{ uid: '1', name: file.name, status: 'done', url: '' }] : []}
                    beforeUpload={(f) => { this.setState({ file: f }); return false; }}
                    accept="audio/*"
                  >
                    <Button icon={<UploadOutlined />}>上传音频</Button>
                  </Upload>
                </Form.Item>
              )
            }

            {
              provider.indexOf('stepfun-api') > -1 && (
                <Form.Item label="试听音频文本">
                  <Input.TextArea
                    rows={4}
                    maxLength={50}
                    showCount
                    value={sampleText}
                    onChange={(e) => { return this.setState({ sampleText: e.target.value }); }}
                    placeholder="请输入试听音频文本（最多50字）"
                  />
                  {sampleAudioUrl && activeTabKey === 'train' && (
                    <audio
                      controls
                      style={{ marginTop: 8, width: '300px' }}
                      src={sampleAudioUrl}
                    />
                  )}
                </Form.Item>
              )
            }
          </Form>
        </Card>

      </Modal>
    );
  }

  render() {
    return (
      <>
        <Button
          shape="circle"
          icon={<PlusOutlined />}
          size="small"
          style={{ marginLeft: 10 }}
          onClick={() => { this.setState({ open: true }); }}
        />
        {this.state.open && this.renderUploadModal()}
      </>
    );
  }
}
