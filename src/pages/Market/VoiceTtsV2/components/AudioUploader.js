/* eslint-disable react/prop-types */
import { UploadOutlined } from '@ant-design/icons';
import { Button, Select, Tooltip, Upload } from 'antd';
import PropTypes from 'prop-types';
import React, { Component } from 'react';

export default class AudioUploader extends Component {
  static propTypes = {
    onUpload: PropTypes.func.isRequired,
  };

  state = {
    asrProvider: null,
  };

  handleUpload = (file) => {
    const { asrProvider } = this.state;
    const { onUpload } = this.props;
    if (asrProvider) {
      onUpload(file, asrProvider);
    }
    return false; // 阻止默认上传行为，用于手动处理上传
  };

  setAsrProvider = (value) => {
    this.setState({ asrProvider: value });
  };

  render() {
    const { asrProvider } = this.state;

    return (
      <div>
        <Select
          style={{ width: 200 }}
          placeholder="选择ASR服务"
          onChange={this.setAsrProvider}
        >
          <Select.Option value="dash-scope">灵积</Select.Option>
          <Select.Option value="aliyun">阿里云</Select.Option>
          <Select.Option value="bytedance">火山</Select.Option>
        </Select>
        <Tooltip title="上传音频将覆盖当前文本，且只能逐句修改">
          <Upload
            beforeUpload={this.handleUpload}
            showUploadList={false}
            disabled={asrProvider == null}
            accept="audio/*"
          >
            <Button disabled={asrProvider == null} icon={<UploadOutlined />}>上传音频</Button>
          </Upload>
        </Tooltip>
      </div>
    );
  }
}
