/* eslint-disable react/prop-types */
import { <PERSON><PERSON>, Divider, Input, InputNumber, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';

export default class ListItem extends Component {
  static propTypes = {
    item: PropTypes.object.isRequired,
    speakers: PropTypes.array.isRequired,
    onSentenceChange: PropTypes.func.isRequired,
    onGenerateAudio: PropTypes.func.isRequired,
    onPlayEnd: PropTypes.func,
  };

  state = {
    selectedService: null,
    generating: false,
    groupedSpeakers: {},
  };

  constructor(props) {
    super(props);

    this.audioRef = null;
  }

  componentDidMount = () => {
    const { item, speakers } = this.props;
    const groupedSpeakers = _.groupBy(speakers, 'ttsSettings.provider');
    if (!_.isUndefined(item.ttsSettings)) {
      const selectedService = speakers.find((x) => {
        return x.ttsSettings.provider === item.ttsSettings.provider && x.ttsSettings.voice === item.ttsSettings.voice;
      });
      this.setState({ selectedService });
    }

    this.setState({ groupedSpeakers });
  }

  handleServiceChange = (value) => {
    this.setState({ selectedService: value });
  };

  handleGenerateAudio = async () => {
    this.setState({ generating: true });
    const { speakers, onGenerateAudio } = this.props;
    const selected = speakers.find((option) => { return option.value === this.state.selectedService; });
    await onGenerateAudio(selected);
    this.setState({ generating: false });
  };

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.props.onSentenceChange({ ...this.props.item, [key]: value });
  }

  render() {
    const { generating, selectedService } = this.state;
    const { item } = this.props;

    return (
      <>
        <div style={{ display: 'flex', marginBottom: 10 }}>
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              marginBottom: 20,
              width: '100%',
            }}
          >
            <div style={{ display: 'flex', justifyContent: 'space-evenly', marginBottom: 10 }}>
              <Input value={item.text} onChange={(e) => { return this.onChangeValue(e, 'text'); }} />
              <Select
                style={{ width: 250, marginLeft: 10 }}
                placeholder="选择语音"
                value={selectedService}
                onChange={this.handleServiceChange}
              >
                {
                  _.map(this.state.groupedSpeakers, (group, key) => {
                    return (
                      <Select.OptGroup key={key} label={key}>
                        {
                          group.map((option) => {
                            return (
                              <Select.Option key={option.value} value={option.value}>
                                {option.label}
                              </Select.Option>
                            );
                          })
                        }
                      </Select.OptGroup>
                    );
                  })
                }
              </Select>
              <Button
                type="primary"
                loading={generating}
                onClick={this.handleGenerateAudio}
                disabled={!selectedService}
                style={{ marginLeft: 10 }}
              >
                生成音频
              </Button>
            </div>
            <div style={{ display: 'flex' }}>
              <InputNumber
                value={item.breakTime || 0}
                size="small"
                step={0.5}
                min={0}
                defaultValue={0}
                precision={1}
                onChange={(e) => { return this.onChangeValue(e, 'breakTime'); }}
                addonBefore="停顿"
                addonAfter="秒"
              />
              <Divider type="vertical" />
              <InputNumber
                value={item.speed || 1}
                size="small"
                step={0.1}
                min={0.8}
                max={2}
                defaultValue={1}
                precision={1}
                onChange={(e) => { return this.onChangeValue(e, 'speed'); }}
                addonBefore="语速"
              />
            </div>
          </div>
        </div>
        {
          item.audioUrl &&
          <audio
            controls
            ref={(ref) => { this.audioRef = ref; }}
            src={item.audioUrl}
            style={{ marginLeft: 10 }}
            onEnded={() => { return this.props.onPlayEnd(); }}
          />
        }
      </>
    );
  }
}
