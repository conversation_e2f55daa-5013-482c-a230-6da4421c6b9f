/* eslint-disable react/prop-types */
import { List } from 'antd';
import PropTypes from 'prop-types';
import React, { Component } from 'react';

import ListItem from './ListItem';

export default class ASRList extends Component {
  static propTypes = {
    items: PropTypes.array.isRequired,
    speakers: PropTypes.array.isRequired,
    onSentencesChange: PropTypes.func.isRequired,
    onGenerateAudio: PropTypes.func.isRequired,
  };

  handlePlayEnd = (index) => {
    const { items } = this.props;
    if (items[index + 1] && this[`ref_${index + 1}`].audioRef) {
      this[`ref_${index + 1}`].audioRef.play();
    }
  }

  render() {
    const { items, speakers, onSentencesChange, onGenerateAudio } = this.props;
    return (
      <List
        dataSource={items}
        renderItem={(item, index) => {
          return (
            <ListItem
              ref={(ref) => { this[`ref_${index}`] = ref; }}
              key={item.id}
              item={{ ...item, audioUrl: item.ttsUrl }}
              speakers={speakers}
              onSentenceChange={(text) => { return onSentencesChange(index, text); }}
              onGenerateAudio={(service) => { return onGenerateAudio(index, service); }}
              onPlayEnd={() => { return this.handlePlayEnd(index); }}
            />
          );
        }}
      />
    );
  }
}
