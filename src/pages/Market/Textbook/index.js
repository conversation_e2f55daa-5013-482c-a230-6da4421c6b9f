import 'react-quill/dist/quill.snow.css';

import './index.less';

import { Toast } from '~/components';
import { Button } from 'antd';
import React, { Component } from 'react';
import ReactQuill from 'react-quill';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

const { Quill } = ReactQuill;
const Inline = Quill.import('blots/inline');

class InsertBlot extends Inline {
  static create(value) {
    const node = super.create();
    node.setAttribute('insert', value || 'true');
    return node;
  }

  static formats(node) {
    return node.getAttribute('insert');
  }
}
InsertBlot.blotName = 'insert';
InsertBlot.tagName = 'span';
Quill.register(InsertBlot);


@connect(
  (state) => {
    return state.marketTextbook;
  },
  actions,
)
export default class Textbook extends Component {
  static propTypes = {
  }

  state = { // eslint-disable-next-line
    content: '<h1>王昌龄 《出塞》</h1><p><br></p><h2>一、背景知识</h2><p><br></p><h3>1. 大唐盛世，边塞建功&nbsp;<span style="color: rgb(230, 0, 0);" insert="true">阴山</span> <span style="color: rgb(230, 0, 0);" insert="true">阳关</span> <span style="color: rgb(230, 0, 0);" insert="true">玉门关</span></h3><p><br></p><h3>2. “<span style="color: rgb(230, 0, 0);">七绝圣手</span>”<span style="background-color: rgb(255, 153, 0);">王昌龄</span> <span style="color: rgb(230, 0, 0);" insert="true">五绝</span></h3><p><br></p><p><br></p><h2>二、文本精析</h2><p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;出塞</p><h3>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;[唐]王昌龄</h3><h3>秦时明月汉时关，</h3><p><span style="color: rgb(230, 0, 0);" insert="true">秦汉时期的明月和关口都还在</span></p><h3>万里长征人未还。</h3><p><span style="color: rgb(230, 0, 0);" insert="true">征战万里的战士没有回来。</span></p><h3>但使龙城飞将在，</h3><p><span style="color: rgb(230, 0, 0);" insert="true">只要像卫青、李广一样守卫边疆的优秀将领在</span></p><h3>不教胡马度阴山。</h3><p><span style="color: rgb(230, 0, 0);" insert="true">不让胡人的军队度过阴山。</span></p><p><br></p><p><br></p><p><br></p><h2>三、重要知识点</h2><p><br></p><h3>互文: 上下两句或一句话中的两个部分，看似各说两件事，实则是互相呼应，互相阐发，互相补充，说的是一件事。</h3><p><br></p><h3>龙城飞将:像卫青、李广那样守卫边关的优秀将士。</h3><p><br></p><p><br></p><h2>四、后人评价</h2><p>明朝四大才子之首<span style="color: rgb(230, 0, 0);">杨慎</span>称赞: <span style="background-color: rgb(230, 0, 0); color: rgb(230, 0, 0);" insert="true">此诗可入神品</span></p>',
  }

  componentDidMount() {
  }

  componentDidUpdate() {
  }

  mergeNestedSpans = (html) => {
    return html.replace(
      /<span style="([^"]*)">\s*<span insert="([^"]*)">(.*?)<\/span><\/span>/g,
      '<span style="$1" insert="$2">$3</span>',
    );
  }

  handleChange = (value) => {
    this.setState({ content: value });
  }

  onSave = async () => {
    const editor = this.quillRef.getEditor();
    const html = this.mergeNestedSpans(editor.root.innerHTML);
    this.outputHtml = html;
    // this.inputRef.resizableTextArea.textArea.value = html;
    await navigator.clipboard.writeText(html);

    Toast.show('已复制到剪贴板', Toast.Type.SUCCESS);
  }

  render = () => {
    const modules = {
      toolbar: {
        container: [
          ['bold', 'italic', 'underline', 'strike'], // toggled buttons
          ['formula'],

          [{ header: 1 }, { header: 2 }], // custom button values
          [{ list: 'ordered' }, { list: 'bullet' }, { list: 'check' }],
          [{ script: 'sub' }, { script: 'super' }], // superscript/subscript
          [{ indent: '-1' }, { indent: '+1' }], // outdent/indent
          [{ direction: 'rtl' }], // text direction

          [{ header: [1, 2, 3, 4, 5, false] }],

          [{ color: [] }, { background: [] }], // dropdown with defaults from theme
          [{ align: [] }],

          ['clean'],
          ['insert'],
        ],
        handlers: {
          insert: () => {
            const quill = this.quillRef.getEditor();
            const range = quill.getSelection();
            if (range) {
              quill.formatText(range.index, range.length, 'insert', 'true');
            }
          },
        },
      },
    };

    return (
      <div className="textbook-container" style={{ padding: 30, background: '#fff' }}>
        <ReactQuill
          defaultValue={`${this.state.content}`}
          ref={(el) => { this.quillRef = el; }}
          theme="snow"
          modules={modules}
        />
        <div style={{ marginTop: 20 }}>
          <Button onClick={this.onSave}>复制</Button>
        </div>

        {/* <Input.TextArea
          disabled
          ref={(el) => { this.inputRef = el; }}
          style={{ margin: '20px 0' }}
        /> */}

        {/* <Button
          onClick={() => { navigator.clipboard.writeText(this.outputHtml); }}
          style={{ marginBottom: 20 }}
        >
          复制
        </Button> */}
      </div>
    );
  }
}

export {
  reducer,
};
