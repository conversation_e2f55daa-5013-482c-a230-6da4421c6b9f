
const SET_STATE = 'DOUYIN_PRIVATE/SET_STATE';
const CLEAR_STATE = 'DOUYIN_PRIVATE/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

const _getInitState = () => {
  return {
    list: [
      { id: '1', name: '抖音1', status: 'active', conversation: '1' },
      { id: '2', name: '抖音2', status: 'active', conversation: '2' },
    ],
    total: 2,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    conversations: [
      {
        id: '1',
        nickname: 'bzy1',
        messageCount: 2,
        promptTokens: 3,
        completionTokens: 4,
        createdAt: '2020-01-01',
        lastMessageAt: '2020-01-02',
      },
      {
        id: '2',
        nickname: 'bzy2',
        messageCount: 2,
        promptTokens: 3,
        completionTokens: 4,
        createdAt: '2020-01-01',
        lastMessageAt: '2020-01-02',
      },
    ],
    conversationTotal: 2,
    conversationPagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
