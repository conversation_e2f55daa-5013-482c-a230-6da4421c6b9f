import { FilterBar, PaginationTable } from '~/components';
import { But<PERSON>, Divider, Drawer, Form, Input, Popconfirm, Select, Tabs } from 'antd';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

const SCENE_MAP = {
  im_replay_msg: '回复私信消息',
  im_enter_direct_msg: '用户主动进入私信会话页',
  im_b2b_direct_msg: 'B2B场景私信触达',
  // im_authorize_message: '主动私信持续触达',
};

@connect(
  (state) => {
    return state.marketDouyinPrivate;
  },
  actions,
)
export default class DouyinPrivate extends Component {
  static propTypes = {
    accounts: PropTypes.array,
    list: PropTypes.array,
    total: PropTypes.number,
    conversations: PropTypes.array,
    conversationsTotal: PropTypes.number,
    conversationsPagination: PropTypes.object,
    pagination: PropTypes.object,
    clearState: PropTypes.func.isRequired,
    location: PropTypes.object,
  }

  state = {
    activeKey: 'channel',
    openDetail: false,
    detail: {},
  }

  componentDidMount = async () => {
    const { activeKey } = this.props.location.query;
    if (activeKey) {
      this.setState({ activeKey });
    }
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  renderSelects = () => {
    return (
      <Select
        allowClear
        style={{ width: 160, marginBottom: 16 }}
        placeholder="请选择账号"
        onChange={() => { }}
      >
        {
          this.props.accounts.map((x) => {
            return (<Select.Option value={x.id}>{x.name}({x.phone})</Select.Option>);
          })
        }
      </Select>
    );
  }

  renderDetailDrawer = () => {
    const { openDetail, detail } = this.state;
    return (
      <Drawer
        title="私信回复"
        placement="right"
        width="50vw"
        visible={openDetail}
        onClose={() => { return this.setState({ openDetail: false }); }}
        extra={
          <Button type="primary" onClick={() => { return this.setState({ openDetail: false }); }}>
            保存
          </Button>
        }
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} className="common-form" >
          <Form.Item label="场景1">
            <Select >
              {
                Object.keys(SCENE_MAP).map((x) => {
                  return (<Select.Option value={x}>{SCENE_MAP[x]}</Select.Option>);
                })
              }
            </Select>
          </Form.Item>

          <Form.Item label="欢迎语">
            <Input.TextArea autoSize={{ minRows: 3 }} value={detail.welcome} />
          </Form.Item>
          <Form.Item label="场景2">
            <Select >
              {
                Object.keys(SCENE_MAP).map((x) => {
                  return (<Select.Option value={x}>{SCENE_MAP[x]}</Select.Option>);
                })
              }
            </Select>
          </Form.Item>

          <Form.Item label="欢迎语">
            <Input.TextArea autoSize={{ minRows: 3 }} value={detail.welcome} />
          </Form.Item>
          <Form.Item label="场景3">
            <Select >
              {
                Object.keys(SCENE_MAP).map((x) => {
                  return (<Select.Option value={x}>{SCENE_MAP[x]}</Select.Option>);
                })
              }
            </Select>
          </Form.Item>

          <Form.Item label="欢迎语">
            <Input.TextArea autoSize={{ minRows: 3 }} value={detail.welcome} />
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '账号名称', dataIndex: 'name', key: 'name', align: 'center' },
      { title: '状态', dataIndex: 'status', key: 'status', align: 'center' },
      {
        title: '会话',
        dataIndex: 'conversation',
        key: 'conversation',
        align: 'center',
        render: (t, row) => {
          return (
            <Button type="link" onClick={() => { return this.onOpenConversation(row); }}>
              查看
            </Button>
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        render: (x, row) => {
          return (
            <div>
              <a onClick={() => { return this.setState({ openDetail: true, detail: row }); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm title="确定删除吗？" onConfirm={() => { }}>
                <a>删除</a>
              </Popconfirm>
            </div>
          );
        },
      },
    ];
  }


  renderConversationColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '用户昵称', dataIndex: 'nickname', key: 'nickname', align: 'center' },
      { title: '消息数', dataIndex: 'messageCount', key: 'messageCount', align: 'center', sorter: true },
      { title: '输入Token', dataIndex: 'promptTokens', key: 'promptTokens', align: 'center', sorter: true },
      { title: '输出Token', dataIndex: 'completionTokens', key: 'completionTokens', align: 'center', sorter: true },
      { title: '会话开始时间', dataIndex: 'createdAt', key: 'createdAt', align: 'center' },
      { title: '最后消息时间', dataIndex: 'lastMessageAt', key: 'lastMessageAt', align: 'center' },
      {
        title: '会话记录',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        render: () => {
          return (
            <a onClick={() => { }}>
              查看
            </a>
          );
        },
      },
    ];
  }

  render = () => {
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <Tabs
          activeKey={this.state.activeKey}
          onChange={(activeKey) => {
            this.$replace('/market-douyin-private', { activeKey });
            return this.setState({ activeKey });
          }}
        >
          <Tabs.TabPane tab="账号" key="channel">
            <FilterBar
              canAdd
              addText="新增账号"
              onAdd={() => { return this.setState({ openDetail: true, detail: {} }); }}
            />

            <PaginationTable
              dataSource={this.props.list}
              totalDataCount={this.props.total}
              pagination={this.props.pagination}
              columns={this.renderColumns()}
              onPaginationChange={() => { }}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="会话" key="conversation">
            <PaginationTable
              dataSource={this.props.conversations}
              totalDataCount={this.props.conversationsTotal}
              pagination={this.props.conversationsPagination}
              columns={this.renderConversationColumns()}
              onPaginationChange={() => { }}

            />
          </Tabs.TabPane>
        </Tabs>


        {this.state.openDetail && this.renderDetailDrawer()}
      </div>
    );
  }
}

export {
  reducer,
};
