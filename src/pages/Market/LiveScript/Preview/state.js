import { Market } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'MARKET_LIVE_SCRIPT_PREVIEW/SET_STATE';
const CLEAR_STATE = 'MARKET_LIVE_SCRIPT_PREVIEW/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchLiveHelperOutlines = (params) => {
  return async () => {
    const result = await Market.fetchLiveHelperOutlines(params);
    return result;
  };
};

export const updateLiveHelper = (params) => {
  return async () => {
    await Market.updateLiveHelper(params);
  };
};

export const detectLiveScript = (params) => {
  return async () => {
    return Market.detectLiveScript(params);
  };
};

export const textToAudio = (params) => {
  return async () => {
    return Market.textToAudio(params);
  };
};

export const fetchVoiceCloneSpeakers = () => {
  return async (dispatch) => {
    const result = await Market.fetchVoiceCloneSpeakers();
    dispatch(setState({ speakers: _.values(result) }));
  };
};

const _getInitState = () => {
  return {
    speakers: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
