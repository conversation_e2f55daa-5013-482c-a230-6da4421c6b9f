/* eslint-disable react/no-array-index-key */
import './index.less';

import { Toast, AudioPlayer } from '~/components';
import Engine, { AliyunHelper } from '~/engine';
import { Button, Descriptions, Divider, Drawer, Image, Input, Modal, Tree, Upload, Select, Form } from 'antd';
import _ from 'lodash';
import OBSWebSocket from 'obs-websocket-js';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import Recorder from 'recorder-core';
import WebSocketConnectMethod from '~/engine/WebSocketConnectMethod';
import TTSSettings from '~/pages/Market/CourseMaterials/components/TTSSetting';
import { v1 as uuid } from 'uuid';

import reducer, * as actions from './state';

const MEDIA_SCENE = '多媒体展示';
const obs = new OBSWebSocket();
let rec;
let sampleBuf = new Int16Array();
let wsconnecter;
let recText = '';
const waitingScripts = [];

@connect(
  (state) => {
    return state.marketLiveScriptPreview;
  },
  actions,
)
export default class MarketLiveScriptPreview extends Component {
  static propTypes = {
    updateLiveHelper: PropTypes.func.isRequired,
    fetchLiveHelperOutlines: PropTypes.func.isRequired,
    match: PropTypes.object.isRequired,
    clearState: PropTypes.func.isRequired,
    detectLiveScript: PropTypes.func.isRequired,
    fetchVoiceCloneSpeakers: PropTypes.func.isRequired,
    speakers: PropTypes.func.isRequired,
    textToAudio: PropTypes.func.isRequired,
  }

  state = {
    audioUrl: '',
    selectedPart: '',
    datas: [],
    outlines: [],
    scripts: [],
    parts: [],
    selectIdx: 0,
    nextIdx: 0,
    isEdit: false,
    isRecording: false,
    isDialogueMode: false,
    isCreateAudioVisible: false,
    preview: {},
    audioStyle: {},
    obsData: {
      checked: false,
      init: false,
    },
    ttsConfig: {
      ttsSettings: {},
    },
    selectedScript: {},
  }

  componentDidMount = async () => {
    this.initObs();
    this.props.fetchVoiceCloneSpeakers();
    const { id, edit } = this.props.match.params;
    const { outlines: datas } = await this.props.fetchLiveHelperOutlines({ id });
    const outlines = _.map(datas, 'title');
    const parts = [];
    // const scripts = this.collectScripts(datas);
    datas.forEach(({ part }) => {
      if (part && parts.indexOf(part) === -1) {
        parts.push(part);
      }
    });
    const scripts = this.collectScripts(datas);
    let isDialogueMode = false;
    scripts.forEach((item) => {
      if (item.role === 'other') {
        isDialogueMode = true;
      }
    });
    this.setState({ datas, outlines, scripts, isEdit: !_.isUndefined(edit), isDialogueMode, parts });
    wsconnecter = new WebSocketConnectMethod({
      msgHandle: this.getJsonMessage,
      stateHandle: this.getConnState,
    });

    if (_.isUndefined(edit)) {
      this.initScroll();
    }
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  initScroll = () => {
    document.addEventListener('keydown', (event) => {
      let idx;
      switch (event.keyCode) {
        case 37: // 左键
        case 38: // 上键
          idx = _.max([this.state.selectIdx - 1, 0]);
          break;
        case 39: // 右键
        case 40: // 下键
          idx = _.min([this.state.selectIdx + 1, this.state.scripts.length - 1]);
          break;
        default:
          return;
      }

      this.setState({ selectIdx: idx }, () => {
        const { top } = this.rows[idx].getBoundingClientRect();
        if ((window.innerHeight / 2) > (top + 100)) return;
        this.rows[idx]?.scrollIntoView({ block: 'center' });
      });
    });

    setTimeout(() => {
      const trs = document.querySelector('.script-desc').querySelectorAll('tr');
      const rows = [];
      trs.forEach((x, i) => {
        if (i % 2 !== 0) {
          rows.push(x);
        }
      });

      this.rows = rows;
    }, 10);
  }

  initObs = () => {
    const obsws = Engine.getItem('OBS_HOST') || 'ws://127.0.0.1:4455';
    obs.connect(obsws).then(async () => {
      console.log('Connected to OBS WebSocket server.'); // eslint-disable-line no-console
      const obsData = { ...this.state.obsData, checked: true };
      const { scenes } = await obs.call('GetSceneList');
      const scene = scenes.find((x) => { return x.sceneName === MEDIA_SCENE; });
      if (scene?.sceneUuid) {
        const { sceneItems } = await obs.call('GetSceneItemList', { sceneName: MEDIA_SCENE });
        const media = sceneItems.find((x) => { return x.sourceName === '浏览器'; });
        if (media.inputKind === 'browser_source') {
          obsData.init = true;
        }
      }
      await this.setState({ obsData });

      if (!obsData.init) {
        this.initObsScene();
      }
    }).catch((err) => {
      console.error('Error connecting to OBS:', err); // eslint-disable-line no-console
    });
  }

  initObsScene = async () => {
    await obs.call('CreateScene', { sceneName: '多媒体展示' });
    await obs.call('CreateInput', {
      sceneName: '多媒体展示',
      inputKind: 'browser_source',
      inputName: '浏览器',
      unversionedInputKind: 'browser_source',
    });

    this.setState({ obsData: { ...this.state.obsData, init: true } });
    // await obs.call('SetCurrentProgramScene', { sceneName: '多媒体展示' });
    // await obs.call('SetInputSettings', {
    //   inputName: '浏览器',
    //   inputSettings: {
    //     url: 'https://video-clip.oss-cn-shanghai.aliyuncs.com/upload/d5523bb0-02e4-11ef-b26f-597cc7096214.jpg',
    //   },
    // });
  }

  collectScripts = (data) => {
    const result = [];

    function traverse(node) {
      const { outlineId, seq, part } = node;
      if (node.scripts && node.scripts.length > 0) {
        node.scripts.forEach((x, oIdx) => {
          result.push({ ...x, [x.type]: x.extraContent, outlineId, seq, oIdx, part });
        });
      }
      if (node.children && node.children.length > 0) {
        node.children.forEach((child) => { return traverse(child); });
      }
    }

    data.forEach((item) => { return traverse(item); });

    return result;
  }

  getJsonMessage = async (jsonMsg) => {
    const rectxt = JSON.parse(jsonMsg.data).text;
    recText += rectxt;
    const { scripts, selectIdx } = this.state;
    const currentSentence = scripts[selectIdx]?.content;
    const nextSentence = scripts[selectIdx + 1]?.content;

    const { switch: switchBool, newAccumulatedText } = await this.props.detectLiveScript({
      sentence: currentSentence,
      next_sentence: nextSentence,
      accumulated_text: recText,
      check_next: true,
    });
    if (switchBool) {
      this.setState({ selectIdx: selectIdx + 1 });
      const script = scripts[selectIdx + 1];
      if (script.audio) {
        this.onPlayAudio(script.audio, selectIdx + 1);
      }
    }
    recText = newAccumulatedText;
  }

  getConnState = (connState) => {
    if (connState === 2) {
      this.stop();
    }
  }

  startRecord = () => {
    this.start();
    // return;
    rec = Recorder({
      type: 'mp3',
      sampleRate: 16000,
      bitRate: 16,
      onProcess: (buffer, powerLevel, bufferDuration, bufferSampleRate) => {
        if (this.state.isRecording) {
          const data48k = buffer[buffer.length - 1];

          const array48k = new Array(data48k);
          const data16k = Recorder.SampleData(array48k, bufferSampleRate, 16000).data;

          sampleBuf = Int16Array.from([...sampleBuf, ...data16k]);
          const chunkSize = 960;
          while (sampleBuf.length >= chunkSize) {
            const sendBuf = sampleBuf.slice(0, chunkSize);
            sampleBuf = sampleBuf.slice(chunkSize, sampleBuf.length);
            wsconnecter.wsSend(sendBuf);
            // console.log(sendBuf);
          }
        }
      },
    });

    rec.open(() => {
      this.setState({ isRecording: true });
      rec.start();
    }, (msg, isUserNotAllow) => {
      // eslint-disable-next-line no-console
      console.log(`${isUserNotAllow ? 'UserNotAllow，' : ''}无法录音:${msg}`);
    });
  }

  stopRecord = () => {
    if (!rec) {
      this.setState({ isRecording: false });
      return;
    }
    rec.stop(() => {
      rec.close();
      rec = null;
      this.setState({ isRecording: false });
      this.stop();
    }, () => {
      rec.close();
      rec = null;
      this.setState({ isRecording: false });
      this.stop();
    });
  }

  start = () => {
    this.clear();
    const ret = wsconnecter.wsStart();
    if (ret === 1) {
      return 1;
    }
    return 0;
  }

  stop = () => {
    const chunkSize = [5, 8, 5];
    const request = {
      chunk_size: chunkSize,
      wav_name: 'h5',
      is_speaking: false,
      chunk_interval: 10,
      mode: '2pass',
    };
    if (sampleBuf.length > 0) {
      wsconnecter.wsSend(sampleBuf);
      sampleBuf = new Int16Array();
    }
    wsconnecter.wsSend(JSON.stringify(request));

    // isRec = false;
    setTimeout(() => {
      wsconnecter.wsStop();
    }, 3000);
  }

  clear = () => {
    recText = '';
  }

  openFullscreen = () => {
    const domSider = document.getElementsByClassName('bzy-sider')[0] || {};
    const domHeader = document.getElementsByClassName('global-header')[0] || {};
    if (this.state.isFullScreen) {
      (domSider.style || {}).display = 'unset';
      (domHeader.style || {}).display = 'unset';
      this.setState({ isFullScreen: false });
    } else {
      (domSider.style || {}).display = 'none';
      (domHeader.style || {}).display = 'none';
      this.setState({ isFullScreen: true });
    }
  }

  openWebFullscreen = () => {
    if (document.body.requestFullscreen) {
      document.body.requestFullscreen();
    } else if (document.body.mozRequestFullScreen) { /* Firefox */
      document.body.mozRequestFullScreen();
    } else if (document.body.webkitRequestFullscreen) { /* Chrome, Safari and Opera */
      document.body.webkitRequestFullscreen();
    } else if (document.body.msRequestFullscreen) { /* IE/Edge */
      document.body.msRequestFullscreen();
    }
  }

  closeWebFullscreen = () => {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.mozCancelFullScreen) { /* Firefox */
      document.mozCancelFullScreen();
    } else if (document.webkitExitFullscreen) { /* Chrome, Safari and Opera */
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) { /* IE/Edge */
      document.msExitFullscreen();
    }
  }

  getRowColor = (s) => {
    let color;
    if (s.type === 'comment') {
      color = '#0071C1';
    }
    if (s.type === 'interactive') {
      color = '#00B14D';
    }
    if (s.type === 'emphasize') {
      color = '#FE0300';
    }
    return color;
  }

  formatOutlines = (datas) => {
    datas.forEach((item) => {
      if (item.scripts) {
        item.scripts = item.scripts.map((script, idx) => { // eslint-disable-line no-param-reassign
          const s = this.state.scripts.find((x) => {
            return x.outlineId === item.outlineId && x.seq === item.seq && x.oIdx === idx;
          });
          return {
            content: s.content,
            type: s.type,
            medias: s.medias,
            extraContent: s.extraContent,
            role: s.role || '',
            audio: s.audio || '',
          };
        });
      }
      if (item.children) {
        this.formatOutlines(item.children);
      }
    });
    return datas;
  }

  onUpload = async (option, idx, key) => {
    try {
      const url = await AliyunHelper.clipsUploadImage(option.file, (progress) => {
        const percent = Math.round((progress.loaded / progress.total) * 100);
        option.onProgress({ percent });
      });
      option.onSuccess();
      const fileType = url.split('.').pop();
      let type;
      if (fileType === 'jpg' || fileType === 'jpeg' || fileType === 'png' || fileType === 'gif') {
        type = 'img';
      } else if (fileType === 'mp4' || fileType === 'mov' || fileType === 'avi') {
        type = 'video';
      } else {
        type = 'unknown';
      }

      this.onChangeValue([{ type, url }], idx, key);
    } catch (e) {
      option.onError();
    }
  }

  onSendToScreen = async (media) => {
    await obs.call('SetCurrentProgramScene', { sceneName: '多媒体展示' });
    await obs.call('SetInputSettings', { inputName: '浏览器', inputSettings: { url: media.url } });
    Toast.show('投屏成功!', Toast.Type.SUCCESS);
  }

  onChangeValue = (e, idx, type) => {
    const value = e?.target ? e.target.value : e;
    const scripts = _.cloneDeep(this.state.scripts);
    scripts[idx][type] = value;
    if (!['content', 'medias'].includes(type)) {
      scripts[idx].type = type;
      scripts[idx].extraContent = value;
    }
    this.setState({ scripts });
  }

  onCancel = () => {
    this.$push('/market-live-script');
  }

  onSubmit = async () => {
    const { id } = this.props.match.params;
    const outlines = this.formatOutlines(this.state.datas);
    await this.props.updateLiveHelper({ id, outlines });
    Toast.show('保存成功!', Toast.Type.SUCCESS);
  }

  onPlayAudio = async (audioUrl, index) => {
    if (audioUrl && this.audioControlRef) {
      await this.setState({ nextIdx: index + 1, audioUrl });
      this.audioControlRef.play();
    }
  }

  onTimeUpdate = (e) => {
    const { duration, currentTime } = e.target;
    const percent = currentTime / duration * 100;
    const audioStyle = {
      background: `linear-gradient(to right, #74ff9f ${percent}%, #fafafa ${percent}%)`,
    };
    this.setState({ audioStyle });
  }

  onEnded = async () => {
    const { nextIdx, isEdit, scripts } = this.state;
    if (isEdit) {
      let audio = '';
      let currentIndex = 0;
      scripts.forEach((script, index) => {
        if (index >= nextIdx && script.audio && !audio) {
          audio = script.audio;
          currentIndex = index;
        }
      });
      if (audio) {
        await this.setState({ selectIdx: currentIndex, nextIdx: currentIndex + 1, audioUrl: audio });
        this.audioControlRef.play();
      } else {
        this.setState({ nextIdx: 0, audioUrl: '', audioStyle: {} });
      }
      return;
    }
    if (nextIdx) {
      this.setState({ selectIdx: nextIdx, nextIdx: 0, audioUrl: '', audioStyle: {} });
    }
  }

  onCreateAudio = async () => {
    const { index, script, ttsSettings } = waitingScripts.shift();
    if (script?.content) {
      const { fileUrl } = await this.props.textToAudio({
        text: script.content,
        ttsSettings,
        uploadPath: `https://video-clip.oss-cn-shanghai.aliyuncs.com/upload/${uuid()}.mp3`,
      });
      const { scripts } = this.state;
      scripts[index].audio = fileUrl;
      scripts[index].isCreatingAudio = false;
      this.setState({ scripts });
      this.onCreateAudio();
    } else {
      Toast.show('全部完成!', Toast.Type.SUCCESS);
    }
  }

  renderOutlineDrawer = () => {
    return (
      <Drawer
        open={this.state.openOutline}
        width="20vw"
        onClose={() => { return this.setState({ openOutline: false }); }}
      >
        <Tree
          defaultExpandAll
          fieldNames={{ title: 'title', key: 'outlineId', children: 'children' }}
          treeData={this.state.datas}
          onSelect={(k, e) => {
            window.location.href = `#${e.node.key}`;
          }}
        />
      </Drawer>
    );
  }

  renderEditItem = (x, idx) => {
    const { audioUrl, audioStyle } = this.state;
    const color = this.getRowColor(x);
    const obj = {};
    if (!_.isEmpty(x.outline)) {
      obj.id = this.state.outlines.findIndex((o) => { return o === x.outline; });
    }

    const backgroundColor = this.state.selectIdx === idx ? '#FBCD72' : undefined;
    const backgroundStyle = audioUrl === x.audio ? audioStyle : {};
    backgroundStyle.padding = 12;
    backgroundStyle.color = color;
    backgroundStyle.display = 'flex';
    backgroundStyle.alignItems = 'center';
    return (
      <>
        <Descriptions.Item style={{ backgroundColor, width: '10%', fontSize: 14, textAlign: 'center' }}>
          {x.part ? `${x.part}-` : ''}{x.role === 'other' ? '【麦手】' : '【老师】'}
        </Descriptions.Item>
        <Descriptions.Item style={{ backgroundColor, width: '55%' }}>
          <div
            style={backgroundStyle}
            onClick={() => { return this.setState({ selectIdx: idx }); }}
          >
            <Input
              color
              value={x.content}
              onChange={(e) => { return this.onChangeValue(e, idx, 'content'); }}
              style={{ flexGrow: 1, flexShrink: 1, with: 1 }}
            />
            {
              // eslint-disable-next-line no-nested-ternary
              x.role === 'other' ? (
                <div style={{ flexGrow: 0, flexShrink: 0, with: '2jursan00px', fontSize: 14 }}>
                  {
                    x.isCreatingAudio ? (
                      <>
                        <Divider type="vertical" />
                        <span>音频生成中...</span>
                      </>
                    ) : (
                      <>
                        {
                            x.audio && (
                            <>
                              <Divider type="vertical" />
                              <span
                                onClick={() => {
                                  this.onPlayAudio(x.audio, idx);
                                }}
                              >
                                播放
                              </span>
                            </>
                          )
                        }
                        <Divider type="vertical" />
                        <Upload
                          accept="audio/*"
                          showUploadList={false}
                          customRequest={(opt) => { return this.onUpload(opt, idx, 'audio'); }}
                        >
                          上传音频
                        </Upload>
                        {
                          x.content && (
                            <>
                              <Divider type="vertical" />
                              <span
                                onClick={() => {
                                  this.setState({ selectedScript: { ...x, index: idx }, isCreateAudioVisible: true });
                                }}
                              >
                                重新生成
                              </span>
                            </>
                          )
                        }
                      </>
                    )
                  }
                </div>
              ) : ''
            }
          </div>
        </Descriptions.Item>
        <Descriptions.Item style={{ backgroundColor, width: '5%' }}>
          {
            _.isEmpty(x?.medias) ?
              <div style={{ textAlign: 'center' }}>
                <Upload
                  accept="image/*,video/*"
                  showUploadList={false}
                  customRequest={(opt) => { return this.onUpload(opt, idx, 'medias'); }}
                >
                  上传
                </Upload>
              </div>
              :
              <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '1rem', padding: '0px 10px' }}>
                <a
                  onClick={() => { return this.setState({ preview: { ..._.head(x.medias), visible: true } }); }}
                >
                  预览
                </a>
                <a onClick={() => { return this.onChangeValue([], idx, 'medias'); }}>删除</a>
              </div>
          }
        </Descriptions.Item>
        <Descriptions.Item style={{ backgroundColor, width: '15%' }}>
          <div
            style={{ padding: 12, color }}
            onClick={() => { return this.setState({ selectIdx: idx }); }}
          >
            <Input
              value={x.interactive}
              onChange={(e) => { return this.onChangeValue(e, idx, 'interactive'); }}
            />
          </div>
        </Descriptions.Item>
        <Descriptions.Item style={{ backgroundColor, width: '15%' }}>
          <div
            style={{ padding: 12, color }}
            onClick={() => { return this.setState({ selectIdx: idx }); }}
          >
            <Input
              value={x.comment}
              onChange={(e) => { return this.onChangeValue(e, idx, 'comment'); }}
            />
          </div>
        </Descriptions.Item>
      </>
    );
  }

  renderPreviewItem = (x, idx) => {
    const { isDialogueMode, audioStyle, audioUrl } = this.state;
    const color = this.getRowColor(x);
    const obj = {};
    if (!_.isEmpty(x.outline)) {
      obj.id = this.state.outlines.findIndex((o) => { return o === x.outline; });
    }
    const backgroundColor = this.state.selectIdx === idx ? '#FBCD72' : undefined;
    return (
      <>
        {/* <Descriptions.Item style={{ backgroundColor, width: '15%' }}>
            <div
              {...obj}
              style={{ padding: 12, color }}
              onClick={() => { return this.setState({ selectIdx: idx }); }}
            >{x.outline}
            </div>
          </Descriptions.Item> */}
        <Descriptions.Item style={{ backgroundColor, width: '65%' }}>
          {
            // eslint-disable-next-line no-nested-ternary
            isDialogueMode ? (
              x.role === 'kol' ? (
                <div
                  id={x.outlineId}
                  onClick={() => { return this.setState({ selectIdx: idx }); }}
                  className="kol-item"
                >
                  <span className="nickname">【老师】：</span>
                  <span className="content">{x.content}</span>
                </div>
              ) : (
                <div
                  id={x.outlineId}
                  style={audioUrl === x.audio ? audioStyle : {}}
                  onClick={() => { return this.onPlayAudio(x.audio, idx); }}
                  className="other-item"
                >
                  <span className="nickname">：【麦手】</span>
                  <span className="content">{x.content}</span>
                </div>
              )
            ) : (
              <div
                id={x.outlineId}
                style={{ padding: 12, color }}
                onClick={() => { return this.setState({ selectIdx: idx }); }}
              >{x.content}
              </div>
            )
          }
        </Descriptions.Item>
        <Descriptions.Item style={{ backgroundColor, width: '5%' }}>
          {
            _.isEmpty(x?.medias) ?
              <></> :
              <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '1rem' }}>
                <Button
                  type="link"
                  onClick={() => { return this.setState({ preview: { ..._.head(x.medias), visible: true } }); }}
                >
                  预览
                </Button>
                <Button
                  type="link"
                  disabled={!this.state.obsData.checked}
                  onClick={() => { return this.onSendToScreen(_.head(x.medias)); }}
                >投屏
                </Button>
              </div>
          }
        </Descriptions.Item>
        <Descriptions.Item style={{ backgroundColor, width: '15%' }}>
          <div
            style={{ padding: 12, color }}
            onClick={() => { return this.setState({ selectIdx: idx }); }}
          >{x.interactive}
          </div>
        </Descriptions.Item>
        <Descriptions.Item style={{ backgroundColor, width: '15%' }}>
          <div
            style={{ padding: 12, color }}
            onClick={() => { return this.setState({ selectIdx: idx }); }}
          >{x.comment}
          </div>
        </Descriptions.Item>
      </>
    );
  }

  renderTextToAudio = () => {
    const { isCreateAudioVisible, selectedPart, parts, ttsConfig, selectedScript } = this.state;
    return (
      <Modal
        title="批量生成音频"
        visible={isCreateAudioVisible}
        onCancel={() => { return this.setState({ isCreateAudioVisible: false, selectedScript: {} }); }}
        okText="应用"
        onOk={() => {
          const { scripts, ttsConfig: { ttsSettings } } = this.state;
          if (selectedScript?.content) {
            waitingScripts.push({
              index: selectedScript.index, script: selectedScript, ttsSettings,
            });
            scripts[selectedScript.index].isCreatingAudio = true;
            scripts[selectedScript.index].audio = '';

            this.setState({ scripts, isCreateAudioVisible: false, selectedScript: {} });
            this.onCreateAudio();
            return;
          }
          if (parts && parts.length && !selectedPart) {
            Toast.show('请选择剧本!', Toast.Type.WARNING);
            return;
          }
          scripts.forEach((item, index) => {
            if (item.part === selectedPart && item.role === 'other') {
              // eslint-disable-next-line no-param-reassign
              item.isCreatingAudio = true;
              // eslint-disable-next-line no-param-reassign
              item.audio = '';
              waitingScripts.push({
                index, script: item, ttsSettings,
              });
            }
          });
          this.setState({ scripts, isCreateAudioVisible: false, selectedScript: {} });
          if (waitingScripts && waitingScripts.length) {
            this.onCreateAudio();
          }
        }}
      >
        <Form labelCol={{ span: 4 }} className="meme-form">
          {
            parts && parts.length && !selectedScript?.content && (
              <Form.Item label="剧本">
                <Select
                  value={selectedPart}
                  style={{ width: '100%' }}
                  placeholder="请选择工作流分组"
                  onChange={(e) => { this.setState({ selectedPart: e }); }}
                >
                  {_.map(parts, (v) => { return <Select.Option value={v}>{v}</Select.Option>; })}
                </Select>
              </Form.Item>
            )
          }
          <TTSSettings
            course={ttsConfig}
            speakers={this.props.speakers}
            onChange={(e) => { this.setState({ ttsConfig: { ttsSettings: e } }); }}
          />
        </Form>
      </Modal>
    );
  }

  renderPreview = () => {
    const { preview } = this.state;
    return (
      <Modal
        title="预览"
        visible={preview.visible}
        footer={null}
        onCancel={() => { return this.setState({ preview: { visible: false } }); }}
      >
        {
          preview?.type === 'img' ?
            <Image src={preview.url} /> :
            <video src={preview.url} controls style={{ width: '100%' }} />
        }
      </Modal>
    );
  }

  render = () => {
    const { obsData, preview, isRecording, audioUrl, isEdit, isCreateAudioVisible, isDialogueMode } = this.state;
    return (
      <div className="market-live-script-preview">
        <AudioPlayer
          url={audioUrl}
          loop={false}
          onTimeUpdate={this.onTimeUpdate}
          onEnded={this.onEnded}
          audioControlRef={(el) => { this.audioControlRef = el; }}
        />
        <div style={{ position: 'fixed', bottom: 10, left: 10, padding: 10, background: '#fff' }}>
          OBS:
          <span style={{ color: obsData.checked ? 'green' : 'red' }}>
            {obsData.checked ? '已连接' : '未连接'}
            {!obsData.checked && <Button type="link" onClick={() => { return this.initObs(); }}>重连</Button>}
          </span>
          {
            (obsData.checked && !obsData.init) &&
            <>
              <Divider type="vertical" />
              <Button onClick={() => { return this.initObsScene(); }} size="small">初始化场景</Button>
            </>
          }
        </div>
        {
          !this.state.isEdit &&
          <div style={{ position: 'fixed', bottom: 10, right: 30, padding: 10, background: '#fff' }}>
            {
              isRecording ? (
                <Button onClick={() => { return this.stopRecord(); }}>
                  暂停录音
                </Button>
              ) : (
                <Button onClick={() => { return this.startRecord(); }}>
                  开始录音
                </Button>
              )
            }

            <Button
              style={{ float: 'right', marginLeft: 30 }}
              onClick={() => { return this.setState({ openOutline: true }); }}
            >
              大纲
            </Button>

            <Button
              style={{ float: 'right', marginLeft: 30 }}
              onClick={() => { return this.openFullscreen(); }}
            >
              {this.state.isFullScreen ? '退出' : ''}全屏
            </Button>
          </div>
        }

        <div className="script-wrap">
          <Descriptions bordered layout="vertical" column={isEdit ? 5 : 4}>
            {/* <Descriptions.Item style={{ width: '15%' }} label={<div style={{ padding: 12 }}>大纲</div>} /> */}
            {
              isEdit && (
                <Descriptions.Item
                  style={{ width: '10%' }}
                  label={<div style={{ textAlign: 'center', padding: 12 }}>角色</div>}
                />
              )
            }
            <Descriptions.Item
              style={{ width: isEdit ? '55%' : '65%' }}
              label={<div style={{ textAlign: 'center', padding: 12 }}>逐字稿</div>}
            />
            <Descriptions.Item
              style={{ width: '5%' }}
              label={<div style={{ textAlign: 'center', padding: 12 }}>多媒体</div>}
            />
            <Descriptions.Item
              style={{ width: '15%' }}
              label={<div style={{ textAlign: 'center', padding: 12 }}>互动</div>}
            />
            <Descriptions.Item
              style={{ width: '15%' }}
              label={<div style={{ textAlign: 'center', padding: 12 }}>评论</div>}
            />
          </Descriptions>
          <Descriptions
            className="script-desc"
            bordered
            layout="vertical"
            column={isEdit ? 5 : 4}
            style={{ marginBottom: 30 }}
          >
            {
              this.state.scripts.map((x, idx) => {
                return this.state.isEdit ? this.renderEditItem(x, idx) : this.renderPreviewItem(x, idx);
              })
            }
          </Descriptions>
        </div>

        {this.state.openOutline && this.renderOutlineDrawer()}
        {
          this.state.isEdit &&
          <div style={{ textAlign: 'end', position: 'fixed', right: 60, bottom: 30 }}>
            {
              isEdit && isDialogueMode && audioUrl && (
                <Button
                  style={{ marginRight: 30 }}
                  onClick={() => {
                    this.audioControlRef.pause();
                    return this.setState({ nextIdx: 0, audioUrl: '', audioStyle: {} });
                  }}
                >
                  暂停播放
                </Button>
              )
            }
            {
              isEdit && isDialogueMode && (
                <Button
                  style={{ marginRight: 30 }}
                  onClick={() => {
                    return this.setState({ isCreateAudioVisible: true });
                  }}
                >
                  批量生成【麦手】音频
                </Button>
              )
            }
            <Button style={{ marginRight: 30 }} onClick={() => { return this.onCancel(); }}>
              返回
            </Button>
            <Button type="primary" onClick={() => { return this.onSubmit(); }}>
              保存
            </Button>
          </div>
        }
        {preview.visible && this.renderPreview()}
        {isCreateAudioVisible && this.renderTextToAudio()}
      </div>
    );
  }
}

export {
  reducer,
};
