import { Market } from '~/engine';

const SET_STATE = 'MARKET_LIVE_SCRIPT/SET_STATE';
const CLEAR_STATE = 'MARKET_LIVE_SCRIPT/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchLiveHelpers = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().marketLiveScript;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };

    const { items, total } = await Market.fetchLiveHelpers(searchParams);
    dispatch(setState({ list: items, total }));
  };
};

export const delLiveHelper = (id) => {
  return async (dispatch) => {
    await Market.delLiveHelper(id);
    dispatch(fetchLiveHelpers());
  };
};

const _getInitState = () => {
  return {
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    list: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
