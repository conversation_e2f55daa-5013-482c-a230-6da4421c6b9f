import { PaginationTable } from '~/components';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>confirm } from 'antd';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.marketLiveScript;
  },
  actions,
)
export default class MarketLiveScript extends Component {
  static propTypes = {
    fetchLiveHelpers: PropTypes.func.isRequired,
    delLiveHelper: PropTypes.func.isRequired,
    list: PropTypes.array.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  componentDidMount = async () => {
    this.props.fetchLiveHelpers();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', align: 'center', width: 80 },
      { title: '名称', dataIndex: 'name', align: 'center' },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        align: 'center',
        render: (t) => { return moment(t).format('YYYY-MM-DD HH:mm:ss'); },
      },
      {
        title: '更新时间',
        dataIndex: 'updatedAt',
        align: 'center',
        render: (t) => { return moment(t).format('YYYY-MM-DD HH:mm:ss'); },
      },
      {
        title: '逐字稿',
        dataIndex: 'script',
        align: 'center',
        render: (x, row) => {
          return <a onClick={() => { return this.$push(`/market-live-script/${row.id}/edit`); }}>编辑</a>;
        },
      },
      {
        title: '直播稿',
        dataIndex: 'script',
        align: 'center',
        render: (x, row) => {
          return <a onClick={() => { return this.$push(`/market-live-script/${row.id}/preview`); }}>直播</a>;
        },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        render: (x, row) => {
          return (
            <>
              <a onClick={() => { return this.$push(`/market-live-script/${row.id}`); }} >编辑</a>
              <Divider type="vertical" />
              <Popconfirm title="确定删除?" onConfirm={() => { return this.props.delLiveHelper(row.id); }}>
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  render = () => {
    return (
      <div style={{ padding: 30, background: '#fff' }}>
        <div style={{ marginBottom: 10, textAlign: 'end' }}>
          <Button type="primary" onClick={() => { return this.$push('/market-live-script/add'); }}>
            新增
          </Button>
        </div>

        <PaginationTable
          dataSource={this.props.list}
          totalDataCount={1}
          columns={this.renderColumns()}
        />
      </div>
    );
  }
}

export {
  reducer,
};
