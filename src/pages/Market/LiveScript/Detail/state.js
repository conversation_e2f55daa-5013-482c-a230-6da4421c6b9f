import { Market } from '~/engine';

const SET_STATE = 'mpArticle/SET_STATE';
const CLEAR_STATE = 'mpArticle/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};


export const addLiveHelper = (params) => {
  return async () => {
    await Market.addLiveHelper(params);
  };
};

export const updateLiveHelper = (params) => {
  return async () => {
    await Market.updateLiveHelper(params);
  };
};

export const fetchLiveHelperOutlines = (params) => {
  return async () => {
    const data = await Market.fetchLiveHelperOutlines(params);
    return data;
  };
};

const _getInitState = () => {
  return {
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
