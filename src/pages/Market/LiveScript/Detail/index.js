import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Divider, Form, Input, Select, Upload } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import * as XLSX from 'xlsx';

import reducer, * as actions from './state';

const TYPE_ENUM = {
  interactive: '互动',
  comment: '评论',
};
@connect(
  (state) => {
    return state.marketLiveScriptDetail;
  },
  actions,
)
export default class MarketLiveScriptDetail extends Component {
  static propTypes = {
    addLiveHelper: PropTypes.func.isRequired,
    updateLiveHelper: PropTypes.func.isRequired,
    fetchLiveHelperOutlines: PropTypes.func.isRequired,
    match: PropTypes.object.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    datas: [{ knowledge: '', script: [{}] }],
  }

  componentDidMount = async () => {
    const { id } = this.props.match.params;
    if (!_.isUndefined(id)) {
      const { outlines, name } = await this.props.fetchLiveHelperOutlines({ id });
      this.setState({
        datas: outlines.map((outline) => {
          return {
            ...outline,
            role: (outline.scripts[0].role === 'other') ? '【麦手】' : '【老师】',
          };
        }),
        name,
      });
    }
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeScriptValueV1 = (e, key, sIdx, idx) => {
    const datas = _.cloneDeep(this.state.datas);
    const value = e?.target ? e.target.value : e;
    datas[sIdx].scripts[idx][key] = value;
    this.setState({ datas });
  }

  onChangeScriptValue = (e, key, idx) => {
    const datas = _.cloneDeep(this.state.datas);
    const value = e?.target ? e.target.value : e;
    datas[idx][key] = value;
    this.setState({ datas });
  }

  onReadFile = async (option, isDialogueMode) => {
    const reader = new FileReader();
    reader.readAsArrayBuffer(option.file);
    const datas = await new Promise((resolve) => {
      reader.onload = (e) => {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        if (!isDialogueMode) {
          const worksheet = workbook.Sheets[workbook.SheetNames[0]];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          const result = jsonData.filter((x) => { return !_.isEmpty(x); });
          result.shift();
          const arrs = result.map(([title, knowledgeContent, content]) => {
            return { title, knowledgeContent, content };
          });
          resolve(arrs);
        } else {
          let arrs = [];
          workbook.SheetNames.forEach((SheetName) => {
            const worksheet = workbook.Sheets[SheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
            const result = jsonData.filter((x) => { return !_.isEmpty(x); });
            result.shift();
            const newPart = result.map(([title, knowledgeContent]) => {
              return { role: title, content: knowledgeContent, part: SheetName };
            }).filter((item) => {
              return !!item.role;
            });
            arrs = arrs.concat(newPart);
          });
          resolve(arrs);
        }
      };
    });
    let { datas: localDatas } = this.state;
    localDatas = localDatas[0]?.content ? localDatas : [];
    this.setState({ datas: localDatas.concat(datas) });
    option.onSuccess();
  }

  onSubmit = async () => {
    const { id } = this.props.match.params;
    const { datas, name } = this.state;
    let outlines = [];
    if (!_.isUndefined(id)) {
      outlines = datas.map((x) => {
        const role = !x.role || (x.role.indexOf('老师') > -1) ? 'kol' : 'other';
        let scripts = x.content.split('\n').map((y) => { return { content: y, role }; });
        scripts = scripts.filter((s) => { return !_.isEmpty(s.content); });
        scripts = _.mergeWith(x.scripts || [], scripts);
        return { ...x, scripts };
      });
    } else {
      outlines = datas.map((x) => {
        const role = !x.role || (x.role.indexOf('老师') > -1) ? 'kol' : 'other';
        let scripts = x.content.split('\n').map((y) => { return { content: y, role }; });
        scripts = scripts.filter((s) => { return !_.isEmpty(s.content); });
        return { ...x, scripts, script: [] };
      });
    }

    if (!_.isUndefined(id)) {
      await this.props.updateLiveHelper({ id, name, outlines });
    } else {
      await this.props.addLiveHelper({ name, outlines });
    }
    this.onCancel();
  }

  onCancel = () => {
    this.$push('/market-live-script');
  }

  renderScript = (obj, sIdx) => {
    return (
      <div style={{ marginBottom: 20, position: 'relative' }}>
        <Form.Item label={`大纲${sIdx + 1}`}>
          <Input
            value={obj.title}
            onChange={(e) => { return this.onChangeScriptValue(e, 'title', sIdx); }}
          />
        </Form.Item>
        <Form.Item label="角色">
          <Input
            value={obj.role}
            onChange={(e) => { return this.onChangeScriptValue(e, 'role', sIdx); }}
          />
        </Form.Item>
        <Form.Item label="干货">
          <Input.TextArea
            autoSize={{ minRows: 3 }}
            value={obj.knowledgeContent}
            onChange={(e) => { return this.onChangeScriptValue(e, 'knowledgeContent', sIdx); }}
          />
        </Form.Item>
        <Form.Item label="内容">
          <Input.TextArea
            autoSize={{ minRows: 3 }}
            value={obj.content}
            onChange={(e) => { return this.onChangeScriptValue(e, 'content', sIdx); }}
          />
        </Form.Item>
        <span
          style={{
            position: 'absolute',
            right: '25%',
            transform: 'translateX(100%)',
            marginRight: '-10px',
            top: '4px',
          }}
        >
          <PlusOutlined
            style={{ fontSize: 24 }}
            onClick={() => {
              const { datas } = this.state;
              this.state.datas.splice((sIdx + 1), 0, { knowledge: '', script: [{}] });
              this.setState({ datas });
            }}
          />
          <Divider type="vertical" />
          <DeleteOutlined
            style={{ fontSize: 24 }}
            onClick={() => {
              const { datas } = this.state;
              this.state.datas.splice(sIdx, 1);
              this.setState({ datas });
            }}
          />
        </span>
      </div>
    );
  }

  renderScriptV1 = (obj, sIdx) => {
    return (
      <div style={{ marginBottom: 20 }}>
        <Form.Item label={`大纲${sIdx + 1}`}>
          <Input value={obj.outline} />
        </Form.Item>
        <Form.Item label="逐字稿">
          {
            (obj?.scripts || [{}]).map((x, idx) => {
              return (
                <Form className="common-form">
                  <Form.Item label="内容">
                    <Input.TextArea
                      value={x.script}
                      onChange={(e) => { return this.onChangeScriptValueV1(e, 'script', sIdx, idx); }}
                    />
                  </Form.Item>
                  <Form.Item label="类型">
                    <Select
                      style={{ width: 200 }}
                      value={x.type}
                      onChange={(e) => { return this.onChangeScriptValueV1(e, 'type', sIdx, idx); }}
                    >
                      {_.map(TYPE_ENUM, (v, k) => { return <Select.Option value={k}>{v}</Select.Option>; })}
                    </Select>
                  </Form.Item>
                  {
                    !_.isEmpty(x.type) && !_.isUndefined(TYPE_ENUM[x.type]) &&
                    <Form.Item label={TYPE_ENUM[x.type]}>
                      <Input.TextArea
                        value={x[x.type]}
                        onChange={(e) => { return this.onChangeScriptValueV1(e, x.type, sIdx, idx); }}
                      />
                    </Form.Item>
                  }
                </Form>
              );
            })
          }
        </Form.Item>
      </div>
    );
  }

  render = () => {
    return (
      <div style={{ background: '#fff', padding: 30, position: 'relative' }}>
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 14 }} className="common-form">
          <Form.Item label="名称" >
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Input
                style={{ width: '50%' }}
                value={this.state.name}
                onChange={(e) => { return this.setState({ name: e.target.value }); }}
              />
              <div>
                <Upload
                  accept=".xlsx"
                  customRequest={(e) => { return this.onReadFile(e, false); }}
                  showUploadList={false}
                >
                  <Button>单角色导入</Button>
                </Upload>
                <Divider type="vertical" />
                <Upload
                  accept=".xlsx"
                  customRequest={(e) => { return this.onReadFile(e, true); }}
                  showUploadList={false}
                >
                  <Button>对话导入</Button>
                </Upload>
              </div>
            </div>
          </Form.Item>
          <Divider />
          {this.state.datas.map((x, idx) => { return this.renderScript(x, idx); })}
          <div style={{ textAlign: 'center' }}>
            <Button
              style={{ width: 200 }}
              type="dashed"
              onClick={() => { return this.setState({ datas: [...this.state.datas, { scripts: [{}] }] }); }}
            >添加
            </Button>
          </div>
        </Form>

        <div style={{ textAlign: 'end', position: 'fixed', right: 60, bottom: 30 }}>
          <Button style={{ marginRight: 30 }} onClick={() => { return this.onCancel(); }}>
            取消
          </Button>
          <Button type="primary" onClick={() => { return this.onSubmit(); }}>
            保存
          </Button>
        </div>
      </div>
    );
  }
}

export {
  reducer,
};
