/* eslint-disable no-unused-vars */

const SET_STATE = 'NOVELIST/SET_STATE';

const CLEAR_STATE = 'NOVELIST/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

// 全剧设定相关actions
export const fetchPlotSettings = (params = {}) => {
  return async (dispatch, getState) => {
    const { plotSettingsPagination } = getState().novelist;

    // 模拟API调用
    const mockData = [
      {
        id: '1',
        title: '玄幻修仙世界观',
        genre: '玄幻',
        worldView: '修仙世界，分为凡人界、修仙界、仙界三个层次',
        mainCharacters: ['主角：林天', '女主：苏雪儿', '反派：魔尊'],
        plotOutline: '少年林天意外获得修仙传承，从凡人一步步修炼成仙的故事',
        createdAt: '2024-01-15',
        status: '进行中',
      },
      {
        id: '2',
        title: '都市重生商战',
        genre: '都市',
        worldView: '现代都市背景，商业竞争激烈',
        mainCharacters: ['主角：张明', '女主：李雅', '对手：王强'],
        plotOutline: '商业精英重生回到十年前，利用先知优势重新崛起',
        createdAt: '2024-01-10',
        status: '已完成',
      },
    ];

    dispatch(setState({
      plotSettings: mockData,
      plotSettingsTotal: mockData.length,
      plotSettingsPagination: {
        ...plotSettingsPagination,
        pageIndex: params.pageIndex || plotSettingsPagination.pageIndex,
      },
    }));
  };
};

export const addPlotSetting = (data) => {
  return async (dispatch) => {
    // 模拟API调用
    dispatch(fetchPlotSettings());
  };
};

export const updatePlotSetting = (id, data) => {
  return async (dispatch) => {
    // 模拟API调用
    dispatch(fetchPlotSettings());
  };
};

export const deletePlotSetting = (id) => {
  return async (dispatch) => {
    // 模拟API调用
    dispatch(fetchPlotSettings());
  };
};

// 章节设定相关actions
export const fetchChapterSettings = (params = {}) => {
  return async (dispatch, getState) => {
    const { chapterSettingsPagination } = getState().novelist;

    const mockData = [
      {
        id: '1',
        plotId: '1',
        plotTitle: '玄幻修仙世界观',
        chapterNumber: 1,
        chapterTitle: '意外传承',
        outline: '林天在山中遇险，意外获得古老修仙传承',
        characters: ['林天', '神秘老者'],
        scenes: ['深山', '古洞府'],
        keyEvents: ['遇险', '获得传承', '初次修炼'],
        wordCount: 3000,
        status: '已完成',
        createdAt: '2024-01-16',
      },
      {
        id: '2',
        plotId: '1',
        plotTitle: '玄幻修仙世界观',
        chapterNumber: 2,
        chapterTitle: '初入修仙门',
        outline: '林天下山后加入修仙门派，开始正式修炼',
        characters: ['林天', '掌门', '师兄弟们'],
        scenes: ['青云门', '练功房'],
        keyEvents: ['拜师', '测试资质', '分配住所'],
        wordCount: 2800,
        status: '进行中',
        createdAt: '2024-01-17',
      },
    ];

    dispatch(setState({
      chapterSettings: mockData,
      chapterSettingsTotal: mockData.length,
      chapterSettingsPagination: {
        ...chapterSettingsPagination,
        pageIndex: params.pageIndex || chapterSettingsPagination.pageIndex,
      },
    }));
  };
};

export const addChapterSetting = (data) => {
  return async (dispatch) => {
    dispatch(fetchChapterSettings());
  };
};

export const updateChapterSetting = (id, data) => {
  return async (dispatch) => {
    dispatch(fetchChapterSettings());
  };
};

export const deleteChapterSetting = (id) => {
  return async (dispatch) => {
    dispatch(fetchChapterSettings());
  };
};


// 小说家功能相关actions
export const generateCoreInfo = (params) => {
  return async (dispatch) => {
    try {
      // 暂时使用mock数据，后续替换为真实API
      const mockResult = {
        coreInfo: `**小说名称：** ${params.description.slice(0, 20)}...

**核心主题：** 基于您的描述生成的核心主题

**全局设定：**

**1. 时代背景与序幕：**
根据您的小说描述，设定在特定的时代背景下，为故事的展开奠定基础。

**2. 地理空间：**
故事发生的主要地理环境和空间设定，包括重要场所的描述。

**3. 核心势力与人物设定：**
主要角色的背景、性格特点以及各方势力的关系网络。

**4. 核心冲突与情节主线：**
推动故事发展的主要矛盾冲突和情节主线。

**5. 结构大纲（五幕式建议）：**
第一幕：开端
第二幕：发展
第三幕：高潮
第四幕：回落
第五幕：结局

**6. 创新点与立意方向：**
本作品的独特创新之处和深层立意。

**7. 关键伏笔与设定补充：**
重要的伏笔设置和世界观补充说明。

**8. 写作建议：**
针对本作品的具体写作技巧和注意事项。

**9. 风险与规避：**
可能遇到的创作风险和相应的规避策略。`,
      };

      // const result = await Market.generateNovelCoreInfo(params);
      return mockResult;
    } catch (error) {
      throw error;
    }
  };
};

export const generateChapterList = (params) => {
  return async (dispatch) => {
    try {
      // 暂时使用mock数据，后续替换为真实API
      const mockResult = {
        chapters: [
          { title: '序章：命运的开端', summary: '故事的起始，主角登场，埋下重要伏笔' },
          { title: '初入江湖', summary: '主角踏入新的世界，遇到第一个重要角色' },
          { title: '意外的相遇', summary: '关键转折点，故事开始加速发展' },
          { title: '暗流涌动', summary: '各方势力开始显现，矛盾逐渐激化' },
          { title: '真相浮现', summary: '重要秘密被揭露，故事进入高潮' },
          { title: '最终对决', summary: '主要矛盾达到顶点，决定性战斗' },
          { title: '新的开始', summary: '故事收尾，为后续发展留下空间' },
        ],
      };

      // const result = await Market.generateNovelChapterList(params);
      return mockResult;
    } catch (error) {
      throw error;
    }
  };
};

export const generateTimeline = (params) => {
  return async (dispatch) => {
    try {
      // 暂时使用mock数据，后续替换为真实API
      const mockResult = {
        timeline: `# 小说时间轴大事记

## 第一阶段：起始期
- **时间点1**：故事开端，主角出场
- **时间点2**：重要背景事件发生
- **时间点3**：第一个转折点

## 第二阶段：发展期
- **时间点4**：主要矛盾开始显现
- **时间点5**：关键角色登场
- **时间点6**：重要事件推动情节发展

## 第三阶段：高潮期
- **时间点7**：矛盾激化，冲突爆发
- **时间点8**：关键决策时刻
- **时间点9**：转折性事件

## 第四阶段：收尾期
- **时间点10**：主要问题解决
- **时间点11**：角色命运确定
- **时间点12**：故事结局，为续集铺垫`,
      };

      // const result = await Market.generateNovelTimeline(params);
      return mockResult;
    } catch (error) {
      throw error;
    }
  };
};

const _getInitState = () => {
  return {
    // 全剧设定
    plotSettings: [],
    plotSettingsTotal: 0,
    plotSettingsPagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt desc',
    },

    // 章节设定
    chapterSettings: [],
    chapterSettingsTotal: 0,
    chapterSettingsPagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'chapterNumber asc',
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
