import { Toast } from '~/components';
import Configs from '~/consts';
import { <PERSON>yunHelper } from '~/engine';
import { Platform } from '~/plugins';
import { Button, Input, Modal, Table, Tabs } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import ChapterSettingTabPane from './components/ChapterSettingTabPane';
import PlotSettingTabPane from './components/PlotSettingTabPane';
import reducer, * as actions from './state';

const NOVEL_KEY = `${Platform.isProd() ? 'prod' : 'stg'}-novel-list`;

@connect(
  (state) => {
    return state.marketNovelist;
  },
  actions,
)
export default class Novelist extends Component {
  static propTypes = {
  }

  state = {
    activeKey: 'plotSetting',
    dataSource: [],
    addOpen: false,
    novelName: '',
    loading: false,
    // 新增状态
    currentView: 'list', // 'list' | 'edit'
    selectedNovel: null, // 当前选中的小说
  }

  componentDidMount = async () => {
    await this.fetchNovelList();
  }

  componentWillUnmount = () => {
  }

  fetchNovelList = async () => {
    try {
      const resp = await fetch(`${Configs.OSS_CDN_DOMAIN}/fe_data/novelist/${NOVEL_KEY}.json?t=${Date.now()}`);
      const data = await resp.json();
      this.setState({ dataSource: data });
    } catch (error) {
      console.error('获取小说列表失败:', error); // eslint-disable-line no-console
      this.setState({ dataSource: [] });
    }
  }

  onAdd = () => {
    this.setState({ addOpen: true, novelName: '' });
  }

  onAddNovel = async () => {
    const { novelName } = this.state;
    const name = _.trim(novelName);

    if (_.isEmpty(name)) {
      Toast.show('请输入小说名称!', Toast.Type.WARNING);
      return;
    }

    this.setState({ loading: true });

    try {
      // 创建新小说数据
      const newNovel = {
        id: Date.now().toString(),
        name,
        status: '创建中',
        createdAt: new Date().toISOString(),
      };

      // 更新本地数据源
      const updatedDataSource = [...this.state.dataSource, newNovel];

      // 同步数据到远程JSON
      const blob = new Blob([JSON.stringify(updatedDataSource)], { type: 'application/json' });
      await AliyunHelper.uploadImageBlob(blob, () => { }, `fe_data/novelist/${NOVEL_KEY}.json`);

      this.setState({
        dataSource: updatedDataSource,
        addOpen: false,
        novelName: '',
        loading: false,
      });

      Toast.show('小说创建成功!', Toast.Type.SUCCESS);
    } catch (error) {
      console.error('创建小说失败:', error); // eslint-disable-line no-console
      Toast.show('创建小说失败!', Toast.Type.ERROR);
      this.setState({ loading: false });
    }
  }

  onTabChange = (activeKey) => {
    this.setState({ activeKey });
  }

  // 编辑小说
  onEditNovel = (novel) => {
    this.setState({
      currentView: 'edit',
      selectedNovel: novel,
      activeKey: 'plotSetting',
    });
  }

  // 返回列表
  onBackToList = () => {
    this.setState({
      currentView: 'list',
      selectedNovel: null,
    });
  }

  renderAddModal = () => {
    const { addOpen, novelName, loading } = this.state;

    return (
      <Modal
        open={addOpen}
        title="新增小说"
        onCancel={() => { return this.setState({ addOpen: false, novelName: '' }); }}
        onOk={this.onAddNovel}
        confirmLoading={loading}
        okText="确定"
        cancelText="取消"
      >
        <Input
          placeholder="请输入小说名称"
          value={novelName}
          onChange={(e) => { return this.setState({ novelName: e.target.value }); }}
          onPressEnter={this.onAddNovel}
        />
      </Modal>
    );
  }

  // 渲染编辑视图
  renderEditView = () => {
    const { activeKey, selectedNovel } = this.state;

    return (
      <div style={{ padding: 30, background: '#fff' }}>
        <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div>
            <Button onClick={this.onBackToList} style={{ marginRight: 16 }}>
              ← 返回列表
            </Button>
            <span style={{ fontSize: 16, fontWeight: 'bold' }}>
              编辑小说：{selectedNovel?.name}
            </span>
          </div>
        </div>

        <Tabs
          activeKey={activeKey}
          onChange={this.onTabChange}
        >
          <Tabs.TabPane tab="全剧设定" key="plotSetting">
            <PlotSettingTabPane
              {...this.props}
              selectedNovel={selectedNovel}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="章节" key="chapterSetting">
            <ChapterSettingTabPane
              {...this.props}
              selectedNovel={selectedNovel}
            />
          </Tabs.TabPane>
        </Tabs>
      </div>
    );
  }

  // 渲染列表视图
  renderListView = () => {
    return (
      <div style={{ padding: 30, background: '#fff' }}>
        <Button type="primary" onClick={this.onAdd} style={{ marginBottom: 16 }}>
          新增小说
        </Button>
        <Table
          dataSource={this.state.dataSource}
          columns={[
            {
              title: '小说名称',
              dataIndex: 'name',
              key: 'name',
            },
            {
              title: '创建时间',
              dataIndex: 'createdAt',
              key: 'createdAt',
              render: (text) => {
                return moment(text).format('YYYY-MM-DD HH:mm:ss');
              },
            },
            {
              title: '操作',
              key: 'action',
              render: (text, record) => {
                return (
                  <Button
                    type="link"
                    onClick={() => { this.onEditNovel(record); }}
                  >
                    编辑
                  </Button>
                );
              },
            },
          ]}
          rowKey="id"
          pagination={false}
        />

        {this.state.addOpen && this.renderAddModal()}
      </div>
    );
  }

  render = () => {
    const { currentView } = this.state;

    if (currentView === 'edit') {
      return this.renderEditView();
    }

    return this.renderListView();
  }
}

export {
  reducer,
};
