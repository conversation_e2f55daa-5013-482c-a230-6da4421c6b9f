import { Toast } from '~/components';
import Configs from '~/consts';
import Engine, { <PERSON><PERSON><PERSON>el<PERSON>, Sessions } from '~/engine';
import { EVENT_TYPE } from '~/pages/Playground/Configs';
import ReconnectingWebSocket from '~/pages/Playground/WebSocket';
import { Platform, StringExtension } from '~/plugins';
import { Button, Card, Divider, Drawer, Input, List, Typography } from 'antd';
import * as JSONC from 'jsonc-parser';
import _ from 'lodash';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { PureComponent } from 'react';
import Markdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const { TextArea } = Input;
const { Title } = Typography;

const FLOW_MAP = {
  pord: {
    plot: '7riRdNnfnxLx9N0TURAeRn',
  },
  stg: {
    plot: 'LaD2x9whJ9xFm9T36cUXZT',
  },
};


export default class PlotSettingTabPane extends PureComponent {
  static propTypes = {
    selectedNovel: PropTypes.object,
  }
  state = {
    // 核心信息生成
    novelDescription: '',
    coreInfo: '',
    coreInfoLoading: false,

    // 章节目录生成
    chapterRequirement: '',
    chapterList: '',
    chapters: [],
    chapterStr: '',
    chapterListLoading: false,

    // 时间轴生成
    timelineRequirement: '',
    timeline: '',
    timelineLoading: false,

    openPreview: false,
    previewContent: '',
  }

  componentDidMount = async () => {
    try {
      await this.fetchNovelInfo();
    } catch (error) {
      console.error('获取小说信息失败:', error); // eslint-disable-line no-console
    }
  }

  componentDidUpdate = async (prevProps) => {
    if (prevProps.selectedNovel?.id !== this.props.selectedNovel?.id) {
      try {
        await this.fetchNovelInfo();
      } catch (error) {
        console.error('获取小说信息失败:', error); // eslint-disable-line no-console
      }
    }
  }

  getNovelKey = () => {
    const { selectedNovel } = this.props;
    if (!selectedNovel?.id) {
      return `${Platform.isProd() ? 'prod' : 'stg'}-novel`;
    }
    return `${Platform.isProd() ? 'prod' : 'stg'}-novel-${selectedNovel.id}`;
  }

  fetchNovelInfo = async () => {
    const novelKey = this.getNovelKey();
    try {
      const resp = await fetch(`${Configs.OSS_CDN_DOMAIN}/fe_data/novelist/${novelKey}.json?t=${Date.now()}`);
      const data = await resp.json();
      const chapters = JSONC.parse(data.chapterList.replace(/'/g, '‘') || '[]');
      let chapterStr = '';
      if (chapters.length > 0) {
        chapterStr = chapters.map((item) => {
          return `第${item.chapterNumber}章 ${item.chapterTitle}：${item.chapterContent}`;
        }).join('\n');
      }
      return this.setState({ ...data, chapters, chapterStr });
    } catch (error) {
      // 如果文件不存在，初始化空数据
      console.log('小说数据文件不存在，初始化空数据'); // eslint-disable-line no-console
      return this.setState({
        novelDescription: '',
        coreInfo: '',
        chapterRequirement: '',
        chapterList: '',
        chapters: [],
        chapterStr: '',
        timelineRequirement: '',
        timeline: '',
      });
    }
  }

  updateNovelInfo = () => {
    return async () => {
      const novelKey = this.getNovelKey();

      // 先获取现有数据
      let existingData = {};
      try {
        const resp = await fetch(`${Configs.OSS_CDN_DOMAIN}/fe_data/novelist/${novelKey}.json?t=${Date.now()}`);
        existingData = await resp.json();
      } catch (error) {
        console.log('获取现有数据失败，使用空对象'); // eslint-disable-line no-console
      }

      // 只更新当前组件相关的字段，保留其他字段
      // 只有当字段有值时才更新，避免用空值覆盖现有数据
      const novelData = { ...existingData };
      const updateKeys = [
        'novelDescription', 'coreInfo', 'chapterRequirement', 'chapterList', 'timelineRequirement', 'timeline',
      ];
      updateKeys.forEach((key) => {
        if (!_.isEmpty(this.state[key])) {
          novelData[key] = this.state[key];
        }
      });

      const blob = new Blob([JSON.stringify(novelData, null, 2)], { type: 'text/plain' });
      await AliyunHelper.clipsUploadImage(blob, () => { }, {
        filePath: 'fe_data/novelist',
        fileName: novelKey,
        fileType: '.json',
      });
      Toast.show('信息已保存!', Toast.Type.SUCCESS);
    };
  };

  onReceiveMsg = async (e, flowType) => {
    if (e?.data !== 'pong') {
      const originData = JSON.parse(e.data);
      const { type, data } = StringExtension.snakeToCamelObj(originData);
      if (type === EVENT_TYPE.FINAL_RESULT) {
        const result = JSON.parse(data?.output);
        // console.log(result);
        await this.setState({ [flowType]: result[flowType], [`${flowType}Loading`]: false });
        // 更新小说信息
        await this.updateNovelInfo()();
        if (flowType === 'chapterList') {
          let chapterStr = '';
          const chapters = JSONC.parse(result.replace(/'/g, '‘') || '[]');
          if (chapters.length > 0) {
            chapterStr = chapters.map((item) => {
              return `第${item.chapterNumber}章 ${item.chapterTitle}：${item.chapterContent}`;
            }).join('\n');
          }

          this.setState({ chapters, chapterStr });
        }
      }
    }
  }

  // 生成核心信息
  onGenerateCoreInfo = async () => {
    const { novelDescription } = this.state;
    if (!novelDescription.trim()) {
      return;
    }

    if (this.ws) {
      this.ws.close();
    }

    try {
      const flowId = FLOW_MAP[Platform.isProd() ? 'pord' : 'stg'].plot;
      const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflow-v2/run/${flowId}`;
      const query = { access_token: Sessions.getToken() };
      this.ws = new ReconnectingWebSocket(
        `${path}?${qs.stringify(query)}`,
        [],
        (e) => { this.onReceiveMsg(e, 'coreInfo'); },
        () => {
          // WebSocket连接成功后发送消息
          this.setState({ coreInfoLoading: true });
          this.ws.send(JSON.stringify({
            text: JSON.stringify({
              desc: novelDescription.trim(),
              chapter: '',
              timeline: '',
            }),
            type: 'message',
            is_beta: false,
          }));
        },
      );
    } catch (error) {
      console.error('生成核心信息失败:', error); // eslint-disable-line no-console
    }
  }

  // 生成章节目录
  onGenerateChapterList = async () => {
    const { chapterRequirement } = this.state;
    if (!chapterRequirement.trim()) {
      return;
    }

    if (this.ws) {
      this.ws.close();
    }
    try {
      const flowId = FLOW_MAP[Platform.isProd() ? 'pord' : 'stg'].plot;
      const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflow-v2/run/${flowId}`;
      const query = { access_token: Sessions.getToken() };
      this.ws = new ReconnectingWebSocket(
        `${path}?${qs.stringify(query)}`,
        [],
        (e) => { this.onReceiveMsg(e, 'chapterList'); },
        () => {
          // WebSocket连接成功后发送消息
          this.setState({ chapterListLoading: true });
          this.ws.send(JSON.stringify({
            text: JSON.stringify({
              desc: this.state.novelDescription.trim(),
              core: this.state.coreInfo.trim(),
              chapter: chapterRequirement.trim(),
              timeline: '',
            }),
            type: 'message',
            is_beta: false,
          }));
        },
      );
    } catch (error) {
      console.error('生成章节目录失败:', error); // eslint-disable-line no-console
    }
  }

  // 生成时间轴
  onGenerateTimeline = async () => {
    const { timelineRequirement } = this.state;
    if (!timelineRequirement.trim()) {
      return;
    }

    if (this.ws) {
      this.ws.close();
    }

    try {
      const flowId = FLOW_MAP[Platform.isProd() ? 'pord' : 'stg'].plot;
      const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflow-v2/run/${flowId}`;
      const query = { access_token: Sessions.getToken() };
      this.ws = new ReconnectingWebSocket(
        `${path}?${qs.stringify(query)}`,
        [],
        (e) => { this.onReceiveMsg(e, 'timeline'); },
        () => {
          // WebSocket连接成功后发送消息
          this.setState({ timelineLoading: true });
          this.ws.send(JSON.stringify({
            text: JSON.stringify({
              desc: this.state.novelDescription.trim(),
              core: this.state.coreInfo.trim(),
              chapter: this.state.chapterRequirement.trim(),
              menu: this.state.chapterStr.trim(),
              timeline: timelineRequirement.trim(),
            }),
            type: 'message',
            is_beta: false,
          }));
        },
      );
    } catch (error) {
      console.error('生成时间轴失败:', error); // eslint-disable-line no-console
    }
  }

  // 预览内容
  onPreview = async (content) => {
    this.setState({
      previewContent: content,
      openPreview: true,
    });
  }

  // 渲染核心信息生成
  renderCoreInfoGenerator = () => {
    const { novelDescription, coreInfo, coreInfoLoading } = this.state;

    return (
      <Card title="核心信息生成" style={{ marginBottom: 24 }}>
        <div style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 2 }}>
            <Title level={5} style={{ marginBottom: 0 }}>小说描述</Title>
            <Divider type="vertical" />
            <Button
              size="small"
              type="primary"
              onClick={this.onGenerateCoreInfo}
              loading={coreInfoLoading}
              disabled={!novelDescription.trim()}
            >
              生成核心信息
            </Button>
          </div>
          <TextArea
            value={novelDescription}
            onChange={(e) => { return this.setState({ novelDescription: e.target.value }); }}
            placeholder="请输入小说描述，包括故事背景、主要情节、角色设定等..."
            rows={4}
            style={{ marginBottom: 12 }}
          />
        </div>

        {coreInfo && (
          <div>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 2 }}>
              <Title level={5} style={{ marginBottom: 0 }}>生成的核心信息</Title>
              <Divider type="vertical" />
              <Button
                size="small"
                type="primary"
                onClick={async () => { await this.updateNovelInfo()(); }}
              >
                保存
              </Button>
              <Divider type="vertical" />
              <Button
                size="small"
                onClick={async () => { await this.onPreview(coreInfo); }}
              >
                预览
              </Button>
            </div>
            <TextArea
              value={coreInfo}
              onChange={(e) => { return this.setState({ coreInfo: e.target.value }); }}
              rows={12}
              placeholder="生成的核心信息将在这里显示..."
            />
          </div>
        )}
      </Card>
    );
  }

  // 渲染章节目录生成
  renderChapterListGenerator = () => {
    const { chapterRequirement, chapterList, chapterListLoading } = this.state;

    return (
      <Card title="章节目录生成" style={{ marginBottom: 24 }}>
        <div style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 2 }}>
            <Title level={5} style={{ marginBottom: 0 }}>章节要求</Title>
            <Divider type="vertical" />
            <Button
              size="small"
              type="primary"
              onClick={this.onGenerateChapterList}
              loading={chapterListLoading}
              disabled={!chapterRequirement.trim()}
            >
              生成章节目录
            </Button>
          </div>
          <TextArea
            value={chapterRequirement}
            onChange={(e) => { return this.setState({ chapterRequirement: e.target.value }); }}
            placeholder="请输入章节要求，如章节数量、每章大致内容、情节发展等..."
            rows={3}
            style={{ marginBottom: 12 }}
          />

        </div>

        {chapterList.length > 0 && (
          <div>
            <Title level={5}>生成的章节目录</Title>
            <List
              style={{ maxHeight: '30vh', overflow: 'auto' }}
              bordered
              dataSource={this.state.chapters}
              renderItem={(item, index) => {
                return (
                  <List.Item>
                    <div style={{ width: '100%' }}>
                      <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
                        第{index + 1}章：{item.chapterTitle}
                      </div>
                      <div style={{ color: '#666', fontSize: '14px' }}>
                        {item.chapterContent}
                      </div>
                    </div>
                  </List.Item>
                );
              }}
            />
          </div>
        )}
      </Card>
    );
  }

  // 渲染时间轴生成
  renderTimelineGenerator = () => {
    const { timelineRequirement, timeline, timelineLoading } = this.state;

    return (
      <Card title="大事记生成" style={{ marginBottom: 24 }}>
        <div style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 2 }}>
            <Title level={5} style={{ marginBottom: 0 }}>大事记要求</Title>
            <Divider type="vertical" />
            <Button
              size="small"
              type="primary"
              onClick={this.onGenerateTimeline}
              loading={timelineLoading}
              disabled={!timelineRequirement.trim()}
            >
              生成大事记
            </Button>
          </div>
          <TextArea
            value={timelineRequirement}
            onChange={(e) => { return this.setState({ timelineRequirement: e.target.value }); }}
            placeholder="请输入大事记要求，如时间跨度、重要事件、发展节点等..."
            rows={3}
            style={{ marginBottom: 12 }}
          />

        </div>
        {timeline && (
          <div>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 2 }}>
              <Title level={5} style={{ marginBottom: 0 }}>生成的核心信息</Title>
              <Divider type="vertical" />
              <Button
                size="small"
                type="primary"
                onClick={async () => { await this.updateNovelInfo()(); }}
              >
                保存
              </Button>
              <Divider type="vertical" />
              <Button
                size="small"
                onClick={async () => { await this.onPreview(timeline); }}
              >
                预览
              </Button>
            </div>
            <TextArea
              value={timeline}
              onChange={(e) => { return this.setState({ timeline: e.target.value }); }}
              rows={10}
              placeholder="生成的大事记将在这里显示..."
            />
          </div>
        )}
      </Card>
    );
  }

  renderPreviewDrawer = () => {
    const { openPreview, previewContent } = this.state;

    return (
      <Drawer
        title="内容预览"
        placement="right"
        width="50vw"
        open={openPreview}
        onClose={() => { this.setState({ openPreview: false }); }}
        destroyOnClose
      >
        <Markdown remarkPlugins={[remarkGfm]}>{previewContent}</Markdown>
      </Drawer>
    );
  }

  render = () => {
    return (
      <div>
        {this.renderCoreInfoGenerator()}
        {this.renderChapterListGenerator()}
        {this.renderTimelineGenerator()}
        {this.state.openPreview && this.renderPreviewDrawer()}
      </div>
    );
  }
}
