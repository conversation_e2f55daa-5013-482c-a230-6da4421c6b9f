/* eslint-disable react/no-array-index-key */
import { AudioPlayer, PaginationTable, Toast } from '~/components';
import Configs from '~/consts';
import Engine, { AliyunHelper, Market, Sessions } from '~/engine';
import { EVENT_TYPE } from '~/pages/Playground/Configs';
import ReconnectingWebSocket from '~/pages/Playground/WebSocket';
import { Platform, StringExtension } from '~/plugins';
import { Button, Divider, Drawer, Form, Input, Tabs } from 'antd';
import * as JSONC from 'jsonc-parser';
import _ from 'lodash';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { PureComponent } from 'react';
import Markdown from 'react-markdown';

const { TextArea } = Input;

const flowId = Platform.isProd() ? '8AxH87bqhrrp6H45zmg8r4' : '290tHjRchSw9wcAgnKzfot';
export default class ChapterSettingTabPane extends PureComponent {
  static propTypes = {
    addChapterSetting: PropTypes.func.isRequired,
    updateChapterSetting: PropTypes.func.isRequired,
    selectedNovel: PropTypes.object,
  }

  state = {
    openDrawer: false,
    chapterInfo: {},
    chapters: [], // 可选章节列表
    chapterSettingRequirements: '', // 章节设定要求
    chapterWritingRequirements: '', // 章节撰写要求
    audioGenerating: false, // 音频生成状态
    audioUrl: '', // 生成的音频URL
  }

  componentDidMount = async () => {
    try {
      await this.fetchNovelInfo();
    } catch (error) {
      console.error('获取小说信息失败:', error); // eslint-disable-line no-console
    }
  }

  componentDidUpdate = async (prevProps) => {
    if (prevProps.selectedNovel?.id !== this.props.selectedNovel?.id) {
      try {
        await this.fetchNovelInfo();
      } catch (error) {
        console.error('获取小说信息失败:', error); // eslint-disable-line no-console
      }
    }
  }

  getNovelKey = () => {
    const { selectedNovel } = this.props;
    if (!selectedNovel?.id) {
      return `${Platform.isProd() ? 'prod' : 'stg'}-novel`;
    }
    return `${Platform.isProd() ? 'prod' : 'stg'}-novel-${selectedNovel.id}`;
  }

  fetchNovelInfo = async () => {
    const novelKey = this.getNovelKey();
    try {
      const resp = await fetch(`${Configs.OSS_CDN_DOMAIN}/fe_data/novelist/${novelKey}.json?t=${Date.now()}`);
      const data = await resp.json();
      const chapters = JSONC.parse(data.chapterList || '[]');
      let chapterStr = '';
      if (chapters.length > 0) {
        chapterStr = chapters.map((item) => {
          return `第${item.chapterNumber}章 ${item.chapterTitle}：${item.chapterContent}`;
        }).join('\n');
      }

      return this.setState({ ...data, chapters, chapterStr });
    } catch (error) {
      // 如果文件不存在，初始化空数据
      console.log('小说数据文件不存在，初始化空数据'); // eslint-disable-line no-console
      return this.setState({
        chapters: [],
        chapterStr: '',
        chapterSettingRequirements: '',
        chapterWritingRequirements: '',
      });
    }
  }

  updateNovelInfo = () => {
    return async () => {
      const novelKey = this.getNovelKey();

      // 先获取现有数据
      let existingData = {};
      try {
        const resp = await fetch(`${Configs.OSS_CDN_DOMAIN}/fe_data/novelist/${novelKey}.json?t=${Date.now()}`);
        existingData = await resp.json();
      } catch (error) {
        console.log('获取现有数据失败，使用空对象'); // eslint-disable-line no-console
      }

      // 只更新当前组件相关的字段
      // 只有当字段有值时才更新，避免用空值覆盖现有数据
      const novelData = { ...existingData };

      const updateKeys = [
        'chapterSettingRequirements', 'chapterWritingRequirements',
      ];
      updateKeys.forEach((key) => {
        if (!_.isEmpty(this.state[key])) {
          novelData[key] = this.state[key];
        }
      });

      const blob = new Blob([JSON.stringify(novelData, null, 2)], { type: 'text/plain' });
      await AliyunHelper.clipsUploadImage(blob, () => { }, {
        filePath: 'fe_data/novelist',
        fileName: novelKey,
        fileType: '.json',
      });
      Toast.show('信息已保存!', Toast.Type.SUCCESS);
    };
  };

  fetchNovelChapterById = async (chapterId) => {
    try {
      const novelKey = this.getNovelKey();
      const fileName = `${novelKey}-article-${chapterId}`;
      const resp = await fetch(`${Configs.OSS_CDN_DOMAIN}/fe_data/novelist/${fileName}.json?t=${Date.now()}`);
      const data = await resp.json();
      this.setState({
        chapterInfo: data,
        chapterId,
        audioUrl: data.audioUrl || '', // 设置音频URL状态
      });
    } catch (error) {
      this.setState({ chapterInfo: {}, chapterId, audioUrl: '' });
    }
  }

  fetchNovelChapterDataById = async (chapterId) => {
    try {
      const novelKey = this.getNovelKey();
      const fileName = `${novelKey}-article-${chapterId}`;
      const resp = await fetch(`${Configs.OSS_CDN_DOMAIN}/fe_data/novelist/${fileName}.json?t=${Date.now()}`);
      const data = await resp.json();
      return data;
    } catch (error) {
      return {};
    }
  }

  updateNovelChapter = async () => {
    const { chapterInfo, chapterId } = this.state;
    const novelKey = this.getNovelKey();
    const fileName = `${novelKey}-article-${chapterId}`;
    const blob = new Blob([JSON.stringify(chapterInfo, null, 2)], { type: 'text/plain' });
    await AliyunHelper.clipsUploadImage(blob, () => { }, {
      filePath: 'fe_data/novelist',
      fileName,
      fileType: '.json',
    });
    Toast.show('章节信息已保存!', Toast.Type.SUCCESS);
  }

  onReceiveMsg = async (e, flowType) => {
    if (e?.data !== 'pong') {
      const originData = JSON.parse(e.data);
      const { type, data } = StringExtension.snakeToCamelObj(originData);
      if (type === EVENT_TYPE.FINAL_RESULT) {
        const result = JSON.parse(data?.output);
        await this.setState({
          chapterInfo: { ...this.state.chapterInfo, [flowType]: result[flowType] },
          [`${flowType}Loading`]: false,
        });
        await this.updateNovelChapter();
      }
    }
  }

  onSave = async () => {
    const { editData } = this.state;
    if (editData.id) {
      await this.props.updateChapterSetting(editData.id, editData);
    } else {
      await this.props.addChapterSetting(editData);
    }
    this.setState({ openDrawer: false, editData: {} });
  }

  onCancel = () => { this.setState({ openDrawer: false }); }

  onChangeValue = (key, value) => {
    this.setState({
      editData: { ...this.state.editData, [key]: value },
    });
  }

  // 生成章节设定
  onGenerateChapterSetting = async () => {
    const { chapterSettingRequirements, chapterId, chapters } = this.state;

    if (!chapterSettingRequirements.trim()) {
      Toast.show('请先输入章节设定要求!', Toast.Type.ERROR);
      return;
    }
    const lastChapterId = +chapterId - 1 || 0;
    const lastChapter = await this.fetchNovelChapterDataById(lastChapterId);

    const cs = chapters.find((item) => { return item.chapterNumber === chapterId; });
    const chapterInfo = `第${cs.chapterNumber}章 ${cs.chapterTitle}：${cs.chapterContent}`;
    const prompt = chapterSettingRequirements.replace(/{{chapter}}/g, chapterInfo);

    let lastChapterInfo = '';
    if (!_.isEmpty(lastChapter)) {
      lastChapterInfo += `上一章设定：${lastChapter.setting}\n\n`;
      lastChapterInfo += `上一章内容：${lastChapter.writing}\n\n`;
    }

    if (this.ws) {
      this.ws.close();
    }

    try {
      const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflow-v2/run/${flowId}`;
      const query = { access_token: Sessions.getToken() };
      this.ws = new ReconnectingWebSocket(
        `${path}?${qs.stringify(query)}`,
        [],
        (e) => { this.onReceiveMsg(e, 'setting'); },
        () => {
          // WebSocket连接成功后发送消息
          this.setState({ settingLoading: true });
          this.ws.send(JSON.stringify({
            text: JSON.stringify({
              core: this.state.coreInfo.trim(),
              menu: this.state.chapterStr.trim(),
              timeline: `${lastChapterInfo}${this.state.timeline.trim()}`,
              setting: '',
              prompt,
            }),
            type: 'message',
            is_beta: false,
          }));
        },
      );
    } catch (error) {
      console.error('生成核心信息失败:', error); // eslint-disable-line no-console
    }
  }

  // 生成音频
  onGenerateAudio = async () => {
    const { chapterInfo } = this.state;

    if (!chapterInfo?.writing) {
      Toast.show('请先生成章节内容!', Toast.Type.ERROR);
      return;
    }

    this.setState({ audioGenerating: true });

    try {
      const { fileUrl } = await Market.textToAudio({
        text: chapterInfo.writing,
        ttsSettings: { provider: 'bytedance', voice: 'zh_male_changtianyi_mars_bigtts' },
      });

      // 更新章节信息中的音频URL
      const updatedChapterInfo = { ...chapterInfo, audioUrl: fileUrl };
      this.setState({
        chapterInfo: updatedChapterInfo,
        audioUrl: fileUrl,
        audioGenerating: false,
      });

      // 保存到远程存储
      await this.updateNovelChapter();

      Toast.show('音频生成成功!', Toast.Type.SUCCESS);
    } catch (error) {
      console.error('音频生成失败:', error); // eslint-disable-line no-console
      Toast.show('音频生成失败，请重试!', Toast.Type.ERROR);
      this.setState({ audioGenerating: false });
    }
  }

  // 生成章节撰写
  onGenerateChapterWriting = async () => {
    const { chapterWritingRequirements, chapterId, chapters } = this.state;

    if (!chapterWritingRequirements.trim()) {
      Toast.show('请先输入章节撰写要求!', Toast.Type.ERROR);
      return;
    }

    if (!this.state.chapterInfo?.setting) {
      Toast.show('请先生成章节设定!', Toast.Type.ERROR);
      return;
    }

    const lastChapterId = +chapterId - 1 || 0;
    const lastChapter = await this.fetchNovelChapterDataById(lastChapterId);

    const cs = chapters.find((item) => { return item.chapterNumber === chapterId; });
    const chapterInfo = `第${cs.chapterNumber}章 ${cs.chapterTitle}：${cs.chapterContent}`;
    const prompt = chapterWritingRequirements.replace(/{{chapter}}/g, chapterInfo);

    const lastChapterInfo = _.isEmpty(lastChapter) ? '' : `上一章内容：${lastChapter.writing}\n\n`;

    if (this.ws) {
      this.ws.close();
    }

    try {
      const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflow-v2/run/${flowId}`;
      const query = { access_token: Sessions.getToken() };
      this.ws = new ReconnectingWebSocket(
        `${path}?${qs.stringify(query)}`,
        [],
        (e) => { this.onReceiveMsg(e, 'writing'); },
        () => {
          // WebSocket连接成功后发送消息
          this.setState({ writingLoading: true });
          this.ws.send(JSON.stringify({
            text: JSON.stringify({
              core: this.state.coreInfo.trim(),
              menu: this.state.chapterStr.trim(),
              timeline: this.state.timeline.trim(),
              setting: `${lastChapterInfo} ${this.state.chapterInfo?.setting || ''}`,
              prompt,
            }),
            type: 'message',
            is_beta: false,
          }));
        },
      );
    } catch (error) {
      console.error('生成章节撰写失败:', error); // eslint-disable-line no-console
    }
  }

  onShowChapterSetting = async (record, activeTab) => {
    await this.fetchNovelChapterById(record.chapterNumber);
    this.setState({ openChapterDrawer: true, activeTab });
  }

  onAdd = () => {
    this.setState({ openDrawer: true, editData: {} });
  }

  renderColumns = () => {
    return [
      {
        title: '章节',
        dataIndex: 'chapterNumber',
        key: 'chapterNumber',
        width: 80,
      },
      {
        title: '章节标题',
        dataIndex: 'chapterTitle',
        key: 'chapterTitle',
        width: 200,
      },
      {
        title: '章节简介',
        dataIndex: 'chapterContent',
        key: 'chapterContent',
      },
      {
        title: '操作',
        key: 'action',
        render: (txt, record) => {
          return (
            <div>
              <Button
                type="link"
                size="small"
                onClick={() => { return this.onShowChapterSetting(record, 'setting'); }}
              >
                生成设定
              </Button>

              <Button
                type="link"
                size="small"
                onClick={() => { return this.onShowChapterSetting(record, 'writing'); }}
              >
                撰写章节
              </Button>
            </div>
          );
        },
      },
    ];
  }

  renderDrawer = () => {
    const { openDrawer, chapterSettingRequirements, chapterWritingRequirements } = this.state;

    return (
      <Drawer
        title="需求设定"
        open={openDrawer}
        width="50vw"
        onClose={this.onCancel}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button onClick={this.onCancel} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button
              type="primary"
              onClick={async () => { await this.updateNovelInfo()(); }}
            >
              保存
            </Button>
          </div>
        }
      >
        <Form labelCol={{ span: 2 }} className="common-form">
          <Form.Item label="章节设定要求" required>
            <TextArea
              value={chapterSettingRequirements}
              onChange={(e) => { return this.setState({ chapterSettingRequirements: e.target.value }); }}
              placeholder="请输入章节设定要求，如：角色设定、场景描述、情节要点、写作风格等..."
              rows={10}
            />
          </Form.Item>
          <Form.Item label="章节撰写要求" required>
            <TextArea
              value={chapterWritingRequirements}
              onChange={(e) => { return this.setState({ chapterWritingRequirements: e.target.value }); }}
              placeholder="请输入章节撰写要求，如：角色对话、场景描写、情节推进等..."
              rows={10}
            />
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderChapterDrawer = () => {
    const { openChapterDrawer, chapterInfo } = this.state;

    return (
      <Drawer
        title="章节"
        open={openChapterDrawer}
        width="50vw"
        onClose={() => { return this.setState({ openChapterDrawer: false, chapterInfo: {} }); }}
      >
        <Tabs
          defaultActiveKey="setting"
          activeKey={this.state.activeTab}
          onChange={(key) => { this.setState({ activeTab: key }); }}
        >
          <Tabs.TabPane tab="章节设定" key="setting">
            <Button
              size="small"
              style={{ marginBottom: 5 }}
              onClick={this.onGenerateChapterSetting}
              loading={this.state.settingLoading}
            >
              生成章节设定
            </Button>
            <Divider style={{ margin: 0 }} />
            <div style={{ height: 'calc(100vh - 320px)', overflow: 'auto' }}>
              <Markdown>{chapterInfo?.setting}</Markdown>
            </div>
          </Tabs.TabPane>
          <Tabs.TabPane tab="章节撰写" key="writing">
            <div style={{ marginBottom: 5 }}>
              <Button
                size="small"
                onClick={this.onGenerateChapterWriting}
                loading={this.state.writingLoading}
                style={{ marginRight: 8 }}
              >撰写章节
              </Button>
              {chapterInfo?.writing && (
                <Button
                  size="small"
                  onClick={this.onGenerateAudio}
                  loading={this.state.audioGenerating}
                  type="default"
                >
                  生成音频
                </Button>
              )}
              {!!chapterInfo?.writing?.length &&
                <span style={{ float: 'right' }}> &nbsp; 共<b>{chapterInfo?.writing?.length}</b>字</span>
              }
            </div>
            <Divider style={{ margin: 0 }} />
            <div style={{ height: 'calc(100vh - 380px)', overflow: 'auto' }}>
              <Markdown>{chapterInfo?.writing}</Markdown>
            </div>

            {/* 音频播放器 */}
            {(chapterInfo?.audioUrl || this.state.audioUrl) && (
              <div style={{ marginTop: 10, padding: '10px 0', borderTop: '1px solid #f0f0f0' }}>
                <div style={{ marginBottom: 5, fontSize: '12px', color: '#666' }}>章节音频：</div>
                <AudioPlayer
                  url={chapterInfo?.audioUrl || this.state.audioUrl}
                  audioStyle={{ width: '100%' }}
                />
              </div>
            )}
          </Tabs.TabPane>
        </Tabs>
      </Drawer>
    );
  }

  render = () => {
    return (
      <>
        <Button type="primary" onClick={this.onAdd} style={{ marginBottom: 16, float: 'right' }}>
          设定需求
        </Button>

        <PaginationTable
          dataSource={this.state.chapters}
          needsPagination={false}
          columns={this.renderColumns()}
        />

        {this.state.openDrawer && this.renderDrawer()}
        {this.state.openChapterDrawer && this.renderChapterDrawer()}
      </>
    );
  }
}
