import _ from 'lodash';

export default class Utils {
  static analyzeDyUrl = async (url) => {
    const sourceUrl = _.head(url.split('?'));
    const userId = _.trimStart(sourceUrl, 'https://www.douyin.com/user/');
    const resp = await fetch(`https://open-video-user-frontend-ehfcwpjogu.cn-shanghai.fcapp.run?q=${userId}`);
    const { nickname, signature } = await resp.json();
    return {
      author: nickname,
      platform: '抖音',
      description: signature,
      sourceUrl,
      reviewType: 'machine',
      enableFetchHistory: true,
      historyLimit: 999,
      enableFetchNew: true,
      optimizeLimit: 999,
      partitionType: 'auto',
    };
  };
}
