/* eslint-disable max-len */
import './index.less';

import { CopyOutlined, DownloadOutlined } from '@ant-design/icons';
import { Toast } from '~/components';
import Engine, { Materials, Sessions } from '~/engine';
import { EVENT_TYPE } from '~/pages/Playground/Configs';
import ReconnectingWebSocket from '~/pages/Playground/WebSocket';
import { Platform, StringExtension } from '~/plugins';
import { Button, Divider, Drawer, Empty, Form, Input, Radio, Select, Table, Tabs } from 'antd';
import { Document, Packer, Paragraph, TextRun } from 'docx';
import { saveAs } from 'file-saver';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { PureComponent } from 'react';

import RewriteDrawer from './RewriteDrawer';

const FLOW_MAP = {
  prod: '8bB6TeQnQ7181PCaR57uWY',
  stg: 'FLYkPxjETG6qgkF0dMSseZ',
};
export default class ArticleDrawer extends PureComponent {
  static propTypes = {
    article: PropTypes.object,
    material: PropTypes.object,
    open: PropTypes.bool,
    isArticleEdit: PropTypes.bool,
    isRerunning: PropTypes.bool,
    updateItem: PropTypes.func,
    initArticleTimer: PropTypes.func, // eslint-disable-line react/no-unused-prop-types
    clearTimerByArticleId: PropTypes.func,
    rerunOptimizeJob: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    activeTab: 'result', // 'result' 或 'original'
    rewriteDrawerOpen: false,
    asrBtnLoading: false, // 原稿提取按钮加载状态
    topicBtnLoading: false, // 话题生成按钮加载状态
    titleGenLoading: false, // 标题生成按钮加载状态
    topics: [],
    // 标题功能相关状态
    titleMode: 'llm', // 标题生成方式：'llm' 或 'manual'
    titleInput: '', // 用户输入的标题
    selectedTitle: '', // 用户选择的标题
    titleOptions: [ // 预设标题选项
    ],
  }

  componentDidMount = async () => {
    const { id } = this.props.material;
    try {
      const data = await Materials.fetchMaterialTopics({ materialId: id });
      this.setState({ topics: data.items || [] });
    } catch (error) {
      this.setState({ topics: [] });
    }
    // Component mounted
  }

  parseMaterialContent = (content) => {
    try {
      const parsedContent = JSON.parse(content);
      if (Array.isArray(parsedContent)) {
        const texts = parsedContent.map((item) => { return item.text; });
        return texts.join(' ');
      }
    } catch (error) {
      // JSON 解析失败，直接返回 content
    }
    return content;
  }

  // 处理转义字符，将 \n 转换为实际换行符
  formatContent = (content) => {
    if (!content) return '';
    return content
      .replace(/\\n/g, '\n') // 将 \n 转换为换行符
      .replace(/\\"/g, '"') // 将 \" 转换为引号
      .replace(/\\'/g, "'") // 将 \' 转换为单引号
      .replace(/\\\\/g, '\\'); // 将 \\ 转换为反斜杠
  }

  // WebSocket 异步封装方法
  sendWebSocketMessage = (content) => {
    return new Promise((resolve, reject) => {
      const flowId = FLOW_MAP[Platform.isProd() ? 'prod' : 'stg'];
      const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflow-v2/run/${flowId}`;
      const query = { access_token: Sessions.getToken() };

      let ws = null;

      const cleanup = () => {
        if (ws) {
          ws.close();
          ws = null;
        }
      };

      ws = new ReconnectingWebSocket(
        `${path}?${qs.stringify(query)}`,
        [],
        (e) => {
          // 消息接收处理
          if (e?.data !== 'pong') {
            try {
              const originData = JSON.parse(e.data);
              const originDataObj = StringExtension.snakeToCamelObj(originData);
              const { type, data } = originDataObj;

              if (type === EVENT_TYPE.FINAL_RESULT) {
                cleanup();
                resolve(data?.output);
              } else if (type === EVENT_TYPE.EXEC_FAILED) {
                cleanup();
                reject(new Error('工作流执行失败'));
              }
            } catch (error) {
              cleanup();
              reject(new Error('消息解析失败'));
            }
          }
        },
        () => {
          // 连接成功后发送消息
          try {
            ws.send(JSON.stringify({
              text: JSON.stringify({ text: content.trim() }),
              type: 'message',
              is_beta: false,
            }));
          } catch (error) {
            cleanup();
            reject(new Error('消息发送失败'));
          }
        },
      );
    });
  }

  // 获取当前标题
  getCurrentTitle = () => {
    const { article, material } = this.props;
    const { titleInput, selectedTitle, titleOptions } = this.state;

    // 优先级：用户输入的标题 > 用户选择的标题 > 原始标题
    if (titleInput) {
      return titleInput;
    }

    if (selectedTitle) {
      const selectedOption = titleOptions.find((opt) => { return opt.value === selectedTitle; });
      if (selectedOption) {
        return selectedOption.label;
      }
    }

    return article.title || material.title || '';
  }

  // 清理文件名中的非法字符
  sanitizeFileName = (fileName) => {
    return fileName
      .replace(/[<>:"/\\|?*]/g, '') // 移除Windows非法字符
      .replace(/\s+/g, '_') // 将空格替换为下划线
      .substring(0, 100); // 限制长度
  }

  // 下载Word文档
  onDownloadWord = async () => {
    const { article } = this.props;
    const title = this.getCurrentTitle();
    const content = this.formatContent(article.content);

    if (!title) {
      Toast.show('请先设置标题', Toast.Type.WARNING);
      return;
    }

    if (!content || content === '洗稿内容将在这里显示...') {
      Toast.show('暂无内容可下载', Toast.Type.WARNING);
      return;
    }

    try {
      // 创建Word文档
      const doc = new Document({
        sections: [
          {
            properties: {},
            children: [
              // 标题段落
              new Paragraph({
                children: [
                  new TextRun({
                    text: title,
                    bold: true,
                    size: 32, // 16pt
                  }),
                ],
                spacing: {
                  after: 400, // 段后间距
                },
              }),
              // 内容段落
              ...content.split('\n').map((line) => {
                return new Paragraph({
                  children: [
                    new TextRun({
                      text: line || ' ', // 空行用空格占位
                      size: 24, // 12pt
                    }),
                  ],
                  spacing: {
                    line: 360, // 行间距1.5倍
                  },
                });
              }),
            ],
          },
        ],
      });

      // 生成文档并下载
      const blob = await Packer.toBlob(doc);
      const fileName = this.sanitizeFileName(title) || '文章';
      saveAs(blob, `${fileName}.docx`);

      Toast.show('文档下载成功', Toast.Type.SUCCESS);
    } catch (error) {
      Toast.show('下载失败，请重试', Toast.Type.ERROR);
    }
  }

  // 复制洗稿内容到剪贴板
  onCopyContent = async () => {
    const { article } = this.props;
    const content = this.formatContent(article.content);

    if (!content || content === '洗稿内容将在这里显示...') {
      Toast.show('暂无内容可复制', Toast.Type.WARNING);
      return;
    }

    try {
      await navigator.clipboard.writeText(content);
      Toast.show('内容已复制到剪贴板', Toast.Type.SUCCESS);
    } catch (error) {
      // 降级方案：使用传统的复制方法
      const textArea = document.createElement('textarea');
      textArea.value = content;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        Toast.show('内容已复制到剪贴板', Toast.Type.SUCCESS);
      } catch (fallbackError) {
        Toast.show('复制失败，请手动复制', Toast.Type.ERROR);
      }
      document.body.removeChild(textArea);
    }
  }

  onTabChange = (activeKey) => {
    this.setState({ activeTab: activeKey });
  }

  onReGenerateArticle = async () => {
    const { title, sourceUrl, sourceId, collectCount, commentCount, diggCount, shareCount } = this.props.material;
    const rawMaterial = {
      title, music: sourceUrl, statistics: { collectCount, commentCount, diggCount, shareCount },
    };
    await this.props.rerunOptimizeJob({ sourceId, rawMaterial });
    this.props.onClose();
    Toast.show('重新创作任务已提交，请稍后查看结果', Toast.Type.SUCCESS);
  }

  // 打开重新创作Drawer
  onOpenRewriteDrawer = () => {
    this.setState({ rewriteDrawerOpen: true });
  }

  // 关闭重新创作Drawer
  onCloseRewriteDrawer = () => {
    this.setState({ rewriteDrawerOpen: false });
  }

  // 执行重新创作
  onRewrite = async (rewriteParams) => {
    const { material } = this.props;

    // 使用现有的rerunOptimizeJob方法
    const { title, sourceUrl, sourceId, collectCount, commentCount, diggCount, shareCount } = material;
    const rawMaterial = {
      title,
      music: sourceUrl,
      statistics: { collectCount, commentCount, diggCount, shareCount },
      workflowUuid: rewriteParams.workflowUuid, // 传递选择的工作流
    };

    await this.props.rerunOptimizeJob({ sourceId, rawMaterial });
    this.props.onClose();
  }
  onGenAsrMaterial = async () => {
    await this.setState({ asrBtnLoading: true });
    await Materials.genAsrContent({ materialId: this.props.material.id });
    Toast.show('原稿提取任务已提交，请稍后查看结果', Toast.Type.SUCCESS);
  }

  onGenTopics = async () => {
    await this.setState({ topicBtnLoading: true });
    try {
      await Materials.genMaterialTopics(this.props.material.id);
      Toast.show('话题生成任务已提交，请稍后查看结果', Toast.Type.SUCCESS);
      // 重新获取话题数据
      setTimeout(async () => {
        try {
          const data = await Materials.fetchMaterialTopics({ materialId: this.props.material.id });
          this.setState({ topics: data.items || [] });
        } catch (error) {
          // 获取话题数据失败，保持当前状态
        }
      }, 2000);
    } catch (error) {
      Toast.show('话题生成失败，请重试', Toast.Type.ERROR);
    }
    await this.setState({ topicBtnLoading: false });
  }

  // 处理标题输入变更
  onTitleInputChange = (e) => {
    const { value } = e.target;
    // 限制标题长度不超过100字符
    if (value.length <= 100) {
      this.setState({ titleInput: value });
    }
  }

  // 处理标题选择变更
  onTitleSelectChange = (value) => {
    this.setState({ selectedTitle: value });
  }

  // 处理标题模式变更
  onTitleModeChange = (e) => {
    const { value } = e.target;
    this.setState({
      titleMode: value,
      // 切换模式时清空相关状态
      titleInput: '',
      selectedTitle: '',
    });
  }

  onGenTitle = async () => {
    const { content } = this.props.article;
    if (!content || content.trim() === '') {
      Toast.show('请先生成文章内容', Toast.Type.WARNING);
      return;
    }

    this.setState({ titleGenLoading: true });

    try {
      const result = await this.sendWebSocketMessage(content);

      if (result) {
        try {
          // 尝试解析结果，假设返回的是标题数组
          const { output } = JSON.parse(result);
          const titles = output.split('\n').filter((x) => { return x.length; }).slice(0, 5).map((title, index) => {
            return {
              value: `title_${index}`,
              label: title.trim(),
            };
          });
          this.setState({ titleOptions: titles });
          Toast.show('标题生成成功', Toast.Type.SUCCESS);
        } catch (parseError) {
          // 解析失败，直接作为字符串处理
          const titleOptions = [{
            value: 'title_0',
            label: result,
          }];
          this.setState({ titleOptions });
          Toast.show('标题生成成功', Toast.Type.SUCCESS);
        }
      } else {
        Toast.show('标题生成失败，请重试', Toast.Type.ERROR);
      }
    } catch (error) {
      Toast.show(error.message || '标题生成失败，请重试', Toast.Type.ERROR);
    } finally {
      this.setState({ titleGenLoading: false });
    }
  }

  onSave = async () => {
    const { article, material, isArticleEdit } = this.props;
    if (isArticleEdit) {
      await this.props.updateItem('article', {
        id: article.id,
        title: article.title,
        content: article.content,
        character_id: article.characterId,
        is_draft: article.isDraft,
        video_url: article.videoUrl,
        audio_url: article.audioUrl,
        tags: article.tags,
        publish_scheduled_at: article.publishScheduledAt,
      });
      if (material?.id) {
        await this.props.updateItem('material', {
          id: material.id,
          content: material.content,
          passed: material.passed,
        });
      }
    }
    this.props.onClose();
  }

  renderTopicContent = () => {
    const { topics } = this.state;

    if (!topics || topics.length === 0) {
      return (
        <div className="topic-empty-state">
          暂无话题数据，请点击&quot;提取话题&quot;按钮
        </div>
      );
    }

    return (
      <div className="topic-content-wrapper">
        {topics.map((topic, index) => { return this.renderTopicItem(topic, index); })}
      </div>
    );
  }

  renderTopicItem = (topic, index) => {
    return (
      <div key={index} className="topic-item">
        {/* 话题标题 */}
        <div className="topic-title">
          {topic.name || '未命名话题'}
        </div>

        {/* 受众群体 */}
        {topic.audience && topic.audience.length > 0 && (
          <div className="topic-field">
            <span className="field-label">受众群体：</span>
            {topic.audience.map((aud) => {
              return (<span className="audience-tag">{aud}</span>);
            })}
          </div>
        )}

        {/* 核心观点 */}
        {topic.coreViewpoint && (
          <div className="topic-field">
            <span className="field-label">核心观点：</span>
            <span className="field-content">{topic.coreViewpoint}</span>
          </div>
        )}

        {/* 逻辑链条 */}
        {topic.logic && (
          <div className="topic-field">
            <span className="field-label">逻辑链条：</span>
            <span className="field-content">{topic.logic}</span>
          </div>
        )}

        {/* 问答对 */}
        {topic.questionsAndAnswers && topic.questionsAndAnswers.length > 0 && (
          <div className="topic-field">
            <div className="field-title">问答列表：</div>
            {topic.questionsAndAnswers.map((qa) => {
              return (
                <div className="qa-item">
                  <div className="qa-question">Q: {qa.question}</div>
                  <div className="qa-answer">A: {qa.answer}</div>
                </div>
              );
            })}
          </div>
        )}

        {/* 关键陈述 */}
        {topic.keyStatements && topic.keyStatements.length > 0 && (
          <div className="topic-field">
            <div className="field-title">关键陈述：</div>
            {topic.keyStatements.map((statement) => {
              return (
                <div className="key-statement" >
                  &quot;{statement}&quot;
                </div>
              );
            })}
          </div>
        )}

        {/* 相关事实 */}
        {topic.relatedFacts && topic.relatedFacts.length > 0 && (
          <div className="topic-field">
            <div className="field-title">相关事实：</div>
            <ul className="related-facts-list">
              {topic.relatedFacts.map((fact) => {
                return (<li className="fact-item">{fact}</li>);
              })}
            </ul>
          </div>
        )}
      </div>
    );
  }

  render = () => {
    const { open, article, material, isRerunning } = this.props;
    const { rewriteDrawerOpen } = this.state;
    return (
      <>
        <Drawer
          width="66vw"
          open={open}
          title="改稿结果"
          maskClosable={false}
          className="article-drawer"
          onClose={() => {
            this.props.onClose();
            this.props.clearTimerByArticleId(article.id);
          }}
          extra={<Button type="primary" onClick={this.onSave}>保存</Button>}
        >
          <Form labelCol={{ span: 2 }} className="article-drawer-form">
            <Form.Item label="标题：">{article.title || material.title}</Form.Item>
            <Form.Item label="生成时间：">{article.createdAt}</Form.Item>

            <Form.Item label="文稿源：">
              <a
                target="_blank"
                href={material.sourceUrl}
                rel="noreferrer"
                className="article-source-link"
              >
                {material.sourceUrl}
              </a>
            </Form.Item>

            <Form.Item label="正文：">
              <Tabs
                activeKey={this.state.activeTab}
                onChange={this.onTabChange}
                items={[
                  {
                    key: 'result',
                    label: '改稿结果',
                    children: (
                      <div>
                        <div style={{ marginBottom: '8px', textAlign: 'right' }}>
                          <Button
                            type="primary"
                            size="small"
                            loading={isRerunning}
                            onClick={this.onOpenRewriteDrawer}
                          >
                            重新改稿
                          </Button>
                          <Divider type="vertical" />
                          <Button
                            type="default"
                            size="small"
                            icon={<CopyOutlined />}
                            onClick={this.onCopyContent}
                          >
                            复制内容
                          </Button>
                          {this.getCurrentTitle() && (
                            <>
                              <Divider type="vertical" />
                              <Button
                                type="default"
                                size="small"
                                icon={<DownloadOutlined />}
                                onClick={this.onDownloadWord}
                              >
                                下载Word
                              </Button>
                            </>
                          )}
                        </div>
                        <div style={{
                          whiteSpace: 'pre-wrap',
                          overflow: 'auto',
                          lineHeight: '1.6',
                          wordBreak: 'break-word',
                          border: '1px solid #d9d9d9',
                          borderRadius: '6px',
                          padding: '12px',
                          backgroundColor: '#fafafa',
                        }}
                        >
                          {this.formatContent(article.content) || '无'}
                        </div>
                        <Divider style={{ margin: '16px 0' }} />

                        {article.content &&
                          <div style={{ marginTop: '16px' }}>
                            <div style={{ marginBottom: '12px' }}>
                              <strong>标题生成：</strong>
                            </div>
                            <Radio.Group
                              value={this.state.titleMode}
                              onChange={this.onTitleModeChange}
                              style={{ marginBottom: '16px' }}
                            >
                              <Radio value="llm">LLM生成</Radio>
                              <Radio value="manual">手动输入</Radio>
                            </Radio.Group>

                            {/* 根据选择的模式显示对应的输入组件 */}
                            {this.state.titleMode === 'llm' && (
                              <div>
                                <div style={{ marginBottom: '8px' }}>
                                  <strong>标题选择：</strong>
                                  <Button
                                    type="primary"
                                    size="small"
                                    loading={this.state.titleGenLoading}
                                    onClick={this.onGenTitle}
                                  >
                                    生成
                                  </Button>
                                </div>
                                <Select
                                  placeholder="请选择预设标题"
                                  value={this.state.selectedTitle}
                                  onChange={this.onTitleSelectChange}
                                  style={{ width: '100%' }}
                                  allowClear
                                >
                                  {this.state.titleOptions.map((option) => {
                                    return (
                                      <Select.Option key={option.value} value={option.value}>
                                        {option.label}
                                      </Select.Option>
                                    );
                                  })}
                                </Select>
                              </div>
                            )}

                            {this.state.titleMode === 'manual' && (
                              <div>
                                <div style={{ marginBottom: '8px' }}>
                                  <strong>标题输入：</strong>
                                </div>
                                <Input
                                  placeholder="请输入自定义标题（最多100字符）"
                                  value={this.state.titleInput}
                                  onChange={this.onTitleInputChange}
                                  maxLength={100}
                                  showCount
                                />
                              </div>
                            )}

                            {/* 显示当前选择的标题 */}
                            {(this.state.titleInput || this.state.selectedTitle) && (
                              <div style={{
                                marginTop: '12px',
                                padding: '8px',
                                backgroundColor: '#f6f6f6',
                                borderRadius: '4px',
                                border: '1px solid #d9d9d9',
                              }}
                              >
                                <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>
                                  当前标题：
                                  <div style={{ fontWeight: 'bold' }}>
                                    {this.state.titleInput ||
                                      this.state.titleOptions.find((opt) => {
                                        return opt.value === this.state.selectedTitle;
                                      })?.label ||
                                      ''}
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        }
                      </div>
                    ),
                  },
                  {
                    key: 'original',
                    label: '原稿',
                    children: (
                      <div className="tab-content">
                        {material.content?.length ?
                          <div style={{
                            whiteSpace: 'pre-wrap',
                            overflow: 'auto',
                            lineHeight: '1.6',
                            wordBreak: 'break-word',
                            padding: '8px 0',
                          }}
                          >
                            {this.formatContent(this.parseMaterialContent(material.content))}
                          </div> :
                          <Button
                            onClick={() => { return this.onGenAsrMaterial(); }}
                            type="primary"
                            loading={this.state.asrBtnLoading}
                          >提取原稿
                          </Button>
                        }
                      </div>
                    ),
                  },
                  {
                    key: 'sourceVerification',
                    label: '事实校验',
                    children: (
                      <div>
                        {article?.sourceVerification && article.sourceVerification.length > 0 ? (
                          <Table
                            dataSource={article.sourceVerification}
                            pagination={false}
                            rowKey={(record) => { return record.sentence; }}
                            columns={[
                              {
                                title: '引用数据',
                                dataIndex: 'sentence',
                                key: 'sentence',
                                width: '30%',
                              },
                              {
                                title: '结论',
                                dataIndex: 'conclusion',
                                key: 'conclusion',
                                width: '10%',
                              },
                              {
                                title: '参考来源',
                                dataIndex: 'references',
                                key: 'references',
                                render: (references) => {
                                  if (!references || references.length === 0) {
                                    return <div style={{ color: '#999' }}>无参考来源</div>;
                                  }

                                  return (
                                    <div>
                                      {references.map((ref, index) => {
                                        return (
                                          <div
                                            style={{
                                              marginBottom: index < references.length - 1 ? 8 : 0,
                                              borderBottom: index < references.length - 1 ? '1px solid #f0f0f0' : '0',
                                            }}
                                          >
                                            {ref.content}{' '}
                                            {ref.url && (
                                              <a
                                                href={ref.url}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                style={{ color: '#1890ff' }}
                                              >
                                                (链接)
                                              </a>
                                            )}
                                          </div>
                                        );
                                      })}
                                    </div>
                                  );
                                },
                              },
                            ]}
                            className="source-verification-table"
                          />
                        ) : (
                          <Empty description="暂无数据" />
                        )}
                      </div>
                    ),
                  },
                  {
                    key: 'topic',
                    label: '话题',
                    children: (
                      <div className="topic-section">
                        <div className="topic-generate-btn-wrapper">
                          <Button
                            onClick={this.onGenTopics}
                            type="primary"
                            size="small"
                            loading={this.state.topicBtnLoading}
                          >
                            提取话题
                          </Button>
                        </div>
                        {this.renderTopicContent()}
                      </div>
                    ),
                  },
                ]}
              />
            </Form.Item>
          </Form>
        </Drawer>

        {/* 二级Drawer - 重新创作 */}
        <RewriteDrawer
          open={rewriteDrawerOpen}
          article={article}
          material={material}
          onClose={this.onCloseRewriteDrawer}
          onRewrite={this.onRewrite}
        />
      </>
    );
  }
}
