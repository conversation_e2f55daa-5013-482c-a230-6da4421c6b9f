import './RewriteDrawer.less';

import { Toast } from '~/components';
import Consts from '~/consts';
import { ChatBot, Materials } from '~/engine';
import { Platform } from '~/plugins';
import { Button, Drawer, Form, InputNumber, Mentions, Select, Spin, Switch, Tabs } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

// 工作流参数提及选项
const MENTIONS_OPTIONS = [
  { value: '{title}}', label: '标题' },
  { value: '{content}}', label: '内容' },
  { value: '{collect_count}}', label: '收藏数' },
  { value: '{comment_count}}', label: '评论数' },
  { value: '{digg_count}}', label: '点赞数' },
  { value: '{share_count}}', label: '分享数' },
  { value: '{source_url}}', label: '来源链接' },
];

// 生成默认值
const generateDefaultValue = (type) => {
  switch (type) {
    case 'str':
      return '';
    case 'bool':
      return false;
    case 'int':
      return 0;
    default:
      return '';
  }
};

export default class RewriteDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    article: PropTypes.object,
    material: PropTypes.object,
    onClose: PropTypes.func,
  }

  state = {
    loading: false,
    workflows: [],
    selectedWorkflow: null,
    selectedWorkflows: [], // 方式二：多选工作流
    workflowParams: [], // 工作流参数
    workflowConfig: {}, // 工作流配置值
    submitting: false,
    activeTabKey: '1', // 当前活跃的Tab
  }

  componentDidMount = async () => {
    if (this.props.open) {
      await this.fetchWorkflows();
    }
  }

  componentDidUpdate = async (prevProps) => {
    if (!prevProps.open && this.props.open) {
      await this.fetchWorkflows();
    }
  }

  fetchWorkflows = async () => {
    this.setState({ loading: true });
    try {
      // 参考GroupTabPane的实现，从OSS CDN获取工作流配置
      const timestamp = new Date().getTime();
      const resp = await fetch(`${Consts.OSS_CDN_DOMAIN}/llmbot/plugin/configs/aivideo.json?t=${timestamp}`);
      const data = await resp.json();
      const env = Platform.isProd() ? 'production' : 'staging';
      const workflows = data?.[env]?.material_flows || [];

      this.setState({
        workflows,
        selectedWorkflow: null,
        selectedWorkflows: [],
        workflowParams: [],
        workflowConfig: {},
      });
    } catch (error) {
      Toast.show('获取改稿方案失败', Toast.Type.ERROR);
    } finally {
      this.setState({ loading: false });
    }
  }
  // 获取工作流参数配置的通用方法
  fetchWorkflowParams = async (workflowUuid, selectedWorkflow) => {
    try {
      const workflow = await ChatBot.getChatbotWorkflow(workflowUuid);
      const { var_defaults } = selectedWorkflow || {}; // eslint-disable-line
      const content = JSON.parse(workflow.content);
      const inputSchema = content.nodes[0].data.input_schema;
      const workflowParams = [];
      Object.entries(inputSchema).forEach(
        ([key, value]) => {
          workflowParams.push({
            key,
            description: value.description || '',
            default: value.default || '',
            type: value.type || 'str',
          });
        },
      );

      // 设置默认值，参考GroupTabPane的实现
      const stateParams = { workflowParams };
      if (!_.isEmpty(var_defaults)) {
        const defaults = {};
        Object.entries(var_defaults).forEach(([key, value]) => {
          defaults[key] = value;
        });
        stateParams.workflowConfig = { ...this.state.workflowConfig, ...defaults };
      }

      this.setState(stateParams);
    } catch (error) {
      this.setState({ workflowParams: [] });
    }
  }

  onWorkflowChange = async (workflowUuid) => {
    const selectedWorkflow = this.state.workflows.find((w) => { return w.workflow_uuid === workflowUuid; });
    this.setState({ selectedWorkflow });

    // 使用通用方法获取工作流参数配置
    await this.fetchWorkflowParams(workflowUuid, selectedWorkflow);
  }

  // 处理工作流参数值变化
  onWorkflowParamChange = (value, paramKey) => {
    this.setState({
      workflowConfig: {
        ...this.state.workflowConfig,
        [paramKey]: value,
      },
    });
  }

  // 处理多选工作流变化（方式二）
  onMultipleWorkflowChange = async (workflowUuids) => {
    const selectedWorkflows = this.state.workflows.filter((w) => {
      return workflowUuids.includes(w.workflow_uuid);
    });
    this.setState({ selectedWorkflows });

    // 如果有选中的工作流，获取第一个工作流的参数配置
    if (selectedWorkflows.length > 0) {
      const firstWorkflow = selectedWorkflows[0];
      await this.fetchWorkflowParams(firstWorkflow.workflow_uuid, firstWorkflow);
    } else {
      // 如果没有选中的工作流，清空参数配置
      this.setState({
        workflowParams: [],
        workflowConfig: {},
      });
    }
  }

  // 处理Tab切换
  onTabChange = (activeKey) => {
    this.setState({ activeTabKey: activeKey });
  }


  onSubmit = async () => {
    const { activeTabKey, selectedWorkflow, selectedWorkflows, workflowParams, workflowConfig } = this.state;

    // 根据不同Tab进行不同的验证
    if (activeTabKey === '1') {
      // 方式一：单选工作流验证
      if (!selectedWorkflow) {
        Toast.show('请选择改稿方案', Toast.Type.WARNING);
        return;
      }
    } else if (activeTabKey === '2') {
      // 方式二：多选工作流验证
      if (selectedWorkflows.length === 0) {
        Toast.show('请至少选择一个改稿方案', Toast.Type.WARNING);
        return;
      }
    }

    this.setState({ submitting: true });
    try {
      const finalWorkflowConfig = {};
      workflowParams.forEach((param) => {
        if (workflowConfig[param.key] !== undefined) {
          finalWorkflowConfig[param.key] = workflowConfig[param.key];
        } else {
          finalWorkflowConfig[param.key] = generateDefaultValue(param.type);
        }
      });

      let workflowSettings = [];
      if (activeTabKey === '1') {
        workflowSettings = [{
          workflow_uuid: selectedWorkflow.workflow_uuid,
          workflow_config: finalWorkflowConfig,
        }];
      } else if (activeTabKey === '2') {
        // 为每个选中的工作流创建配置
        workflowSettings = selectedWorkflows.map((workflow) => {
          return {
            workflow_uuid: workflow.workflow_uuid,
            workflow_config: finalWorkflowConfig, // 使用相同的参数配置
          };
        });
      }
      const rewriteParams = {
        materialId: this.props.material.id,
        optimize_config: {
          workflow_settings: workflowSettings, // 注意这里是复数形式
          count: workflowConfig.count || 1,
        },
      };

      await Materials.genCharacterMaterial(rewriteParams);

      Toast.show('重新创作任务已提交，请稍后查看结果', Toast.Type.SUCCESS);
      this.props.onClose();
    } catch (error) {
      Toast.show('提交失败，请重试', Toast.Type.ERROR);
    } finally {
      this.setState({ submitting: false });
    }
  }

  onCancel = () => {
    this.setState({
      selectedWorkflow: null,
      selectedWorkflows: [],
      workflowParams: [],
      workflowConfig: {},
      submitting: false,
      activeTabKey: '1', // 重置为默认Tab
    });
    this.props.onClose();
  }

  // 渲染工作流选择和参数配置
  renderWorkflowConfiguration = () => {
    const { workflows, selectedWorkflow, workflowParams, workflowConfig } = this.state;

    return (
      <>
        <Form.Item
          label="改稿方案"
          required
          help="选择用于重新创作的工作流方案"
        >
          <Select
            placeholder="请选择改稿方案"
            value={selectedWorkflow?.workflow_uuid}
            onChange={this.onWorkflowChange}
            style={{ width: '100%' }}
            showSearch
            filterOption={(input, option) => { return option.children.toLowerCase().includes(input.toLowerCase()); }
            }
          >
            {workflows.map((workflow) => {
              return (
                <Select.Option key={workflow.workflow_uuid} value={workflow.workflow_uuid}>
                  {workflow.name}
                </Select.Option>
              );
            })}
          </Select>
        </Form.Item>

        {/* 工作流参数配置 */}
        {workflowParams.length > 0 && (
          <Form.Item label="参数配置">
            <div className="workflow-params-container">
              {workflowParams.map((param) => {
                let paramInput = null;
                switch (param.type) {
                  case 'bool':
                    paramInput = (
                      <Switch
                        style={{ width: 'auto' }}
                        checked={workflowConfig[param.key] || param.default}
                        onChange={(value) => { return this.onWorkflowParamChange(value, param.key); }}
                      />
                    );
                    break;
                  case 'int':
                    paramInput = (
                      <InputNumber
                        value={workflowConfig[param.key] || param.default}
                        onChange={(value) => { return this.onWorkflowParamChange(value, param.key); }}
                        style={{ width: 'auto' }}
                      />
                    );
                    break;
                  default:
                    paramInput = (
                      <Mentions
                        prefix="{"
                        autoSize={{ minRows: 1 }}
                        value={workflowConfig[param.key] || param.default}
                        onChange={(value) => { return this.onWorkflowParamChange(value, param.key); }}
                        options={MENTIONS_OPTIONS}
                        placeholder="支持{{变量}}格式"
                        style={{ width: '100%' }}
                      />
                    );
                    break;
                }

                return (
                  <div key={param.key} className="param-item">
                    <div className="param-label">
                      <strong>{param.description || param.key}：</strong>
                    </div>
                    <div className="param-input">
                      {paramInput}
                    </div>
                  </div>
                );
              })}
            </div>
          </Form.Item>
        )}
      </>
    );
  }

  // 渲染方式二的多选工作流配置
  renderMultipleWorkflowConfiguration = () => {
    const { workflows, selectedWorkflows, workflowParams, workflowConfig } = this.state;

    return (
      <>
        <Form.Item
          label="改稿方案"
          required
          help="可选择多个工作流方案进行重新创作"
        >
          <Select
            mode="multiple"
            placeholder="请选择改稿方案（可多选）"
            value={selectedWorkflows.map((w) => { return w.workflow_uuid; })}
            onChange={this.onMultipleWorkflowChange}
            style={{ width: '100%' }}
            showSearch
            filterOption={(input, option) => { return option.children.toLowerCase().includes(input.toLowerCase()); }}
          >
            {workflows.map((workflow) => {
              return (
                <Select.Option key={workflow.workflow_uuid} value={workflow.workflow_uuid}>
                  {workflow.name}
                </Select.Option>
              );
            })}
          </Select>
        </Form.Item>

        {/* 显示已选择的工作流 */}
        {selectedWorkflows.length > 0 && (
          <Form.Item label="已选择的方案">
            <div className="selected-workflows-container">
              {selectedWorkflows.map((workflow, index) => {
                return (
                  <div key={workflow.workflow_uuid} className="selected-workflow-item">
                    <strong>{workflow.name}</strong>
                    {index === 0 && <span className="primary-workflow-tag">（主要配置）</span>}
                  </div>
                );
              })}
            </div>
          </Form.Item>
        )}

        {/* 显示第一个工作流的参数配置 */}
        {selectedWorkflows.length > 0 && workflowParams.length > 0 && (
          <Form.Item label={`参数配置（${selectedWorkflows[0].name}）`}>
            <div className="workflow-params-container">
              {workflowParams.map((param) => {
                let paramInput = null;
                switch (param.type) {
                  case 'bool':
                    paramInput = (
                      <Switch
                        style={{ width: 'auto' }}
                        checked={workflowConfig[param.key] || param.default}
                        onChange={(value) => { return this.onWorkflowParamChange(value, param.key); }}
                      />
                    );
                    break;
                  case 'int':
                    paramInput = (
                      <InputNumber
                        value={workflowConfig[param.key] || param.default}
                        onChange={(value) => { return this.onWorkflowParamChange(value, param.key); }}
                        style={{ width: 'auto' }}
                      />
                    );
                    break;
                  default:
                    paramInput = (
                      <Mentions
                        prefix="{"
                        autoSize={{ minRows: 1 }}
                        value={workflowConfig[param.key] || param.default}
                        onChange={(value) => { return this.onWorkflowParamChange(value, param.key); }}
                        options={MENTIONS_OPTIONS}
                        placeholder="支持{{变量}}格式"
                        style={{ width: '100%' }}
                      />
                    );
                    break;
                }

                return (
                  <div key={param.key} className="param-item">
                    <div className="param-label">
                      <strong>{param.description || param.key}：</strong>
                    </div>
                    <div className="param-input">
                      {paramInput}
                    </div>
                  </div>
                );
              })}
            </div>
          </Form.Item>
        )}
      </>
    );
  }

  render() {
    const { open, article, material } = this.props;
    const { loading, selectedWorkflow, selectedWorkflows, submitting } = this.state;

    return (
      <Drawer
        title="选择改稿方案"
        open={open}
        width="45vw"
        onClose={this.onCancel}
        maskClosable={false}
        className="rewrite-drawer"
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button onClick={this.onCancel} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button
              type="primary"
              onClick={this.onSubmit}
              loading={submitting}
              disabled={!selectedWorkflow && selectedWorkflows.length === 0}
            >
              开始重新创作
            </Button>
          </div>
        }
      >
        <Spin spinning={loading}>
          <Form layout="vertical">
            <Form.Item label="文章信息">
              <div className="workflow-info-card">
                <div className="info-item">
                  <strong>标题：</strong>{article?.title || material?.title}
                </div>
                <div className="info-item">
                  <strong>来源：</strong>
                  <a
                    href={material?.sourceUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{ color: '#1890ff' }}
                  >
                    {material?.sourceUrl}
                  </a>
                </div>
              </div>
            </Form.Item>

            {/* 使用Tabs组织工作流配置 */}
            <Tabs
              defaultActiveKey="1"
              type="card"
              onChange={this.onTabChange}
            >
              <Tabs.TabPane tab="方式一" key="1">
                <Form layout="vertical">
                  {this.renderWorkflowConfiguration()}

                  <Form.Item label="改稿条数" required>
                    <InputNumber
                      addonBefore="生成"
                      addonAfter="条"
                      min={1}
                      max={10}
                      defaultValue={1}
                      onChange={(value) => { return this.onWorkflowParamChange(value, 'count'); }}
                    />
                  </Form.Item>
                </Form>
              </Tabs.TabPane>

              <Tabs.TabPane tab="方式二" key="2">
                <Form layout="vertical">
                  {this.renderMultipleWorkflowConfiguration()}
                </Form>
              </Tabs.TabPane>
            </Tabs>
          </Form>
        </Spin>
      </Drawer>
    );
  }
}
