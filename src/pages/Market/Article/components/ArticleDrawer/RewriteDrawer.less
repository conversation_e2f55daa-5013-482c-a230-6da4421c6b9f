// RewriteDrawer 样式文件
.rewrite-drawer {
  .ant-drawer-header {
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-drawer-body {
    padding: 24px;
  }

  .workflow-info-card {
    margin-bottom: 16px;
    padding: 12px;
    background: #f5f5f5;
    border-radius: 6px;

    .info-item {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .workflow-detail-card {
    padding: 12px;
    border: 1px solid #d9d9d9;
    background: #f9f9f9;
    border-radius: 6px;

    .detail-item {
      margin-top: 8px;

      &:first-child {
        margin-top: 0;
      }
    }
  }

  .warning-tip {
    padding: 12px;
    border: 1px solid #ffd591;
    background: #fff7e6;
    color: #d46b08;
    border-radius: 6px;
  }

  .ant-select {
    .ant-select-selector {
      border-radius: 6px;
    }
  }

  .ant-form-item-label > label {
    font-weight: 500;
  }

  .workflow-params-container {
    .param-item {
      margin-bottom: 16px;
      padding: 12px;
      border: 1px solid #f0f0f0;
      background: #fafafa;
      border-radius: 6px;

      &:last-child {
        margin-bottom: 0;
      }

      .param-label {
        margin-bottom: 8px;
        font-size: 14px;
        color: #333;
      }

      .param-input {
        .ant-mentions,
        .ant-input-number,
        .ant-switch {
          width: 100%;
        }
      }
    }
  }

  .selected-workflows-container {
    .selected-workflow-item {
      margin-bottom: 8px;
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      background: #f0f8ff;
      color: #1890ff;
      border-radius: 4px;

      &:last-child {
        margin-bottom: 0;
      }

      .primary-workflow-tag {
        margin-left: 8px;
        font-size: 12px;
        font-weight: normal;
        color: #52c41a;
      }
    }
  }
}
