.article-drawer {
  .article-drawer-form {
    .ant-form-item {
      margin-bottom: 20px;
    }

    .ant-form-item-label {
      text-align: left;
      font-weight: 500;
      color: #333;
    }

    .preview-txt {
      overflow: auto;
      max-block-size: 640px;
      white-space: pre-wrap;
    }

    // 改稿结果页面特定样式
    .article-result-content {
      border: 1px solid #d9d9d9;
      background-color: #fafafa;
      border-radius: 6px;

      .ant-tabs {
        .ant-tabs-tab {
          font-weight: 500;

          &.ant-tabs-tab-active {
            .ant-tabs-tab-btn {
              font-weight: 600;
              color: #1890ff;
            }
          }
        }

        .ant-tabs-content-holder {
          .ant-tabs-content {
            .ant-tabs-tabpane {
              padding: 0;
            }
          }
        }
      }

      .tab-content {
        min-height: 400px;
        padding: 16px;
        font-size: 14px;
        line-height: 1.6;
        color: #333;

        .ant-input {
          padding: 0;
          border: none;
          font-size: 14px;
          line-height: 1.6;
          color: inherit;
          background-color: transparent;
          box-shadow: none;
          resize: none;

          &:focus {
            border: none;
            box-shadow: none;
          }

          &::placeholder {
            color: #bfbfbf;
          }
        }
      }
    }

    .article-buttons {
      margin-bottom: 16px;

      .ant-btn {
        font-weight: 500;
        border-radius: 4px;
      }
    }

    .article-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 1.4;
      color: #333;
    }

    .article-time {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 14px;
      color: #666;
    }

    .article-source-link {
      color: #1890ff;
      text-decoration: none;
      word-break: break-all;

      &:hover {
        color: #40a9ff;
        text-decoration: underline;
      }
    }

    // 话题相关样式
    .topic-section {
      .topic-generate-btn-wrapper {
        margin-bottom: 16px;
      }

      .topic-empty-state {
        padding: 40px 0;
        border: 1px dashed #d9d9d9;
        text-align: center;
        color: #999;
        border-radius: 6px;
      }

      .topic-content-wrapper {
        max-height: 75vh;
        overflow: auto;
      }

      .topic-item {
        margin-bottom: 16px;
        padding: 16px;
        border: 1px solid #e8e8e8;
        background-color: #fafafa;
        border-radius: 8px;

        .topic-title {
          margin-bottom: 12px;
          font-size: 16px;
          font-weight: bold;
          color: #1890ff;
        }

        .topic-field {
          margin-bottom: 8px;

          .field-label {
            font-weight: bold;
            color: #666;
          }

          .field-content {
            margin-left: 8px;
          }

          .field-title {
            margin-bottom: 4px;
            font-weight: bold;
            color: #666;
          }

          // 受众群体标签
          .audience-tag {
            display: inline-block;
            margin-left: 4px;
            padding: 2px 8px;
            background: #e6f7ff;
            font-size: 12px;
            color: #1890ff;
            border-radius: 4px;
          }

          // 问答对卡片
          .qa-item {
            margin-bottom: 4px;
            margin-left: 16px;
            padding: 8px;
            border: 1px solid #f0f0f0;
            background-color: #fff;
            border-radius: 4px;

            .qa-question {
              font-weight: bold;
              color: #1890ff;
            }

            .qa-answer {
              margin-top: 4px;
              color: #666;
            }
          }

          // 关键陈述
          .key-statement {
            margin-bottom: 4px;
            margin-left: 16px;
            padding: 6px 12px;
            border-left: 3px solid #faad14;
            font-style: italic;
            background-color: #fff7e6;
          }

          // 相关事实列表
          .related-facts-list {
            margin-bottom: 0;
            margin-left: 16px;

            .fact-item {
              margin-bottom: 2px;
              color: #666;
            }
          }
        }
      }
    }
  }
}
