import { InboxOutlined, InfoCircleOutlined, DownloadOutlined, EyeOutlined } from '@ant-design/icons';
import { FilterBar, PaginationTable } from '~/components';
import Market from '~/engine/Market';
import { But<PERSON>, Divider, Popconfirm, Select, message as Toast, Popover, Modal } from 'antd';

import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, Table as DocxTable, TableRow, TableCell, WidthType } from 'docx';
import { saveAs } from 'file-saver';
import { marked } from 'marked';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

import ResearchReportDrawer from './ResearchReportDrawer';

export default class ResearchReportTabPane extends Component {
  static propTypes = {
    fetchItems: PropTypes.func.isRequired,
    createItem: PropTypes.func.isRequired,
    updateItem: PropTypes.func.isRequired,
    deleteItem: PropTypes.func.isRequired,
    setState: PropTypes.func.isRequired,
  }

  state = {
    reportDrawerOpen: false,
    selectedReport: null,
    reportKeywords: '',
    reportCategory: undefined, // 添加分类过滤
    reportStatus: undefined, // 添加状态过滤
    reports: [],
    reportsTotal: 0,
    reportsPagination: { pageIndex: 1, pageSize: 10 },
    loading: false,
    categories: [], // 存储所有分类
    contentPreviewVisible: false, // 内容预览模态框是否可见
    contentPreviewData: null, // 当前预览的内容数据
  }

  componentDidMount() {
    this.fetchReports();
  }

  fetchReports = async (params = {}) => {
    try {
      this.setState({ loading: true });

      const { reportsPagination, reportKeywords, reportCategory, reportStatus } = this.state;
      const { pageIndex, pageSize } = reportsPagination || { pageIndex: 1, pageSize: 10 };

      const requestParams = {
        pagination: {
          skip: (pageIndex - 1) * pageSize,
          limit: pageSize,
        },
        keywords: reportKeywords,
        category: reportCategory,
        status: reportStatus,
        ...params,
      };

      const response = await Market.fetchResearchReports(requestParams);

      if (response && response.items) {
        // 提取所有唯一的分类
        const categories = [...new Set(response.items.map(item => item.category).filter(Boolean))];

        this.setState({
          reports: response.items,
          reportsTotal: response.total || response.items.length,
          categories: categories,
        });
      }
    } catch (error) {
      Toast.error('获取研报列表失败');
      console.error('获取研报列表失败:', error);
    } finally {
      this.setState({ loading: false });
    }
  }

  onOpenReportDrawer = (report = null) => {
    this.setState({
      reportDrawerOpen: true,
      selectedReport: report,
    });
  }

  onCloseReportDrawer = () => {
    this.setState({
      reportDrawerOpen: false,
      selectedReport: null,
    });
  }

  onSubmitReport = async (values) => {
    try {
      if (values.id) {
        // 更新研报
        await Market.updateResearchReport(values);
        Toast.success('研报更新成功');
      } else {
        // 创建研报
        await Market.createResearchReport(values);
        Toast.success('研报创建成功');
      }
      this.onCloseReportDrawer();
      this.fetchReports();
    } catch (error) {
      Toast.error('操作失败，请重试');
      console.error('提交研报失败:', error);
    }
  }

  onDeleteReport = async (id) => {
    try {
      await Market.deleteResearchReport(id);
      Toast.success('研报删除成功');
      this.fetchReports();
    } catch (error) {
      Toast.error('删除失败，请重试');
      console.error('删除研报失败:', error);
    }
  }

  showContentPreview = (record) => {
    if (!record.content) {
      Toast.error('研报内容为空');
      return;
    }

    this.setState({
      contentPreviewVisible: true,
      contentPreviewData: record,
    });
  }

  closeContentPreview = () => {
    this.setState({
      contentPreviewVisible: false,
      contentPreviewData: null,
    });
  }

  // 文件名清理函数
  sanitizeFileName = (fileName) => {
    if (!fileName) return '';
    return fileName.replace(/[\\/:*?"<>|]/g, '_');
  }

  // 解析 HTML 并转换为 docx 段落的辅助函数
  parseHtmlToDocxElements = (htmlString) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, 'text/html');
    const elements = [];

    const processNode = (node) => {
      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent.trim();
        if (text) {
          return new TextRun({ text });
        }
        return null;
      }

      if (node.nodeType === Node.ELEMENT_NODE) {
        const tagName = node.tagName.toLowerCase();
        const textContent = node.textContent.trim();

        switch (tagName) {
          case 'h1':
            return new Paragraph({
              children: [new TextRun({ text: textContent, bold: true, size: 32 })],
              heading: HeadingLevel.HEADING_1,
              spacing: { after: 400 },
            });
          case 'h2':
            return new Paragraph({
              children: [new TextRun({ text: textContent, bold: true, size: 28 })],
              heading: HeadingLevel.HEADING_2,
              spacing: { after: 300 },
            });
          case 'h3':
            return new Paragraph({
              children: [new TextRun({ text: textContent, bold: true, size: 26 })],
              heading: HeadingLevel.HEADING_3,
              spacing: { after: 200 },
            });
          case 'p':
            if (textContent) {
              const children = this.processInlineContent(node);
              return new Paragraph({
                children: children.length > 0 ? children : [new TextRun({ text: textContent })],
                spacing: { after: 200 },
              });
            }
            return new Paragraph({ children: [new TextRun({ text: ' ' })] });
          case 'strong':
          case 'b':
            return new TextRun({ text: textContent, bold: true });
          case 'em':
          case 'i':
            return new TextRun({ text: textContent, italics: true });
          case 'code':
            return new TextRun({
              text: textContent,
              font: 'Consolas',
              highlight: 'yellow'
            });
          case 'blockquote':
            return new Paragraph({
              children: [new TextRun({ text: textContent, italics: true })],
              indent: { left: 720 },
              spacing: { after: 200 },
            });
          case 'ul':
          case 'ol':
            return this.processListNode(node, tagName);
          case 'table':
            return this.processTable(node);
          case 'hr':
            return new Paragraph({
              children: [new TextRun({ text: '─'.repeat(50) })],
              spacing: { before: 200, after: 200 },
            });
          default:
            if (textContent) {
              return new Paragraph({
                children: [new TextRun({ text: textContent })],
                spacing: { after: 200 },
              });
            }
            return null;
        }
      }
      return null;
    };

    const processInlineElements = (node, children) => {
      Array.from(node.childNodes).forEach(child => {
        if (child.nodeType === Node.TEXT_NODE) {
          const text = child.textContent;
          if (text.trim()) {
            children.push(new TextRun({ text }));
          }
        } else if (child.nodeType === Node.ELEMENT_NODE) {
          const tagName = child.tagName.toLowerCase();
          const textContent = child.textContent;

          switch (tagName) {
            case 'strong':
            case 'b':
              children.push(new TextRun({ text: textContent, bold: true }));
              break;
            case 'em':
            case 'i':
              children.push(new TextRun({ text: textContent, italics: true }));
              break;
            case 'code':
              children.push(new TextRun({
                text: textContent,
                font: 'Consolas',
                highlight: 'yellow'
              }));
              break;
            default:
              children.push(new TextRun({ text: textContent }));
              break;
          }
        }
      });
    };

    this.processInlineElements = processInlineElements;

    Array.from(doc.body.children).forEach(node => {
      const element = processNode(node);
      if (element) {
        if (Array.isArray(element)) {
          elements.push(...element);
        } else {
          elements.push(element);
        }
      }
    });

    return elements;
  };

  // 处理复杂列表的辅助方法
  processListNode = (listNode, listType, indentLevel = 0) => {
    const listItems = [];
    const baseIndent = 720 + (indentLevel * 360); // 每层缩进增加360

    Array.from(listNode.children).forEach((li, index) => {
      if (li.tagName.toLowerCase() === 'li') {
        const bullet = listType === 'ul' ? '• ' : `${index + 1}. `;

        // 处理列表项中的内容
        const liElements = this.processListItemContent(li, bullet, baseIndent, indentLevel);
        listItems.push(...liElements);
      }
    });

    return listItems;
  };

  // 处理内联元素并保持格式
  processInlineContent = (node) => {
    const children = [];

    Array.from(node.childNodes).forEach(child => {
      if (child.nodeType === Node.TEXT_NODE) {
        const text = child.textContent;
        if (text.trim()) {
          children.push(new TextRun({ text }));
        }
      } else if (child.nodeType === Node.ELEMENT_NODE) {
        const tagName = child.tagName.toLowerCase();
        const textContent = child.textContent;

        switch (tagName) {
          case 'strong':
          case 'b':
            children.push(new TextRun({ text: textContent, bold: true }));
            break;
          case 'em':
          case 'i':
            children.push(new TextRun({ text: textContent, italics: true }));
            break;
          case 'code':
            children.push(new TextRun({
              text: textContent,
              font: 'Consolas',
              highlight: 'yellow'
            }));
            break;
          default:
            children.push(new TextRun({ text: textContent }));
            break;
        }
      }
    });

    return children;
  };

  // 处理列表项内容（可能包含段落、子列表等）
  processListItemContent = (liNode, bullet, baseIndent, indentLevel) => {
    const elements = [];
    let hasMainContent = false;

    // 检查是否有段落作为第一个子元素
    const firstChild = liNode.children[0];
    if (firstChild && firstChild.tagName.toLowerCase() === 'p') {
      // 处理第一个段落，添加项目符号
      const children = this.processInlineContent(firstChild);
      if (children.length > 0) {
        // 为第一个文本元素添加项目符号
        if (children[0] && children[0].text) {
          const originalText = children[0].text;
          children[0] = new TextRun({
            text: bullet + originalText,
            bold: children[0].bold || false,
            italics: children[0].italics || false,
            font: children[0].font,
            highlight: children[0].highlight,
          });
        }
        elements.push(new Paragraph({
          children,
          indent: { left: baseIndent },
          spacing: { after: 100 },
        }));
        hasMainContent = true;
      }
    }

    // 处理其他子元素
    Array.from(liNode.children).forEach((child, index) => {
      if (child.nodeType === Node.ELEMENT_NODE) {
        const tagName = child.tagName.toLowerCase();

        switch (tagName) {
          case 'p':
            // 跳过第一个段落（已经处理过了）
            if (index === 0 && hasMainContent) {
              break;
            }

            const children = this.processInlineContent(child);
            if (children.length > 0) {
              const prefix = !hasMainContent ? bullet : '';
              if (!hasMainContent && children[0] && children[0].text) {
                const originalText = children[0].text;
                children[0] = new TextRun({
                  text: prefix + originalText,
                  bold: children[0].bold || false,
                  italics: children[0].italics || false,
                  font: children[0].font,
                  highlight: children[0].highlight,
                });
              }
              elements.push(new Paragraph({
                children,
                indent: { left: baseIndent },
                spacing: { after: 100 },
              }));
              hasMainContent = true;
            }
            break;
          case 'ul':
          case 'ol':
            // 处理嵌套列表
            const nestedItems = this.processListNode(child, tagName, indentLevel + 1);
            elements.push(...nestedItems);
            break;
        }
      }
    });

    // 如果没有处理任何内容，使用默认处理
    if (elements.length === 0) {
      const text = liNode.textContent.trim();
      if (text) {
        elements.push(new Paragraph({
          children: [new TextRun({ text: bullet + text })],
          indent: { left: baseIndent },
          spacing: { after: 100 },
        }));
      }
    }

    return elements;
  };

  // 获取节点的直接文本内容（不包括子元素）
  getDirectTextContent = (node) => {
    let text = '';
    Array.from(node.childNodes).forEach(child => {
      if (child.nodeType === Node.TEXT_NODE) {
        text += child.textContent;
      }
    });
    return text.trim();
  };

  // 处理节点中的内联元素（只处理直接子元素）
  processInlineElementsInNode = (node, children, directOnly = false) => {
    Array.from(directOnly ? node.childNodes : node.childNodes).forEach(child => {
      if (child.nodeType === Node.TEXT_NODE) {
        const text = child.textContent.trim();
        if (text) {
          children.push(new TextRun({ text }));
        }
      } else if (child.nodeType === Node.ELEMENT_NODE && !directOnly) {
        const tagName = child.tagName.toLowerCase();
        const textContent = child.textContent.trim();

        switch (tagName) {
          case 'strong':
          case 'b':
            children.push(new TextRun({ text: textContent, bold: true }));
            break;
          case 'em':
          case 'i':
            children.push(new TextRun({ text: textContent, italics: true }));
            break;
          case 'code':
            children.push(new TextRun({
              text: textContent,
              font: 'Consolas',
              highlight: 'yellow'
            }));
            break;
          default:
            children.push(new TextRun({ text: textContent }));
            break;
        }
      } else if (child.nodeType === Node.ELEMENT_NODE && directOnly) {
        // 对于直接子元素，递归处理内联格式
        const tagName = child.tagName.toLowerCase();
        const textContent = child.textContent.trim();

        switch (tagName) {
          case 'strong':
          case 'b':
            children.push(new TextRun({ text: textContent, bold: true }));
            break;
          case 'em':
          case 'i':
            children.push(new TextRun({ text: textContent, italics: true }));
            break;
          case 'code':
            children.push(new TextRun({
              text: textContent,
              font: 'Consolas',
              highlight: 'yellow'
            }));
            break;
          case 'p':
          case 'ul':
          case 'ol':
            // 这些是块级元素，不在这里处理
            break;
          default:
            children.push(new TextRun({ text: textContent }));
            break;
        }
      }
    });
  };

  // 处理表格的辅助方法
  processTable = (tableNode) => {
    const rows = [];
    const tableRows = Array.from(tableNode.querySelectorAll('tr'));

    tableRows.forEach((tr, rowIndex) => {
      const cells = Array.from(tr.querySelectorAll('td, th'));
      const tableCells = cells.map(cell => {
        const isHeader = cell.tagName.toLowerCase() === 'th';
        return new TableCell({
          children: [
            new Paragraph({
              children: [
                new TextRun({
                  text: cell.textContent.trim() || ' ',
                  bold: isHeader,
                  size: isHeader ? 24 : 22,
                })
              ],
              spacing: { before: 100, after: 100 },
            })
          ],
          width: {
            size: 100 / cells.length,
            type: WidthType.PERCENTAGE,
          },
          margins: {
            top: 100,
            bottom: 100,
            left: 100,
            right: 100,
          },
        });
      });

      if (tableCells.length > 0) {
        rows.push(new TableRow({
          children: tableCells,
        }));
      }
    });

    if (rows.length > 0) {
      return new DocxTable({
        rows: rows,
        width: {
          size: 100,
          type: WidthType.PERCENTAGE,
        },
        margins: {
          top: 200,
          bottom: 200,
        },
      });
    }

    return null;
  };

  handleDownloadContent = async (record) => {
    if (!record.content) {
      Toast.error('研报内容为空');
      return;
    }

    try {
      // 第一步：将 Markdown 转换为 HTML
      const htmlContent = marked(record.content, {
        breaks: true,
        gfm: true,
      });

      // 第二步：解析 HTML 并转换为 docx 元素
      const docxElements = this.parseHtmlToDocxElements(htmlContent);

      // 第三步：创建 Word 文档
      const doc = new Document({
        sections: [
          {
            properties: {},
            children: [
              // 文档标题
              new Paragraph({
                children: [
                  new TextRun({
                    text: record.title || '研报内容',
                    bold: true,
                    size: 36,
                  }),
                ],
                heading: HeadingLevel.TITLE,
                spacing: { after: 600 },
              }),
              // 分隔线
              new Paragraph({
                children: [new TextRun({ text: '' })],
                spacing: { after: 400 },
              }),
              // 解析后的内容
              ...docxElements,
            ],
          },
        ],
      });

      // 第四步：生成并下载文档
      const blob = await Packer.toBlob(doc);
      const fileName = this.sanitizeFileName(record.title) || '研报内容';
      saveAs(blob, `${fileName}.docx`);
      Toast.success('文档下载成功');
    } catch (error) {
      console.error('下载失败:', error);
      Toast.error('下载失败，请重试');
    }
  }

  renderReportColumns = () => {
    return [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 80,
      },
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        ellipsis: true,
        width: '20%',
        render: (text, record) => {
          return (
            <a onClick={() => { return this.onOpenReportDrawer(record); }}>{text}</a>
          );
        },
      },
      {
        title: '来源',
        dataIndex: 'source',
        key: 'source',
      },
      {
        title: '分类',
        dataIndex: 'category',
        key: 'category',
      },
      {
        title: '内容预览',
        dataIndex: 'content',
        key: 'content',
        ellipsis: true,
        width: '15%',
        render: (text, record) => {
          if (!text) return '-';
          return (
            <a onClick={() => this.showContentPreview(record)}>
              {text.length > 30 ? `${text.substring(0, 30)}...` : text}
            </a>
          );
        },
      },
      {
        title: 'PDF文件',
        dataIndex: 'pdfUrl',
        key: 'pdfUrl',
        ellipsis: true,
        render: (text, record) => {
          if (!text) return '-';
          return (
            <a href={text} target="_blank" rel="noopener noreferrer">
              查看PDF
            </a>
          );
        },
      },
      {
        title: '音频',
        dataIndex: 'audioUrl',
        key: 'audioUrl',
        ellipsis: true,
        render: (text) => {
          if (!text) return '-';
          return (
            <a href={text} target="_blank" rel="noopener noreferrer">
              播放音频
            </a>
          );
        },
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (text, record) => {
          const statusMap = {
            pending: '待处理',
            running: '处理中',
            done: '已完成',
            failed: '失败'
          };

          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {statusMap[text] || text}
              {record.statusDetail && (
                <Popover
                  content={<div style={{ maxWidth: 300 }}>{record.statusDetail}</div>}
                  title="状态详情"
                  trigger="hover"
                >
                  <InfoCircleOutlined style={{ marginLeft: 8, color: '#1890ff' }} />
                </Popover>
              )}
            </div>
          );
        },
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        render: (text) => {
          return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-';
        },
      },
      {
        title: '操作',
        key: 'action',
        render: (_, record) => {
          return (
            <>
              <a onClick={() => { return this.onOpenReportDrawer(record); }}>编辑</a>
              <Divider type="vertical" />
              {record.content && (
                <>
                  <a onClick={() => this.showContentPreview(record)}>
                    <EyeOutlined /> 查看
                  </a>
                  <Divider type="vertical" />
                </>
              )}
              <Popconfirm
                title="确定要删除这个研报吗?"
                onConfirm={() => { return this.onDeleteReport(record.id); }}
              >
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  renderSelects = () => {
    const { categories } = this.state;

    return [
      <Select
        key="category"
        placeholder="选择分类"
        style={{ width: 150, marginRight: 8, marginBottom: 16 }}
        allowClear
        onChange={(value) => {
          this.setState({ reportCategory: value, reportsPagination: { ...this.state.reportsPagination, pageIndex: 1 } }, () => {
            this.fetchReports();
          });
        }}
      >
        {categories.map(category => (
          <Select.Option key={category} value={category}>{category}</Select.Option>
        ))}
      </Select>,
      <Select
        key="status"
        placeholder="选择状态"
        style={{ width: 150, marginBottom: 16 }}
        allowClear
        onChange={(value) => {
          this.setState({ reportStatus: value, reportsPagination: { ...this.state.reportsPagination, pageIndex: 1 } }, () => {
            this.fetchReports();
          });
        }}
      >
        <Select.Option value="pending">待处理</Select.Option>
        <Select.Option value="running">处理中</Select.Option>
        <Select.Option value="done">已完成</Select.Option>
        <Select.Option value="failed">失败</Select.Option>
      </Select>
    ];
  }

  renderButtons = () => {
    return [
      <Button
        type="primary"
        onClick={() => { return this.onOpenReportDrawer(); }}
      >
        新增研报
      </Button>,
    ];
  }

  render() {
    const {
      reports,
      reportsTotal,
      reportsPagination,
      reportDrawerOpen,
      selectedReport,
      reportKeywords,
      loading,
      contentPreviewVisible,
      contentPreviewData
    } = this.state;

    return (
      <div>
        <FilterBar
          shouldShowSearchInput
          searchKeyWords={reportKeywords}
          onChange={(value) => { return this.setState({ reportKeywords: value }); }}
          onSearch={() => {
            this.setState({ reportsPagination: { ...reportsPagination, pageIndex: 1 } }, () => {
              this.fetchReports();
            });
          }}
          placeholder="请输入研报标题"
          renderSelects={this.renderSelects}
          renderButtons={this.renderButtons}
        />

        <PaginationTable
          totalDataCount={reportsTotal}
          dataSource={reports}
          pagination={reportsPagination}
          columns={this.renderReportColumns()}
          loading={loading}
          onPaginationChange={(pagination) => {
            if (!pagination) return;

            this.setState({ reportsPagination: pagination });
            this.fetchReports({
              pagination: {
                skip: (pagination.pageIndex - 1) * pagination.pageSize,
                limit: pagination.pageSize,
              },
            });
          }}
        />

        {reportDrawerOpen && (
          <ResearchReportDrawer
            open={reportDrawerOpen}
            report={selectedReport}
            onClose={this.onCloseReportDrawer}
            onSubmit={this.onSubmitReport}
            categories={this.state.reportCategories}
            workflows={this.props.workflowsMap || []}
          />
        )}

        {/* 内容预览模态框 */}
        <Modal
          title={contentPreviewData?.title ? `${contentPreviewData.title} - 内容预览` : '研报内容预览'}
          open={contentPreviewVisible}
          onCancel={this.closeContentPreview}
          footer={[
            <Button key="close" onClick={this.closeContentPreview}>
              关闭
            </Button>,
            <Button
              key="download"
              type="primary"
              icon={<DownloadOutlined />}
              onClick={() => this.handleDownloadContent(contentPreviewData)}
            >
              下载Word文档
            </Button>,
          ]}
          width={800}
          bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }}
        >
          {contentPreviewData?.content && (
            <div className="content-preview" style={{ padding: '16px' }}>
              <ReactMarkdown remarkPlugins={[remarkGfm]}>{contentPreviewData.content}</ReactMarkdown>
            </div>
          )}
        </Modal>
      </div>
    );
  }
}
