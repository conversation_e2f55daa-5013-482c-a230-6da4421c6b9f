import { InboxOutlined } from '@ant-design/icons';
import Consts from '~/consts';
import { AliyunHelper } from '~/engine';
import ChatBot from '~/engine/ChatBot';
import { Platform } from '~/plugins';
import {
  Button,
  Drawer,
  Form,
  Input,
  InputNumber,
  Mentions,
  Select,
  Spin,
  Switch,
  message as Toast,
  Upload,
} from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';

// 生成默认值函数，根据参数类型生成默认值
const generateDefaultValue = (type) => {
  switch (type) {
    case 'string':
      return '';
    case 'number':
      return 0;
    case 'boolean':
      return false;
    case 'array':
      return [];
    case 'object':
      return {};
    default:
      return null;
  }
};

const MENTIONS_OPTIONS = [
  { value: '{title}}', label: '标题' },
  { value: '{source}}', label: '来源' },
  { value: '{category}}', label: '分类' },
  { value: '{pdf_url}}', label: '文件' },
];

export default class ResearchReportDrawer extends Component {
  static propTypes = {
    open: PropTypes.bool.isRequired,
    report: PropTypes.object,
    onClose: PropTypes.func.isRequired,
    onSubmit: PropTypes.func.isRequired,
    categories: PropTypes.array.isRequired,
  }


  constructor(props) {
    super(props);
    this.formRef = React.createRef();
    this.state = {
      loading: false,
      fileList: [],
      workflowParams: [],
      workflowUuid: '',
      workflowConfig: {},
      uploadLoading: false,
      cdnWorkflows: [],
      data: {},
    };
  }

  componentDidMount() {
    this.initData();
    this.fetchCdnWorkflows();
  }

  initData = () => {
    const { report } = this.props;

    if (report) {
      this.setState({ data: report });
      // 如果有文件，初始化文件列表
      if (report.pdfUrl) {
        this.setState({
          fileList: [
            {
              uid: '-1',
              name: report.fileName || '研报文件.pdf',
              status: 'done',
              url: report.pdfUrl,
            },
          ],
        });
      }

      // 如果有工作流ID，获取工作流参数
      if (report?.workflowSetting?.workflowUuid) {
        this.onWorkflowChange(report?.workflowSetting?.workflowUuid);
      }

      // 如果有工作流配置，初始化工作流配置
      if (report?.workflowSetting?.workflowConfig) {
        this.setState({
          workflowConfig: report?.workflowSetting?.workflowConfig,
        });
      }
    }
  }

  fetchCdnWorkflows = async () => {
    try {
      // 参考其他组件的实现，从OSS CDN获取工作流配置
      const timestamp = new Date().getTime();
      const resp = await fetch(`${Consts.OSS_CDN_DOMAIN}/llmbot/plugin/configs/aivideo.json?t=${timestamp}`);
      const data = await resp.json();
      const env = Platform.isProd() ? 'production' : 'staging';
      const cdnWorkflows = data?.[env]?.yanbao_flows || [];
      this.setState({ cdnWorkflows });
    } catch (error) {
      console.error('获取CDN工作流配置失败:', error);
    }
  }

  handleSubmit = async () => {
    try {
      const values = await this.formRef.current.validateFields();
      const { workflowConfig, workflowUuid } = this.state;

      // 合并表单值和工作流配置
      const reportData = {
        ...values,
        workflowSetting: {
          workflowUuid,
          workflowConfig,
        },
      };

      this.setState({ loading: true });
      await this.props.onSubmit(reportData);
      this.setState({ loading: false });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  }


  uploadPdf = () => {
    return (option) => {
      let aborted = false;
      (async () => {
        try {
          const pdfUrl = await AliyunHelper.clipsUploadImage(option.file, (progress) => {
            const percent = Math.round((progress.loaded / progress.total) * 100);
            option.onProgress({ percent });
          });

          if (aborted) {
            return;
          }

          // 更新表单值和文件列表
          if (this.formRef && this.formRef.current) {
            this.formRef.current.setFieldsValue({
              pdfUrl,
              fileName: option.file.name,
            });
          }

          this.setState({
            fileList: [{
              uid: '-1',
              name: option.file.name,
              status: 'done',
              url: pdfUrl,
            }],
          });

          option.onSuccess();
        } catch (e) {
          console.error('上传PDF文件失败:', e);
          option.onError();
        }
      })();

      return {
        abort() {
          aborted = true;
        },
      };
    };
  }


  onWorkflowChange = async (workflowId) => {
    try {
      this.setState({ loading: true });

      // // 设置表单的workflowUuid字段
      // if (this.formRef && this.formRef.current) {
      //   this.formRef.current.setFieldsValue({
      //     workflowUuid: workflowId
      //   });
      // }
      this.setState({ workflowUuid: workflowId });

      // 获取工作流详情
      const workflow = await ChatBot.getChatbotWorkflow(workflowId);

      if (workflow) {
        // 查找选中的工作流配置（从CDN获取的工作流列表中）
        const selectedWorkflow = this.state.cdnWorkflows.find((w) => { return w.workflow_uuid === workflowId; });
        // 获取工作流默认参数配置
        // eslint-disable-next-line camelcase
        const { var_defaults } = selectedWorkflow || {};

        // 解析工作流内容
        const content = JSON.parse(workflow.content);
        const inputSchema = content.nodes[0].data.input_schema;
        const workflowParams = [];

        // 转换输入模式为参数列表
        Object.entries(inputSchema).forEach(
          ([key, value]) => {
            workflowParams.push({
              key: key.includes('_') ? key.replace(/_([a-z])/g, (g) => g[1].toUpperCase()) : key,
              description: value.description || '',
              default: value.default || '',
              type: value.type || 'str',
            });
          },
        );

        // 准备状态更新对象
        const stateParams = {
          workflowParams,
        };

        // 如果有默认值配置，设置工作流配置的默认值
        if (!_.isEmpty(var_defaults)) {
          const defaults = {};
          Object.entries(var_defaults).forEach(([key, value]) => {
            defaults[key.includes('_') ? key.replace(/_([a-z])/g, (g) => g[1].toUpperCase()) : key] = value;
          });
          stateParams.workflowConfig = { ...this.state.workflowConfig, ...defaults };
        } else {
          // 如果没有默认值配置，根据参数类型生成默认值
          const workflowConfig = {};
          workflowParams.forEach((param) => {
            workflowConfig[param.key] = generateDefaultValue(param.type);
          });
          stateParams.workflowConfig = {
            ...workflowConfig,
            ...this.state.workflowConfig,
          };
        }

        this.setState(stateParams);
      }
    } catch (error) {
      Toast.error('获取工作流详情失败');
      this.setState({
        workflowParams: [],
        workflowConfig: {},
      });
    } finally {
      this.setState({ loading: false });
    }
  }

  onWorkflowParamChange = (value, key) => {
    this.setState({
      workflowConfig: {
        ...this.state.workflowConfig,
        [key]: value,
      },
    });
  }

  onChangeValue = (e, type) => {
    const value = e.target ? e.target.value : e;
    this.setState({ report: { ...this.state.report, [type]: value } });
  }

  render() {
    const { open, onClose, report } = this.props;
    const { loading, fileList, uploadLoading, cdnWorkflows, workflowConfig } = this.state;
    const title = report ? '编辑研报' : '新增研报';

    // 文件上传配置
    const uploadProps = {
      name: 'file',
      multiple: false,
      fileList,
      showUploadList: true,
      accept: 'application/pdf',
      beforeUpload: (file) => {
        const isPDF = file.type === 'application/pdf';
        if (!isPDF) {
          Toast.error('只能上传PDF文件!');
        }
        return isPDF;
      },
      customRequest: this.uploadPdf(),
    };

    return (
      <Drawer
        title={title}
        width={720}
        onClose={onClose}
        open={open}
        bodyStyle={{ paddingBottom: 80 }}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button onClick={onClose} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button onClick={this.handleSubmit} type="primary" loading={loading}>
              提交
            </Button>
          </div>
        }
      >
        <Spin spinning={loading}>
          <Form
            ref={this.formRef}
            layout="vertical"
            initialValues={report || {}}
          >
            <Form.Item
              name="id"
              hidden
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="title"
              label="研报标题"
              rules={[{ required: true, message: '请输入研报标题' }]}
            >
              <Input value={report?.title} placeholder="请输入研报标题" />
            </Form.Item>

            <Form.Item
              name="source"
              label="研报来源"
              rules={[{ required: true, message: '请输入研报来源' }]}
            >
              <Input value={report?.source} placeholder="请输入研报来源，如：某研究机构" />
            </Form.Item>

            <Form.Item
              name="category"
              label="研报分类"

              rules={[{ required: true, message: '请输入研报分类' }]}
            >
              <Input value={report?.category} placeholder="请输入研报分类" />
            </Form.Item>

            <Form.Item
              label="研报文件"
              required
              tooltip="上传PDF格式的研报文件"
            >
              <Form.Item
                name="pdfUrl"
                hidden
                rules={[{ required: true, message: '请上传研报文件' }]}
              >
                <Input value={report?.pdfUrl} />
              </Form.Item>

              <Form.Item
                name="fileName"
                hidden
              >
                <Input value={report?.fileName} />
              </Form.Item>

              <Upload.Dragger {...uploadProps} disabled={uploadLoading}>
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p className="ant-upload-hint">仅支持PDF格式文件</p>
              </Upload.Dragger>
            </Form.Item>

            <Form.Item
              label="解读工作流"
              rules={[{ required: true, message: '请选择解读工作流' }]}
            >
              <Select
                placeholder="请选择解读工作流"
                onChange={this.onWorkflowChange}
                loading={loading}
                value={report?.workflowSetting?.workflowUuid}
              >
                {cdnWorkflows.map((workflow) => {
                  return (
                    <Select.Option
                      key={workflow.workflow_uuid}
                      value={workflow.workflow_uuid}
                    >
                      {workflow.name}
                    </Select.Option>
                  );
                })}
              </Select>
            </Form.Item>

            {
              this.state.workflowParams.map((param) => {
                let contentStr = null;
                switch (param.type) {
                  case 'bool':
                    contentStr = (
                      <Switch
                        checked={workflowConfig?.[param.key]}
                        onChange={(e) => { return this.onWorkflowParamChange(e, param.key); }}
                      />
                    );
                    break;
                  case 'int':
                    contentStr = (
                      <InputNumber
                        value={workflowConfig?.[param.key] || param.default}
                        onChange={(e) => { return this.onWorkflowParamChange(e, param.key); }}
                      />
                    );
                    break;
                  default:
                    contentStr = (
                      <Mentions
                        prefix="{"
                        autoSize={{ minRows: 1 }}
                        value={workflowConfig?.[param.key] || param.default}
                        onChange={(e) => { return this.onWorkflowParamChange(e, param.key); }}
                        options={MENTIONS_OPTIONS}
                        placeholder="支持{{变量}}格式"
                      />
                    );
                    break;
                }

                return (
                  <Form.Item
                    key={param.key}
                    label={param.description || param.key}
                  >
                    {contentStr}
                  </Form.Item>
                );
              })
            }
          </Form>
        </Spin>
      </Drawer>
    );
  }
}
