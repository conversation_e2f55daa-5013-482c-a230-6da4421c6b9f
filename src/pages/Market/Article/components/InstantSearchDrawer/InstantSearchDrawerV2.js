/* eslint-disable no-await-in-loop */
import './InstantSearchDrawerV2.less';

import { Toast } from '~/components';
import { Materials } from '~/engine';
import { <PERSON><PERSON>, Divider, Drawer, Select, Spin, Table } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

const ANALYSIS_MAP = { digg_count: '点赞(阅读)数', comment_count: '评论数', share_count: '分享数', collect_count: '收藏数' };


export default class InstantSearchDrawerV2 extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    onClose: PropTypes.func,
  }

  state = {
    earliest: 10,
    author: '',
    dataSource: [],
    spin: false,
    authors: [],
  }

  componentDidMount = async () => {
    const { items } = await Materials.fetchmaterialSources({
      platform: '抖音',
      usage: 'character',
      'pagination.pageIndex': 1,
      'pagination.pageSize': 1000,
    });

    const authors = items.map((x) => {
      return { value: x.sourceUrl.split('/').pop(), name: x.author, url: x.sourceUrl, sourceId: x.id };
    }).filter((x) => { return !_.isEmpty(x.value); });

    this.setState({ authors });
  }

  onSearch = async (isAutoRetry = false, isLoadMore = false) => {
    const { dataSource, author, earliest } = this.state;
    if (_.isEmpty(author)) {
      Toast.show('请输入作者', Toast.Type.WARNING);
      return;
    }

    // 提取真正的 sec_user_id，去除查询参数和其他无关字符
    const cleanSecUserId = author.split('?')[0].trim();

    if (!isAutoRetry) {
      this.setState({ spin: true });
    }

    try {
      const resp = await fetch('https://douk-wcacudgbct.cn-shanghai.fcapp.run/douyin/account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sec_user_id: cleanSecUserId,
          earliest,
        }),
      });
      const { data } = await resp.json();

      // 如果是首次搜索(earliest=10)且没有数据，自动递增重试
      if (earliest === 10 && (!data || data.length === 0) && earliest < 180) {
        await this.setState({ earliest: earliest + 10 });
        this.onSearch(true);
        return;
      }

      // 如果是加载更多且数据重复，自动递增重试
      if (isLoadMore && data && data.length > 0 && dataSource.length > 0) {
        const currentIds = dataSource.map((item) => { return item.id; });
        const newIds = data.map((item) => { return item.id; });
        const isDuplicate = _.isEqual(_.sortBy(currentIds), _.sortBy(newIds));

        if (isDuplicate && earliest < 180) {
          await this.setState({ earliest: earliest + 10 });
          this.onSearch(true, true);
          return;
        }
      }

      this.setState({ spin: false, dataSource: data || [] });
    } catch (error) {
      this.setState({ spin: false, dataSource });
      Toast.show('未查询到结果', Toast.Type.WARNING);
    }
  }

  onSync = async (row) => {
    const { sourceId } = this.state.authors.find((x) => { return x.value === this.state.author; });

    await Materials.manualSyncMaterial({ sourceId, rawMaterial: row });
    Toast.show('重新创作任务已提交，请稍后查看结果', Toast.Type.SUCCESS);
  }

  renderColumns = () => {
    return [
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        ellipsis: true,
        render: (txt, row) => {
          return txt || row.desc || '-';
        },
      },
      {
        title: '链接',
        dataIndex: 'id',
        key: 'id',
        width: 100,
        align: 'center',
        render: (txt) => {
          return (<a href={`https://www.douyin.com/video/${txt}`} target="_blank" rel="noopener noreferrer">查看</a>);
        },
      },
      ..._.map(ANALYSIS_MAP, (v, k) => {
        return {
          title: v,
          width: 120,
          dataIndex: k,
          key: k,
          align: 'center',
          render: (text, row) => {
            return <span>{row[k]}</span>;
          },
          sorter: (a, b) => { return a[k] - b[k]; },
        };
      }),
      {
        title: '发布时间',
        dataIndex: 'create_timestamp',
        key: 'create_timestamp',
        width: 180,
        render: (txt) => { return moment(txt * 1000).format('YYYY-MM-DD HH:mm'); },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        width: 180,
        render: (txt, row) => {
          return (
            <a onClick={() => { return this.onSync(row); }}>同步文稿</a>
          );
        },
      },
    ];
  }

  render = () => {
    const { open } = this.props;
    const { dataSource } = this.state;

    return (
      <Drawer
        width="66vw"
        open={open}
        title="即时搜索 V2"
        maskClosable={false}
        className="instant-search-drawer-v2"
        onClose={() => { this.props.onClose(); }}
      >
        <div style={{ marginBottom: 16 }}>
          <Select
            showSearch
            style={{ width: 200 }}
            value={this.state.author}
            placeholder="请选择作者"
            filterOption={(input, option) => { return option.children.includes(input); }}
            onChange={(e) => { return this.setState({ author: e }); }}
          >
            {this.state.authors.map((author) => {
              return <Select.Option key={author.value} value={author.value}>{author.name}</Select.Option>;
            })}
          </Select>
          <Divider type="vertical" />
          <Button
            type="primary"
            onClick={() => {
              return this.setState({ earliest: 10, dataSource: [] }, () => { return this.onSearch(); });
            }}
          >
            搜索
          </Button>
        </div>
        <Spin spinning={this.state.spin}>
          <Table
            size="small"
            rowKey="id"
            dataSource={dataSource}
            columns={this.renderColumns()}
            pagination={false}
          />
          {
            !_.isEmpty(dataSource) && this.state.earliest < 180 &&
            <Button
              className="load-more-btn"
              onClick={async () => {
                await this.setState({ earliest: this.state.earliest + 10 });
                this.onSearch(false, true);
              }}
            >
              加载更多
            </Button>
          }
        </Spin>
      </Drawer>
    );
  }
}
