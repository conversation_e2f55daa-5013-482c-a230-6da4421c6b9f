// ManualAddDrawer 样式文件
.manual-add-drawer {
  .ant-drawer-body {
    padding: 24px;
  }

  .ant-drawer-footer {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    background: #fff;
  }

  .common-form {
    .ant-form-item {
      margin-bottom: 24px;
    }

    .ant-form-item-label {
      font-weight: 500;
    }

    .ant-form-item-required::before {
      color: #ff4d4f;
    }

    .ant-input {
      border-radius: 4px;

      &:focus,
      &:hover {
        border-color: #1890ff;
      }
    }

    .ant-select {
      .ant-select-selector {
        border-radius: 4px;

        &:focus,
        &:hover {
          border-color: #1890ff;
        }
      }
    }

    .ant-input-number {
      width: 100%;
      border-radius: 4px;

      &:focus,
      &:hover {
        border-color: #1890ff;
      }
    }

    .ant-switch {
      &.ant-switch-checked {
        background-color: #1890ff;
      }
    }
  }

  // 工作流参数区域样式
  .workflow-params-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;

    .section-title {
      margin-bottom: 16px;
      font-size: 14px;
      font-weight: 500;
      color: #262626;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    width: 90vw !important;

    .ant-form {
      .ant-form-item-label {
        text-align: left;
      }
    }
  }

  // 加载状态
  .loading-overlay {
    display: flex;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    z-index: 1000;
  }
}
