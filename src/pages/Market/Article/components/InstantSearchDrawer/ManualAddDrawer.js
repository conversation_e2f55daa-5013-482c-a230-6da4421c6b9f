import './ManualAddDrawer.less';

import { Toast } from '~/components';
import Consts from '~/consts';
import { ChatBot } from '~/engine';
import { Platform } from '~/plugins';
import { Button, Drawer, Form, Input, InputNumber, Mentions, Select, Space, Spin, Switch } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

// 工作流参数提及选项
const MENTIONS_OPTIONS = [
  { value: '{title}}', label: '标题' },
  { value: '{content}}', label: '内容' },
  { value: '{collect_count}}', label: '收藏数' },
  { value: '{comment_count}}', label: '评论数' },
  { value: '{digg_count}}', label: '点赞数' },
  { value: '{share_count}}', label: '分享数' },
  { value: '{source_url}}', label: '来源链接' },
];

// 生成默认值
const generateDefaultValue = (type) => {
  switch (type) {
    case 'str':
      return '';
    case 'bool':
      return false;
    case 'int':
      return 0;
    default:
      return '';
  }
};

export default class ManualAddDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    materialSourcesList: PropTypes.array,
    onClose: PropTypes.func,
    onSubmit: PropTypes.func,
  }

  static defaultProps = {
    open: false,
    onClose: () => { },
    onSubmit: () => { },
  }

  state = {
    title: '',
    content: '',
    materialSourceId: undefined,
    loading: false,
    // 工作流相关状态
    workflowLoading: false,
    workflows: [],
    selectedWorkflow: null,
    workflowParams: [],
    workflowConfig: {},
  }

  componentDidMount = async () => {
    if (this.props.open) {
      await this.fetchWorkflows();
    }
  }

  componentDidUpdate = async (prevProps) => {
    if (!prevProps.open && this.props.open) {
      await this.fetchWorkflows();
    }
  }

  fetchWorkflows = async () => {
    this.setState({ workflowLoading: true });
    try {
      // 参考RewriteDrawer的实现，从OSS CDN获取工作流配置
      const timestamp = new Date().getTime();
      const resp = await fetch(`${Consts.OSS_CDN_DOMAIN}/llmbot/plugin/configs/aivideo.json?t=${timestamp}`);
      const data = await resp.json();
      const env = Platform.isProd() ? 'production' : 'staging';
      const workflows = data?.[env]?.material_flows || [];

      this.setState({
        workflows,
        selectedWorkflow: null,
        workflowParams: [],
        workflowConfig: {},
      });
    } catch (error) {
      Toast.show('获取改稿方案失败', Toast.Type.ERROR);
    } finally {
      this.setState({ workflowLoading: false });
    }
  }

  onWorkflowChange = async (workflowUuid) => {
    const selectedWorkflow = this.state.workflows.find((w) => { return w.workflow_uuid === workflowUuid; });
    this.setState({ selectedWorkflow });

    // 获取工作流参数配置
    try {
      const workflow = await ChatBot.getChatbotWorkflow(workflowUuid);
      // eslint-disable-next-line
      const { var_defaults } = selectedWorkflow || {};
      const content = JSON.parse(workflow.content);
      const inputSchema = content.nodes[0].data.input_schema;
      const workflowParams = [];
      Object.entries(inputSchema).forEach(
        ([key, value]) => {
          workflowParams.push({
            key,
            description: value.description || '',
            default: value.default || '',
            type: value.type || 'str',
          });
        },
      );

      // 设置默认值，参考GroupTabPane的实现
      const stateParams = { workflowParams };
      if (!_.isEmpty(var_defaults)) {
        const defaults = {};
        Object.entries(var_defaults).forEach(([key, value]) => {
          defaults[key] = value;
        });
        stateParams.workflowConfig = { ...this.state.workflowConfig, ...defaults };
      }

      this.setState(stateParams);
    } catch (error) {
      this.setState({ workflowParams: [] });
    }
  }

  // 处理工作流参数值变化
  onWorkflowParamChange = (value, paramKey) => {
    this.setState({
      workflowConfig: {
        ...this.state.workflowConfig,
        [paramKey]: value,
      },
    });
  }

  onSubmit = async () => {
    const { title, content, materialSourceId, selectedWorkflow, workflowParams, workflowConfig } = this.state;

    // 表单验证
    if (!title.trim()) {
      Toast.show('请输入标题', Toast.Type.WARNING);
      return;
    }
    if (!content.trim()) {
      Toast.show('请输入内容', Toast.Type.WARNING);
      return;
    }

    if (!materialSourceId) {
      Toast.show('请选择文稿源', Toast.Type.WARNING);
      return;
    }

    this.setState({ loading: true });

    try {
      const submitData = { sourceId: materialSourceId };

      // 如果选择了改稿方案，添加工作流配置
      if (selectedWorkflow) {
        const finalWorkflowConfig = {};
        workflowParams.forEach((param) => {
          if (workflowConfig[param.key] !== undefined) {
            finalWorkflowConfig[param.key] = workflowConfig[param.key];
          } else {
            finalWorkflowConfig[param.key] = generateDefaultValue(param.type);
          }
        });

        submitData.optimize_config = {
          workflow_setting: {
            workflow_uuid: selectedWorkflow.workflow_uuid,
            workflow_config: finalWorkflowConfig,
          },
        };
      }

      submitData.rawMaterial = {
        title: title.trim(),
        desc: title.trim(),
        content: content.trim(),
        sourceId: materialSourceId,
        sourceUrl: `${moment().valueOf()}`,
        collectCount: 0,
        commentCount: 0,
        diggCount: 0,
        shareCount: 0,
      };

      await this.props.onSubmit(submitData);
      Toast.show('提交成功', Toast.Type.SUCCESS);
      this.onReset();
      this.props.onClose();
    } catch (error) {
      Toast.show('提交失败', Toast.Type.ERROR);
    } finally {
      this.setState({ loading: false });
    }
  }

  onReset = () => {
    this.setState({
      title: '',
      content: '',
      materialSourceId: undefined,
      selectedWorkflow: null,
      workflowParams: [],
      workflowConfig: {},
    });
  }

  render() {
    const { open } = this.props;
    const { workflowLoading, workflows, selectedWorkflow, workflowParams, workflowConfig } = this.state;

    return (
      <Drawer
        className="manual-add-drawer"
        width="66vw"
        open={open}
        title="手动新增"
        maskClosable={false}
        onClose={this.props.onClose}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={this.props.onClose}>
                取消
              </Button>
              <Button
                type="primary"
                loading={this.state.loading}
                onClick={this.onSubmit}
              >
                提交
              </Button>
            </Space>
          </div>
        }
      >
        <Spin spinning={workflowLoading}>
          <Form labelCol={{ span: 3 }} className="common-form">
            <Form.Item label="文稿源" required>
              <Select
                showSearch
                style={{ width: '100%' }}
                value={this.state.materialSourceId}
                filterOption={(input, option) => { return option.children.includes(input); }}
                onChange={(e) => { return this.setState({ materialSourceId: e }); }}
              >
                {
                  _.map(this.props.materialSourcesList, (w) => {
                    return <Select.Option value={w.value}>{w.name}</Select.Option>;
                  })
                }
              </Select>
            </Form.Item>
            <Form.Item label="标题" required>
              <Input
                value={this.state.title}
                placeholder="请输入标题"
                onChange={(e) => { return this.setState({ title: e.target.value }); }}
              />
            </Form.Item>

            <Form.Item label="内容" required>
              <Input.TextArea
                autoSize={{ minRows: 3, maxRows: 6 }}
                placeholder="请输入内容"
                value={this.state.content}
                onChange={(e) => { return this.setState({ content: e.target.value }); }}
              />
            </Form.Item>
            <Form.Item label="改稿方案" >
              <Select
                placeholder="请选择改稿方案"
                value={selectedWorkflow?.workflow_uuid}
                onChange={this.onWorkflowChange}
                style={{ width: '100%' }}
                showSearch
                filterOption={(input, option) => {
                  return option.children.toLowerCase().includes(input.toLowerCase());
                }}
              >
                {workflows.map((workflow) => {
                  return (
                    <Select.Option key={workflow.workflow_uuid} value={workflow.workflow_uuid}>
                      {workflow.name}
                    </Select.Option>
                  );
                })}
              </Select>
            </Form.Item>

            {/* 工作流参数配置 */}
            {workflowParams.length > 0 && (
              <Form.Item label="参数配置">
                <div className="workflow-params-container">
                  {workflowParams.map((param) => {
                    let paramInput = null;
                    switch (param.type) {
                      case 'bool':
                        paramInput = (
                          <Switch
                            style={{ width: 'auto' }}
                            checked={workflowConfig[param.key] || param.default}
                            onChange={(value) => { return this.onWorkflowParamChange(value, param.key); }}
                          />
                        );
                        break;
                      case 'int':
                        paramInput = (
                          <InputNumber
                            value={workflowConfig[param.key] || param.default}
                            onChange={(value) => { return this.onWorkflowParamChange(value, param.key); }}
                            style={{ width: 'auto' }}
                          />
                        );
                        break;
                      default:
                        paramInput = (
                          <Mentions
                            prefix="{"
                            autoSize={{ minRows: 1 }}
                            value={workflowConfig[param.key] || param.default}
                            onChange={(value) => { return this.onWorkflowParamChange(value, param.key); }}
                            options={MENTIONS_OPTIONS}
                            placeholder="支持{{变量}}格式"
                            style={{ width: '100%' }}
                          />
                        );
                        break;
                    }

                    return (
                      <div key={param.key} className="param-item">
                        <div className="param-label">
                          <strong>{param.description || param.key}：</strong>
                        </div>
                        <div className="param-input">
                          {paramInput}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </Form.Item>
            )}
          </Form>
        </Spin>
      </Drawer>
    );
  }
}
