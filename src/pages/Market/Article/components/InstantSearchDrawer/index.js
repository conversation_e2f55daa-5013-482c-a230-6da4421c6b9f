/* eslint-disable no-await-in-loop */
import { PlusOutlined } from '@ant-design/icons';
import { FilterBar, Toast } from '~/components';
import { But<PERSON>, Divider, Drawer, Input, Select, Space, Spin, Table } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { v4 as uuidV4 } from 'uuid';

const ANALYSIS_MAP = { digg_count: '点赞(阅读)数', comment_count: '评论数', share_count: '分享数', collect_count: '收藏数' };
const PLATFORM_MAP = {
  toutiao: '头条',
  douyin: '抖音',
  bilibili: 'B站',
  xigua: '西瓜',
  ytb: 'YouTuBe',
};
export default class InstantSearchDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    materialSourcesList: PropTypes.object,
    createItem: PropTypes.func,
    createFetchJob: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    page: 1,
    topic: '',
    platform: _.keys(PLATFORM_MAP),
    dataSource: [],
    materialSources: [],
    spin: false,
    open: false,
    pageInfo: {},
  }

  componentDidMount = () => {
    const list = (this.props.materialSourcesList || []).filter((x) => { return x.platform === '即时话题'; });
    this.setState({ materialSources: list });
  }

  onSearch = async () => {
    await this.setState({ spin: true });
    const { platform, topic, page, dataSource, pageInfo } = this.state;
    if (_.isEmpty(topic) || _.isEmpty(platform)) {
      Toast.show('请输入话题和平台', Toast.Type.WARNING);
      return;
    }

    try {
      const resp = await fetch('https://open-con-rsshub-frontend-dopdngtznt.cn-shanghai.fcapp.run', {
        method: 'POST',
        body: JSON.stringify({ topic, platforms: platform, page, token: pageInfo.token, key: pageInfo.key }),
      });
      const { items, key, token } = await resp.json();

      const newSource = [
        ...dataSource,
        ...items.map((x) => { return { share_count: 0, collect_count: 0, ...x, ...x.tags }; }),
      ];
      this.setState({ spin: false, dataSource: newSource, pageInfo: { key, token } });
    } catch (error) {
      this.setState({ spin: false, dataSource });
      Toast.show('未查询到结果', Toast.Type.WARNING);
    }
  }

  onAdd = async () => {
    const { name } = this.state;
    if (_.isEmpty(name)) {
      Toast.show('请输入文稿源', Toast.Type.WARNING);
      return;
    }

    const { id, author, platform } = await this.props.createItem('materialSource', {
      author: name,
      platform: '即时话题',
      usage: 'character',
      sourceUrl: `${window.location.href}/${uuidV4()}`,
    });
    await this.setState({
      name: null,
      sourceId: id,
      materialSources: [...this.state.materialSources, { value: id, name: author, url: '', platform }],
    });
  }

  onSync = async () => {
    const { sourceId, syncData, selectedMaterials, dataSource } = this.state;
    if (_.isUndefined(sourceId)) {
      Toast.show('请选择文稿源', Toast.Type.WARNING);
      return;
    }
    let items = (selectedMaterials || []);
    items = dataSource.filter((x) => { return items.includes(x.id); });
    if (_.isEmpty(items) && !_.isEmpty(syncData)) {
      items.push(syncData);
    }

    if (_.isEmpty(items)) {
      Toast.show('请选择搜索文章', Toast.Type.WARNING);
      return;
    }

    const newItems = [];
    for (let index = 0; index < items.length; index++) {
      const item = items[index];
      if (item.platform === 'xigua') {
        const resp = await fetch(`https://open-coon-xgurl-frontend-abmgurxpvu.cn-shanghai.fcapp.run?id=${item.id}`);
        const url = await resp.text();
        newItems.push({ ...item, url });
      } else {
        newItems.push(item);
      }
    }
    const promises = [];
    newItems.forEach((item) => {
      promises.push(this.props.createItem('material', {
        title: item.content_html,
        content: '',
        sourceUrl: item.url,
        passed: false,
        sourceId,
        pubDate: item.date_published,
        materialId: item.id,
        fetchStatus: 'pending',
        optimizeStatus: 'pending',
        collectCount: item.tags.collect_count || 0,
        commentCount: item.tags.comment_count || 0,
        diggCount: item.tags.digg_count || 0,
        shareCount: item.tags.share_count || 0,
      }));
    });
    const result = await Promise.all(promises);
    await this.props.createFetchJob({ materialIds: _.map(result, 'id') });
    await this.setState({ open: false, selectedMaterials: [], syncData: null, sourceId: null });
  }

  renderSelects = () => {
    return (
      <Select
        allowClear
        mode="multiple"
        value={this.state.platform}
        placeholder="平台"
        style={{ width: 320, marginBottom: 16 }}
        onChange={(e) => { return this.setState({ platform: e, page: 1, dataSource: [] }); }}
      >
        {_.map(PLATFORM_MAP, (v, k) => { return <Select.Option value={k}>{v}</Select.Option>; })}
      </Select>
    );
  }

  renderColumns = () => {
    return [
      {
        title: '标题',
        dataIndex: 'content_html',
        key: 'content_html',
        ellipsis: true,
      },
      {
        title: '链接',
        dataIndex: 'url',
        key: 'url',
        render: (txt, row) => {
          let url = txt;
          if (row.platform === 'ytb') {
            url = `https://www.youtube.com/watch?v=${row.id}`;
          }
          return (<a href={url} target="_blank" rel="noopener noreferrer">查看</a>);
        },
      },
      {
        title: '平台',
        dataIndex: 'platform',
        key: 'platform',
        align: 'center',
        render: (txt) => { return PLATFORM_MAP[txt]; },
      },
      ..._.map(ANALYSIS_MAP, (v, k) => {
        return { title: v, dataIndex: k, key: k, align: 'center', sorter: (a, b) => { return a[k] - b[k]; } };
      }),
      {
        title: '发布时间',
        dataIndex: 'date_published',
        key: 'date_published',
        render: (txt) => { return moment(txt).format('YYYY-MM-DD HH:mm'); },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        render: (txt, row) => {
          return (
            <a onClick={() => { return this.setState({ open: true, syncData: row }); }}>同步文稿</a>
          );
        },
      },
    ];
  }

  renderDropdown = (menu) => {
    return (
      <>
        {menu}
        <Divider style={{ margin: '8px 0' }} />
        <Space style={{ padding: '0 8px 4px' }} >
          <Input
            placeholder=" 请输入"
            ref={(el) => { this.refInput = el; }}
            value={this.state.name}
            onChange={(e) => { return this.setState({ name: e.target.value }); }}
          />
          <Button type="primary" icon={<PlusOutlined />} onClick={() => { return this.onAdd(); }}>
            新增
          </Button>
        </Space>
      </>
    );
  }

  renderDrawer = () => {
    return (
      <Drawer
        width="40vw"
        open={this.state.open}
        title="选择文稿源"
        maskClosable={false}
        className="article-drawer"
        onClose={() => { this.setState({ open: false }); }}
      >
        <Select
          showSearch
          value={this.state.sourceId}
          onChange={(e) => { return this.setState({ sourceId: e }); }}
          style={{ width: '100%' }}
          filterOption={(input, option) => { return option?.children?.includes(input); }}
          dropdownRender={this.renderDropdown}
        >
          {this.state.materialSources.map((w) => { return <Select.Option value={w.value}>{w?.name}</Select.Option>; })}
        </Select>
        <Divider />
        <Button type="primary" style={{ float: 'right' }} onClick={() => { return this.onSync(); }}>立即同步</Button>
      </Drawer>
    );
  }

  render = () => {
    const { open } = this.props;
    const { dataSource, selectedMaterials } = this.state;

    return (
      <Drawer
        width="66vw"
        open={open}
        title="即时搜索"
        maskClosable={false}
        className="article-drawer"
        onClose={() => { this.props.onClose(); }}
        extra={(
          <Button
            disabled={_.isEmpty(selectedMaterials)}
            type="primary"
            onClick={() => { return this.setState({ open: true, syncData: null }); }}
          >
            同步文稿
          </Button>
        )}
      >
        <FilterBar
          shouldShowSearchInput
          searchKeyWords={this.state.topic}
          onChange={(topic) => { this.setState({ topic, page: 1, dataSource: [] }); }}
          onSearch={() => { return this.onSearch(); }}
          placeholder="请输入话题"
          renderSelects={this.renderSelects}
        />
        <Spin spinning={this.state.spin}>
          <Table
            size="small"
            rowKey="id"
            rowSelection={{
              type: 'checkbox',
              selectedRowKeys: this.state.selectedMaterials,
              onChange: (keys) => { return this.setState({ selectedMaterials: keys }); },
            }}
            dataSource={dataSource}
            columns={this.renderColumns()}
            pagination={false}
          />
          {
            !_.isEmpty(dataSource) &&
            <Button
              style={{ float: 'right', marginTop: 10 }}
              onClick={async () => {
                await this.setState({ page: this.state.page + 1 });
                this.onSearch();
              }}
            >
              加载更多
            </Button>
          }
        </Spin>
        {this.state.open && this.renderDrawer()}
      </Drawer>
    );
  }
}
