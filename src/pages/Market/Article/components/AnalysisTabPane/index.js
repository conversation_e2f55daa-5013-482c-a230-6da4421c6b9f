import { DownloadOutlined, InboxOutlined, EyeOutlined } from '@ant-design/icons';
import { FilterBar, PaginationTable } from '~/components';
import Market from '~/engine/Market';
import { Button, Divider, Form, Input, Modal, Popover, Radio, Select, message, Upload, Drawer } from 'antd';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, WidthType } from 'docx';
import { saveAs } from 'file-saver';
import { marked } from 'marked';

import moment from 'moment';
import React, { Component } from 'react';
import { AliyunHelper } from '~/engine';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const PLATFROM_MAP = {
  douyin: '抖音',
  channels: '视频号'
};

export default class AnalysisTabPane extends Component {
  state = {
    analyses: [],
    analysesTotal: 0,
    analysesPagination: { pageIndex: 1, pageSize: 10 },
    loading: false,
    modalVisible: false,
    fileList: [],
    excelFileList: [],
    analysisType: 'live',
    title: '',
    prompts: '',
    submitting: false,
    // 搜索条件
    searchKeywords: '',
    searchAnalysisType: undefined,
    searchStatus: undefined,
    // Markdown预览
    previewVisible: false,
    previewContent: '',
    previewTitle: '',
    // 直播记录
    liveStreamRecords: [],
    selectedLiveRecord: null,
    loadingLiveRecords: false,
    // 额外选择的直播记录
    selectedAdditionalLiveRecords: [],
    // 新增output相关状态
    selectedOutput: '',
    newOutputText: '',
    newOutputName: '',
    editingOutputIndex: null,
    editingOutputText: '',
    editingOutputName: '',
    outputManageModalVisible: false,
    // 投放数据记录
    liveStreamCampaigns: [],
    selectedCampaigns: [],
    loadingCampaigns: false,
  };

  // 默认提示词配置
  defaultPrompts = {
    "live": {
      "prompt":"我们是一家知识付费类的mcn，这是我们孵化的财经主播的直播音频，以及整场直播的分钟级数据，对照数据和直播内容（包含并不限于文字内容、语音语调等），做出专业级直播复盘，输出模板如下：",
      "output": [{"name":"模板1",
        "content":"【[主播姓名]老师】[直播日期] - 直播复盘报告\\n主讲人： [主播姓名]\\n直播主题： [本次直播的核心主题或课程名称]\\n直播时长： [例如：2小时15分钟]\\n复盘制作人： [运营人员姓名]\\n\\n哈喽，[主播姓名]老师！\\n辛苦啦！本次直播圆满结束，我们一起对本次的表现进行一次全面的复盘。这份报告将帮助我们清晰地看到本次直播的亮点与机会点，为下一场直播的爆发积蓄能量！\\n一、 本次直播核心结论 (Executive Summary)\\n整体表现一览：\\n• 流量人气： [例如：本次流量相比上场提升20%，但用户停留时长略有下滑，需重点关注。]\\n• 商业转化： [例如：成交额创下新高！达到XXX元，主要得益于XXX课程的精准讲解和逼单话术。]\\n• 主播表现： [例如：控场能力稳定，与观众互动氛围热烈，特别是在讲解XXX知识点时，极具感染力。]\\n三大核心亮点 (Top 3 Wins)：\\n1. [例如：开场3分钟的“黄金三角”设计，成功吸引并留住了第一波流量。]\\n2. [例如：XXX课程的讲解逻辑清晰，用户好评如潮，评论区互动量达到峰值。]\\n3. [例如：首次尝试的“限时秒杀”活动，转化率高达XX%，效果显著。]\\n首要优化方向 (Top Priority for Next Time)：\\n• [例如：优化直播中段的内容节奏，通过增加互动问答或案例分享，提升用户停留时长。]\\nPart 1：流量与人气深度分析\\n目标：让更多对的人，愿意走进来，并留下来。\\n1. 流量来源与进房效率\\n| 数据指标 | 本次数据 | 上一场对比 | 同行均值对比 |\\n| 曝光量 (Impressions) | [具体数字] | ▲/▼ [百分比] | ▲/▼ [百分比] |\\n| 场观人数 (PV) | [具体数字] | ▲/▼ [百分比] | ▲/▼ [百分比] |\\n| 进房率 (CTR) | [具体百分比] | ▲/▼ [百分比] | ▲/▼ [百分比] |\\n数据解读与分析：\\n• 和上一场相比：本次进房率 [好/不好]，可能的原因是 [例如：直播间标题更吸引人 / 封面图点击欲望不强 / 开播时间段选择更佳 / 预热视频引流效果差]。\\n• 和同行相比：我们的进房率处于 [领先/持平/落后] 水平。优秀同行的标题/封面通常会 [举例说明同行的做法]。这说明我们在 [例如：痛点提炼、利益点展示] 方面还有提升空间。\\n2. 用户在线与内容节奏\\n峰值在线时刻：本次直播最高在线人数达到 [具体数字]，出现在 [具体时间点，如 20:45]。\\n峰值内容总结：当时，您正在讲解 [精确到当时讲解的具体知识点、案例、或互动环节，例如：“关于30岁后职业规划的三个误区”的第二个误区]。\\n成功原因分析：这个内容显然是用户的“心头好”！因为它 [例如：直击用户痛点 / 提供了极具价值的解决方案 / 观点新颖引发共鸣]。\\n3. 用户停留时长\\n| 数据指标 | 本次数据 | 上一场对比 | 同行均值对比 |\\n| 人均停留时长 | [例如：X分X秒] | ▲/▼ [百分比] | ▲/▼ [百分比] |\\n数据解读与分析：\\n• 和上一场相比：停留时长 [好/不好]，可能的原因是 [例如：开场吸引力足，但中段内容略显枯燥 / 互动环节设计少，用户参与感不强 / 课程讲解节奏过快，用户跟不上]。\\n• 和同行相比：我们停留时长 [领先/持平/落后]。优秀同行通常会通过 [例如：设置“内容彩蛋”、每15分钟进行一次抽奖互动、用故事串联知识点] 等方式来“钩住”用户。\\n4. 用户互动数据\\n| 数据指标 | 本次数据 | 上一场对比 | 同行均值对比 |\\n| 点赞数 | [具体数字] | ▲/▼ [百分比] | ▲/▼ [百分比] |\\n| 评论数 | [具体数字] | ▲/▼ [百分比] | ▲/▼ [百分比] |\\n| 新增关注 | [具体数字] | ▲/▼ [百分比] | ▲/▼ [百分比] |\\n| 加入粉丝团 | [具体数字] | ▲/▼ [百分比] | ▲/▼ [百分比] |\\n数据解读与分析：\\n• 和上一场相比：互动数据 [好/不好]，可能的原因是 [例如：本场主动引导点赞/评论的话术更多 / 设置的提问更具开放性 / 福袋或粉丝团福利吸引力不足]。\\n• 和同行相比：我们的互动氛围 [热烈/一般/偏冷]。这提示我们可以在 [例如：引导话术的设计、评论区问题的选择性回复、互动工具的使用] 上做更多尝试。\\n▶ 流量部分整体建议：\\n1. 引流端：下次我们可以尝试 [例如：在预热视频中埋下更强的悬念钩子，并在标题中直接点出直播解决的核心痛点]。\\n2. 承接端：建议优化直播中段 [例如：XX分钟处] 的内容，可以加入一个 [例如：学员成功案例的短分享]，打破平淡节奏，拉升用户停留。\\nPart 2：商业化变现深度分析\\n目标：让信任您的用户，愿意为价值买单。\\n1. 核心成交数据\\n| 数据指标 | 本次数据 | 上一场对比 |\\n| 成交金额 (GMV) | ¥ [具体金额] | ▲/▼ [百分比] |\\n| 总订单数 | [具体数字] | ▲/▼ [百分比] |\\n| 客单价 | ¥ [具体金额] | ▲/▼ [百分比] |\\n下单高峰分析：\\n• 本场下单最集中的时间点是 [例如：21:10-21:20]。\\n• 当时口播内容复盘：您当时的核心口播/逼单话术是 [例如：“这是我们全年最低价，直播间专属，错过今天再等一年，并且前50名下单的同学，我还额外赠送我的私人笔记...”]。\\n数据解读与分析：\\n• 本次成交额 [好/不好]，关键原因在于 [例如：主推课程与用户需求高度匹配 / 价格锚点设置合理 / 赠品策略极具吸引力 / 紧迫感和稀缺感营造得不够]。\\n2. 课程讲解与转化效率\\n| 数据指标 | 本次数据 | 上一场对比 |\\n| 商品曝光-点击率 | [具体百分比] | ▲/▼ [百分比] |\\n| 点击-成交转化率 | [具体百分比] | ▲/▼ [百分比] |\\n商品曝光-点击率分析：\\n• 本次数据 [好/不好]，原因可能是 [例如：讲解时对课程亮点的提炼非常到位，激发了用户的好奇心 / 讲解时展示的PPT或课件不够吸引人，用户无感]。\\n点击-成交转化率分析：\\n• 本次数据 [好/不好]，原因可能是 [例如：用户点击进入后，发现课程详情页信息不足以支撑决策 / 逼单环节的话术和节奏恰到好处，打消了用户的最后顾虑 / 支付流程引导不清晰]。\\n▶ 商业化部分整体建议：\\n1. 产品讲解：建议将本次峰值成交时段的口播话术提炼成SOP，作为我们的“黄金话术”固化下来。\\n2. 转化优化：下次我们可以尝试 [例如：在逼单环节，增加一个“学员评价”的截图展示，用真实口碑打动犹豫的用户]。\\nPart 3：主播表现复盘与赋能\\n目标：发挥您的长处，优化您的表达，打造更强的个人IP。\\n1. 您的闪光点 (Strengths) ✨\\n• 专业性： [例如：在讲解“费曼学习法”时，引经据典，深入浅出，展现了极强的专业功底，评论区都在刷“老师牛逼！”]。\\n• 互动感： [例如：能敏锐捕捉评论区的优质问题，并进行即兴、真诚的回答，有效拉近了与用户的距离]。\\n• 表现力： [例如：本次直播全程精力充沛，语言风趣幽默，尤其是在分享个人经历时，感染力十足]。\\n• 节奏感： [例如：开场、讲解、互动、逼单的环节切换流畅自然，体现了优秀的控场能力]。\\n2. 我们可以一起优化的方向 (Areas for Optimization) 🚀\\n• 现状描述： [例如：在讲解A课程到B课程的过渡时，衔接略显生硬，感觉像是突然切换话题。]\\n优化建议：下次我们可以共同设计一个“承上启下”的案例或小故事，让课程之间的逻辑关联更强，过渡更丝滑。\\n• 现状描述： [例如：面对评论区的一些负面或质疑声音，有时会显得有些急于解释。]\\n优化建议：我们可以准备一套“高情商回应SOP”，用自嘲或幽默的方式化解，既展现格局，又能将话题引导回对我们有利的方向。\\n• 现状描述： [例如：直播后半段，引导用户点赞、关注的话术重复率较高。]\\n优化建议：我们可以一起设计3-5套不同的互动引导话术，在不同环节穿插使用，保持用户的新鲜感。\\nPart 4：下次直播行动计划 (Action Plan)\\n基于以上复盘，我们为下一次直播制定了如下行动计划，我们一起加油！\\n| 优化模块 | 具体行动点 | 负责人 | 完成时限 |\\n| 流量优化 | 1. 优化直播标题，突出“3个方法”解决“XX焦虑”。 | 运营 & 主播 | [日期] |\\n|           | 2. 设计一个时长30秒的悬念式预热短视频。 | 运营 | [日期] |\\n| 内容优化 | 1. 在直播中段（约45分钟处）加入学员案例分享。 | 主播 | [日期] |\\n|           | 2. 准备3个新的互动引导话术。 | 主播 & 运营 | [日期] |\\n| 转化优化 | 1. 制作一张包含学员好评的逼单图卡。 | 运营 | [日期] |\\n|           | 2. 演练高情商回应质疑的话术。 | 主播 | [日期] |\\n再次感谢您的辛苦付出！复盘是为了更好地前行。期待我们下一场直播，再创佳绩！\\n[您的MCN公司名称]\\n[复盘报告生成日期]"}]
    },
    "video": {
      "prompt":"我们是一家知识付费类的mcn，这是我们孵化的财经主播以及视频的各项分钟级数据，结合数据专业级复盘该短视频（包含并不限于长相、人设、内容、表达、视觉等等）",
      "output":[]
    }
  };

  componentDidMount() {
    this.fetchAnalyses();
    // this.fetchDefaultPrompts();
  }

  // 获取远程提示词配置
  fetchDefaultPrompts = async () => {
    try {
      const timestamp = new Date().getTime();
      const response = await fetch(`https://video-clip.oss-cn-shanghai.aliyuncs.com/upload/materialAnalysisPrompts.json?t=${timestamp}`);
      if (response.ok) {
        const data = await response.json();
        this.defaultPrompts = data;
        this.setState({
          prompts: this.defaultPrompts[this.state.analysisType].prompt,
        });
        console.log('获取远程提示词配置成功:', data);
      }
    } catch (error) {
      console.error('获取远程提示词配置失败:', error);
      // 如果获取失败，使用默认配置
    }
  };

  // 更新远程提示词配置
  updateDefaultPrompts = async (newPrompts) => {
    try {
      const currentPrompts = { ...this.defaultPrompts, ...newPrompts };
      const blob = new Blob([JSON.stringify(currentPrompts, null, 2)], { type: 'application/json' });
      await AliyunHelper.clipsUploadImage(blob, () => {}, {
        fileName: 'materialAnalysisPrompts',
        fileType: '.json'
      });
      console.log('更新远程提示词配置成功');
    } catch (error) {
      console.error('更新远程提示词配置失败:', error);
    }
  };

  fetchAnalyses = async (params = {}) => {
    try {
      this.setState({ loading: true });

      const { analysesPagination, searchKeywords, searchAnalysisType, searchStatus } = this.state;
      const { pageIndex, pageSize } = analysesPagination || { pageIndex: 1, pageSize: 10 };

      const requestParams = {
        pagination: {
          skip: (pageIndex - 1) * pageSize,
          limit: pageSize,
        },
        title: searchKeywords,
        analysisType: searchAnalysisType,
        status: searchStatus,
        ...params,
      };

      const response = await Market.fetchMaterialAnalyses(requestParams);

      if (response && response.items) {
        this.setState({
          analyses: response.items,
          analysesTotal: response.total || response.items.length,
        });
      }
    } catch (error) {
      message.error('获取复盘列表失败');
      console.error('获取复盘列表失败:', error);
    } finally {
      this.setState({ loading: false });
    }
  };

  showModal = () => {
    const defaultAnalysisType = 'live';
    this.setState({
      modalVisible: true,
      fileList: [],
      excelFileList: [],
      analysisType: defaultAnalysisType,
      title: '',
      prompts: this.defaultPrompts[defaultAnalysisType].prompt,
      selectedLiveRecord: null,
      selectedAdditionalLiveRecords: [],
      selectedCampaigns: [],
      // 清空输出模板选择
      selectedOutput: '',
      // 清空编辑状态
      editingOutputIndex: null,
      editingOutputText: '',
      editingOutputName: '',
      newOutputText: '',
      newOutputName: '',
    }, () => {
      // 打开模态框后立即获取直播记录和投放数据
      this.fetchLiveStreamRecords();
      this.fetchLiveStreamCampaigns();
      this.fetchDefaultPrompts();
    });
  };

  handleCancel = () => {
    this.setState({ modalVisible: false });
  };

  handleSubmit = async () => {
    const { analysisType, title, prompts, fileList, excelFileList, selectedLiveRecord } = this.state;

    if (!title) {
      message.error('请输入复盘名称');
      return;
    }

    // if (fileList.length === 0) {
    //   message.error('请上传音频或视频文件');
    //   return;
    // }

    // if (excelFileList.length === 0) {
    //   message.error('请上传至少一个CSV文件');
    //   return;
    // }

    try {
      this.setState({ submitting: true });

      const fileUrl = fileList[0].url || '';
      const excelFiles = excelFileList.map(file => (file.url || file.response?.url));

      // 获取选中的输出模板内容
      const selectedOutputContent = this.getSelectedOutputContent();

      const params = {
        analysisType,
        title,
        prompts: selectedOutputContent ?
          `${prompts}\n${selectedOutputContent}` :
          prompts,
        excelFiles,
        usageMetadata: {},
      };

      // 根据类型设置音频或视频URL
      if (analysisType === 'live') {
        // 如果有选定的直播记录，优先使用其回放音频URL
        if (selectedLiveRecord && selectedLiveRecord.liveReplayAudioUrl) {
          params.audioUrl = selectedLiveRecord.liveReplayAudioUrl;
        } else {
          params.audioUrl = fileUrl;
        }
      } else {
        params.videoUrl = fileUrl;
      }

      // 检查用户是否修改了提示词
      if (prompts !== this.defaultPrompts[analysisType].prompt) {
        // 用户修改了提示词，更新远程配置
        const newPrompts = { ...this.defaultPrompts };
        newPrompts[analysisType].prompt = prompts;
        await this.updateDefaultPrompts(newPrompts);
      }

      await Market.createMaterialAnalysis(params);
      message.success('复盘创建成功');
      this.setState({ modalVisible: false });
      this.fetchAnalyses();
    } catch (error) {
      message.error('创建复盘失败');
      console.error('创建复盘失败:', error);
    } finally {
      this.setState({ submitting: false });
    }
  };

  // 解析 HTML 并转换为 docx 段落的辅助函数
  parseHtmlToDocxElements = (htmlString) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, 'text/html');
    const elements = [];

    const processNode = (node) => {
      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent.trim();
        if (text) {
          return new TextRun({ text });
        }
        return null;
      }

      if (node.nodeType === Node.ELEMENT_NODE) {
        const tagName = node.tagName.toLowerCase();
        const textContent = node.textContent.trim();

        switch (tagName) {
          case 'h1':
            return new Paragraph({
              children: [new TextRun({ text: textContent, bold: true, size: 32 })],
              heading: HeadingLevel.HEADING_1,
              spacing: { after: 400 },
            });
          case 'h2':
            return new Paragraph({
              children: [new TextRun({ text: textContent, bold: true, size: 28 })],
              heading: HeadingLevel.HEADING_2,
              spacing: { after: 300 },
            });
          case 'h3':
            return new Paragraph({
              children: [new TextRun({ text: textContent, bold: true, size: 26 })],
              heading: HeadingLevel.HEADING_3,
              spacing: { after: 200 },
            });
          case 'p':
            if (textContent) {
              const children = this.processInlineContent(node);
              return new Paragraph({
                children: children.length > 0 ? children : [new TextRun({ text: textContent })],
                spacing: { after: 200 },
              });
            }
            return new Paragraph({ children: [new TextRun({ text: ' ' })] });
          case 'strong':
          case 'b':
            return new TextRun({ text: textContent, bold: true });
          case 'em':
          case 'i':
            return new TextRun({ text: textContent, italics: true });
          case 'code':
            return new TextRun({
              text: textContent,
              font: 'Consolas',
              highlight: 'yellow'
            });
          case 'blockquote':
            return new Paragraph({
              children: [new TextRun({ text: textContent, italics: true })],
              indent: { left: 720 },
              spacing: { after: 200 },
            });
          case 'ul':
          case 'ol':
            return this.processListNode(node, tagName);
          case 'table':
            return this.processTable(node);
          case 'hr':
            return new Paragraph({
              children: [new TextRun({ text: '─'.repeat(50) })],
              spacing: { before: 200, after: 200 },
            });
          default:
            if (textContent) {
              return new Paragraph({
                children: [new TextRun({ text: textContent })],
                spacing: { after: 200 },
              });
            }
            return null;
        }
      }
      return null;
    };

    const processInlineElements = (node, children) => {
      Array.from(node.childNodes).forEach(child => {
        if (child.nodeType === Node.TEXT_NODE) {
          const text = child.textContent;
          if (text.trim()) {
            children.push(new TextRun({ text }));
          }
        } else if (child.nodeType === Node.ELEMENT_NODE) {
          const tagName = child.tagName.toLowerCase();
          const textContent = child.textContent;

          switch (tagName) {
            case 'strong':
            case 'b':
              children.push(new TextRun({ text: textContent, bold: true }));
              break;
            case 'em':
            case 'i':
              children.push(new TextRun({ text: textContent, italics: true }));
              break;
            case 'code':
              children.push(new TextRun({
                text: textContent,
                font: 'Consolas',
                highlight: 'yellow'
              }));
              break;
            default:
              children.push(new TextRun({ text: textContent }));
              break;
          }
        }
      });
    };

    this.processInlineElements = processInlineElements;

    Array.from(doc.body.children).forEach(node => {
      const element = processNode(node);
      if (element) {
        if (Array.isArray(element)) {
          elements.push(...element);
        } else {
          elements.push(element);
        }
      }
    });

    return elements;
  };

  // 处理复杂列表的辅助方法
  processListNode = (listNode, listType, indentLevel = 0) => {
    const listItems = [];
    const baseIndent = 720 + (indentLevel * 360); // 每层缩进增加360

    Array.from(listNode.children).forEach((li, index) => {
      if (li.tagName.toLowerCase() === 'li') {
        const bullet = listType === 'ul' ? '• ' : `${index + 1}. `;

        // 处理列表项中的内容
        const liElements = this.processListItemContent(li, bullet, baseIndent, indentLevel);
        listItems.push(...liElements);
      }
    });

    return listItems;
  };

  // 处理内联元素并保持格式
  processInlineContent = (node) => {
    const children = [];

    Array.from(node.childNodes).forEach(child => {
      if (child.nodeType === Node.TEXT_NODE) {
        const text = child.textContent;
        if (text.trim()) {
          children.push(new TextRun({ text }));
        }
      } else if (child.nodeType === Node.ELEMENT_NODE) {
        const tagName = child.tagName.toLowerCase();
        const textContent = child.textContent;

        switch (tagName) {
          case 'strong':
          case 'b':
            children.push(new TextRun({ text: textContent, bold: true }));
            break;
          case 'em':
          case 'i':
            children.push(new TextRun({ text: textContent, italics: true }));
            break;
          case 'code':
            children.push(new TextRun({
              text: textContent,
              font: 'Consolas',
              highlight: 'yellow'
            }));
            break;
          default:
            children.push(new TextRun({ text: textContent }));
            break;
        }
      }
    });

    return children;
  };

  // 处理列表项内容（可能包含段落、子列表等）
  processListItemContent = (liNode, bullet, baseIndent, indentLevel) => {
    const elements = [];
    let hasMainContent = false;

    // 检查是否有段落作为第一个子元素
    const firstChild = liNode.children[0];
    if (firstChild && firstChild.tagName.toLowerCase() === 'p') {
      // 处理第一个段落，添加项目符号
      const children = this.processInlineContent(firstChild);
      if (children.length > 0) {
        // 为第一个文本元素添加项目符号
        if (children[0] && children[0].text) {
          const originalText = children[0].text;
          children[0] = new TextRun({
            text: bullet + originalText,
            bold: children[0].bold || false,
            italics: children[0].italics || false,
            font: children[0].font,
            highlight: children[0].highlight,
          });
        }
        elements.push(new Paragraph({
          children,
          indent: { left: baseIndent },
          spacing: { after: 100 },
        }));
        hasMainContent = true;
      }
    }

    // 处理其他子元素
    Array.from(liNode.children).forEach((child, index) => {
      if (child.nodeType === Node.ELEMENT_NODE) {
        const tagName = child.tagName.toLowerCase();

        switch (tagName) {
          case 'p':
            // 跳过第一个段落（已经处理过了）
            if (index === 0 && hasMainContent) {
              break;
            }

            const children = this.processInlineContent(child);
            if (children.length > 0) {
              const prefix = !hasMainContent ? bullet : '';
              if (!hasMainContent && children[0] && children[0].text) {
                const originalText = children[0].text;
                children[0] = new TextRun({
                  text: prefix + originalText,
                  bold: children[0].bold || false,
                  italics: children[0].italics || false,
                  font: children[0].font,
                  highlight: children[0].highlight,
                });
              }
              elements.push(new Paragraph({
                children,
                indent: { left: baseIndent },
                spacing: { after: 100 },
              }));
              hasMainContent = true;
            }
            break;
          case 'ul':
          case 'ol':
            // 处理嵌套列表
            const nestedItems = this.processListNode(child, tagName, indentLevel + 1);
            elements.push(...nestedItems);
            break;
        }
      }
    });

    // 如果没有处理任何内容，使用默认处理
    if (elements.length === 0) {
      const text = liNode.textContent.trim();
      if (text) {
        elements.push(new Paragraph({
          children: [new TextRun({ text: bullet + text })],
          indent: { left: baseIndent },
          spacing: { after: 100 },
        }));
      }
    }

    return elements;
  };

  // 获取节点的直接文本内容（不包括子元素）
  getDirectTextContent = (node) => {
    let text = '';
    Array.from(node.childNodes).forEach(child => {
      if (child.nodeType === Node.TEXT_NODE) {
        text += child.textContent;
      }
    });
    return text.trim();
  };

  // 处理节点中的内联元素（只处理直接子元素）
  processInlineElementsInNode = (node, children, directOnly = false) => {
    Array.from(directOnly ? node.childNodes : node.childNodes).forEach(child => {
      if (child.nodeType === Node.TEXT_NODE) {
        const text = child.textContent.trim();
        if (text) {
          children.push(new TextRun({ text }));
        }
      } else if (child.nodeType === Node.ELEMENT_NODE && !directOnly) {
        const tagName = child.tagName.toLowerCase();
        const textContent = child.textContent.trim();

        switch (tagName) {
          case 'strong':
          case 'b':
            children.push(new TextRun({ text: textContent, bold: true }));
            break;
          case 'em':
          case 'i':
            children.push(new TextRun({ text: textContent, italics: true }));
            break;
          case 'code':
            children.push(new TextRun({
              text: textContent,
              font: 'Consolas',
              highlight: 'yellow'
            }));
            break;
          default:
            children.push(new TextRun({ text: textContent }));
            break;
        }
      } else if (child.nodeType === Node.ELEMENT_NODE && directOnly) {
        // 对于直接子元素，递归处理内联格式
        const tagName = child.tagName.toLowerCase();
        const textContent = child.textContent.trim();

        switch (tagName) {
          case 'strong':
          case 'b':
            children.push(new TextRun({ text: textContent, bold: true }));
            break;
          case 'em':
          case 'i':
            children.push(new TextRun({ text: textContent, italics: true }));
            break;
          case 'code':
            children.push(new TextRun({
              text: textContent,
              font: 'Consolas',
              highlight: 'yellow'
            }));
            break;
          case 'p':
          case 'ul':
          case 'ol':
            // 这些是块级元素，不在这里处理
            break;
          default:
            children.push(new TextRun({ text: textContent }));
            break;
        }
      }
    });
  };

  // 处理表格的辅助方法
  processTable = (tableNode) => {
    const rows = [];
    const tableRows = Array.from(tableNode.querySelectorAll('tr'));

    tableRows.forEach((tr, rowIndex) => {
      const cells = Array.from(tr.querySelectorAll('td, th'));
      const tableCells = cells.map(cell => {
        const isHeader = cell.tagName.toLowerCase() === 'th';
        return new TableCell({
          children: [
            new Paragraph({
              children: [
                new TextRun({
                  text: cell.textContent.trim() || ' ',
                  bold: isHeader,
                  size: isHeader ? 24 : 22,
                })
              ],
              spacing: { before: 100, after: 100 },
            })
          ],
          width: {
            size: 100 / cells.length,
            type: WidthType.PERCENTAGE,
          },
          margins: {
            top: 100,
            bottom: 100,
            left: 100,
            right: 100,
          },
        });
      });

      if (tableCells.length > 0) {
        rows.push(new TableRow({
          children: tableCells,
        }));
      }
    });

    if (rows.length > 0) {
      return new Table({
        rows: rows,
        width: {
          size: 100,
          type: WidthType.PERCENTAGE,
        },
        margins: {
          top: 200,
          bottom: 200,
        },
      });
    }

    return null;
  };

  handleDownloadMarkdown = async (record) => {
    if (!record.markdownContent) {
      message.error('复盘内容为空');
      return;
    }

    try {
      // 第一步：将 Markdown 转换为 HTML
      const htmlContent = marked(record.markdownContent, {
        breaks: true,
        gfm: true,
      });

      // 第二步：解析 HTML 并转换为 docx 元素
      const docxElements = this.parseHtmlToDocxElements(htmlContent);

      // 第三步：创建 Word 文档
      const doc = new Document({
        sections: [
          {
            properties: {},
            children: [
              // 文档标题
              new Paragraph({
                children: [
                  new TextRun({
                    text: record.title,
                    bold: true,
                    size: 36,
                  }),
                ],
                heading: HeadingLevel.TITLE,
                spacing: { after: 600 },
              }),
              // 分隔线
              new Paragraph({
                children: [new TextRun({ text: '' })],
                spacing: { after: 400 },
              }),
              // 解析后的内容
              ...docxElements,
            ],
          },
        ],
      });

      // 第四步：生成并下载文档
      const blob = await Packer.toBlob(doc);
      saveAs(blob, `${record.title}.docx`);
      message.success('文档下载成功');
    } catch (error) {
      message.error('下载失败');
      console.error('下载失败:', error);
    }
  };

  showMarkdownPreview = (record) => {
    if (!record.markdownContent) {
      message.error('复盘内容为空');
      return;
    }

    this.setState({
      previewVisible: true,
      previewContent: record.markdownContent,
      previewTitle: record.title,
    });
  };

  closeMarkdownPreview = () => {
    this.setState({
      previewVisible: false,
      previewContent: '',
      previewTitle: '',
    });
  };

  renderColumns = () => {
    return [
      {
        title: '名称',
        dataIndex: 'title',
        key: 'title',
        ellipsis: true,
        width: '20%',
      },
      {
        title: '类型',
        dataIndex: 'analysisType',
        key: 'analysisType',
        render: (text) => text === 'live' ? '直播复盘' : '视频复盘',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (text) => {
          const statusMap = {
            pending: '待处理',
            running: '处理中',
            done: '已完成',
            failed: '失败',
          };
          return statusMap[text] || text;
        },
      },
      {
        title: '费用',
        dataIndex: 'usageMetadata',
        key: 'cost',
        align: 'center',
        render: (usageMetadata) => {
          if (!usageMetadata) return '-';

          // 计算费用，使用不同类型token的不同费率
          const promptCost = (usageMetadata.promptTokenCount || 0) / 1000 * 0.00125;
          const thoughtsCost = (usageMetadata.thoughtsTokenCount || 0) / 1000 * 0.01;
          const candidatesCost = (usageMetadata.candidatesTokenCount || 0) / 1000 * 0.01;
          const totalCost = (promptCost + thoughtsCost + candidatesCost).toFixed(4);

          return (
            <Popover
              content={this.renderCostDetails(usageMetadata)}
              title="费用详情"
              trigger="hover"
            >
              <span>${totalCost}</span>
            </Popover>
          );
        },
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        render: (text) => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-',
      },
      {
        title: '操作',
        key: 'action',
        render: (_, record) => (
          <>
            <Button
              type="link"
              disabled={!record.markdownContent || record.status !== 'done'}
              onClick={() => this.showMarkdownPreview(record)}
              icon={<EyeOutlined />}
            >
              查看复盘
            </Button>
            <Divider type="vertical" />
            <Button
              type="link"
              disabled={!record.markdownContent || record.status !== 'done'}
              onClick={() => this.handleDownloadMarkdown(record)}
              icon={<DownloadOutlined />}
            >
              下载复盘
            </Button>
          </>
        ),
      },
    ];
  };

  renderButtons = () => {
    return [
      <Button
        key="create"
        type="primary"
        onClick={this.showModal}
      >
        新建复盘
      </Button>,
    ];
  };

  renderSelects = () => {
    return [
      <Select
        key="analysisType"
        placeholder="选择类型"
        style={{ width: 150, marginRight: 8, marginBottom: 16 }}
        allowClear
        onChange={(value) => {
          this.setState({
            searchAnalysisType: value,
            analysesPagination: { ...this.state.analysesPagination, pageIndex: 1 }
          }, () => {
            this.fetchAnalyses();
          });
        }}
      >
        <Select.Option value="live">直播复盘</Select.Option>
        <Select.Option value="video">视频复盘</Select.Option>
      </Select>,
      <Select
        key="status"
        placeholder="选择状态"
        style={{ width: 150, marginBottom: 16 }}
        allowClear
        onChange={(value) => {
          this.setState({
            searchStatus: value,
            analysesPagination: { ...this.state.analysesPagination, pageIndex: 1 }
          }, () => {
            this.fetchAnalyses();
          });
        }}
      >
        <Select.Option value="pending">待处理</Select.Option>
        <Select.Option value="running">处理中</Select.Option>
        <Select.Option value="done">已完成</Select.Option>
        <Select.Option value="failed">失败</Select.Option>
      </Select>
    ];
  };

  renderCostDetails = (usageMetadata) => {
    if (!usageMetadata) return null;

    const {
      totalTokenCount = 0,
      promptTokenCount = 0,
      thoughtsTokenCount = 0,
      candidatesTokenCount = 0,
      promptTokensDetails = []
    } = usageMetadata;

    // 计算各部分费用，使用不同类型token的不同费率
    const promptCost = (promptTokenCount / 1000 * 0.00125).toFixed(4);
    const thoughtsCost = (thoughtsTokenCount / 1000 * 0.01).toFixed(4);
    const candidatesCost = (candidatesTokenCount / 1000 * 0.01).toFixed(4);
    const totalCost = (
      parseFloat(promptCost) +
      parseFloat(thoughtsCost) +
      parseFloat(candidatesCost)
    ).toFixed(4);

    return (
      <div style={{ width: 300 }}>
        <p><strong>总Token数:</strong> {totalTokenCount} (${totalCost})</p>
        <p><strong>提示Token数:</strong> {promptTokenCount} (${promptCost})</p>
        <p><strong>思考Token数:</strong> {thoughtsTokenCount} (${thoughtsCost})</p>
        <p><strong>候选Token数:</strong> {candidatesTokenCount} (${candidatesCost})</p>

        {promptTokensDetails.length > 0 && (
          <>
            <Divider style={{ margin: '8px 0' }} />
            <p><strong>提示Token详情:</strong></p>
            {promptTokensDetails.map((detail, index) => {
              // 根据不同模态计算费用
              const modalityCost = (detail.tokenCount / 1000 * 0.00125).toFixed(4);
              return (
                <p key={index}>
                  {detail.modality}: {detail.tokenCount} Tokens (${modalityCost})
                </p>
              );
            })}
          </>
        )}
      </div>
    );
  };

  // 获取直播记录列表
  fetchLiveStreamRecords = async () => {
    try {
      this.setState({ loadingLiveRecords: true });

      const response = await Market.fetchLiveStreamRecords({
        skip: 0,
        limit: 100
      });

      console.log('获取直播记录成功:', response);

      this.setState({
        liveStreamRecords: response.items || [],
        loadingLiveRecords: false
      });
    } catch (error) {
      console.error('获取直播记录失败:', error);
      message.error('获取直播记录失败');
      this.setState({ loadingLiveRecords: false });
    }
  };

  // 获取投放数据记录列表
  fetchLiveStreamCampaigns = async () => {
    try {
      this.setState({ loadingCampaigns: true });

      const response = await Market.fetchLiveStreamCampaigns({
        skip: 0,
        limit: 100
      });

      console.log('获取投放数据记录成功:', response);

      this.setState({
        liveStreamCampaigns: response.items || [],
        loadingCampaigns: false
      });
    } catch (error) {
      console.error('获取投放数据记录失败:', error);
      message.error('获取投放数据记录失败');
      this.setState({ loadingCampaigns: false });
    }
  };

  // 处理选择直播记录
  handleSelectLiveRecord = (recordId) => {
    const { liveStreamRecords, selectedAdditionalLiveRecords } = this.state;
    const selectedRecord = liveStreamRecords.find(record => record.id === recordId);
    console.log(selectedRecord);

    if (selectedRecord) {
      // 格式化日期为YYYYMMDD
      const liveDate = moment(selectedRecord.liveTime).format('YYYYMMDD');
      const formattedTitle = `${selectedRecord.anchorName}-${selectedRecord.liveTitle}-${liveDate}`;

      // 主直播记录的数据文件
      const mainDataFiles = selectedRecord.liveDataUrls && selectedRecord.liveDataUrls.length > 0
        ? selectedRecord.liveDataUrls.map((url, index) => ({
            uid: `live-data-${index}`,
            name: `${formattedTitle}-数据${index + 1}.md`,
            status: 'done',
            url: url,
          }))
        : [];

      // 保留已选择的额外直播记录数据
      const additionalDataFiles = [];
      liveStreamRecords
        .filter(record =>
          selectedAdditionalLiveRecords.includes(record.id) &&
          record.liveDataUrls &&
          record.liveDataUrls.length > 0 &&
          record.id !== recordId // 排除当前选中的主记录
        )
        .forEach(record => {
          const liveDate = moment(record.liveTime).format('YYYYMMDD');
          const recordTitle = `${record.anchorName}-${record.liveTitle}-${liveDate}`;

          record.liveDataUrls.forEach((url, index) => {
            additionalDataFiles.push({
              uid: `additional-data-${record.id}-${index}`,
              name: `${recordTitle}-数据${index + 1}.md`,
              status: 'done',
              url: url,
            });
          });
        });

      this.setState({
        selectedLiveRecord: selectedRecord,
        title: formattedTitle,
        // 合并主数据和额外数据
        excelFileList: [...mainDataFiles, ...additionalDataFiles],
        // 如果有直播回放URL，设置为fileList
        fileList: selectedRecord.liveReplayAudioUrl
          ? [{
              uid: 'live-replay',
              name: '直播音频.mp3',
              status: 'done',
              url: selectedRecord.liveReplayAudioUrl,
            }]
          : [],
        // 更新额外选择的记录，排除当前选中的主记录
        selectedAdditionalLiveRecords: selectedAdditionalLiveRecords.filter(id => id !== recordId),
      });
    }
  };

  // 处理选择额外的直播记录
  handleSelectAdditionalLiveRecords = (recordIds) => {
    const { liveStreamRecords, selectedLiveRecord } = this.state;

    // 过滤掉当前选中的主直播记录
    const filteredRecordIds = recordIds.filter(id =>
      !selectedLiveRecord || id !== selectedLiveRecord.id
    );

    const selectedRecords = liveStreamRecords.filter(record =>
      filteredRecordIds.includes(record.id)
    );

    // 生成额外的数据文件列表
    const additionalDataFiles = [];
    selectedRecords.forEach(record => {
      if (record.liveDataUrls && record.liveDataUrls.length > 0) {
        const liveDate = moment(record.liveTime).format('YYYYMMDD');
        const formattedTitle = `${record.anchorName}-${record.liveTitle}-${liveDate}`;

        record.liveDataUrls.forEach((url, index) => {
          additionalDataFiles.push({
            uid: `additional-data-${record.id}-${index}`,
            name: `${formattedTitle}-数据${index + 1}.md`,
            status: 'done',
            url: url,
          });
        });
      }
    });

    // 更新excelFileList，保留主直播记录的数据和投放数据，添加额外的数据
    const { excelFileList } = this.state;
    const mainDataFiles = excelFileList.filter(file =>
      file.uid.startsWith('live-data-') && !file.uid.startsWith('additional-data-')
    );
    const campaignDataFiles = excelFileList.filter(file =>
      file.uid.startsWith('campaign-')
    );

    const newExcelFileList = [...mainDataFiles, ...campaignDataFiles, ...additionalDataFiles];

    this.setState({
      selectedAdditionalLiveRecords: filteredRecordIds,
      excelFileList: newExcelFileList,
    });
  };

  // 处理选择投放数据
  handleSelectCampaigns = (campaignIds) => {
    const { liveStreamCampaigns } = this.state;

    const selectedCampaigns = liveStreamCampaigns.filter(campaign =>
      campaignIds.includes(campaign.id)
    );

    // 生成投放数据文件列表
    const campaignDataFiles = [];
    selectedCampaigns.forEach(campaign => {
      if (campaign.campaignData) {
        const liveDate = moment(campaign.liveTime).format('YYYYMMDD');
        const baseTitle = `${campaign.anchorName}-${campaign.liveTitle}-${liveDate}`;

        const { campaignData } = campaign;

        // 添加summary文件
        if (campaignData.summary) {
          campaignDataFiles.push({
            uid: `campaign-summary-${campaign.id}`,
            name: `${baseTitle}-投放汇总.csv`,
            status: 'done',
            url: campaignData.summary,
          });
        }

        // 添加planSummary文件
        if (campaignData.planSummary) {
          campaignDataFiles.push({
            uid: `campaign-plan-summary-${campaign.id}`,
            name: `${baseTitle}-计划汇总.csv`,
            status: 'done',
            url: campaignData.planSummary,
          });
        }

        // 添加planDetail文件
        if (campaignData.planDetail && Array.isArray(campaignData.planDetail)) {
          campaignData.planDetail.forEach((detail, index) => {
            if (detail.data) {
              campaignDataFiles.push({
                uid: `campaign-plan-detail-data-${campaign.id}-${index}`,
                name: `${baseTitle}-计划详情数据${index + 1}.csv`,
                status: 'done',
                url: detail.data,
              });
            }
            if (detail.logs) {
              campaignDataFiles.push({
                uid: `campaign-plan-detail-logs-${campaign.id}-${index}`,
                name: `${baseTitle}-计划详情日志${index + 1}.md`,
                status: 'done',
                url: detail.logs,
              });
            }
            if (detail.detail) {
              campaignDataFiles.push({
                uid: `campaign-plan-detail-detail-${campaign.id}-${index}`,
                name: `${baseTitle}-计划详情${index + 1}.md`,
                status: 'done',
                url: detail.detail,
              });
            }
          });
        }

        // 添加material文件
        if (campaignData.material) {
          if (campaignData.material.live) {
            campaignDataFiles.push({
              uid: `campaign-material-live-${campaign.id}`,
              name: `${baseTitle}-素材直播.csv`,
              status: 'done',
              url: campaignData.material.live,
            });
          }
          if (campaignData.material.video) {
            campaignDataFiles.push({
              uid: `campaign-material-video-${campaign.id}`,
              name: `${baseTitle}-素材视频.csv`,
              status: 'done',
              url: campaignData.material.video,
            });
          }
        }

        // 添加crowd文件
        if (campaignData.crowd) {
          Object.entries(campaignData.crowd).forEach(([key, url]) => {
            if (url) {
              const crowdNames = {
                province: '省份',
                city: '城市',
                gender: '性别',
                platform: '平台',
                age: '年龄',
                ac: 'AC',
                category: '类别',
                industry: '行业',
                eightAudience: '八大人群'
              };
              campaignDataFiles.push({
                uid: `campaign-crowd-${key}-${campaign.id}`,
                name: `${baseTitle}-人群${crowdNames[key] || key}.csv`,
                status: 'done',
                url: url,
              });
            }
          });
        }
      }
    });

    // 更新excelFileList，保留现有数据，添加投放数据
    const { excelFileList } = this.state;
    const nonCampaignFiles = excelFileList.filter(file =>
      !file.uid.startsWith('campaign-')
    );

    const newExcelFileList = [...nonCampaignFiles, ...campaignDataFiles];

    this.setState({
      selectedCampaigns: campaignIds,
      excelFileList: newExcelFileList,
    });
  };

  // 格式化output内容，将\n转换为换行符
  formatOutputContent = (outputItem) => {
    if (!outputItem) return '';
    // 如果是新的数据结构（包含name和content）
    if (typeof outputItem === 'object' && outputItem.content !== undefined) {
      return outputItem.content.replace(/\\n/g, '\n');
    }
    // 兼容旧的字符串格式
    if (typeof outputItem === 'string') {
      return outputItem.replace(/\\n/g, '\n');
    }
    return '';
  };

  // 反格式化output内容，将换行符转换为\n
  unformatOutputContent = (content) => {
    if (!content) return '';
    return content.replace(/\n/g, '\\n');
  };

  // 添加新的output
  handleAddOutput = () => {
    const { newOutputText, newOutputName, analysisType } = this.state;
    if (!newOutputText.trim()) {
      message.error('请输入输出内容');
      return;
    }
    if (!newOutputName.trim()) {
      message.error('请输入模板名称');
      return;
    }

    const newPrompts = { ...this.defaultPrompts };
    if (!newPrompts[analysisType].output) {
      newPrompts[analysisType].output = [];
    }

    // 使用新的数据结构
    newPrompts[analysisType].output.push({
      name: newOutputName.trim(),
      content: this.unformatOutputContent(newOutputText.trim())
    });

    this.defaultPrompts = newPrompts;
    this.setState({
      newOutputText: '',
      newOutputName: ''
    });
    this.updateDefaultPrompts(newPrompts);
    message.success('添加成功');
  };

  // 删除output
  handleDeleteOutput = (index) => {
    const { analysisType } = this.state;
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个输出模板吗？',
      onOk: () => {
        const newPrompts = { ...this.defaultPrompts };
        if (newPrompts[analysisType].output) {
          newPrompts[analysisType].output.splice(index, 1);
          this.defaultPrompts = newPrompts;

          // 强制更新状态以触发重新渲染
          this.setState({}, () => {
            this.updateDefaultPrompts(newPrompts);
            message.success('删除成功');
          });
        }
      }
    });
  };

  // 开始编辑output
  handleEditOutput = (index, outputItem) => {
    this.setState({
      editingOutputIndex: index,
      editingOutputText: this.formatOutputContent(outputItem),
      editingOutputName: outputItem.name || `模板${index + 1}`,
    });
  };

  // 保存编辑的output
  handleSaveEditOutput = () => {
    const { editingOutputIndex, editingOutputText, editingOutputName, analysisType } = this.state;
    if (!editingOutputText.trim()) {
      message.error('请输入输出内容');
      return;
    }
    if (!editingOutputName.trim()) {
      message.error('请输入模板名称');
      return;
    }

    const newPrompts = { ...this.defaultPrompts };
    if (newPrompts[analysisType].output && newPrompts[analysisType].output[editingOutputIndex]) {
      // 更新name和content
      newPrompts[analysisType].output[editingOutputIndex] = {
        name: editingOutputName.trim(),
        content: this.unformatOutputContent(editingOutputText.trim())
      };
      this.defaultPrompts = newPrompts;
      this.setState({
        editingOutputIndex: null,
        editingOutputText: '',
        editingOutputName: '',
      });
      this.updateDefaultPrompts(newPrompts);
      message.success('保存成功');
    }
  };

  // 取消编辑
  handleCancelEdit = () => {
    this.setState({
      editingOutputIndex: null,
      editingOutputText: '',
      editingOutputName: '',
    });
  };

  // 处理选择输出模板
  handleSelectOutput = (value) => {
    // 这里只存储索引，而不是整个对象
    this.setState({ selectedOutput: value });
  };

  // 获取选中的输出模板内容
  getSelectedOutputContent = () => {
    const { selectedOutput, analysisType } = this.state;
    const outputs = this.defaultPrompts[analysisType]?.output || [];

    // 如果selectedOutput是索引
    if (typeof selectedOutput === 'number' && outputs[selectedOutput]) {
      return this.formatOutputContent(outputs[selectedOutput]);
    }

    return '';
  };

  render() {
    const {
      analyses,
      analysesTotal,
      analysesPagination,
      loading,
      modalVisible,
      fileList,
      excelFileList,
      analysisType,
      title,
      prompts,
      submitting,
      searchKeywords,
      previewVisible,
      previewContent,
      previewTitle,
    } = this.state;

    const uploadProps = {
      name: 'file',
      multiple: false,
      customRequest: (option) => {
        let aborted = false;
        (async () => {
          try {
            const fileUrl = await AliyunHelper.clipsUploadImage(option.file, (progress) => {
              const percent = Math.round((progress.loaded / progress.total) * 100);
              option.onProgress({ percent });
            });

            if (aborted) {
              return;
            }

            // 更新文件列表
            this.setState({
              fileList: [{
                uid: option.file.uid,
                name: option.file.name,
                status: 'done',
                url: fileUrl,
              }],
            });

            option.onSuccess();
          } catch (e) {
            console.error('上传文件失败:', e);
            option.onError();
          }
        })();

        return {
          abort() {
            aborted = true;
          },
        };
      },
      onChange: (info) => {
        const { status } = info.file;
        if (status === 'done') {
          message.success(`${info.file.name} 上传成功`);
        } else if (status === 'error') {
          message.error(`${info.file.name} 上传失败`);
        }
      },
      fileList,
    };

    const excelUploadProps = {
      name: 'file',
      multiple: true,
      customRequest: (option) => {
        let aborted = false;
        (async () => {
          try {
            const fileUrl = await AliyunHelper.clipsUploadImage(option.file, (progress) => {
              const percent = Math.round((progress.loaded / progress.total) * 100);
              option.onProgress({ percent });
            });

            if (aborted) {
              return;
            }

            // 更新文件列表
            const newFile = {
              uid: option.file.uid,
              name: option.file.name,
              status: 'done',
              url: fileUrl,
            };

            this.setState({
              excelFileList: [...this.state.excelFileList, newFile],
            });

            option.onSuccess();
          } catch (e) {
            console.error('上传CSV文件失败:', e);
            option.onError();
          }
        })();

        return {
          abort() {
            aborted = true;
          },
        };
      },
      onChange: (info) => {
        const { status, uid } = info.file;
        if (status === 'removed') {
          // 当文件被移除时，从文件列表中删除
          this.setState({
            excelFileList: this.state.excelFileList.filter(file => file.uid !== uid)
          });
        }
      },
      fileList: excelFileList,
      accept: '.csv,.xlsx,.xls',
    };

    return (
      <div>
        <FilterBar
          shouldShowSearchInput
          searchKeyWords={searchKeywords}
          onChange={(value) => { this.setState({ searchKeywords: value }); }}
          onSearch={() => {
            this.setState({ analysesPagination: { ...analysesPagination, pageIndex: 1 } }, () => {
              this.fetchAnalyses();
            });
          }}
          placeholder="请输入复盘标题"
          renderSelects={this.renderSelects}
          renderButtons={this.renderButtons}
        />

        <PaginationTable
          totalDataCount={analysesTotal}
          dataSource={analyses}
          pagination={analysesPagination}
          columns={this.renderColumns()}
          loading={loading}
          onPaginationChange={(pagination) => {
            if (!pagination) return;

            this.setState({ analysesPagination: pagination });
            this.fetchAnalyses({
              pagination: {
                skip: (pagination.pageIndex - 1) * pagination.pageSize,
                limit: pagination.pageSize,
              },
            });
          }}
        />

        <Drawer
          title="新建复盘"
          open={modalVisible}
          onClose={this.handleCancel}
          width={1200}
          extra={
            <Button
              type="primary"
              onClick={this.handleSubmit}
              loading={submitting}
            >
              提交
            </Button>
          }
        >
          <Form layout="vertical">
            <Form.Item label="复盘类型">
              <Radio.Group
                value={analysisType}
                onChange={(e) => this.setState({
                  analysisType: e.target.value,
                  prompts: this.defaultPrompts[e.target.value].prompt
                })}
              >
                <Radio value="live">直播复盘</Radio>
                <Radio value="video">视频复盘</Radio>
              </Radio.Group>
            </Form.Item>

            {analysisType === 'live' && (
              <Form.Item label="选择直播记录">
                <Select
                  placeholder="选择已有直播记录"
                  style={{ width: '100%' }}
                  loading={this.state.loadingLiveRecords}
                  onChange={this.handleSelectLiveRecord}
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  value={this.state.selectedLiveRecord?.id}
                >
                  {Object.entries(
                    this.state.liveStreamRecords
                      .sort((a, b) => {
                        // 首先按platform排序
                        if (a.platform !== b.platform) {
                          return a.platform.localeCompare(b.platform);
                        }
                        // 然后按liveTime倒序排序（最新的在前）
                        return new Date(b.liveTime) - new Date(a.liveTime);
                      })
                      .reduce((groups, record) => {
                        const anchorName = record.anchorName || '未知主播';
                        if (!groups[anchorName]) {
                          groups[anchorName] = [];
                        }
                        groups[anchorName].push(record);
                        return groups;
                      }, {})
                  ).map(([anchorName, records]) => (
                    <Select.OptGroup key={anchorName} label={anchorName}>
                      {records.map(record => {
                        const liveDate = moment(record.liveTime).format('YYYYMMDD');
                        const optionLabel = `【${PLATFROM_MAP[record.platform]}】${record.liveTitle}-${liveDate}${record.liveReplayAudioUrl ? '-有音频' : ''}`;
                        return (
                          <Select.Option key={record.id} value={record.id}>
                            {optionLabel}
                          </Select.Option>
                        );
                      })}
                    </Select.OptGroup>
                  ))}
                </Select>
                <div style={{ marginTop: 8, color: '#888' }}>
                  选择已有直播记录将自动填充相关信息
                </div>
              </Form.Item>
            )}

            <Form.Item label="复盘名称" required>
              <Input
                value={title}
                onChange={(e) => this.setState({ title: e.target.value })}
                placeholder="请输入复盘名称"
              />
            </Form.Item>

            <Form.Item label={analysisType === 'live' ? "上传音频" : "上传视频"}>
              <Upload.Dragger {...uploadProps}>
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p className="ant-upload-hint">
                  {analysisType === 'live' ? "支持常见音频格式" : "支持常见视频格式"}
                </p>
              </Upload.Dragger>
            </Form.Item>

            <Form.Item label="上传CSV文件">
              <Upload.Dragger {...excelUploadProps}>
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p className="ant-upload-hint">支持上传多个CSV文件</p>
              </Upload.Dragger>
            </Form.Item>

            {analysisType === 'live' && (
              <Form.Item label="选择其他直播数据（可选）">
                <Select
                  mode="multiple"
                  placeholder="选择其他直播记录的数据进行对比分析"
                  style={{ width: '100%' }}
                  loading={this.state.loadingLiveRecords}
                  value={this.state.selectedAdditionalLiveRecords}
                  onChange={this.handleSelectAdditionalLiveRecords}
                  allowClear
                  showSearch
                  optionFilterProp="children"
                >
                  {Object.entries(
                    this.state.liveStreamRecords
                      .filter(record =>
                        // 过滤掉当前选中的主直播记录和没有数据URL的记录
                        (!this.state.selectedLiveRecord || record.id !== this.state.selectedLiveRecord.id) &&
                        record.liveDataUrls && record.liveDataUrls.length > 0
                      )
                      .sort((a, b) => {
                        // 首先按platform排序
                        if (a.platform !== b.platform) {
                          return a.platform.localeCompare(b.platform);
                        }
                        // 然后按liveTime倒序排序（最新的在前）
                        return new Date(b.liveTime) - new Date(a.liveTime);
                      })
                      .reduce((groups, record) => {
                        const anchorName = record.anchorName || '未知主播';
                        if (!groups[anchorName]) {
                          groups[anchorName] = [];
                        }
                        groups[anchorName].push(record);
                        return groups;
                      }, {})
                  ).map(([anchorName, records]) => (
                    <Select.OptGroup key={anchorName} label={anchorName}>
                      {records.map(record => {
                        const liveDate = moment(record.liveTime).format('YYYYMMDD');
                        const optionLabel = `【${PLATFROM_MAP[record.platform]}】${record.liveTitle}-${liveDate}`;
                        return (
                          <Select.Option key={record.id} value={record.id}>
                            {optionLabel}
                          </Select.Option>
                        );
                      })}
                    </Select.OptGroup>
                  ))}
                </Select>
                <div style={{ marginTop: 8, color: '#888' }}>
                  选择其他直播记录的数据进行对比分析（仅显示有数据的记录）
                </div>
              </Form.Item>
            )}

            {analysisType === 'live' && (
              <Form.Item label="选择投放数据（可选）">
                <Select
                  mode="multiple"
                  placeholder="选择投放数据进行分析"
                  style={{ width: '100%' }}
                  loading={this.state.loadingCampaigns}
                  value={this.state.selectedCampaigns}
                  onChange={this.handleSelectCampaigns}
                  allowClear
                  showSearch
                  optionFilterProp="children"
                >
                  {Object.entries(
                    this.state.liveStreamCampaigns
                      .sort((a, b) => {
                        // 首先按platform排序
                        if (a.platform !== b.platform) {
                          return a.platform.localeCompare(b.platform);
                        }
                        // 然后按live_time倒序排序（最新的在前）
                      return new Date(b.liveTime) - new Date(a.liveTime);
                      })
                      .reduce((groups, campaign) => {
                        const anchorName = campaign.anchorName || '未知主播';
                        if (!groups[anchorName]) {
                          groups[anchorName] = [];
                        }
                        groups[anchorName].push(campaign);
                        return groups;
                      }, {})
                  ).map(([anchorName, campaigns]) => (
                    <Select.OptGroup key={anchorName} label={anchorName}>
                      {campaigns.map(campaign => {
                        const liveDate = moment(campaign.liveTime).format('YYYYMMDD');
                        const optionLabel = `【${PLATFROM_MAP[campaign.platform]}】${campaign.liveTitle}-${liveDate}`;
                        return (
                          <Select.Option key={campaign.id} value={campaign.id}>
                            {optionLabel}
                          </Select.Option>
                        );
                      })}
                    </Select.OptGroup>
                  ))}
                </Select>
                <div style={{ marginTop: 8, color: '#888' }}>
                  选择投放数据进行分析（仅显示处理完成的记录）
                </div>
              </Form.Item>
            )}

            <Form.Item label="复盘提示词">
              <Input.TextArea
                value={prompts}
                onChange={(e) => this.setState({ prompts: e.target.value })}
                placeholder="请输入复盘提示词"
                rows={4}
              />
            </Form.Item>

            <Form.Item label="选择输出模板">
              <Select
                placeholder="选择一个输出模板"
                value={this.state.selectedOutput}
                onChange={this.handleSelectOutput}
                allowClear
              >
                {(this.defaultPrompts[analysisType]?.output || []).map((outputItem, index) => {
                  const formattedOutput = this.formatOutputContent(outputItem);
                  const displayText = formattedOutput.length > 50 ?
                    `${formattedOutput.substring(0, 50)}...` :
                    formattedOutput;
                  const templateName = outputItem.name || `模板${index + 1}`;
                  return (
                    <Select.Option key={index} value={index}>
                      <div>
                        <div style={{ fontWeight: 'bold', marginBottom: 4 }}>{templateName}</div>
                        <div style={{ whiteSpace: 'pre-wrap', fontSize: '12px', color: '#666' }}>
                          {displayText}
                        </div>
                      </div>
                    </Select.Option>
                  );
                })}
              </Select>
            </Form.Item>

            <Form.Item label="输出模板管理">
              <Button
                type="dashed"
                onClick={() => this.setState({ outputManageModalVisible: true })}
                style={{ width: '100%' }}
              >
                管理输出模板
              </Button>
            </Form.Item>


          </Form>
        </Drawer>

        <Modal
          title={`${previewTitle} - 复盘内容`}
          open={previewVisible}
          onCancel={this.closeMarkdownPreview}
          footer={[
            <Button key="close" onClick={this.closeMarkdownPreview}>
              关闭
            </Button>,
            <Button
              key="download"
              type="primary"
              onClick={() => {
                this.handleDownloadMarkdown({title: previewTitle, markdownContent: previewContent});
                this.closeMarkdownPreview();
              }}
            >
              下载文档
            </Button>,
          ]}
          width={800}
          bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }}
        >
          <div className="markdown-preview">
            <ReactMarkdown remarkPlugins={[remarkGfm]}>{previewContent}</ReactMarkdown>
          </div>
        </Modal>

        <Modal
          title="输出模板管理"
          open={this.state.outputManageModalVisible}
          onCancel={() => this.setState({
            outputManageModalVisible: false,
            editingOutputIndex: null,
            editingOutputText: '',
            editingOutputName: '',
            newOutputText: '',
            newOutputName: ''
          })}
          footer={null}
          width={800}
          bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }}
        >
          <div style={{ marginBottom: 16 }}>
            <Input.Group compact>
              <Input
                style={{ width: '30%' }}
                placeholder="模板名称"
                value={this.state.newOutputName}
                onChange={(e) => this.setState({ newOutputName: e.target.value })}
              />
              <Input.TextArea
                style={{ width: 'calc(70% - 80px)' }}
                placeholder="输入新的输出模板内容"
                value={this.state.newOutputText}
                onChange={(e) => this.setState({ newOutputText: e.target.value })}
                autoSize={{ minRows: 2, maxRows: 6 }}
              />
              <Button type="primary" onClick={this.handleAddOutput}>
                添加
              </Button>
            </Input.Group>
          </div>

          <div style={{ maxHeight: 400, overflowY: 'auto' }}>
            {(this.defaultPrompts[this.state.analysisType]?.output || []).map((outputItem, index) => (
              <div key={index} style={{ marginBottom: 16, padding: 12, border: '1px solid #d9d9d9', borderRadius: 6 }}>
                {this.state.editingOutputIndex === index ? (
                  <div>
                    <Input
                      value={this.state.editingOutputName}
                      onChange={(e) => this.setState({ editingOutputName: e.target.value })}
                      placeholder="模板名称"
                      style={{ marginBottom: 8 }}
                    />
                    <Input.TextArea
                      value={this.state.editingOutputText}
                      onChange={(e) => this.setState({ editingOutputText: e.target.value })}
                      autoSize={{ minRows: 3, maxRows: 8 }}
                      style={{ marginBottom: 8 }}
                    />
                    <div>
                      <Button size="small" type="primary" onClick={this.handleSaveEditOutput}>
                        保存
                      </Button>
                      <Button size="small" style={{ marginLeft: 8 }} onClick={this.handleCancelEdit}>
                        取消
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div>
                    <div style={{ marginBottom: 8, fontWeight: 'bold', color: '#1890ff' }}>
                      {outputItem.name || `模板${index + 1}`}
                    </div>
                    <div style={{
                      marginBottom: 8,
                      whiteSpace: 'pre-wrap',
                      padding: 8,
                      backgroundColor: '#f8f9fa',
                      borderRadius: 4,
                      maxHeight: 200,
                      overflowY: 'auto'
                    }}>
                      {this.formatOutputContent(outputItem)}
                    </div>
                    <div>
                      <Button size="small" onClick={() => this.handleEditOutput(index, outputItem)}>
                        编辑
                      </Button>
                      <Button
                        size="small"
                        danger
                        style={{ marginLeft: 8 }}
                        onClick={() => this.handleDeleteOutput(index)}
                      >
                        删除
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ))}

            {(this.defaultPrompts[this.state.analysisType]?.output || []).length === 0 && (
              <div style={{ textAlign: 'center', color: '#999', padding: 20 }}>
                暂无输出模板，请添加
              </div>
            )}
          </div>
        </Modal>
      </div>
    );
  }
}
