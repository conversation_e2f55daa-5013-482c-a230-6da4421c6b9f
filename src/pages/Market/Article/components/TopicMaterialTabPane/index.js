import { DownloadOutlined } from '@ant-design/icons';
import { PaginationTable, Toast } from '~/components';
import Engine, { Materials, Sessions } from '~/engine';
import { EVENT_TYPE } from '~/pages/Playground/Configs';
import ReconnectingWebSocket from '~/pages/Playground/WebSocket';
import { Platform, StringExtension } from '~/plugins';
import { Button, Divider, Drawer, Form, Input, Radio, Select } from 'antd';
import { Document, Packer, Paragraph, TextRun } from 'docx';
import { saveAs } from 'file-saver';
import moment from 'moment';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { Component } from 'react';
import * as XLSX from 'xlsx';

const FLOW_MAP = {
  prod: '8bB6TeQnQ7181PCaR57uWY',
  stg: 'FLYkPxjETG6qgkF0dMSseZ',
};

export default class TopicMaterialTabPane extends Component {
  static propTypes = {
    topicMaterials: PropTypes.array.isRequired,
    topicMaterialsTotal: PropTypes.number.isRequired,
    topicMaterialsPagination: PropTypes.object.isRequired,
    fetchTopicMaterials: PropTypes.func,
  }

  state = {
    openDetailDrawer: false,
    selectedRecord: null,
    // 标题功能相关状态
    titleMode: 'llm', // 标题生成方式：'llm' 或 'manual'
    titleInput: '', // 用户输入的标题
    selectedTitle: '', // 用户选择的标题
    titleOptions: [], // 预设标题选项
    titleGenLoading: false, // 标题生成按钮加载状态
    // Excel导出相关状态
    exportLoading: false, // Excel导出加载状态
  }

  handleViewDetail = (record) => {
    this.setState({
      openDetailDrawer: true,
      selectedRecord: record,
    });
  }

  // 获取当前标题
  getCurrentTitle = () => {
    const { selectedRecord } = this.state;
    const { titleInput, selectedTitle, titleOptions } = this.state;

    // 优先级：用户输入的标题 > 用户选择的标题 > 原始标题
    if (titleInput) {
      return titleInput;
    }

    if (selectedTitle) {
      const selectedOption = titleOptions.find((opt) => { return opt.value === selectedTitle; });
      if (selectedOption) {
        return selectedOption.label;
      }
    }

    return selectedRecord?.title || '';
  }

  // 清理文件名中的非法字符
  sanitizeFileName = (fileName) => {
    return fileName
      .replace(/[<>:"/\\|?*]/g, '') // 移除Windows非法字符
      .replace(/\s+/g, '_') // 将空格替换为下划线
      .substring(0, 100); // 限制长度
  }

  // 格式化内容
  formatContent = (content) => {
    if (!content) return '';
    return content
      .replace(/\\n/g, '\n') // 将 \n 转换为换行符
      .replace(/\\"/g, '"') // 将 \" 转换为引号
      .replace(/\\'/g, "'") // 将 \' 转换为单引号
      .replace(/\\\\/g, '\\'); // 将 \\ 转换为反斜杠
  }

  // WebSocket 异步封装方法
  sendWebSocketMessage = (content) => {
    return new Promise((resolve, reject) => {
      const flowId = FLOW_MAP[Platform.isProd() ? 'prod' : 'stg'];
      const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflow-v2/run/${flowId}`;
      const query = { access_token: Sessions.getToken() };

      let ws = null;

      const cleanup = () => {
        if (ws) {
          ws.close();
          ws = null;
        }
      };

      ws = new ReconnectingWebSocket(
        `${path}?${qs.stringify(query)}`,
        [],
        (e) => {
          // 消息接收处理
          if (e?.data !== 'pong') {
            try {
              const originData = JSON.parse(e.data);
              const originDataObj = StringExtension.snakeToCamelObj(originData);
              const { type, data } = originDataObj;

              if (type === EVENT_TYPE.FINAL_RESULT) {
                cleanup();
                resolve(data?.output);
              } else if (type === EVENT_TYPE.EXEC_FAILED) {
                cleanup();
                reject(new Error('工作流执行失败'));
              }
            } catch (error) {
              cleanup();
              reject(new Error('消息解析失败'));
            }
          }
        },
        () => {
          // 连接成功后发送消息
          try {
            ws.send(JSON.stringify({
              text: JSON.stringify({ text: content.trim() }),
              type: 'message',
              is_beta: false,
            }));
          } catch (error) {
            cleanup();
            reject(new Error('消息发送失败'));
          }
        },
      );
    });
  }

  // 批量获取所有话题文稿数据（最多5页，每页20条）
  fetchAllTopicMaterials = async () => {
    const MAX_PAGES = 5;
    const PAGE_SIZE = 20;
    let allData = [];

    try {
      // eslint-disable-next-line no-await-in-loop
      for (let pageIndex = 1; pageIndex <= MAX_PAGES; pageIndex++) {
        const searchParams = {
          'pagination.pageIndex': pageIndex,
          'pagination.pageSize': PAGE_SIZE,
          'pagination.orderBy': 'created_at desc',
        };

        // eslint-disable-next-line no-await-in-loop
        const { items, total } = await Materials.fetchTopicMaterials(searchParams);

        if (items && items.length > 0) {
          allData = allData.concat(items);
        }

        // 如果当前页数据少于页大小，说明已经是最后一页
        if (!items || items.length < PAGE_SIZE) {
          break;
        }

        // 如果已经获取了所有数据，提前结束
        if (allData.length >= total) {
          break;
        }

        // 添加延迟避免服务器压力过大
        if (pageIndex < MAX_PAGES) {
          // eslint-disable-next-line no-await-in-loop
          await new Promise((resolve) => { return setTimeout(resolve, 300); });
        }
      }

      return allData;
    } catch (error) {
      throw new Error(`数据获取失败: ${error.message}`);
    }
  }

  // Excel导出方法
  exportToExcel = async () => {
    if (this.state.exportLoading) {
      return;
    }

    this.setState({ exportLoading: true });

    try {
      // 获取所有数据
      const allData = await this.fetchAllTopicMaterials();

      if (!allData || allData.length === 0) {
        Toast.show('暂无数据可导出', Toast.Type.WARNING);
        return;
      }

      // 格式化数据为Excel格式
      const exportData = allData.map((item, index) => {
        return {
          序号: index + 1,
          ID: item.id || '',
          标题: item.title || '',
          话题: item.topic || '',
          内容: this.formatContent(item.content) || '',
          评估结果: item.contentEvaluation?.result || '',
          创建时间: item.createdAt ? moment(item.createdAt).format('YYYY-MM-DD HH:mm:ss') : '',
        };
      });

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      ws['!cols'] = [
        { wch: 8 }, // 序号
        { wch: 10 }, // ID
        { wch: 30 }, // 标题
        { wch: 20 }, // 话题
        { wch: 50 }, // 内容
        { wch: 30 }, // 评估结果
        { wch: 20 }, // 创建时间
      ];

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '话题文稿数据');

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const fileName = `话题文稿数据_${timestamp}.xlsx`;

      // 导出文件
      XLSX.writeFile(wb, fileName);

      Toast.show(`成功导出 ${allData.length} 条数据`, Toast.Type.SUCCESS);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Excel导出失败:', error);
      Toast.show(error.message || 'Excel导出失败，请重试', Toast.Type.ERROR);
    } finally {
      this.setState({ exportLoading: false });
    }
  }

  // 下载Word文档
  onDownloadWord = async () => {
    const { selectedRecord } = this.state;
    const title = this.getCurrentTitle();
    const content = this.formatContent(selectedRecord?.content);

    if (!title) {
      Toast.show('请先设置标题', Toast.Type.WARNING);
      return;
    }

    if (!content) {
      Toast.show('暂无内容可下载', Toast.Type.WARNING);
      return;
    }

    try {
      // 创建Word文档
      const doc = new Document({
        sections: [
          {
            properties: {},
            children: [
              // 标题段落
              new Paragraph({
                children: [
                  new TextRun({
                    text: title,
                    bold: true,
                    size: 32, // 16pt
                  }),
                ],
                spacing: {
                  after: 400, // 段后间距
                },
              }),
              // 内容段落
              ...content.split('\n').map((line) => {
                return new Paragraph({
                  children: [
                    new TextRun({
                      text: line || ' ', // 空行用空格占位
                      size: 24, // 12pt
                    }),
                  ],
                  spacing: {
                    line: 360, // 行间距1.5倍
                  },
                });
              }),
            ],
          },
        ],
      });

      // 生成文档并下载
      const blob = await Packer.toBlob(doc);
      const fileName = this.sanitizeFileName(title) || '文章';
      saveAs(blob, `${fileName}.docx`);

      Toast.show('文档下载成功', Toast.Type.SUCCESS);
    } catch (error) {
      Toast.show('下载失败，请重试', Toast.Type.ERROR);
    }
  }

  // 生成标题
  onGenTitle = async () => {
    const { selectedRecord } = this.state;
    const content = this.formatContent(selectedRecord?.content);

    if (!content) {
      Toast.show('暂无内容，无法生成标题', Toast.Type.WARNING);
      return;
    }

    this.setState({ titleGenLoading: true });

    try {
      const result = await this.sendWebSocketMessage(content);

      if (result) {
        try {
          // 尝试解析结果，假设返回的是标题数组
          const { output } = JSON.parse(result);
          const titles = output.split('\n').filter((x) => { return x.length; }).slice(0, 5).map((title, index) => {
            return {
              value: `title_${index}`,
              label: title.trim(),
            };
          });
          this.setState({ titleOptions: titles });
          Toast.show('标题生成成功', Toast.Type.SUCCESS);
        } catch (parseError) {
          // 解析失败，直接作为字符串处理
          const titleOptions = [{
            value: 'title_0',
            label: result,
          }];
          this.setState({ titleOptions });
          Toast.show('标题生成成功', Toast.Type.SUCCESS);
        }
      } else {
        Toast.show('标题生成失败，请重试', Toast.Type.ERROR);
      }
    } catch (error) {
      Toast.show(error.message || '标题生成失败，请重试', Toast.Type.ERROR);
    } finally {
      this.setState({ titleGenLoading: false });
    }
  }

  // 标题模式变更
  onTitleModeChange = (e) => {
    this.setState({ titleMode: e.target.value });
  }

  // 处理标题输入变更
  onTitleInputChange = (e) => {
    const { value } = e.target;
    // 限制标题长度不超过100字符
    if (value.length <= 100) {
      this.setState({ titleInput: value });
    }
  }

  // 标题选择变更
  onTitleSelectChange = (value) => {
    this.setState({ selectedTitle: value });
  }

  renderDetailDrawer = () => {
    const { openDetailDrawer, selectedRecord } = this.state;

    if (!selectedRecord) return null;

    return (
      <Drawer
        width="50vw"
        title="详情查看"
        open={openDetailDrawer}
        onClose={() => { this.setState({ openDetailDrawer: false, selectedRecord: null }); }}
        placement="right"
      >
        <Form labelCol={{ span: 4 }} className="topic-material-detail-form">
          <Form.Item label="话题">
            <div style={{ wordBreak: 'break-word' }}>
              {selectedRecord.topic || '-'}
            </div>
          </Form.Item>
          <Form.Item label="内容">
            <div style={{
              whiteSpace: 'pre-line',
              maxHeight: '60vh',
              overflow: 'auto',
              wordBreak: 'break-word',
              padding: '8px',
              backgroundColor: '#fafafa',
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
            }}
            >
              {selectedRecord.content || '-'}
            </div>
          </Form.Item>
          <Form.Item label="评估结果">
            <div style={{
              whiteSpace: 'pre-line',
              maxHeight: '40vh',
              overflow: 'auto',
              wordBreak: 'break-word',
              padding: '8px',
              backgroundColor: '#f6ffed',
              border: '1px solid #b7eb8f',
              borderRadius: '6px',
            }}
            >
              {selectedRecord.contentEvaluation?.result || '-'}
            </div>
          </Form.Item>
        </Form>

        {/* 标题生成功能 */}
        {selectedRecord?.content && (
          <div style={{ marginTop: '16px' }}>
            <Divider />
            <div style={{ marginBottom: '12px' }}>
              <strong>标题生成：</strong>
            </div>
            <Radio.Group
              value={this.state.titleMode}
              onChange={this.onTitleModeChange}
              style={{ marginBottom: '16px' }}
            >
              <Radio value="llm">LLM生成</Radio>
              <Radio value="manual">手动输入</Radio>
            </Radio.Group>

            {/* 根据选择的模式显示对应的输入组件 */}
            {this.state.titleMode === 'llm' && (
              <div>
                <div style={{ marginBottom: '8px' }}>
                  <strong>标题选择：</strong>
                  <Button
                    type="primary"
                    size="small"
                    loading={this.state.titleGenLoading}
                    onClick={this.onGenTitle}
                    style={{ marginLeft: '8px' }}
                  >
                    生成
                  </Button>
                </div>
                <Select
                  placeholder="请选择预设标题"
                  value={this.state.selectedTitle}
                  onChange={this.onTitleSelectChange}
                  style={{ width: '100%' }}
                  allowClear
                >
                  {this.state.titleOptions.map((option) => {
                    return (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    );
                  })}
                </Select>
              </div>
            )}

            {this.state.titleMode === 'manual' && (
              <div>
                <div style={{ marginBottom: '8px' }}>
                  <strong>标题输入：</strong>
                </div>
                <Input
                  placeholder="请输入自定义标题（最多100字符）"
                  value={this.state.titleInput}
                  onChange={this.onTitleInputChange}
                  maxLength={100}
                  showCount
                />
              </div>
            )}

            {/* 显示当前选择的标题 */}
            {(this.state.titleInput || this.state.selectedTitle) && (
              <div style={{
                marginTop: '12px',
                padding: '8px',
                backgroundColor: '#f6f6f6',
                borderRadius: '4px',
                border: '1px solid #d9d9d9',
              }}
              >
                <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>
                  当前标题：
                  <div style={{ fontWeight: 'bold' }}>
                    {this.state.titleInput ||
                      this.state.titleOptions.find((opt) => {
                        return opt.value === this.state.selectedTitle;
                      })?.label ||
                      ''}
                  </div>
                </div>
              </div>
            )}

            {/* 下载按钮 */}
            {(this.state.titleInput || this.state.selectedTitle) && (
              <div style={{ marginTop: '16px', textAlign: 'right' }}>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={this.onDownloadWord}
                >
                  下载Word
                </Button>
              </div>
            )}
          </div>
        )}
      </Drawer>
    );
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        render: (txt) => {
          return txt || '-';
        },
      },
      {
        title: '内容',
        dataIndex: 'content',
        key: 'content',
        align: 'center',
        width: '50%',
        ellipsis: true,
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        width: 180,
        render: (text) => {
          return moment(text).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, record) => {
          return (
            <div>
              <Button
                type="link"
                size="small"
                onClick={() => { return this.handleViewDetail(record); }}
              >
                查看
              </Button>
            </div>
          );
        },
      },
    ];
  }

  render() {
    const { topicMaterials, topicMaterialsTotal, topicMaterialsPagination } = this.props;
    const { exportLoading } = this.state;

    return (
      <>
        {/* 导出按钮区域 */}
        <div style={{ marginBottom: '16px', textAlign: 'right' }}>
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            loading={exportLoading}
            onClick={this.exportToExcel}
            disabled={!topicMaterials || topicMaterials.length === 0}
          >
            {exportLoading ? '导出中...' : '导出Excel'}
          </Button>
        </div>

        <PaginationTable
          dataSource={topicMaterials}
          columns={this.renderColumns()}
          pagination={topicMaterialsPagination}
          totalDataCount={topicMaterialsTotal}
          onPaginationChange={(pageIndex, pageSize) => {
            return this.props.fetchTopicMaterials(pageIndex, pageSize);
          }}
          rowKey="id"
        />
        {this.state.openDetailDrawer && this.renderDetailDrawer()}
      </>
    );
  }
}
