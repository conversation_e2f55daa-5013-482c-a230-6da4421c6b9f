import './index.less';

import { Button, Checkbox, Drawer, Form, Input, Radio, Select, Space, Switch, message } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

// const EXAMINED_SETTING = [
//   { value: 'manual', name: '人工审核' },
//   { value: 'machine', name: '机器审核' },
// ];

const PLATFORMS = [
  // { value: '今日头条', name: '今日头条' },
  // { value: 'B站', name: 'B站' },
  { value: '抖音', name: '抖音' },
  { value: '抖音直播', name: '抖音直播' },
  { value: '视频号直播', name: '视频号直播' },
  // { value: '即时话题', name: '即时话题' },
  // { value: 'YouTube', name: 'YouTube' },
];

export default class MaterialSourceDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    materialSource: PropTypes.object,
    materialSourceGroups: PropTypes.array,
    kols: PropTypes.array,
    onClose: PropTypes.func,
    onSubmit: PropTypes.func,
    onChangeValue: PropTypes.func,
    onAnalyzeUrl: PropTypes.func,
    handleNumber: PropTypes.func,
  }

  static defaultProps = {
    open: false,
    materialSource: {
      partitionType: 'manual',
    },
    materialSourceGroups: [],
    kols: [],
    onClose: () => { },
    onSubmit: () => { },
    onChangeValue: () => { },
    onAnalyzeUrl: () => { },
    handleNumber: () => { },
  }

  state = {
    // workflowParams: [],
    // workflows: [],
  }


  componentDidMount = async () => {
    // const timestamp = new Date().getTime();
    // const resp = await fetch(`${Consts.OSS_CDN_DOMAIN}/llmbot/plugin/configs/aivideo.json?t=${timestamp}`);
    // const data = await resp.json();
    // const env = Platform.isProd() ? 'production' : 'staging';
    // const workflows = data?.[env]?.material_flows || [];
    // this.setState({ workflows });
    // if (this.props.materialSource?.workflowUuid) {
    //   this.onChangeWorkflow(this.props.materialSource.workflowUuid);
    // }
  }

  // 验证必填项
  validateRequiredFields = () => {
    const { materialSource } = this.props;
    const requiredFields = [
      { key: 'sourceUrl', name: '来源链接' },
      { key: 'author', name: '作者' },
      { key: 'platform', name: '平台' },
      { key: 'description', name: '简短说明' },
      { key: 'groupIds', name: '源分组' },
    ];

    for (const field of requiredFields) {
      const value = materialSource[field.key];
      const isEmpty = !value ||
        (Array.isArray(value) && value.length === 0) ||
        (typeof value === 'string' && value.trim() === '');

      if (isEmpty) {
        message.error(`请填写${field.name}`);
        return false;
      }
    }
    return true;
  }

  // 处理提交
  handleSubmit = () => {
    if (this.validateRequiredFields()) {
      this.props.onSubmit();
    }
  }

  // onChangeWorkflow = async (workflowUuid) => {
  //   const workflow = await ChatBot.getChatbotWorkflow(workflowUuid);
  //   const content = JSON.parse(workflow.content);
  //   const inputSchema = content.nodes[0].data.input_schema;
  //   const workflowParams = [];
  //   Object.entries(inputSchema).forEach(
  //     ([key, value]) => {
  //       workflowParams.push({
  //         key, description: value.description || '', default: value.default || '', type: value.type || 'str',
  //       });
  //     },
  //   );
  //   this.setState({ workflowParams });

  //   this.props.onChangeValue(workflowUuid, 'materialSource', 'workflowUuid');
  // }

  render() {
    const {
      open,
      materialSource,
      materialSourceGroups,
      kols,
      onClose,
      onChangeValue,
      onAnalyzeUrl,
      handleNumber,
    } = this.props;

    return (
      <Drawer
        className="material-source-drawer"
        width="66vw"
        open={open}
        title={materialSource.id ? '编辑文稿源' : '创建文稿源'}
        onClose={onClose}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={onClose}>
                取消
              </Button>
              <Button type="primary" onClick={this.handleSubmit}>
                确定
              </Button>
            </Space>
          </div>
        }
      >
        <Form labelCol={{ span: 2 }} className="common-form">
          <Form.Item label="来源链接" required>
            <Input.Search
              enterButton="解析"
              value={materialSource.sourceUrl}
              onChange={(e) => { return onChangeValue(e, 'materialSource', 'sourceUrl'); }}
              onSearch={(e) => { return onAnalyzeUrl(e); }}
            />
          </Form.Item>
          <Form.Item label="作者" required>
            <Input
              value={materialSource.author}
              onChange={(e) => { return onChangeValue(e, 'materialSource', 'author'); }}
            />
          </Form.Item>
          <Form.Item label="平台" required>
            <Select
              value={materialSource.platform}
              onChange={(e) => { return onChangeValue(e, 'materialSource', 'platform'); }}
            >
              {PLATFORMS.map((w) => {
                return (
                  <Select.Option key={w.value} value={w.value}>
                    {w?.name}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item label="简短说明" required>
            <Input.TextArea
              value={materialSource.description}
              onChange={(e) => { return onChangeValue(e, 'materialSource', 'description'); }}
            />
          </Form.Item>
          <Form.Item label="源分组" required>
            <Select
              allowClear
              mode="multiple"
              value={materialSource.groupIds}
              onChange={(e) => { return onChangeValue(e, 'materialSource', 'groupIds'); }}
            >
              {materialSourceGroups.map((w) => {
                return (
                  <Select.Option key={w.id} value={w.id}>
                    {w?.name}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          {/* <Form.Item label="审核设定">
            <Select
              value={materialSource.reviewType}
              onChange={(e) => { return onChangeValue(e, 'materialSource', 'reviewType'); }}
            >
              {EXAMINED_SETTING.map((w) => {
                return (
                  <Select.Option key={w.value} value={w.value}>
                    {w?.name}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item label="优先级">
            <Select
              value={materialSource.priority}
              onChange={(e) => { return onChangeValue(e, 'materialSource', 'priority'); }}
            >
              <Select.Option value={0}>高</Select.Option>
              <Select.Option value={1}>普通</Select.Option>
            </Select>
          </Form.Item> */}
          <Form.Item label="状态">
            <Switch
              checkedChildren="启用"
              unCheckedChildren="禁用"
              checked={materialSource.enabled}
              onChange={(e) => { return onChangeValue(e, 'materialSource', 'enabled'); }}
            />
          </Form.Item>

          <Form.Item label="自动ASR">
            <Switch
              checkedChildren="启用"
              unCheckedChildren="禁用"
              checked={materialSource.autoAsr}
              onChange={(e) => { return onChangeValue(e, 'materialSource', 'autoAsr'); }}
            />
          </Form.Item>
          {
            (!['抖音直播', '视频号直播'].includes(materialSource.platform)) &&
            <>
              <Form.Item label="入库设定">
                <Checkbox
                  checked={materialSource.enableFetchHistory}
                  onChange={(e) => { return onChangeValue(e, 'materialSource', 'enableFetchHistory'); }}
                >
                  抓取最近的
                  <Input
                    value={materialSource.historyLimit}
                    style={{ width: 100, margin: '0 10px 10px 10px' }}
                    onChange={(e) => {
                      const { value: inputValue } = e.target;
                      const reg = /^-?\d*(\.\d*)?$/;
                      if (reg.test(inputValue) || inputValue === '' || inputValue === '-') {
                        onChangeValue(inputValue, 'materialSource', 'historyLimit');
                      }
                    }}
                    onBlur={handleNumber('historyLimit')}
                    maxLength={5}
                  />
                  天
                </Checkbox>
                <Checkbox
                  checked={materialSource.enableFetchNew}
                  style={{ marginLeft: 0 }}
                  onChange={(e) => { return onChangeValue(e, 'materialSource', 'enableFetchNew'); }}
                >
                  新文章监控并自动入库
                </Checkbox>
              </Form.Item>
              <Form.Item label="清洗设定">
                清洗每日入库的
                <Input
                  value={materialSource.optimizeLimit}
                  style={{ width: 100, margin: '0 10px' }}
                  onChange={(e) => {
                    const { value: inputValue } = e.target;
                    const reg = /^-?\d*(\.\d*)?$/;
                    if (reg.test(inputValue) || inputValue === '' || inputValue === '-') {
                      onChangeValue(inputValue, 'materialSource', 'optimizeLimit');
                    }
                  }}
                  onBlur={handleNumber('optimizeLimit')}
                  maxLength={5}
                />
                条新文章
              </Form.Item>
            </>
          }
          <Form.Item label="分配设定" style={{ display: 'none' }}>
            <Input.Group compact style={{ display: 'flex', alignItems: 'center' }}>
              <Radio.Group
                value={materialSource?.partitionType}
                onChange={(e) => { return onChangeValue(e.target.value, 'materialSource', 'partitionType'); }}
              >
                <Radio value="auto">随机分配</Radio>
                <Radio value="manual">手动分配</Radio>
                <Radio value="fixed">固定分配</Radio>
              </Radio.Group>
              {
                materialSource?.partitionType === 'fixed' && (
                  <Select
                    style={{ width: 200 }}
                    value={materialSource?.characterId}
                    onChange={(e) => { return onChangeValue(e, 'materialSource', 'characterId'); }}
                  >
                    {kols.map((w) => {
                      return (
                        <Select.Option key={w.value} value={w.value}>
                          {w?.name}
                        </Select.Option>
                      );
                    })}
                  </Select>
                )
              }
            </Input.Group>
          </Form.Item>
        </Form>
      </Drawer>
    );
  }
}
