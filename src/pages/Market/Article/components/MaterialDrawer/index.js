/* eslint-disable no-case-declarations */
import './index.less';

import { Toast } from '~/components';
import Engine, { ChatBot, Market, Sessions } from '~/engine';
import { EVENT_TYPE, EVENT_TYPE_ZH } from '~/pages/Playground/Configs';
import ReconnectingWebSocket from '~/pages/Playground/WebSocket';
import { Platform, StringExtension } from '~/plugins';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Drawer,
  Form,
  Input,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  Spin,
  Table,
  Tag,
  Typography,
} from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { PureComponent } from 'react';

const FILTER_PARAMS = {
  fetchStatus: 'done',
  optimizeStatus: 'done',
  passed: 'true',
  order_by: 'created_at asc',
  pagination: { skip: 0, limit: 1000 },
};
const ANALYSIS_MAP = { diggCount: '点赞(阅读)数', commentCount: '评论数', shareCount: '分享数', collectCount: '收藏数' };
const EXTRACT_WORKFLOW_MAP = {
  material: { STG: '53kmcTLHTRyynkEiNsNL6E', PROD: 'GhD409xG12HNWBFLDFzHcg' },
  outline: { STG: '5TbffFTJAc27XzQLdLLfWm', PROD: 'DyNqLJ73QH6BmJ8xvKWdoG' },
};

export default class MaterialDrawer extends PureComponent {
  static propTypes = {
    material: PropTypes.object,
    article: PropTypes.object,
    workflowsMap: PropTypes.object,
    materialSourcesMap: PropTypes.object,
    open: PropTypes.bool,
    isMaterialEdit: PropTypes.bool,
    isRerunning: PropTypes.bool,
    createItem: PropTypes.func,
    updateItem: PropTypes.func,
    rerunOptimizeJob: PropTypes.func,
    clearTimerByArticleId: PropTypes.func,
    initArticleTimer: PropTypes.func,
    onChange: PropTypes.func,
    onSearch: PropTypes.func,
    onClose: PropTypes.func,
  }

  static defaultProps = {
    rerunOptimizeJob: () => { },
    clearTimerByArticleId: () => { },
    initArticleTimer: () => { },
    onChange: () => { },
    onSearch: () => { },
    onClose: () => { },
  }

  state = {
    open: false,
    logs: [],
    nodes: [],
    articles: [],
    selectedRowKeys: [],
    article: {},
    selectedTagObjs: {},
    embeddingMethod: 'default',
  }

  componentDidMount = async () => {
    const { article } = this.props;
    if (article.characterId === 0) {
      article.characterId = undefined;
    }
    this.setState({ article });
  }

  initWebSocket = (workflowId) => {
    if (this.ws) {
      this.ws.close();
    }

    const uri = Engine.getWssEndpoint();
    const path = `${uri}/v2/chatbot/workflows/run/${workflowId}`;
    const query = { access_token: Sessions.getToken() };
    this.ws = new ReconnectingWebSocket(`${path}?${qs.stringify(query)}`, [], this.onReceiveMsg);
  }

  parseMaterialContent = (content) => {
    try {
      const parsedContent = JSON.parse(content);
      if (Array.isArray(parsedContent)) {
        const texts = parsedContent.map((item) => { return item.text; });
        return texts.join(' ');
      }
    } catch (error) {
      // JSON 解析失败，直接返回 content
    }
    return content;
  }

  genWsUrl = (workflowId) => {
    const uri = Engine.getWssEndpoint();
    const path = `${uri}/v2/chatbot/workflows/run/${workflowId}`;
    const query = { access_token: Sessions.getToken() };
    return `${path}?${qs.stringify(query)}`;
  }

  formatLogHtml = (style, type, msg) => {
    const typeName = EVENT_TYPE_ZH[type];
    let message = msg;
    try {
      message = JSON.parse(message); message = JSON.stringify(message, null, 2);
    } catch (error) { /** do nothing */ }
    return `<div style="background:#eee;padding:4px 6px;"><b style="${style}">[${typeName}]:</b>
<pre style="white-space: pre-wrap;margin-bottom:0;">${message}</pre>
</div>\n`;
  }

  onChangeTagbyKey = (tags, key) => {
    const selectedTagObjs = _.clone(this.state.selectedTagObjs);
    selectedTagObjs[key] = tags;
    this.setState({ selectedTagObjs });
  }

  onChangeCharacter = async (e) => {
    const { material } = this.props;
    const { article } = this.state;

    if (_.isUndefined(article.id)) {
      const newArticle = await this.props.createItem('article', { characterId: e, materialId: material.id });
      this.setState({ article: newArticle });
    } else {
      await this.props.updateItem('article', { id: article.id, characterId: e });
      this.setState({ article: { ...article, characterId: e } });
    }
  }

  onSearch = async () => {
    const { startAt, endAt, selectedTagObjs, article } = this.state;
    if (_.isUndefined(article.characterId)) {
      Toast.show('请先分配KOL', Toast.Type.WARNING);
      return;
    }

    const conditions = [];
    const { sourceId, ...otherTagObjs } = selectedTagObjs;
    _.map(otherTagObjs, (v, k) => { v.forEach((x) => { conditions.push({ [k]: x }); }); });
    const data = await this.props.onSearch({ ...FILTER_PARAMS, startAt, endAt, conditions, sourceId });
    this.setState({
      open: true,
      articles: _.orderBy(_.values(data), ['diggCount'], ['desc']),
      selectedRowKeys: [],
      sourceId: undefined,
      workflowId: undefined,
    });
  }

  onExtractMaterial = (type = 'material', isRelated = false) => {
    if (this.emWs) {
      this.emWs.close();
    }
    const workflowId = EXTRACT_WORKFLOW_MAP[type][Platform.isProd() ? 'PROD' : 'STG'];
    const uri = Engine.getWssEndpoint();
    const path = `${uri}/v2/chatbot/workflows/run/${workflowId}`;
    const query = { access_token: Sessions.getToken() };
    this.emWs = new ReconnectingWebSocket(`${path}?${qs.stringify(query)}`, [], () => { });

    const material = isRelated ? this.state.material : this.props.material;
    let data = { material_id: material.id };
    if (type === 'outline') {
      const { id, title, content, pubDate } = material;
      data = { id, title, content, pub_date: pubDate };
    }
    setTimeout(() => {
      this.emWs.send(JSON.stringify({ text: JSON.stringify(data), type: 'message', is_beta: false }));
    }, 300);
    Toast.show('提取中，请稍后查看...', Toast.Type.INFO);
  }

  onSave = async () => {
    const { material, article, isMaterialEdit } = this.props;

    if (isMaterialEdit) {
      const { id, title, content, passed } = material;
      await this.props.updateItem('material', { id, title, content, passed });

      if (article?.id) {
        await this.props.updateItem('article', { id: article.id, content: article.content });
      }
    }
    this.props.onClose();
  }

  onUseResult = async () => {
    const { article } = this.state;
    await this.props.updateItem('article', { id: article.id, content: this.state.finalResult });
    this.setState({
      logs: [],
      finalResult: '',
      openLogs: false,
      article: { ...article, content: this.state.finalResult },
    });
  }

  onExtractOutlines = async (articles) => {
    const outlineWorkflowId = EXTRACT_WORKFLOW_MAP.outline[Platform.isProd() ? 'PROD' : 'STG'];
    const outlineWsUrl = this.genWsUrl(outlineWorkflowId);
    const promises = [];
    for (let index = 0; index < articles.length; index++) {
      const a = new Promise((resolve, reject) => {
        const webSocket = new WebSocket(outlineWsUrl);
        webSocket.onopen = () => {
          const { id, title, content, pubDate } = articles[index];
          webSocket.send(JSON.stringify({
            text: JSON.stringify({ id, title, content, pub_date: pubDate }),
            type: 'message',
            is_beta: false,
          }));
        };
        webSocket.onerror = (error) => {
          reject(error);
        };
        webSocket.onmessage = (e) => {
          let originData = JSON.parse(e.data);
          originData = StringExtension.snakeToCamelObj(originData);
          if (!_.isUndefined(originData?.taskId)) {
            const logs = _.cloneDeep(this.state.logs);
            let msg = logs[0].message || '';
            msg += JSON.stringify(originData);
            logs[0].message = msg;
            this.setState({ logs });
          }

          if (originData.type === EVENT_TYPE.FINAL_RESULT) {
            resolve(originData.data.output);
          }
          if (originData.type === EVENT_TYPE.EXEC_FAILED) {
            resolve(false);
          }
        };
        webSocket.onclose = () => { console.log('WebSocket closed'); }; // eslint-disable-line
      });
      promises.push(a);
    }
    const outlines = await Promise.all(promises);
    return outlines;
  }

  onExtractMaterials = (articles) => {
    const materialWorkflowId = EXTRACT_WORKFLOW_MAP.material[Platform.isProd() ? 'PROD' : 'STG'];
    const materialWsUrl = this.genWsUrl(materialWorkflowId);
    const promises = [];
    for (let index = 0; index < articles.length; index++) {
      const a = new Promise((resolve, reject) => {
        const webSocket = new WebSocket(materialWsUrl);
        webSocket.onopen = () => {
          webSocket.send(JSON.stringify({
            text: JSON.stringify({ material_id: articles[index].id }),
            type: 'message',
            is_beta: false,
          }));
        };
        webSocket.onerror = (error) => {
          reject(error);
        };
        webSocket.onmessage = (e) => {
          let originData = JSON.parse(e.data);
          originData = StringExtension.snakeToCamelObj(originData);
          if (!_.isUndefined(originData?.taskId)) {
            const logs = _.cloneDeep(this.state.logs);
            let msg = logs[1].message || '';
            msg += JSON.stringify(originData);
            logs[1].message = msg;
            this.setState({ logs });
          }

          if (originData.type === EVENT_TYPE.FINAL_RESULT) {
            resolve(originData.data.output);
          }

          if (originData.type === EVENT_TYPE.EXEC_FAILED) {
            resolve(false);
          }
        };
        webSocket.onclose = () => { console.log('WebSocket closed'); }; // eslint-disable-line
      });
      promises.push(a);
    }
    return Promise.all(promises);
  }

  onConfirmViewpoint = () => {
    this.ws.send(JSON.stringify({ text: this.state.selectedViewpoint, type: 'action_confirm', is_beta: false }));
    this.setState({ openViewpoints: false, viewpoint: '' });
  }

  onRewrite = async () => {
    if (_.isUndefined(this.state.workflowId)) {
      Toast.show('请选择工作流', Toast.Type.WARNING);
      return;
    }

    await this.setState({ open: false, logs: [{ nodeId: '开始提取大纲...' }], openLogs: true });
    // 1. 执行提取大纲
    const { selectedRowKeys, articles, article } = this.state;
    const selectArticles = _.filter(articles, (x) => { return _.includes(selectedRowKeys, x.id); });
    const outlines = await this.onExtractOutlines(selectArticles);
    if (outlines.find((x) => { return x === false; })) {
      const logs = _.cloneDeep(this.state.logs);
      logs[0].message += '\nError';
      this.setState({ logs, finalResult: 'error' });
    }
    await this.setState({ logs: [...this.state.logs, { nodeId: '开始提取素材...' }] });
    // 2. 执行提取素材
    const materials = await this.onExtractMaterials(selectArticles);
    if (materials.find((x) => { return x === false; })) {
      const logs = _.cloneDeep(this.state.logs);
      logs[1].message += '\nError';
      this.setState({ logs, finalResult: 'error' });
    }
    const key = this.state.workflowId;
    this.initWebSocket(key);
    const data = await ChatBot.getChatbotWorkflow(key);
    await this.setState({ nodes: JSON.parse(data?.content)?.nodes });
    const { material } = this.props;
    let character = await Market.getPublishCharacter(article.characterId);
    character = _.pickBy(character, (v, k) => { return !_.includes(k, 'At'); });
    character = StringExtension.camelToSnakeObj(character);
    const textObj = { material_id: material.id, related_material_ids: selectedRowKeys, character };
    if (!_.isUndefined(this.state.sourceId)) {
      textObj.source_id = this.state.sourceId;
    }
    const text = JSON.stringify(textObj);

    setTimeout(() => {
      this.ws.send(JSON.stringify({
        text,
        type: 'message',
        is_beta: false,
        embedding_method: this.state.embeddingMethod || 'default',
      }));
    }, 1000);
  }

  onReceiveMsg = (e) => {
    if (e?.data !== 'pong') {
      const originData = JSON.parse(e.data);
      const originDataObj = StringExtension.snakeToCamelObj(originData);
      if (!_.isUndefined(originDataObj?.taskId)) {
        this.setState({ logs: [...this.state.logs, { nodeId: '开始创作', message: JSON.stringify(originData) }] });
      }
      const { type, data } = originDataObj;
      const nodeIdStr = _.trim(data?.nodeId, ' ');
      if (type === EVENT_TYPE.LLM_STEP_RESPONSE && _.isEmpty(data?.token)) {
        return;
      }

      switch (type) {
        case EVENT_TYPE.EXEC_STEP:
          const createdAt = moment().format('YYYY-MM-DD HH:mm');
          if (['Start', 'Done'].includes(nodeIdStr)) {
            this.setState({ logs: [...this.state.logs, { nodeId: nodeIdStr, message: '', createdAt }] });
          } else {
            const nodeId = _.last(nodeIdStr.split('@'));
            const node = this.state.nodes.find((x) => { return x.id === nodeId; });
            this.setState({
              logs: [
                ...this.state.logs,
                { message: '', nodeId: node?.data?.name || data.nodeName, createdAt },
              ],
            });
          }
          break;
        case EVENT_TYPE.OP_RESULT:
        case EVENT_TYPE.EXEC_FAILED:
        case EVENT_TYPE.TOOL_INPUT:
        case EVENT_TYPE.TOOL_OUTPUT:
        case EVENT_TYPE.LLM_REQUEST:
        case EVENT_TYPE.LLM_TOKEN_USAGE:
          this.llmStepResponse = '';
          this.beforeStepResponse = '';
          const logs = _.clone(this.state.logs);
          const lastLog = logs.pop();
          const style = type === EVENT_TYPE.EXEC_FAILED ? 'color:red' : '';
          if (type === EVENT_TYPE.EXEC_FAILED) {
            this.setState({ finalResult: 'err' });
          }
          let msg = data?.output || data?.msg || data?.prompt;
          if ([EVENT_TYPE.OP_RESULT].includes(type)) {
            try {
              const msgObj = JSON.parse(msg);
              delete msgObj.raw_content;
              msg = JSON.stringify(msgObj);
            } catch (error) {
              // nothing
            }
          }
          if ([EVENT_TYPE.LLM_TOKEN_USAGE].includes(type)) {
            try { msg = JSON.stringify(JSON.parse(msg), null, 2); } catch (error) { /* nothing*/ }
          }
          lastLog.message += this.formatLogHtml(style, type, msg);
          lastLog.message = _.trimStart(lastLog.message, '\n');
          this.setState({ logs: [...logs, lastLog] });
          break;
        case EVENT_TYPE.LLM_STEP_RESPONSE:
          const stepLogs = _.clone(this.state.logs);
          const lastSetpLog = stepLogs.pop();
          if (!_.isEmpty(lastSetpLog.message) && _.isEmpty(this.llmStepResponse)) {
            this.beforeStepResponse = lastSetpLog.message;
          }

          this.llmStepResponse += data?.token;
          if (!_.isEmpty(this.llmStepResponse)) {
            lastSetpLog.message = this.formatLogHtml(style, type, this.llmStepResponse);
            lastSetpLog.message = this.beforeStepResponse + _.trimStart(lastSetpLog.message, '\n');
            this.setState({ logs: [...stepLogs, lastSetpLog] });
          }
          break;
        case EVENT_TYPE.EXEC_LOG:
          if (data.msg !== 'Workflow run start') {
            const execLogs = _.clone(this.state.logs);
            const lastExecLog = execLogs.pop();
            lastExecLog.message += this.formatLogHtml(style, type, data.msg);
            lastExecLog.message = _.trimStart(lastExecLog.message, '\n');
            this.setState({ logs: [...execLogs, lastExecLog] });
          }
          break;
        case EVENT_TYPE.EXEC_ACTION_REQUIRED:
          this.setState({ openViewpoints: true, viewpoint: data.output });
          break;
        case EVENT_TYPE.FINAL_RESULT:
          this.setState({ finalResult: data?.output });
          break;
        default:
          break;
      }

      const logWrap = document.getElementById('log-wrap');
      logWrap.scrollTop = logWrap.scrollHeight;
    }
  }

  renderMaterialColumns = () => {
    const { materialSourcesMap } = this.props;
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center', width: 80 },
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        ellipsis: true,
        width: '30%',
        render: (txt, row) => {
          return (
            <a onClick={() => { return this.setState({ openArticle: true, material: row }); }}>
              {txt}
            </a>
          );
        },
      },
      {
        title: '作者',
        dataIndex: 'sourceId',
        key: 'sourceId',
        align: 'center',
        ellipsis: true,
        render: (txt) => { return materialSourcesMap[txt] || ''; },
      },
      ..._.map(ANALYSIS_MAP, (v, k) => {
        return {
          title: v, dataIndex: k, key: k, align: 'center', width: 140, sorter: (a, b) => { return a[k] - b[k]; },
        };
      }),
      { title: '发布时间', dataIndex: 'pubDate', key: 'pubDate', align: 'center', width: 120 },
    ];
  }

  renderMeta = (meta, isRelated = false) => {
    if (_.isEmpty(meta)) return null;
    if (isRelated) {
      return <>{_.map(meta, (v, k) => { return <Form.Item label={k}>{v.toString()}</Form.Item>; })}</>;
    }

    const materialSources = [];
    _.map(_.cloneDeep(this.props.materialSourcesMap), (v, k) => { materialSources.push({ key: +k, value: v }); });
    materialSources.sort((a, b) => { return b.key - a.key; });

    const { selectedTagObjs } = this.state;
    return (
      <Form.Item label="元信息" >
        <div style={{ textAlign: 'end' }}>
          <Button type="primary" size="small" onClick={this.onSearch}>搜索相关文章</Button>
        </div>
        <Form labelCol={{ span: 2 }}>
          {
            _.map(meta, (v, k) => {
              return (
                <Form.Item label={k}>
                  {
                    k === '主题' || k === '摘要' ?
                      <Checkbox.Group
                        value={selectedTagObjs[k]}
                        onChange={(e) => { return this.onChangeTagbyKey(e, k); }}
                      >
                        <Checkbox value={v}>{v}</Checkbox>
                      </Checkbox.Group> :
                      <Checkbox.Group
                        value={selectedTagObjs[k]}
                        onChange={(e) => { return this.onChangeTagbyKey(e, k); }}
                      >
                        {v.map((tag) => { return <Checkbox value={tag}>{tag}</Checkbox>; })}
                      </Checkbox.Group>
                  }
                </Form.Item>
              );
            })
          }
          <Form.Item label="文稿源">
            <Select
              style={{ width: 260, marginBottom: 16, marginRight: 16 }}
              allowClear
              showSearch
              filterOption={(input, option) => { return option.children.includes(input); }}
              value={selectedTagObjs.sourceId}
              onChange={(e) => { return this.onChangeTagbyKey(e, 'sourceId'); }}
              placeholder="请选择文稿源"
            >
              {materialSources.map((x) => { return <Select.Option value={x.key}>{x.value}</Select.Option>; })}
            </Select>
          </Form.Item>
        </Form>
      </Form.Item>
    );
  }

  renderUserInputModal = () => {
    const { openViewpoints, viewpoint } = this.state;
    const viewpoints = viewpoint.match(/^\d+\..+/gm);
    return (
      <Modal
        title="请选择观点"
        width={800}
        visible={openViewpoints}
        onCancel={() => { this.setState({ openViewpoints: false }); }}
        footer={null}
      >
        <Form labelCol={{ span: 4 }}>
          <Form.Item label="观点">
            <Radio.Group onChange={(e) => { return this.setState({ selectedViewpoint: e.target.value }); }}>
              <Space direction="vertical">
                {(viewpoints || []).map((x) => { return <Radio value={x}>{x}</Radio>; })}
              </Space>
            </Radio.Group>
          </Form.Item>
          <Form.Item label="自定义观点">
            <Input.TextArea
              autoSize={{ minRows: 4 }}
              onChange={(e) => { return this.setState({ selectedViewpoint: e.target.value }); }}
            />
          </Form.Item>
          <Divider />
          <div style={{ textAlign: 'center' }}>
            <Button onClick={() => { return this.onConfirmViewpoint(); }}>确定</Button>
          </div>
        </Form>
      </Modal>
    );
  }

  renderRelatedArticleDrawer = () => {
    const { startAt, endAt } = this.state;
    const materialSources = [];
    _.map(_.cloneDeep(this.props.materialSourcesMap), (v, k) => { materialSources.push({ key: +k, value: v }); });
    materialSources.sort((a, b) => { return b.key - a.key; });

    return (
      <Drawer
        width="60vw"
        open={this.state.open}
        title="相关文章"
        onClose={() => {
          this.setState({ open: false, selectedRowKeys: [], sourceId: undefined, workflowId: undefined });
        }}
      >
        <div>
          <DatePicker.RangePicker
            style={{ marginRight: 16 }}
            value={_.isUndefined(startAt) ? [] : [moment(startAt), moment(endAt)]}
            onChange={async (e) => {
              const [startTime, endTime] = e || [];
              await this.setState({
                startAt: startTime?.format('YYYY-MM-DDT00:00:00.SSSSSS') || undefined,
                endAt: endTime?.format('YYYY-MM-DDT23:59:59.SSSSSS') || undefined,
              });
              this.onSearch();
            }}
          />

          <div style={{ float: 'right' }}>
            <Select
              style={{ width: 260, marginBottom: 16, marginRight: 16 }}
              allowClear
              showSearch
              filterOption={(input, option) => { return option.children.includes(input); }}
              onChange={(e) => { return this.setState({ sourceId: e }); }}
              placeholder="请选择文稿源"
            >
              {materialSources.map((x) => { return <Select.Option value={x.key}>{x.value}</Select.Option>; })}
            </Select>
            <Select
              style={{ width: 200, marginRight: 16 }}
              placeholder="请选择Embedding方法"
              onChange={(e) => { return this.setState({ embeddingMethod: e }); }}
            >
              <Select.Option value="default">Embedding V1</Select.Option>
              <Select.Option value="bge">Embedding V2</Select.Option>
              <Select.Option value="bce">Embedding bce</Select.Option>
            </Select>
            <Select style={{ width: 260 }} onChange={(e) => { return this.setState({ workflowId: e }); }}>
              {
                _.map(this.props.workflowsMap, (v, k) => {
                  return <Select.Option value={k}>{v}</Select.Option>;
                })
              }
            </Select>
            <Button type="primary" onClick={() => { return this.onRewrite(); }}>
              创作
            </Button>
          </div>
        </div>
        <Divider />
        <Table
          rowKey="id"
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys: this.state.selectedRowKeys,
            onChange: (selectedRowKeys) => { this.setState({ selectedRowKeys }); },
          }}
          dataSource={this.state.articles}
          columns={this.renderMaterialColumns()}
          pagination={false}
        />
      </Drawer>
    );
  }

  renderExtractBtns = (isRelated = false) => {
    return (
      <span>
        <Button
          size="small"
          type="primary"
          style={{ marginRight: 8 }}
          onClick={() => { return this.onExtractMaterial('outline', isRelated); }}
        >提取大纲
        </Button>
        <Button
          size="small"
          type="primary"
          onClick={() => { return this.onExtractMaterial('material', isRelated); }}
        >
          提取素材
        </Button>
      </span>
    );
  }

  renderArticleDetailDrawer = () => {
    const { material, openArticle, selectedRowKeys } = this.state;
    return (
      <Drawer
        width="40vw"
        title="文章详情"
        open={openArticle}
        className="material-drawer"
        onClose={() => { this.setState({ openArticle: false }); }}
        extra={
          <Button
            type="primary"
            onClick={() => { this.setState({ selectedRowKeys: [...selectedRowKeys, material.id] }); }}
          >
            选用
          </Button>
        }
      >
        <Form labelCol={{ span: 4 }} className="material-drawer-form">
          <Form.Item label="标题">
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              {material.title}
              {/* {this.renderExtractBtns(true)} */}
            </div>
          </Form.Item>
          {/* {this.renderMeta(material?.meta, true)} */}
          <Form.Item label="生成时间">
            {material.createdAt}
          </Form.Item>
          <Form.Item label="文稿源">
            <a target="_blank" href={material.sourceUrl} rel="noreferrer">{material.sourceUrl}</a>
          </Form.Item>
          {/* <Form.Item label="是否合格">
            {material.passed ? <Tag color="success">合格</Tag> : <Tag color="error">不合格</Tag>}
          </Form.Item> */}
          <Form.Item label="正文">
            <p style={{ whiteSpace: 'pre-line', maxHeight: '60vh', overflow: 'auto', position: 'relative' }}>
              <Button
                type="link"
                style={{ position: 'absolute', right: 0, top: 0 }}
                onClick={async () => {
                  await navigator.clipboard.writeText(this.parseMaterialContent(material.content));
                  Toast.show('复制成功', Toast.Type.SUCCESS);
                }}
              >
                复制正文
              </Button>
              {this.parseMaterialContent(material.content)}
            </p>
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderLogDrawer = () => {
    return (
      <Drawer
        width="60vw"
        open={this.state.openLogs}
        title="执行日志"
        onClose={() => { this.setState({ openLogs: false }); }}
      >
        <div id="log-wrap" style={{ maxHeight: 'calc(100vh - 120px)', overflow: 'auto' }}>
          {
            _.map(this.state.logs, (v, i) => {
              return (
                <p key={i} style={{ whiteSpace: 'pre-line' }}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Typography.Title level={5} style={{ marginBottom: 0 }}>{v.nodeId}</Typography.Title>
                    &nbsp;[{v.createdAt}]<br />
                  </div>
                  <div dangerouslySetInnerHTML={{ __html: v?.message }} />
                </p>
              );
            })
          }
        </div>
        {
          !_.isEmpty(this.state.finalResult) ?
            <Button type="primary" onClick={() => { return this.onUseResult(); }}>使用结果</Button>
            :
            <div style={{ textAlign: 'center' }}>
              <Spin spinning size="large" />
            </div>
        }
      </Drawer>
    );
  }

  render = () => {
    const { article } = this.state;
    const { material, open, isMaterialEdit, isRerunning } = this.props;

    return (
      <Drawer
        width="66vw"
        open={open}
        maskClosable={!isMaterialEdit}
        className="material-drawer"
        title={isMaterialEdit ? '编辑' : '详情'}
        onClose={() => {
          this.props.onClose();
          if (article && article.id) {
            this.props?.clearTimerByArticleId(article.id);
          }
        }}
        extra={isMaterialEdit ? <Button type="primary" onClick={this.onSave}>保存</Button> : null}
      >
        <Form labelCol={{ span: 4 }} className="material-drawer-form">
          <Form.Item label="标题">
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              {
                isMaterialEdit ? (
                  <Input.TextArea
                    value={material.title}
                    onChange={(e) => { return this.props.onChange(e, 'material', 'title'); }}
                  />
                ) : (material.title)
              }
              {/* {this.renderExtractBtns()} */}
            </div>
          </Form.Item>
          {/* {this.renderMeta(material?.meta)} */}
          <Form.Item label="生成时间">
            {material.createdAt}
          </Form.Item>
          <Form.Item label="文稿源">
            <a target="_blank" href={material.sourceUrl} rel="noreferrer">{material.sourceUrl}</a>
          </Form.Item>
          {/* <Form.Item label="分配的KOL">
            <Select
              value={_.isUndefined(article.characterId) ? undefined : +article.characterId}
              onChange={(e) => { return this.onChangeCharacter(e); }}
            >
              {_.map(kolsMap, (v, k) => { return <Select.Option value={+k}>{v}</Select.Option>; })}
            </Select>
          </Form.Item>
          <Form.Item label="是否合格">
            {
              // eslint-disable-next-line no-nested-ternary
              isMaterialEdit ? (
                <Switch
                  checked={material.passed}
                  checkedChildren="合格"
                  unCheckedChildren="不合格"
                  onChange={async (e) => { this.props.onChange(e, 'material', 'passed'); }}
                />
              ) : (
                material.passed ? <Tag color="success">合格</Tag> : <Tag color="error">不合格</Tag>
              )
            }
          </Form.Item> */}
          <Form.Item label="正文">
            <Row>
              <Col span={article.id ? 12 : 24} style={{ paddingRight: 10 }}>
                <Tag color="processing" style={{ margin: '6px 0' }}>原稿</Tag>
                {
                  isMaterialEdit ? (
                    <Input.TextArea
                      autoSize={{ maxRows: 20, minRows: 20 }}
                      style={{ lineHeight: '32px' }}
                      value={this.parseMaterialContent(material.content)}
                      onChange={(e) => { return this.props.onChange(e, 'material', 'content'); }}
                    />
                  ) : (<p className="preview-txt">{this.parseMaterialContent(material.content)}</p>)
                }
              </Col>
              {
                article.id && (
                  <Col span={12} style={{ paddingRight: 10 }}>
                    <Tag color="success" style={{ margin: '6px 0' }}>成品稿</Tag>
                    {
                      isMaterialEdit ? (
                        <>
                          <Button
                            size="small"
                            type="primary"
                            style={{ margin: '6px 0 4px', float: 'right' }}
                            loading={isRerunning}
                            onClick={async () => {
                              const { optimizeJobId } = await this.props.rerunOptimizeJob(material.id);
                              this.props.initArticleTimer(article.id, optimizeJobId);
                            }}
                          >
                            重新创作
                          </Button>
                          <Input.TextArea
                            autoSize={{ maxRows: 20, minRows: 20 }}
                            style={{ lineHeight: '32px' }}
                            value={article.content}
                            onChange={(e) => { return this.props.onChange(e, 'article', 'content'); }}
                          />
                        </>
                      ) : (<p className="preview-txt">{article.content}</p>)
                    }
                  </Col>
                )
              }
            </Row>
          </Form.Item>
        </Form>

        {this.state.openLogs && this.renderLogDrawer()}
        {this.state.open && this.renderRelatedArticleDrawer()}
        {this.state.openArticle && this.renderArticleDetailDrawer()}
        {this.state.openViewpoints && this.renderUserInputModal()}
      </Drawer>
    );
  }
}
