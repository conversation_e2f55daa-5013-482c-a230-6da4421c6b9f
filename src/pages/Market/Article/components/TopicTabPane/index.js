import { PaginationTable } from '~/components';
import { Materials, Sessions } from '~/engine';
import { Platform } from '~/plugins';
import { But<PERSON>, DatePicker, Drawer, InputNumber, Select } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import MaterialDrawer from '../MaterialDrawer';

const DEFAULT_FILTER = {
  sourceGroupId: undefined,
  startAt: undefined,
  endAt: undefined,
  sourceId: undefined,
  fetchStatus: 'done',
  optimizeStatus: 'done',
  passed: 'true',
  minArticleCount: 5,
  intersectThreshold: 0.5,
};
const ANALYSIS_MAP = { diggCount: '点赞(阅读)数', commentCount: '评论数', shareCount: '分享数', collectCount: '收藏数' };

export default class TopicTabPane extends PureComponent {
  static propTypes = {
    kolsMap: PropTypes.object,
    materialSourcesMap: PropTypes.object,
    materialSourceGroups: PropTypes.array,
    materialSourcesList: PropTypes.array,
    onSearch: PropTypes.func,
  }

  state = {
    list: [],
    dataSource: [],
    articles: [],
    relatedArticles: [],
    selectedRowKeys: [],
    filter: DEFAULT_FILTER,
    selectedTagObjs: {},
  }

  componentDidMount = async () => {
    let list = [];
    const baseUrl = 'https://video-clip.oss-cn-shanghai.aliyuncs.com/temp/material/topic';
    const { partnerId } = Sessions.getPartner();
    const env = Platform.isProd() ? 'production' : 'staging';

    try { // eslint-disable-next-line
      const resp = await fetch(`${baseUrl}/${env}-${partnerId}.json?t=${new Date().getTime()}`);
      list = await resp.json();
    } catch (error) {
      list = [];
    }

    this.setState({ list });
  }

  onChangeFilter = async (e, key) => {
    const value = e?.target ? e?.target?.value : e;
    await this.setState({ filter: { ...this.state.filter, [key]: value } });
  }

  onShowArticleDrawer = async (row) => {
    const { items } = await Materials.fetchmaterials({ ids: row.articles });
    const articles = _.orderBy(items, ['diggCount'], 'desc');
    this.setState({ openArticleDrawer: true, articles });
  }

  onShowMaterialDrawer = async (row) => {
    const article = await Materials.getArticleWithId(row.id);
    this.setState({ openMaterial: true, material: row, article });
  }

  onAnalyze = async () => {
    const { filter } = this.state;
    const { result } = await Materials.analyzeTopic(filter);
    this.setState({ list: result });
  }

  renderArticleColumns = () => {
    return [
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        ellipsis: true,
        width: '30%',
        render: (txt, row) => {
          const btnProps = { onClick: () => { return this.onShowMaterialDrawer(row); } };
          return <a {...btnProps}>{txt}</a>;
        },
      },
      {
        title: '作者',
        dataIndex: 'sourceId',
        key: 'sourceId',
        align: 'center',
        render: (txt) => {
          const author = this.props.materialSourcesList.find((x) => { return x.value === txt; });
          return <a target="_blank" href={author?.url} rel="noreferrer">{author?.name}</a>;
        },
      },
      ..._.map(ANALYSIS_MAP, (v, k) => {
        return {
          title: v, dataIndex: k, key: k, align: 'center', width: 140, sorter: (a, b) => { return a[k] - b[k]; },
        };
      }),
      { title: '发布时间', dataIndex: 'pubDate', key: 'pubDate', align: 'center' },
    ];
  }

  renderArticleDrawer = (articles = [], key) => {
    const openKey = `open${key}Drawer`;
    const arrKey = _.trimEnd(_.camelCase(key), 's');

    return (
      <Drawer
        width="80vw"
        title="关联文章"
        placement="right"
        closable={false}
        className="material-drawer"
        open={this.state[openKey]}
        onClose={() => { this.setState({ [openKey]: false, [arrKey]: [], selectedTagObjs: {} }); }}
      >
        <PaginationTable
          columns={this.renderArticleColumns()}
          dataSource={articles}
          pagination={false}
        />
      </Drawer>
    );
  }

  renderColumns = () => {
    return [
      { title: '关键词', dataIndex: 'topic', key: 'topic', align: 'center' },
      {
        title: '总文章数',
        dataIndex: 'articles',
        key: 'articles',
        align: 'center',
        render: (x) => { return x.length; },
      },
      { title: '总得分', dataIndex: 'score', key: 'score', align: 'center' },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        render: (txt, row) => {
          return (
            <a onClick={() => { return this.onShowArticleDrawer(row); }}>
              关联文章
            </a>
          );
        },
      },
    ];
  }

  renderFilterBar = () => {
    const { filter } = this.state;
    const { materialSourceGroups, materialSourcesList } = this.props;

    return (
      <div>
        <Select
          placeholder="源分组"
          style={{ width: 180, marginRight: 16 }}
          allowClear
          value={filter?.sourceGroupId}
          onChange={(e) => { return this.onChangeFilter(e, 'sourceGroupId'); }}
        >
          {materialSourceGroups.map((w) => { return <Select.Option value={w.id}>{w?.name}</Select.Option>; })}
        </Select>
        <DatePicker.RangePicker
          style={{ marginRight: 16 }}
          value={_.isUndefined(filter?.startAt) ? [] : [moment(filter?.startAt), moment(filter?.endAt)]}
          onChange={async (e) => {
            const [startTime, endTime] = e || [];
            await this.onChangeFilter(startTime?.format('YYYY-MM-DDT00:00:00.SSSSSS') || undefined, 'startAt');
            await this.onChangeFilter(endTime?.format('YYYY-MM-DDT23:59:59.SSSSSS') || undefined, 'endAt');
          }}
        />
        <Select
          style={{ width: 260, marginBottom: 16, marginRight: 16 }}
          allowClear
          showSearch
          filterOption={(input, option) => { return option.children.includes(input); }}
          value={filter?.sourceId}
          placeholder="请选择文稿源"
          onChange={(e) => { return this.onChangeFilter(e, 'sourceId'); }}
        >
          {materialSourcesList.map((w) => { return <Select.Option value={w.value}>{w?.name}</Select.Option>; })}
        </Select>
        <InputNumber
          min={0}
          style={{ marginRight: 16 }}
          value={filter?.intersectThreshold}
          placeholder="交集阈值"
          onChange={(e) => { return this.onChangeFilter(e, 'intersectThreshold'); }}
        />
        <InputNumber
          min={1}
          placeholder="最小文章数"
          value={filter?.minArticleCount}
          onChange={(e) => { return this.onChangeFilter(e, 'minArticleCount'); }}
        />
        <Button type="primary" onClick={this.onAnalyze}>分析</Button>
      </div>
    );
  }

  render = () => {
    const { list, articles, openArticleDrawer, openMaterial, material, article } = this.state;
    return (
      <>
        {this.renderFilterBar()}

        <PaginationTable
          columns={this.renderColumns()}
          dataSource={list}
          pagination={false}
        />

        {openArticleDrawer && this.renderArticleDrawer(articles, 'Article')}
        {openMaterial &&
          <MaterialDrawer
            open={openMaterial}
            material={material}
            article={article}
            kolsMap={this.props.kolsMap}
            materialSourcesMap={this.props.materialSourcesMap}
            onSearch={this.props.onSearch}
            onClose={() => { this.setState({ openMaterial: false }); }}
          />
        }
      </>
    );
  }
}
