import './index.less';
import '../ArticleDrawer/index.less';

import { PaginationTable } from '~/components';
import { <PERSON><PERSON>, Drawer, Select, Tag, Typography } from 'antd';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';

import WritingDrawer from './WritingDrawer';

export default class LiveMaterialTabPane extends Component {
  static propTypes = {
    topics: PropTypes.array.isRequired,
    topicsTotal: PropTypes.number.isRequired,
    topicsPagination: PropTypes.object.isRequired,
    fetchDistinctMaterialTopics: PropTypes.func.isRequired,
  }

  state = {
    openTopicDetail: false,
    selectedTopicData: null,
    openWritingDrawer: false,
  }

  // 锚点跳转方法
  scrollToTopic = (topicId) => {
    const element = document.getElementById(`topic-${topicId}`);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest',
      });
      // 添加高亮效果
      element.classList.add('topic-highlight');
      setTimeout(() => {
        element.classList.remove('topic-highlight');
      }, 2000);
    }
  }

  onWritingDrawerClose = () => {
    this.setState({ openWritingDrawer: false });
  }

  renderTopicItem = (topic, index) => {
    return (
      <div key={index} id={`topic-${topic.id || index}`} className="topic-item">
        {/* 话题标题 */}
        <div className="topic-title">
          {topic.name || '未命名话题'} - [ {topic.author} ]
        </div>
        <div className="topic-field">
          <span className="field-label">提取时间：</span>
          {moment(topic.createdAt).format('YYYY-MM-DD HH:mm:ss')}
        </div>

        {/* 受众群体 */}
        {topic.audience && topic.audience.length > 0 && (
          <div className="topic-field">
            <span className="field-label">受众群体：</span>
            {topic.audience.map((aud) => {
              return (<span key={aud} className="audience-tag">{aud}</span>);
            })}
          </div>
        )}

        {/* 核心观点 */}
        {topic.coreViewpoint && (
          <div className="topic-field">
            <span className="field-label">核心观点：</span>
            <span className="field-content">{topic.coreViewpoint}</span>
          </div>
        )}

        {/* 逻辑链条 */}
        {topic.logic && (
          <div className="topic-field">
            <span className="field-label">逻辑链条：</span>
            <span className="field-content">{topic.logic}</span>
          </div>
        )}

        {/* 问答对 */}
        {topic.questionsAndAnswers && topic.questionsAndAnswers.length > 0 && (
          <div className="topic-field">
            <div className="field-title">问答列表：</div>
            {topic.questionsAndAnswers.map((qa) => {
              return (
                <div className="qa-item">
                  <div className="qa-question">Q: {qa.question}</div>
                  <div className="qa-answer">A: {qa.answer}</div>
                </div>
              );
            })}
          </div>
        )}

        {/* 关键陈述 */}
        {topic.keyStatements && topic.keyStatements.length > 0 && (
          <div className="topic-field">
            <div className="field-title">关键陈述：</div>
            {topic.keyStatements.map((statement) => {
              return (
                <div key={statement} className="key-statement" >
                  &quot;{statement}&quot;
                </div>
              );
            })}
          </div>
        )}

        {/* 相关事实 */}
        {topic.relatedFacts && topic.relatedFacts.length > 0 && (
          <div className="topic-field">
            <div className="field-title">相关事实：</div>
            <ul className="related-facts-list">
              {topic.relatedFacts.map((fact) => {
                return (<li key={fact} className="fact-item">{fact}</li>);
              })}
            </ul>
          </div>
        )}
      </div>
    );
  }

  renderTopicDetailDrawer = () => {
    const { openTopicDetail, selectedTopicData } = this.state;

    if (!selectedTopicData) return null;

    return (
      <Drawer
        width="50vw"
        title={`话题详情 - ${selectedTopicData.name || '未命名话题'}`}
        open={openTopicDetail}
        onClose={() => { this.setState({ openTopicDetail: false, selectedTopicData: null }); }}
        className="topic-detail-drawer article-drawer"
        extra={
          <Button
            type="primary"
            onClick={() => {
              this.setState({ openWritingDrawer: true });
            }}
          >
            写稿
          </Button>
        }
      >
        <div className="topic-content-wrapper">
          <div className="topic-item">
            {/* 话题标题 */}
            <div className="topic-title">
              {selectedTopicData.name || '-'}
            </div>

            {/* 话题内容 */}
            {selectedTopicData.context && (
              <div className="topic-field">
                <span className="field-label">话题内容：</span>
                <span className="field-content">{selectedTopicData.context}</span>
              </div>
            )}

            {/* 相关事实 */}
            {selectedTopicData.relatedFacts && selectedTopicData.relatedFacts.length > 0 && (
              <div className="topic-field">
                <div className="field-title">相关事实：</div>
                <ul className="related-facts-list">
                  {selectedTopicData.relatedFacts.map((fact) => {
                    return (
                      <li key={`fact-${fact.slice(0, 20)}`} className="fact-item">{fact}</li>
                    );
                  })}
                </ul>
              </div>
            )}

            {/* 相关观点 */}
            {selectedTopicData.relatedViewpoints && selectedTopicData.relatedViewpoints.length > 0 && (
              <div className="topic-field">
                <div className="field-title">相关观点：</div>
                <ul className="related-facts-list">
                  {selectedTopicData.relatedViewpoints.map((item) => {
                    return (
                      <li key={`viewpoint-${item.viewpoint.slice(0, 20)}`} className="fact-item">
                        {item.viewpoint}
                        {item.sourceTopicIds.map((x) => {
                          return (
                            <>&nbsp;
                              <a
                                onClick={() => { return this.scrollToTopic(x); }}
                                style={{ cursor: 'pointer', color: '#1890ff' }}
                              >[{x}]
                              </a>
                              &nbsp;
                            </>
                          );
                        })}

                      </li>
                    );
                  })}
                </ul>
              </div>
            )}

            {/* 相关受众 */}
            {selectedTopicData.relatedAudiences && selectedTopicData.relatedAudiences.length > 0 && (
              <div className="topic-field">
                <span className="field-label">相关受众：</span>
                {selectedTopicData.relatedAudiences.map((item) => {
                  return (
                    <span key={`audience-${item.audience}`} className="audience-tag">
                      {item.audience}
                      {item.sourceTopicIds.map((x) => {
                        return (
                          <>&nbsp;
                            <a
                              onClick={() => { return this.scrollToTopic(x); }}
                              style={{ cursor: 'pointer', color: '#1890ff' }}
                            >[{x}]
                            </a>&nbsp;
                          </>
                        );
                      })}
                    </span>
                  );
                })}
              </div>
            )}
          </div>
        </div>
        {
          selectedTopicData.topics && selectedTopicData.topics.length > 0 &&
          <div className="article-drawer-form">
            <Typography.Title level={5}>子话题列表：</Typography.Title>
            <div className="topic-section">
              {selectedTopicData.topics.map((topic, index) => {
                return this.renderTopicItem(topic, index);
              })}
            </div>
          </div>
        }
      </Drawer>
    );
  }

  renderTopicTable = () => {
    const topicColumns = [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center', width: 80 },
      {
        title: '话题',
        dataIndex: 'name',
        key: 'name',
        ellipsis: true,
        width: '10%',
        render: (text) => { return text || '-'; },
      },
      {
        title: '分类',
        dataIndex: 'category',
        key: 'category',
        align: 'center',
        width: 100,
        render: (text) => {
          if (!text) return '-';
          const color = text === 'video' ? 'blue' : 'green';
          return <Tag color={color}>{text === 'video' ? '视频' : '直播'}</Tag>;
        },
      },
      {
        title: '相关话题',
        dataIndex: 'duplicateTopicIds',
        key: 'duplicateTopicIds',
        align: 'center',
        width: 150,
        render: (text, row) => {
          if (!text || text.length === 0) return '-';
          return (
            <a onClick={() => { this.setState({ openTopicDetail: true, selectedTopicData: row }); }} >
              {text.length}
            </a>
          );
        },
      },
      { title: '话题内容', dataIndex: 'context', key: 'context', ellipsis: true, width: '30%' },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (text) => {
          if (!text) return '-';
          return new Date(text).toLocaleString('zh-CN');
        },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        width: 100,
        render: (txt, row) => {
          return (
            <a onClick={() => { this.setState({ openTopicDetail: true, selectedTopicData: row }); }} >
              查看
            </a>
          );
        },
      },
    ];


    return (
      <PaginationTable
        pagination={this.props.topicsPagination}
        totalDataCount={this.props.topicsTotal}
        dataSource={this.props.topics}
        columns={topicColumns}
        onPaginationChange={(pageIndex, pageSize) => {
          return this.props.fetchDistinctMaterialTopics(pageIndex, pageSize);
        }}
      />
    );
  }

  render() {
    const { openWritingDrawer, selectedTopicData } = this.state;

    return (
      <>
        <Select
          style={{ width: '200px', marginBottom: '16px' }}
          placeholder="选择话题分类"
          onChange={(value) => {
            this.props.fetchDistinctMaterialTopics({
              pageIndex: 1,
              pageSize: this.props.topicsPagination.pageSize,
              category: value || undefined, // 如果没有选择分类，则传 undefined
            });
          }}
        >
          <Select.Option value="">全部分类</Select.Option>
          <Select.Option value="video">视频</Select.Option>
          <Select.Option value="live">直播</Select.Option>
        </Select>

        {this.renderTopicTable()}
        {this.renderTopicDetailDrawer()}
        {
          openWritingDrawer &&
          <WritingDrawer
            open={openWritingDrawer}
            topicData={selectedTopicData}
            onClose={this.onWritingDrawerClose}
          />
        }
      </>
    );
  }
}
