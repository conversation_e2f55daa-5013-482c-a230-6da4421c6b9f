import './WritingDrawer.less';

import { Toast } from '~/components';
import Consts from '~/consts';
import { ChatBot, Materials } from '~/engine';
import { Platform } from '~/plugins';
import { Button, Drawer, Form, InputNumber, Mentions, Radio, Select, Spin } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';

const MENTIONS_OPTIONS = [
  { value: 'title', label: '标题' },
  { value: 'content', label: '内容' },
  { value: 'author', label: '作者' },
];

const generateDefaultValue = (type) => {
  switch (type) {
    case 'str':
      return '';
    case 'bool':
      return false;
    case 'int':
      return 0;
    default:
      return '';
  }
};

export default class WritingDrawer extends Component {
  static propTypes = {
    open: PropTypes.bool,
    topicData: PropTypes.object,
    onClose: PropTypes.func,
  }

  state = {
    loading: false,
    submitting: false,
    workflows: [],
    selectedWorkflows: [], // 改为多选工作流数组
    workflowParams: [],
    workflowConfig: {},
    selectedViewpoint: null,
  }

  componentDidMount = () => {
    this.fetchWorkflows();
  }

  fetchWorkflows = async () => {
    this.setState({ loading: true });
    try {
      // 参考RewriteDrawer的实现，从OSS CDN获取工作流配置
      const timestamp = new Date().getTime();
      const resp = await fetch(`${Consts.OSS_CDN_DOMAIN}/llmbot/plugin/configs/aivideo.json?t=${timestamp}`);
      const data = await resp.json();
      const env = Platform.isProd() ? 'production' : 'staging';
      const workflows = data?.[env]?.material_flows || [];

      this.setState({
        workflows,
        selectedWorkflows: [],
        workflowParams: [],
        workflowConfig: {},
      });
    } catch (error) {
      Toast.show('获取写稿方案失败', Toast.Type.ERROR);
    } finally {
      this.setState({ loading: false });
    }
  }


  fetchWorkflowParams = async (workflowUuid) => {
    try {
      // 使用API获取工作流详情，而不是从配置文件获取
      const workflow = await ChatBot.getChatbotWorkflow(workflowUuid);
      const content = JSON.parse(workflow.content);
      const inputSchema = content.nodes[0].data.input_schema;

      // 同时获取配置文件中的默认值
      const timestamp = new Date().getTime();
      const resp = await fetch(`${Consts.OSS_CDN_DOMAIN}/llmbot/plugin/configs/aivideo.json?t=${timestamp}`);
      const data = await resp.json();
      const env = Platform.isProd() ? 'production' : 'staging';
      const workflows = data?.[env]?.material_flows || [];
      const configWorkflow = workflows.find((w) => { return w.workflow_uuid === workflowUuid; });

      const workflowParams = [];
      Object.entries(inputSchema).forEach(([key, value]) => {
        workflowParams.push({
          key,
          description: value.description || key,
          default: value.default || '',
          type: value.type || 'str',
        });
      });

      // 设置默认值
      const stateParams = { workflowParams };
      const varDefaults = configWorkflow?.var_defaults || {};
      if (!_.isEmpty(varDefaults)) {
        const defaults = {};
        Object.entries(varDefaults).forEach(([key, value]) => {
          defaults[key] = value;
        });
        stateParams.workflowConfig = { ...this.state.workflowConfig, ...defaults };
      }

      this.setState(stateParams);
    } catch (error) {
      this.setState({ workflowParams: [] });
    }
  }


  // 取消操作
  onCancel = () => {
    if (this.props.onClose) {
      this.props.onClose();
    }
  }

  // 提交写稿请求
  onSubmit = async () => {
    const { selectedWorkflows, selectedViewpoint, workflowParams, workflowConfig } = this.state;
    const { topicData } = this.props;

    // 验证必填项
    if (!selectedViewpoint) {
      Toast.show('请选择观点', Toast.Type.WARNING);
      return;
    }

    if (selectedWorkflows.length === 0) {
      Toast.show('请至少选择一个写稿方案', Toast.Type.WARNING);
      return;
    }

    this.setState({ submitting: true });
    try {
      // 构造工作流参数配置
      const finalWorkflowConfig = {};
      workflowParams.forEach((param) => {
        if (workflowConfig[param.key] !== undefined) {
          finalWorkflowConfig[param.key] = workflowConfig[param.key];
        } else {
          finalWorkflowConfig[param.key] = generateDefaultValue(param.type);
        }
      });

      const workflowSettings = selectedWorkflows.map((workflow) => {
        return {
          workflow_uuid: workflow.workflow_uuid,
          workflow_config: finalWorkflowConfig,
        };
      });

      const optimizeConfig = {
        workflow_settings: workflowSettings,
      };

      let topic = `${topicData?.context}\n\n相关事实:\n ${topicData?.relatedFacts.join('\n')}`;

      const subTopic = topicData?.topics.find((x) => {
        return selectedViewpoint === x?.coreViewpoint;
      });
      if (subTopic) {
        topic += `\n\n关键论点: ${subTopic.keyStatements.join(', ')}\n\n逻辑: ${subTopic.logic}`;
        topic += `\n\n问题和答案: ${subTopic.questionsAndAnswers.map((q) => {
          return `${q.question} - ${q.answer}`;
        }).join(', ')}`;
      }

      const writingParams = {
        topicId: topicData?.id,
        title: selectedViewpoint,
        topic,
        optimizeConfig,
      };

      await Materials.genTopicMaterial(writingParams);

      Toast.show('写稿任务已提交，请稍后查看结果', Toast.Type.SUCCESS);
      this.props.onClose();
    } catch (error) {
      Toast.show('提交失败，请重试', Toast.Type.ERROR);
    } finally {
      this.setState({ submitting: false });
    }
  }

  // 观点选择变化
  onViewpointChange = (e) => {
    this.setState({ selectedViewpoint: e.target.value });
  }

  // 工作流选择变化（多选）
  onWorkflowChange = async (workflowUuids) => {
    const selectedWorkflows = this.state.workflows.filter((w) => {
      return workflowUuids.includes(w.workflow_uuid);
    });
    this.setState({ selectedWorkflows });

    // 如果有选中的工作流，获取第一个工作流的参数配置
    if (selectedWorkflows.length > 0) {
      const firstWorkflow = selectedWorkflows[0];
      await this.fetchWorkflowParams(firstWorkflow.workflow_uuid);
    } else {
      // 如果没有选中的工作流，清空参数配置
      this.setState({
        workflowParams: [],
        workflowConfig: {},
      });
    }
  }


  // 工作流参数变化
  onWorkflowParamChange = (value, key) => {
    this.setState((prevState) => {
      return {
        workflowConfig: {
          ...prevState.workflowConfig,
          [key]: value,
        },
      };
    });
  }


  render() {
    const { open, topicData } = this.props;
    const { loading, workflows, selectedWorkflows, workflowParams,
      workflowConfig, submitting, selectedViewpoint } = this.state;

    if (!topicData) return null;

    return (
      <Drawer
        title="选择观点和写稿方案"
        open={open}
        width="35vw"
        onClose={this.onCancel}
        maskClosable={false}
        className="writing-drawer"
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button onClick={this.onCancel} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button
              type="primary"
              onClick={this.onSubmit}
              loading={submitting}
            >
              开始写稿
            </Button>
          </div>
        }
      >
        <Spin spinning={loading}>
          <Form layout="vertical">
            <Form.Item label="选择观点" required>
              <Radio.Group
                value={selectedViewpoint}
                onChange={this.onViewpointChange}
                style={{ width: '100%' }}
              >
                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                  {(topicData.relatedViewpoints || []).map((viewpointItem, index) => {
                    const viewpointText = viewpointItem.viewpoint;
                    const viewpointKey = `viewpoint-${index}`;
                    return (
                      <Radio key={viewpointKey} value={viewpointText} style={{ marginLeft: 0 }}>
                        {viewpointText}
                      </Radio>
                    );
                  })}
                </div>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              label="写稿方案"
              required
              help="可选择多个工作流方案进行写稿"
            >
              <Select
                mode="multiple"
                placeholder="请选择写稿方案（可多选）"
                value={selectedWorkflows.map((w) => { return w.workflow_uuid; })}
                onChange={this.onWorkflowChange}
                style={{ width: '100%' }}
                showSearch
                filterOption={(input, option) => {
                  return option.children.toLowerCase().includes(input.toLowerCase());
                }}
              >
                {workflows.map((workflow) => {
                  return (
                    <Select.Option key={workflow.workflow_uuid} value={workflow.workflow_uuid}>
                      {workflow.name}
                    </Select.Option>
                  );
                })}
              </Select>
            </Form.Item>

            {/* 显示已选择的工作流 */}
            {selectedWorkflows.length > 0 && (
              <Form.Item label="已选择的方案">
                <div className="selected-workflows-container">
                  {selectedWorkflows.map((workflow, index) => {
                    return (
                      <div key={workflow.workflow_uuid} className="selected-workflow-item">
                        <strong>{workflow.name}</strong>
                        {index === 0 && <span className="primary-workflow-tag">（主要配置）</span>}
                      </div>
                    );
                  })}
                </div>
              </Form.Item>
            )}

            {workflowParams.length > 0 && (
              <Form.Item label="参数配置">
                <div className="workflow-params">
                  {workflowParams.map((param) => {
                    let paramInput;
                    switch (param.type) {
                      case 'int':
                        paramInput = (
                          <InputNumber
                            value={workflowConfig[param.key] || param.default}
                            onChange={(value) => { return this.onWorkflowParamChange(value, param.key); }}
                            style={{ width: '100%' }}
                          />
                        );
                        break;
                      default:
                        paramInput = (
                          <Mentions
                            prefix="{"
                            autoSize={{ minRows: 1 }}
                            value={workflowConfig[param.key] || param.default}
                            onChange={(value) => { return this.onWorkflowParamChange(value, param.key); }}
                            options={MENTIONS_OPTIONS}
                            placeholder="支持{{变量}}格式"
                            style={{ width: '100%' }}
                          />
                        );
                        break;
                    }

                    return (
                      <div key={param.key} className="param-item">
                        <div className="param-label">
                          <strong>{param.description || param.key}：</strong>
                        </div>
                        <div className="param-input">
                          {paramInput}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </Form.Item>
            )}
          </Form>
        </Spin>

      </Drawer>
    );
  }
}
