.writing-drawer {
  .topic-info-card {
    margin-bottom: 16px;
    padding: 16px;
    border: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 8px;

    .info-item {
      margin-bottom: 8px;
      line-height: 1.6;

      &:last-child {
        margin-bottom: 0;
      }

      strong {
        margin-right: 8px;
        color: #333;
      }
    }
  }

  .param-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    gap: 12px;

    .param-label {
      flex-shrink: 0;
      min-width: 120px;
      padding-top: 6px;
      color: #333;
    }

    .param-input {
      flex: 1;
    }
  }

  .ant-form-item {
    margin-bottom: 24px;
  }

  .ant-checkbox-group {
    .ant-checkbox-wrapper {
      display: block;
      margin-bottom: 8px;
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      transition: all 0.3s;

      &:hover {
        background-color: #f0f8ff;
        border-color: #40a9ff;
      }

      &.ant-checkbox-wrapper-checked {
        background-color: #e6f7ff;
        border-color: #1890ff;
      }
    }
  }

  .ant-select,
  .ant-input-number {
    width: 100%;
  }

  .ant-drawer-footer {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
  }

  .selected-workflows-container {
    .selected-workflow-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding: 8px 12px;
      border: 1px solid #b7eb8f;
      background-color: #f6ffed;
      border-radius: 6px;

      &:last-child {
        margin-bottom: 0;
      }

      .primary-workflow-tag {
        margin-left: 8px;
        padding: 2px 6px;
        font-size: 12px;
        color: white;
        background-color: #52c41a;
        border-radius: 4px;
      }
    }
  }
}
