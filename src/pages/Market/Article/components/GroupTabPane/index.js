import { PaginationTable } from '~/components';
import Consts from '~/consts';
import { ChatBot, Materials } from '~/engine';
import { Platform, StringExtension } from '~/plugins';
import {
  Button,
  Divider,
  Drawer,
  Form,
  Input,
  InputNumber,
  Mentions,
  Modal,
  Popconfirm,
  Radio,
  Select,
  Switch,
  Tag,
} from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

// const ANALYSIS_MAP = { diggCount: '点赞(阅读)数', commentCount: '评论数', shareCount: '分享数', collectCount: '收藏数' };

const META_PROMPT = `
{{material.content}}

提取文章的主题、主要人物、事件、关键词。

以JSON格式输出：
{
"主题": "主题内容",
"主要人物": [人物名称列表],
"事件": [事件列表],
"关键词": [关键词列表]
}`;

const MENTIONS_OPTIONS = [
  { value: '{title}}', label: '标题' },
  { value: '{content}}', label: '内容' },
  { value: '{collect_count}}', label: '收藏数' },
  { value: '{comment_count}}', label: '评论数' },
  { value: '{digg_count}}', label: '点赞数' },
  { value: '{share_count}}', label: '分享数' },
  { value: '{source_url}}', label: '来源链接' },
];

// 分类与平台的映射关系
const CATEGORY_PLATFORM_MAP = {
  video: '抖音',
  live: '抖音直播',
};

const generateDefaultValue = (type) => {
  switch (type) {
    case 'str':
      return '';
    case 'bool':
      return false;
    case 'int':
      return 0;
    default:
      return '';
  }
};

// 根据分类获取材料源选项
const getMaterialSourceOptions = (category, materialSourcesList) => {
  const targetPlatform = CATEGORY_PLATFORM_MAP[category];
  return targetPlatform
    ? _.filter(materialSourcesList, (item) => { return item.platform === targetPlatform; })
    : [];
};

export default class GroupTabPane extends PureComponent {
  static propTypes = {
    requestParams: PropTypes.object.isRequired,
    materialSourceGroupsTotal: PropTypes.number.isRequired,
    materialSourceGroups: PropTypes.array.isRequired,
    materialSourcesList: PropTypes.array.isRequired,
    createItem: PropTypes.func.isRequired,
    updateItem: PropTypes.func.isRequired,
    deleteItem: PropTypes.func.isRequired,
  }

  state = {
    data: {},
    workflowParams: [],
    workflows: [],
  }

  componentDidMount = async () => {
    const timestamp = new Date().getTime();
    const resp = await fetch(`${Consts.OSS_CDN_DOMAIN}/llmbot/plugin/configs/aivideo.json?t=${timestamp}`);
    const data = await resp.json();
    const env = Platform.isProd() ? 'production' : 'staging';
    const workflows = data?.[env]?.material_flows || [];
    this.setState({ workflows });
  }

  onChangeValue = (e, type) => {
    const value = e.target ? e.target.value : e;
    this.setState({ data: { ...this.state.data, [type]: value } });
  }

  onChangeFilterValue = (e, type) => {
    const value = e.target ? e.target.value : e;
    this.setState({ data: { ...this.state.data, filter: { ...this.state.data.filter, [type]: value } } });
  }

  onChangeWorkflow = async (workflowUuids = []) => {
    const [workflowUuid] = workflowUuids;

    if (!_.isUndefined(workflowUuid)) {
      const workflow = await ChatBot.getChatbotWorkflow(workflowUuid); // eslint-disable-next-line
      const { var_defaults } = this.state.workflows.find((w) => { return w.workflow_uuid === workflowUuid; });
      const content = JSON.parse(workflow.content);
      const inputSchema = content.nodes[0].data.input_schema;
      const workflowParams = [];
      Object.entries(inputSchema).forEach(
        ([key, value]) => {
          workflowParams.push({
            key, description: value.description || '', default: value.default || '', type: value.type || 'str',
          });
        },
      );
      const stateParams = { workflowParams };
      if (!_.isEmpty(var_defaults)) {
        const defaults = {};
        Object.entries(var_defaults).forEach(([key, value]) => {
          defaults[key] = this.state.data[key] || value;
        });
        stateParams.data = { ...this.state.data, ...defaults };
      }
      this.setState(stateParams);
    } else {
      this.setState({ workflowParams: [], data: { ...this.state.data, workflowUuid: undefined } });
    }

    this.onChangeValue(workflowUuid || '', 'workflowUuid');
    this.onChangeValue(workflowUuids || [], 'workflowUuids');
  }

  onShowMaterialSource = async (row) => {
    const { items } = await Materials.fetchmaterialSources({
      ...this.props.requestParams, ...Consts.ALL_PAGE_PARAMS, sourceGroupId: row.id,
    });
    const materialSourceIds = _.orderBy(_.map(items, 'id'));
    this.setState({
      data: row,
      openMaterialSource: true,
      materialSourceIds,
      oldIds: materialSourceIds,
      sourceGroupId: row.id,
    });
  }

  onLinkMaterialSource = async () => {
    const { materialSourceIds, oldIds, sourceGroupId } = this.state;
    const putPromises = [];
    const delPromises = [];
    const obj = {};
    materialSourceIds.forEach((x) => {
      obj[x] = [];
      putPromises.push(Materials.getMaterialSourceGroups(x));
    });

    const removeIds = _.difference(oldIds, materialSourceIds);
    removeIds.forEach((x) => {
      obj[x] = [];
      delPromises.push(Materials.getMaterialSourceGroups(x));
    });

    const [putResps, delResps] = await Promise.all([Promise.all(putPromises), Promise.all(delPromises)]);
    materialSourceIds.forEach((x, i) => {
      obj[x] = _.uniq([...(putResps[i].groupIds), sourceGroupId]);
    });
    removeIds.forEach((x, i) => {
      obj[x] = _.filter(delResps[i].groupIds, (w) => { return w !== sourceGroupId; });
    });

    const promises = [];
    _.map(obj, (v, k) => { promises.push(Materials.putMaterialSourceGroups({ id: k, groupIds: v })); });
    await Promise.all(promises);

    this.setState({ openMaterialSource: false, materialSourceIds: [], oldIds: [], data: {}, sourceGroupId: undefined });
  }

  onSubmitGroup = async () => {
    const { workflowParams } = this.state;
    const data = { ...this.state.data };
    const { id, name } = this.state.data;
    if (!name) { return Modal.error({ title: '名称不能为空' }); }

    if (!_.isUndefined(workflowParams)) {
      const workflowKeys = _.map(workflowParams, 'key');
      const workflowConfig = {};
      _.forEach(workflowKeys, (item) => {
        if (data[item] !== undefined) {
          workflowConfig[item] = data[item];
          delete data[item];
        } else {
          workflowConfig[item] = generateDefaultValue(workflowParams.find((x) => { return x.key === item; }).type);
        }
      });
      data.setting = {
        workflow_setting: {
          workflow_uuid: data.workflowUuid,
          workflow_config: workflowConfig,
        },
      };
    }

    if (!_.isEmpty(data.workflowUuids)) {
      data.setting.workflow_settings = _.map(data.workflowUuids, (uid) => {
        return { workflow_uuid: uid, workflow_config: data.setting.workflow_setting.workflow_config };
      });
    }

    if (id) {
      await this.props.updateItem('materialSourceGroup', data);
    } else {
      const result = await this.props.createItem('materialSourceGroup', data);
      await this.setState({ oldIds: [], sourceGroupId: result.id });
    }

    await this.onLinkMaterialSource();
    return this.setState({ openGroup: false, data: {} });
  }

  renderGroupModal = () => {
    const { openGroup, data } = this.state;
    const { materialSourcesList } = this.props;
    const materialSourcesOptions = getMaterialSourceOptions(data?.category, materialSourcesList);

    return (
      <Drawer
        open={openGroup}
        title={data?.id ? '编辑分组' : '新增分组'}
        width="50vw"
        onClose={() => {
          return this.setState({ openGroup: false, data: {}, workflowParams: [], materialSourceIds: [] });
        }}
        extra={
          <Button type="primary" onClick={this.onSubmitGroup}>
            确定
          </Button>
        }
      >
        <Form labelCol={{ span: 4 }} className="common-form">
          <Form.Item label="名称">
            <Input value={data?.name} onChange={(e) => { return this.onChangeValue(e, 'name'); }} />
          </Form.Item>
          <Form.Item label="分类">
            <Radio.Group
              value={data?.category}
              onChange={(e) => { return this.onChangeValue(e, 'category'); }}
            >
              <Radio value="video">视频</Radio>
              <Radio value="live">直播</Radio>
            </Radio.Group>
          </Form.Item>
          {
            !!materialSourcesOptions?.length && (
              <Form.Item label="文稿源">
                <Select
                  mode="multiple"
                  style={{ width: '100%' }}
                  value={this.state.materialSourceIds}
                  onChange={(e) => { return this.setState({ materialSourceIds: _.orderBy(e) }); }}
                >
                  {
                    _.map(materialSourcesOptions, (w) => {
                      return <Select.Option value={w.value}>{`${w.name} - [${w.platform}]`}</Select.Option>;
                    })
                  }
                </Select>
              </Form.Item>
            )
          }
          <Form.Item label="改稿方案">
            <Select
              mode="multiple"
              value={data?.workflowUuids || data?.workflowUuid || []}
              onChange={(e) => { return this.onChangeWorkflow(e); }}
            >
              {this.state.workflows.map((w) => {
                return (
                  <Select.Option key={w.workflow_uuid} value={w.workflow_uuid}>
                    {w?.name}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          {
            this.state.workflowParams.map((param) => {
              let contentStr = null;
              switch (param.type) {
                case 'bool':
                  contentStr = (
                    <Switch
                      checked={data?.[param.key]}
                      onChange={(e) => { return this.onChangeValue(e, param.key); }}
                    />
                  );
                  break;
                case 'int':
                  contentStr = (
                    <InputNumber
                      value={data?.[param.key] || param.default}
                      onChange={(e) => { return this.onChangeValue(e, param.key); }}
                    />
                  );
                  break;
                default:
                  contentStr = (
                    <Mentions
                      prefix="{"
                      autoSize={{ minRows: 1 }}
                      value={data?.[param.key] || param.default}
                      onChange={(e) => { return this.onChangeValue(e, param.key); }}
                      options={MENTIONS_OPTIONS}
                      placeholder="支持{{变量}}格式"
                    />
                  );
                  break;
              }

              return (
                <Form.Item
                  key={param.key}
                  label={param.description || param.key}
                >
                  {contentStr}
                </Form.Item>
              );
            })
          }
          {/* <Form.Item label="总结提示词">
            <Input.TextArea
              autoSize={{ minRows: 3 }}
              value={data?.metaPrompt}
              onChange={(e) => { return this.onChangeValue(e, 'metaPrompt'); }}
            />
          </Form.Item> */}
          {/* <Form.Item label="分配设定">
            <Input.Group compact style={{ display: 'flex', alignItems: 'center' }}>
              <Radio.Group
                value={data?.partitionType}
                onChange={(e) => { this.onChangeValue(e, 'partitionType'); }}
              >
                <Radio value="auto">随机分配</Radio>
                <Radio value="manual">手动分配</Radio>
                <Radio value="fixed">固定分配</Radio>
              </Radio.Group>
              {
                data?.partitionType === 'fixed' && (
                  <Select
                    style={{ width: 200 }}
                    value={data?.characterId}
                    onChange={(e) => { this.onChangeValue(e, 'characterId'); }}
                  >
                    {[].map((w) => {
                      return <Select.Option value={w.value}>{w?.name}</Select.Option>;
                    })}
                  </Select>
                )
              }
            </Input.Group>
          </Form.Item> */}
          {/* <Form.Item label="话题生成">
            <Input.Group compact style={{ display: 'flex', alignItems: 'center' }}>
              <InputNumber
                width={100}
                addonBefore="筛选近"
                addonAfter="天的文稿,按照"
                value={data?.filter?.days}
                onChange={(e) => { return this.onChangeFilterValue(e, 'days'); }}
              />
              <Select
                style={{ width: 120 }}
                value={data?.filter?.orderBy}
                onChange={(e) => { return this.onChangeFilterValue(e, 'orderBy'); }}
              >
                {
                  _.map(ANALYSIS_MAP, (v, k) => {
                    return <Select.Option value={`${k} asc`}>{v}</Select.Option>;
                  })
                }
              </Select>
              <InputNumber
                width={100}
                addonBefore="排序, 选取"
                addonAfter="篇的文稿"
                value={data?.filter?.limit}
                onChange={(e) => { return this.onChangeFilterValue(e, 'limit'); }}
              />
            </Input.Group>
          </Form.Item> */}
        </Form>
      </Drawer>
    );
  }

  renderMaterialSourceModal = () => {
    const { materialSourcesList } = this.props;
    const materialSourcesOptions = getMaterialSourceOptions(this.state.data?.category, materialSourcesList);

    return (
      <Modal
        maskClosable={false}
        title="关联文稿源"
        width={960}
        visible={this.state.openMaterialSource}
        onCancel={() => { return this.setState({ openMaterialSource: false, materialSourceIds: [], data: {} }); }}
        onOk={() => { return this.onLinkMaterialSource(); }}
      >
        <Select
          mode="multiple"
          style={{ width: '100%' }}
          value={this.state.materialSourceIds}
          onChange={(e) => { return this.setState({ materialSourceIds: _.orderBy(e) }); }}
        >
          {
            _.map(materialSourcesOptions, (w) => {
              return <Select.Option value={w.value}>{`${w.name} - [${w.platform}]`}</Select.Option>;
            })
          }
        </Select>
      </Modal>
    );
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'uuid', key: 'uuid', align: 'center' },
      { title: '分组名称', dataIndex: 'name', key: 'name', align: 'center' },
      {
        title: '分类',
        dataIndex: 'category',
        key: 'category',
        align: 'center',
        render: (txt) => {
          if (txt === 'video') { return <Tag color="blue">视频</Tag>; }
          if (txt === 'live') { return <Tag color="green">直播</Tag>; }

          return '-';
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <a onClick={() => { return this.onShowMaterialSource(row); }}>
                关联文稿源
              </a>
              <Divider type="vertical" />
              <a onClick={async () => {
                const { items } = await Materials.fetchmaterialSources({
                  ...this.props.requestParams, ...Consts.ALL_PAGE_PARAMS, sourceGroupId: row.id,
                });
                const materialSourceIds = _.orderBy(_.map(items, 'id'));
                const { workflowSetting, workflowSettings } = row.setting || {};
                const workflowUuids = _.map(workflowSettings, 'workflowUuid');
                let data = {
                  metaPrompt: META_PROMPT,
                  ...row,
                  workflowUuid: workflowSetting?.workflowUuid || '',
                  workflowUuids,
                };
                if (workflowSetting?.workflowConfig) {
                  data = { ...data, ...StringExtension.camelToSnakeObj(workflowSetting?.workflowConfig, {}) };
                }

                await this.setState({
                  openGroup: true,
                  materialSourceIds,
                  sourceGroupId: row.id,
                  oldIds: materialSourceIds,
                  data,
                });

                if (!_.isEmpty(data.workflowUuids) || data.workflowUuid) {
                  const ids = _.isEmpty(data.workflowUuids) ? [data.workflowUuid] : data.workflowUuids;
                  this.onChangeWorkflow(ids);
                }
              }}
              >
                编辑
              </a>
              <Divider type="vertical" />
              <Popconfirm
                title="确定删除吗？"
                onConfirm={() => { return this.props.deleteItem('materialSourceGroup', row.id); }}
              >
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  render = () => {
    const { materialSourceGroupsTotal, materialSourceGroups } = this.props;

    return (
      <>
        <Button
          type="primary"
          onClick={() => { return this.setState({ openGroup: true, data: { metaPrompt: META_PROMPT } }); }}
          style={{ float: 'right', marginBottom: 10 }}
        >新增分组
        </Button>
        <PaginationTable
          dataSource={materialSourceGroups}
          totalDataCount={materialSourceGroupsTotal}
          columns={this.renderColumns()}
        />

        {this.state.openGroup && this.renderGroupModal()}
        {this.state.openMaterialSource && this.renderMaterialSourceModal()}
      </>
    );
  }
}
