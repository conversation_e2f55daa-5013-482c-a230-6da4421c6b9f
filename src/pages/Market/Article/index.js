/* eslint-disable max-lines */
import { SyncOutlined } from '@ant-design/icons';
import { FilterBar, PaginationTable, Toast } from '~/components';
import { Button, DatePicker, Divider, Popconfirm, Select, Switch, Tabs, Tag } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { v4 as uuidV4 } from 'uuid';

import ArticleDrawer from './components/ArticleDrawer';
import GroupTabPane from './components/GroupTabPane';
import InstantSearchDrawer from './components/InstantSearchDrawer/InstantSearchDrawerV2';
import ManualAddDrawer from './components/InstantSearchDrawer/ManualAddDrawer';
import LiveMaterialTabPane from './components/LiveMaterialTabPane';
import MaterialDrawer from './components/MaterialDrawer';
import MaterialSourceDrawer from './components/MaterialSourceDrawer';
import ResearchReportTabPane from './components/ResearchReportTabPane';
import TopicMaterialTabPane from './components/TopicMaterialTabPane';
import TopicTabPane from './components/TopicTabPane';
import AnalysisTabPane from './components/AnalysisTabPane';
import reducer, * as actions from './state';
import Utils from './Utils';

const PLATFORMS = [
  { value: '抖音', name: '抖音' },
  { value: '抖音直播', name: '抖音直播' },
  { value: '视频号直播', name: '视频号直播' },
];

const JOBS_STATUS = { pending: '初始化', running: '执行中', skipped: '不创作', done: '成功', failed: '失败' };
const ANALYSIS_MAP = { diggCount: '点赞(场观)数', commentCount: '评论数', shareCount: '分享数', collectCount: '收藏(订单)数' };
const jobsMap = {};

@connect(
  (state) => {
    return state.marketArticle;
  },
  actions,
)
export default class MarketArticle extends Component {
  static propTypes = {
    isWework: PropTypes.bool,
    article: PropTypes.object,
    material: PropTypes.object,
    materials: PropTypes.array,
    materialsTotal: PropTypes.number,
    materialsPagination: PropTypes.object,
    liveMaterials: PropTypes.array,
    liveMaterialsTotal: PropTypes.number,
    liveMaterialsPagination: PropTypes.object,
    materialSource: PropTypes.object,
    materialSources: PropTypes.array,
    materialSourcesTotal: PropTypes.number,
    materialSourcesPagination: PropTypes.object,
    materialSourceGroups: PropTypes.array,
    jobs: PropTypes.array,
    jobsTotal: PropTypes.number,
    jobsPagination: PropTypes.object,
    articleKeywords: PropTypes.string,
    materialKeywords: PropTypes.string,
    liveMaterialKeywords: PropTypes.string,
    materialSourceKeywords: PropTypes.string,
    materialSourcePlatform: PropTypes.string,
    selectedMaterialSourceId: PropTypes.string,
    selectedLiveMaterialSourceId: PropTypes.string,
    selectedJobSourceId: PropTypes.string,
    selectedJobId: PropTypes.string,
    materialSourcesList: PropTypes.array,
    materialSourcesMap: PropTypes.object,
    workflowsMap: PropTypes.object,
    jobsList: PropTypes.array,
    kols: PropTypes.array,
    kolsMap: PropTypes.object,
    sourceGroupId: PropTypes.string,
    selectedArticleKolId: PropTypes.string,
    fetchItems: PropTypes.func.isRequired,
    deleteItem: PropTypes.func.isRequired,
    updateItem: PropTypes.func.isRequired,
    getItem: PropTypes.func.isRequired,
    createItem: PropTypes.func.isRequired,
    deleteItems: PropTypes.func.isRequired,
    fetchKols: PropTypes.func.isRequired,
    fetchMaterialSources: PropTypes.func.isRequired,
    fetchChatbotWorkflows: PropTypes.func.isRequired,
    fetchJobs: PropTypes.func.isRequired,
    retryJob: PropTypes.func.isRequired,
    getArticleWithId: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
    setState: PropTypes.func.isRequired,
    rerunOptimizeJob: PropTypes.func.isRequired,
    getMaterialOptimizeJobs: PropTypes.func.isRequired,
    putMaterialSourceGroups: PropTypes.func.isRequired,
    searchArticleByMeta: PropTypes.func.isRequired,
    createFetchJob: PropTypes.func.isRequired,
    manualSyncMaterial: PropTypes.func.isRequired, // 搜索同步文稿||重新创作
    manualSyncMaterialSource: PropTypes.func.isRequired,
    fetchDistinctMaterialTopics: PropTypes.func.isRequired,
    fetchTopicMaterials: PropTypes.func.isRequired,
    match: PropTypes.object.isRequired,
  }

  state = {
    activeKey: 'group',
    materialOpen: false,
    materialSourceOpen: false,
    manualAddOpen: false,
    isMaterialEdit: false,
    selectedMaterialSources: [],
    isRerunning: false,
    requestParams: { usage: 'character' },
    materialSources: [],
    windowWidth: window.innerWidth, // 添加窗口宽度状态
  }

  componentDidMount = async () => {
    await this.props.fetchItems('materialSources', this.state.requestParams);
    this.props.fetchItems('materialSourceGroups', { pageIndex: 1, pageSize: 1000 });
    this.props.fetchItems('materials');
    // this.props.fetchItems('liveMaterials');
    // this.props.fetchItems('articles');
    this.props.fetchItems('jobs');
    this.props.fetchKols();
    this.props.fetchMaterialSources(this.state.requestParams);
    this.props.fetchJobs();
    this.props.fetchChatbotWorkflows();
    this.props.fetchDistinctMaterialTopics();
    this.props.fetchTopicMaterials();

    // 添加窗口大小变化监听器
    this.handleResize = () => {
      this.setState({ windowWidth: window.innerWidth });
    };
    window.addEventListener('resize', this.handleResize);
  }

  componentWillUnmount = () => {
    // 清理窗口大小变化监听器
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }
    this.props.clearState();
  }

  clearTimerByArticleId = (articleId) => {
    if (jobsMap[articleId]) {
      clearInterval(jobsMap[articleId].timer);
      this.setState({ isRerunning: false });
    }
  }

  intervalJob = async (articleId, jobId) => {
    const { status } = await this.props.getMaterialOptimizeJobs(jobId);
    if (status === 'done') {
      clearInterval(jobsMap[articleId].timer);
      delete jobsMap[articleId];
      this.setState({ isRerunning: false });

      this.props.getItem('article', articleId);
      Toast.show('创作成功，已为您重新生成', Toast.Type.SUCCESS);
    } else if (status === 'failed') {
      clearInterval(jobsMap[articleId].timer);
      delete jobsMap[articleId];
      this.setState({ isRerunning: false });

      Toast.show('重新创作失败，请稍后重试', Toast.Type.WARNING);
    }
    return status;
  }

  initArticleTimer = async (articleId, jobId) => {
    let runningJobId = jobId;
    if (jobId) {
      jobsMap[articleId] = { jobId };
    } else if (jobsMap[articleId]) {
      runningJobId = jobsMap[articleId].jobId;
    }

    if (runningJobId) {
      this.setState({ isRerunning: true });
      let status;
      if (!jobId) {
        status = await this.intervalJob(articleId, runningJobId);
      }
      if (status !== 'done' && status !== 'failed') {
        jobsMap[articleId].timer = setInterval(async () => {
          this.intervalJob(articleId, runningJobId);
        }, 5000);
      }
    }
  }

  handleNumber = (key) => {
    return () => {
      const value = this.props.materialSource[key];
      let valueTemp = value;
      if (value.charAt(value.length - 1) === '.' || value === '-') {
        valueTemp = value.slice(0, -1);
      }
      this.onChangeValue(valueTemp.replace(/0*(\d+)/, '$1'), 'materialSource', key);
    };
  }

  jobToMaterials = async (jobId) => {
    await this.props.setState({ selectedJobId: jobId });
    this.props.fetchItems('materials');
    this.setState({ activeKey: 'materials' });
  }

  // 获取文稿表格的滚动配置
  getMaterialTableScroll = () => {
    const { windowWidth } = this.state;

    if (windowWidth < 1024) {
      return { x: 'max-content' }; // 内容宽度自适应
    } else if (windowWidth < 1440) {
      return { x: 1200 }; // 固定宽度滚动
    }

    return undefined; // 大屏幕不需要滚动
  }

  onChangeValue = (e, parentsKey, key) => {
    // eslint-disable-next-line no-nested-ternary
    const value = e?.target ? (e.target?.value ? e.target.value : e.target.checked) : e;
    this.props.setState({ [parentsKey]: { ...this.props[parentsKey], [key]: value } });
  }

  onAnalyzeUrl = async (url) => {
    const materialSource = await Utils.analyzeDyUrl(url);
    this.props.setState({ materialSource });
  }

  onManualAddSubmit = async (params) => {
    await this.props.manualSyncMaterial(params);
  }

  onOpenSource = async (source) => {
    await this.props.getItem('materialSource', source.usageId);
    this.setState({ materialSourceOpen: true });
  }

  onSyncMaterialSources = async (sourceGroupId) => {
    const datas = await this.props.fetchMaterialSources(
      { usage: 'character', sourceGroupId }, true,
    );
    await this.setState({ materialSources: datas });
  }

  onChangeTab = async (activeKey) => {
    this.setState({ activeKey });
    await this.props.fetchItems(activeKey);
    if (activeKey === 'materials') {
      await this.onSyncMaterialSources(this.props.sourceGroupId);
    }
  }

  onUpdateMaterialSource = async (row, value, key) => {
    await this.props.getItem('materialSource', row.usageId);
    await this.props.setState({ materialSource: { ...this.props.materialSource, [key]: value } });
    this.onCreateMaterialSource();
  }

  onCreateMaterialSource = async () => {
    const data = this.props.materialSource;

    const materialSource = {
      ...data,
      source_url: _.isEmpty(data.sourceUrl) ? `${window.location.href}/${uuidV4()}` : data.sourceUrl,
      setting: {
        fetch_setting: {
          enable_fetch_history: data.enableFetchHistory,
          history_limit: data.historyLimit,
          enable_fetch_new: data.enableFetchNew,
        },
        review_setting: {
          review_type: data.reviewType,
        },
        optimize_setting: {
          optimize_limit: data.optimizeLimit,
        },
        partition_setting: {
          partition_type: data.partitionType,
          character_id: data.characterId,
        },
      },
      enabled: data.enabled || false,
      autoAsr: data.autoAsr || false,
      ...this.state.requestParams,
    };
    const result = await this.props[materialSource.id ? 'updateItem' : 'createItem']('materialSource', materialSource);
    const mId = data.id || result.id;
    this.props.putMaterialSourceGroups({ id: mId, groupIds: materialSource.groupIds });
    this.setState({ materialSourceOpen: false });
  }

  renderMaterialSourcesColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center', width: 80 },
      { title: '作者', dataIndex: 'author', key: 'author', align: 'center' },
      { title: '平台', dataIndex: 'platform', key: 'platform', align: 'center' },
      { title: '简短说明', dataIndex: 'description', key: 'description', ellipsis: true, width: '20%' },
      {
        title: '来源链接',
        dataIndex: 'sourceUrl',
        key: 'sourceUrl',
        align: 'center',
        render: (txt) => { return <a href={txt} target="_blank" rel="noreferrer">查看</a>; },
      },
      {
        title: '待入库',
        dataIndex: 'pendingMaterialCount',
        key: 'pendingMaterialCount',
        align: 'center',
        render: (txt) => { return txt || '-'; },
      },
      {
        title: '最新文章时间',
        dataIndex: 'lastMaterialPubdate',
        key: 'lastMaterialPubdate',
        align: 'center',
        render: (txt) => {
          return txt ? moment(txt).format('YYYY-MM-DD HH:mm:ss') : '-';
        },
      },
      {
        title: '状态',
        dataIndex: 'enabled',
        key: 'enabled',
        align: 'center',
        render: (txt, row) => {
          return (
            <Switch
              checked={txt}
              checkedChildren="启用"
              unCheckedChildren="禁用"
              onChange={(e) => { return this.onUpdateMaterialSource(row, e, 'enabled'); }}
            />
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <a onClick={async () => {
                await this.props.setState({ selectedMaterialSourceId: row.id });
                await this.props.fetchItems('materials', { pageIndex: 1 });
                this.setState({ activeKey: 'materials' });
              }}
              >
                查看文稿
              </a>
              <Divider type="vertical" />
              <Popconfirm
                title="是否立即同步文稿?!"
                onConfirm={async () => {
                  await this.props.manualSyncMaterialSource(row.id);
                  Toast.show('同步任务已创建，请稍后查看文稿列表', Toast.Type.SUCCESS);
                }}
              >
                <a>同步文稿</a>
              </Popconfirm>
              <Divider type="vertical" />
              <a onClick={async () => {
                await this.props.getItem('materialSource', row.usageId);
                this.setState({ materialSourceOpen: true });
              }}
              >
                编辑
              </a>
              <Divider type="vertical" />
              <Popconfirm
                title="是否删除?!"
                onConfirm={() => { return this.props.deleteItem('materialSource', row.usageId); }}
              >
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  renderStatus = (title, key) => {
    return {
      title,
      dataIndex: key,
      key,
      align: 'center',
      render: (txt, row) => {
        let retryIcon = null;
        let tag;
        if (key === 'fetchStatus') {
          retryIcon = (
            <SyncOutlined
              style={{ cursor: 'pointer' }}
              onClick={() => {
                return this.props.updateItem('material', { id: row.id, fetchStatus: 'pending' });
              }}
            />
          );
        }

        switch (txt) {
          case 'done':
            tag = <Tag color="success">成功</Tag>;
            break;
          case 'failed':
            tag = <Tag color="error">失败</Tag>;
            tag = (
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                {tag}{retryIcon}
              </div>
            );
            break;
          case 'pending':
            tag = <Tag color="warning">初始化</Tag>;
            break;
          case 'running':
            tag = <Tag color="processing">执行中</Tag>;
            if (moment(row.updatedAt).isBefore(moment().subtract(2, 'hours'))) {
              tag = (
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                  {tag}{retryIcon}
                </div>
              );
            }
            break;
          default:
            tag = <Tag color="default">未知状态</Tag>;
        }
        return tag;
      },
    };
  }

  renderMaterialColumns = () => {
    const { materialSourcesList } = this.props;
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center', width: 120 },
      { title: '标题', dataIndex: 'title', key: 'title', ellipsis: true, width: '20%' },
      {
        title: '作者',
        dataIndex: 'sourceId',
        key: 'sourceId',
        align: 'center',
        render: (txt, row) => {
          const author = materialSourcesList.find((x) => { return x.value === txt; });
          return <a onClick={() => { return this.onOpenSource(author, row); }}>{author?.name}</a>;
        },
      },
      ..._.map(ANALYSIS_MAP, (v, k) => {
        return { title: v, dataIndex: k, key: k, align: 'center', width: 140, sorter: true };
      }),
      { title: '发布时间', dataIndex: 'pubDate', key: 'pubDate', align: 'center', sorter: true },
      this.renderStatus('抓取状态', 'fetchStatus'),
      this.renderStatus('创作状态', 'optimizeStatus'),
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <Button
                type="link"
                onClick={async () => {
                  await this.props.getArticleWithId(row.id);
                  await this.props.getItem('material', row.id);
                  this.setState({ articleOpen: true });
                }}
              > 编辑
              </Button>
              <Popconfirm
                title="是否删除?!"
                onConfirm={() => {
                  this.props.deleteItem('material', row.id);
                }}
              >
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  renderJobColumns = () => {
    return [
      {
        title: '抓取平台',
        dataIndex: 'platform',
        key: 'platform',
        align: 'center',
        render: (txt, row) => {
          return row.params ? row.params.platform : '';
        },
      },
      { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt', align: 'center' },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        align: 'center',
        render: (txt) => {
          let tag;
          switch (txt) {
            case 'done':
              tag = <Tag color="success">成功</Tag>;
              break;
            case 'failed':
              tag = <Tag color="error">失败</Tag>;
              break;
            case 'pending':
              tag = <Tag color="warning">初始化</Tag>;
              break;
            case 'running':
              tag = <Tag color="processing">执行中</Tag>;
              break;
            default:
              tag = <Tag color="default">未知状态</Tag>;
          }
          return tag;
        },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          let result = (
            <a onClick={async () => { return this.jobToMaterials(row.id); }}>查看文稿</a>
          );
          if (row.status === 'failed') {
            result = (
              <>
                {result}
                <Divider type="vertical" />
                <a onClick={async () => { this.props.retryJob(row.id); }}>重试</a>
              </>
            );
          }
          return result;
        },
      },
    ];
  }

  renderMaterialSourceSelects = () => {
    const { materialSourcePlatform, sourceGroupId, materialSourceGroups, isWework } = this.props;
    const { selectedMaterialSources } = this.state;
    return (
      <>
        <Popconfirm
          title="是否删除?!"
          onConfirm={() => { this.props.deleteItems('materialSources', { ids: selectedMaterialSources }); }}
        >
          <Button
            style={{ marginBottom: 16 }}
            disabled={!selectedMaterialSources || !selectedMaterialSources.length}
          >
            删除
          </Button>
        </Popconfirm>
        {
          !isWework &&
          <Select
            placeholder="源分组"
            style={{ width: 260, marginLeft: 16 }}
            allowClear
            value={sourceGroupId}
            onChange={async (e) => {
              await this.props.setState({ sourceGroupId: e });
              this.props.fetchItems('materialSources', this.state.requestParams);
            }}
          >
            {materialSourceGroups.map((w) => { return <Select.Option value={w.id}>{w?.name}</Select.Option>; })}
          </Select>
        }
        <Select
          placeholder="请选择平台"
          style={{ marginLeft: 16, width: 260 }}
          allowClear
          value={materialSourcePlatform}
          onChange={async (e) => {
            await this.props.setState({ materialSourcePlatform: e });
            this.props.fetchItems('materialSources', this.state.requestParams);
          }}
        >
          {PLATFORMS.map((w) => { return <Select.Option value={w.value}>{w?.name}</Select.Option>; })}
        </Select>
      </>
    );
  }

  renderMaterialSelects = () => {
    const { selectedMaterialSourceId, materialSourcesList,
      isWework, sourceGroupId, materialSourceGroups, startAt, endAt } = this.props; // eslint-disable-line
    let materialSources = _.isEmpty(this.state.materialSources) ? materialSourcesList : this.state.materialSources;
    if (!_.isUndefined(this.state.platform)) {
      materialSources = materialSourcesList.filter((x) => { return x.platform === this.state.platform; });
    }

    return (
      <>
        {
          !isWework &&
          <Select
            placeholder="源分组"
            style={{ width: 180, marginRight: 16 }}
            allowClear
            value={sourceGroupId}
            onChange={async (e) => {
              await this.props.setState({ sourceGroupId: e });
              await this.onSyncMaterialSources(this.props.sourceGroupId);
              this.props.fetchItems('materials');
            }}
          >
            {materialSourceGroups.map((w) => {
              return (
                <Select.Option value={w.id}>
                  <Tag size="small" color={w.category === 'video' ? 'blue' : 'green'}>
                    {w?.category === 'video' ? '视频' : ' 直播'}
                  </Tag>
                  &nbsp;{w?.name}
                </Select.Option>
              );
            })}
          </Select>
        }
        <DatePicker.RangePicker
          style={{ marginRight: 16 }}
          value={_.isUndefined(startAt) ? [] : [moment(startAt), moment(endAt)]}
          onChange={async (e) => {
            const [startTime, endTime] = e || [];
            await this.props.setState({
              startAt: startTime?.format('YYYY-MM-DDT00:00:00.SSSSSS') || undefined,
              endAt: endTime?.format('YYYY-MM-DDT23:59:59.SSSSSS') || undefined,
            });
            this.props.fetchItems('materials');
          }}
        />
        <Select
          style={{ width: 260, marginBottom: 16, marginRight: 16 }}
          allowClear
          showSearch
          filterOption={(input, option) => { return option.children.includes(input); }}
          value={selectedMaterialSourceId}
          placeholder="请选择文稿源"
          onChange={async (e) => {
            await this.props.setState({ selectedMaterialSourceId: e });
            this.props.fetchItems('materials');
          }}
        >
          {materialSources.map((w) => { return <Select.Option value={w.value}>{w?.name}</Select.Option>; })}
        </Select>
        {
          [
            { name: '抓取状态', value: 'fetchStatus', opts: JOBS_STATUS },
            { name: '创作状态', value: 'optimizeStatus', opts: JOBS_STATUS },
          ].map((x) => {
            return (
              <Select
                style={{ width: 120, marginBottom: 16, marginRight: 16 }}
                allowClear
                showSearch={x.showSearch}
                filterOption={(input, option) => { return option.children.includes(input); }}
                value={this.props[x.value]}
                placeholder={x.name}
                onChange={async (e) => {
                  await this.props.setState({ [x.value]: e });
                  this.props.fetchItems('materials');
                }}
              >
                {_.map(x.opts, (v, k) => { return <Select.Option value={k}>{v}</Select.Option>; })}
              </Select>
            );
          })
        }
      </>
    );
  }

  renderMaterialButtons = () => {
    return [
      <Button
        type="primary"
        onClick={() => { return this.setState({ searchOpen: true }); }}
      >
        即时搜索
      </Button>,
      <Divider type="vertical" />,
      <Button
        type="primary"
        onClick={() => { return this.setState({ manualAddOpen: true }); }}
      >
        手动新增
      </Button>,
    ];
  }

  renderjobSelects = () => {
    const { selectedJobSourceId, materialSourcesList } = this.props;
    return (
      <Select
        style={{ width: 260, marginBottom: 16 }}
        allowClear
        value={selectedJobSourceId}
        onChange={async (e) => {
          await this.props.setState({ selectedJobSourceId: e });
          this.props.fetchItems('jobs');
        }}
      >
        {materialSourcesList.map((w) => { return <Select.Option value={w.value}>{w?.name}</Select.Option>; })}
      </Select>
    );
  }

  render = () => {
    const {
      materials,
      materialsTotal,
      materialsPagination,
      materialSources,
      materialSourcesTotal,
      materialSourcesPagination,
      jobs,
      jobsTotal,
      jobsPagination,
      materialKeywords,
      materialSourceKeywords,
    } = this.props;
    const {
      activeKey,
      selectedMaterialSources,
      requestParams,
      articleOpen,
      materialOpen,
      materialSourceOpen,
      manualAddOpen,
      searchOpen,
    } = this.state;

    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <Tabs activeKey={activeKey} onChange={this.onChangeTab}>
          {
            !this.props.isWework && (
              <Tabs.TabPane tab="源分组" key="group">
                <GroupTabPane
                  {...this.props}
                  requestParams={requestParams}
                  onChangeActiveKey={(e) => { this.setState({ activeKey: e }); }}
                />
              </Tabs.TabPane>
            )
          }
          <Tabs.TabPane tab="文稿源" key="materialSources">
            <FilterBar
              canAdd
              shouldShowSearchInput
              searchKeyWords={materialSourceKeywords}
              onChange={(value) => { this.props.setState({ materialSourceKeywords: value }); }}
              onAdd={() => {
                this.props.setState({ materialSource: {} });
                this.setState({ materialSourceOpen: true });
              }}
              onSearch={() => {
                this.props.fetchItems('materialSources', requestParams);
              }}
              placeholder="请输入作者"
              renderSelects={this.renderMaterialSourceSelects}
            />
            <PaginationTable
              totalDataCount={materialSourcesTotal}
              dataSource={materialSources}
              pagination={materialSourcesPagination}
              columns={this.renderMaterialSourcesColumns()}
              rowKey="id"
              rowSelection={{
                type: 'checkbox',
                selectedMaterialSources,
                onChange: (keys) => { return this.setState({ selectedMaterialSources: keys }); },
              }}
              onPaginationChange={(e) => {
                return this.props.fetchItems('materialSources', { ...requestParams, ...e });
              }}
            />
          </Tabs.TabPane>
          {
            !1 &&
            <Tabs.TabPane tab="抓取记录" key="jobs">
              <PaginationTable
                totalDataCount={jobsTotal}
                dataSource={jobs}
                pagination={jobsPagination}
                columns={this.renderJobColumns()}
                onPaginationChange={(e) => { return this.props.fetchItems('jobs', e); }}
              />
            </Tabs.TabPane>
          }
          <Tabs.TabPane tab="文稿" key="materials">
            <FilterBar
              shouldShowSearchInput
              searchKeyWords={materialKeywords}
              onChange={(value) => { this.props.setState({ materialKeywords: value }); }}
              onSearch={() => { this.props.fetchItems('materials'); }}
              placeholder="请输入标题"
              renderSelects={this.renderMaterialSelects}
              renderButtons={this.renderMaterialButtons}
            />
            <PaginationTable
              totalDataCount={materialsTotal}
              dataSource={materials}
              pagination={materialsPagination}
              columns={this.renderMaterialColumns()}
              scroll={this.getMaterialTableScroll()}
              onPaginationChange={(e) => { return this.props.fetchItems('materials', e); }}
              onTableChange={({ current, pageSize }, filters, { order, field }, { action }) => {
                if (action === 'sort') {
                  const sortParams = {
                    pageIndex: current,
                    pageSize,
                    orderBy: order ? `${_.snakeCase(field)} ${_.trimEnd(order, 'end')}` : 'pub_date desc',
                  };
                  return this.props.fetchItems('materials', sortParams);
                }
                return null;
              }}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="话题" key="liveMaterials">
            <LiveMaterialTabPane
              {...this.props}
              onChangeActiveKey={(e) => { this.setState({ activeKey: e }); }}
            />
          </Tabs.TabPane>
          {/* 话题文稿 */}
          <Tabs.TabPane tab="话题文稿" key="topicMaterials">
            <TopicMaterialTabPane
              {...this.props}
              onChangeActiveKey={(e) => { this.setState({ activeKey: e }); }}
            />
          </Tabs.TabPane>

          {
            !1 && (
              <Tabs.TabPane tab="话题" key="topic">
                <TopicTabPane
                  kolsMap={this.props.kolsMap}
                  materialSourcesMap={this.props.materialSourcesMap}
                  materialSourceGroups={this.props.materialSourceGroups}
                  materialSourcesList={this.props.materialSourcesList}
                  onSearch={this.props.searchArticleByMeta}
                />
              </Tabs.TabPane>
            )
          }
          <Tabs.TabPane tab="研报" key="researchReport">
            <ResearchReportTabPane
              {...this.props}
              onChangeActiveKey={(e) => { this.setState({ activeKey: e }); }}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="复盘" key="analysis">
            <AnalysisTabPane
              {...this.props}
              onChangeActiveKey={(e) => { this.setState({ activeKey: e }); }}
            />
          </Tabs.TabPane>
        </Tabs>

        {
          materialSourceOpen &&
          <MaterialSourceDrawer
            open={materialSourceOpen}
            materialSource={this.props.materialSource}
            materialSourceGroups={this.props.materialSourceGroups}
            kols={this.props.kols}
            onClose={() => { return this.setState({ materialSourceOpen: false }); }}
            onSubmit={(e) => { return this.onCreateMaterialSource(e); }}
            onChangeValue={this.onChangeValue}
            onAnalyzeUrl={this.onAnalyzeUrl}
            handleNumber={this.handleNumber}
          />
        }
        {materialOpen &&
          <MaterialDrawer
            workflowsMap={this.props.workflowsMap}
            material={this.props.material}
            article={this.props.article}
            open={materialOpen}
            isMaterialEdit={this.state.isMaterialEdit}
            isRerunning={this.state.isRerunning}
            createItem={this.props.createItem}
            updateItem={this.props.updateItem}
            rerunOptimizeJob={this.props.rerunOptimizeJob}
            kolsMap={this.props.kolsMap}
            materialSourcesMap={this.props.materialSourcesMap}
            onSearch={this.props.searchArticleByMeta}
            clearTimerByArticleId={this.clearTimerByArticleId}
            initArticleTimer={this.initArticleTimer}
            onChange={this.onChangeValue}
            onClose={() => { return this.setState({ materialOpen: false }); }}
          />
        }
        {
          articleOpen &&
          <ArticleDrawer
            isArticleEdit
            open={articleOpen}
            article={this.props.article}
            material={this.props.material}
            kols={this.props.kols}
            kolsMap={this.props.kolsMap}
            isRerunning={this.state.isRerunning}
            rerunOptimizeJob={this.props.rerunOptimizeJob}
            clearTimerByArticleId={this.clearTimerByArticleId}
            initArticleTimer={this.initArticleTimer}
            manualSyncMaterial={this.props.manualSyncMaterial}
            onChange={this.onChangeValue}
            updateItem={this.props.updateItem}
            onClose={() => { return this.setState({ articleOpen: false }); }}
          />
        }
        {
          searchOpen &&
          <InstantSearchDrawer
            open={searchOpen}
            createItem={this.props.createItem}
            createFetchJob={this.props.createFetchJob}
            materialSourcesList={this.props.materialSourcesList}
            onClose={() => { return this.setState({ searchOpen: false }); }}
          />
        }
        {
          manualAddOpen &&
          <ManualAddDrawer
            open={manualAddOpen}
            materialSourcesList={this.props.materialSourcesList}
            onClose={() => { return this.setState({ manualAddOpen: false }); }}
            onSubmit={this.props.manualSyncMaterial}
          />
        }
      </div>
    );
  }
}

export {
  reducer,
};
