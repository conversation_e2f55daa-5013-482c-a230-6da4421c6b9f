/* eslint-disable max-len */
import { ChatBot, Materials } from '~/engine';
import { Platform, StringExtension } from '~/plugins';
import _ from 'lodash';
import moment from 'moment';

const SET_STATE = 'MARKET_ARTICLE/SET_STATE';
const CLEAR_STATE = 'MARKET_ARTICLE/CLEAR_STATE';
export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchChatbotWorkflows = () => {
  return async (dispatch) => {
    let { items } = await ChatBot.fetchChatbotWorkflows({ groupId: 12 });

    if (Platform.isProd()) {
      items = await ChatBot.fetchSharedWorkflows({});
      items = _.values(items);
    }

    const workflowsMap = {};
    items.forEach((item) => { workflowsMap[item.uuid] = item.name; });
    dispatch(setState({ workflowsMap }));
  };
};

export const createFetchJob = (params) => {
  return async () => {
    return Materials.createFetchJob(params);
  };
};

export const fetchKols = () => {
  return async (dispatch) => {
    const { items } = await Materials.fetchKols();
    const kolsMap = {};
    const kols = items.map((item) => {
      kolsMap[item.id] = item.name;
      return {
        value: item.id,
        name: item.name,
      };
    });
    dispatch(setState({ kols, kolsMap }));
  };
};

export const fetchMaterialSources = (params = {}, isReturn = false) => {
  return async (dispatch) => {
    const { items } = await Materials.fetchmaterialSources({
      ...params,
      'pagination.pageIndex': 1,
      'pagination.pageSize': 1000,
    });
    const materialSourcesMap = {};
    const materialSourcesList = items.map((item) => {
      materialSourcesMap[item.id] = item.author;
      return { value: item.id, name: item.author, url: item.sourceUrl, platform: item.platform, usageId: item.usageId };
    });

    if (isReturn) {
      return materialSourcesList;
    }

    dispatch(setState({ materialSourcesList, materialSourcesMap }));
    return [];
  };
};

export const fetchJobs = () => {
  return async (dispatch) => {
    const { items } = await Materials.fetchjobs({
      'pagination.pageIndex': 1,
      'pagination.pageSize': 1000,
    });
    const jobsMap = {};
    const jobsList = items.map((item) => {
      const title = `${item.params ? item.params.platform : ''}-${item.createdAt ? moment(item.createdAt).format('YYYY-MM-DD HH:mm:ss') : ''}`;
      jobsMap[item.id] = title;
      return {
        value: item.id,
        name: title,
      };
    });
    dispatch(setState({ jobsList, jobsMap }));
  };
};

export const fetchItems = (type, params = {}) => {
  if (!type || type === 'topicMaterials') return () => { };
  return async (dispatch, getState) => {
    const data = getState().marketArticle;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || data[`${type}Pagination`].pageIndex,
      'pagination.pageSize': params.pageSize || data[`${type}Pagination`].pageSize,
      'pagination.orderBy': params.orderBy || data[`${type}Pagination`].orderBy,
    };

    if (type === 'materialSources') {
      searchParams.usage = params?.usage || data.usage || 'character';
      searchParams.usageAssociationId = params?.usageAssociationId || 0;
      searchParams.author = data.materialSourceKeywords || '';
      searchParams.platform = data.materialSourcePlatform || '';
      searchParams.sourceGroupId = data.sourceGroupId || undefined;
    } else if (type === 'materials') {
      searchParams.source_id = data.selectedMaterialSourceId;
      searchParams.title = data.materialKeywords || undefined;
      searchParams.fetch_job_id = data.selectedJobId;
      searchParams.fetch_status = data.fetchStatus;
      searchParams.optimize_status = data.optimizeStatus;
      searchParams.character_id = data.characterId;
      // searchParams.passed = data.passed === 'null' ? null : data.passed;
      searchParams.order_by = searchParams['pagination.orderBy'];
      searchParams.sourceGroupId = data.sourceGroupId || undefined;
      searchParams.startAt = data.startAt || undefined;
      searchParams.endAt = data.endAt || undefined;
      // searchParams.optimize_job_id = data.selectedJobId;
    } else if (type === 'articles') {
      searchParams.character_id = data.selectedArticleKolId;
      searchParams.title = data.articleKeywords;
    } else if (type === 'jobs') {
      searchParams.source_id = data.selectedJobSourceId;
    }

    const { items, total } = await Materials[`fetch${type}`](searchParams);
    const result = {
      [`${type}Total`]: total,
      [type]: items.map((item) => {
        return {
          ...item,
          createdAt: item.createdAt ? moment(item.createdAt).format('YYYY-MM-DD HH:mm:ss') : '',
          pubDate: item.pubDate ? moment(item.pubDate).format('YYYY-MM-DD HH:mm:ss') : '',
        };
      }),
      [`${type}Pagination`]: {
        pageIndex: searchParams['pagination.pageIndex'],
        pageSize: searchParams['pagination.pageSize'],
        orderBy: searchParams['pagination.orderBy'],
      },
    };

    if (type === 'materialSourceGroups') {
      result.sourceGroupId = items[0]?.id;
    }

    dispatch(setState(result));
    if (type === 'materialSourceGroups') {
      dispatch(fetchItems('materials'));
    }
  };
};

export const retryJob = (id) => {
  return async (dispatch) => {
    await Materials.retryJob(id);
    dispatch(fetchItems('jobs'));
  };
};

export const deleteItem = (type, id) => {
  return async (dispatch) => {
    await Materials[`delete${type}`](id);
    dispatch(fetchItems(`${type}s`));
  };
};

export const deleteItems = (type, params) => {
  return async (dispatch) => {
    if (type === 'materialSources') {
      await Materials.batchDeletematerialSources(params);
    } else {
      await Promise.all(params.ids.map((id) => {
        return Materials[`delete${type.slice(0, -1)}`](id);
      }));
    }
    dispatch(fetchItems(type));
  };
};

export const updateItem = (type, params) => {
  return async (dispatch) => {
    await Materials[`put${type}`](params);
    const { usage, usageAssociationId } = params;
    dispatch(fetchItems(`${type}s`, { usage, usageAssociationId }));
  };
};

export const createItem = (type, params) => {
  return async (dispatch) => {
    const data = await Materials[`create${type}`](params);
    const { usage, usageAssociationId } = params;
    dispatch(fetchItems(`${type}s`, { pageIndex: 1, orderBy: 'created_at asc', usage, usageAssociationId }));
    return data;
  };
};

export const getItem = (type, id) => {
  return async (dispatch) => {
    let data = await Materials[`get${type}`](id);
    if (type === 'materialSource') {
      data = {
        ...data,
        ...data.setting.optimizeSetting,
        ...data.setting.fetchSetting,
        ...data.setting.reviewSetting,
        ...data.setting.partitionSetting,
        ...data.setting.workflowSetting,
      };

      if (data.workflowConfig) {
        data = {
          ...data,
          ...StringExtension.camelToSnakeObj(data.workflowConfig, {}),
        };
      }
      const { groupIds } = await Materials.getMaterialSourceGroups(data.id);
      data.groupIds = groupIds;
    }
    data.createdAt = data.createdAt ? moment(data.createdAt).format('YYYY-MM-DD HH:mm:ss') : '';
    dispatch(setState({ [type]: data }));
  };
};

export const getArticleWithId = (id) => {
  return async (dispatch) => {
    const data = await Materials.getArticleWithId(id);
    dispatch(setState({ article: data || {} }));
  };
};

export const rerunOptimizeJob = (id) => {
  return async () => {
    return Materials.rerunOptimizeJob(id);
  };
};

export const getMaterialOptimizeJobs = (id) => {
  return async () => {
    return Materials.getMaterialOptimizeJobs(id);
  };
};

export const putMaterialSourceGroups = (params) => {
  return async () => {
    return Materials.putMaterialSourceGroups(params);
  };
};

export const searchArticleByMeta = (params) => {
  return async () => {
    return Materials.searchArticleByMeta(params);
  };
};

export const manualSyncMaterialSource = (sourceId) => {
  return async () => {
    return Materials.manualSyncMaterialSource(sourceId);
  };
};

export const manualSyncMaterial = (params) => {
  return async () => {
    return Materials.manualSyncMaterial(params);
  };
};

export const fetchMaterialTopics = (params) => {
  return async () => {
    const data = await Materials.fetchMaterialTopics(params);
    return data;
  };
};

export const genMaterialTopics = (materialId) => {
  return async () => {
    const data = await Materials.genMaterialTopics(materialId);
    return data;
  };
};

export const fetchDistinctMaterialTopics = (params = {}) => {
  return async (dispatch, getState) => {
    const { topicsPagination } = getState().marketArticle;
    const searchParams = {
      category: params.category || undefined,
      'pagination.pageIndex': params.pageIndex || topicsPagination.pageIndex,
      'pagination.pageSize': params.pageSize || topicsPagination.pageSize,
      'pagination.orderBy': params.orderBy || topicsPagination.orderBy,
    };

    const { items, total } = await Materials.fetchDistinctMaterialTopics(searchParams);
    dispatch(setState({
      topics: items,
      topicsTotal: total,
      topicsPagination: {
        pageIndex: searchParams['pagination.pageIndex'],
        pageSize: searchParams['pagination.pageSize'],
        orderBy: searchParams['pagination.orderBy'],
      },
    }));
  };
};

export const fetchTopicMaterials = (params = {}) => {
  return async (dispatch, getState) => {
    const { topicMaterialsPagination } = getState().marketArticle;
    const searchParams = {
      topic: params.topic || undefined,
      'pagination.pageIndex': params.pageIndex || topicMaterialsPagination.pageIndex,
      'pagination.pageSize': params.pageSize || topicMaterialsPagination.pageSize,
      'pagination.orderBy': params.orderBy || topicMaterialsPagination.orderBy,
    };

    const { items, total } = await Materials.fetchTopicMaterials(searchParams);
    dispatch(setState({
      topicMaterials: items,
      topicMaterialsTotal: total,
      topicMaterialsPagination: {
        pageIndex: searchParams['pagination.pageIndex'],
        pageSize: searchParams['pagination.pageSize'],
        orderBy: searchParams['pagination.orderBy'],
      },
    }));
  };
};


const _getInitState = () => {
  const result = {};
  ['materialSourceGroup', 'materialSource', 'material', 'article', 'job', 'topic', 'topicMaterial'].forEach((key) => {
    result[`${key}sTotal`] = 0;
    result[`${key}sPagination`] = {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'created_at desc',
    };
    result[`${key}s`] = [];
    result[`${key}Keywords`] = '';
    result[key] = {};
  });

  return {
    ...result,
    // fetchStatus: 'done',
    // optimizeStatus: 'done',
    passed: 'true',
    materialSourcePlatform: undefined,
    selectedMaterialSourceId: undefined,
    selectedJobSourceId: undefined,
    selectedArticleKolId: undefined,
    selectedJobId: undefined,
    materialSourcesList: [],
    materialSourcesMap: {},
    kols: [],
    kolsMap: {},
    jobsList: [],
    jobsMap: {},
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
