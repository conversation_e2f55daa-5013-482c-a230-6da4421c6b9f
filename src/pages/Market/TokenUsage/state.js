import { Market } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'MARKET_PARTNER/SET_STATE';
const CLEAR_STATE = 'MARKET_PARTNER/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchTokenUsagesMonthly = () => {
  return async (dispatch) => {
    const data = await Market.fetchTokenUsagesMonthly();
    dispatch(setState({ tokenUsagesMonthly: data }));
  };
};

export const fetchTokenUsages = () => {
  return async (dispatch) => {
    const data = await Market.fetchTokenUsages();
    dispatch(setState({ tokenUsages: _.values(data) }));
  };
};

export const fetchTokenUsageLogs = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().marketTokenUsage;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };
    const { items, total } = await Market.fetchTokenUsageLogs(searchParams);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const fetchImageCost = () => {
  return async () => {
    const { cost } = await Market.fetchImageCost();
    return cost;
  };
};
export const fetchImageUsageLogs = (params = {}) => {
  return async (dispatch, getState) => {
    const { imgPage } = getState().marketTokenUsage;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || imgPage.pageIndex,
      'pagination.pageSize': params.pageSize || imgPage.pageSize,
      'pagination.orderBy': params.orderBy || imgPage.orderBy,
    };
    const { items, total } = await Market.fetchImageUsageLogs(searchParams);
    dispatch(
      setState({
        imgTotal: total,
        imgLogs: items,
        imgPage: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

const _getInitState = () => {
  return {
    total: 0,
    imgTotal: 0,
    list: [],
    imgLogs: [],
    tokenUsages: [],
    tokenUsagesMonthly: {},
    pagination: {
      pageIndex: 1,
      pageSize: 10,
      orderBy: 'createdAt asc',
    },
    imgPage: {
      pageIndex: 1,
      pageSize: 10,
      orderBy: 'createdAt asc',
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
