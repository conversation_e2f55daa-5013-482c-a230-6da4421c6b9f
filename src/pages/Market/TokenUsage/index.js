import { PaginationTable } from '~/components';
import { Button, Descriptions, Divider, Image, Table, Tabs, Typography } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

const IMG_ENUM = {
  text2image: '文生图',
  image2video: '图转视频',
};
@connect(
  (state) => {
    return state.marketTokenUsage;
  },
  actions,
)
export default class MarketTokenUsage extends Component {
  static propTypes = {
    total: PropTypes.number,
    imgTotal: PropTypes.number,
    list: PropTypes.array,
    tokenUsages: PropTypes.array,
    imgLogs: PropTypes.array,
    imgPage: PropTypes.object,
    pagination: PropTypes.object,
    tokenUsagesMonthly: PropTypes.object,
    fetchTokenUsagesMonthly: PropTypes.func.isRequired,
    fetchTokenUsages: PropTypes.func.isRequired,
    fetchTokenUsageLogs: PropTypes.func.isRequired,
    fetchImageUsageLogs: PropTypes.func.isRequired,
    fetchImageCost: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    imgCost: 0.0,
    llmCollapse: true,
  }

  componentDidMount = async () => {
    this.props.fetchTokenUsages();
    this.props.fetchImageUsageLogs();
    this.props.fetchTokenUsageLogs();
    this.props.fetchTokenUsagesMonthly();
    const imgCost = await this.props.fetchImageCost();
    this.setState({ imgCost });
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  renderColumns = () => {
    return [
      {
        title: '操作',
        dataIndex: 'flowUuid',
        key: 'flowUuid',
        align: 'center',
        render: (flowUuid) => {
          return (flowUuid || '').indexOf('session') >= 0 ? 'Playground运行'
            : <a onClick={() => { this.$push(`/workflow/${flowUuid}`); }}>执行工作流</a>;
        },
      },
      {
        title: '时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (createdAt) => {
          return moment(createdAt).format('YYYY-MM-DD HH:ss');
        },
      },
      {
        title: '费用',
        dataIndex: 'tokenUsage',
        key: 'tokenUsage',
        align: 'center',
        render: (data) => { return (<>${_.ceil(data?.totalCost || 0, 2)}</>); },
      },
      Table.EXPAND_COLUMN,
    ];
  }

  renderLLMTable = (tokenUsages) => {
    const { llmCollapse } = this.state;
    const dataSource = llmCollapse ? _.head(_.chunk(tokenUsages, 5)) : tokenUsages;
    return (
      <>
        <Table
          size="small"
          dataSource={dataSource}
          pagination={false}
          columns={[
            {
              title: '模型',
              dataIndex: 'modelName',
              key: 'modelName',
              width: '300',
              render: (x) => { return <Typography.Title level={5}>{x}</Typography.Title>; },
            },
            { title: '输入Token', dataIndex: 'promptTokens', key: 'promptTokens', align: 'center' },
            { title: '输出Token', dataIndex: 'completionTokens', key: 'completionTokens', align: 'center' },
            {
              title: '总费用',
              dataIndex: 'totalCost',
              key: 'totalCost',
              align: 'center',
              render: (txt) => { return _.ceil(txt, 2); },
            },
          ]}
        />
        <div style={{ textAlign: 'center', marginTop: 10, display: tokenUsages?.length > 5 ? 'block' : 'none' }}>
          <Button onClick={() => { return this.setState({ llmCollapse: !llmCollapse }); }} type="link">
            {llmCollapse ? '展开更多' : '折叠'}
          </Button>
        </div>
      </>
    );
  }

  renderLLM = (totalCost, tokenUsages, title = '模型总消耗') => {
    return (
      <>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography.Title level={3}>{title}</Typography.Title>
          <Typography.Title level={3} style={{ marginTop: 0 }}>
            总计:${_.ceil(_.sum(totalCost), 2)}
          </Typography.Title>
        </div>
        {this.renderLLMTable(_.orderBy(tokenUsages, ['totalCost'], ['desc']))}
        <Divider />
      </>
    );
  }

  render = () => {
    const { tokenUsages, total, list, pagination, imgTotal, imgLogs, imgPage, tokenUsagesMonthly } = this.props;
    const totalCost = _.map(tokenUsages, 'totalCost').map((x) => { return _.ceil(x, 2); });
    const { currentMonth, lastMonth } = tokenUsagesMonthly;

    return (
      <div style={{ padding: 30, background: '#fff', overflow: 'auto', maxHeight: 'calc(100vh - 100px)' }}>
        <Tabs tabPosition="left">
          <Tabs.TabPane tab="语言模型" key="llm">
            <Tabs >
              <Tabs.TabPane tab="月度消耗" key="monthly">
                {this.renderLLM([currentMonth?.totalCost], currentMonth?.details, '本月消耗')}
                {this.renderLLM([lastMonth?.totalCost], lastMonth?.details, '上月消耗')}
              </Tabs.TabPane>
              <Tabs.TabPane tab="总消耗" key="total">
                {this.renderLLM(totalCost, tokenUsages)}
                <div style={{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center', marginBottom: 10 }}>
                  <Typography.Title level={3} style={{ marginBottom: 0, marginRight: 10 }}>详情</Typography.Title>
                  <Typography.Text type="secondary">
                    明细账目仅展示从2023年8月17日开始的账目，8月17日前费用仅包含在总费用中，不展示明细.
                  </Typography.Text>
                </div>
                <PaginationTable
                  rowKey="id"
                  totalDataCount={total}
                  dataSource={list}
                  pagination={pagination}
                  columns={this.renderColumns()}
                  expandable={{
                    rowExpandable: (record) => { return !!record.tokenUsage?.totalCost; },
                    expandedRowRender: (x) => {
                      return (
                        <div style={{ width: '40vw', float: 'right', background: '#fff' }}>
                          <Descriptions bordered column={4} size="small">
                            <Descriptions.Item label="输入Token">{x.tokenUsage?.promptTokens}</Descriptions.Item>
                            <Descriptions.Item label="输出Token">{x.tokenUsage?.completionTokens}</Descriptions.Item>
                            <Descriptions.Item label="总Token">{x.tokenUsage?.totalTokens}</Descriptions.Item>
                            <Descriptions.Item label="总费用">
                              <strong>${_.ceil(x.tokenUsage?.totalCost, 2)}</strong>
                            </Descriptions.Item>
                          </Descriptions>
                        </div>
                      );
                    },
                  }}
                  onPaginationChange={(e) => { return this.props.fetchTokenUsageLogs(e); }}
                />
              </Tabs.TabPane>
            </Tabs>
          </Tabs.TabPane>
          <Tabs.TabPane tab="文生图" key="img">
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography.Title level={3}>模型总消耗</Typography.Title>
              <Typography.Title level={3} style={{ marginTop: 0 }}>
                总计:¥{_.ceil(this.state.imgCost, 2)}
              </Typography.Title>
            </div>
            <PaginationTable
              rowKey="id"
              totalDataCount={imgTotal}
              dataSource={imgLogs}
              pagination={imgPage}
              columns={[
                {
                  title: '模型',
                  dataIndex: 'modelName',
                  key: 'modelName',
                  width: '300',
                  render: (x) => { return <Typography.Title level={5}>{x}</Typography.Title>; },
                },
                {
                  title: '类型',
                  dataIndex: 'generateType',
                  key: 'generateType',
                  align: 'center',
                  render: (x) => { return IMG_ENUM[x]; },
                },
                {
                  title: 'prompt',
                  dataIndex: 'params',
                  key: 'params',
                  align: 'center',
                  render: (x) => { return x?.prompt || <Image src={x.imageUrl} width={30} height={30} />; },
                },
                { title: '费用', dataIndex: 'cost', key: 'cost', align: 'center', render: (x) => { return `¥ ${x}`; } },
                {
                  title: '预览',
                  dataIndex: 'preview',
                  key: 'preview',
                  align: 'center',
                  render: (x, row) => {
                    if (row.generateType === 'image2video') {
                      return <a target="_blank" href={_.head(row.images)} rel="noreferrer">预览</a>;
                    }
                    return (
                      <>
                        <a onClick={() => { return document.getElementById(`preview-${row.id}`).click(); }}>预览</a>
                        <Image.PreviewGroup>
                          {
                            row.images.map((i, idx) => {
                              return (
                                <Image
                                  id={idx ? undefined : `preview-${row.id}`}
                                  src={i}
                                  height={1}
                                  width={1}
                                />
                              );
                            })
                          }
                        </Image.PreviewGroup>
                      </>
                    );
                  },
                },
              ]}
              onPaginationChange={(e) => { return this.props.fetchImageUsageLogs(e); }}
            />
          </Tabs.TabPane>
        </Tabs>
      </div>
    );
  };
}

export {
  reducer,
};
