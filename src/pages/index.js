import { IeOutlined } from '@ant-design/icons';
import { Loading, Toast } from '~/components';
import loading from '~/components/Loading/state';
import Configs from '~/consts';
import Engine, { Sessions } from '~/engine';
import { Platform, Storage, StringExtension } from '~/plugins';
import * as ReduxEnhance from '~/plugins/ReduxEnhance';
import res from '~/resources';
import { ConfigProvider, Typography } from 'antd';
import zhCN from 'antd/lib/locale-provider/zh_CN';
import { ConnectedRouter, connectRouter, routerActions, routerMiddleware } from 'connected-react-router';
import { createBrowserHistory } from 'history';
import qhistory from 'qhistory';
import { parse, stringify } from 'qs';
import React, { Component } from 'react';
import { Provider } from 'react-redux';
import { Redirect, Route, Switch } from 'react-router-dom';
import { applyMiddleware, combineReducers, compose, createStore } from 'redux';
import { createLogger } from 'redux-logger';
import { i18n } from 'redux-pagan';
import thunk from 'redux-thunk';

import CommonLayout, { reducer as commonLayout } from './CommonLayout';
import Login, { reducer as accountLogin } from './CommonLogin/Login';
import TokenLogin, { reducer as tokenLogin } from './CommonLogin/TokenLogin';
import WxLogin, { reducer as wxLogin } from './CommonLogin/WxLogin';
import Home from './Home';
import MarkeyApikey, { reducer as marketApikey } from './Market/ApiKey';
import MarketArticle, { reducer as marketArticle } from './Market/Article';
import MarketCourseMaterials, { reducer as marketCourseMaterials } from './Market/CourseMaterials';
import MarketCourseMaterialDetail, { reducer as marketCourseMaterialDetail } from './Market/CourseMaterials/Detail';
import MarketDouyinData, { reducer as marketDouyinData } from './Market/DouyinData';
import MarketDouyinPrivate, { reducer as marketDouyinPrivate } from './Market/DouyinPrivate';
import MarketInvitation, { reducer as marketInvitation } from './Market/Invitation';
import MarketLiveKnowledge, { reducer as marketLiveKnowledge } from './Market/LiveKnowledge';
import MarketLiveKnowledgeDetail, { reducer as marketLiveKnowledgeDetail } from './Market/LiveKnowledge/Detail';
import MarketLiveScript, { reducer as marketLiveScript } from './Market/LiveScript';
import MarketLiveScriptDetail, { reducer as marketLiveScriptDetail } from './Market/LiveScript/Detail';
import MarketLiveScriptPreview, { reducer as marketLiveScriptPreview } from './Market/LiveScript/Preview';
import MCNWriter from './Market/MCNWriter';
import MarketMeMe, { reducer as marketMeMe } from './Market/MeMe';
import MarketMeMeDetail, { reducer as marketMeMeDetail } from './Market/MeMe/Detail';
import MarketNovel, { reducer as marketNovel } from './Market/Novel';
import MarketNovelist, { reducer as marketNovelist } from './Market/Novelist';
import MarketPartner, { reducer as marketPartner } from './Market/Partners';
import MarketPartnerMember, { reducer as marketPartnerMember } from './Market/Partners/Members';
import MarketPodcastChat, { reducer as marketPodcastChat } from './Market/PodcastChat';
import MarketGptImage, { reducer as marketGptImage } from './Market/GptImage';
import MarketPublish, { reducer as marketPublish } from './Market/Publish';
import MarketPublishMaterial, { reducer as marketPublishMaterial } from './Market/Publish/Material';
import MarketScriptEvaluation from './Market/ScriptEvaluation';
import MarketScriptEvaluationV2 from './Market/ScriptEvaluationV2';
import MarketSKUFilter from './Market/SKUFilter';
import MarketTextbook, { reducer as marketTextbook } from './Market/Textbook';
import MarketTokenUsage, { reducer as marketTokenUsage } from './Market/TokenUsage';
import MarketVoiceClone, { reducer as marketVoiceClone } from './Market/VoiceClone';
import MarketVoiceCloneDetail, { reducer as marketVoiceCloneDetail } from './Market/VoiceClone/Detail';
import MarketVoiceCloneTopicDetail, { reducer as marketVoiceCloneTopicDetail } from './Market/VoiceClone/TopicDetail';
import MarketVoiceChanger, { reducer as marketVoiceChanger } from './Market/VoiceClone/VoiceChanger';
import MarketVoiceTtsV2Detail from './Market/VoiceTtsV2';
import MarketVoiceTtsV2, { reducer as marketVoiceTtsV2 } from './Market/VoiceTtsV2/list';
import MarketWework, { reducer as marketWework } from './Market/Wework';
import MarketWeworkKF, { reducer as marketWeworkKF } from './Market/WeworkKF';
import MarketWhatsapp, { reducer as marketWhatsapp } from './Market/Whatsapp';
import MarketZhiBo, { reducer as marketZhiBo } from './Market/ZhiBo';
import Materials, { reducer as materials } from './Material';
import MaterialDetail, { reducer as materialDetail } from './Material/Detail';
import CharacterTemplate, { reducer as characterTemplate } from './Playground/CharacterTemplate';
import Chat, { reducer as chatBotChat } from './Playground/Chat';
import Feedback, { reducer as chatBotFeedback } from './Playground/Feedback';
import Group, { reducer as chatBotGroup } from './Playground/Groups';
import ChatKnowledge, { reducer as chatKnowledge } from './Playground/Knowledge';
import ChatKnowledgeFileList, { reducer as chatKnowledgeFileList } from './Playground/Knowledge/FileList';
import ChatKnowledgeList, { reducer as chatKnowledgeList } from './Playground/Knowledge/List';
import WorkflowDetail, { reducer as workflowDetail } from './Playground/Workflow';
import WorkflowFunctions, { reducer as workflowFunctions } from './Playground/Workflow/Functions';
import WorkflowJobs, { reducer as chatBotWorkflowJobs } from './Playground/Workflow/Jobs';
import Workflow, { reducer as chatBotWorkflow } from './Playground/Workflow/List';
import Transforms, { reducer as transforms } from './Playground/Workflow/Transforms';
import Prompts, { reducer as prompts } from './Prompts';
import WorkflowV2, { reducer as workflowv2 } from './Workflow';
import Workflows, { reducer as workflows } from './Workflow/List';
import TestNode from './WorkflowNodes/test';

const createHistory = createBrowserHistory();
const history = qhistory(createHistory, stringify, parse);
const reducer = combineReducers({
  i18n,
  loading,
  router: connectRouter(history),

  commonLayout,
  accountLogin,
  wxLogin,
  tokenLogin,
  materials,
  materialDetail,

  prompts,

  chatBotChat,
  chatBotGroup,
  chatBotFeedback,
  chatKnowledge,
  chatKnowledgeList,
  chatKnowledgeFileList,
  chatBotWorkflow,
  workflowDetail,
  workflowFunctions,
  chatBotWorkflowJobs,
  characterTemplate,
  transforms,
  workflows,
  workflowv2,

  marketMeMe,
  marketMeMeDetail,
  marketPublish,
  marketPublishMaterial,
  marketInvitation,
  marketPartner,
  marketPartnerMember,
  marketVoiceTtsV2,
  marketWework,
  marketTokenUsage,
  marketApikey,
  marketArticle,
  marketNovel,
  marketNovelist,
  marketCourseMaterials,
  marketCourseMaterialDetail,
  marketVoiceClone,
  marketVoiceCloneDetail,
  marketVoiceCloneTopicDetail,
  marketWeworkKF,
  marketWhatsapp,
  marketDouyinData,
  marketDouyinPrivate,
  marketZhiBo,
  marketTextbook,
  marketPodcastChat,
  marketGptImage,
  marketVoiceChanger,
  marketLiveScript,
  marketLiveScriptDetail,
  marketLiveScriptPreview,

  marketLiveKnowledge,
  marketLiveKnowledgeDetail,
});

export const store = createStore(
  connectRouter(history)(reducer),
  compose(
    applyMiddleware(thunk),
    applyMiddleware(routerMiddleware(history)),
    applyMiddleware(createLogger({
      predicate: () => {
        if (process.env.ENV === 'production') return false;

        return StringExtension.formatBoolValue(localStorage.debug);
      },
    })),
  ),
);
ReduxEnhance.init(store, res);

const SwitchX = (props) => {
  const paths = [];
  props.children.forEach((r) => { // eslint-disable-line
    if (r.props.path && !/\/:/.test(r.props.path)) {
      paths.push(r.props.path);
    }
  });

  return <Switch {...props} />;
};

export default class Pages extends Component {
  state = { isReady: false }

  componentDidMount = async () => { // eslint-disable-next-line
    const resp = await fetch(`https://video-clip.oss-cn-shanghai.aliyuncs.com/faas/ai_config.json?v=${new Date().getTime()}`);
    const envData = await resp.json();
    const endpoint = Platform.isProd() ? envData.prodEnv : envData.stgEnv;

    const searchParams = new URLSearchParams(window.location.search);
    const isClient = !!(searchParams.get('isClient'));

    try {
      await Engine.init({
        storage: new Storage({ scope: `chat-bzy-${process.env.ENV}` }),
        apiEndpoint: `${process.env.BACKEND_PROTOCOL}://${endpoint}`,
        wssEndpoint: `wss://${endpoint}`,
        onInitSuccess: this.onInitSuccess,
        showLoading: Loading.show,
        showToast: Toast.show,
        hideLoading: Loading.hide,
        onLogout: this.onLogout,
        i18n: this.$i18n,
      });
      this.setState({ isReady: true }); // eslint-disable-line
      if (isClient) {
        Engine.setItem('isClient', isClient);
      }
    } catch (error) {
      console.error(error); // eslint-disable-line
    }
  }

  onInitSuccess = () => {
  }

  onLogout = () => {
    let loginPath = Configs.ROUTE.WX_LOGIN;
    if (window.location.search) {
      const query = parse(window.location.search, { ignoreQueryPrefix: true });
      if (query.channel) {
        loginPath = `${loginPath}?channel=${query.channel}`;
      }
    }

    store.dispatch(routerActions.replace(loginPath));
  }

  renderRoutes = () => {
    return (
      <SwitchX>
        <Route exact path="/home" component={Home} />
        <Route exact path="/playground" component={Chat} />
        <Route exact path="/group" component={Group} />
        <Route exact path="/feedback" component={Feedback} />
        <Route exact path="/knowledge" component={ChatKnowledge} />
        <Route exact path="/knowledge/:id" component={ChatKnowledgeList} />
        <Route exact path="/knowledge/:id/files" component={ChatKnowledgeFileList} />
        <Route exact path="/workflow" component={Workflow} />
        <Route exact path="/workflow/functions" component={WorkflowFunctions} />
        <Route exact path="/workflow/transforms" component={Transforms} />
        <Route exact path="/workflow/add" component={WorkflowDetail} />
        <Route exact path="/workflow/:id" component={WorkflowDetail} />
        <Route exact path="/workflow/:id/jobs" component={WorkflowJobs} />
        <Route exact path="/template" component={CharacterTemplate} />

        <Route exact path="/market-meme" component={MarketMeMe} />
        <Route exact path="/market-meme/add" component={MarketMeMeDetail} />
        <Route exact path="/market-meme/:id" component={MarketMeMeDetail} />
        <Route exact path="/market-publish" component={MarketPublish} />
        <Route exact path="/market-publish/:id/materials" component={MarketPublishMaterial} />
        <Route exact path="/market-invitation" component={MarketInvitation} />
        <Route exact path="/market-partner" component={MarketPartner} />
        <Route exact path="/market-partner/member" component={MarketPartnerMember} />
        <Route exact path="/market-wework" component={MarketWework} />
        <Route
          exact
          path="/market-wework/article/:groupId"
          component={(props) => { return <MarketArticle {...props} isWework />; }}
        />
        <Route exact path="/market-token-usage" component={MarketTokenUsage} />
        <Route exact path="/market-live-script" component={MarketLiveScript} />
        <Route exact path="/market-live-script/add" component={MarketLiveScriptDetail} />
        <Route exact path="/market-live-script/:id" component={MarketLiveScriptDetail} />
        <Route exact path="/market-live-script/:id/preview" component={MarketLiveScriptPreview} />
        <Route exact path="/market-live-script/:id/:edit" component={MarketLiveScriptPreview} />
        <Route exact path="/market-live-knowledge" component={MarketLiveKnowledge} />
        <Route exact path="/market-live-knowledge/add" component={MarketLiveKnowledgeDetail} />
        <Route exact path="/market-live-knowledge/:id" component={MarketLiveKnowledgeDetail} />
        <Route exact path="/market-article" component={MarketArticle} />
        <Route exact path="/market-novel" component={MarketNovelist} />
        <Route exact path="/market-novel/:name" component={MarketNovel} />
        <Route exact path="/market-course-materials" component={MarketCourseMaterials} />
        <Route exact path="/market-course-materials/:id" component={MarketCourseMaterialDetail} />
        <Route exact path="/market-voice-clone" component={MarketVoiceClone} />
        <Route exact path="/market-voice-clone/add" component={MarketVoiceCloneDetail} />
        <Route exact path="/market-voice-clone/topic-add" component={MarketVoiceCloneTopicDetail} />
        <Route exact path="/market-voice-clone/changer" component={MarketVoiceChanger} />
        <Route exact path="/market-voice-clone/topic/:id" component={MarketVoiceCloneTopicDetail} />
        <Route exact path="/market-voice-clone/:id" component={MarketVoiceCloneDetail} />
        <Route exact path="/market-voice-tts" component={MarketVoiceTtsV2} />
        <Route exact path="/market-voice-tts/add" component={MarketVoiceTtsV2Detail} />
        <Route exact path="/market-voice-tts/edit/:id" component={MarketVoiceTtsV2Detail} />
        <Route exact path="/market-wework-kf" component={MarketWeworkKF} />
        <Route exact path="/market-whatsapp" component={MarketWhatsapp} />
        <Route exact path="/market-douyin-data" component={MarketDouyinData} />
        <Route exact path="/market-douyin-private" component={MarketDouyinPrivate} />
        <Route exact path="/market-apikey" component={MarkeyApikey} />
        <Route exact path="/market-zhibo" component={MarketZhiBo} />
        <Route exact path="/market-textbook" component={MarketTextbook} />
        <Route exact path="/market-mcnwriter" component={MCNWriter} />
        <Route exact path="/market-podcast-chat" component={MarketPodcastChat} />
        <Route exact path="/market-gpt-image" component={MarketGptImage} />
        <Route exact path="/market-sku" component={MarketSKUFilter} />
        <Route exact path="/market-script-evaluation" component={MarketScriptEvaluationV2} />
        <Route exact path="/market-script-evaluation-v1" component={MarketScriptEvaluation} />

        <Route exact path="/prompts" component={Prompts} />
        <Route exact path="/workflow-v2" component={Workflows} />
        <Route exact path="/workflow-v2/:id" component={WorkflowV2} />

        <Redirect exact from="/" to={Sessions.getHomePage()} />

        <Redirect to={Sessions.getHomePage()} />
      </SwitchX>
    );
  }

  render = () => {
    if (!this.state.isReady) {
      return null;
    }

    const h5Paths = ['/live/materials/detail'];
    let isSkip = h5Paths.includes(window.location.pathname);
    if (window.location.pathname.indexOf('/live/material/') === 0) {
      isSkip = true;
    }

    if (Platform.isMobile() && !isSkip) {
      return (
        <div className="mobile-layout">
          <IeOutlined style={{ fontSize: 100, color: '#1890ff' }} />
          <Typography.Title level={3} style={{ margin: '10px 0' }}>
            为了给您提供更好的体验，
          </Typography.Title>
          <Typography.Title level={3} style={{ margin: '10px 0' }}>
            请使用电脑端浏览器打开本页面。
          </Typography.Title>
        </div>
      );
    }


    return (
      <ConfigProvider locale={zhCN} >
        <Provider store={store}>
          <ConnectedRouter history={history} >
            <>
              <Switch>
                <Route exact path={Configs.ROUTE.LOGIN} component={Login} />
                <Route exact path={Configs.ROUTE.WX_LOGIN} component={WxLogin} />
                <Route exact path="/token-login" component={TokenLogin} />
                <Route exact path="/node/test" component={TestNode} />
                <Route exact path="/live/material/:id" component={Materials} />
                <Route exact path="/live/materials/detail" component={MaterialDetail} />

                <CommonLayout collapsed={false}>
                  {this.renderRoutes()}
                </CommonLayout>
              </Switch>
              <Loading />
            </>
          </ConnectedRouter>
        </Provider>
      </ConfigProvider>
    );
  }
}

if (module.hot) {
  module.hot.accept(() => {
    store.replaceReducer(reducer);
  });
}
