/* eslint-disable no-console */
export default class ReconnectingWebSocket {
  constructor(url, protocols = [], messageCallback = () => { }, openCallback = () => { }) {
    this.url = url;
    this.protocols = protocols;
    this.messageCallback = messageCallback;
    this.openCallback = openCallback;
    this.manuallyClosed = false;

    this.socket = null;
    this.reconnectInterval = 1000;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;

    this.openConnection();
  }

  openConnection() {
    this.socket = new WebSocket(this.url, this.protocols);

    this.socket.addEventListener('open', (event) => {
      console.log('WebSocket connection opened:', event);
      this.openCallback();
      this.reconnectAttempts = 0;
    });

    this.socket.addEventListener('message', (event) => {
      this.messageCallback(event);
    });

    this.socket.addEventListener('close', (event) => {
      console.log('WebSocket connection closed:', event);

      if (this.manuallyClosed) {
        console.log('WebSocket closed by user. No more reconnections will be attempted.');
        return;
      }

      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        console.log(`Reconnecting (attempt ${this.reconnectAttempts + 1})...`);
        this.reconnectAttempts++;

        setTimeout(() => {
          this.openConnection();
        }, this.reconnectInterval);
      } else {
        console.log('Max reconnect attempts reached. No further reconnections will be attempted.');
      }
    });

    this.socket.addEventListener('error', (event) => {
      console.error('WebSocket error:', event);

      if (!this.manuallyClosed && this.reconnectAttempts < this.maxReconnectAttempts) {
        console.log(`Reconnecting due to error (attempt ${this.reconnectAttempts + 1})...`);
        this.reconnectAttempts++;
        setTimeout(() => { this.openConnection(); }, this.reconnectInterval);
      } else {
        console.log(
          this.reconnectAttempts < this.maxReconnectAttempts
            ? 'Error: WebSocket closed by user. No more reconnections will be attempted.'
            : 'Error: Max reconnect attempts reached. No further reconnections will be attempted.',
        );
      }
    });
  }

  send(data) {
    this.socket.send(data);
  }

  close(code, reason) {
    this.manuallyClosed = true;
    this.socket.close(code, reason);
  }
}
