/* eslint-disable no-prototype-builtins */
import { Toast } from '~/components';
import { DatePicker, Form, Input, Modal, Radio } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class CreateJobModal extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    workflow: PropTypes.object,
    flowParams: PropTypes.object,
    onSave: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    job: { historyMode: 'none' },
  }

  onChangeJobValue = (e, key, isParams = false) => {
    const job = _.cloneDeep(this.state.job);
    const value = e?.target ? e.target.value : e;
    if (isParams) {
      job.params = job.params || {};
      job.params[key] = value;
    } else {
      job[key] = value;
    }
    this.setState({ job });
  }

  onSave = async () => {
    const allKeys = ['historyMode', 'name', 'runAt', 'userMessage'];
    const { id, params, historyMode, name, runAt, userMessage } = this.state.job;
    if (!_.isEmpty(this.props.flowParams)) {
      allKeys.push('params');
    }

    const data = {
      name,
      runAt,
      historyMode,
      userMessage,
      flowUuid: this.props.workflow.uuid,
      params: JSON.stringify(params),
    };
    const hasKeyAndValue = (key) => { return data.hasOwnProperty(key) && Boolean(data[key]); };
    if (!allKeys.every(hasKeyAndValue)) {
      Toast.show('请检查参数!', Toast.Type.WARNING);
      return;
    }

    await this.props.onSave({ ...data, id });
    Toast.show('操作成功!', Toast.Type.SUCCESS);
  }

  render = () => {
    const { job } = this.state;
    return (
      <Modal
        width={1000}
        title="新增定时任务"
        open={this.props.open}
        maskClosable={false}
        onCancel={this.props.onClose}
        onOk={this.onSave}
      >
        <Form labelCol={{ span: 4 }}>
          <Form.Item label="任务名称">
            <Input value={job?.name} onChange={(e) => { return this.onChangeJobValue(e, 'name'); }} />
          </Form.Item>
          <Form.Item label="运行配置">
            <Radio.Group
              value={job.historyMode}
              onChange={(e) => { return this.onChangeJobValue(e, 'historyMode'); }}
            >
              <Radio value="none">忽略节点历史消息</Radio>
              <Radio value="autofit">启用并自动压缩节点消息</Radio>
              <Radio value="all">启用全部节点历史消息</Radio>
            </Radio.Group>
          </Form.Item>
          {
            _.keys(this.props.flowParams).map((key) => {
              return (
                <Form.Item label={key}>
                  <Input.TextArea
                    autoSize={{ minRows: 2 }}
                    value={(job?.params || {})[key]}
                    onChange={(e) => { return this.onChangeJobValue(e, key, true); }}
                  />
                </Form.Item>
              );
            })
          }
          <Form.Item label="执行时间">
            <DatePicker
              value={_.isUndefined(job.runAt) ? null : moment(job.runAt)}
              showTime={{ format: 'HH:mm' }}
              format="YYYY-MM-DD HH:mm"
              onChange={(e) => { return this.onChangeJobValue(e.toISOString(), 'runAt'); }}
              onOk={(e) => { return this.onChangeJobValue(e.toISOString(), 'runAt'); }}
            />
          </Form.Item>
          <Form.Item label="输入">
            <Input.TextArea
              autoSize={{ minRows: 2 }}
              value={job.userMessage}
              onChange={(e) => { return this.onChangeJobValue(e, 'userMessage'); }}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  }
}
