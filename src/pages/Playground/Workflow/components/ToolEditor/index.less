.base-common-editor-container {
  width: 100%;
  height: 150px;
  margin-top: 4px;
  border: 1px solid #d1d1d1;
  background-color: #fff;
  border-radius: 5px;

  .user-item {
    position: relative;
    top: 0;
    margin-right: 2px;
    padding: 2px 6px;
    font-size: 14px;
    color: #1890ff;
    background-color: #e6f7ff;
    border-color: #91d5ff;
    border-radius: 2px;
    box-sizing: border-box;
    cursor: default;
  }

  .editor-icon {
    margin-top: -3px;
    font-size: 24px;
  }

  .link,
  .form {
    padding: 0;
    border-bottom: 1px solid #1890ff;
    background-color: #fff;
    cursor: pointer;
  }

  .bf-controlbar {
    .separator-line {
      display: none;
    }

    .bfi-drop-down {
      height: 18px;
      line-height: 18px;
    }

    .dropdown-handler,
    .control-item {
      height: 18px;

      i:before {
        height: 18px;
        line-height: 18px;
      }
    }

    .dropdown-handler > span {
      line-height: 18px;

      i {
        height: 18px;
        line-height: 18px;
      }
    }

    :nth-last-child(2),
    :last-child {
      float: right;
    }
  }

  .bf-dropdown {
    .dropdown-content-inner {
      background-color: #fff;

      div {
        padding: 0 !important;
      }
    }
  }

  .bf-content {
    height: 100%;
    font-size: 14px;

    .public-DraftEditor-content {
      padding: 10px;
    }
  }

  .dropdown-container {
    .dropdown-content-inner {
      background-color: #fff;
    }

    .dropdown-arrow {
      display: none;
    }
  }
}

.min-tool-editor {
  height: 80px;
}
