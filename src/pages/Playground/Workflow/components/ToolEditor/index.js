/* eslint-disable react/prop-types, consistent-return, max-len */
import 'braft-editor/dist/index.css';
import 'braft-extensions/dist/emoticon.css';

import './index.less';

import { <PERSON>yunHelper } from '~/engine';
import { Editor, Platform } from '~/plugins';
import { Button, Upload } from 'antd';
import BraftEditor from 'braft-editor';
import { ContentUtils } from 'braft-utils';
import classNames from 'classnames';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import FullEditor from '../EditorDrawer';

const STATR_SHOW_STR = '用户输入';
export default class ToolEditor extends PureComponent {
  static propTypes = {
    isMin: PropTypes.bool,
    isFull: PropTypes.bool,
    showNode: PropTypes.bool,
    showUpload: PropTypes.bool,
    noEscaped: PropTypes.bool,
    params: PropTypes.object,
    types: PropTypes.array,
    controls: PropTypes.array,
    value: PropTypes.string,
    accept: PropTypes.string,
    onChange: PropTypes.func,
    editorId: PropTypes.string.isRequired, // 由外部传入, 需保证唯一
  }

  static defaultProps = {
    isMin: false,
    isFull: true,
    value: '',
    controls: [],
    types: [],
    noEscaped: false,
    showNode: true,
    onChange: () => { },
  }

  i18nRootPath = 'montage.materialSources'

  state = {
    nodeMap: {},
    controlKeys: [],
    editorState: null,
    open: false,
  }

  componentDidMount = async () => {
    const { value, editorId, datas, controls } = this.props;
    const nodeMap = {};
    controls.forEach((x) => {
      const key = _.trimStart(x.id, '#<').replace('>#', '');
      if (!['Start'].includes(key)) {
        nodeMap[key] = x.value;
      }
    });

    await this.formatControlKeys(datas);
    BraftEditor.use([this.entityUser]);

    this.setState({ // eslint-disable-line
      editorState: BraftEditor.createEditorState(this.formatTextToHtml(value), { editorId }),
      nodeMap,
    });
  }

  entityUser = {
    type: 'entity',
    includeEditors: [this.props.editorId],
    name: 'ENTITY-USER',
    mutability: 'IMMUTABLE',
    data: { value: '', link: '' },
    component: (props) => {
      const entity = props.contentState.getEntity(props.entityKey);
      const { value, link } = entity.getData();
      return (
        <span
          data-value={value}
          data-link={link}
          onClick={() => { return this.onClickTag(props); }} // 别问为什么..
          className={classNames('user-item', { link })}
        >
          {props.children}
        </span>
      );
    },
    importer: (nodeName, node) => {
      if (nodeName.toLowerCase() === 'span' && node.classList && node.classList.contains('user-item')) {
        const { link, title, value, form } = node.dataset;
        return { mutability: 'IMMUTABLE', data: { link, title, value, form } };
      }
    },
    exporter: (entity) => {
      const { link, title, value } = entity.data;
      if (link && title) { // LINK
        return `<a href="${link}">${title}</a>`;
      }

      return value;
    },
  };

  formatControlKeys = async (datas) => {
    const items = datas.filter((x) => { return x.showStr !== 'Start'; });
    const controlKeys = [];
    (items || []).forEach((x) => { controlKeys.push({ key: x.fixedStr, value: x.showStr }); });
    _.map((this.props.params || []), (value, key) => {
      controlKeys.push({ key: `#<${key}>#`, value: key === 'start' ? STATR_SHOW_STR : key });
    });
    const keys = _.map(controlKeys, 'key');
    _.map((this.props.controls || []), (x) => {
      if (!keys.includes(x.id)) {
        controlKeys.push({ key: x.id, value: x.value === 'start' ? STATR_SHOW_STR : x.value });
      }
    });

    await this.setState({ controlKeys });
  }

  formatTextToHtml = (text = '') => {
    const { controlKeys } = this.state;
    return Editor.formatTextToHtml(text, controlKeys, true);
  }

  formatHtmlToText = (text = '') => {
    return this.props.noEscaped ? Editor.formatJsonToText(text, this.props.types) : Editor.formatHtmlToText(text);
  }

  insertEditor = (...args) => {
    const { editorState } = this.state;

    this.setState({
      editorState: ContentUtils.insertText(editorState, ...args), // 插入Tag
    }, () => { this.afterInsert(); });
  }

  afterInsert = () => {
    this.setState({ editorState: ContentUtils.insertText(this.state.editorState, ' ') }); // 后跟空格以显示光标
  }

  onChangeEditor = (editorState) => {
    if (typeof editorState === 'string') {
      // eslint-disable-next-line no-param-reassign
      editorState = BraftEditor.createEditorState(this.formatTextToHtml(editorState), { editorId: this.props.editorId });
    }
    this.setState({ editorState });
    const text = this.formatHtmlToText(this.props.noEscaped ? editorState.toRAW(true) : editorState.toHTML());
    this.props.onChange(Editor.convertString(text));
  }

  onClickTag = () => {
    // To do something
  }

  onUpload = async (option) => {
    try {
      const url = await AliyunHelper.clipsUploadImage(option.file, (progress) => {
        const percent = Math.round((progress.loaded / progress.total) * 100);
        option.onProgress({ percent });
      });
      option.onSuccess();
      this.setState({ editorState: ContentUtils.insertText(this.state.editorState, url) }); // 后跟空格以显示光标
    } catch (e) {
      option.onError();
    }
  }

  onInsertUser = (item, keyStr) => {
    const entity = { type: 'ENTITY-USER', mutability: 'IMMUTABLE', data: { value: item.fixedStr } };
    const value = item.showStr === 'start' ? STATR_SHOW_STR : item.showStr;
    this.insertEditor(value, null, entity);
    const dom = document.getElementById(this.props.editorId).querySelector(`.${keyStr}`);
    dom.querySelector('.dropdown-handler').click();
  }

  onInsertNode = (item, keyStr) => {
    const { editorState } = this.state;

    this.setState({
      editorState: ContentUtils.insertText(editorState, item.fixedStr),
    }, () => { this.afterInsert(); });
    const dom = document.getElementById(this.props.editorId).querySelector(`.${keyStr}`);
    dom.querySelector('.dropdown-handler').click();
  }

  onChangeFullValue = (value) => {
    const { editorId } = this.props;
    this.setState({
      editorState: BraftEditor.createEditorState(this.formatTextToHtml(value), { editorId }),
    });

    this.props.onChange(value);
  }

  renderDropdown = (datas, keyStr = '', isNode = false) => {
    return (
      <div style={{ color: '#fff' }}>
        {
          datas.map((i) => {
            return (
              <Button
                type="link"
                style={{ width: '100%', marginBottom: 2, padding: '0px 4px' }}
                onClick={() => { return isNode ? this.onInsertNode(i, keyStr) : this.onInsertUser(i, keyStr); }}
              >
                {i.showStr === 'start' ? STATR_SHOW_STR : i.showStr}
              </Button>
            );
          })
        }
      </div>
    );
  }

  render = () => {
    const { editorId, datas, isMin, isFull, showUpload, params, controls } = this.props;
    const extendControls = [];
    const items = datas.filter((x) => { return x.showStr !== 'Start'; });
    const itemKeys = _.map(items, 'fixedStr');
    (controls || []).forEach((x) => {
      if (!itemKeys.includes(x.id)) {
        items.push({ fixedStr: x.id, showStr: x.value });
      }
    });

    if (!_.isEmpty(items)) {
      extendControls.push({
        key: 'output-dropdown',
        type: 'dropdown',
        text: '输入',
        className: 'output-dropdown',
        component: this.renderDropdown(items, 'output-dropdown'),
      });
    }

    if (!_.isEmpty(params)) {
      const keyObjs = Object.keys(params).map((key) => { return { fixedStr: `#<${key}>#`, showStr: key }; });
      extendControls.push({
        key: 'const-dropdown',
        type: 'dropdown',
        text: '常量',
        className: 'const-dropdown',
        component: this.renderDropdown(keyObjs, 'const-dropdown'),
      });
    }

    if (!_.isEmpty(this.state.nodeMap)) {
      const keyObjs = Object.keys(this.state.nodeMap).map((key) => {
        return { fixedStr: key, showStr: `${this.state.nodeMap[key]}(${key})` };
      });
      extendControls.push({
        key: 'node-dropdown',
        type: 'dropdown',
        text: '节点',
        className: 'node-dropdown',
        component: this.renderDropdown(keyObjs, 'node-dropdown', true),
      });
    }

    const editorStyle = _.isEmpty(extendControls) ? null : { height: 120 };
    if (showUpload) {
      extendControls.push(
        {
          key: 'custom-upload',
          type: 'component',
          component: (
            <Upload
              accept={this.props.accept}
              showUploadList={false}
              customRequest={this.onUpload}
            >
              <button type="button" className="control-item button upload-button" data-title="上传">
                上传
              </button>
            </Upload>
          ),
        },
      );
    }
    const btns = [
      {
        key: 'custom-button',
        type: 'button',
        text: isFull ? '全屏' : '',
        onClick: isFull ? () => { return this.setState({ open: true }); } : () => { },
      },
    ];

    if (this.props.showNode) {
      btns.push({
        key: 'edit-button',
        type: 'button',
        text: '节点信息',
        onClick: () => { Platform.emit('SHOW_NODE_ID', {}); },
      });
    }


    return (
      <div id={editorId} className={classNames('base-common-editor-container', { 'min-tool-editor': isMin })}>
        <BraftEditor
          style={editorStyle}
          id={editorId}
          controls={extendControls}
          extendControls={btns}
          value={this.state.editorState}
          ref={(ref) => { this[`ref${editorId}`] = ref; }}
          onChange={this.onChangeEditor}
          stripPastedStyles
        />

        {
          this.state.open &&
          <FullEditor
            {...this.props}
            open={this.state.open}
            onChange={(e) => { return this.onChangeFullValue(e); }}
            onClose={() => { return this.setState({ open: false }); }}
          />
        }
      </div>
    );
  }
}
