import { MoreOutlined } from '@ant-design/icons';
import { IconFont } from '~/components';
import Utils from '~/pages/Playground/Utils';
import { Platform } from '~/plugins';
import { Avatar, Collapse, Dropdown, Input, List, Modal, Popconfirm } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import {
  AIGC_NODES,
  BASE_NODES,
  CONTROL_NODES,
  DEMO_NODES,
  INFO_NODES,
  MSG_NODES,
  TOOL_NODES,
} from '../CustomNodes/Configs';

export default class ExtraSidebar extends PureComponent {
  static propTypes = {
    assistants: PropTypes.array,
    subflows: PropTypes.array,
    onDel: PropTypes.func,
    onUpdate: PropTypes.func,
  }

  state = {
    open: false,
    newName: '',
    controlNodes: { ...BASE_NODES, ...CONTROL_NODES },
  }

  onDragStart = (e, item) => {
    e.dataTransfer.setData('application/reactflow', JSON.stringify(item));
    e.dataTransfer.effectAllowed = 'move';
  };

  onSubmit = async () => {
    const { editData, newName } = this.state;
    if (_.isEmpty(newName) || editData?.name === newName) {
      return;
    }

    await this.props.onUpdate({ ...editData, name: newName });
    this.setState({ open: false, editData: {}, newName: '' });
  }

  renderAvatar = (item) => {
    let type = Utils.formatModalIcon(item?.llmSetting?.model);
    if (_.isEmpty(type)) {
      type = item?.icon || 'subflow';
    }

    return (
      <Avatar
        size="large"
        style={{ backgroundColor: '#fff' }}
        icon={<IconFont style={{ fontSize: 36, marginTop: 2 }} type={_.toLower(type)} />}
      />
    );
  }

  renderList = (nodes = []) => {
    return (
      <List
        dataSource={_.keys(nodes)}
        renderItem={(key) => {
          const item = nodes[key];
          return (
            <div
              draggable
              style={{ position: 'relative' }}
              onDragStart={(e) => { return this.onDragStart(e, item); }}
            >
              <List.Item key={item.id} >
                <List.Item.Meta
                  title={item.nodeName}
                  style={{ alignItems: 'center' }}
                  avatar={this.renderAvatar(item)}
                />
              </List.Item>
            </div>
          );
        }}
      />
    );
  }

  renderModal = () => {
    return (
      <Modal
        open={this.state.open}
        title="角色 - 重命名"
        onCancel={() => { return this.setState({ open: false, editData: {}, newName: '' }); }}
        onOk={this.onSubmit}
      >
        <Input value={this.state.newName} onChange={(e) => { return this.setState({ newName: e.target.value }); }} />
      </Modal>
    );
  }

  renderDropdown = (item) => {
    const items = [
      {
        key: 'delete',
        label: (
          <Popconfirm
            title="是否删除?"
            onConfirm={() => { return this.props.onDel(item.id); }}
          >
            <a>删除</a>
          </Popconfirm>
        ),
      },
      {
        key: 'rename',
        label: (
          <a onClick={() => { return this.setState({ open: true, editData: item, newName: item.name }); }}>重命名</a>
        ),
      },
    ];

    return (
      <Dropdown menu={{ items }} trigger={['click']}>
        <MoreOutlined style={{ position: 'absolute', top: 24, right: 0, fontSize: 18 }} />
      </Dropdown>
    );
  }

  render = () => {
    return (
      <>
        <Collapse defaultActiveKey={['base']}>
          <Collapse.Panel header="角色模块" key="assistant">
            <List
              dataSource={this.props.assistants || []}
              renderItem={(item) => {
                return (
                  <div
                    draggable
                    style={{ position: 'relative' }}
                    onDragStart={(e) => { return this.onDragStart(e, item); }}
                  >
                    <List.Item key={item.id}>
                      <List.Item.Meta
                        title={item.name}
                        style={{ alignItems: 'center' }}
                        avatar={this.renderAvatar(item)}
                      />
                    </List.Item>
                    {this.renderDropdown(item)}
                  </div>
                );
              }}
            />
          </Collapse.Panel>
          <Collapse.Panel header="控制模块" key="base">
            {this.renderList(this.state.controlNodes)}
          </Collapse.Panel>
          <Collapse.Panel header="信息模块" key="info" >
            {this.renderList(INFO_NODES)}
          </Collapse.Panel>
          <Collapse.Panel header="工具模块" key="tool">
            {this.renderList(TOOL_NODES)}
          </Collapse.Panel>
          <Collapse.Panel header="AIGC模块" key="aigc">
            {this.renderList(AIGC_NODES)}
          </Collapse.Panel>
          <Collapse.Panel header="消息模块" key="msg">
            {this.renderList(MSG_NODES)}
          </Collapse.Panel>
          <Collapse.Panel header="工作流模块" key="custom">
            <List
              dataSource={this.props.subflows || []}
              renderItem={(item) => {
                return (
                  <div
                    draggable
                    style={{ position: 'relative' }}
                    onDragStart={(e) => { return this.onDragStart(e, item); }}
                  >
                    <List.Item key={item.id} >
                      <List.Item.Meta
                        title={item.name}
                        style={{ alignItems: 'center' }}
                        avatar={this.renderAvatar(item)}
                      />
                    </List.Item>
                  </div>
                );
              }}
            />
          </Collapse.Panel>
          {
            !Platform.isProd() &&
            <Collapse.Panel header="自定义模块" key="demo">
              {this.renderList(DEMO_NODES)}
            </Collapse.Panel>
          }
        </Collapse>
        {this.state.open && this.renderModal()}
      </>
    );
  }
}
