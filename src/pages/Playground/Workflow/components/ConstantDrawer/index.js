import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Drawer, Input } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class ConstantDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    params: PropTypes.object,
    onSave: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    params: [],
  }

  componentDidMount = () => {
    const { params } = this.props;
    const obj = Object.keys(params).map((key) => { return { key, value: params[key] }; });
    this.setState({ params: _.isEmpty(obj) ? [{}] : obj });
  }

  onAdd = (index = 0) => {
    const params = _.cloneDeep(this.state.params) || [];
    params.splice(index + 1, 0, {});
    this.setState({ params });
  }

  onDel = (index) => {
    const params = _.cloneDeep(this.state.params) || [];
    params.splice(index, 1);
    this.setState({ params });
  }

  onChangeValue = (e, key, i) => {
    const value = e?.target ? e.target.value : e;
    const params = _.cloneDeep(this.state.params);
    params[i][key] = value;
    this.setState({ params });
  }

  onClose = async (isClose = true) => {
    const params = {};
    this.state.params.forEach(({ key, value }) => {
      if (!_.isUndefined(key)) { params[key] = value || ''; }
    });
    await this.props.onSave(params);
    if (isClose) {
      this.props.onClose();
    }
  }

  render = () => {
    return (
      <Drawer
        title="常量"
        open={this.props.open}
        placement="right"
        contentWrapperStyle={{ width: '20vw' }}
        onClose={this.onClose}
      >
        {
          (this.state.params || [{}]).map((obj, idx) => {
            return (
              <div>
                <Input.Group
                  compact
                  style={{ display: 'flex', alignItems: 'center', marginBottom: 10 }}
                >
                  <Input
                    value={obj.key}
                    placeholder="请输入变量的名称"
                    onChange={(e) => { return this.onChangeValue(e, 'key', idx); }}
                  />
                  <div style={{ display: 'flex', width: 80, justifyContent: 'space-evenly' }}>
                    <PlusOutlined onClick={() => { return this.onAdd(idx); }} />
                    <MinusOutlined onClick={() => { return this.onDel(idx); }} />
                  </div>
                </Input.Group>
              </div>
            );
          })
        }

        <div style={{ textAlign: 'center' }}>
          <Button type="primary" onClick={() => { return this.onClose(false); }}>保存</Button>
        </div>
      </Drawer>
    );
  }
}
