import { Drawer } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ToolEditor from '../ToolEditor';

export default class EditorDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    params: PropTypes.object,
    controls: PropTypes.array,
    value: PropTypes.string,
    editorId: PropTypes.string,
    onChange: PropTypes.func,
    onClose: PropTypes.func,
  }

  componentDidMount = async () => {
    setTimeout(() => {
      const editorDom = document.getElementById(`${this.props.editorId}-full`);
      editorDom.style.height = 'unset';
      editorDom.style['max-height'] = '90vh';
      editorDom.querySelector('.bf-container ').style.height = '90vh';
      editorDom.querySelector('.bf-container ').style['max-height'] = '90vh';
    }, 300);
  }

  render = () => {
    const { editorId, controls, params, value } = this.props;
    return (
      <Drawer
        open={this.props.open}
        placement="right"
        contentWrapperStyle={{ width: '30vw' }}
        onClose={this.props.onClose}
      >
        <ToolEditor
          isMin={false}
          isFull={false}
          key={`${editorId}-full`}
          editorId={`${editorId}-full`}
          value={value}
          datas={[]}
          controls={controls}
          params={params}
          onChange={(e) => { return this.props.onChange(e); }}
        />
      </Drawer>
    );
  }
}
