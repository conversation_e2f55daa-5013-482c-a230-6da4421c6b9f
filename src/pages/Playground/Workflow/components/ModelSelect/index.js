import { PlusOutlined } from '@ant-design/icons';
import { Sessions } from '~/engine';
import { LLM_PRICE_ENUM } from '~/pages/Market/Partners/configs';
import { Platform } from '~/plugins';
import { Button, Divider, Input, Select, Space } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class ModelSelect extends PureComponent {
  static propTypes = {
    canAdd: PropTypes.bool,
    value: PropTypes.string,
    params: PropTypes.object,
    onChange: PropTypes.func,
  }

  static defaultProps = {
    canAdd: true,
  }

  state = {
    name: '',
    aiModels: _.map(Sessions.getModels(), (v, k) => {
      return { label: k, options: v.map((x) => { return { label: Sessions.getModelNames()[x], value: x }; }) };
    }),
  }

  componentDidMount = () => {
  }

  onAdd = () => {
    const { name } = this.state;
    if (_.isEmpty(name)) return;
    Platform.emit('WORKFLOW_ADD_CONSTANT', { ...this.props.params, [name]: '' });
    this.setState({ name: '' }, () => {
      this.refInput?.focus();
    });
  }

  renderDropdown = (menu) => {
    if (!this.props.canAdd) return menu;

    return (
      <>
        {menu}
        <Divider style={{ margin: '8px 0' }} />
        <Space style={{ padding: '0 8px 4px' }} >
          <Input
            placeholder=" 请输入"
            ref={(el) => { this.refInput = el; }}
            value={this.state.name}
            onChange={(e) => { return this.setState({ name: e.target.value }); }}
          />
          <Button type="text" icon={<PlusOutlined />} onClick={() => { return this.onAdd(); }}>
            新增
            <span style={{ marginLeft: 20, color: '#ccc', fontSize: 14 }}>
              新增后,自动添加至工作流常量
            </span>
          </Button>
        </Space>
      </>
    );
  }

  render = () => {
    const { value } = this.props;

    return (
      <Select
        value={value}
        onChange={this.props.onChange}
        optionLabelProp="label"
        dropdownRender={this.renderDropdown}
      >
        {
          _.map(this.state.aiModels, (v) => {
            return (
              <Select.OptGroup key={v.label} label={v.label}>
                {
                  v.options.map((x) => {
                    return (
                      <Select.Option value={x.value} label={x.label}>
                        <div className="demo-option-label-item">
                          {x.label}
                          <span style={{ marginLeft: 20, color: '#ccc', fontSize: 14 }} aria-label={x.label}>
                            {LLM_PRICE_ENUM[x.label]}
                          </span>
                        </div>
                      </Select.Option>
                    );
                  })
                }
              </Select.OptGroup>
            );
          })
        }
        {/* {
          !_.isEmpty(params) &&
          <Select.OptGroup key="自定义" label="自定义">
            {
              _.keys(params).map((x) => {
                return <Select.Option value={`{{${x}}}`}>{`{{${x}}}`}</Select.Option>;
              })
            }
          </Select.OptGroup>
        } */}
      </Select>
    );
  }
}
