import { CloseOutlined, SyncOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { BaseEdge, EdgeLabelRenderer, getSmoothStepPath } from 'reactflow';

import { COMMON_NODE_ENUM } from '../CustomNodes/Configs';

export default class CloseEdge extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    source: PropTypes.string,
    style: PropTypes.object,
    onDel: PropTypes.func,
  }


  renderLoopLabel = (labelX, labelY) => {
    return (
      <div
        style={{
          position: 'absolute',
          background: 'transparent',
          padding: 10,
          color: '#ff5050',
          fontSize: 12,
          fontWeight: 700,
          transform: `translate(-50%, 0%) translate(${labelX}px,${labelY}px)`,
        }}
        className="nodrag nopan"
      >
        <SyncOutlined /> 循环
      </div>
    );
  }

  render = () => {
    const [edgePath, labelX, labelY] = getSmoothStepPath(this.props);

    return (
      <>
        <BaseEdge path={edgePath} labelShowBg={false} style={this.props.style} />
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              fontSize: 12,
              pointerEvents: 'all',
            }}
            className="nodrag nopan"
          >
            <Button
              ghost
              size="small"
              shape="circle"
              icon={<CloseOutlined />}
              style={{
                border: '2px solid',
                color: 'rgba(0,0,0,0.5)',
                borderColor: 'rgba(0,0,0,0.5)',
                backgroundColor: '#fff',
              }}
              onClick={() => { return this.props.onDel(this.props.id); }}
            />
          </div>
          {
            _.startsWith(this.props.source, COMMON_NODE_ENUM.loopN) &&
            this.renderLoopLabel(labelX, labelY)
          }
        </EdgeLabelRenderer>
      </>
    );
  }
}
