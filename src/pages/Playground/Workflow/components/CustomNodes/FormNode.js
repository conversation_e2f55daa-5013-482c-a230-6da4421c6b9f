import { DeleteOutlined, Radar<PERSON>hartOutlined, SettingOutlined } from '@ant-design/icons';
import { Popconfirm } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

export default class FormNode extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
  }

  render = () => {
    const { data, id } = this.props;
    return (
      <div className="form-node">
        <Popconfirm title="是否删除?!" onConfirm={() => { data.onDel(id); }} >
          <DeleteOutlined className="left-icon" />
        </Popconfirm>
        <SettingOutlined
          className="right-icon"
          onClick={() => { data.onOpenSetting(id); }}
        />
        <RadarChartOutlined style={{ color: '#fff', fontSize: 24, marginTop: 2 }} />
        <div className="text">信息收集</div>
        <Handle
          type="target"
          position={Position.Left}
          className="base-handle"
          style={{ left: -10, color: '#69f' }}
        />
        <Handle
          type="source"
          position={Position.Right}
          className="base-handle"
          style={{ right: -10 }}
        />
      </div>
    );
  }
}
