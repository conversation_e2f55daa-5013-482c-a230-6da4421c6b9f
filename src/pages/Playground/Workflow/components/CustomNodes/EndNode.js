import { CheckSquareOutlined, SettingOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

export default class EndNode extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
  }

  render = () => {
    const { id, data } = this.props;

    return (
      <div className="start-node" style={{ backgroundColor: '#f66' }}>
        <SettingOutlined
          className="right-icon"
          onClick={() => { data.onOpenSetting(id); }}
        />
        <div style={{ backgroundColor: '#fff' }}>
          <CheckSquareOutlined style={{ fontSize: 48, color: '#fff' }} />
        </div>
        <Handle
          type="target"
          position={Position.Left}
          className="base-handle"
          style={{ left: -4 }}
        />
      </div>
    );
  }
}
