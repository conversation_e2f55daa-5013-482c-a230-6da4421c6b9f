import { CaretRightOutlined, SettingOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

export default class StartNode extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
  }

  render = () => {
    const { id, data } = this.props;

    return (
      <div className="start-node">
        <SettingOutlined
          className="right-icon"
          onClick={() => { data.onOpenSetting(id); }}
        />
        <CaretRightOutlined style={{ fontSize: 64, color: '#fff' }} />
        <Handle type="source" position={Position.Right} className="base-handle" style={{ right: -8 }} />
      </div>
    );
  }
}
