
.system-handle,
.user-handle,
.base-handle {
  width: 16px;
  height: 16px;
  border: 3px solid;
  background: #fff;
  color: #030;
}

.system-handle {
  left: -8px;
  color: #fa8;
  z-index: 10;
}

.user-handle {
  top: auto;
  bottom: 0;
  left: -8px;
  color: #9ce;
  z-index: 10;
}

.start-node {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  background-color: #0c6;
  border-radius: 10px;

  .right-icon {
    display: flex;
    position: absolute;
    top: 4px;
    right: 4px;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    color: #fff;
  }
}

.form-node,
.flow-node,
.process-txt,
.common-node {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 120px;
  margin-left: 10px;
  background-color: #6cf;
  border-radius: 10px;

  .left-icon {
    display: flex;
    position: absolute;
    top: 4px;
    left: 4px;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    color: #fff;
  }

  .right-icon {
    display: flex;
    position: absolute;
    top: 4px;
    right: 4px;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    color: #fff;
  }

  .text {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 20px;
    font-size: 16px;
    color: #fff;
  }

  .system-handle {
    color: #03c;
  }
  .user-handle {
    color: #39f;
  }
}

.decision-node {
  position: relative;
  width: 120px;
  height: 120px;

  .inner-wrap {
    position: absolute;
    top: 0;
    left: 0;
    width: 120px;
    height: 120px;
    background-color: #f9c;
    border-radius: 10px;
    transform: rotate(45deg);
  }
}

.python-interpreter,
.python-evaluator {
  background-color: #d21b14;
}

.form-node {
  background-color: #96f;
}

.save-data {
  background-color: #f93;
}

.process-txt {
  background-color: #c6c;
}

.extract-html {
  background-color: #f90;
}

.extract-pdf {
  background-color: #c60;
}

.execute-agent {
  background-color: #f66;
}

.search-kb {
  background-color: #c9f;
}

.search-cite-kb {
  background-color: #5e548e;
}

.search-web {
  background-color: #0cf;
}

.search-feedback {
  background-color: #09f;
}

.summarize-pdf {
  background-color: #fc6;
}

.summarize-web {
  background-color: #f6c;
}

.summarize-text {
  background-color: #6cc;
}

.loop-n {
  background-color: #9c9;
}

.switch-case {
  background-color: #66f;
}

.json-extractor {
  background-color: #f3f;
}

.input-transform {
  background-color: #996;
}

.save-vector {
  background-color: #0af;
}

.iterator {
  background-color: #fa0;
}

.save-feedback {
  background-color: #fc6;
}

.webhook {
  background-color: #3c9;
}

.openapi {
  background-color: #87ceeb;
}

.extract-text {
  background-color: #d4237a;
}

.whatsapp-sender {
  background-color: #25d366;
}

.wework-rpa-sender {
  background-color: #0079e7;
}

.dingtalk-sender {
  background-color: #228df4;
}

.wework-kf-sender {
  background-color: #51ab3a;
}

.tts {
  background-color: #d81e06;
}

.asr {
  background-color: #9c0;
}

.rss-reader {
  background-color: #f0f;
}

.video-downloader {
  background-color: #0092e5;
}

.video2audio {
  background-color: #cd853f;
}

.image2video {
  background-color: #1eca66;
}

.text2video {
  background-color: #9acd32;
}

.text2image {
  background-color: #eb5e28;
}

.summary-viewpoint {
  background-color: #e76f51;
}

.mp-article-publisher {
  background-color: #00ad19;
}

.xiaoyuzhou-publisher {
  background-color: #00008b;
}

.db-insert {
  background-color: #219ebc;
}

.publisher {
  background-color: #d4a373;
}

.sleep {
  background-color: #640d14;
}

.text-user-input {
  background-color: #06d6a0;
}

.excel-processor {
  background-color: #007f5f;
}

.image-styled {
  background-color: #ff5339;
}

.remove-text-ad {
  background-color: #3f37c9;
}

.ad-text-evaluate {
  background-color: #f94144;
}

.video-ocr-fix {
  background-color: #00b4d8;
}

.article-material-extract {
  background-color: #8d99ae;
}

.article-material-search-and-merge {
  background-color: #4cc9f0;
}

.summary-outline {
  background-color: #b8e0d2;
}

.gather-from-chat {
  background-color: #12b1a1;
}

.save-flow-param {
  background-color: #59607f;
}

.transform-content {
  background-color: #4caf50;
}

.subflow-node {
  width: 320px;
  height: 240px;
  background-color: rgba(255, 0, 0, 0.2);
  border-radius: 10px;

  .node-name {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
}
