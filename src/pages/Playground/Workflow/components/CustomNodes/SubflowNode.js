import { DeleteOutlined, SettingOutlined } from '@ant-design/icons';
import { IconFont } from '~/components';
import { Avatar, Divider, Popconfirm, Typography } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

export default class SubflowNode extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
  }

  render = () => {
    const { id, data } = this.props;
    return (
      <div className="subflow-node">
        <Popconfirm
          title="是否打开工作流?!"
          onConfirm={() => { window.open(`${window.location.origin}/workflow/${data.uuid}`); }}
        >
          <a style={{ position: 'absolute', left: 10, top: 10, fontSize: 16 }}>
            工作流
          </a>
        </Popconfirm>
        <div style={{ position: 'absolute', right: 10, top: 10 }}>
          <Popconfirm title="是否删除?!" onConfirm={() => { data.onDel(id); }} >
            <DeleteOutlined style={{ fontSize: 20 }} />
          </Popconfirm>
          <Divider type="vertical" style={{ margin: '0 3px' }} />
          <SettingOutlined
            style={{ fontSize: 20 }}
            onClick={() => { data.onOpenSetting(id); }}
          />
        </div>
        <div style={{ height: '100%', display: 'flex', justifyContent: 'space-evenly', alignItems: 'center' }}>
          <Avatar
            style={{ backgroundColor: '#fff', marginRight: 10, width: 50, height: 50 }}
            icon={<IconFont type="sub-workflow" style={{ fontSize: 48 }} />}
          />
          <Typography.Title style={{ maxWidth: '75%' }} className="node-name">{data.name}</Typography.Title>
        </div>
        <Handle type="target" position={Position.Left} className="base-handle" style={{ left: -10 }} />
        <Handle type="source" position={Position.Right} className="base-handle" style={{ right: -10 }} />
      </div>
    );
  }
}
