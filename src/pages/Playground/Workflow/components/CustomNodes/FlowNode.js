import './index.less';

import { DeleteOutlined, QuestionCircleOutlined, SettingOutlined } from '@ant-design/icons';
import { IconFont } from '~/components';
import Utils from '~/pages/Playground/Utils';
import { Avatar, Popconfirm, Popover, Typography } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

import nodeDesc from './desc';

export default class FlowNode extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    type: PropTypes.string,
    data: PropTypes.object,
  }

  renderAvatar = (item) => {
    const type = Utils.formatModalIcon(item?.llmSetting?.model);

    return (
      <Avatar
        size="large"
        style={{ backgroundColor: '#fff' }}
        icon={<IconFont style={{ fontSize: 24, marginTop: 2 }} type={_.toLower(type)} />}
      />
    );
  };

  render = () => {
    const { id, data, type } = this.props;
    const desc = nodeDesc[type];

    return (
      <div className="flow-node">
        {this.renderAvatar(data)}
        <Popconfirm title="是否删除" onConfirm={() => { data.onDel(id); }} >
          <DeleteOutlined className="left-icon" />
        </Popconfirm>
        {/* <PlayCircleOutlined
          className="right-icon"
          style={{ right: 36 }}
          onClick={() => { data.onOpenSetting(id); }}
        /> */}
        <SettingOutlined
          className="right-icon"
          onClick={() => { data.onOpenSetting(id); }}
        />
        <Popover
          title={desc.name}
          content={<div>{desc.content.map((x) => { return <>{x}<br /></>; })}</div>}
        >
          <QuestionCircleOutlined className="right-icon" style={{ bottom: 4, top: 'unset' }} />
        </Popover>
        <div className="text" style={{ marginLeft: 10 }}>
          <Typography.Paragraph
            ellipsis={{ rows: 2, expandable: false }}
            style={{ marginBottom: 0, color: '#fff' }}
          >
            {data?.name}
          </Typography.Paragraph>
        </div>
        {/* <Tooltip title="系统">
          <Handle id="system" type="target" position={Position.Left} className="system-handle" />
        </Tooltip> */}
        <Handle id="user" type="target" position={Position.Left} className="system-handle" />
        <Handle type="source" position={Position.Right} className="base-handle" style={{ right: -8 }} />
      </div>
    );
  };
}
