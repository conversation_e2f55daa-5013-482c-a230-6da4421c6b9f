import { DeleteOutlined, SettingOutlined } from '@ant-design/icons';
import { IconFont } from '~/components';
import { Avatar, Popconfirm } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

export default class ProcessTxtNode extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
  }

  render = () => {
    const { id, data } = this.props;
    return (
      <div className="save-data-node">
        <Popconfirm title="是否删除?!" onConfirm={() => { data.onDel(id); }} >
          <DeleteOutlined className="left-icon" />
        </Popconfirm>
        <SettingOutlined
          className="right-icon"
          onClick={() => { data.onOpenSetting(id); }}
        />
        <Avatar
          style={{ backgroundColor: '#fff' }}
          icon={<IconFont type="save-data-node" style={{ fontSize: 32 }} />}
        />
        <div className="text">保存数据</div>
        <Handle
          type="target"
          position={Position.Left}
          className="base-handle"
          style={{ left: -10, color: '#69f' }}
        />
        <Handle
          type="source"
          position={Position.Right}
          className="base-handle"
          style={{ right: -10 }}
        />
      </div>
    );
  }
}
