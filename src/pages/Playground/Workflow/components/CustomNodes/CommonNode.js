import { DeleteOutlined, QuestionCircleOutlined, SettingOutlined } from '@ant-design/icons';
import { IconFont } from '~/components';
import Utils from '~/pages/Playground/Utils';
import { Avatar, Popconfirm, Popover } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

import { COMMON_NODE_ENUM, MULTI_OUTPUT_NODES } from './Configs';
import nodeDesc from './desc';

export default class CommonNode extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
  }

  state = {
    className: '',
  }

  componentDidMount = () => {
    const { id, data } = this.props;
    const iconName = data.oType || _.head(Utils.formatSubtype(id));
    let className = iconName.split(/(?=[A-Z])/).map((x) => { return _.lowerCase(x); }).join('-');
    className = _.replace(className, / /g, '');
    const nodeKey = iconName.split('_').map((x) => { return _.upperFirst(x); }).join('');
    this.setState({ className, nodeKey: _.lowerFirst(nodeKey) });
  }

  render = () => {
    const { id, data } = this.props;
    const { className, nodeKey } = this.state;
    const desc = nodeDesc[nodeKey];

    return (
      <div className={`common-node ${className}`}>
        <Popconfirm title="是否删除?!" onConfirm={() => { data.onDel(id); }} >
          <DeleteOutlined className="left-icon" />
        </Popconfirm>
        <SettingOutlined
          className="right-icon"
          onClick={() => { data.onOpenSetting(id); }}
        />
        {
          !_.isUndefined(desc) &&
          <Popover
            title={desc.name}
            content={<div>{desc.content.map((x) => { return <>{x}<br /></>; })}</div>}
          >
            <QuestionCircleOutlined className="right-icon" style={{ bottom: 4, top: 'unset' }} />
          </Popover>
        }
        {
          nodeKey === 'pythonEvaluator' &&
          <span style={{ position: 'absolute', top: 4, color: '#000' }}>即将下线</span>
        }
        <Avatar
          style={{ backgroundColor: '#fff', marginRight: 5 }}
          icon={<IconFont type={className} style={{ fontSize: 32 }} />}
        />
        <div className="text">{data?.name || COMMON_NODE_ENUM[nodeKey]}</div>
        <Handle
          type="target"
          position={Position.Left}
          className="base-handle"
          style={{ left: -10, color: '#69f' }}
        />
        <Handle
          type="source"
          position={Position.Right}
          className="base-handle"
          style={{ right: -10 }}
        />
        {
          MULTI_OUTPUT_NODES.includes(nodeKey) &&
          <Handle
            id="default"
            type="source"
            position={Position.Right}
            className="user-handle"
            style={{ right: -10, left: 'unset' }}
          />
        }
      </div>
    );
  }
}
