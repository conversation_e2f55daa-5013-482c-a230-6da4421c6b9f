import { DeleteOutlined, QuestionCircleOutlined, SettingOutlined } from '@ant-design/icons';
import { Divider, Popconfirm, Popover } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

import nodeDesc from './desc';

export default class DecisionNode extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    type: PropTypes.string,
    data: PropTypes.object,
  }

  render = () => {
    const { id, data, type } = this.props;
    const desc = nodeDesc[type];

    return (
      <div className="decision-node">
        <div className="inner-wrap" />
        <Handle
          type="target"
          position="left"
          className="base-handle"
          style={{ left: -20, color: '#69f' }}
        />
        <Handle
          id="true"
          type="source"
          position={Position.Right}
          className="base-handle"
          style={{ color: '#4dff4d', right: -20 }}
        />
        <Handle
          id="false"
          type="source"
          position={Position.Bottom}
          className="base-handle"
          style={{ color: '#ff4d4d', bottom: -20 }}
        />
        <Popover
          title={desc.name}
          content={<div>{desc.content.map((x) => { return <>{x}<br /></>; })}</div>}
        >
          <QuestionCircleOutlined className="right-icon" style={{ bottom: 4, top: 'unset' }} />
        </Popover>
        <div style={{ position: 'absolute', top: -24, right: 0 }}>
          <Popconfirm title="是否删除?!" onConfirm={() => { data.onDel(id); }} >
            <DeleteOutlined style={{ fontSize: 16 }} />
          </Popconfirm>
          <Divider type="vertical" style={{ margin: '0 3px' }} />
          <SettingOutlined
            style={{ fontSize: 16 }}
            onClick={() => { data.onOpenSetting(id); }}
          />
        </div>
        <div style={{ position: 'absolute', color: '#fff', bottom: 0, left: 52 }}>否</div>
        <div style={{ position: 'absolute', color: '#fff', right: 0, top: 36 }}>是</div>
      </div>
    );
  }
}
