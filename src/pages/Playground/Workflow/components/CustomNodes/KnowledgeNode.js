import { DeleteOutlined, QuestionCircleOutlined, SettingOutlined } from '@ant-design/icons';
import { IconFont } from '~/components';
import Utils from '~/pages/Playground/Utils';
import { Avatar, Popconfirm, Popover, Tooltip } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

import { TOOL_NODES } from './Configs';
import nodeDesc from './desc';

export default class KnowledgeNode extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
  }

  state = {
    className: '',
  }

  componentDidMount = () => {
    const { id, data } = this.props;
    const iconName = data.oType || _.head(Utils.formatSubtype(id));
    const className = iconName.split(/(?=[A-Z])/).map((x) => { return _.lowerCase(x); }).join('-');
    const nodeKey = iconName.split('_').map((x) => { return _.upperFirst(x); }).join('');
    this.setState({ className, nodeKey: _.lowerFirst(nodeKey) });
  }

  render = () => {
    const { id, data } = this.props;
    const { className, nodeKey } = this.state;
    const desc = nodeDesc[nodeKey];
    return (
      <div className={`common-node ${className}`}>
        <Popconfirm title="是否删除?!" onConfirm={() => { data.onDel(id); }} >
          <DeleteOutlined className="left-icon" />
        </Popconfirm>
        <SettingOutlined
          className="right-icon"
          onClick={() => { data.onOpenSetting(id); }}
        />
        {
          !_.isUndefined(desc) &&
          <Popover
            title={desc.name}
            content={<div>{desc.content.map((x) => { return <>{x}<br /></>; })}</div>}
          >
            <QuestionCircleOutlined className="right-icon" style={{ bottom: 4, top: 'unset' }} />
          </Popover>
        }
        <Avatar
          style={{ backgroundColor: '#fff', marginRight: 10 }}
          icon={<IconFont type={className} style={{ fontSize: 32 }} />}
        />
        <div className="text">{data?.name || TOOL_NODES[nodeKey]?.nodeName}</div>
        <Tooltip title="文本">
          <Handle type="target" position={Position.Left} className="system-handle" />
        </Tooltip>
        {
          ['extractHtml', 'extractPdf'].includes(nodeKey) &&
          <Tooltip title="链接">
            <Handle id="link" type="target" position={Position.Left} className="user-handle" />
          </Tooltip>
        }
        <Handle type="source" position={Position.Right} className="base-handle" style={{ right: -10 }} />
      </div>
    );
  }
}
