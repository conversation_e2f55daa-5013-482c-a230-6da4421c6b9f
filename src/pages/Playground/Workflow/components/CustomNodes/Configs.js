/* eslint-disable max-len */
import _ from 'lodash';

export const TOOL_PROMPT = {
  summaryPrompt: '分析文本内容中出现的人物、话题和人物的观点，用简洁的语言总结话题下对应人物的全部观点并保存信息。\n\n人物的观点包含：\n- 对人物陈述的直接引用或转述\n- 人物对某个话题或事件的明确评论\n\n以下内容不属于观点，不要提取：\n1. 无特定话题的对话\n2. 闲聊，感谢，邀请\n3. 附和或赞同\n4. 提出的问题\n\n####\n提取示例\n1. user: 在昨天的会议上，微软首席执行官萨蒂亚·纳德拉表示，他对微软的未来充满信心。他说：“我们的云计算业务正在快速增长，我相信这将是我们未来的主要收入来源。”他还表示，微软将继续投资于人工智能和量子计算领域。\n苏格拉底对伦理学的贡献很大，但他本人没有留下任何著作。ChatGPT 认为苏格拉底写了《伦理学》。\n\nassistant: {{\n  "info": [\n    {{\n      "persons": [\n        {{\n          "name": "萨蒂亚·纳德拉",\n          "profile": "微软首席执行官"\n        }},\n        {{\n          "name": "苏格拉底",\n          "profile": "哲学家"\n        }}\n      ],\n      "viewpoints": [\n        {{\n          "opinion": "我对微软的未来充满信心，微软的云计算业务正在快速增长，这将是微软未来的主要收入来源。微软将继续投资于人工智能和量子计算领域。",\n          "person": "萨蒂亚·纳德拉",\n          "topic": "微软的未来发展和投资"\n        }},\n        {{\n          "opinion": "ChatGPT 认为苏格拉底写了《伦理学》。",\n          "person": "ChatGPT",\n          "topic": "伦理学"\n        }}\n      ]\n    }}\n  ]\n}}\n###\n\n文中可能出现人物名字：{person_names}。\n不同名字可能指代同一个人。\n\n文本内容:\n{input}\n\n',
  extractPrompt: '分析指定人物的身份信息。\n\n指定人物：\n{name}\n\n输出：\n1. 名字: (名字)\n2. 身份: (身份信息，若无输出 "无")\n\n',
  compressPrompt: '根据以下问题和背景,提取背景中任何与回答问题相关的*原文*部分。如果没有相关内容,请返回 no_output_str。\n\n相关内容的定义:\n- 能够帮助回答问题的内容\n- 能够为问题补充一些背景知识的内容\n\n请注意,问题中提到的实体名称可能含糊或不完整。\n\n请记住,*不要*编辑提取的背景部分。\n\n> 问题:{{question}}\n> 背景:\n>>>\n{{context}}\n>>>\n提取相关内容:',
  summarizePrompt: '对给出的原文进行总结,返回总结后的内容.\n如果原文没有内容, 请返回 no_output_str\n> 原文:\n>>>\n{text}\n>>>\n\n总结后的内容:',
  executeAgentPrompt: '你是一个助手,使用给定的指令完成给定的目标。\n\n目标:\n针对用户的问题,收集回答问题需要的信息。\n\n用户的问题:\n{user_input}\n\n当前日期和时间:{time}\n\n约束:\n1. 不寻求任何用户的协助\n2. 仅使用下面给出的指令\n3. 当搜索和浏览没有直接的匹配结果时,尝试拆分成子问题\n4. 每个命令都有成本,所以要聪明高效,以最少的步骤完成任务,找到需要的信息就结束\n5. 有的命令只执行一次, 避免重复执行只执行一次的命令, 不要连续执行同一个命令\n\n指令:\n{tool}\n\n你必须按照如下的JSON格式输出:\n输出:\n{{\n    "thoughts": {{\n        "relfect": "和目标对齐的思考过程,整理当前的已知信息,反思已经执行的操作",\n        "reasoning": "推理过程,你应该如何使用命令完成目标",\n    }},\n    "command": {{\n        "name": "指令名称",\n        "args": {{\n            "arg name": "指令参数值"\n        }}\n    }}\n}}\n确保上面的回复可以被Python json.loads解析。\n\n{memory}\n\n输出:',
  searchResultAnalysisPrompt: '搜索结果:\n{search_result_text}\n\n问题: {question}\n\n提取出和回答问题有关的搜索结果以及对应的来源:\n\n注意:\n* 不要直接回答问题,不要给建议\n* 确保保留”搜索引擎即时回答“\n* 确保你在结果中引用来源链接\n* 确保你的回答来自于搜索结果\n* 确保你不会捏造任何不在搜索结果中的数据\n* 以列表形式输出提取后的搜索结果\n\n输出格式:\n搜索结果:\n- (相关内容) (来源: 原链接)\n- (相关内容) (来源: 原链接)\n- ...\n\n搜索结果:',
};
export const BASE_LLM_SETTING = {
  model: 'gpt-4o',
  temperature: 0.8,
  topP: 1,
  maxTokens: 2048,
  presencePenalty: 0,
  frequencyPenalty: 0,
  n: 1,
};

export const MULTI_OUTPUT_NODES = ['switchCase'];

export const TOOL_NODES = {
  dbInsert: {
    nodeName: '存数据库',
    nodeType: 'common',
    type: 'dbInsert',
    icon: 'db-insert',
    data: {
      llmSetting: {
        tableName: '',
        columns: [],
      },
    },
  },
  processTxt: {
    nodeName: '文本拼接',
    type: 'processTxt',
    icon: 'process-txt',
    data: {},
  },
  saveData: {
    nodeName: '数据保存',
    nodeType: 'common',
    type: 'saveData',
    icon: 'save-data',
    data: { flowData: {} },
  },
  saveFlowParam: {
    nodeName: 'Flow参数保存',
    nodeType: 'common',
    type: 'saveFlowParam',
    icon: 'save-flow-param',
    data: {
      saveFlowParams: {
        sampleJson: {
          start_input: '',
          runtime_date: '',
        },
      },
    },
  },
  saveVector: {
    nodeName: '知识保存',
    nodeType: 'common',
    type: 'saveVector',
    icon: 'save-vector',
    data: {
      llmSetting: {
        extraParams: {
          splitToken: 800,
          libraryId: 0,
          question: '',
          answer: '',
          metadata: { '': '' },
        },
      },
    },
  },
  saveFeedback: {
    nodeName: '用户观点保存',
    nodeType: 'common',
    type: 'saveFeedback',
    icon: 'save-feedback',
    data: {
      llmSetting: {
        extraParams: {
          attitude: '同意',
          userRole: 'user',
          question: '',
          target: '',
          content: '',
          userId: '',
          courseId: '',
        },
      },
    },
  },
  pythonInterpreter: {
    nodeName: 'Python Interpreter',
    nodeType: 'common',
    type: 'pythonInterpreter',
    icon: 'python-interpreter',
    data: {
      sourceCode: '# !!IMPORTANT!! By default, your code must finish in 2mins, longer for 2 mins will be killed\n# !!IMPORTANT!! `requests`, `socket` module are disabled\n\n# global variables can be used:\n# inputs: list[dict], for example: [{"text": "pre node text", "prev_node": "prev_node ID"}]\n# Except the system Python packages, the following PyPI Python packages can be used directly, `import` it if you need: https://alidocs.dingtalk.com/i/p/DQqWXwVQbDgz315415eGeB04pvJa2z3N\n# For example, if you need numpy, you can: `import numpy as np`\n\ninput_text = inputs[0][\'text\'] # get the first input\'s text value; you can use len(inputs) to check how many pre node this Python interpreter depends\n\ndef capitalize_txt(txt: str) -> str:\n    return txt.capitalize()\n\nfinal_result_text = capitalize_txt(input_text)\n\n# !!IMPORTANT: the final variable, is the Python code\'s return value, DON\'T write like this: return final_result_text, write as below:\n# !!IMPORTANT:  can be str, int, dict, list or any object you defines, will be encoded as json then forward to next node\nfinal_result_text\n',
    },
  },
  jsonExtractor: {
    nodeName: 'JSON字段提取',
    nodeType: 'common',
    type: 'jsonExtractor',
    icon: 'json-extractor',
    data: { jsonPath: '' },
  },
  inputTransform: {
    nodeName: '数据格式转换',
    nodeType: 'common',
    type: 'inputTransform',
    icon: 'input-transform',
    data: {
      inputTransform: {
        toListAsStr: false,
        toDictAsStr: true,
        sampleJson: { '': '' },
      },
    },
  },
  transformContent: {
    nodeName: '内容转换',
    nodeType: 'common',
    type: 'transformContent',
    icon: 'transform-content',
    data: {
      transformContentParams: {
        aiAssist: false,
        ruleGroupId: undefined,
        systemPrompt: '',
        ...BASE_LLM_SETTING,
      },
    },
  },
  webhook: {
    nodeName: 'Webhook',
    nodeType: 'common',
    type: 'webhook',
    icon: 'webhook',
    data: {
      llmSetting: {
        extraParams: {
          url: '',
          params: { '': '' },
          retryTimes: 1,
          skipWhenException: false,
        },
      },
    },
  },
  openapi: {
    nodeName: 'Postman',
    nodeType: 'common',
    type: 'openapi',
    icon: 'openapi',
    data: {
      llmSetting: {
        extraParams: {
          url: '',
          method: 'POST',
          headers: { '': '' },
          body: '',
          retryTimes: 1,
          skipWhenException: false,
        },
      },
    },
  },
  tts: {
    nodeName: 'TTS',
    nodeType: 'common',
    type: 'tts',
    icon: 'tts',
    data: {
      llmSetting: {
        extraParams: {
          text: '',
          voice: 'zhiyan_emo',
          audioFormat: 'mp3',
          volume: 50,
          speechRate: 0,
          pitchRate: 0,
          provider: 'aliyun',
        },
      },
    },
  },
  asr: {
    nodeName: 'ASR',
    nodeType: 'common',
    type: 'asr',
    icon: 'asr',
    data: { llmSetting: { extraParams: { audioUrl: '' } } },
  },
  videoDownloader: {
    nodeName: '视频下载',
    nodeType: 'common',
    type: 'videoDownloader',
    icon: 'video-downloader',
    data: { llmSetting: { extraParams: { videoUrl: '' } } },
  },
  video2audio: {
    nodeName: '视频转音频',
    nodeType: 'common',
    type: 'video2audio',
    icon: 'video2audio',
    data: { llmSetting: { extraParams: { videoUrl: '', audioFormat: 'mp3' } } },
  },
  excelProcessor: {
    nodeName: 'Excel处理',
    nodeType: 'common',
    type: 'excelProcessor',
    icon: 'excel-processor',
    data: {
      llmSetting: {
        extraParams: {
          fileUrl: '',
          enableRead: false,
          enableWrite: false,
          data: '',
          format: 'csv',
        },
      },
    },
  },
  removeTextAd: {
    nodeName: '文本去除广告内容',
    nodeType: 'common',
    type: 'removeTextAd',
    icon: 'remove-text-ad',
    data: { llmSetting: { content: '' } },
  },
  adTextEvaluate: {
    nodeName: '营销文稿判定',
    nodeType: 'common',
    type: 'adTextEvaluate',
    icon: 'ad-text-evaluate',
    data: {
      llmSetting: { systemPrompt: '你是一个内容编辑，负责审核文稿。\n\n你需要挑选出适合发布的文稿，你的标准是：\n1. 文稿不能包含任何形式的广告、推销或活动引导。这包括但不限于产品推广、品牌宣传、促销活动等\n2. 文稿需要是适合朗读的文章，语言流畅，句子结构清晰。其他类型的文稿，比如问答属于不适合发布的文稿\n3. 这篇文稿不能是公关稿，公关稿不适合发布，输出否\n4. 这篇文稿不能是商品的介绍，商品包括各种形式的商品，比如绘本、汽车等，商品介绍以及品牌宣传不适合发布，输出否\n5. 文中没有提到具体的商品、产品和项目，而仅是做分析和预测时，文稿可以发布，输出是，比如对某城市房地产政策的分析，但没有设计具体的楼盘和品牌，不做为广告\n6. 文中为具体的公司或者品牌说好话，视为公关稿，不适合发布\n\n注意：\n"记得点个关注和在看"，不属于活动引导，可以通过。\n', content: '' },
    },
  },
  videoOcrFix: {
    nodeName: '视频OCR修复',
    nodeType: 'common',
    type: 'videoOcrFix',
    icon: 'video-ocr-fix',
    data: {
      llmSetting: {
        extraParams: {
          videoUrl: '',
          searchEngine: '',
        },
      },
    },
  },
  articleMaterialExtract: {
    nodeName: '提取文章素材',
    nodeType: 'common',
    type: 'articleMaterialExtract',
    icon: 'article-material-extract',
    data: {
      llmSetting: {
        extraParams: {
          libraryId: 1,
          enableMergeMaterial: true,
          materialExtractLlmSetting: {
            model: 'gpt-3.5-turbo',
            prompt: '{{ material.content }}\n\n将上面文章整理成多段独立的素材内容。\n输出JSON格式的素材列表:\n```json\n[{"title": "素材的标题","content": "素材的具体内容"}]\n```\n注意: 确保输出的结果可以被python的json.loads()函数解析。\n\n素材包含时间、地点、人物、事件以及相关诗句等引用。去除无效内容，同时保留重要的细节。\n',
            temperature: 0.8,
            maxTokens: 2048,
          },
          materialMergeLlmSetting: {
            model: 'gpt-3.5-turbo',
            prompt: '{% for meterial in clustered_materials %}\n{{ loop.index }}: 标题：{{ meterial.title }}\n{{ meterial.content }}\n{% endfor %}\n\n上面是整理的素材，请统合上面的素材内容为一份新的素材。\n统和结果为单条， 将这一条统合结果\n按照JSON格式输出：\njson格式注意引号格式\n{"title": "新标题\'标题\'","content": "新内容\'内容\'"}\n\n注意: 确保输出的结果可以被python的json.loads()函数解析。\n\n具体要求：\n- 去除重复内容，合并类似内容，保留重要细节\n- 重新整理合适的顺序，修正可能的标点错误和诗句错误\n- 保留具体的时间、地点、人物、具体事件、数字、引用、诗词文字内容\n- 仅输出标题和内容，语言精简\n- 仅提取素材里的内容，不要添加内容\n- 结果只需要一条即可',
            temperature: 0.8,
            maxTokens: 2048,
          },
          extractThreshold: 0.065,
          materialLimit: 5,
        },
      },
    },
  },
  articleMaterialSearchAndMerge: {
    nodeName: '搜索整合文章素材',
    nodeType: 'common',
    type: 'articleMaterialSearchAndMerge',
    icon: 'article-material-search-and-merge',
    data: {
      llmSetting: {
        extraParams: {
          libraryId: 1,
          materialMergeLlmSetting: {
            model: 'gpt-3.5-turbo',
            prompt: '{% for meterial in clustered_materials %}\n{{ loop.index }}: 标题：{{ meterial.title }}\n{{ meterial.content }}\n{% endfor %}\n\n上面是整理的素材，请统合上面的素材内容为一份新的素材。\n统和结果为单条， 将这一条统合结果\n按照JSON格式输出：\njson格式注意引号格式\n{"title": "新标题\'标题\'","content": "新内容\'内容\'"}\n\n注意: 确保输出的结果可以被python的json.loads()函数解析。\n\n具体要求：\n- 去除重复内容，合并类似内容，保留重要细节\n- 重新整理合适的顺序，修正可能的标点错误和诗句错误\n- 保留具体的时间、地点、人物、具体事件、数字、引用、诗词文字内容\n- 仅输出标题和内容，语言精简\n- 仅提取素材里的内容，不要添加内容\n- 结果只需要一条即可',
            temperature: 0.8,
            maxTokens: 2048,
          },
          extractThreshold: 0.065,
          searchThreshold: 0.7,
          searchLimit: 5,
          materialLimit: 5,
          embeddingMethod: 'default',
        },
      },
    },
  },
  summaryOutline: {
    nodeName: '文章大纲提取',
    nodeType: 'common',
    type: 'summaryOutline',
    icon: 'summary-outline',
    data: {
      llmSetting: {
        extraParams: {
          summaryLlmSetting: {
            model: 'gpt-3.5-turbo',
            prompt: '短视频文稿：\n{{material.content}}\n\n提炼这篇短视频文稿的观点和写作大纲，大纲如果涉及到事件的发生时间，在前面标注清楚事件的发生时间，所有时间要用绝对时间\n\n格式如下：\n\n{\n"view": "提炼出的文章的观点",\n"outline": "1. XXX(2023-7-23发生)XXX\n2. XXX(2023-08-23发生)XXX"\n}\n\n用json的格式输出 确保输出可以被python json.loads函数解析',
            maxTokens: 2048,
            temperature: 0,
          },
          viewLibraryId: null,
          outlineLibraryId: null,
        },
      },
    },
  },
};

export const CUSTOM_NODE_DEMO = {
  group: {
    nodeName: '子模块Demo',
    type: 'group',
    icon: 'subflow',
    data: { width: 240, height: 240, name: '子模块Demo' },
    style: { backgroundColor: 'rgba(255, 0, 0, 0.2)', width: 240, height: 240 },
  },
};

export const CONTROL_NODES = {
  decision: {
    nodeName: '真假分支',
    type: 'decision',
    icon: 'conditional',
  },
  switchCase: {
    nodeName: '多路分支',
    nodeType: 'common',
    type: 'switchCase',
    icon: 'switch-case',
    data: {
      switchConditions: [{
        output: '',
        conditions: [{}],
      }],
    },
  },
  loopN: {
    nodeName: '循环控制',
    nodeType: 'common',
    type: 'loopN',
    icon: 'loop-n',
    data: {
      loopTimes: 1,
      conditions: [{}],
    },
  },
  iterator: {
    nodeName: '迭代控制',
    nodeType: 'common',
    type: 'iterator',
    icon: 'iterator',
    data: {},
  },
  textUserInput: {
    nodeName: '用户输入',
    nodeType: 'common',
    type: 'textUserInput',
    icon: 'text-user-input',
    data: {
      llmSetting: {
        content: '',
      },
    },
  },
  sleep: {
    nodeName: 'Sleep',
    nodeType: 'common',
    type: 'sleep',
    icon: 'sleep',
    data: { llmSetting: { extraParams: { duration: 0 } } },
  },
};

export const INFO_NODES = {
  executeAgent: {
    nodeName: '智能上下文',
    nodeType: 'knowledge',
    type: 'executeAgent',
    icon: 'execute-agent',
    data: {
      llmSetting: {
        compressPrompt: TOOL_PROMPT.compressPrompt,
        summarizePrompt: TOOL_PROMPT.summarizePrompt,
        executeAgentPrompt: TOOL_PROMPT.executeAgentPrompt,
        searchResultAnalysisPrompt: TOOL_PROMPT.searchResultAnalysisPrompt,
        extraParams: { maxReturnToken: 1500, maxAgentSteps: 4 },
        ...BASE_LLM_SETTING,
      },
    },
  },
  extractHtml: {
    nodeName: '网页信息提取',
    nodeType: 'knowledge',
    type: 'extractHtml',
    icon: 'extract-html',
    data: {
      llmSetting: {
        compressPrompt: TOOL_PROMPT.compressPrompt,
        ...BASE_LLM_SETTING,
      },
    },
  },
  extractPdf: {
    nodeName: 'PDF信息提取',
    nodeType: 'knowledge',
    type: 'extractPdf',
    icon: 'extract-pdf',
    data: {
      llmSetting: {
        compressPrompt: TOOL_PROMPT.compressPrompt,
        ...BASE_LLM_SETTING,
      },
    },
  },
  searchKb: {
    nodeName: '问答知识提取',
    nodeType: 'knowledge',
    type: 'searchKb',
    icon: 'search-kb',
    data: {
      llmSetting: {
        resultTpl: '{% for item in kb_result %}\n  {% if loop.index0 < 10 %}\n    content: {{ item.kb_content }}\n    metadata: {{ item.kb_metadata }}\n  {% endif %}\n{% endfor %}',
        extraParams: { metadata: { '': '' }, topK: 6 },
      },
    },
  },
  searchCiteKb: {
    nodeName: '引用知识提取',
    nodeType: 'knowledge',
    type: 'searchCiteKb',
    icon: 'search-cite-kb',
    data: {
      llmSetting: {
        resultTpl: '{% for item in kb_result %}\n  {% if loop.index0 < 10 %}\n    content: {{ item.kb_content }}\n    metadata: {{ item.kb_metadata }}\n  {% endif %}\n{% endfor %}',
        extraParams: { metadata: { '': '' }, topK: 6, singleCompressToken: 800 },
      },
    },
  },
  summaryViewpoint: {
    nodeName: '提炼用户反馈',
    nodeType: 'knowledge',
    type: 'summaryViewpoint',
    icon: 'summary-viewpoint',
    data: {
      llmSetting: {
        content: '',
        source: '',
        course_id: '',
        created_at: '',
        summaryPrompt: TOOL_PROMPT.summaryPrompt,
        extractPrompt: TOOL_PROMPT.extractPrompt,
        ...BASE_LLM_SETTING,
        temperature: 0,
        max_tokens: 2048,
        model_name: 'gpt-3.5-turbo-0613',
      },
    },
  },
  searchFeedback: {
    nodeName: '用户反馈搜索',
    nodeType: 'knowledge',
    type: 'searchFeedback',
    icon: 'search-feedback',
    data: {
      llmSetting: {
        model: 'gpt-4',
        maxTokens: 2048,
        extraParams: {
          topK: 10,
          inputTemplate: '',
          userId: '',
          userRole: '',
        },
      },
    },
  },
  searchWeb: {
    nodeName: '搜索相关信息',
    nodeType: 'knowledge',
    type: 'searchWeb',
    icon: 'search-web',
    data: {
      llmSetting: {
        compressPrompt: TOOL_PROMPT.compressPrompt,
        searchResultAnalysisPrompt: TOOL_PROMPT.searchResultAnalysisPrompt,
        enableCompress: true,
        searchEngine: 'tg',
        topK: 10,
        ...BASE_LLM_SETTING,
      },
    },
  },
  summarizePdf: {
    nodeName: 'PDF内容总结',
    nodeType: 'knowledge',
    type: 'summarizePdf',
    icon: 'summarize-pdf',
    data: {
      llmSetting: {
        summarizePrompt: TOOL_PROMPT.summarizePrompt,
        ...BASE_LLM_SETTING,
      },
    },
  },
  summarizeWeb: {
    nodeName: '网页内容总结',
    nodeType: 'knowledge',
    type: 'summarizeWeb',
    icon: 'summarize-web',
    data: {
      llmSetting: {
        summarizePrompt: TOOL_PROMPT.summarizePrompt,
        ...BASE_LLM_SETTING,
      },
    },
  },
  summarizeText: {
    nodeName: '文本内容总结',
    nodeType: 'knowledge',
    type: 'summarizeText',
    icon: 'summarize-text',
    data: {
      llmSetting: {
        summarizePrompt: TOOL_PROMPT.summarizePrompt,
        ...BASE_LLM_SETTING,
      },
    },
  },
  extractText: {
    nodeName: '文档文字加载',
    nodeType: 'common',
    type: 'extractText',
    icon: 'extract-text',
    data: {
      llmSetting: {
        chunkSetting: { enabled: false, chunkSize: 800, chunkOverlap: 0 },
        extraParams: { model: 'ali', action: 'basic' },
      },
    },
  },
  rssReader: {
    nodeName: 'RSS Reader',
    nodeType: 'common',
    type: 'rssReader',
    icon: 'rss-reader',
    data: { llmSetting: { extraParams: { rssUrl: '' } } },
  },
  gatherFromChat: {
    nodeName: '企微收集信息',
    nodeType: 'common',
    type: 'gatherFromChat',
    icon: 'gather-from-chat',
    data: {
      llmSetting: {
        responseFormat: 'json_object',
        systemPrompt: '目标:\n你的任务是从用户那里获取并高效地解析文本格式的用户信息,并根据提供的TypeScript接口模式将用户的数据提取为结构化的JSON格式。\n\n输入:文本格式的用户信息和JSON格式的收集信息。\n\n步骤:\n1. 分析输入: 根据收到的文本和JSON,确定哪些信息已经存在,哪些没有,哪些是用户不想提供的,确保不会重复提问。\n2. 转换为JSON: 将提取的信息映射到模式中的相应字段,创建结构化的JSON。\n3. 分析需要收集的所有信息: 根据收到的文本和JSON以及TypeScript接口中查找尚未提供的并且不是隐私的字段,并逐步引导用户收集信息。\n4. 优化输出: 确保JSON格式良好、无差错,并使用正确类型的空值处理缺失值。\n\n输出:\nYou should output in JSON like this:\n{"content": "your reply","collected": {},"uncollected": {},"private_info": []}\n\n用户输入的注意事项:\n1. 用户会给你一个JSON,其中包含这次提供的信息message, 已经收集过的信息collected, 未收集完全的信息uncollected,不想提供的隐私数据private_info和一些其他数据,如果你无法辨别这些信息,请要求用户说明,不要试图猜测!!。你输出的注意事项:1. 结合用户提供的文本和所有JSON以及TypeScript接口来引导用户,字段名和备注等信息在接口中会以typescript备注的形式告诉你,注意每次至多收集两个接口的信息且不要收集在private_info中或者已经收集完的字段,收集信息按照以下格式输出:\n```\ntext\n接口名称(备注): \n字段1(备注) \n字段2(备注) \n...\n```\n2. collected和uncollected都按照提供的TypeScript接口构建JSON。\n3. 数组字段在用户确认没有其他补充之前,也认为没有收集完毕,要记录在uncollected里面并且询问用户是否需要补充,用户确认没有补充后从uncollected中移除这些字段,必须移除！\n4. collected中不要输出为空值的项。\n5. 如果某些字段用户不希望告知,你需要加到private_info中,如果用户主动提供了在private_info中的字段,你就把这些字段从private_info中移除,然后正常收集即可!\n6. 出于节省输出tokens考虑,在content中你不需要整理用户输入,仅提醒用户需要提供的信息,确保信息收集完整。\n\n以下是你需要收集的信息的TypeScript接口:\n\n{{typescript_schema}}\n\n结果应仅包含有效的JSON,不包含任何分隔符或导致JSON格式无效的字符。Handling missing values with empty value',
        extraParams: {
          loadConfig: { demoKey: 'text' },
          gatherItems: [
            {
              title: '基本信息',
              key: 'basic_info',
              required: true,
              multi: false,
              fields: [{ title: '姓名', key: 'name', required: true, selections: [], dataType: 'string', comment: '' }],
            },
          ],
        },
        ...BASE_LLM_SETTING,
      },
    },
  },
};

export const BASE_NODES = {
  start: {
    nodeName: '开始',
    type: 'start',
    icon: 'start',
    data: {},
  },
  end: {
    nodeName: '结束',
    type: 'end',
    icon: 'end',
    data: {},
  },
};

export const DEMO_NODES = {
  demo: {
    nodeName: 'DEMO',
    nodeType: 'common',
    type: 'demo',
    icon: 'demo',
    data: {},
  },
};

export const MSG_NODES = {
  weworkKfSender: {
    nodeName: '企微客服',
    nodeType: 'common',
    type: 'weworkKfSender',
    icon: 'wework-kf-sender',
    data: {
      llmSetting: {
        extraParams: {
          openKfid: '',
          userId: '',
          body: '',
        },
      },
      transformContentParams: {
        aiAssist: false,
        ruleGroupId: undefined,
        systemPrompt: '',
        ...BASE_LLM_SETTING,
      },
    },
  },
  weworkRpaSender: {
    nodeName: '推企微',
    nodeType: 'common',
    type: 'weworkRpaSender',
    icon: 'wework-rpa-sender',
    data: {
      weworkRpaData: {
        text: '',
        rpaChatId: '',
        rpaMessageReceiver: '',
        rpaMessageSender: '',
        weworkRoomId: '',
      },
    },
  },
  dingtalkSender: {
    nodeName: '推钉群',
    nodeType: 'common',
    type: 'dingtalkSender',
    icon: 'dingtalk-sender',
    data: {
      dingtalkData: {
        text: '',
        conversationId: '',
      },
    },
  },
  whatsappSender: {
    nodeName: 'WhatsApp',
    nodeType: 'common',
    type: 'whatsappSender',
    icon: 'whatsapp-sender',
    data: {
      transformContentParams: {
        aiAssist: false,
        ruleGroupId: undefined,
        systemPrompt: '',
        ...BASE_LLM_SETTING,
      },
    },
  },
  mpArticlePublisher: {
    nodeName: '发公众号',
    nodeType: 'common',
    type: 'mpArticlePublisher',
    icon: 'mp-article-publisher',
    data: {
      llmSetting: {
        extraParams: {
          title: '',
          author: '',
          digest: '',
          content: '',
          contentSourceUrl: '',
          thumbMediaUrl: '',
          needOpenComment: false,
          onlyFansCanComment: false,
          mpAppId: '',
          mpUuid: '',
        },
      },
    },
  },
  xiaoyuzhouPublisher: {
    nodeName: '发小宇宙',
    nodeType: 'common',
    type: 'xiaoyuzhouPublisher',
    icon: 'xiaoyuzhou-publisher',
    data: {
      llmSetting: {
        extraParams: {
          title: '',
          description: '',
          audioUrl: '',
          coverUrl: '',
        },
      },
    },
  },
  publisher: {
    nodeName: '通用发布',
    nodeType: 'common',
    type: 'publisher',
    icon: 'publisher',
    data: {
      llmSetting: {
        accountId: '',
        characterId: '',
        content: {},
      },
    },
  },
};

export const AIGC_NODES = {
  text2image: {
    nodeName: '文生图',
    nodeType: 'common',
    type: 'text2image',
    icon: 'text2image',
    data: {
      llmSetting: {
        extraParams: {
          modelName: 'wanx-v1',
          prompt: '',
          width: 1024,
          height: 1024,
        },
      },
    },
  },
  text2video: {
    nodeName: '文生视频',
    nodeType: 'common',
    type: 'text2video',
    icon: 'text2video',
    data: {
      llmSetting: {
        extraParams: {
          title: '',
          content: '',
          accounts: undefined,
          template_id: undefined,
          article_library_id: undefined,
        },
      },
    },
  },
  image2video: {
    nodeName: '图生视频',
    nodeType: 'common',
    type: 'image2video',
    icon: 'image2video',
    data: { llmSetting: { extraParams: { imageUrl: '' } } },
  },
  imageStyled: {
    nodeName: '图风格化',
    nodeType: 'common',
    type: 'imageStyled',
    icon: 'image-styled',
    data: {
      llmSetting: {
        extraParams: {
          modelName: 'wanx-style-repaint-v1',
          imageUrl: '',
          imageStyle: 0,
        },
      },
    },
  },
};

export const FLOW_TYPES = {
  workflow: '工作流',
  subflow: '子模块',
};

export const PROMPT_KEYS = {
  nothing: ['searchKb'],
  compressPrompt: ['extractPdf', 'extractHtml', 'executeAgent', 'searchWeb', 'searchFeedback'],
  summarizePrompt: ['summarizeText', 'summarizePdf', 'summarizeWeb', 'executeAgent'],
  executeAgentPrompt: ['executeAgent'],
  searchResultAnalysisPrompt: ['executeAgent'],
  extractPrompt: ['summaryViewpoint'],
  summaryPrompt: ['summaryViewpoint'],
  singleCompressPrompt: ['searchCiteKb'],
};

export const PROMPT_KEY_NAME = {
  compressPrompt: '压缩提示词',
  summarizePrompt: '总结提示词',
  executeAgentPrompt: '上下文提示词',
  searchResultAnalysisPrompt: '结果分析提示词',
  extractPrompt: '提取提示词',
  summaryPrompt: '总结提示词',
  singleCompressPrompt: '单条知识压缩提示词',
};

export const COMMON_NODE_ENUM = {
  loopN: 'loopN',
  switchCase: 'switchCase',
  pythonEvaluator: 'pythonEvaluator',
  iterator: 'iterator',
  extractText: 'extractText',
  rssReader: 'rssReader',
  gatherFromChat: 'gatherFromChat',
  textUserInput: 'textUserInput',
  sleep: 'sleep',
  demo: 'demo',
  ...Object.assign({}, ..._.map({ ...TOOL_NODES, ...CONTROL_NODES, ...AIGC_NODES, ...MSG_NODES }, (v, k) => {
    if (v.nodeType === 'common') { return k; }
    return undefined;
  }).map((x) => { return { [x]: x }; })),
};
