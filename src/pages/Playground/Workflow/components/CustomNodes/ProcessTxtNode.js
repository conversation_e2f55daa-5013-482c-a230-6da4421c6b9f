import { DeleteOutlined, Pi<PERSON><PERSON>enterOutlined, QuestionCircleOutlined, SettingOutlined } from '@ant-design/icons';
import { Popconfirm, Popover } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

import { TOOL_NODES } from './Configs';
import nodeDesc from './desc';

export default class ProcessTxtNode extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    type: PropTypes.string,
    data: PropTypes.object,
  }

  render = () => {
    const { id, type, data } = this.props;
    const desc = nodeDesc[type];
    return (
      <div className="process-txt">
        <Popconfirm title="是否删除?!" onConfirm={() => { data.onDel(id); }} >
          <DeleteOutlined className="left-icon" />
        </Popconfirm>
        <SettingOutlined
          className="right-icon"
          onClick={() => { data.onOpenSetting(id); }}
        />
        <Popover
          title={desc.name}
          content={<div>{desc.content.map((x) => { return <>{x}<br /></>; })}</div>}
        >
          <QuestionCircleOutlined className="right-icon" style={{ bottom: 4, top: 'unset' }} />
        </Popover>
        <PicCenterOutlined style={{ color: '#fff', fontSize: 24, marginTop: 2 }} />
        <div className="text">{data?.name || TOOL_NODES[type]?.nodeName}</div>
        <Handle
          type="target"
          position={Position.Left}
          className="base-handle"
          style={{ left: -10, color: '#69f' }}
        />
        <Handle
          type="source"
          position={Position.Right}
          className="base-handle"
          style={{ right: -10 }}
        />
      </div>
    );
  }
}
