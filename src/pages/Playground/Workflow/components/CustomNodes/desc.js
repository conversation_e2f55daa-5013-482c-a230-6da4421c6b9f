/* eslint-disable max-len */
export default {
  assistant: {
    name: '角色模块',
    content: [
      '介绍：通过LLM Prompt构建一个AI角色, 根据文字和LLM互动',
      '节点输入：可为任意节点的任意输入文字，可在Prompt中引用输入的文字以及所有前置节点的输入。',
      '节点输出：默认LLM的输出为无格式文字，使用者可调整Prompt引导LLM输出json结构，输出的无格式文字或者json将是下个节点的输入。',
    ],
  },
  decision: {
    name: '真假分支',
    content: [
      '介绍：通过当前节点的输入是否满足条件，来控制工作流执行，满足条件则执行 “是” 的分支；不满足条件，则执行“否”的分支。',
    ],
  },
  switchCase: {
    name: '多路分支',
    content: [
      '介绍：通过当前节点的输入满足配置的哪个条件，来执行对应的后续工作流。可以配置1~10个条件，不同的条件执行不同的后续工作流。当所有条件都不满足时，则执行默认分支。',
      '节点输入：可为任意文字。',
    ],
  },
  loopN: {
    name: '循环控制',
    content: [
      '介绍：通过配置循环次数和终止条件，来控制后置节点执行的次数，完成对**后置节点**的循环。该节点和后置节点循环完毕，循环中间数据会被保存，后置节点以及后置所有节点可引用是用。',
      '节点输入：可为任意文字。',
      '节点输出：依赖后置节点的输出以及后置节点的循环次数。示例：后置被循环节点，每次循环输出当前循环次数: {"count": 1}，被循环3次，则循环完毕，该节点的输出为json str：`[{"count": 1}, {"count": 2}, {"count": 31}]`',
    ],
  },
  iterator: {
    name: '迭代控制',
    content: [
      '介绍：迭代控制节点的输入必须是数组，例如 [1, 2, 3]，迭代控制将每次从这个数据中读取一条内容，将该内容输出给后置节点去执行，一次循环直至数组最后一条数据。该节点和后置节点循环完毕，循环中间数据会被保存，后置节点以及后置所有节点可引用是用。',
      '节点输入：必须为数组的string格式，例如字符串表示的数组： "[1, 2, 4]"',
      '节点输出：依赖后置节点的输出以及后置节点的循环次数。示例：后置被循环节点，每次循环输出当前循环次数: {"count": 1}，被循环3次，则循环完毕，该节点的输出为json str：`[{"count": 1}, {"count": 2}, {"count": 31}]`',
    ],
  },
  executeAgent: {
    name: '智能上下文',
    content: [
      '介绍：根据该节点的输入，进行 知识库信息检索、搜索引擎数据检索以及文件(如果输入的文字中含有URL、文档链接等)相关知识检索。',
      '输入：仅支持纯文字，如果前置节点输出为json，则需要配合使用 **JSON字段提取** 节点从json里提取需要是用的文字字段。',
      '输出：纯文字，后置节点可引用该文字。',
    ],
  },
  extractHtml: {
    name: '网页信息提取',
    content: [
      '介绍：从指定的网页里，提取相关信息。',
      '输入：一路输入为URL，一路输入为文字。如果只有文字的输入，则从文字里提取URL后，进行相关信息的提取。例如：输入："文章 https://toutiao.com/llm 讲了什么是LLM吗？" 则，执行从链接中的正文里提取和问题相关的答案。',
      '输出：纯文字，即提取的相关信息。',
    ],
  },
  extractPdf: {
    name: 'PDF信息提取',
    content: [
      '介绍：从指定的PDF里，提取相关信息。',
      '输入：一路输入为PDF文档URL，一路输入为文字。如果只有文字的输入，则从文字里提取PDF URL后，进行相关信息的提取。例如：输入："文章 https://toutiao.com/llm.pdf 讲了什么是LLM吗？" 则，执行从链接中的正文里提取和问题相关的答案。',
      '输出：纯文字，即提取的相关信息。',
    ],
  },
  searchKb: {
    name: '知识提取',
    content: [
      '介绍：从指定的知识库里，检索和输入相关的信息，并输出信息文字。',
      '输入：检索的关键文字或者问题文字。',
      '输出：纯文字，即检索到的相关信息。根据输入，进行向量检索，返回节点配置的条数的信息。',
    ],
  },
  summaryViewpoint: {
    name: '提炼用户反馈',
    content: [
      '介绍：从输入的文章或者文字里，提取 人物 和 人物观点，并自动将 人物 和 人物观点入库保存。',
      '输入：长文字',
      '输出：总结的人物、人物描述以及人物观点。输出为数组，转string后的结果。示例：`{"name": "查尔斯·埃文斯", "profile": "美国教授", "viewpoints": [{"topic":"话题", "opinion": "话题观点"}]}`',
    ],
  },
  searchFeedback: {
    name: '用户反馈搜索',
    content: [
      '介绍：根据 "搜索文本" 进行用户观点的搜索，并返回满足条件的用户观点文字。返回的条数由"TopK"和"MaxTokens"控制，满足其一。',
      '输入：可以是任意节点的输出，检索的条件配置在节点设置里。必填信息为：搜索文本。',
      '输出：用户的观点文字。如果检索到多个人的观点，则合并返回。举例：',
      '',
      '<1> [引用内容] 物理学',
      '[用户观点] 伽利略是物理学家，被称为「现代科学之父」。',
      '[发布人] 牛顿',
      '[发布时间] 2023-07-25 19:20',
    ],
  },
  searchWeb: {
    name: '搜索相关信息',
    content: [
      '介绍：根据输入的文本，进行搜索引擎的检索，并返回检索到的文本。',
      '输入：前置节点的输出，必须是文字，例如：大模型的发展前景怎么样？',
      '输出：搜索引擎的检索结果的汇总，搜集到信息后，会通过LLM Prompt进行整理成简短文字输出。',
    ],
  },
  summarizePdf: {
    name: 'PDF内容总结',
    content: [
      '介绍：对给定的PDF文档进行核心要点总结。',
      '输入：PDF链接，或者带有PDF链接的文字，会自动从文字里提取PDF的链接。',
      '输出：总结好的PDF核心要点文字。用json string输出。示例：`{"content": "总结好的PDF核心要点文字", "title": "PDF的标题", "origin_content": "PDF原文"}`',
    ],
  },
  summarizeWeb: {
    name: '网页内容总结',
    content: [
      '介绍：对给定的URL进行核心要点的总结。',
      '输入：URL链接，或者带有URL链接的文字，会自动从文字里提取URL的链接。',
      '输出：总结好的核心要点文字。用json string输出。示例：`{"content": "总结好的核心要点文字", "title": "网页的标题", "origin_content": "网页带HTML的源代码"}`',
    ],
  },
  summarizeText: {
    name: '文本内容总结',
    content: [
      '介绍：对给定的长文字，进行核心要点的总结。',
      '输入：长文字',
      '输出：长文字的核心要点文字。用json string输出。示例：`{"content": "长文字的核心要点文字", "title": "原文的标题", "origin_content": "原文"}`',
    ],
  },
  extractText: {
    name: '文档文字加载',
    content: [
      '介绍：从给定的文档里，提取文档里的文字，支持Office docs、 图片、PDF和URL等。',
      '输入：文档的下载URL',
      '输出：文档里的文字信息，用json string输出。示例：`{"title":"标题，如果有的话", "text":"文档正文", "published_time": "2023-03-24 12:12"}`',

    ],
  },
  rssReader: {
    name: 'RSS Reader',
    content: [
      '介绍：从给定的RSS源里，解析rss items，并用json的格式输出。',
      '输入：可订阅的RSS URL',
      '输出：json string。示例：`[{"title": "title", "description": "描述", "link": "http url","pub_date": "rss pubDate"}]`',
    ],
  },
  processTxt: {
    name: '文本拼接',
    content: [
      '介绍：将多个输入的结果，用换行符拼接在一起后输出。',
      '输入：多个节点的文字输入',
      '输出：用换行符将输入拼接在一起后输出。',
    ],
  },
  saveData: {
    name: '数据保存',
    content: ['介绍：暂时不对外开放，是用请联系产品。'],
  },
  saveVector: {
    name: '知识保存',
    content: [
      '介绍：可将工作流中产生的数据，保存至指定的知识库，用于后续的知识搜索使用。',
      '输入：可以是任意节点的输出，保存的配置参见节点配置。',
      '输出：保存完成功后，将该节点的输入直接输出。',
    ],
  },
  saveFeedback: {
    name: '用户观点保存',
    content: [
      '介绍：可将工作流中产生的用户观点，保存至观点向量数据库，用于后续的用户观点检索使用。',
      '输入：可以是任意节点的输出，保存的配置参见节点配置。',
      '输出：保存完成功后，将该节点的输入直接输出。',
    ],
  },
  jsonExtractor: {
    name: 'JSON字段提取',
    content: [
      '介绍：将输入的string，转成json结构，并根据提取配置，提取json里的指定字段。使用说明参见：https://jmespath.org/',
      '输入：可以被encode成json结构的string',
      '输出：根据 "JSON路径" 配置的提取规则，返回相应的json字段，可能是string, int, object或者数组等。注意，如果是数组也是转成string后输出。',
    ],
  },
  inputTransform: {
    name: '数据格式转换',
    content: [
      '介绍：将多个输入，或者前置节点的数据，转换成List数组或者Map dict格式后输出。',
      '输入：可以是任意节点的输出，转换配置参见节点配置。',
      '输出：数组或者字典格式的string表示。示例：`["str1", "str2"]` or `{"k1":"v2"}`',
    ],
  },
  tts: {
    name: 'TTS',
    content: [
      '介绍：将录入或者工作流节点生成的文字，转成音频。',
      '输入：可以是任意节点的输出，转换配置参见节点配置。',
      '输出：音频的URL地址。',
    ],
  },
  asr: {
    name: 'ASR',
    content: [
      '介绍：从输入的音频或者视频URL里，提取声音的文字。支持 mp3|mp4地址，也支持Youtube的观看地址链接。',
      '输入：音视频的URL地址。',
      '输出：从音视频里提取的文字。',
    ],
  },
  videoDownloader: {
    name: '视频下载',
    content: [
      '介绍：从指定的URL里解析下载视频，支持：B站、抖音、YouTube等。',
      '输入：视频播放页地址。',
      '输出：视频的可下载地址。',
    ],
  },
  video2audio: {
    name: '视频转音频',
    content: [
      '介绍：mp4 转 mp3，返回mp3的地址。',
      '输入：mp4 的URL',
      '输出：mp3 的下载URL',
    ],
  },
  text2video: {
    name: '文本转视频',
    content: ['介绍：暂时不对外开放，是用请联系产品。'],
  },
  weworkRpaSender: {
    name: '推企微',
    content: ['介绍：暂时不对外开放，是用请联系产品。'],
  },
  dingtalkSender: {
    name: '推钉群',
    content: ['介绍：暂时不对外开放，是用请联系产品。'],
  },
  mpArticlePublisher: {
    name: '发布公众号',
    content: ['介绍：暂时不对外开放，是用请联系产品。'],
  },
  xiaoyuzhouPublisher: {
    name: '发布小宇宙',
    content: ['介绍：暂时不对外开放，是用请联系产品。'],
  },
  textUserInput: {
    name: '用户输入',
    content: ['介绍：工作流执行到该节点，会询问用户输入指定的内容，例如：请问您的生日是几月？'],
  },
};
