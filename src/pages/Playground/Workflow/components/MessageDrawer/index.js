/* eslint-disable func-names */
/* eslint-disable no-case-declarations */
import '@chatscope/chat-ui-kit-styles/dist/default/styles.min.css';

import './index.less';

import {
  CloseCircleOutlined,
  CopyOutlined,
  DownCircleOutlined,
  FilePdfOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import {
  ArrowButton,
  Avatar,
  ChatContainer,
  ConversationHeader,
  Message,
  MessageInput,
  MessageList,
  MessageSeparator,
} from '@chatscope/chat-ui-kit-react';
import { Toast } from '~/components';
import { AliyunHelper } from '~/engine';
import { Al<PERSON>, Avatar as AntdAvatar, <PERSON><PERSON>, But<PERSON>, Drawer, Form, Input, Radio, Spin } from 'antd';
import Upload from 'antd/lib/upload/Upload';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class MessageDrawer extends PureComponent {
  static propTypes = {
    isJob: PropTypes.bool,
    jobId: PropTypes.bool,
    flow: PropTypes.object,
    params: PropTypes.object,
    historyMode: PropTypes.object,
    logs: PropTypes.array,
    messages: PropTypes.array,
    open: PropTypes.bool,
    onChange: PropTypes.func,
    onSend: PropTypes.func,
    onCancel: PropTypes.func,
    onClose: PropTypes.func,
  }

  static defaultProps = {
    isJob: false,
    params: {},
    logs: [],
    messages: [],
    onChange: () => { },
  }


  state = {
    messages: [],
    logs: [],
    params: [],
    historyMode: 'autofit',
    pdfUrl: '',
  }

  componentDidMount = () => {
    const { logs, messages, params, historyMode } = this.props;
    const obj = Object.keys(params).map((key) => { return { key, value: params[key] }; });
    this.setState({
      params: _.isEmpty(obj) ? [] : obj,
      historyMode: historyMode || 'autofit',
      logs,
      messages,
    }, () => {
      setTimeout(() => {
        this.onScroll('msg-list', 'down');
        this.onScroll('log-list', 'down');
      }, 300);
    });
  }

  componentWillReceiveProps = ({ logs, messages }) => {
    this.setState({ logs, messages });
  }

  onScroll = (domId, direction) => {
    const dom = _.head(document.getElementById(domId).children);
    dom.scrollTop = direction === 'up' ? 0 : dom.scrollHeight;
  }

  onChangeValue = (e, key, i) => {
    const value = e?.target ? e.target.value : e;
    const params = _.cloneDeep(this.state.params);
    params[i][key] = value;
    this.setState({ params });
  }

  onChangeHistoryMode = (e) => {
    const { value } = e?.target;
    this.setState({ historyMode: value });
    this.props.onChange(value, 'historyMode');
  }

  onAdd = (index = 0) => {
    const params = _.cloneDeep(this.state.params) || [];
    params.splice(index + 1, 0, {});
    this.setState({ params });
  }

  onDel = (index) => {
    const params = _.cloneDeep(this.state.params) || [];
    params.splice(index, 1);
    this.setState({ params });
  }

  onClose = async () => {
    const params = {};
    this.state.params.forEach(({ key, value }) => {
      if (!_.isUndefined(key)) { params[key] = value || ''; }
    });
    await this.props.onChange(params);
    this.props.onClose();
  }

  onUpload = () => {
    const vm = this;
    const input = document.createElement('input');
    input.type = 'file';
    input.style.display = 'none';
    input.click();
    input.addEventListener('change', async function () {
      const file = this.files[0];
      const url = await AliyunHelper.clipsUploadImage(file);
      vm.setState({ pdfUrl: url });
    });
  }

  onSend = (txt) => {
    let value = txt;
    if (!_.isEmpty(this.state.pdfUrl)) {
      value += `\n${this.state.pdfUrl}`;
    }
    this.props.onSend(value);
    this.setState({ pdfUrl: '' });
  }

  renderMessages = () => {
    return (
      <MessageList id="msg-list">
        {
          this.state.messages.map((x) => {
            let msg = x.message;
            try {
              const msgObj = JSON.parse(msg);
              delete msgObj.raw_content;
              msg = JSON.stringify(msgObj);
            } catch (error) {
              // nothing
            }
            return (
              <Message model={{ message: msg, direction: !x.isClient ? 'outgoing' : '' }}>
                {x.isClient && <Avatar src="/static/ai-avatar.jpg" />}
                <Message.Header>
                  <div style={{ width: '100%', position: 'relative' }}>
                    {x.createdAt}
                    {
                      x.isClient &&
                      <CopyOutlined
                        style={{ float: 'right' }}
                        onClick={
                          async () => {
                            await navigator.clipboard.writeText(msg);
                            Toast.show('复制成功', Toast.Type.SUCCESS);
                          }}
                      />
                    }
                  </div>
                </Message.Header>
              </Message>
            );
          })
        }
      </MessageList>
    );
  }

  renderAttachButton = () => {
    return (
      <Upload>
        <Button icon={<UploadOutlined />} />
      </Upload>
    );
  }

  render = () => {
    const { isJob, open, flow, jobId } = this.props;

    return (
      <Drawer
        open={open}
        title="运行"
        placement="right"
        className="message-drawer"
        contentWrapperStyle={{ width: '60vw', transform: 'translateX(0px) !important' }}
        onClose={this.onClose}
      >
        <div className="chat-main-container">
          {
            !isJob &&
            <>
              <Drawer
                title="常量设置"
                placement="top"
                closable={false}
                onClose={() => { return this.setState({ open: false }); }}
                open={this.state.open}
                getContainer={false}
              >
                <Form labelCol={{ span: 4 }}>
                  <Form.Item label="运行配置" >
                    <Radio.Group
                      value={this.state.historyMode}
                      onChange={(e) => { return this.onChangeHistoryMode(e); }}
                    >
                      <Radio value="none">忽略节点历史消息</Radio>
                      <Radio value="autofit">启用并自动压缩节点消息</Radio>
                      <Radio value="all">启用全部节点历史消息</Radio>
                    </Radio.Group>
                  </Form.Item>
                  {
                    (this.state.params || [{}]).map((obj, idx) => {
                      return (
                        <Form.Item label={obj.key}>
                          <Input.TextArea
                            value={obj.value}
                            placeholder="请输入变量的值"
                            autoSize={{ minRows: 4, maxRows: 6 }}
                            onChange={(e) => { return this.onChangeValue(e, 'value', idx); }}
                          />
                        </Form.Item>
                      );
                    })
                  }
                </Form>
              </Drawer>
              <Alert
                message="常量设置"
                action={<Button icon={<DownCircleOutlined />} size="small" type="primary">设置</Button>}
                onClick={() => { return this.setState({ open: true }); }}
              />
            </>
          }

          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div className="chat-wrap" style={{ position: 'relative' }}>
              {
                !_.isEmpty(this.state.pdfUrl) &&
                <Badge
                  className="pdf-icon"
                  count={
                    <CloseCircleOutlined
                      style={{ color: '#f5222d' }}
                      onClick={() => { return this.setState({ pdfUrl: '' }); }}
                    />
                  }
                >
                  <AntdAvatar shape="square" size="large" icon={<FilePdfOutlined style={{ fontSize: 30 }} />} />
                </Badge>
              }
              <ChatContainer>
                <ConversationHeader>
                  <ConversationHeader.Content userName={flow?.name} />
                  <ConversationHeader.Actions>
                    <ArrowButton
                      direction="up"
                      style={{ marginRight: 30 }}
                      onClick={() => { return this.onScroll('msg-list', 'up'); }}
                    />
                    <ArrowButton direction="down" onClick={() => { return this.onScroll('msg-list', 'down'); }} />
                  </ConversationHeader.Actions>
                </ConversationHeader>
                {this.renderMessages()}
                {
                  !isJob &&
                  <MessageInput
                    attachButton
                    attachDisabled={!_.isEmpty(this.state.pdfUrl)}
                    onAttachClick={() => { return this.onUpload(); }}
                    onSend={(html, txt) => { return this.onSend(txt); }}
                    placeholder="请输入.."
                  />
                }
              </ChatContainer>
            </div>
            <div className="log-wrap" style={{ position: 'relative' }}>
              <ChatContainer>
                <ConversationHeader>
                  <ConversationHeader.Content userName="执行记录" />
                  <ConversationHeader.Actions>
                    <ArrowButton
                      direction="up"
                      style={{ marginRight: 30 }}
                      onClick={() => { return this.onScroll('log-list', 'up'); }}
                    />
                    <ArrowButton direction="down" onClick={() => { return this.onScroll('log-list', 'down'); }} />
                  </ConversationHeader.Actions>
                </ConversationHeader>
                <MessageList id="log-list">
                  {
                    this.state.logs.map((x) => {
                      return (
                        <>
                          <MessageSeparator>
                            <span style={{ fontSize: 18, fontWeight: 'bold' }}>{x.nodeId}</span>
                          </MessageSeparator>
                          <Message model={{ message: x.message }}>
                            <Message.Header>
                              <div style={{ width: '100%', position: 'relative' }}>
                                <CopyOutlined
                                  style={{ float: 'right' }}
                                  onClick={
                                    async () => {
                                      const dom = document.createElement('div');
                                      dom.innerHTML = x.message;
                                      await navigator.clipboard.writeText(dom.innerText);
                                      Toast.show('复制成功', Toast.Type.SUCCESS);
                                    }}
                                />
                              </div>
                            </Message.Header>
                            <Message.Footer sentTime={x.createdAt} />
                          </Message>
                        </>
                      );
                    })
                  }
                </MessageList>
              </ChatContainer>
              {
                !_.isUndefined(jobId) &&
                <div style={{ position: 'absolute', bottom: 0, left: 0, right: 0 }}>
                  <Button
                    type="link"
                    style={{ position: 'absolute', bottom: 0 }}
                    onClick={() => { return this.props.onCancel(flow.uuid, jobId); }}
                  >
                    取消
                  </Button>
                  <Spin
                    spinning={1}
                    tip={<span style={{ marginLeft: 30, color: '#1890ff' }}>Loading...</span>}
                    style={{ display: 'flex', width: '50%', justifyContent: 'end' }}
                  />
                </div>
              }
            </div>
          </div>
        </div>
      </Drawer>
    );
  }
}
