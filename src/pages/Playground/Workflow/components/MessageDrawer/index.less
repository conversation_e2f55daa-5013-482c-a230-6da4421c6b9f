.message-drawer {
  .chat-main-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }

  .ant-drawer-body {
    padding-top: 0;
  }

  .cs-conversation-header {
    text-align: center;
  }

  .ant-tabs-content,
  .ant-tabs-tabpane {
    height: 100%;
  }

  .setting-item {
    display: flex;
    margin-bottom: 2px;

    .btn-opt {
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 160px;
    }
  }

  .chat-wrap,
  .log-wrap {
    display: inline-block;
    width: calc(50% - 30px);
    height: calc(100vh - 130px);
    border: 1px solid #d1dbe3;

    .pdf-icon {
      position: absolute;
      bottom: 8px;
      left: 2px;
      width: 44px;
      height: 36px;
      z-index: 10;
    }
  }

  .chat-wrap {
    .cs-message--outgoing .cs-message__sent-time {
      display: unset !important;
    }

    .cs-message__html-content {
      display: flex;
      flex-direction: column;

      .language-json {
        white-space: pre-wrap;
      }

      pre:last-child,
      p:last-child {
        margin-bottom: 0;
      }
    }

    .cs-message__content-wrapper {
      width: 100%;

      img {
        max-width: 100%;
      }
    }
  }

  .log-wrap {
    width: 50%;

    .cs-message {
      max-width: 100%;
      margin-bottom: 16px;
    }

    .cs-message__header {
      .cs-message__sender-name {
        font-size: 16px;
        color: #000;
      }
    }

    .cs-message__content-wrapper {
      width: 100%;
    }

    .cs-message__content {
      padding: 0;
      background-color: #fff;
    }
  }
}
