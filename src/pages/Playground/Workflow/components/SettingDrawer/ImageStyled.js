import { Form, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ToolEditor from '../ToolEditor';

const PROVIDER = {
  'wanx-style-repaint-v1': '阿里万象',
};

const PROVIDER_STYLE_ENUM = {
  'wanx-style-repaint-v1': {
    0: '复古漫画',
    1: '3D童话',
    2: '二次元',
    3: '小清新',
    4: '未来科技',
    5: '3D写实',
  },
};

export default class ImageStyled extends PureComponent {
  static propTypes = {
    params: PropTypes.object,
    data: PropTypes.object,
    onChange: PropTypes.func,
  }

  state = {
  }

  render = () => {
    const { data, params } = this.props;
    const { extraParams } = data?.llmSetting;
    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label="模型">
          <Select
            value={extraParams?.modelName}
            onChange={(e) => { return this.props.onChange(e, 'modelName', data); }}
          >
            {_.map(PROVIDER, (label, value) => {
              return (
                <Select.Option key={value} value={value}>{label}</Select.Option>
              );
            })}
          </Select>
        </Form.Item>
        <Form.Item label="图片">
          <ToolEditor
            showUpload
            key="data-imageUrl"
            editorId="data-imageUrl"
            value={extraParams?.imageUrl}
            datas={[]}
            controls={data?.controls || []}
            params={params}
            onChange={(e) => { return this.props.onChange(e, 'imageUrl', data); }}
          />
        </Form.Item>

        <Form.Item label="风格">
          <Select
            value={extraParams?.imageStyle}
            onChange={(e) => { return this.props.onChange(e, 'imageStyle', data); }}
          >
            {
              _.map(PROVIDER_STYLE_ENUM[extraParams?.modelName], (label, value) => {
                return (
                  <Select.Option key={value} value={_.toSafeInteger(value)}>{label}</Select.Option>
                );
              })
            }
          </Select>
        </Form.Item>
      </Form>
    );
  }
}
