import { ChatBot } from '~/engine';
import { Form, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ToolEditor from '../ToolEditor';

const EDITOR_KEYS = {
  title: '标题',
  description: '描述',
  audioUrl: '音频',
  coverUrl: '封面',
};

export default class XiaoyuzhouPublisher extends PureComponent {
  static propTypes = {
    params: PropTypes.object,
    data: PropTypes.object,
    onChange: PropTypes.func,
  }


  state = {
    apps: [],
  }

  componentDidMount = async () => {
    const items = await ChatBot.fetchOpenApps({ openApps: 'xiaoyuzhou' });
    const apps = _.map(items, (v) => { return v; });
    this.setState({ apps });
  }

  render = () => {
    const { data, params } = this.props;
    const { extraParams } = data?.llmSetting;

    return (
      <>
        <Form.Item label="账号">
          <Select
            value={extraParams.openAppId}
            options={this.state.apps.map((item) => { return { label: item.name, value: item.id }; })}
            onChange={(e) => { return this.props.onChange(e, 'openAppId', data); }}
          />
        </Form.Item>
        {
          _.map(EDITOR_KEYS, (v, key) => {
            return (
              <Form.Item label={v}>
                <ToolEditor
                  key={key}
                  editorId={key}
                  value={extraParams[key]}
                  datas={[]}
                  controls={data?.controls || []}
                  params={params}
                  onChange={(e) => { return this.props.onChange(e, key, data); }}
                />
              </Form.Item>
            );
          })
        }
      </>
    );
  }
}
