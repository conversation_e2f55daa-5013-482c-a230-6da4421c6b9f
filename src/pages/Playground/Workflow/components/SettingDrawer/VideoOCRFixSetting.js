import { Form, Select } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ToolEditor from '../ToolEditor';

export default class VideoOCRFixSetting extends PureComponent {
  static propTypes = {
    params: PropTypes.object,
    data: PropTypes.object,
    onChange: PropTypes.func,
  }

  state = {
  }

  render = () => {
    const { data, params } = this.props;
    const { videoUrl, searchEngine } = data?.llmSetting.extraParams;

    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label="链接">
          <ToolEditor
            key="videoUrl"
            editorId="prompt"
            value={videoUrl}
            datas={[]}
            controls={data?.controls || []}
            params={params}
            onChange={(e) => { return this.props.onChange(e, 'videoUrl', data); }}
          />
        </Form.Item>
        <Form.Item label="搜索引擎">
          <Select
            value={searchEngine}
            onChange={(e) => { return this.props.onChange(e, 'searchEngine', data); }}
          >
            <Select.Option value="tg">天宫搜索</Select.Option>
            <Select.Option value="serper">Google</Select.Option>
          </Select>
        </Form.Item>
      </Form>
    );
  }
}
