import { DeleteFilled, PlusCircleFilled } from '@ant-design/icons';
import { ObjectExtension } from '~/plugins';
import { Button, Divider, Form, Input, InputNumber, Radio, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import { OPENAI_PARAMS, OPENAI_PARAMS_MAX_VALUE, OPENAI_PARAMS_STEP } from '../../../Configs';
import ModelSelect from '../ModelSelect';
import ToolEditor from '../ToolEditor';

export default class GatherFromChatSetting extends PureComponent {
  static propTypes = {
    type: PropTypes.string,
    params: PropTypes.object,
    data: PropTypes.object,
    onChange: PropTypes.func,
  }

  state = {
  }

  onChangeValue = (e, key) => {
    const llmSetting = _.cloneDeep(this.props.data?.llmSetting) || {};
    const value = e?.target ? e.target.value : e;
    this.props.onChange({ ...llmSetting, [key]: value });
  }

  onChangeExtraParams = (e, key) => {
    const extraParams = _.cloneDeep(this.props.data?.llmSetting?.extraParams) || {};
    const value = e?.target ? e.target.value : e;
    this.props.onChange({ ...this.props.data?.llmSetting, extraParams: { ...extraParams, [key]: value } });
  }

  onChangeGatherItemValue = (e, idx, key) => {
    const gatherItems = _.cloneDeep(this.props.data?.llmSetting?.extraParams?.gatherItems) || [];
    const value = e?.target ? e.target.value : e;
    gatherItems[idx][key] = value;
    this.props.onChange({
      ...this.props.data?.llmSetting,
      extraParams: { ...this.props.data?.llmSetting?.extraParams, gatherItems },
    });
  }

  onChangeGatherFieldValue = (e, key, gIdx, idx) => {
    const gatherItems = _.cloneDeep(this.props.data?.llmSetting?.extraParams?.gatherItems) || [];
    const value = e?.target ? e.target.value : e;
    gatherItems[gIdx].fields[idx][key] = value;
    this.props.onChange({
      ...this.props.data?.llmSetting,
      extraParams: { ...this.props.data?.llmSetting?.extraParams, gatherItems },
    });
  }

  onAddGatherItem = () => {
    const gatherItems = _.cloneDeep(this.props.data?.llmSetting?.extraParams?.gatherItems) || [];
    gatherItems.push({
      title: '',
      key: '',
      required: true,
      multi: false,
      fields: [{
        title: '', key: '', dataType: 'string', selections: [], required: true, comment: '',
      }],
    });
    this.props.onChange({
      ...this.props.data?.llmSetting,
      extraParams: { ...this.props.data?.llmSetting?.extraParams, gatherItems },
    });
  }

  onAddField = (idx) => {
    const gatherItems = _.cloneDeep(this.props.data?.llmSetting?.extraParams?.gatherItems) || [];
    gatherItems[idx].fields.push({
      title: '', key: '', dataType: 'string', selections: [], required: true, comment: '',
    });
    this.props.onChange({
      ...this.props.data?.llmSetting,
      extraParams: { ...this.props.data?.llmSetting?.extraParams, gatherItems },
    });
  }

  onAddLoadConfig = (idx) => {
    const { extraParams } = this.props.data?.llmSetting;
    const loadConfig = _.cloneDeep(extraParams?.loadConfig) || {};
    const newLoadConfig = ObjectExtension.objToArray(loadConfig);
    newLoadConfig.splice(idx + 1, 0, { key: '', value: '' });
    this.props.onChange({
      ...this.props.data?.llmSetting,
      extraParams: {
        ...extraParams,
        loadConfig: newLoadConfig.reduce((acc, x) => { return { ...acc, [x.key]: x.value }; }, {}),
      },
    });
  }

  onDelLoadConfig = (idx) => {
    const { extraParams } = this.props.data?.llmSetting;
    const loadConfig = _.cloneDeep(extraParams?.loadConfig) || {};
    const newLoadConfig = ObjectExtension.objToArray(loadConfig);
    if (newLoadConfig.length === 1) return;

    newLoadConfig.splice(idx, 1);
    this.props.onChange({
      ...this.props.data?.llmSetting,
      extraParams: {
        ...extraParams,
        loadConfig: newLoadConfig.reduce((acc, x) => { return { ...acc, [x.key]: x.value }; }, {}),
      },
    });
  }

  onChangeLoadConfig = (e, key, idx) => {
    const value = e?.target ? e.target.value : e;
    const { extraParams } = this.props.data?.llmSetting;
    const loadConfig = _.cloneDeep(extraParams?.loadConfig) || {};
    const newLoadConfig = ObjectExtension.objToArray(loadConfig);
    newLoadConfig[idx][key] = value;
    this.props.onChange({
      ...this.props.data?.llmSetting,
      extraParams: {
        ...extraParams,
        loadConfig: newLoadConfig.reduce((acc, x) => { return { ...acc, [x.key]: x.value }; }, {}),
      },
    });
  }

  renderOpenAIParams = (data = {}, onChange = () => { }) => {
    return (
      <>
        {
          _.map(OPENAI_PARAMS, (v, k) => {
            const numProps = OPENAI_PARAMS_MAX_VALUE[k] > 1 ?
              { min: 0, max: OPENAI_PARAMS_MAX_VALUE[k], step: OPENAI_PARAMS_STEP[k], value: data[k] } :
              { min: 0, max: 1, step: 0.1, value: data[k] };

            return (
              <Form.Item label={_.upperFirst(k)}>
                <InputNumber
                  {...numProps}
                  onChange={(e) => { return onChange(e, k); }}
                />
              </Form.Item>
            );
          })
        }
        <Form.Item label="响应类型">
          <Select
            value={data?.responseFormat}
            onChange={(e) => { return onChange(e, 'responseFormat'); }}
          >
            <Select.Option value="text">文本</Select.Option>
            <Select.Option value="json_object">JSON</Select.Option>
          </Select>
        </Form.Item>
      </>
    );
  }

  renderGatherItem = (x, idx) => {
    return (
      <Form.Item label={`信息${idx + 1}`} key={idx} >
        <Input
          addonBefore={<div style={{ width: 50 }}>名称</div>}
          value={x.title}
          onChange={(e) => { return this.onChangeGatherItemValue(e, idx, 'title'); }}
          style={{ width: '50%' }}
        />
        <Input
          value={x.key}
          addonBefore={<div style={{ width: 50 }}>KEY</div>}
          onChange={(e) => { return this.onChangeGatherItemValue(e, idx, 'key'); }}
          style={{ width: '50%' }}
        />
        <div style={{ margin: '5px 0' }}>
          是否必填:&nbsp;
          <Radio.Group
            value={x.required}
            onChange={(e) => { return this.onChangeGatherItemValue(e, idx, 'required'); }}
          >
            <Radio value>必填</Radio>
            <Radio value={false}>非必填</Radio>
          </Radio.Group>
          |
          是否多选:&nbsp;
          <Radio.Group
            value={x.multi}
            onChange={(e) => { return this.onChangeGatherItemValue(e, idx, 'multi'); }}
          >
            <Radio value>多选</Radio>
            <Radio value={false}>单选</Radio>
          </Radio.Group>
        </div>
        <Form.Item label="字段">
          {
            (x.fields || [{}]).map((f, fIdx) => {
              return (
                <>
                  <div key={f.key}>
                    <Input
                      addonBefore={<div style={{ width: 50 }}>名称</div>}
                      value={f.title}
                      style={{ width: '50%' }}
                      onChange={(e) => { return this.onChangeGatherFieldValue(e, 'title', idx, fIdx); }}
                    />
                    <Input
                      addonBefore={<div style={{ width: 50 }}>KEY</div>}
                      defaultValue={f.key}
                      style={{ width: '50%' }}
                      onChange={(e) => { return this.onChangeGatherFieldValue(e, 'key', idx, fIdx); }}
                    />
                    <div style={{ display: 'flex', margin: '5px 0' }}>
                      <Input.Group style={{ width: '50%' }}>
                        <Button style={{ width: 75 }}>类型</Button>
                        <Select
                          value={f.dataType}
                          style={{ width: 'calc(100% - 75px)' }}
                          onChange={(e) => { return this.onChangeGatherFieldValue(e, 'dataType', idx, fIdx); }}
                        >
                          <Select.Option value="string">文本</Select.Option>
                          <Select.Option value="string[]">数组</Select.Option>
                        </Select>
                      </Input.Group>
                      <Input.Group style={{ width: '50%' }}>
                        <Button style={{ width: 75 }}>选项</Button>
                        <Select
                          mode="tags"
                          open={false}
                          value={f.selections}
                          style={{ width: 'calc(100% - 75px)' }}
                          onChange={(e) => { return this.onChangeGatherFieldValue(e, 'selections', idx, fIdx); }}
                        />
                      </Input.Group>
                    </div>
                    <div style={{ display: 'flex', margin: '5px 0', justifyContent: 'space-between' }}>
                      <Input.Group style={{ width: '50%' }}>
                        <Button style={{ width: 75 }}>必填</Button> &nbsp;
                        <Radio.Group
                          value={f.required}
                          onChange={(e) => { return this.onChangeGatherFieldValue(e, 'required', idx, fIdx); }}
                        >
                          <Radio value>是</Radio>
                          <Radio value={false}>否</Radio>
                        </Radio.Group>
                      </Input.Group>
                      <Input.Group style={{ width: 'calc(50% - 80px)' }} compact>
                        <Button style={{ width: 75 }}>备注</Button>
                        <Input
                          value={f.comment}
                          style={{ width: 'calc(100% - 75px)' }}
                          onChange={(e) => { return this.onChangeGatherFieldValue(e, 'comment', idx, fIdx); }}
                        />
                      </Input.Group>
                      <Button
                        type="link"
                        size="small"
                        style={{ width: 80 }}
                        onClick={() => { return this.onAddField(idx); }}
                      >
                        新增字段
                      </Button>
                    </div>
                  </div>

                  <Divider style={{ margin: '5px 0' }} />
                </>

              );
            })
          }
        </Form.Item>
      </Form.Item>
    );
  }

  render = () => {
    const { data, params } = this.props;
    const { gatherItems, loadConfig } = data.llmSetting?.extraParams || {};

    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label="模型">
          <ModelSelect
            params={this.props.params}
            value={data?.llmSetting?.model}
            onChange={(e) => { return this.onChangeValue(e, 'model'); }}
          />
        </Form.Item>
        {this.renderOpenAIParams(data?.llmSetting, this.onChangeValue)}
        <Form.Item label="系统提示词" >
          <ToolEditor
            key="extractSystemPrompt"
            editorId="extractSystemPrompt"
            value={data.llmSetting?.systemPrompt}
            datas={[]}
            controls={data?.controls || []}
            params={params}
            onChange={(e) => { return this.onChangeValue(e, 'systemPrompt'); }}
          />
        </Form.Item>

        <Form.Item label="加载配置">
          {
            ObjectExtension.objToArray(loadConfig).map((x, idx) => {
              return (
                <Input.Group compact style={{ marginBottom: 5 }}>
                  <Input
                    value={x.key}
                    style={{ width: 200 }}
                    onChange={(e) => { return this.onChangeLoadConfig(e, 'key', idx); }}
                  />
                  <Select
                    value={x.value}
                    style={{ width: 200 }}
                    onChange={(e) => { return this.onChangeLoadConfig(e, 'value', idx); }}
                  >
                    <Select.Option value="text">文本</Select.Option>
                    <Select.Option value="dict">字典</Select.Option>
                    <Select.Option value="list">列表</Select.Option>
                  </Select>
                  <Divider type="vertical" />
                  <PlusCircleFilled
                    style={{ fontSize: 28 }}
                    onClick={() => { return this.onAddLoadConfig(idx); }}
                  />
                  <Divider type="vertical" />
                  <DeleteFilled
                    style={{ fontSize: 28 }}
                    onClick={() => { return this.onDelLoadConfig(idx); }}
                  />
                </Input.Group>
              );
            })
          }
        </Form.Item>
        <Form.Item label="信息收集">
          <Button type="link" size="small" onClick={() => { return this.onAddGatherItem(); }}>新增信息</Button>
          {
            (gatherItems || []).map((x, idx) => {
              return this.renderGatherItem(x, idx);
            })
          }
        </Form.Item>
      </Form>
    );
  }
}
