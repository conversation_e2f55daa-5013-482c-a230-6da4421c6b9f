/* eslint-disable max-len */
import { CodepenOutlined } from '@ant-design/icons';
import { python } from '@codemirror/lang-python';
import CodeMirror from '@uiw/react-codemirror';
import { Toast } from '~/components';
import Consts from '~/consts';
import { Extentions } from '~/engine';
import { OSSFileHelper } from '~/plugins';
import { Button, Checkbox, Divider, Form, Input, Modal, Popconfirm, Table } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import { loadPyodide } from 'pyodide';
import React, { PureComponent } from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';

export default class PythonInterpreter extends PureComponent {
  static propTypes = {
    data: PropTypes.object,
    onChange: PropTypes.func,
  }


  state = {
    codeDescription: '',
    code: '',
    logs: [],
    codes: [],
    codeOpen: false,
    isCoding: false,
    aiCodeOpen: false,
    runData: {
      inputs: 'pre node text',
      context: `{"workflow_id":"${window.location.pathname.replace('/workflow/', '')}","run_id":"mock run ID","workflow_run_input":"workflow's user input","workflow_node_data":{"node_id":"node output text","pre_defined_key":"value"}}`,
    },
  }

  componentDidMount = async () => {
    const data = await OSSFileHelper.fetchData('python_code', 'code');
    this.setState({ codes: _.isEmpty(data) ? [] : data });
  }

  onChangeRunData = (e, key) => {
    this.setState({ runData: { ...this.state.runData, [key]: e.target.value } });
  }

  onRunCode = async (pyodide, namespace) => {
    try {
      const jsResult = await pyodide.runPythonAsync(`${this.props.data?.sourceCode}`, { globals: namespace });
      this.setState({ logs: [...this.state.logs, `结果: ${jsResult}`] });
    } catch (error) {
      const regex = /<module>\n(.*?)\nYou can install it by calling/g;
      const match = regex.exec(error.toString());
      if (match) {
        const pkgName = /'([^']*)'/g.exec(match[1]);
        if (pkgName[1]) {
          await pyodide.loadPackage('micropip');
          const micropip = pyodide.pyimport('micropip');
          await micropip.install(pkgName[1]);
          await this.onRunCode(pyodide, namespace);
        }
      } else {
        let err = error.toString();
        err = err.replace(/IndentationError: unindent does not match any outer indentation level.*/g, '');
        err = err.replace(/pyodide/gi, 'pyrunner');
        this.setState({ logs: [...this.state.logs, `错误: ${err}`] });
      }
    }
  }

  onRun = async () => {
    const { inputs, context } = this.state.runData;
    try {
      JSON.parse(context || '{}');
    } catch (error) {
      Toast.show('Context Err', Toast.Type.ERROR);
      return;
    }

    await this.setState({ logs: [], isRun: true });
    const pyodide = await loadPyodide({ indexURL: 'https://cdn.jsdelivr.net/pyodide/v0.23.4/full/' });
    pyodide.setStdout({
      batched: (e) => {
        return this.setState({ logs: [...this.state.logs, `日志: ${e}`] });
      },
    });

    const namespace = pyodide.toPy({
      inputs: [{ text: inputs, prev_node: 'prev_node ID' }],
      context: JSON.parse(context || '{}'),
    });
    await this.onRunCode(pyodide, namespace);
    await this.setState({ isRun: false });
  }

  onChangeCode = async (index) => {
    const { code } = this.state.codes[index];
    await this.props.onChange(code, 'sourceCode');
    this.setState({ codeOpen: false });
  }

  onDelCode = async (index) => {
    const codes = _.cloneDeep(this.state.codes);
    codes.splice(index, 1);
    await OSSFileHelper.updateData(JSON.stringify(codes), 'python_code', 'code');
    this.setState({ codes });
  }

  onSave = async (name) => {
    const { codes } = this.state;
    codes.push({ name, code: this.props.data.sourceCode });
    await OSSFileHelper.updateData(JSON.stringify(codes), 'python_code', 'code');
    this.setState({ open: false });
    Toast.show('保存成功!', Toast.Type.SUCCESS);
  }

  onCode = () => {
    const { codeDescription } = this.state;
    if (codeDescription) {
      this.setState({ isCoding: true });
      Extentions.formatPrompt(codeDescription, (text, isStop) => {
        if (isStop) {
          this.setState({ isCoding: false });
        }
        this.setState({ code: text });
        setTimeout(() => {
          const objDiv = document.getElementById('code').querySelectorAll('.cm-scroller')[0];
          objDiv.scrollTop = objDiv?.scrollHeight;
        }, 500);
      }, 1, Consts.PROMPT_TEMPLATE_PYTHON_CODER);
    }
  }

  renderAiCodeModal = () => {
    const { codeDescription, isCoding, code } = this.state;
    return (
      <Modal
        width={1200}
        title="AI自动生成代码"
        open={this.state.aiCodeOpen}
        onCancel={() => { return this.setState({ aiCodeOpen: false }); }}
        footer={(!isCoding && code) ? [
          <CopyToClipboard text={code} onCopy={() => { Toast.show('复制成功!', Toast.Type.SUCCESS); }}>
            <Button type="primary">复制代码</Button>
          </CopyToClipboard>,
        ] : null}
      >
        <Input.Search
          placeholder="请描述需要实现的功能（例：将简体中文转化成繁体）"
          value={codeDescription}
          enterButton="生成"
          onChange={(e) => { return this.setState({ codeDescription: e.target.value }); }}
          onSearch={this.onCode}
          loading={isCoding}
        />
        <CodeMirror
          id="code"
          style={{ marginTop: 10 }}
          value={code}
          height="50vh"
          extensions={[python()]}
        />
      </Modal>
    );
  }

  renderModal = () => {
    return (
      <Modal
        title="代码另存为"
        open={this.state.open}
        onCancel={() => { return this.setState({ open: false }); }}
        footer={null}
      >
        <Input.Search enterButton="保存" onSearch={this.onSave} />
      </Modal>
    );
  }

  renderCodeModal = () => {
    return (
      <Modal
        title="代码库"
        open={this.state.codeOpen}
        onCancel={() => { return this.setState({ codeOpen: false }); }}
        footer={null}
      >
        <Table
          dataSource={this.state.codes}
          columns={[
            { title: '名称', dataIndex: 'name', key: 'name', align: 'center' },
            {
              title: '操作',
              dataIndex: 'opt',
              align: 'center',
              key: 'opt',
              render: (opt, row, index) => {
                return (
                  <>
                    <Popconfirm title="是否应用至当前节点?!" onConfirm={() => { return this.onChangeCode(index); }} >
                      <a>应用至当前节点</a>
                    </Popconfirm>
                    <Divider type="vertical" />
                    <Popconfirm title="是否删除代码?!" onConfirm={() => { return this.onDelCode(index); }} >
                      <a>删除</a>
                    </Popconfirm>
                  </>
                );
              },
            },
          ]}
        />
      </Modal>
    );
  }

  render = () => {
    const { data } = this.props;

    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label=" " colon={false}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Checkbox
              checked={data?.codeInterpreterExperiment}
              onChange={(e) => { return this.props.onChange(e.target.checked, 'codeInterpreterExperiment'); }}
            >
              抢鲜版：抢鲜版可联网、可使用所有Python pip包；运行限制3分钟，临时磁盘500M。
            </Checkbox>
            <div>
              <Button
                icon={<CodepenOutlined />}
                style={{ fontSize: 32, marginRight: 10 }}
                onClick={() => { return this.setState({ codeOpen: true }); }}
              />
              <Button
                style={{ marginRight: 10 }}
                onClick={() => { return this.setState({ open: true }); }}
              >
                代码另存为
              </Button>
              <Button onClick={() => { return this.setState({ aiCodeOpen: true }); }}>AI自动生成代码</Button>
            </div>
          </div>
          <CodeMirror
            value={data?.sourceCode}
            height="50vh"
            extensions={[python()]}
            onChange={(e) => { return this.props.onChange(e, 'sourceCode'); }}
          />
          {
            !data?.codeInterpreterExperiment &&
            <>
              <Input
                addonBefore={<div style={{ width: 72 }}>Inputs</div>}
                value={this.state.runData.inputs}
                onChange={(e) => { return this.onChangeRunData(e, 'inputs'); }}
              />
              <Input
                addonBefore={<div style={{ width: 72 }}>Context</div>}
                style={{ margin: '10px 0' }}
                value={this.state.runData.context}
                onChange={(e) => { return this.onChangeRunData(e, 'context'); }}
              />
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                说明：inputs 是当前Python节点的所有前置依赖节点的输出； workflow_node_data保存了自定义变量对应的值以及所有前置节点的输出(key是节点的ID, value是节点的输出)
                <Button
                  type="primary"
                  size="small"
                  loading={this.state.isRun}
                  onClick={() => { return this.onRun(); }}
                >
                  调试
                </Button>
              </div>
              <Divider style={{ marginTop: 5 }} />
              {
                this.state.logs.map((x) => {
                  return (
                    <span>
                      {x.split('\n').map((i) => { return <span>{i}<br /></span>; })}<br />
                    </span>
                  );
                })
              }
            </>
          }
        </Form.Item>

        {this.state.open && this.renderModal()}
        {this.state.codeOpen && this.renderCodeModal()}
        {this.state.aiCodeOpen && this.renderAiCodeModal()}
      </Form>
    );
  }
}
