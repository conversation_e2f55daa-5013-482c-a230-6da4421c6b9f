import { DeleteFilled } from '@ant-design/icons';
import { ChatBot } from '~/engine';
import { Button, Form, Input, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ToolEditor from '../ToolEditor';

export default class SaveDatabase extends PureComponent {
  static propTypes = {
    params: PropTypes.object,
    data: PropTypes.object,
    onChange: PropTypes.func,
  }

  state = {
    tables: [],
  }

  componentDidMount = async () => {
    let tables = await ChatBot.fetchTables();
    tables = _.map(tables, (v) => { return v; });
    this.setState({ tables });
  }

  onChangeTable = async (e) => {
    const { columns } = this.state.tables.find((x) => { return x.tableName === e; });
    await this.props.onChange({ tableName: e, columns });
  }

  onChangeColumnData = async (e, type, idx) => {
    const { tableName, columns } = this.props.data?.llmSetting;
    const newItems = _.cloneDeep(columns) || [{}];
    newItems[idx][type] = e?.target ? e.target.value : e;
    await this.props.onChange({ tableName, columns: newItems });
  }

  onDelColumn = async (idx = 0) => {
    const { tableName, columns } = this.props.data?.llmSetting;
    const newItems = _.cloneDeep(columns) || [{}];
    if (newItems?.length === 1) return;
    newItems.splice(idx, 1);
    await this.props.onChange({ tableName, columns: newItems });
  }

  render = () => {
    const { data, params } = this.props;
    const { tableName, columns } = data?.llmSetting;
    return (
      <>
        <Form.Item label="表名">
          <Select value={tableName} onChange={(e) => { return this.onChangeTable(e); }}>
            {
              (this.state.tables || []).map((x) => {
                return <Select.Option value={x.tableName}>{x.tableName}</Select.Option>;
              })
            }
          </Select>
        </Form.Item>
        <Form.Item label="字段">
          {
            (columns || []).map((x, index) => {
              return (
                <div style={{ marginBottom: 10 }}>
                  <div style={{ display: 'flex' }}>
                    <Input addonBefore={`类型(${x.columnType})`} value={x.columnName} />
                    <span style={{ marginLeft: 30, display: 'flex' }}>
                      {
                        x.isNullable &&
                        <Button
                          icon={<DeleteFilled />}
                          style={{ marginRight: 10 }}
                          onClick={() => { return this.onDelColumn(index); }}
                        />
                      }
                    </span>
                  </div>
                  <ToolEditor
                    key={`${x.columnName}`}
                    editorId={`${x.columnName}`}
                    datas={[]}
                    value={x.columnValue}
                    params={params}
                    controls={data?.controls || []}
                    onChange={(e) => { return this.onChangeColumnData(e, 'columnValue', index); }}
                  />
                </div>
              );
            })
          }
        </Form.Item>
      </>
    );
  }
}
