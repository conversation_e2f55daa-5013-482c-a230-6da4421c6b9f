import { Form, Input, InputNumber } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ModelSelect from '../ModelSelect';
import ToolEditor from '../ToolEditor';

export default class FeedbackSetting extends PureComponent {
  static propTypes = {
    params: PropTypes.object,
    data: PropTypes.object,
    onChangeSetting: PropTypes.func,
    onChangeExtraParams: PropTypes.func,
    onChange: PropTypes.func,
  }

  render = () => {
    const { data, params } = this.props;

    return (
      <Form labelCol={{ span: 2 }} >
        <Form.Item label="名称">
          <Input
            value={data?.name}
            onChange={(e) => { return this.props.onChange({ node: { ...data, name: e.target.value } }); }}
          />
        </Form.Item>
        <Form.Item label="模型">
          <ModelSelect
            params={params}
            value={data?.llmSetting?.model}
            onChange={(e) => { return this.props.onChangeSetting(e, 'model'); }}
          />
        </Form.Item>
        <Form.Item label="搜索文本">
          <ToolEditor
            key="inputTemplate"
            editorId="inputTemplate"
            controls={data?.controls || []}
            datas={[]}
            params={this.props.params}
            value={data?.llmSetting?.extraParams?.inputTemplate}
            onChange={(e) => { return this.props.onChangeExtraParams(e, 'inputTemplate', data); }}
          />
        </Form.Item>
        <Form.Item label="用户ID">
          <ToolEditor
            key="userId"
            editorId="userId"
            controls={data?.controls || []}
            datas={[]}
            params={this.props.params}
            value={data?.llmSetting?.extraParams?.userId}
            onChange={(e) => { return this.props.onChangeExtraParams(e, 'userId', data); }}
          />
        </Form.Item>
        <Form.Item label="用户角色">
          <ToolEditor
            key="userRole"
            editorId="userRole"
            controls={data?.controls || []}
            datas={[]}
            params={this.props.params}
            value={data?.llmSetting?.extraParams?.userRole}
            onChange={(e) => { return this.props.onChangeExtraParams(e, 'userRole', data); }}
          />
        </Form.Item>
        <Form.Item label="课程ID">
          <ToolEditor
            key="courseId"
            editorId="courseId"
            controls={data?.controls || []}
            datas={[]}
            params={this.props.params}
            value={data?.llmSetting?.extraParams?.courseId}
            onChange={(e) => { return this.props.onChangeExtraParams(e, 'courseId', data); }}
          />
        </Form.Item>
        <Form.Item label="MaxTokens">
          <InputNumber
            value={data?.llmSetting?.maxTokens}
            onChange={(e) => { return this.props.onChangeSetting(e, 'maxTokens'); }}
          />
        </Form.Item>
        <Form.Item label="TopK">
          <InputNumber
            value={data?.llmSetting?.extraParams?.topK}
            onChange={(e) => { return this.props.onChangeExtraParams(e, 'topK', data); }}
          />
        </Form.Item>
        <Form.Item label="反馈天数">
          <InputNumber
            min={0}
            max={30}
            addonBefore="最近"
            addonAfter="天"
            value={data?.llmSetting?.extraParams?.maxDays}
            onChange={(e) => { return this.props.onChangeExtraParams(e, 'maxDays', data); }}
          />
        </Form.Item>
      </Form>
    );
  }
}
