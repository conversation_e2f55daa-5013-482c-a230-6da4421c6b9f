import { Form } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ToolEditor from '../ToolEditor';
import TransformContentSetting from './TransformContentSetting';

const EDITOR_KEYS = {
  openKfid: '客服ID',
  userId: '用户ID',
  body: '内容',
};

export default class WeworkKfSender extends PureComponent {
  static propTypes = {
    params: PropTypes.object,
    data: PropTypes.object,
    onChange: PropTypes.func,
    onChangeParams: PropTypes.func,
  }

  state = {
  }

  componentDidMount = async () => {
  }

  render = () => {
    const { data, params } = this.props;
    const { extraParams } = data?.llmSetting;

    return (
      <>
        <TransformContentSetting
          data={data}
          params={this.props.params}
          onChange={(e) => { return this.props.onChangeParams(e); }}
        />
        {
          _.map(EDITOR_KEYS, (v, key) => {
            return (
              <Form.Item label={v}>
                <ToolEditor
                  isMin
                  key={key}
                  editorId={key}
                  value={extraParams[key]}
                  datas={[]}
                  controls={data?.controls || []}
                  params={params}
                  onChange={(e) => { return this.props.onChange(e, key, data); }}
                />
              </Form.Item>
            );
          })
        }
      </>
    );
  }
}
