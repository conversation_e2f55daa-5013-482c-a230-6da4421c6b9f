import { Form, Select, Switch } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ToolEditor from '../ToolEditor';

export default class ExcelProcessor extends PureComponent {
  static propTypes = {
    params: PropTypes.object,
    data: PropTypes.object,
    onChange: PropTypes.func,
  }

  state = {
  }

  render = () => {
    const { data, params } = this.props;
    const { extraParams } = data?.llmSetting;

    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label="文件">
          <ToolEditor
            showUpload
            key="data-fileUrl"
            editorId="data-fileUrl"
            value={extraParams?.fileUrl}
            datas={[]}
            controls={data?.controls || []}
            params={params}
            onChange={(e) => { return this.props.onChange(e, 'fileUrl', data); }}
          />
        </Form.Item>
        <Form.Item label="输出文件数据">
          <Switch
            checked={extraParams?.enableRead}
            onChange={(e) => { return this.props.onChange(e, 'enableRead', data); }}
          />
        </Form.Item>
        <Form.Item label="输出新表格">
          <Switch
            checked={extraParams?.enableWrite}
            onChange={(e) => { return this.props.onChange(e, 'enableWrite', data); }}
          />
        </Form.Item>
        {
          extraParams?.enableWrite &&
          <>
            <Form.Item label="表格数据">
              <ToolEditor
                key="data-new-excel"
                editorId="data-new-excel"
                value={extraParams?.data}
                datas={[]}
                controls={data?.controls || []}
                params={params}
                onChange={(e) => { return this.props.onChange(e, 'data', data); }}
              />
            </Form.Item>
            <Form.Item label="表格类型">
              <Select value={extraParams?.format} onChange={(e) => { return this.props.onChange(e, 'format', data); }}>
                <Select.Option value="csv">csv</Select.Option>
                <Select.Option value="xlsx">xlsx</Select.Option>
              </Select>
            </Form.Item>
          </>
        }
      </Form>
    );
  }
}
