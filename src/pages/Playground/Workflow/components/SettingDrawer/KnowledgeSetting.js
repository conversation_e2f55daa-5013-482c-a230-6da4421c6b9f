import { python } from '@codemirror/lang-python';
import CodeMirror from '@uiw/react-codemirror';
import { ObjectExtension } from '~/plugins';
import { Form, Input, InputNumber, Select, Switch } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import { OPENAI_PARAMS, OPENAI_PARAMS_MAX_VALUE, OPENAI_PARAMS_STEP } from '../../../Configs';
import { INFO_NODES, PROMPT_KEYS, PROMPT_KEY_NAME } from '../CustomNodes/Configs';
import ModelSelect from '../ModelSelect';
import ToolEditor from '../ToolEditor';
import SaveDataSetting from './SaveDataSetting';

const EXECUTE_AGENT_PARAMS = {
  maxReturnToken: '最大TOKEN数',
  maxAgentSteps: '最大循环次数',
};
export default class KnowledgeSetting extends PureComponent {
  static propTypes = {
    data: PropTypes.array,
    params: PropTypes.object,
    originData: PropTypes.object,
    subType: PropTypes.string,
    onChange: PropTypes.func,
    onChangeSetting: PropTypes.func,
    onChangeExtraParams: PropTypes.func,
  }

  onChangeParams = ({ flowData }, data, key) => {
    const obj = {};
    for (const item of flowData) {
      obj[item.key || ''] = item.value || '';
    }
    this.props.onChangeExtraParams(obj, key, data);
  }

  renderOpenAIParams = (data = {}, onChange = () => { }) => {
    return (
      <>
        {
          _.map(OPENAI_PARAMS, (v, k) => {
            const numProps = OPENAI_PARAMS_MAX_VALUE[k] > 1 ?
              { min: 0, max: OPENAI_PARAMS_MAX_VALUE[k], step: OPENAI_PARAMS_STEP[k], value: data[k] } :
              { min: 0, max: 1, step: 0.1, value: data[k] };

            return (
              <Form.Item label={_.upperFirst(k)}>
                <InputNumber
                  {...numProps}
                  onChange={(e) => { return onChange(e, k); }}
                />
              </Form.Item>
            );
          })
        }
        <Form.Item label="响应类型">
          <Select
            value={data?.responseFormat}
            onChange={(e) => { return onChange(e, 'responseFormat'); }}
          >
            <Select.Option value="text">文本</Select.Option>
            <Select.Option value="json_object">JSON</Select.Option>
          </Select>
        </Form.Item>
      </>
    );
  }

  renderKnowledgePrompt = (data) => {
    const content = [];

    _.map(PROMPT_KEYS, (keys, type) => {
      if (
        type === 'compressPrompt' && this.props.subType === INFO_NODES.searchWeb.type &&
        !data?.llmSetting?.enableCompress
      ) {
        keys = keys.filter((x) => { return x !== this.props.subType; }); // eslint-disable-line
      }

      if (keys.includes(this.props.subType) && !_.isEmpty(PROMPT_KEY_NAME[type])) {
        content.push(
          <Form.Item label={PROMPT_KEY_NAME[type]}>
            <Input.TextArea
              autoSize={{ minRows: 3, maxRows: 8 }}
              value={(data?.llmSetting || {})[type]}
              onChange={(e) => { return this.props.onChangeSetting(e, type); }}
            />
          </Form.Item>,
        );
      }
    });
    return <>{content}</>;
  }

  renderExecuteAgentParams = (data) => {
    return (
      <>
        {
          this.props.subType === INFO_NODES.searchCiteKb.type &&
          <>
            <Form.Item label="单条知识压缩TOEKN">
              <InputNumber
                min={0}
                value={data?.llmSetting?.extraParams?.singleCompressToken}
                onChange={(e) => { return this.props.onChangeExtraParams(e, 'singleCompressToken', data); }}
              />
            </Form.Item>
            <Form.Item label="排除关键字">
              <Select
                mode="tags"
                open={false}
                value={data?.llmSetting?.extraParams?.excludeKeywords}
                onChange={(e) => { return this.props.onChangeExtraParams(e, 'excludeKeywords', data); }}
              />
            </Form.Item>
          </>
        }
        {
          INFO_NODES.searchWeb.type === this.props.subType &&
          <>
            <Form.Item label="提取条数">
              <InputNumber
                min={0}
                value={data?.llmSetting?.topK}
                onChange={(e) => { return this.props.onChangeSetting(e, 'topK'); }}
              />
            </Form.Item>
            <Form.Item label="搜索引擎">
              <Select
                value={data?.llmSetting?.searchEngine}
                onChange={(e) => { return this.props.onChangeSetting(e, 'searchEngine'); }}
              >
                <Select.Option value="tg">天宫搜索</Select.Option>
                <Select.Option value="serper">Google</Select.Option>
              </Select>
            </Form.Item>
          </>
        }
        {
          [INFO_NODES.searchKb.type, INFO_NODES.searchCiteKb.type].includes(this.props.subType) &&
          <>
            <Form.Item label="搜索内容">
              <ToolEditor
                key="query"
                editorId="query"
                controls={data?.controls || []}
                datas={[]}
                params={this.props.params}
                value={data?.llmSetting?.extraParams?.query}
                onChange={(e) => { return this.props.onChangeExtraParams(e, 'query', data); }}
              />
            </Form.Item>
            <Form.Item label="知识库">
              <Select
                value={data?.llmSetting?.extraParams?.libraryId}
                onChange={(e) => { return this.props.onChangeExtraParams(e, 'libraryId', data); }}
              >
                <Select.Option value={0}>全部</Select.Option>
                {
                  this.props?.originData?.libraries.map((x) => {
                    return <Select.Option value={x.id}>{x.field}</Select.Option>;
                  })
                }
              </Select>
            </Form.Item>
            <Form.Item label="提取条数">
              <InputNumber
                min={0}
                value={data?.llmSetting?.extraParams?.topK}
                onChange={(e) => { return this.props.onChangeExtraParams(e, 'topK', data); }}
              />
            </Form.Item>
            <Form.Item label="元数据">
              <SaveDataSetting
                node={data}
                params={this.props.params}
                data={ObjectExtension.objToArray(data?.llmSetting?.extraParams?.metadata)}
                onChange={(e) => { return this.onChangeParams(e, data, 'metadata'); }}
              />
            </Form.Item>
            <Form.Item label="输出模版">
              <CodeMirror
                id="sourceCode"
                value={data?.llmSetting?.resultTpl}
                height="100px"
                extensions={[python()]}
                onChange={(e) => { return this.props.onChangeSetting(e, 'resultTpl'); }}
              />
            </Form.Item>
          </>
        }
        {
          _.map(data?.llmSetting?.extraParams, (value, key) => {
            if (_.isUndefined(EXECUTE_AGENT_PARAMS[key])) return null;
            return (
              <Form.Item label={EXECUTE_AGENT_PARAMS[key]}>
                <InputNumber
                  value={value}
                  onChange={(e) => { return this.props.onChangeExtraParams(e, key, data); }}
                />
              </Form.Item>
            );
          })
        }
      </>
    );
  }

  renderViewpoint = (data) => {
    return (
      <>
        {
          _.map({
            content: '内容',
            sourceUrl: '内容来源网址',
            courseId: '课程ID',
            createdAt: '创建时间',
          }, (val, key) => {
            return (
              <Form.Item label={val}>
                <ToolEditor
                  key={key}
                  editorId={key}
                  controls={data?.controls || []}
                  datas={[]}
                  params={this.props.params}
                  value={data?.llmSetting[key]}
                  onChange={(e) => { return this.props.onChangeSetting(e, key); }}
                />
              </Form.Item>
            );
          })
        }
      </>
    );
  }

  renderName = (data) => {
    return (
      <Form.Item label="名称">
        <Input
          value={data?.name}
          onChange={(e) => { return this.props.onChange({ node: { ...data, name: e.target.value } }); }}
        />
      </Form.Item>
    );
  }

  renderLLMSetting = (data) => {
    if ([INFO_NODES.searchKb.type, INFO_NODES.searchCiteKb.type].includes(this.props.subType)) return null;
    if (this.props.subType === INFO_NODES.searchWeb.type && !data?.llmSetting?.enableCompress) return null;

    return (
      <>
        <Form.Item label="运行时禁止蹦字日志">
          <Switch
            checked={data?.llmSetting?.disableStepToken}
            onChange={(e) => { return this.props.onChangeSetting(e, 'disableStepToken'); }}
          />
        </Form.Item>
        <Form.Item label="模型">
          <ModelSelect
            params={this.props.params}
            value={data?.llmSetting?.model}
            onChange={(e) => { return this.props.onChangeSetting(e, 'model'); }}
          />
        </Form.Item>
        {this.renderOpenAIParams(data?.llmSetting, this.props.onChangeSetting)}
      </>
    );
  }

  render = () => {
    const { data, subType } = this.props;
    return (
      <>
        <Form labelCol={{ span: 4 }}>
          {
            INFO_NODES.searchWeb.type === this.props.subType &&
            <Form.Item label="是否压缩">
              <Switch
                checked={data?.llmSetting?.enableCompress}
                onChange={(e) => { return this.props.onChangeSetting(e, 'enableCompress'); }}
              />
            </Form.Item>
          }
          {this.renderKnowledgePrompt(data)}
          {this.renderExecuteAgentParams(data)}
          {this.renderName(data)}
          {subType === 'summaryViewpoint' && this.renderViewpoint(data)}
          {this.renderLLMSetting(data)}
        </Form>
      </>
    );
  }
}
