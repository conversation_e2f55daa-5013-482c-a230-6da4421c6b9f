/* eslint-disable max-len */
import { markdown } from '@codemirror/lang-markdown';
import CodeMirror from '@uiw/react-codemirror';
import { ChatBot } from '~/engine';
import { Platform } from '~/plugins';
import { Button, Checkbox, Divider, Drawer, Form, Input, InputNumber, Select, Table, Typography } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

const HOOK_ENUM = {
  successHook: '执行成功之后触发',
  failureHook: '执行失败之后触发',
};
const JSON_STR = '{\n    "user_message": job.user_message, //当前工作流的用户输入的消息\n    "workflow_result": "error", //执行成功则是工作流的输出消息，失败则为 error\n    "error": e.__str__(), //仅当失败的时候有值，为失败的消息\n    "trace": sys.exc_info() //仅当失败的时候有值，为异常的上下文\n}\n';
export default class EndSetting extends PureComponent {
  static propTypes = {
    flowId: PropTypes.string,
    hookObj: PropTypes.object,
    subflows: PropTypes.array,
  }

  state = {
    query: {
      keyword: '',
      time: 5,
      offset: 0,
      limit: 10,
      onlyError: true,
    },
    logs: [],
  }

  onChangeValue = (e, k) => {
    const hookObj = _.cloneDeep(this.props.hookObj);
    hookObj[k] = `workflow@${e}`;
    Platform.emit('UPDATE_WORKFLOW_HOOK', hookObj);
  }

  onQueryLogs = async () => {
    const { time, limit, onlyError, keyword } = this.state.query;
    const fromTime = moment().subtract(time, 'minutes').unix();
    const toTime = moment().unix();

    const result = await ChatBot.fetchWorkflowSLSLogs({
      flowId: this.props.flowId,
      fromTime,
      toTime,
      offset: 0,
      limit,
      querys: keyword ? [keyword] : undefined,
      error_only: onlyError,
    });
    const logs = result.item.map((x) => { return JSON.parse(x.content); });
    this.setState({ logs });
  }

  renderLogDrawer = () => {
    return (
      <Drawer
        title="错误日志"
        placement="right"
        closable={false}
        onClose={() => { this.setState({ openLog: false }); }}
        visible={this.state.openLog}
        width={800}
      >
        <Input.TextArea
          value={JSON.stringify(this.state.log, null, 2)}
          autoSize={{ minRows: 20 }}
        />
      </Drawer>
    );
  }

  renderErrLogs = () => {
    const { query, logs } = this.state;
    return (
      <>
        <div>
          <Select
            style={{ width: 120 }}
            value={query.time}
            onChange={(e) => { this.setState({ query: { ...query, time: e } }); }}
          >
            {
              [5, 15, 30, 60, 120, 180].map((x) => {
                return <Select.Option value={x}>{`${x}分钟内`}</Select.Option>;
              })
            }
          </Select>
          <Divider type="vertical" />
          <Input
            addonBefore="关键词"
            style={{ width: 240 }}
            value={query.keyword}
            onChange={(e) => { this.setState({ query: { ...query, keyword: e.target.value } }); }}
          />
          <Divider type="vertical" />
          <InputNumber
            addonBefore="显示"
            addonAfter="条"
            value={query.limit}
            onChange={(e) => { this.setState({ query: { ...query, limit: e } }); }}
          />
          <Divider type="vertical" />
          <Checkbox
            checked={query.onlyError}
            onChange={(e) => { this.setState({ query: { ...query, onlyError: e.target.checked } }); }}
          >仅显示错误
          </Checkbox>
          <Divider type="vertical" />
          <Button
            onClick={() => { return this.onQueryLogs(); }}
          >查询
          </Button>
        </div>

        <Table
          size="small"
          pagination={{ total: logs.length, pageSize: 10 }}
          dataSource={logs}
          columns={
            [
              { title: 'level', dataIndex: 'level', key: 'level', width: 80 },
              {
                title: 'time',
                dataIndex: 'time',
                key: 'time',
                width: 160,
                render: (text) => { return moment(text).format('MM-DD HH:mm:ss'); },
              },
              { title: 'message', dataIndex: 'message', key: 'message', ellipsis: true },
              {
                title: 'options',
                dataIndex: 'timestamp',
                key: 'timestamp',
                width: 100,
                render: (text, record) => {
                  return (
                    <a onClick={() => { return this.setState({ log: record, openLog: true }); }}>详情</a>
                  );
                },
              },
            ]}
        />
      </>
    );
  }

  render = () => {
    const { subflows, hookObj } = this.props;

    return (
      <>
        {
          _.map(HOOK_ENUM, (v, k) => {
            return (
              <Form.Item label={v}>
                <Select
                  showSearch
                  value={(hookObj[k] || '').replace('workflow@', '')}
                  filterOption={(input, option) => { return option.children.includes(input); }}
                  onChange={(e) => { return this.onChangeValue(e, k); }}
                >
                  {(subflows || []).map((x) => { return <Select.Option value={x.uuid}>{x.name}</Select.Option>; })}
                </Select>
              </Form.Item>
            );
          })
        }
        <CodeMirror readOnly theme="dark" value={JSON_STR} extensions={[markdown()]} />

        说明：成功和失败的工作流上配置的成功和失败的回调工作流将被忽略，不再执行。
        <Typography.Title level={5} style={{ margin: '10px 0' }}>错误日志</Typography.Title>
        {this.renderErrLogs()}

        {this.state.openLog && this.renderLogDrawer()}
      </>
    );
  }
}
