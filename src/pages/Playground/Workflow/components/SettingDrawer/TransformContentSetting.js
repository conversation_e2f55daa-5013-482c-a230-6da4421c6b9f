import Configs from '~/consts';
import { Transforms } from '~/engine';
import { Form, InputNumber, Select, Switch } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ModelSelect from '../ModelSelect';
import ToolEditor from '../ToolEditor';

export default class TransformContentSetting extends PureComponent {
  static propTypes = {
    params: PropTypes.object,
    data: PropTypes.object,
    onChange: PropTypes.func,
  }

  state = {
    groups: [],
  }

  componentDidMount = async () => {
    const { items } = await Transforms.fetchGroups(Configs.ALL_PAGE_PARAMS);
    this.setState({ groups: items });
  }

  onChangeValue = (e, key) => {
    const data = _.cloneDeep(this.props.data?.transformContentParams) || {};
    const value = e?.target ? e.target.value : e;
    data[key] = value;
    this.props.onChange(data);
  }

  renderLLMSetting = () => {
    const { transformContentParams: obj } = this.props.data;

    return (
      <>
        <Form.Item label="模型">
          <ModelSelect
            params={this.props.params}
            value={obj?.model}
            onChange={(e) => { return this.onChangeValue(e, 'model'); }}
          />
        </Form.Item>
        <Form.Item label="温度">
          <InputNumber
            min={0}
            value={obj?.temperature}
            onChange={(e) => { return this.onChangeValue(e, 'temperature'); }}
          />
        </Form.Item>
        <Form.Item label="最大长度">
          <InputNumber
            min={0}
            value={obj?.maxTokens}
            onChange={(e) => { return this.onChangeValue(e, 'maxTokens'); }}
          />
        </Form.Item>
        <Form.Item label="提示词">
          <ToolEditor
            key="system_prompt"
            editorId="system_prompt"
            value={obj?.systemPrompt}
            datas={[]}
            controls={this.props.data?.controls || []}
            params={this.props.params}
            onChange={(e) => { return this.onChangeValue(e, 'systemPrompt'); }}
          />
        </Form.Item>
      </>
    );
  }

  render = () => {
    const { transformContentParams } = this.props.data;

    return (
      <Form labelCol={{ span: 2 }} className="common-form">
        <Form.Item label="转换规则">
          <Select
            showSearch
            style={{ width: '100%' }}
            value={transformContentParams?.ruleGroupId}
            filterOption={(input, option) => { return option?.children?.includes(input); }}
            onChange={(e) => { return this.onChangeValue(e, 'ruleGroupId'); }}
          >
            {
              (this.state.groups || []).map((x) => {
                return (
                  <Select.Option value={x.id}>{x.name}</Select.Option>
                );
              })
            }
          </Select>
        </Form.Item>
        <Form.Item label="AI助手">
          <Switch
            checkedChildren="开启"
            unCheckedChildren="关闭"
            checked={transformContentParams?.aiAssist}
            onChange={(e) => { return this.onChangeValue(e, 'aiAssist'); }}
          />
        </Form.Item>

        {
          transformContentParams?.aiAssist &&
          this.renderLLMSetting()
        }
      </Form>
    );
  }
}
