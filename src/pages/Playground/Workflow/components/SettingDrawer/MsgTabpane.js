import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Input, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class MsgTabpane extends PureComponent {
  static propTypes = {
    messages: PropTypes.array,
    createConversation: PropTypes.func,
    deleteConversation: PropTypes.func,
    updateConversation: PropTypes.func,
  }

  state = {
    conversations: [],
  }

  componentDidMount = () => {
    this.setState({ conversations: this.props.messages });
  }

  onChangeMsgRole = async (role, index) => {
    const { conversations } = this.state;
    await this.props.updateConversation({ ...(conversations[index] || {}), role });
  }

  onChangeMsgContent = (e, index) => {
    const value = e?.target ? e.target.value : e;
    const conversations = _.cloneDeep(this.state.conversations);
    conversations[index] = { ...(conversations[index] || {}), edited: true, content: value };
    this.setState({ conversations });
  }

  onAddMsg = async () => {
    const { sessionId, topicId } = _.head(this.state.conversations);
    const conversation = await this.props.createConversation({ sessionId, topicId, content: '', role: 'USER' });
    this.setState({ conversations: [...this.state.conversations, conversation] });
  }

  onDelMsg = async (msgId) => {
    const conversations = this.state.conversations.filter((x) => { return x.id !== msgId; });
    await this.props.deleteConversation(msgId);
    this.setState({ conversations });
  }

  onSaveMsg = async (index) => {
    const { conversations } = this.state;
    const conversation = conversations[index];
    if (conversation?.edited) {
      await this.props.updateConversation(conversation);
    }
  }

  render = () => {
    return (
      <>
        {
          (this.state.conversations || []).map((conversation, index) => {
            return (
              <Input.Group
                compact
                style={{ display: 'flex', marginBottom: 5, padding: 2, borderBottom: '1px solid #ddd' }}
              >
                <Select
                  bordered={false}
                  style={{ width: 130, maxHeight: 32 }}
                  value={conversation.role}
                  onChange={(e) => { return this.onChangeMsgRole(e, index); }}
                >
                  <Select.Option value="USER">USER</Select.Option>
                  <Select.Option value="ASSISTANT">ASSISTANT</Select.Option>
                </Select>
                <Input.TextArea
                  autoSize
                  bordered={false}
                  value={conversation.content}
                  onChange={(e) => { return this.onChangeMsgContent(e, index); }}
                  onBlur={() => { return this.onSaveMsg(index); }}
                />
                <div style={{ display: 'flex' }}>
                  <Button
                    type="text"
                    icon={<MinusCircleOutlined />}
                    onClick={() => { return this.onDelMsg(conversation.id); }}
                  />
                </div>
              </Input.Group>
            );
          })
        }
        <Button
          icon={<PlusOutlined />}
          type="text"
          onClick={() => { return this.onAddMsg(); }}
        >
          新增信息
        </Button>
      </>
    );
  }
}
