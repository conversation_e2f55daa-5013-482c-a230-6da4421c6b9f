import hash from '@emotion/hash';
import { Sessions } from '~/engine';
import ReconnectingWebSocket from '~/pages/Playground/WebSocket';
import { Spin } from 'antd';
import md5 from 'blueimp-md5';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import Iframe from 'react-iframe';

export default class IframeSetting extends PureComponent {
  static propTypes = {
    data: PropTypes.object,
    params: PropTypes.object,
    nodes: PropTypes.array,
    onClose: PropTypes.func,
  }

  state = {
    md5Key: '',
  }

  componentDidMount = () => {
    const { data, nodes } = this.props;
    const { id: nodeId } = nodes.find((x) => { return x.name === data.name; });
    const md5Key = md5(`${window.location.pathname}/${nodeId}`);
    const path = `wss://fn.bzy.ai/v2/webcast/connect/${md5Key}`;
    this.ws = new ReconnectingWebSocket(path, [], this.onReceiveMsg, () => {
      this.ws.send(JSON.stringify({ status: 'ready', from: 'workflow' }));
      this.setState({ md5Key });
    });
  }

  onReceiveMsg = async (e) => {
    const obj = JSON.parse(e?.data);
    if (obj?.status === 'done') {
      const { data, params } = this.props;
      const flowConstant = _.keys(params);
      const nodeConstant = data?.controls;
      const { partnerId } = Sessions.getPartner();
      const pId = hash(`${partnerId}`);

      this.ws.send(JSON.stringify({
        type: 'message',
        to: 'iframe',
        content: { flowConstant, nodeConstant, name: data?.name, pId },
      }));
    }

    if (obj.type === 'action' && obj.action === 'close') {
      this.props.onClose();
    }
  }

  renderLoading = () => {
    return (
      <div style={{ height: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <div>
          <Spin size="large" tip="设置初始化..." />
        </div>
      </div>
    );
  }

  render = () => {
    if (_.isEmpty(this.state.md5Key)) return this.renderLoading();

    return (
      <Iframe
        width="100%"
        height="100%"
        styles={{ border: 'none' }}
        url={`${window.location.origin}/node/test?key=${this.state.md5Key}`}
      />
    );
  }
}
