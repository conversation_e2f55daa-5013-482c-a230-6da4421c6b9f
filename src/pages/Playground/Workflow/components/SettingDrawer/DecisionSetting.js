import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Input, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class DecisionSetting extends PureComponent {
  static propTypes = {
    conditions: PropTypes.array,
    onChange: PropTypes.func,
  }

  state = {
  }

  onChangeCondition = (e, type, idx) => {
    const conditions = _.cloneDeep(this.props.conditions) || [{}];
    conditions[idx][type] = e?.target ? e?.target.value : e;
    this.props.onChange({ conditions });
  }

  onAddCondition = (idx = 0) => {
    const conditions = _.cloneDeep(this.props.conditions) || [{}];
    conditions.splice(idx + 1, 0, {});
    this.props.onChange({ conditions });
  }

  onDelCondition = (idx = 0) => {
    const conditions = _.cloneDeep(this.props.conditions) || [{}];
    if (conditions?.length === 1) return;
    conditions.splice(idx, 1);
    this.props.onChange({ conditions });
  }

  render = () => {
    const conditions = _.isEmpty(this.props.conditions) ? [{}] : this.props.conditions;
    return (
      <>
        {
          conditions.map((x, idx) => {
            return (
              <Input.Group compact style={{ marginBottom: 5, display: 'flex', width: '100%' }}>
                <Button style={{ width: 80 }}>输入</Button>
                <Select
                  value={x.condition}
                  style={{ width: 100 }}
                  onChange={((e) => { return this.onChangeCondition(e, 'condition', idx); })}
                >
                  <Select.Option value="eq">等于</Select.Option>
                  <Select.Option value="neq">不等于</Select.Option>
                  <Select.Option value="include">包含</Select.Option>
                  <Select.Option value="exclude">不包含</Select.Option>
                  <Select.Option value="lt">小于</Select.Option>
                  <Select.Option value="gt">大于</Select.Option>
                </Select>
                <Input
                  value={x.value}
                  style={{ width: 'calc(100% - 244px)' }}
                  onChange={((e) => { return this.onChangeCondition(e, 'value', idx); })}
                />
                <Button
                  icon={<PlusOutlined />}
                  style={{ width: 32 }}
                  onClick={() => { return this.onAddCondition(idx); }}
                />
                <Button
                  icon={<MinusOutlined />}
                  style={{ width: 32 }}
                  onClick={() => { return this.onDelCondition(idx); }}
                />
              </Input.Group>
            );
          })
        }
      </>
    );
  }
}
