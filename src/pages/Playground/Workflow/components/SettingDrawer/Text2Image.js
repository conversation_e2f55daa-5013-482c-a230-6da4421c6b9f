import { PlusOutlined } from '@ant-design/icons';
import { InputUpload } from '~/components';
import { Platform } from '~/plugins';
import { Button, Divider, Form, Input, InputNumber, Select, Space } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ToolEditor from '../ToolEditor';

const PROVIDER = {
  'ernie-text2image-v2': '百度指尖作画',
  'wanx-v1': '阿里万象',
  'tencent-aiart': '腾讯AI作画',
  'stable-diffusion-v1.5': 'Stable Diffusion',
  'dall-e-2': 'DALL-E 2',
  'dall-e-3': 'DALL-E 3',
};
const PROVIDER_SIZE_ENUM = {
  'stable-diffusion-v1.5': ['512x512'],
  'ernie-text2image-v2': [
    '512x512', '640x360', '360x640', '1024x1024', '1280x720', '720x1280', '2048x2048', '2560x1440', '1440x2560',
  ],
  'wanx-v1': ['1024x1024', '720x1280', '1280x720'],
  'tencent-aiart': ['768x768', '768x1024', '1024x768'],
  'dall-e-2': ['1024x1024', '1024x1792', '1792x1024'],
  'dall-e-3': ['1024x1024', '1024x1792', '1792x1024'],
};
const PROVIDER_STYLE_ENUM = {
  'wanx-v1': {
    '<photography>': '摄影',
    '<portrait>': '人像写真',
    '<3d cartoon>': '3D卡通',
    '<anime>': '动画',
    '<oil painting>': '油画',
    '<watercolor>': '水彩',
    '<sketch>': '素描',
    '<chinese painting>': '中国画',
    '<flat illustration>': '扁平插画',
    '<auto>': '默认',
  },
  'tencent-aiart': {
    101: '水墨画',
    102: '概念艺术',
    103: '油画1',
    118: '油画2（梵高）',
    104: '水彩画',
    105: '像素画',
    106: '厚涂风格',
    107: '插图',
    108: '剪纸风格',
    109: '印象派1（莫奈）',
    119: '印象派2',
    111: '古典肖像画',
    112: '黑白素描画',
    113: '赛博朋克',
    114: '科幻风格',
    115: '暗黑风格',
    116: '3D',
    117: '蒸汽波',
    201: '日系动漫',
    202: '怪兽风格',
    203: '唯美古风',
    204: '复古动漫',
    301: '游戏卡通手绘',
    401: '通用写实风格',
  },
};
const PROVIDER_QUALITY_ENUM = {
  'dall-e-2': { hd: '高清', standard: '标准' },
  'dall-e-3': { hd: '高清', standard: '标准' },
};
export default class Text2Image extends PureComponent {
  static propTypes = {
    params: PropTypes.object,
    data: PropTypes.object,
    onChange: PropTypes.func,
  }

  state = {
  }

  componentDidMount = () => {

  }

  onAddProvider = () => {
    const { providerName } = this.state;
    if (_.isEmpty(providerName)) return;
    Platform.emit('WORKFLOW_ADD_CONSTANT', { ...this.props.params, [providerName]: '' });
    this.setState({ providerName: '' }, () => {
      this.refInput?.focus();
    });
  }

  onChangeModelName = (e, data) => {
    const obj = _.cloneDeep(data);
    const size = _.head(PROVIDER_SIZE_ENUM[e]);
    const [width, height] = (size || '').split('x');
    obj.llmSetting.extraParams.width = width;
    obj.llmSetting.extraParams.height = height;
    obj.llmSetting.extraParams.style = _.head(_.keys(PROVIDER_STYLE_ENUM[e]));
    this.props.onChange(e, 'modelName', obj);
  }

  onChangeSize = async (e, data) => {
    const [width, height] = e.split('x');
    const obj = _.cloneDeep(data);
    obj.llmSetting.extraParams.width = width;
    setTimeout(async () => {
      await this.props.onChange(height, 'height', obj);
    }, 0);
  }

  renderDropdown = (menu) => {
    return (
      <>
        {menu}
        <Divider style={{ margin: '8px 0' }} />
        <Space style={{ padding: '0 8px 4px' }} >
          <Input
            placeholder=" 请输入"
            ref={(el) => { this.refInput = el; }}
            value={this.state.providerName}
            onChange={(e) => { return this.setState({ providerName: e.target.value }); }}
          />
          <Button type="text" icon={<PlusOutlined />} onClick={() => { return this.onAddProvider(); }}>
            新增
            <span style={{ marginLeft: 20, color: '#ccc', fontSize: 14 }}>
              新增后,自动添加至工作流常量
            </span>
          </Button>
        </Space>
      </>
    );
  }

  renderExtraItems = (extraParams, data) => {
    let content = [];
    switch (extraParams?.modelName) {
      case 'ernie-text2image-v2':
        content = [
          this.renderImageNum(extraParams, data),
          this.renderReferImage(extraParams, data),
        ];
        break;
      case 'wanx-v1':
        content = [
          this.renderImageNum(extraParams, data),
          this.renderStyle(extraParams, data),
        ];
        break;
      case 'tencent-aiart':
        content = [
          this.renderNegativePrompt(extraParams, data),
          this.renderStyle(extraParams, data),
        ];
        break;
      case 'stable-diffusion-v1.5':
        content = [
          this.renderImageNum(extraParams, data),
          this.renderNegativePrompt(extraParams, data),
        ];
        break;
      case 'dall-e-2':
      case 'dall-e-3':
        content = [
          this.renderImageNum(extraParams, data),
          this.renderQuality(extraParams, data),
        ];
        break;
      default:
        break;
    }

    return content;
  }

  renderStyle = (extraParams, data) => {
    const styles = PROVIDER_STYLE_ENUM[extraParams?.modelName] || [];
    if (_.isEmpty(styles)) return null;
    return (
      <Form.Item label="风格">
        <Select
          value={extraParams?.style}
          onChange={(e) => { return this.props.onChange(e, 'style', data); }}
        >
          {_.map(styles, (v, k) => { return <Select.Option value={k}>{v}</Select.Option>; })}
        </Select>
      </Form.Item>
    );
  }

  renderQuality = (extraParams, data) => {
    const quality = PROVIDER_QUALITY_ENUM[extraParams?.modelName] || [];
    if (_.isEmpty(quality)) return null;
    return (
      <Form.Item label="质量">
        <Select
          value={extraParams?.style}
          onChange={(e) => { return this.props.onChange(e, 'quality', data); }}
        >
          {_.map(quality, (v, k) => { return <Select.Option value={k}>{v}</Select.Option>; })}
        </Select>
      </Form.Item>
    );
  }

  renderImageNum = (extraParams, data) => {
    return (
      <Form.Item label="出图数">
        <InputNumber
          min={1}
          max={4}
          value={extraParams?.imageNum}
          onChange={(e) => { return this.props.onChange(e, 'imageNum', data); }}
        />
      </Form.Item>
    );
  }

  renderReferImage = (extraParams, data) => {
    return (
      <>
        <Form.Item label="参考图">
          <InputUpload
            accept="image/*"
            url={extraParams?.imageUrl}
            onChange={(e) => { return this.props.onChange(e, 'imageUrl', data); }}
          />
        </Form.Item>
        <Form.Item label="影响因子" help="参考图片影响因子 1-10 内；数值越大参考图影响越大">
          <InputNumber
            min={1}
            max={10}
            value={extraParams?.imageChangeDegree}
            onChange={(e) => { return this.props.onChange(e, 'imageChangeDegree', data); }}
          />
        </Form.Item>
      </>
    );
  }

  renderNegativePrompt = (extraParams, data) => {
    return (
      <Form.Item label="反向提示词">
        <ToolEditor
          key="text"
          editorId="text"
          value={extraParams?.negativePrompt}
          datas={[]}
          controls={data?.controls || []}
          params={this.props.params}
          onChange={(e) => { return this.props.onChange(e, 'negativePrompt', data); }}
        />
      </Form.Item>
    );
  }

  render = () => {
    const { data, params } = this.props;
    const { extraParams } = data?.llmSetting;
    const size = `${extraParams.width || 1024}x${extraParams.height || 1024}`;
    const sizeOpts = PROVIDER_SIZE_ENUM[extraParams?.modelName];

    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label="服务">
          <Select
            value={extraParams?.modelName}
            dropdownRender={this.renderDropdown}
            onChange={(e) => { return this.onChangeModelName(e, data); }}
          >
            {_.map(PROVIDER, (v, k) => { return <Select.Option value={k}>{v}</Select.Option>; })}
            {
              !_.isEmpty(params) &&
              <Select.OptGroup key="自定义" label="自定义">
                {
                  _.keys(params).map((x) => {
                    return <Select.Option value={`{{${x}}}`}>{`{{${x}}}`}</Select.Option>;
                  })
                }
              </Select.OptGroup>
            }
          </Select>
        </Form.Item>
        <Form.Item label="提示词">
          <ToolEditor
            key="text"
            editorId="text"
            value={extraParams?.prompt}
            datas={[]}
            controls={data?.controls || []}
            params={params}
            onChange={(e) => { return this.props.onChange(e, 'prompt', data); }}
          />
        </Form.Item>
        {
          !_.isUndefined(sizeOpts) &&
          <Form.Item label="尺寸">
            <Select
              value={size}
              onChange={(e) => { return this.onChangeSize(e, data); }}
            >
              {sizeOpts.map((x) => { return <Select.Option value={x}>{x}</Select.Option>; })}
            </Select>
          </Form.Item>
        }

        {this.renderExtraItems(extraParams, data)}
      </Form>
    );
  }
}
