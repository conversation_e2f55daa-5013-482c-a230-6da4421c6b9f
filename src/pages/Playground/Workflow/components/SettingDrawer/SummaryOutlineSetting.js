import { Form, InputNumber, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ModelSelect from '../ModelSelect';
import ToolEditor from '../ToolEditor';

export default class SummaryOutlineSetting extends PureComponent {
  static propTypes = {
    data: PropTypes.object,
    params: PropTypes.object,
    libraries: PropTypes.array,
    onChange: PropTypes.func,
  }

  onChangeLLMValue = async (e, key, data, topKey) => {
    const obj = _.cloneDeep(data?.llmSetting?.extraParams?.[topKey]);
    obj[key] = e;
    this.props.onChange(obj, topKey, data);
  }

  renderLLMSetting = (data, key = 'summaryLlmSetting') => {
    const obj = data?.llmSetting?.extraParams?.[key] || {};
    return (
      <>
        <Form.Item label="模型">
          <ModelSelect
            params={this.props.params}
            value={obj?.model}
            onChange={(e) => { return this.onChangeLLMValue(e, 'model', data, key); }}
          />
        </Form.Item>
        <Form.Item label="温度">
          <InputNumber
            min={0}
            value={obj?.temperature}
            onChange={(e) => { return this.onChangeLLMValue(e, 'temperature', data, key); }}
          />
        </Form.Item>
        <Form.Item label="最大长度">
          <InputNumber
            min={0}
            value={obj?.maxTokens}
            onChange={(e) => { return this.onChangeLLMValue(e, 'maxTokens', data, key); }}
          />
        </Form.Item>
        <Form.Item label="提示词">
          <ToolEditor
            key="materialExtractLlmSettingPrompt"
            editorId="materialExtractLlmSettingPrompt"
            value={obj?.prompt}
            datas={[]}
            controls={data?.controls || []}
            params={this.props.params}
            onChange={(e) => { return this.onChangeLLMValue(e, 'prompt', data, key); }}
          />
        </Form.Item>
      </>
    );
  }

  render = () => {
    const { data, libraries } = this.props;

    return (
      <>
        <Form.Item label="观点知识库">
          <Select
            value={data?.llmSetting?.extraParams?.viewLibraryId}
            onChange={(e) => { return this.props.onChange(e, 'viewLibraryId', data); }}
          >
            {(libraries || []).map((x) => { return <Select.Option value={x.id}>{x.field}</Select.Option>; })}
          </Select>
        </Form.Item>
        <Form.Item label="大纲知识库">
          <Select
            value={data?.llmSetting?.extraParams?.outlineLibraryId}
            onChange={(e) => { return this.props.onChange(e, 'outlineLibraryId', data); }}
          >
            {(libraries || []).map((x) => { return <Select.Option value={x.id}>{x.field}</Select.Option>; })}
          </Select>
        </Form.Item>
        {this.renderLLMSetting(data)}
      </>
    );
  }
}
