import { PlusOutlined } from '@ant-design/icons';
import { ObjectExtension, Platform, StringExtension } from '~/plugins';
import { Button, Divider, Form, Input, InputNumber, Radio, Select, Space, Switch } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import { BASE_NODES, CONTROL_NODES, TOOL_NODES } from '../CustomNodes/Configs';
import ToolEditor from '../ToolEditor';
import AdSetting from './AdSetting';
import ArticleMaterialExtractSetting from './ArticleMaterialExtractSetting';
import DecisionSetting from './DecisionSetting';
import ExcelProcessor from './ExcelProcessor';
import GatherFromChatSetting from './GatherFromChatSetting';
import IframeSetting from './IframeSetting';
import ImageStyled from './ImageStyled';
import MpArticlePublisher from './MpPublisher';
import Publisher from './Publisher';
import PythonInterpreter from './PythonInterpreter';
import SaveDatabase from './SaveDatabase';
import SaveDataSetting from './SaveDataSetting';
import SummaryOutlineSetting from './SummaryOutlineSetting';
import Text2Image from './Text2Image';
import TransformContentSetting from './TransformContentSetting';
import VideoOCRFixSetting from './VideoOCRFixSetting';
import WeworkKfSender from './WeworkKfSender';
import XiaoyuzhouPublisher from './XYZPublisher';

const TTS_ENUM = require('~/resources/tts-templates.json');

const LOOP_N_TIPS = [
  '1. 被循环节点文本中，可以使用变量 {{ __loop_index__ }} 获取当前是第几次循环',
  '2. 在Python代码中，可使用 context[\'workflow_run_data\'][\'__loop_index__\'] 获取当前循环次数',
];

const MESSAGE_ENUM = {
  text: '内容',
  conversationId: '会话ID',
  rpaRoomId: '聊天ID',
  rpaChatId: '会话ID',
  rpaMessageReceiver: '接收者',
  rpaMessageSender: '发送者',
  weworkRoomId: '企微群ID',
};
const PUBLISH_ENUM = {
  accounts: { label: '账号', key: 'accounts', mode: 'multiple' },
  templates: { label: '模版', key: 'templateId', mode: undefined },
  libraries: { label: '文库', key: 'articleLibraryId', mode: undefined },
};
export default class CommonNodeSetting extends PureComponent {
  static propTypes = {
    type: PropTypes.string,
    data: PropTypes.object,
    params: PropTypes.object,
    flowData: PropTypes.object,
    originData: PropTypes.object,
    nodes: PropTypes.array,
    onChange: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    nodeMap: {},
  }

  componentDidMount = () => {
    const nodeMap = { ...BASE_NODES, ...CONTROL_NODES, ...TOOL_NODES };
    this.props.nodes.forEach((x) => { nodeMap[x.id] = { nodeName: x.name }; });
    this.setState({ nodeMap });
    setTimeout(() => { this.setState({ nodeMap }); }, 300);
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.props.onChange({ node: { ...this.props.data, [key]: value } });
  }

  onAddProvider = () => {
    const { providerName } = this.state;
    if (_.isEmpty(providerName)) return;
    Platform.emit('WORKFLOW_ADD_CONSTANT', { ...this.props.params, [providerName]: '' });
    this.setState({ providerName: '' }, () => {
      this.refInput?.focus();
    });
  }

  onChangeSwitchCase = (e, idx) => {
    const datas = _.cloneDeep(this.props.data?.switchConditions);
    datas[idx] = { ...datas[idx], conditions: e };
    this.onChangeValue(datas, 'switchConditions');
  }

  onChangeInputTransform = (e, key) => {
    const value = e?.target ? e.target.value : e;
    let { inputTransform } = this.props.data;
    if (_.endsWith(key, 'AsStr')) {
      const params = { [key]: value };
      const otherKey = key === 'toListAsStr' ? 'toDictAsStr' : 'toListAsStr';
      params[otherKey] = !value;
      inputTransform = { ...inputTransform, ...params };
    } else {
      inputTransform[key] = value;
    }

    this.onChangeValue(inputTransform, 'inputTransform');
  }

  onChangeSampleJson = ({ flowData }) => {
    const obj = {};
    for (const item of flowData) {
      obj[item.key || ''] = item.value || '';
    }
    this.onChangeInputTransform(obj, 'sampleJson');
  }

  onChangeSaveFlowParams = ({ flowData }) => {
    const obj = {};
    for (const item of flowData) {
      const key = StringExtension.camelToSnake(item.key);
      obj[key || ''] = item.value || '';
    }

    this.onChangeValue({ sampleJson: obj }, 'saveFlowParams');
  }

  onChangeWebhookParams = ({ flowData }, data, key) => {
    const obj = {};
    for (const item of flowData) {
      obj[item.key || ''] = item.value || '';
    }
    this.onChangeExtraParams(obj, key, data);
  }

  onChangeExtraParams = (e, key, data) => {
    const extraParams = _.cloneDeep(data?.llmSetting?.extraParams) || {};
    const value = e?.target ? e.target.value : e;
    extraParams[key] = value;
    this.onChangeValue({ extraParams }, 'llmSetting');
  };

  onChangeChunkSetting = (e, key, data) => {
    const chunkSetting = _.cloneDeep(data?.llmSetting?.chunkSetting) || {};
    const value = e?.target ? e.target.value : e;
    chunkSetting[key] = value;
    this.onChangeValue({ chunkSetting }, 'llmSetting');
  }

  onChangeSendData = (e, key, data, type) => {
    const params = _.cloneDeep(data[type]) || {};
    const value = e?.target ? e.target.value : e;
    params[key] = value;
    this.onChangeValue(params, type);
  }

  onChangeTtsProvider = (e, data) => {
    const extraParams = _.cloneDeep(data?.llmSetting?.extraParams) || {};
    const provider = e?.target ? e.target.value : e;
    extraParams.provider = provider;
    extraParams.voice = '';
    this.onChangeValue({ extraParams }, 'llmSetting');
  }

  renderNameItem = (data) => {
    return (
      <Form.Item label="名称">
        <Input value={data?.name} onChange={(e) => { return this.onChangeValue(e, 'name'); }} />
      </Form.Item>
    );
  }

  renderPythonEvaluator = (data = {}) => {
    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label="代码">
          <Input.TextArea
            autoSize={{ minRows: 8 }}
            value={data?.sourceCode}
            onChange={(e) => { return this.onChangeValue(e, 'sourceCode'); }}
          />
        </Form.Item>
      </Form>
    );
  }

  renderPythonInterpreter = (data = {}) => {
    return <PythonInterpreter data={data} onChange={this.onChangeValue} />;
  }

  renderLoopN = (data) => {
    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label="循环次数">
          <Input
            value={data?.loopTimes}
            onChange={(e) => { return this.onChangeValue(e, 'loopTimes'); }}
          />
        </Form.Item>
        <Form.Item label="终止条件">
          <DecisionSetting
            conditions={data?.conditions}
            onChange={({ conditions }) => { return this.onChangeValue(conditions, 'conditions'); }}
          />
        </Form.Item>
        <Form.Item label="注">{LOOP_N_TIPS.map((x) => { return <b>{x}<br /></b>; })}</Form.Item>
      </Form>
    );
  }

  renderSwitchCase = (data) => {
    const { nodeMap } = this.state;

    return (
      <Form labelCol={{ span: 2 }}>
        {
          (data?.switchConditions || [{}]).map((x, idx) => {
            if (x.type === 'default') return null;
            return (
              <Form.Item label={nodeMap[x.output]?.nodeName || nodeMap[_.head(x.output.split('-'))]?.nodeName}>
                <DecisionSetting
                  conditions={x?.conditions}
                  onChange={({ conditions }) => { return this.onChangeSwitchCase(conditions, idx); }}
                />
              </Form.Item>
            );
          })
        }
      </Form>
    );
  }

  renderSaveData = (data) => {
    if (_.isUndefined(this.props.flowData)) return null;
    return (
      <SaveDataSetting
        node={data}
        data={this.props.flowData}
        params={this.props.params}
        onChange={(e) => { return this.props.onChange(e); }}
      />
    );
  }

  renderDbInsert = (data) => {
    return (
      <SaveDatabase
        data={data}
        params={this.props.params}
        onChange={(e) => { return this.onChangeValue(e, 'llmSetting'); }
        }
      />
    );
  }
  renderPublisher = (data) => {
    return (
      <Publisher
        data={data}
        params={this.props.params}
        onChange={(e) => { return this.onChangeValue(e, 'llmSetting'); }
        }
      />
    );
  }

  renderJsonExtractor = (data) => {
    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label="JSON路径">
          <Input
            value={data?.jsonPath}
            onChange={(e) => { return this.onChangeValue(e, 'jsonPath'); }}
          />
        </Form.Item>
      </Form>
    );
  }

  renderAudioFormat = (value, data, options) => {
    return (
      <Form.Item label="音频格式">
        <Select
          options={options}
          value={value}
          onChange={(e) => { return this.onChangeExtraParams(e, 'audioFormat', data); }}
        />
      </Form.Item>
    );
  }

  renderTtsProvider = (value, data, options) => {
    return (
      <Form.Item label="音频来源">
        <Select
          options={options}
          value={value}
          onChange={(e) => { return this.onChangeTtsProvider(e, data); }}
        />
      </Form.Item>
    );
  }

  renderSleep = (data) => {
    return (
      <Form.Item label="时间">
        <InputNumber
          min={0}
          addonAfter="秒"
          value={data?.llmSetting?.extraParams?.duration}
          onChange={(e) => { return this.onChangeExtraParams(e, 'duration', data); }}
        />
      </Form.Item>
    );
  }

  renderSaveVector = (data) => {
    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label="分割标记">
          <InputNumber
            value={data?.llmSetting?.extraParams?.splitToken}
            onChange={(e) => { return this.onChangeExtraParams(e, 'splitToken', data); }}
          />
        </Form.Item>
        <Form.Item label="知识库">
          <Select
            value={data?.llmSetting?.extraParams?.libraryId}
            onChange={(e) => { return this.onChangeExtraParams(e, 'libraryId', data); }}
          >
            {
              this.props?.originData?.libraries.map((x) => {
                return <Select.Option value={x.id}>{x.field}</Select.Option>;
              })
            }
          </Select>
        </Form.Item>
        <Form.Item label="元数据">
          <SaveDataSetting
            node={data}
            params={this.props.params}
            data={ObjectExtension.objToArray(data?.llmSetting?.extraParams?.metadata)}
            onChange={(e) => { return this.onChangeWebhookParams(e, data, 'metadata'); }}
          />
        </Form.Item>
        {
          _.map({ question: '问题', answer: '答案', source: '唯一来源' }, (label, key) => {
            return (
              <Form.Item label={label}>
                <ToolEditor
                  key={key}
                  editorId={key}
                  controls={data?.controls || []}
                  datas={[]}
                  params={this.props.params}
                  value={data?.llmSetting?.extraParams[key]}
                  onChange={(e) => { return this.onChangeExtraParams(e, key, data); }}
                />
              </Form.Item>
            );
          })
        }
      </Form>
    );
  }

  renderSaveFeedback = (data) => {
    return (
      <Form labelCol={{ span: 2 }}>
        {
          [_.map(
            {
              attitude: { label: '意见', opts: ['同意', '不同意', ''] },
              userRole: { label: '身份', opts: ['user', 'moderator'] },
            },
            (x, key) => {
              return (
                <Form.Item label={x.label}>
                  <Select
                    value={data?.llmSetting?.extraParams[key]}
                    onChange={(e) => { return this.onChangeExtraParams(e, key, data); }}
                  >
                    {x.opts.map((o) => { return <Select.Option value={o}>{o || '未知'}</Select.Option>; })}
                  </Select>
                </Form.Item>
              );
            },
          ),
          _.map(
            {
              question: '问题',
              target: '目标',
              content: '内容',
              userId: '用户ID',
              courseId: '课程ID',
              sourceUrl: '内容来源网址',
            },
            (label, key) => {
              return (
                <Form.Item label={label}>
                  <ToolEditor
                    key={key}
                    editorId={key}
                    controls={data?.controls || []}
                    datas={[]}
                    params={this.props.params}
                    value={data?.llmSetting?.extraParams[key]}
                    onChange={(e) => { return this.onChangeExtraParams(e, key, data); }}
                  />
                </Form.Item>
              );
            },
          )]
        }
      </Form>
    );
  }

  renderInputTransform = (data) => {
    const { inputTransform } = data;

    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label="类型">
          <Radio.Group
            value={inputTransform?.toListAsStr}
            onChange={(e) => { return this.onChangeInputTransform(e, 'toListAsStr'); }}
          >
            <Radio value>转List</Radio>
          </Radio.Group>
          <Radio.Group
            value={inputTransform?.toDictAsStr}
            onChange={(e) => { return this.onChangeInputTransform(e, 'toDictAsStr'); }}
          >
            <Radio value>转Dict</Radio>
          </Radio.Group>
        </Form.Item>
        {
          inputTransform?.toDictAsStr &&
          <Form.Item label="Dict内容">
            <SaveDataSetting
              node={data}
              params={this.props.params}
              data={ObjectExtension.objToArray(inputTransform?.sampleJson)}
              onChange={(e) => { return this.onChangeSampleJson(e); }}
            />
          </Form.Item>
        }
      </Form>
    );
  }

  renderWebhook = (data, paramsObj = { label: '参数', key: 'params' }) => {
    const { url, retryTimes, skipWhenException } = data?.llmSetting?.extraParams;
    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label="URL">
          <ToolEditor
            key="url"
            editorId="url"
            value={url}
            datas={[]}
            controls={data?.controls || []}
            params={this.props.params}
            onChange={(e) => { return this.onChangeExtraParams(e, 'url', data); }}
          />
        </Form.Item>
        <Form.Item label="重试次数">
          <InputNumber
            value={retryTimes}
            onChange={(e) => { return this.onChangeExtraParams(e, 'retryTimes', data); }}
          />
        </Form.Item>
        <Form.Item label="跳过异常">
          <Switch
            checked={skipWhenException}
            onChange={(e) => { return this.onChangeExtraParams(e, 'skipWhenException', data); }}
          />
        </Form.Item>
        <Form.Item label={paramsObj?.label}>
          <SaveDataSetting
            node={data}
            params={this.props.params}
            data={ObjectExtension.objToArray(data?.llmSetting?.extraParams[paramsObj?.key])}
            onChange={(e) => { return this.onChangeWebhookParams(e, data, paramsObj?.key); }}
          />
        </Form.Item>
      </Form>
    );
  }

  renderOpenapi = (data) => {
    const { method, body } = data?.llmSetting?.extraParams;
    return (
      <>
        {this.renderWebhook(data, { label: 'Header', key: 'headers' })}
        <Form labelCol={{ span: 2 }}>
          <Form.Item label="Method">
            <Select
              value={method}
              onChange={(e) => { return this.onChangeExtraParams(e, 'method', data); }}
            >
              {
                ['GET', 'POST', 'PUT', 'DELETE'].map((o) => {
                  return <Select.Option value={o}>{o}</Select.Option>;
                })
              }
            </Select>
          </Form.Item>
          <Form.Item label="Body">
            <ToolEditor
              key="body"
              editorId="body"
              controls={data?.controls || []}
              datas={[]}
              params={this.props.params}
              value={body}
              onChange={(e) => { return this.onChangeExtraParams(e, 'body', data); }}
            />
          </Form.Item>
        </Form>
      </>
    );
  }

  renderExtractText = (data) => {
    const { chunkSetting } = data.llmSetting || {};
    return (
      <>
        <Form.Item label="是否分段">
          <Switch
            checked={chunkSetting?.enabled}
            onChange={(e) => { return this.onChangeChunkSetting(e, 'enabled', data); }}
          />
        </Form.Item>
        {
          chunkSetting?.enabled &&
          <>
            <Form.Item label="分段大小">
              <InputNumber
                min={200}
                max={9999}
                value={chunkSetting?.chunkSize}
                onChange={(e) => { return this.onChangeChunkSetting(e, 'chunkSize', data); }}
              />
            </Form.Item>
            <Form.Item label="交叠大小">
              <InputNumber
                min={0}
                max={999}
                value={chunkSetting?.chunkOverlap}
                onChange={(e) => { return this.onChangeChunkSetting(e, 'chunkOverlap', data); }}
              />
            </Form.Item>
          </>
        }
        <Form.Item label="说明">从 pdf, docx, png, gif, jpeg 等文件中读取正文内容并返回</Form.Item>
        <Form.Item label="服务商" help="图片OCR服务商">
          <Select
            value={data?.llmSetting?.extraParams?.model}
            onChange={(e) => { return this.onChangeExtraParams(e, 'model', data); }}
          >
            <Select.Option value="ali">阿里</Select.Option>
            <Select.Option value="tencent">腾讯</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label="图片类型" help="通用Or手写">
          <Select
            value={data?.llmSetting?.extraParams?.action}
            onChange={(e) => { return this.onChangeExtraParams(e, 'action', data); }}
          >
            <Select.Option value="basic">通用</Select.Option>
            <Select.Option value="handWriting">手写</Select.Option>
          </Select>
        </Form.Item>
      </>
    );
  }

  renderSender = (data, dataKey) => {
    return (
      <>
        {
          _.map(data[dataKey], (v, k) => {
            return (
              <Form.Item label={MESSAGE_ENUM[k]}>
                <ToolEditor
                  key={k}
                  editorId={k}
                  value={v}
                  datas={[]}
                  controls={data?.controls || []}
                  params={this.props.params}
                  onChange={(e) => { return this.onChangeSendData(e, k, data, dataKey); }}
                />
              </Form.Item>
            );
          })
        }
      </>
    );
  }

  renderWeworkKfSender = (data) => {
    return (
      <WeworkKfSender
        data={data}
        onChange={this.onChangeExtraParams}
        params={this.props.params}
        onChangeParams={(e) => { return this.onChangeValue(e, 'transformContentParams'); }}
      />
    );
  }

  renderWhatsappSender = (data) => {
    return (
      <TransformContentSetting
        data={data}
        params={this.props.params}
        onChange={(e) => { return this.onChangeValue(e, 'transformContentParams'); }}
      />
    );
  }

  renderASRDropdown = (menu) => {
    return (
      <>
        {menu}
        <Divider style={{ margin: '8px 0' }} />
        <Space style={{ padding: '0 8px 4px' }} >
          <Input
            placeholder=" 请输入"
            ref={(el) => { this.refInput = el; }}
            value={this.state.providerName}
            onChange={(e) => { return this.setState({ providerName: e.target.value }); }}
          />
          <Button type="text" icon={<PlusOutlined />} onClick={() => { return this.onAddProvider(); }}>
            新增
            <span style={{ marginLeft: 20, color: '#ccc', fontSize: 14 }}>
              新增后,自动添加至工作流常量
            </span>
          </Button>
        </Space>
      </>
    );
  }

  renderUrlEditor = (data, key, type) => {
    const { extraParams } = data?.llmSetting;

    return (
      <>
        <Form.Item label="链接">
          <ToolEditor
            key={key}
            editorId={key}
            value={extraParams[key]}
            datas={[]}
            controls={data?.controls || []}
            params={this.props.params}
            onChange={(e) => { return this.onChangeExtraParams(e, key, data); }}
          />
        </Form.Item>
        {
          type === 'asr' &&
          <Form.Item label="服务">
            <Select
              value={extraParams?.provider}
              onChange={(e) => { return this.onChangeExtraParams(e, 'provider', data); }}
              dropdownRender={this.renderASRDropdown}
            >
              <Select.Option value="aliyun">阿里</Select.Option>
              <Select.Option value="tencent">腾讯</Select.Option>
              <Select.Option value="paraformer">灵积</Select.Option>
              {
                !_.isEmpty(this.props.params) &&
                <Select.OptGroup key="自定义" label="自定义">
                  {
                    _.keys(this.props.params).map((x) => {
                      return <Select.Option value={`{{${x}}}`}>{`{{${x}}}`}</Select.Option>;
                    })
                  }
                </Select.OptGroup>
              }
            </Select>
          </Form.Item>
        }
        {
          key === 'rssUrl' &&
          <Form.Item label="内容" help="仅获取新内容: 依据rss的pubDate获取新条目">
            <Radio.Group
              value={extraParams?.fetchAll}
              onChange={(e) => { return this.onChangeExtraParams(e, 'fetchAll', data); }}
            >
              <Radio value>全部获取</Radio>
              <Radio value={false}>仅获取新内容</Radio>
            </Radio.Group>
          </Form.Item>
        }
        {
          type === 'video2audio' &&
          this.renderAudioFormat(
            extraParams?.audioFormat,
            data,
            [
              { label: 'mp3', value: 'mp3' },
              { label: 'm4a', value: 'm4a' },
              { label: 'aac', value: 'aac' },
              { label: 'wav', value: 'wav' },
            ],
          )
        }
      </>
    );
  }

  renderTts = (data) => {
    const { text, volume, voice, audioFormat, speechRate, pitchRate, provider } = data?.llmSetting?.extraParams;
    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label="文本">
          <ToolEditor
            key="text"
            editorId="text"
            value={text}
            datas={[]}
            controls={data?.controls || []}
            params={this.props.params}
            onChange={(e) => { return this.onChangeExtraParams(e, 'text', data); }}
          />
        </Form.Item>
        {this.renderTtsProvider(
          provider || 'aliyun',
          data,
          [{ label: '阿里云', value: 'aliyun' }, { label: '微软', value: 'azure' }],
        )}
        <Form.Item label="声音">
          <Select
            showSearch
            filterOption={(input, option) => { return option.children.includes(input); }}
            value={voice}
            onChange={(e) => { return this.onChangeExtraParams(e, 'voice', data); }}
          >
            {TTS_ENUM?.[provider || 'aliyun'].map((w) => {
              return <Select.Option value={w.voice}>{w?.name}</Select.Option>;
            })}
          </Select>
        </Form.Item>
        {this.renderAudioFormat(audioFormat, data, [{ label: 'mp3', value: 'mp3' }, { label: 'silk', value: 'silk' }])}
        <Form.Item label="音量">
          <InputNumber
            min={0}
            max={100}
            value={volume}
            onChange={(e) => { return this.onChangeExtraParams(e, 'volume', data); }}
          />
        </Form.Item>
        <Form.Item label="速度">
          <InputNumber
            min={-500}
            max={500}
            value={speechRate}
            onChange={(e) => { return this.onChangeExtraParams(e, 'speechRate', data); }}
          />
        </Form.Item>
        <Form.Item label="音调">
          <InputNumber
            min={-500}
            max={500}
            value={pitchRate}
            onChange={(e) => { return this.onChangeExtraParams(e, 'pitchRate', data); }}
          />
        </Form.Item>
      </Form>
    );
  }

  renderText2video = (data) => {
    const { extraParams } = data?.llmSetting;
    const { publishInfo } = this.props?.originData;
    return (
      <>
        {
          _.map({ title: '标题', content: '内容' }, (v, k) => {
            return (
              <Form.Item label={v}>
                <ToolEditor
                  key={k}
                  editorId={k}
                  value={extraParams[k]}
                  datas={[]}
                  controls={data?.controls || []}
                  params={this.props.params}
                  onChange={(e) => { return this.onChangeExtraParams(e, k, data); }}
                />
              </Form.Item>
            );
          })
        }
        {
          _.map(PUBLISH_ENUM, (v, k) => {
            return (
              <Form.Item label={v.label}>
                <Select
                  mode={v.mode}
                  value={extraParams[v.key]}
                  onChange={(e) => { return this.onChangeExtraParams(e, v.key, data); }}
                >
                  {
                    _.map(publishInfo[k], (x) => {
                      return <Select.Option value={x.id}>{x.author || x.name || x.nickname}</Select.Option>;
                    })
                  }
                </Select>
              </Form.Item>
            );
          })
        }
      </>
    );
  }

  renderText2image = (data) => {
    return <Text2Image data={data} onChange={this.onChangeExtraParams} params={this.props.params} />;
  }

  renderImageStyled = (data) => {
    return <ImageStyled data={data} onChange={this.onChangeExtraParams} params={this.props.params} />;
  }

  renderExcelProcessor = (data) => {
    return <ExcelProcessor data={data} onChange={this.onChangeExtraParams} params={this.props.params} />;
  }

  renderMpArticlePublisher = (data) => {
    return <MpArticlePublisher data={data} onChange={this.onChangeExtraParams} params={this.props.params} />;
  }

  renderXiaoyuzhouPublisher = (data) => {
    return <XiaoyuzhouPublisher data={data} onChange={this.onChangeExtraParams} params={this.props.params} />;
  }

  renderVideoOcrFix = (data) => {
    return <VideoOCRFixSetting data={data} onChange={this.onChangeExtraParams} params={this.props.params} />;
  }

  renderArticleMaterialExtract = (data) => {
    return (
      <ArticleMaterialExtractSetting
        data={data}
        libraries={this.props.originData.libraries || []}
        onChange={this.onChangeExtraParams}
        params={this.props.params}
      />);
  }

  renderArticleMaterialSearchAndMerge = (data) => {
    return (
      <ArticleMaterialExtractSetting
        isSearch
        data={data}
        libraries={this.props.originData.libraries || []}
        onChange={this.onChangeExtraParams}
        params={this.props.params}
      />);
  }

  renderSummaryOutline = (data) => {
    return (
      <SummaryOutlineSetting
        data={data}
        libraries={this.props.originData.libraries || []}
        onChange={this.onChangeExtraParams}
        params={this.props.params}
      />);
  }

  renderAdSetting = (data) => {
    return (<AdSetting
      data={data}
      type={this.props.type}
      onChange={(e) => { return this.onChangeValue(e, 'llmSetting'); }}
    />);
  }

  renderGatherFromChat = (data) => {
    return (
      <GatherFromChatSetting
        data={data}
        type={this.props.type}
        params={this.props.params}
        onChange={(e) => { return this.onChangeValue(e, 'llmSetting'); }}
      />
    );
  }

  renderSaveFlowParam = (data) => {
    return (
      <Form labelCol={{ span: 2 }}>
        <SaveDataSetting
          node={data}
          params={this.props.params}
          data={ObjectExtension.objToArray(data?.saveFlowParams?.sampleJson)}
          onChange={(e) => { return this.onChangeSaveFlowParams(e, 'sampleJson'); }}
        />
      </Form>
    );
  }

  renderTransformContent = (data) => {
    return (
      <TransformContentSetting
        data={data}
        params={this.props.params}
        onChange={(e) => { return this.onChangeValue(e, 'transformContentParams'); }}
      />
    );
  }

  renderTextUserInput = (data) => {
    return (
      <Form.Item label="询问问题">
        <ToolEditor
          key="TextUserInput"
          editorId="TextUserInput"
          value={data?.llmSetting?.content}
          datas={[]}
          controls={data?.controls || []}
          params={this.props.params}
          onChange={(e) => { return this.onChangeValue({ content: e }, 'llmSetting'); }}
        />
      </Form.Item>
    );
  }

  render = () => {
    const { type, data } = this.props;
    if (type === 'demo') {
      return <IframeSetting {...this.props} />;
    }

    if (_.isEmpty(type) || _.isEmpty(this.state.nodeMap)) return null;

    const renderName = `render${_.upperFirst(type)}`;
    let content = _.hasIn(this, renderName) ? this[renderName](data) : null;
    if (_.endsWith(type, 'Sender') && !['weworkKfSender', 'whatsappSender'].includes(type)) {
      content = this.renderSender(data, type.replace('Sender', 'Data'));
    }

    if (['rssReader', 'asr', 'video2audio', 'image2video'].includes(type) || _.endsWith(type, 'Downloader')) {
      let urlType = type === 'asr' ? 'audioUrl' : 'rssUrl';
      if (_.endsWith(type, 'Downloader') || ['video2audio'].includes(type)) {
        urlType = 'videoUrl';
      }
      if (['image2video'].includes(type)) { urlType = 'imageUrl'; }
      content = this.renderUrlEditor(data, urlType, type);
    }
    if (['removeTextAd', 'adTextEvaluate'].includes(type)) {
      content = this.renderAdSetting(data);
    }

    return (
      <Form labelCol={{ span: 2 }}>
        {this.renderNameItem(data)}
        {content}
      </Form>
    );
  }
}
