import { Market } from '~/engine';
import { Form, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ToolEditor from '../ToolEditor';

const PLATFORM_ENUM = {
  toutiao: { name: '头条', params: [{ key: 'title', name: '标题' }, { key: 'content', name: '内容' }] },
  xiaoyuzhou: {
    name: '小宇宙',
    params: [
      { key: 'title', name: '标题' },
      { key: 'description', name: '描述' },
      { key: 'audioUrl', name: '音频地址' },
      { key: 'coverUrl', name: '封面地址' },
    ],
  },
  douyin: {
    name: '抖音',
    params: [
      { key: 'videoUrl', name: '视频地址' },
      { key: 'coverUrl', name: '封面地址' },
      { key: 'description', name: '描述' },
      { key: 'tags', name: '标签(json格式)' },
    ],
  },
};

export default class Publisher extends PureComponent {
  static propTypes = {
    params: PropTypes.object,
    data: PropTypes.object,
    onChange: PropTypes.func,
  }

  state = {
    accounts: [],
    characters: [],
    platform: '',
  }

  componentDidMount = async () => {
    const { characterId, accountId } = this.props.data.llmSetting;
    const { items: characters } = await Market.fetchPublishCharacters({});
    if (!_.isEmpty(`${characterId}`)) {
      await this.onFetchAccounts({ characterId });
      const { platform } = this.state.accounts.find((x) => { return x.id === accountId; });
      this.setState({ platform });
    }
    this.setState({ characters });
  }

  onFetchAccounts = async (params) => {
    const { items: accounts } = await Market.fetchCharacterAccounts(params);
    await this.setState({ accounts });
  }

  onChangeValue = async (e, key) => {
    const value = e?.target ? e?.target.value : e;
    const llmSetting = _.cloneDeep(this.props.data?.llmSetting);
    llmSetting[key] = e?.target ? e?.target.value : e;
    if (key === 'characterId') {
      await this.onFetchAccounts({ characterId: value });
      llmSetting.accountId = '';
      llmSetting.content = {};
      this.setState({ platform: '' });
    }
    if (key === 'accountId') {
      const { platform } = this.state.accounts.find((x) => { return x.id === value; });
      this.setState({ platform });
    }

    await this.props.onChange(llmSetting);
  }

  onChangeContent = async (e, type) => {
    const content = _.cloneDeep(this.props.data?.llmSetting?.content) || {};
    content[type] = e?.target ? e.target.value : e;
    this.onChangeValue(content, 'content');
  }

  renderContent = (content) => {
    const { params } = PLATFORM_ENUM[this.state.platform] || {};
    return (
      <>
        {
          (params || []).map((x) => {
            return (
              <Form.Item label={x.name} >
                <ToolEditor
                  key={`${x.key}`}
                  editorId={`${x.key}`}
                  datas={[]}
                  value={content[x.key]}
                  params={this.props?.params}
                  controls={this.props.data?.controls || []}
                  onChange={(e) => { return this.onChangeContent(e, x.key); }}
                />
              </Form.Item>
            );
          })
        }
      </>
    );
  }

  render = () => {
    const { accountId, characterId, content } = this.props?.data?.llmSetting;

    return (
      <>
        <Form.Item label="角色">
          <Select value={characterId} onChange={(e) => { return this.onChangeValue(e, 'characterId'); }} >
            {
              (this.state.characters || []).map((v) => {
                return <Select.Option value={v.id}>{v.name}</Select.Option>;
              })
            }
          </Select>
        </Form.Item>
        <Form.Item label="账号">
          <Select value={accountId} onChange={(e) => { return this.onChangeValue(e, 'accountId'); }} >
            {
              (this.state.accounts || []).map((v) => {
                return <Select.Option value={v.id}>{v.name}[{PLATFORM_ENUM[v.platform]?.name}]</Select.Option>;
              })
            }
          </Select>
        </Form.Item>
        {this.renderContent(content)}
      </>
    );
  }
}
