import { Clips } from '~/engine';
import { Form, Select, Switch } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ToolEditor from '../ToolEditor';

const EDITOR_KEYS = {
  title: '标题',
  author: '作者',
  digest: '摘要',
  content: '内容',
  contentSourceUrl: '阅读原文链接',
  thumbMediaUrl: '封面链接',
};

export default class MpArticlePublisher extends PureComponent {
  static propTypes = {
    params: PropTypes.object,
    data: PropTypes.object,
    onChange: PropTypes.func,
  }

  state = {
    mpAccounts: {},
  }

  componentDidMount = async () => {
    const mpAccounts = await Clips.fetchMpAccounts();
    const obj = {};
    _.map(mpAccounts, (v) => {
      obj[v.appId] = { name: v.name, mpUuid: v.id };
    });
    this.setState({ mpAccounts: obj });
  }

  onChangeMp = async (e) => {
    const { mpUuid } = this.state.mpAccounts[e];
    await this.props.onChange(e, 'mpAppId', this.props.data);
    await this.props.onChange(mpUuid, 'mpUuid', this.props.data);
  }

  render = () => {
    const { data, params } = this.props;
    const { extraParams } = data?.llmSetting;

    return (
      <>
        <Form.Item label="公众号">
          <Select
            value={extraParams.mpAppId}
            onChange={(e) => { return this.onChangeMp(e); }}
          >
            {
              _.map(this.state.mpAccounts, (v, value) => {
                return <Select.Option value={value}>{v.name}</Select.Option>;
              })
            }
          </Select>
        </Form.Item>
        {
          _.map(EDITOR_KEYS, (v, key) => {
            return (
              <Form.Item label={v}>
                <ToolEditor
                  key={key}
                  editorId={key}
                  value={extraParams[key]}
                  datas={[]}
                  controls={data?.controls || []}
                  params={params}
                  onChange={(e) => { return this.props.onChange(e, key, data); }}
                />
              </Form.Item>
            );
          })
        }
        <Form.Item label="打开评论">
          <Switch
            checked={extraParams?.needOpenComment}
            onChange={(e) => { return this.props.onChange(e, 'needOpenComment', data); }}

          />
        </Form.Item>
        <Form.Item label="仅粉丝评论">
          <Switch
            checked={extraParams?.onlyFansCanComment}
            onChange={(e) => { return this.props.onChange(e, 'onlyFansCanComment', data); }}
          />
        </Form.Item>
      </>
    );
  }
}
