import { Empty, Form } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ToolEditor from '../ToolEditor';

export default class SubflowSetting extends PureComponent {
  static propTypes = {
    params: PropTypes.object,
    node: PropTypes.object,
    subflowParams: PropTypes.array,
    onChange: PropTypes.func,
  }

  state = {
  }

  onChangeValue = (e, key, i) => {
    const value = e?.target ? e.target.value : e;
    const subflowParams = _.cloneDeep(this.props.subflowParams);
    subflowParams[i][key] = value;
    this.props.onChange({ subflowParams });
  }

  render = () => {
    const { node, params, subflowParams } = this.props;
    if (_.isEmpty(subflowParams)) {
      return <Empty description="当前模块暂无配置" />;
    }

    return (
      <Form labelCol={{ span: 4 }}>
        {
          (subflowParams || [{}]).map((obj, idx) => {
            return (
              <Form.Item label={obj.key}>
                <ToolEditor
                  key={`${obj.key}`}
                  editorId={`${obj.key}`}
                  datas={[]}
                  value={obj.value}
                  params={params}
                  controls={node?.controls || []}
                  onChange={(e) => { return this.onChangeValue(e, 'value', idx); }}
                />
              </Form.Item>
            );
          })
        }
      </Form>
    );
  }
}
