/* eslint-disable react/no-array-index-key */
import { DeleteFilled, PlusOutlined } from '@ant-design/icons';
import { Button, Input } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ToolEditor from '../ToolEditor';

export default class SaveDataSetting extends PureComponent {
  static propTypes = {
    data: PropTypes.array,
    params: PropTypes.object,
    node: PropTypes.object,
    onChange: PropTypes.func,
  }

  state = {
  }

  onChangeFlowData = (e, type, idx) => {
    const flowData = _.cloneDeep(this.props.data) || [{}];
    flowData[idx][type] = e?.target ? e.target.value : e;
    this.props.onChange({ flowData });
  }

  onAddFlowData = (idx = 0) => {
    const flowData = _.cloneDeep(this.props.data) || [{}];
    flowData.splice(idx + 1, 0, {});
    this.props.onChange({ flowData });
  }

  onDelFlowData = (idx = 0) => {
    const flowData = _.cloneDeep(this.props.data) || [{}];
    if (flowData?.length === 1) return;
    flowData.splice(idx, 1);
    this.props.onChange({ flowData });
  }

  render = () => {
    const { data, node, params } = this.props;
    return (
      <>
        {
          data.map((x, index) => {
            return (
              <div style={{ marginBottom: 10 }}>
                <div style={{ display: 'flex' }}>
                  <Input
                    addonBefore="Key"
                    value={x.key}
                    onChange={(e) => { return this.onChangeFlowData(e, 'key', index); }}
                  />
                  <span style={{ marginLeft: 30, display: 'flex' }}>
                    <Button
                      icon={<DeleteFilled />}
                      style={{ marginRight: 10 }}
                      onClick={() => { return this.onDelFlowData(index); }}
                    />
                    <Button icon={<PlusOutlined />} onClick={() => { return this.onAddFlowData(index); }} />
                  </span>
                </div>
                <ToolEditor
                  key={`${x.key}-${index}`}
                  editorId={`${x.key}-${index}`}
                  datas={[]}
                  value={x.value}
                  params={params}
                  controls={node?.controls || []}
                  onChange={(e) => { return this.onChangeFlowData(e, 'value', index); }}
                />
              </div>
            );
          })
        }
      </>
    );
  }
}
