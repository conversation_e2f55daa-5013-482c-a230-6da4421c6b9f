import { Divider, Form, InputNumber, Select, Switch } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import ModelSelect from '../ModelSelect';
import ToolEditor from '../ToolEditor';

export default class ArticleMaterialExtractSetting extends PureComponent {
  static propTypes = {
    isSearch: PropTypes.bool,
    data: PropTypes.object,
    params: PropTypes.object,
    libraries: PropTypes.array,
    onChange: PropTypes.func,
  }

  static defaultProps = {
    isSearch: false,
  }

  onChangeMaterialValue = async (e, key, data) => {
    const obj = _.cloneDeep(data?.llmSetting?.extraParams?.materialExtractLlmSetting);
    obj[key] = e;
    this.props.onChange(obj, 'materialExtractLlmSetting', data);
  }

  onChangeMergeValue = async (e, key, data) => {
    const obj = _.cloneDeep(data?.llmSetting?.extraParams?.materialMergeLlmSetting);
    obj[key] = e;
    this.props.onChange(obj, 'materialMergeLlmSetting', data);
  }

  renderMaterialExtractLLMSetting = (data) => {
    if (this.props.isSearch) return null;
    return (
      <>
        <Divider>提取素材</Divider>
        <Form.Item label="提取素材模型">
          <ModelSelect
            params={this.props.params}
            value={data?.llmSetting?.extraParams?.materialExtractLlmSetting?.model}
            onChange={(e) => { return this.onChangeMaterialValue(e, 'model', data); }}
          />
        </Form.Item>
        <Form.Item label="提取素材温度">
          <InputNumber
            min={0}
            value={data?.llmSetting?.extraParams?.materialExtractLlmSetting?.temperature}
            onChange={(e) => { return this.onChangeMaterialValue(e, 'temperature', data); }}
          />
        </Form.Item>
        <Form.Item label="提取素材最大长度">
          <InputNumber
            min={0}
            value={data?.llmSetting?.extraParams?.materialExtractLlmSetting?.maxTokens}
            onChange={(e) => { return this.onChangeMaterialValue(e, 'maxTokens', data); }}
          />
        </Form.Item>
        <Form.Item label="提取素材提示词">
          <ToolEditor
            key="materialExtractLlmSettingPrompt"
            editorId="materialExtractLlmSettingPrompt"
            value={data?.llmSetting?.extraParams?.materialExtractLlmSetting?.prompt}
            datas={[]}
            controls={data?.controls || []}
            params={this.props.params}
            onChange={(e) => { return this.onChangeMaterialValue(e, 'prompt', data); }}
          />
        </Form.Item>
      </>
    );
  }

  renderSearchSetting = (data) => {
    if (!this.props.isSearch) return null;

    return (
      <>
        <Form.Item label="搜索阈值">
          <InputNumber
            min={0}
            value={data?.llmSetting?.extraParams?.searchThreshold}
            onChange={(e) => { return this.props.onChange(e, 'searchThreshold', data); }}
          />
        </Form.Item>
        <Form.Item label="搜索条数">
          <InputNumber
            min={0}
            value={data?.llmSetting?.extraParams?.searchLimit}
            onChange={(e) => { return this.props.onChange(e, 'searchLimit', data); }}
          />
        </Form.Item>
      </>
    );
  }

  render = () => {
    const { data, libraries, isSearch } = this.props;

    return (
      <>
        <Form.Item label="知识库">
          <Select
            value={data?.llmSetting?.extraParams?.libraryId}
            onChange={(e) => { return this.props.onChange(e, 'libraryId', data); }}
          >
            {(libraries || []).map((x) => { return <Select.Option value={x.id}>{x.field}</Select.Option>; })}
          </Select>
        </Form.Item>
        <Form.Item label="提取阈值">
          <InputNumber
            min={0}
            value={data?.llmSetting?.extraParams?.extractThreshold}
            onChange={(e) => { return this.props.onChange(e, 'extractThreshold', data); }}
          />
        </Form.Item>

        <Form.Item label="素材条数">
          <InputNumber
            min={0}
            value={data?.llmSetting?.extraParams?.materialLimit}
            onChange={(e) => { return this.props.onChange(e, 'materialLimit', data); }}
          />
        </Form.Item>
        <Form.Item label="Embedding">
          <ToolEditor
            key="base_embedding"
            editorId="base_embedding"
            datas={[]}
            controls={data?.controls || []}
            params={this.props.params}
            value={data?.llmSetting?.extraParams?.embeddingMethod}
            onChange={(e) => { return this.props.onChange(e, 'embeddingMethod', data); }}
          />
        </Form.Item>
        {
          !isSearch &&
          <Form.Item label="是否整合素材">
            <Switch
              checked={data?.llmSetting?.extraParams?.enableMergeMaterial}
              onChange={(e) => { return this.props.onChange(e, 'enableMergeMaterial', data); }}
            />
          </Form.Item>
        }
        {this.renderMaterialExtractLLMSetting(data)}
        {this.renderSearchSetting(data)}
        {
          (data?.llmSetting?.extraParams?.enableMergeMaterial || isSearch) && (
            <>
              <Divider>整合素材</Divider>
              <Form.Item label="整合素材模型">
                <ModelSelect
                  params={this.props.params}
                  value={data?.llmSetting?.extraParams?.materialMergeLlmSetting?.model}
                  onChange={(e) => { return this.onChangeMergeValue(e, 'model', data); }}
                />
              </Form.Item>
              <Form.Item label="整合素材温度">
                <InputNumber
                  min={0}
                  value={data?.llmSetting?.extraParams?.materialMergeLlmSetting?.temperature}
                  onChange={(e) => { return this.onChangeMergeValue(e, 'temperature', data); }}
                />
              </Form.Item>
              <Form.Item label="整合素材最大长度">
                <InputNumber
                  min={0}
                  value={data?.llmSetting?.extraParams?.materialMergeLlmSetting?.maxTokens}
                  onChange={(e) => { return this.onChangeMergeValue(e, 'maxTokens', data); }}
                />
              </Form.Item>
              <Form.Item label="整合素材提示词">
                <ToolEditor
                  key="materialMergeLlmSettingPrompt"
                  editorId="materialMergeLlmSettingPrompt"
                  value={data?.llmSetting?.extraParams?.materialMergeLlmSetting?.prompt}
                  datas={[]}
                  controls={data?.controls || []}
                  params={this.props.params}
                  onChange={(e) => { return this.onChangeMergeValue(e, 'prompt', data); }}
                />
              </Form.Item>
            </>
          )
        }
      </>
    );
  }
}
