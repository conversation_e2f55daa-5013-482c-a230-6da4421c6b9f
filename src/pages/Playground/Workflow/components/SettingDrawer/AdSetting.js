import { AutoPrompt } from '~/components';
import { Form } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { v4 as uuid } from 'uuid';

import ToolEditor from '../ToolEditor';

export default class AdSetting extends PureComponent {
  static propTypes = {
    type: PropTypes.string,
    params: PropTypes.object,
    data: PropTypes.object,
    onChange: PropTypes.func,
  }

  state = {
    md5Key: uuid(),
  }

  render = () => {
    const { data, params, type } = this.props;
    const { systemPrompt, content } = data?.llmSetting;
    const { md5Key } = this.state;

    return (
      <Form labelCol={{ span: 2 }}>
        <Form.Item label="系统提示词">
          {
            type !== 'removeTextAd' &&
            <>
              <AutoPrompt
                text={systemPrompt}
                onImprove={(text) => {
                  this.props.onChange({ ...data?.llmSetting, systemPrompt: text });
                  this.setState({ md5Key: uuid() });
                }}
              />
              <ToolEditor
                key={md5Key}
                ref={(ref) => { this.refprompt = ref; }}
                editorId="prompt"
                value={systemPrompt}
                datas={[]}
                controls={data?.controls || []}
                params={params}
                onChange={(e) => { return this.props.onChange({ ...data?.llmSetting, systemPrompt: e }); }}
              />
            </>
          }
        </Form.Item>
        <Form.Item label="内容">
          <ToolEditor
            key="content"
            editorId="content"
            value={content}
            datas={[]}
            controls={data?.controls || []}
            params={params}
            onChange={(e) => { return this.props.onChange({ ...data?.llmSetting, content: e }); }}
          />
        </Form.Item>
      </Form>
    );
  }
}
