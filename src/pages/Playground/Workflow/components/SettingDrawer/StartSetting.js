/* eslint-disable max-len */
import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { markdown } from '@codemirror/lang-markdown';
import CodeMirror from '@uiw/react-codemirror';
import { InputUpload, Toast } from '~/components';
import Engine, { ChatBot } from '~/engine';
import { Platform } from '~/plugins';
import { Alert, Button, Col, Divider, Form, Image, Input, Modal, Row, Table } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

const OPEN_API_ENUM = [
  { key: 'webhook', value: 'Webhook推送', apiName: '推送地址' },
  { key: 'dingding', value: '钉钉应用', apiName: '回调地址' },
];

const DING_ENUM = {
  name: '应用名',
  accessKeyId: 'Access Key ID',
  accessKeySecret: 'Access Key Secret',
  robotCode: 'Robot Code',
  aesKey: 'AES Key',
  token: 'Token',
  encryptKey: 'Encrypt Key Value',
};

const PARAMS_KEY_ENUM = {
  webhook: ['open_app', 'key'],
  qiwei: ['rpa_chat_id', 'rpa_message_sender', 'room_id'],
  dingding: ['open_app', 'conversation_id'],
};

const OPEN_AI_JSON = {
  webhook: '{\n    "user_message": "用户输入的聊天消息", //在workflow中通过 "Start" 节点引用\n    "user_id": "xdefdafeef",\n    "webhook_data": {\n        "open_app": "webhook", //内置数据，工作流中通过 {{open_app}} 或者 工作流常量引用使用\n        "key": "value" // 工作流开发者自定义数据，工作流中通过 {{key}} 或者 工作流常量引用使用\n    }\n}',
  qiwei: '{\n    "user_message": "用户输入的聊天消息", //在workflow中通过 "Start" 节点引用\n    "rpa_chat_id": "fdaefdfff", //工作流中，通过 {{rpa_chat_id}} 或者 工作流常量引用使用\n    "rpa_message_sender": "发送消息的微信用户ID", //工作流中，通过 {{rpa_message_sender}} 或者 工作流常量引用使用\n    "room_id": "聊天群的ID"  //工作流中，通过 {{room_id}} 或者 工作流常量引用使用\n}',
  dingding: '{\n    "user_message": "{}", //钉钉推送的事件消息，，参见[官方文档](https://open.dingtalk.com/document/orgapp/custom-bot-to-send-group-chat-messages#efeae6a0deb4o)。在workflow中通过 "Start" 节点引用，解析需要使用 JsonExtract 节点进行字段提取\n    "conversation_id": "fdafefeef" //该条钉钉消息的会话ID，工作流中通过 {{conversation_id}} 或者 工作流常量引用使用\n    "open_app": "feishu", //工作流中，通过 {{open_app}} 或者 工作流常量引用使用\n}\n',
};


export default class StartSetting extends PureComponent {
  static propTypes = {
    params: PropTypes.object,
    onSaveParams: PropTypes.func,
    fetchWorkflowGroups: PropTypes.func,
    createWorkflowGroup: PropTypes.func,
  }

  state = {
    flowId: window.location.pathname.replace('/workflow/', ''),
    columns: [
      { title: '群名', dataIndex: 'roomName', key: 'roomName' },
      {
        title: '二维码',
        dataIndex: 'qrCode',
        key: 'qrCode',
        render: (qrCode) => { return <Image src={qrCode} width={50} />; },
      },
    ],
    dataSource: [],
    ding: {},
    chatId: '',
    chat: {
      bot: {
        name: '',
        description: '',
        avatar: '',
        conversationStart: [''],
      },
      welcome: '',
      oauth_required: false,
    },
  }

  componentDidMount = async () => {
    const { flowId } = this.state;
    const apps = await ChatBot.fetchOpenApps({ workflowUuid: flowId, openApps: 'dingding' });
    const chats = await ChatBot.fetchOpenApps({ workflowUuid: flowId, openApps: 'chat' });
    const items = await this.props.fetchWorkflowGroups({ flowId });
    const { id, params } = _.head(_.values(apps)) || {};
    const { id: chatId, params: chat } = _.head(_.values(chats)) || {};
    this.setState({ dataSource: items, ding: params || {}, dingId: id, chatId, chat: chat || {} });
  }

  onCopyLink = async (text) => {
    await navigator.clipboard.writeText(text);
    Toast.show('复制成功', Toast.Type.SUCCESS);
  }

  onChangeGroup = (e, key) => {
    const { value } = e.target;
    this.setState({ group: { ...this.state.group, [key]: value } });
  }

  onChangeDingValue = (e, key) => {
    this.setState({ ding: { ...this.state.ding, [key]: e.target.value } });
  }

  onChangeChatValue = (e, key, isBot) => {
    const { chat } = this.state;
    const value = e.target ? e.target.value : e;
    if (isBot) {
      if (!chat.bot) chat.bot = {};
      chat.bot[key] = value;
    } else {
      chat[key] = value;
    }
    this.setState({ chat: { ...chat } });
  }

  onAddConversationStart = () => {
    const { chat } = this.state;
    if (!chat.bot) chat.bot = {};
    if (!chat.bot.conversationStart) chat.bot.conversationStart = [];
    chat.bot.conversationStart.push('');
    this.setState({ chat: { ...chat } });
  }

  onDelConversationStart = (idx) => {
    const { chat } = this.state;
    if (!chat.bot) chat.bot = {};
    if (!chat.bot.conversationStart) chat.bot.conversationStart = [];
    chat.bot.conversationStart.splice(idx, 1);
    this.setState({ chat: { ...chat } });
  }

  onChangeConversationStart = (e, idx) => {
    const { chat } = this.state;
    if (!chat.bot) chat.bot = {};
    if (!chat.bot.conversationStart) chat.bot.conversationStart = [];
    chat.bot.conversationStart[idx] = e.target.value;
    this.setState({ chat: { ...chat } });
  }

  onSaveParams = (key) => {
    const params = _.cloneDeep(this.props.params);
    PARAMS_KEY_ENUM[key].forEach((x) => {
      if (_.isUndefined(_.findKey(params, x))) {
        params[x] = '';
      }
    });

    this.props.onSaveParams(params);
  }

  onSaveDing = async () => {
    const { ding, dingId, flowId } = this.state;
    let checked = false;
    _.keys(DING_ENUM).forEach((x) => { checked = checked || _.isEmpty(ding[x]); });
    if (checked) {
      Toast.show('请完善钉钉应用配置!', Toast.Type.WARNING);
      return;
    }
    if (_.isUndefined(dingId)) {
      await ChatBot.addOpenApp({ openApp: 'dingding', flowId, params: ding });
    } else {
      await ChatBot.updateOpenApp({ id: dingId, flowId, params: ding });
    }
    Toast.show('操作成功!', Toast.Type.SUCCESS);
  }

  onSaveChat = async () => {
    const { chat, chatId, flowId } = this.state;
    if (_.isUndefined(chatId)) {
      await ChatBot.addOpenApp({ openApp: 'chat', flowId, params: chat });
    } else {
      await ChatBot.updateOpenApp({ id: chatId, flowId, params: chat });
    }
    Toast.show('操作成功!', Toast.Type.SUCCESS);
  }

  onCreateGroup = async () => {
    const { roomName, greeting } = this.state.group;
    if (_.isEmpty(roomName)) {
      Toast.show('请完善信息!', Toast.Type.WARNING);
      return;
    }
    await this.props.createWorkflowGroup({ flowId: this.state.flowId, roomName, greeting });
    const items = await this.props.fetchWorkflowGroups({ flowId: this.state.flowId });
    this.setState({ addGroupOpen: false, dataSource: items });
  }

  renderCreateModal = () => {
    const { addGroupOpen, group } = this.state;
    return (
      <Modal
        open={addGroupOpen}
        title="新增群聊"
        onCancel={() => { return this.setState({ addGroupOpen: false, group: {} }); }}
        onOk={this.onCreateGroup}
      >
        <Input
          placeholder="请输入群名称"
          value={group?.roomName}
          onChange={(e) => { return this.onChangeGroup(e, 'roomName'); }}
        />
        <Input.TextArea
          placeholder="请输入群欢迎语"
          style={{ margin: '5px 0' }}
          value={group?.greeting}
          onChange={(e) => { return this.onChangeGroup(e, 'greeting'); }}
        />
      </Modal>
    );
  }

  renderOpenItem = (item) => {
    const { key, value, apiName } = item;
    const workflowId = window.location.pathname.replace('/workflow/', '');
    const apiPath = `${Engine.getApiEndpoint()}/v2/proxy/workflow/${workflowId}/triggers/${key}`;
    return (
      <>
        <Form.Item label={value}>
          <Input
            value={apiPath}
            addonBefore={apiName}
            addonAfter={
              <Button type="text" onClick={() => { return this.onCopyLink(apiPath); }}>复制</Button>
            }
          />
          <Alert
            message="消息格式"
            style={{ marginTop: 20 }}
            action={<Button type="link" onClick={() => { return this.onSaveParams(key); }}>一键创建工作流常量</Button>}
          />
          <CodeMirror
            readOnly
            theme="dark"
            value={OPEN_AI_JSON[key]}
            extensions={[markdown()]}
          />
        </Form.Item>
        {key === 'webhook' && <Divider />}
      </>
    );
  }

  render = () => {
    const { columns, dataSource, ding, flowId, chat } = this.state;
    const { BACKEND_DOMAIN, BACKEND_PROTOCOL } = process.env;
    const apiPath = `${BACKEND_PROTOCOL}://${BACKEND_DOMAIN.indexOf('staging') > -1 ? 'staging-' : ''}chat.openai.mobi/bot/${flowId}/`;
    const domain = Platform.isProd() ? 'https://teacher.bzy.ai' : 'https://k12-test.bzy.ai';
    const h5url = `${domain}/consultation/${flowId}`;
    return (
      <Form className="start-setting" labelCol={{ span: 2 }}>
        {OPEN_API_ENUM.map((x) => { return this.renderOpenItem(x); })}
        <Form.Item label=" " colon={false}>
          <Row gutter={[16, 16]}>
            {
              _.map(DING_ENUM, (v, k) => {
                return (
                  <Col span={9}>
                    {k === 'name' ?
                      <Input
                        value={ding[k]}
                        addonBefore={<div style={{ width: 150 }}>{v}</div>}
                        placeholder={`请输入${v}`}
                        onChange={(e) => { return this.onChangeDingValue(e, k); }}
                      /> :
                      <Input.Password
                        value={ding[k]}
                        addonBefore={<div style={{ width: 150 }}>{v}</div>}
                        placeholder={`请输入${v}`}
                        onChange={(e) => { return this.onChangeDingValue(e, k); }}
                      />
                    }
                  </Col>
                );
              })
            }
            <Col span={9}>
              <Button style={{ float: 'right' }} onClick={() => { return this.onSaveDing(); }}>
                保存应用配置
              </Button>
            </Col>
          </Row>
        </Form.Item>
        <Divider />
        <Form.Item label="企微社群">
          <Alert
            message="消息格式:"
            action={<Button type="link" onClick={() => { return this.onSaveParams('qiwei'); }}>一键创建工作流常量</Button>}
          />
          <CodeMirror
            readOnly
            theme="dark"
            value={OPEN_AI_JSON.qiwei}
            extensions={[markdown()]}
          />

          <Button
            type="primary"
            style={{ float: 'right', marginLeft: 10 }}
            onClick={() => { return this.setState({ addGroupOpen: true }); }}
          >
            新增群
          </Button>
          <Table size="small" pagination={false} dataSource={dataSource} columns={columns} />
        </Form.Item>
        <Divider />
        <Form.Item label="智能对话">
          <Input
            value={apiPath}
            addonBefore="访问地址"
            addonAfter={
              <Button type="text" onClick={() => { return this.onCopyLink(apiPath); }}>复制</Button>
            }
          />
          <Input
            value={h5url}
            style={{ marginTop: 5 }}
            addonBefore="Copolit地址"
            addonAfter={
              <Button type="text" onClick={() => { return this.onCopyLink(h5url); }}>复制</Button>
            }
          />
          <Row gutter={[24, 24]} style={{ marginTop: 20 }}>
            <Col span={12}>
              <Input
                value={chat?.bot?.name}
                addonBefore={<div style={{ width: 150 }}>机器人名称</div>}
                placeholder="请输入机器人名称"
                onChange={(e) => { return this.onChangeChatValue(e, 'name', true); }}
              />
            </Col>
            <Col className="input-wrapper" span={24}>
              <span className="input-title">机器人头像</span>
              {
                chat?.bot?.avatar && (
                  <img src={chat?.bot?.avatar} />
                )
              }
              <InputUpload
                accept="image/*"
                url={chat?.bot?.avatar}
                onChange={(e) => { return this.onChangeChatValue(e, 'avatar', true); }}
              />
            </Col>
            <Col span={24}>
              <Input
                value={chat?.bot?.description}
                addonBefore={<div style={{ width: 150 }}>机器人简介</div>}
                placeholder="请输入机器人简介"
                onChange={(e) => { return this.onChangeChatValue(e, 'description', true); }}
              />
            </Col>
            <Col className="input-wrapper" span={24}>
              <span className="input-title" style={{ borderRight: 0 }}>欢迎语</span>
              <Input.TextArea
                value={chat.welcome}
                addonBefore={<div style={{ width: 150 }}>欢迎语</div>}
                placeholder="请输入欢迎语"
                onChange={(e) => { return this.onChangeChatValue(e, 'welcome'); }}
              />
            </Col>

            {
              (_.isEmpty(chat?.bot?.conversationStart) ? [''] : chat?.bot?.conversationStart).map((x, i) => {
                return (
                  <div style={{ width: '100%', display: 'flex' }}>
                    <Col span={20}>
                      <Input
                        value={x}
                        addonBefore={<div style={{ width: 150 }}>开始会话{i + 1}.</div>}
                        placeholder="请输入开始会话"
                        onChange={(e) => { return this.onChangeConversationStart(e, i); }}
                      />
                    </Col>
                    <span>
                      <Button
                        icon={<PlusCircleOutlined />}
                        onClick={() => { return this.onAddConversationStart(); }}
                      />
                      <Divider type="vertical" />
                      <Button
                        icon={<DeleteOutlined />}
                        onClick={() => { return this.onDelConversationStart(i); }}
                      />
                    </span>
                  </div>
                );
              })
            }
            <Col span={24}>
              <Button style={{ float: 'right' }} onClick={() => { return this.onSaveChat(); }}>
                保存应用配置
              </Button>
            </Col>
          </Row>
        </Form.Item>
        {this.state.addGroupOpen && this.renderCreateModal()}
      </Form>
    );
  }
}
