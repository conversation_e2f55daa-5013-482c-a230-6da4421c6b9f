/* eslint-disable max-lines */
/* eslint-disable max-len */
/* eslint-disable no-case-declarations */
import './index.less';

import { DeleteFilled, MenuOutlined, MoreOutlined, PlusCircleFilled } from '@ant-design/icons';
import { python } from '@codemirror/lang-python';
import CodeMirror from '@uiw/react-codemirror';
import { AutoPrompt } from '~/components';
import { ChatBot, Sessions } from '~/engine';
import * as Conf from '~/pages/Playground/Configs';
import Utils from '~/pages/Playground/Utils';
import { ObjectExtension, StringExtension } from '~/plugins';
import {
  Alert,
  Button,
  Divider,
  Drawer,
  Dropdown,
  Form,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Select,
  Switch,
  Table,
  Tabs,
} from 'antd';
import arrayMove from 'array-move';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { sortableContainer, sortableElement, sortableHandle } from 'react-sortable-hoc';
import { v4 as uuid } from 'uuid';

import { FUNC_TYPES, OPENAI_PARAMS_MAX_VALUE, OPENAI_PARAMS_STEP } from '../../../Configs';
import ModelSelect from '../ModelSelect';
import ToolEditor from '../ToolEditor';
import CommonNodeSetting from './CommonNodeSetting';
import DecisionSetting from './DecisionSetting';
import EndSetting from './EndSetting';
import FeedbackSetting from './FeedbackSetting';
import KnowledgeSetting from './KnowledgeSetting';
import MsgTabpane from './MsgTabpane';
import StartSetting from './StartSetting';
import SubflowSetting from './SubflowSetting';

const DragHandle = sortableHandle(() => {
  return <MenuOutlined style={{ cursor: 'grab', fontWeight: 900, fontSize: 24 }} />;
});
const flexStyle = { display: 'flex', justifyContent: 'space-between', alignItems: 'center' };
const SortableItem = sortableElement((props) => {
  const { vm, id, type, fixedStr, showStr, inputKey } = props;
  if (showStr === 'Start') {
    return (
      <div style={{ ...flexStyle, marginBottom: 10, zIndex: 1001 }} >
        <Alert style={{ width: '100%', marginRight: 10 }} message="Start - 输入占位符" type="warning" />
        <DragHandle />
      </div>
    );
  }
  const nodeMap = {};
  vm.props.nodes.forEach((x) => {
    if (x.name !== undefined) {
      nodeMap[x.id] = x.name;
    }
  });

  return (
    <div style={{ display: 'flex', flexDirection: 'column', width: '100%', marginBottom: 10, zIndex: 1001 }}>
      <div style={flexStyle}>
        <Select
          value={type}
          disabled={!_.isEmpty(fixedStr)}
          onChange={(e) => { vm.onChangeInputType(e, id, inputKey); }}
          style={{ width: 200 }}
          options={[
            { label: 'ASSISTANT', value: 'assistant' },
            { label: 'USER', value: 'user' },
          ]}
        />
        <div>
          <DeleteFilled
            style={{ marginRight: 30, fontSize: 24 }}
            onClick={() => { return vm.onDelInput(id, inputKey); }}
          />
          <DragHandle />
        </div>
      </div>
      <ToolEditor
        key={`${inputKey}-${id}`}
        editorId={`${inputKey}-${id}`}
        value={vm[`${inputKey}-${id}`]}
        params={vm.props.params}
        controls={vm.state.node?.controls || []}
        datas={_.filter(vm.state.inputs, (x) => { return !_.isEmpty(x.fixedStr); })}
        onChange={(e) => { return vm.onChangeTextArea(e, fixedStr, id, inputKey); }}
      />
    </div>
  );
});

const SortableContainer = sortableContainer(({ children }) => { return <div>{children}</div>; });

export default class SettingDrawer extends PureComponent {
  static propTypes = {
    node: PropTypes.object,
    params: PropTypes.object,
    hookObj: PropTypes.object,
    nodes: PropTypes.array,
    subflows: PropTypes.array,
    fullFuncs: PropTypes.array,
    globalFuncs: PropTypes.array,
    globalApiFuncs: PropTypes.object,
    open: PropTypes.bool,
    flowId: PropTypes.string,
    historyMode: PropTypes.string,
    onSaveParams: PropTypes.func,
    onChange: PropTypes.func,
    onSave: PropTypes.func,
    onClose: PropTypes.func,
    addAssistant: PropTypes.func,
    createConversation: PropTypes.func,
    updateConversation: PropTypes.func,
    deleteConversation: PropTypes.func,
    fetchWorkflowGroups: PropTypes.func,
    createWorkflowGroup: PropTypes.func,
  }

  state = {
    inputs: [],
    sessions: [],
    conversations: [],
    funcTools: Sessions.getFuncTools(),
    node: {},
    functions: {},
    activeKey: 'input',
    isLoading: false,
    preText: '',
    md5Key: uuid(),
  }

  constructor(props) {
    super(props);

    this.cacheParams = {};
  }

  componentDidMount = async () => {
    const { node, flowId } = this.props;
    const obj = { node: node?.data, conversations: node?.messages, libraries: node?.libraries };
    const flowResult = await ChatBot.fetchWorkflowSessions({ flowId, nodeId: node.id });
    obj.md5Key = uuid();
    obj.sessionList = flowResult?.items;
    if (node?.type === 'knowledge' || node?.type === 'common') {
      obj.subType = node.data.oType || _.head(Utils.formatSubtype(node?.id));
    }
    if (['processTxt', 'assistant'].includes(node?.type)) {
      obj.inputs = this.formatInputs(node?.data?.inputs, 'inputs');
      obj.backgroundInputs = this.formatInputs(node?.data?.backgroundInputs, 'backgroundInputs');
    }
    if (node?.type === 'decision') {
      obj.conditions = node?.data?.conditions || [{}];
    }
    if (node?.type === 'subflow') {
      const subflowParamsData = node?.data?.subflowParams || {};
      const subflowParams = Object.keys(node?.data?.params || {}).map((key) => {
        return {
          key,
          value: subflowParamsData[key] || subflowParamsData[StringExtension.snakeToCamel(key)],
        };
      });
      obj.subflowParams = subflowParams || [];
    }
    if (obj.subType === 'saveData') {
      obj.flowData = _.isEmpty(node?.data?.flowData) ? [{}] : ObjectExtension.objToArray(node?.data?.flowData);
    }
    if (['form', 'processTxt'].includes(node?.type)) {
      obj[node?.type] = node?.data[node?.type] || {};
    }

    if (obj.node?.llmSetting?.functions?.length) {
      const tempFunc = obj.node?.llmSetting?.functions[0];
      try {
        JSON.parse(tempFunc);
        const funcs = obj.node?.llmSetting?.functions.map((x) => {
          return JSON.parse(x);
        });
        obj.node.llmSetting.functions = _.map(funcs, 'name');
        obj.functions = _.keyBy(funcs, 'name');
      } catch (error) {
        this.onChangeFuncSelect(obj.node?.llmSetting?.functions);
      }
    }
    this.setState(obj);
  }

  formatInputs = (arr = [], key) => {
    const inputs = (arr || []).map(({ fixedStr, content, showStr, type }, i) => {
      if (_.isUndefined(fixedStr) || _.isEmpty(fixedStr)) {
        this[`${key}-txt-${i}`] = content;
        return { id: `txt-${i}`, fixedStr: '', content, type };
      }
      this[`${key}-${fixedStr}`] = content;
      return { id: `${fixedStr}`, fixedStr, showStr, content: this[`${key}-${fixedStr}`], type };
    });

    return inputs;
  }

  cacheModelParams = (data) => {
    const modelKey = Utils.pairModel(data.model);
    if (_.isUndefined(this.cacheParams[modelKey])) {
      this.cacheParams[modelKey] = {};
    }

    _.map(Conf[`${_.toUpper(modelKey)}_PARAMS`], (v, k) => {
      this.cacheParams[modelKey][k] = data[k];
    });
  }

  onChangeSetting = (e, key) => {
    const llmSetting = _.clone(this.state.node?.llmSetting) || {};
    const value = e?.target ? e.target.value : e;

    if (key === 'model' && (Utils.pairModel(value) !== Utils.pairModel(llmSetting.model))) {
      const modelKey = Utils.pairModel(value);

      this.cacheModelParams(llmSetting);
      if (!_.isUndefined(this.cacheParams[modelKey])) {
        _.map(Conf[`${_.toUpper(modelKey)}_PARAMS`], (v, k) => {
          llmSetting[k] = this.cacheParams[modelKey][k];
        });
      }
    }

    this.setState({ node: { ...this.state.node, llmSetting: { ...llmSetting, [key]: value } } });
  }

  onChangeTextArea = (e, fixedStr, id, key) => {
    this[`${key}-${id}`] = e;
    this.setState({ [key]: this.state[key] });
  }

  onAddInput = (key) => {
    const inputs = this.state[key];
    this.setState({ [key]: [...(inputs || []), { id: `txt-${inputs?.length}`, fixedStr: '', type: 'user' }] });
  }

  onDelInput = (id, key) => {
    const inputs = this.state[key].filter((x) => { return x.id !== id; });
    this.setState({ [key]: inputs });
  }

  onChangeInputType = (type, id, key) => {
    const inputs = this.state[key].map((x) => { return x.id === id ? { ...x, type } : x; });
    this.setState({ [key]: inputs });
  }

  onSortEnd = ({ oldIndex, newIndex }, key) => {
    if (oldIndex !== newIndex) {
      const inputs = arrayMove([].concat(this.state[key]), oldIndex, newIndex).filter((el) => {
        return !!el;
      });
      this.setState({ [key]: inputs });
    }
  };

  onChangeCommon = (e, key) => {
    const { type } = this.props.node;
    const data = _.cloneDeep(this.state[type]);
    data[key] = e?.target?.value || e;
    this.setState({ [type]: data });
  }

  onChangeExtraParams = (e, key, data) => {
    let { extraParams } = data?.llmSetting || {};
    const value = e?.target ? e.target.value : e;
    if (_.isUndefined(extraParams)) {
      extraParams = {};
    }
    extraParams[key] = value;
    this.onChangeSetting(extraParams, 'extraParams');
  };

  onChangeFuncSelect = async (e) => {
    const { functions } = this.state;
    let newFunctions = { ...functions };

    if (e && e.length > 0) {
      Object.keys(newFunctions).forEach((key) => {
        if (!e.includes(key)) {
          delete newFunctions[key];
        }
      });

      e.forEach((x) => {
        if (!newFunctions[x]) {
          newFunctions[x] = this.props.fullFuncs[x];
        }
      });
    } else {
      newFunctions = {};
    }

    await this.setState({ functions: newFunctions });
    this.onChangeSetting(e, 'functions');
  }

  onChangeFuncValue = async (e, key, funcKey, pKey) => {
    const functions = _.cloneDeep(this.state.functions);
    const value = e?.target ? e.target.value : e;
    if (pKey) {
      functions[funcKey][key] = functions[funcKey][key].map((x) => {
        return x.name === pKey ? { ...x, description: value } : x;
      });
    } else {
      functions[funcKey][key] = value;
    }
    this.setState({ functions });
  }

  onChangeFuncParams = async (e, key, idx, fKey) => {
    const functions = _.cloneDeep(this.state.functions);
    const value = e?.target ? e.target.value : e;
    if (_.isEmpty(functions[fKey].params)) {
      functions[fKey].params = [{}];
    }
    functions[fKey].params[idx][key] = value;
    this.setState({ functions });
  }

  onSaveAssistant = async (e) => {
    const { llmSetting, role } = this.props.node?.data;
    const { model, temperature, maxTokens, systemPrompt, extra } = llmSetting;
    await this.props.addAssistant({
      name: e, role, llmSetting: { model, temperature, maxTokens, systemPrompt, version: '1', extra },
    });

    this.setState({ visible: false });
  }

  onClose = () => {
    const data = this.state.node;
    if (!_.isEmpty(data.llmSetting?.functions)) {
      const functions = _.values(this.state.functions).map((x) => { return JSON.stringify(x); });
      data.llmSetting.functions = functions;
    }

    this.props.onSave({ ...this.props.node, data });
    this.props.onClose();
  }

  onSave = () => {
    const { type } = this.props.node;
    const { inputs, backgroundInputs, node, conditions, subflowParams, flowData, subType } = this.state;
    const data = node;
    if (['assistant', 'processTxt'].includes(type)) {
      const newInputs = [];
      inputs.forEach((x) => { newInputs.push({ ...x, content: this[`inputs-${x.id}`] }); });
      data.inputs = newInputs;
      const newBackgroundInputs = [];
      backgroundInputs.forEach((x) => { newBackgroundInputs.push({ ...x, content: this[`backgroundInputs-${x.id}`] }); });
      data.backgroundInputs = newBackgroundInputs;
    }

    if (type === 'decision' || subType === 'loopN') {
      data.conditions = (conditions || node?.conditions).filter((x) => { return !_.isEmpty(x); });
    }

    if (type === 'subflow') {
      const subflowParamsObj = {};
      subflowParams.forEach(({ key, value }) => {
        if (!_.isUndefined(key)) { subflowParamsObj[key] = value || ''; }
      });
      data.subflowParams = subflowParamsObj;
    }

    if (subType === 'saveData') {
      const obj = {};
      for (const item of flowData) {
        obj[item.key] = item.value;
      }
      data.flowData = obj;
    }

    const nodeNames = [
      'articleMaterialSearchAndMerge', 'articleMaterialExtract',
      'summaryOutline', 'assistant',
    ];
    if (nodeNames.includes(subType)) {
      const extraParamObj = Utils.formatExtraParams(data?.llmSetting?.extraParams, true);
      data.llmSetting.extraParams = { ...data?.llmSetting?.extraParams, ...extraParamObj };
      ['materialExtractLlmSetting', 'materialMergeLlmSetting', 'summaryLlmSetting'].forEach((x) => {
        data.llmSetting.extraParams[_.snakeCase(x)] = Utils.formatExtraParams(data.llmSetting.extraParams[x], true);
      });
    }

    if (['gatherFromChat'].includes(subType)) {
      ['gatherItems'].forEach((x) => {
        let values = _.values(Utils.formatExtraParams(data.llmSetting.extraParams[x], true));
        values = values.map((v) => {
          const fields = v.fields.map((f) => {
            return Utils.formatExtraParams(f, true);
          });
          return { ...v, fields };
        });
        data.llmSetting.extraParams[_.snakeCase(x)] = values;
      });
      ['loadConfig'].forEach((x) => {
        data.llmSetting.extraParams[_.snakeCase(x)] = data.llmSetting.extraParams[x];
      });
    }

    if (['form', 'processTxt'].includes(type)) {
      data[type] = this.state[type];
    }

    if (!_.isEmpty(data.llmSetting?.functions)) {
      const functions = _.values(this.state.functions).map((x) => { return JSON.stringify(x); });
      data.llmSetting.functions = functions;
    }

    const params = { ...this.props.node, data };
    delete params.publishInfo;
    this.props.onSave(params);
  }

  renderAssistantModal = () => {
    return (
      <Modal
        title="角色 - 另存为"
        open={this.state.visible}
        onCancel={() => { return this.setState({ visible: false }); }}
        footer={null}
      >
        <Input.Search enterButton="另存为" onSearch={this.onSaveAssistant} />
      </Modal>
    );
  }

  renderOpenAIParams = (data = {}, onChange = () => { }) => {
    const model = Utils.pairModel(data?.model);
    const params = Conf[`${_.toUpper(model)}_PARAMS`];

    return (
      <>
        {
          _.map(params, (v, k) => {
            const value = _.isUndefined(data[k]) ? params[k] : data[k];
            const numProps = OPENAI_PARAMS_MAX_VALUE[k] > 1 ?
              { min: 0, max: OPENAI_PARAMS_MAX_VALUE[k], step: OPENAI_PARAMS_STEP[k], value } :
              { min: 0, max: 1, step: 0.1, value };

            return (
              <Form.Item label={_.upperFirst(k)}>
                {
                  _.isBoolean(value) ?
                    <Switch checked={value} onChange={(e) => { return onChange(e, k); }} /> :
                    <InputNumber {...numProps} onChange={(e) => { return onChange(e, k); }} />
                }
              </Form.Item>
            );
          })
        }
        {
          model === 'openai' &&
          <>
            <Form.Item label="响应类型">
              <Select
                value={data?.responseFormat}
                onChange={(e) => { return onChange(e, 'responseFormat'); }}
              >
                <Select.Option value="text">文本</Select.Option>
                <Select.Option value="json_object">JSON</Select.Option>
              </Select>
            </Form.Item>
            {
              data?.responseFormat === 'json_object' &&
              <Form.Item label="JSON处理函数">
                <Button
                  size="small"
                  style={{ position: 'absolute', bottom: 0, zIndex: 10, left: -100 }}
                  onClick={() => {
                    return this.onChangeExtraParams('def evaluate(json_resp):\n', 'sourceCode', this.state.node);
                  }}
                >填充函数
                </Button>
                <CodeMirror
                  id="sourceCode"
                  value={data?.extraParams?.sourceCode || ''}
                  height="20vh"
                  extensions={[python()]}
                  onChange={(e) => { return this.onChangeExtraParams(e, 'sourceCode', this.state.node); }}
                />
              </Form.Item>
            }
          </>
        }
      </>
    );
  }

  renderSortableContainer = (tips = '', key) => {
    return (
      <>
        <Divider style={{ margin: '5px 0' }}>
          <span style={{ fontSize: 14, color: '#ccc' }}>{tips}</span>
        </Divider>
        <div style={{ textAlign: 'end', marginBottom: 5 }}>
          <Button size="small" onClick={() => { return this.onAddInput(key); }}>新增</Button>
        </div>
        <SortableContainer onSortEnd={(e) => { return this.onSortEnd(e, key); }} useDragHandle>
          {
            (this.state[key] || []).map((x, index) => {
              if (x.type === 'system') return null;
              return <SortableItem {...x} key={`item-${x.value}`} index={index} vm={this} inputKey={key} />;
            })
          }
        </SortableContainer>
      </>
    );
  }

  renderMsgTabpane = () => {
    return (
      <Tabs.TabPane tab="消息" key="msg">
        <MsgTabpane
          messages={this.state.conversations}
          createConversation={this.props.createConversation}
          deleteConversation={this.props.deleteConversation}
          updateConversation={this.props.updateConversation}
        />
      </Tabs.TabPane>
    );
  }

  renderMsgDrawer = () => {
    const { chatHistories } = this.state;

    return (
      <Drawer
        title="消息历史"
        width="45vw"
        open={this.state.openMsg}
        onClose={() => { return this.setState({ openMsg: false }); }}
      >
        {
          chatHistories.map((x) => {
            return (
              <Input.Group
                compact
                style={{ display: 'flex', marginBottom: 5, padding: 2, borderBottom: '1px solid #ddd' }}
              >
                <Select bordered={false} style={{ width: 130, maxHeight: 32 }} value={x.role} disabled>
                  <Select.Option value="USER">USER</Select.Option>
                  <Select.Option value="ASSISTANT">ASSISTANT</Select.Option>
                </Select>
                <Input.TextArea autoSize bordered={false} value={x.content} />
              </Input.Group>
            );
          })
        }
      </Drawer>
    );
  }


  renderSessionTabpane = () => {
    return (
      <Tabs.TabPane tab="管理" key="session">
        <Table
          dataSource={this.state.sessionList}
          columns={[
            { title: 'UID', dataIndex: 'uid' },
            {
              title: '操作',
              dataIndex: 'id',
              render: (id, row) => {
                return (
                  <>
                    <a
                      onClick={async () => {
                        const res = await ChatBot.fetchWorkflowConversations({
                          lastId: 0,
                          topicId: row.topicId,
                          nodeId: this.props.node.id,
                          sessionId: row.id,
                        });
                        this.setState({ chatHistories: res.items, openMsg: true });
                      }}
                    >
                      查看历史
                    </a>
                    <Divider type="vertical" />
                    <Popconfirm
                      title="确认清空?"
                      onConfirm={
                        async () => {
                          return ChatBot.clearChatbotSessionConversation(row.topicId);
                        }}
                    >
                      <a>清空记录</a>
                    </Popconfirm>
                  </>
                );
              },
            },
          ]}
        />
      </Tabs.TabPane>
    );
  }

  renderRightExtra = () => {
    const items = [
      {
        key: 'rename',
        label: (<a onClick={() => { return this.setState({ visible: true }); }}>另存为角色模版</a>),
      },
    ];
    return (
      <Dropdown menu={{ items }}><MoreOutlined style={{ fontSize: 24, color: '#000' }} /></Dropdown>
    );
  }

  renderFunParams = (v, k) => {
    if (_.startsWith(k, 'kl@')) {
      return (
        <Form.Item label="查询词描述">
          <Input.TextArea
            autoSize
            value={v.queryDescription}
            onChange={(e) => { return this.onChangeFuncValue(e, 'queryDescription', k); }}
          />
        </Form.Item>
      );
    }

    if (!_.isEmpty(v.params) && _.isEmpty(v.argsSchemaType)) {
      return (
        <Form.Item label="参数">
          {
            _.map(v.params, (p) => {
              return (
                <Form.Item label={<div style={{ width: 100 }}>{p.name}</div>}>
                  <Input.TextArea
                    autoSize
                    value={p.description}
                    onChange={(e) => { return this.onChangeFuncValue(e, 'params', k, p.name); }}
                  />
                </Form.Item>
              );
            })
          }
        </Form.Item>
      );
    }

    if (v.argsSchemaType === 'custom') {
      const params = _.isEmpty(v.params) ? [{ name: '', description: '', type: '' }] : v.params;
      return (
        <Form.Item label="参数">
          {
            _.map(params, (p, i) => {
              return (
                <div style={{ marginBottom: 10 }}>
                  <div style={{ display: 'flex' }}>
                    <Input
                      addonBefore="参名"
                      value={p.name}
                      onChange={(e) => { return this.onChangeFuncParams(e, 'name', i, k); }}
                    />
                    <div style={{ width: 120, display: 'flex', justifyContent: 'space-evenly' }}>
                      <PlusCircleFilled
                        fontSize={24}
                        onClick={() => {
                          const newParams = _.cloneDeep(v.params);
                          newParams.push({ name: '', description: '', type: '' });
                          this.setState({ functions: { ...this.state.functions, [k]: { ...v, params: newParams } } });
                        }}
                      />
                      <DeleteFilled
                        fontSize={24}
                        onClick={() => {
                          const newParams = _.cloneDeep(v.params);
                          if (newParams.length === 1) return;
                          newParams.splice(i, 1);
                          this.setState({ functions: { ...this.state.functions, [k]: { ...v, params: newParams } } });
                        }}
                      />
                    </div>
                  </div>
                  <Input
                    addonBefore="类型"
                    value={p.type}
                    style={{ margin: '5px 0' }}
                    onChange={(e) => { return this.onChangeFuncParams(e, 'type', i, k); }}
                  />
                  <Input
                    addonBefore="描述"
                    value={p.description}
                    onChange={(e) => { return this.onChangeFuncParams(e, 'description', i, k); }}
                  />
                </div>
              );
            })
          }
        </Form.Item>
      );
    }

    return null;
  }

  renderGPTFuncDrawer = () => {
    return (
      <Drawer
        title="GPT函数"
        width="50%"
        open={this.state.openFunc}
        onClose={() => { return this.setState({ openFunc: false }); }}
      >
        <Form labelCol={{ span: 3 }} className="common-form">
          {
            _.map(this.state.functions, (v, k) => {
              return (
                <>
                  <Divider orientation="left">{v.displayName}</Divider>
                  <Form.Item label="描述">
                    <Input.TextArea
                      autoSize
                      value={v.description}
                      onChange={(e) => { return this.onChangeFuncValue(e, 'description', k); }}
                    />
                  </Form.Item>
                  {this.renderFunParams(v, k)}
                </>
              );
            })
          }
        </Form>
      </Drawer>
    );
  }

  renderAssistant = (data) => {
    const model = Utils.pairModel(data?.llmSetting?.model);
    const showFunc = ['ernie-bot-4'].includes(data?.llmSetting?.model) || model === 'openai';
    const { md5Key } = this.state;

    return (
      <Tabs
        onChange={(e) => { return this.setState({ activeKey: e }); }}
        tabBarExtraContent={{ right: this.renderRightExtra() }}
      >
        <Tabs.TabPane tab="输入" key="input">
          <Form labelCol={{ span: 3 }}>
            <Form.Item label="系统提示词">
              <AutoPrompt
                text={data?.llmSetting?.systemPrompt}
                onImprove={(text) => {
                  this.onChangeSetting(text, 'systemPrompt');
                  this.setState({ md5Key: uuid() });
                }}
              />
              <ToolEditor
                ref={(ref) => { this.refprompt = ref; }}
                key={md5Key}
                editorId="prompt"
                value={data?.llmSetting?.systemPrompt}
                params={this.props.params}
                controls={this.state.node?.controls || []}
                datas={_.filter(this.state.inputs, (x) => { return !_.isEmpty(x.fixedStr); })}
                onChange={(e) => { return this.onChangeSetting(e, 'systemPrompt'); }}
              />
            </Form.Item>
            <Form.Item label="名称">
              <Input
                value={data?.name}
                onChange={(e) => { return this.setState({ node: { ...data, name: e.target.value } }); }}
              />
            </Form.Item>
            <Form.Item label="运行时禁止蹦字日志">
              <Switch
                checked={data?.llmSetting?.disableStepToken}
                onChange={(e) => { return this.onChangeSetting(e, 'disableStepToken'); }}
              />
            </Form.Item>
            <Form.Item label="模型">
              <ModelSelect
                params={this.props.params}
                value={data?.llmSetting?.model}
                onChange={(e) => { return this.onChangeSetting(e, 'model'); }}
              />
            </Form.Item>
            {
              showFunc &&
              <>
                <Form.Item label="GPT函数">
                  <div style={{ display: 'flex' }}>
                    <Select
                      mode="multiple"
                      value={data?.llmSetting?.functions}
                      onChange={(e) => { return this.onChangeFuncSelect(e); }}
                    >
                      {
                        (this.props.globalFuncs || []).map((x, i) => {
                          return (
                            <Select.OptGroup key={x.length} label={FUNC_TYPES[i]}>
                              {_.map(x, (v, k) => { return <Select.Option value={k}>{v}</Select.Option>; })}
                            </Select.OptGroup>
                          );
                        })
                      }
                    </Select>
                    <Button onClick={() => { return this.setState({ openFunc: true }); }}>修改函数 Prompt</Button>
                  </div>
                </Form.Item>
                {
                  (data?.llmSetting?.functions || []).includes('search_knowledge') &&
                  <>
                    <Form.Item label="知识库">
                      <Select
                        value={data?.llmSetting?.libraryId}
                        onChange={(e) => { return this.onChangeSetting(e, 'libraryId'); }}
                      >
                        {
                          (this.state?.libraries || []).map((x) => {
                            return <Select.Option value={x.id}>{x.field}</Select.Option>;
                          })
                        }
                      </Select>
                    </Form.Item>
                    <Form.Item label="输出模版">
                      <CodeMirror
                        id="sourceCode"
                        value={data?.llmSetting?.resultTpl}
                        height="100px"
                        extensions={[python()]}
                        onChange={(e) => { return this.onChangeSetting(e, 'resultTpl'); }}
                      />
                    </Form.Item>
                  </>
                }
                <Form.Item label="API Action">
                  <Select
                    mode="multiple"
                    value={data?.llmSetting?.openapiFuncs}
                    onChange={(e) => { return this.onChangeSetting(e, 'openapiFuncs'); }}
                  >
                    {
                      _.map(this.props.globalApiFuncs || [], (v, k) => {
                        return <Select.Option value={+k}>{v}</Select.Option>;
                      })
                    }
                  </Select>
                </Form.Item>
              </>
            }
            <Form.Item label="运行配置">
              <Radio.Group
                value={data?.llmSetting?.historyMode || this.props.historyMode}
                onChange={(e) => { return this.onChangeSetting(e, 'historyMode'); }}
              >
                <Radio value="none">忽略节点历史消息</Radio>
                <Radio value="autofit">启用并自动压缩节点消息</Radio>
                <Radio value="all">启用全部节点历史消息</Radio>
              </Radio.Group>
              {
                (data?.llmSetting?.historyMode || this.props.historyMode) === 'autofit' &&
                <div style={{ marginTop: 10 }}>
                  <span style={{ marginRight: 30 }}>历史MaxTokens:&nbsp;
                    <InputNumber
                      value={data?.llmSetting?.historyMaxTokens}
                      onChange={(e) => { return this.onChangeSetting(e, 'historyMaxTokens'); }}
                    />
                  </span>
                  <InputNumber
                    addonBefore="最近的"
                    addonAfter="条消息"
                    value={data?.llmSetting?.historyMaxMessages}
                    onChange={(e) => { return this.onChangeSetting(e, 'historyMaxMessages'); }}
                  />
                </div>
              }
            </Form.Item>
            {this.renderOpenAIParams(data?.llmSetting, this.onChangeSetting)}
          </Form>
          {this.renderSortableContainer('初始聊天历史消息', 'backgroundInputs')}
          {this.renderSortableContainer('新聊天消息', 'inputs')}
        </Tabs.TabPane>
        {this.renderMsgTabpane()}
        {this.renderSessionTabpane()}
      </Tabs>
    );
  }

  renderDecision = () => {
    return (
      <DecisionSetting conditions={this.state.conditions} onChange={((e) => { return this.setState(e); })} />
    );
  }

  renderSubflow = (node) => {
    return (
      <SubflowSetting
        node={node}
        params={this.props.params}
        subflowParams={this.state.subflowParams}
        onChange={((e) => { return this.setState(e); })}
      />
    );
  }

  renderProcessTxt = (data) => {
    return (
      <div className="process-txt-wrap">
        <Form layout="vertical">
          <Form.Item label="名称">
            <Input
              value={data?.name}
              onChange={(e) => { return this.setState({ node: { ...data, name: e.target.value } }); }}
            />
          </Form.Item>
        </Form>
        {this.renderSortableContainer('', 'inputs')}
      </div>
    );
  }

  renderFeedback = (data) => {
    return (
      <FeedbackSetting
        data={data}
        params={this.props.params}
        onChange={((e) => { return this.setState(e); })}
        onChangeSetting={this.onChangeSetting}
        onChangeExtraParams={this.onChangeExtraParams}
      />
    );
  }

  renderKnowledge = (data) => {
    const { subType } = this.state;
    if (_.isEmpty(data)) return null;
    if (subType === 'searchFeedback') return this.renderFeedback(data);

    return (
      <KnowledgeSetting
        data={data}
        subType={subType}
        params={this.props.params}
        originData={this.props.node}
        onChange={((e) => { return this.setState(e); })}
        onChangeSetting={this.onChangeSetting}
        onChangeExtraParams={this.onChangeExtraParams}
      />
    );
  }

  renderCommonNodeSetting = () => {
    const { subType, flowData, node } = this.state;

    return (
      <CommonNodeSetting
        data={node}
        type={subType}
        flowData={flowData || [{}]}
        nodes={this.props.nodes}
        params={this.props.params}
        originData={this.props.node}
        onChange={(e) => { return this.setState(e); }}
        onClose={this.props.onClose}
      />
    );
  }

  render = () => {
    const { node, activeKey } = this.state;
    const { type, id } = this.props.node;

    return (
      <Drawer
        keyboard={false}
        maskClosable={false}
        title={`设置 - [ ${id} ]`}
        placement="right"
        open={this.props.open}
        contentWrapperStyle={{ width: '65vw' }}
        onClose={() => { return this.onClose(); }}
        className="workflow-setting-drawer"
      >
        {type === 'start' &&
          <StartSetting
            data={node}
            params={this.props.params}
            onSaveParams={this.props.onSaveParams}
            fetchWorkflowGroups={this.props.fetchWorkflowGroups}
            createWorkflowGroup={this.props.createWorkflowGroup}
          />
        }
        {
          type === 'end' &&
          <EndSetting flowId={this.props.flowId} subflows={this.props.subflows} hookObj={this.props.hookObj} />
        }
        {type === 'assistant' && this.renderAssistant(node)}
        {type === 'common' && this.renderCommonNodeSetting(node)}
        {
          !['assistant', 'common', 'saveData', 'start', 'end'].includes(type) &&
          <Tabs onChange={(e) => { return this.setState({ activeKey: e }); }}>
            <Tabs.TabPane tab="设置" key="setting">
              {type === 'subflow' && this.renderSubflow(node)}
              {type === 'decision' && this.renderDecision(node)}
              {type === 'processTxt' && this.renderProcessTxt(node)}
              {type === 'knowledge' && this.renderKnowledge(node)}
            </Tabs.TabPane>
            {this.renderMsgTabpane()}
          </Tabs>
        }

        {
          (activeKey !== 'msg' && !type.includes('end', 'start')) && this.state.subType !== 'demo' &&
          <div style={{ textAlign: 'center', position: 'absolute', left: 0, right: 0, bottom: 10, zIndex: 100 }}>
            <Button type="primary" shape="round" onClick={this.onSave}>保存</Button>
          </div>
        }
        {this.state.visible && this.renderAssistantModal()}
        {this.state.openFunc && this.renderGPTFuncDrawer()}
        {this.state.openMsg && this.renderMsgDrawer()}
      </Drawer>
    );
  }
}
