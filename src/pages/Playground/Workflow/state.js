import Configs from '~/consts';
import { ChatBot, Clips } from '~/engine';
import { StringExtension } from '~/plugins';
import _ from 'lodash';
import moment from 'moment';

import { EVENT_TYPE, EVENT_TYPE_ZH } from '../Configs';
import { BASE_NODES, CONTROL_NODES, INFO_NODES, MSG_NODES, TOOL_NODES } from './components/CustomNodes/Configs';

const SET_STATE = 'CHATBOT_WORKFLOW/SET_STATE';
const CLEAR_STATE = 'CHATBOT_WORKFLOW/CLEAR_STATE';

const formatLogs = (arrs = []) => {
  const typeObj = { ...TOOL_NODES, ...CONTROL_NODES, ...INFO_NODES, ...BASE_NODES, ...MSG_NODES };
  _.forEach(typeObj, (v, k) => { typeObj[k] = v.nodeName; });

  let items = arrs;
  items = items.filter((x) => { return x.logType !== EVENT_TYPE.FINAL_RESULT; });
  items = items.filter((x) => {
    return !(
      x.logType === EVENT_TYPE.EXEC_LOG &&
      x.nodeId === 'Start' &&
      x.content === '{"msg": "Workflow run start"}'
    );
  });
  items = _.reverse(items);

  const result = [];
  let temp = [];
  for (const x of items) {
    if (x.logType === EVENT_TYPE.EXEC_STEP) {
      if (temp.length > 0) { result.push(temp); }
      temp = [];
      temp.push(x);
    } else {
      temp.push(x);
    }
  }
  if (temp.length > 0) {
    result.push(temp);
  }
  const logs = [];
  result.forEach((arr) => {
    const [node, ...oth] = arr;
    let message = '';
    oth.forEach((x) => {
      const obj = JSON.parse(x.content);
      const style = x.logType === EVENT_TYPE.EXEC_FAILED ? 'color:red' : '';
      let msg = obj?.output || obj?.prompt || obj?.msg;
      try {
        const msgObj = JSON.parse(msg);
        delete msgObj.raw_content;
        msg = JSON.stringify(msgObj, null, 2);
      } catch (error) {
        // nothing
      }
      const typeName = EVENT_TYPE_ZH[x.logType];
      message += `<div style="background:#eee;padding:4px 6px;"><b style="${style}">[${typeName}]:</b>
<pre style="white-space: pre-wrap;margin-bottom:0;">${msg || ''}</pre>
</div >\n`;
    });

    message = _.trimEnd(message, '\n');
    const nodeObj = JSON.parse(node?.content);
    logs.push({
      message,
      nodeId: nodeObj?.node_name || nodeObj?.node_id,
      createdAt: moment(node.createdAt).add(8, 'h').format('YYYY-MM-DD HH:mm'),
    });
  });

  return logs.filter((x) => { return !_.isEmpty(x.message); });
};

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchPublishInfo = () => {
  return async () => {
    const [accounts, templates, libraries] = await Promise.all([
      Clips.fetchPublishAccounts(),
      Clips.fetchBundleTemplates(),
      Clips.fetchArticleLibraries(),
    ]);
    return { accounts, templates, libraries };
  };
};

export const fetchAssistants = () => {
  return async (dispatch) => {
    const { items } = await ChatBot.fetchChatbotAssistants(Configs.ALL_PAGE_PARAMS);
    dispatch(setState({ assistants: items }));
  };
};

export const addAssistant = (params = {}) => {
  return async (dispatch) => {
    await ChatBot.createChatbotAssistant(params);
    dispatch(fetchAssistants());
  };
};

export const updateAssistant = (params = {}) => {
  return async (dispatch) => {
    await ChatBot.updateChatbotAssistant(params);
    dispatch(fetchAssistants());
  };
};

export const delAssistant = (id) => {
  return async (dispatch) => {
    await ChatBot.delChatbotAssistant(id);
    dispatch(fetchAssistants());
  };
};

export const fetchLibraries = () => {
  return async () => {
    const { items } = await ChatBot.searchKnowledgeLibraries(Configs.ALL_PAGE_PARAMS);
    return items;
  };
};

export const fetchChatbotWorkflows = (uuid) => {
  return async (dispatch) => {
    let { items } = await ChatBot.fetchChatbotWorkflows(Configs.ALL_PAGE_PARAMS);
    if (!_.isUndefined(uuid)) {
      items = items.filter((x) => { return x.uuid !== uuid; });
    }
    items = items.map((x) => { return { ...x, type: 'subflow' }; });
    dispatch(setState({ subflows: items }));
  };
};

export const fetchWorkflow = (uuid) => {
  return async () => {
    const data = await ChatBot.getChatbotWorkflow(uuid);
    return data;
  };
};

export const addWorkflow = (params) => {
  return async () => {
    const result = await ChatBot.createChatbotWorkflow(params);
    return result;
  };
};

export const updateWorkflow = (params) => {
  return async () => {
    await ChatBot.updateChatbotWorkflow(params);
  };
};

export const publishWorkflow = (id) => {
  return async () => {
    await ChatBot.publishChatbotWorkflow(id);
  };
};

export const cancelWorkflow = (flowId, jobId) => {
  return async () => {
    await ChatBot.cancelWorkflow(flowId, jobId);
  };
};

export const fetchRunLogs = (id) => {
  return async () => {
    const { items } = await ChatBot.fetchWorkflowLogs({
      flowUuid: id,
      'pagination.pageIndex': 1,
      'pagination.pageSize': 100,
    });
    return formatLogs(items);
  };
};

export const fetchConversations = (id, nodeId) => {
  return async () => {
    const { items } = await ChatBot.fetchWorkflowConversations({ flowId: id, nodeId, orderBy: 'id desc' });
    const messages = [];
    items.forEach((x) => {
      messages.push({
        ...x,
        message: x.content,
        isClient: x.role !== 'USER',
        createdAt: moment(x.createdAt).add(8, 'h').format('YYYY-MM-DD HH:mm'),
      });
    });
    return _.reverse(messages);
  };
};

export const createConversation = (params) => {
  return async () => {
    const result = await ChatBot.createChatbotSessionConversation(params);
    return result;
  };
};

export const updateConversation = (params) => {
  return async () => {
    await ChatBot.updateChatbotSessionConversation(params);
  };
};

export const deleteConversation = (id) => {
  return async () => {
    await ChatBot.deleteChatbotSessionConversation(id);
  };
};

export const addJob = (params) => {
  return async () => {
    await ChatBot.addChatbotWorkflowJob(params);
  };
};

export const fetchWorkflowGroups = (params) => {
  return async () => {
    const { items } = await ChatBot.fetchWorkflowGroups(params);
    return items;
  };
};

export const createWorkflowGroup = (params) => {
  return async () => {
    await ChatBot.createWorkflowGroup(params);
  };
};

export const fetchInnerParams = (params) => {
  return async (dispatch) => {
    const result = await ChatBot.fetchWorkflowInnerParams(params);
    const filters = [];
    _.map(result?.filters, (v, k) => {
      if (!_.isEmpty(v)) {
        filters.push({ key: StringExtension.camelToSnake(k), value: v });
      }
    });
    dispatch(setState({ innerParams: { filters, runtimeParams: result?.runtimeParams } }));
  };
};

const _getInitState = () => {
  return {
    assistants: [],
    subflows: [],
    innerParams: {},
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
