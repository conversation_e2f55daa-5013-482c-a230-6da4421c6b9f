import Configs from '~/consts';
import { ChatBot } from '~/engine';

const SET_STATE = 'CHAT_KNOWLEDGE/SET_STATE';
const CLEAR_STATE = 'CHAT_KNOWLEDGE/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchGroups = () => {
  return async (dispatch) => {
    const { items } = await ChatBot.fetchChatbotWorkflowGroups(Configs.ALL_PAGE_PARAMS);
    dispatch(setState({ groups: items }));
  };
};

export const addGroup = (params) => {
  return async (dispatch) => {
    await ChatBot.addWorkflowGroup(params);
    dispatch(fetchGroups());
  };
};

export const updateGroup = (params) => {
  return async (dispatch) => {
    await ChatBot.updateWorkflowGroup(params);
    dispatch(fetchGroups());
  };
};

export const delGroup = (id) => {
  return async (dispatch) => {
    await ChatBot.delWorkflowGroup(id);
    dispatch(fetchGroups());
  };
};

export const addWorkflow = (params) => {
  return async () => {
    const result = await ChatBot.createChatbotWorkflow(params);
    return result;
  };
};

export const updateWorkflowInputSpec = (params) => {
  return async () => {
    await ChatBot.updateWorkflowInputSpec(params);
  };
};

export const fetchChatbotWorkflows = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().chatBotWorkflow;
    const searchParams = {
      name: params?.name,
      groupId: params?.groupId,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };

    const { items, total } = await ChatBot.fetchChatbotWorkflows(searchParams);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const copyWorkflow = (params) => {
  return async (dispatch) => {
    await ChatBot.createChatbotWorkflow(params);
    dispatch(fetchChatbotWorkflows());
  };
};

export const delChatbotWorkflow = (id) => {
  return async (dispatch) => {
    await ChatBot.delChatbotWorkflow(id);
    dispatch(fetchChatbotWorkflows());
  };
};

export const updateChatbotWorkflow = (id) => {
  return async (dispatch) => {
    await ChatBot.updateChatbotWorkflow(id);
    dispatch(fetchChatbotWorkflows());
  };
};

export const fetchWorkflowGroups = (params) => {
  return async () => {
    const { items } = await ChatBot.fetchWorkflowGroups(params);
    return items;
  };
};

export const createWorkflowGroup = (params) => {
  return async () => {
    await ChatBot.createWorkflowGroup(params);
  };
};

export const exportWorkflow = (id) => {
  return async () => {
    const result = await ChatBot.exportWorkflow(id);
    return result;
  };
};

export const importWorkflow = (params) => {
  return async (dispatch) => {
    await ChatBot.importWorkflow(params);
    dispatch(fetchChatbotWorkflows());
  };
};

const _getInitState = () => {
  return {
    total: 0,
    groupId: undefined,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    list: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
