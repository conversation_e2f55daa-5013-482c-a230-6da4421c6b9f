import { json } from '@codemirror/lang-json';
import CodeMirror from '@uiw/react-codemirror';
import { FilterBar, PaginationTable, Toast } from '~/components';
import Engine from '~/engine';
import { Platform, StringExtension } from '~/plugins';
import {
  <PERSON><PERSON>,
  Button,
  Divider,
  Drawer,
  Form,
  Image,
  Input,
  Modal,
  Popconfirm,
  Select,
  Table,
  Tabs,
  Typography,
} from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

const TabKeys = [
  { key: 'dingding', name: '钉群' },
  { key: 'feishu', name: '飞书' },
  { key: 'webhook', name: '通用' },
];
@connect(
  (state) => {
    return state.chatBotWorkflow;
  },
  actions,
)
export default class ChatBotWorkflows extends Component {
  static propTypes = {
    groups: PropTypes.array,
    list: PropTypes.array,
    total: PropTypes.number,
    pagination: PropTypes.object,
    fetchGroups: PropTypes.func.isRequired,
    addGroup: PropTypes.func.isRequired,
    updateGroup: PropTypes.func.isRequired,
    delGroup: PropTypes.func.isRequired,
    fetchChatbotWorkflows: PropTypes.func.isRequired,
    addWorkflow: PropTypes.func.isRequired,
    copyWorkflow: PropTypes.func.isRequired,
    exportWorkflow: PropTypes.func.isRequired,
    importWorkflow: PropTypes.func.isRequired,
    updateWorkflowInputSpec: PropTypes.func.isRequired,
    delChatbotWorkflow: PropTypes.func.isRequired,
    updateChatbotWorkflow: PropTypes.func.isRequired,
    fetchWorkflowGroups: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    columns: [
      { title: '群名', dataIndex: 'roomName', key: 'roomName' },
      {
        title: '二维码',
        dataIndex: 'qrCode',
        key: 'qrCode',
        render: (qrCode) => { return <Image src={qrCode} width={100} />; },
      },
    ],
    publishData: {
      dataSource: [],
    },
  }

  componentDidMount = () => {
    this.props.fetchGroups();
    this.props.fetchChatbotWorkflows();
    Platform.emit(Platform.Event.RELOAD_FUNC_TOOLS);
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onSearch = (e) => {
    const { name, groupId } = this.state;
    this.props.fetchChatbotWorkflows({ ...e, name: name || '', groupId });
  }

  onCopy = async (data) => {
    const { name, type, content, betaContent } = data;
    await this.props.copyWorkflow({ name: `${name}-COPY`, type, content, betaContent });
    Toast.show('复制成功', Toast.Type.SUCCESS);
  }

  onAdd = async () => {
    const flow = { name: `工作流-${StringExtension.randomString(4)}`, type: 'workflow' };
    const nodes = [
      { id: 'start', name: '开始', data: {}, position: { x: 150, y: 420 }, type: 'start' },
      { id: 'end', name: '结束', data: {}, position: { x: 1200, y: 420 }, type: 'end' },
    ];
    const contentObj = { nodes, edges: [], params: {}, history_mode: 'autofit' };
    const obj = { ...flow, betaContent: JSON.stringify(contentObj) };
    const result = await this.props.addWorkflow(obj);
    this.$push(`/workflow/${result.uuid}`);
  }

  onOpenDrawer = async (row) => {
    const items = await this.props.fetchWorkflowGroups({ flowId: row.uuid });
    this.setState({ open: true, publishData: { data: row, dataSource: items } });
  }

  onCopyLink = async (text) => {
    await navigator.clipboard.writeText(text);
    Toast.show('复制成功', Toast.Type.SUCCESS);
  }

  onChangeInputTempValue = (e, key) => {
    const value = e?.target ? e?.target.value : e;
    this.setState({ inputTemp: { ...this.state.inputTemp, [key]: value } });
  }

  onShare = async (id) => {
    const { key, readonlyKey } = await this.props.exportWorkflow(id);
    Modal.info({
      width: 800,
      title: '分享',
      okText: '关闭',
      content: (
        <div>
          <Alert
            style={{ padding: '2px 10px', marginBottom: 4 }}
            message="↓↓将你的工作流的配置，分享给好友，好友将你的工作流配置导入创建自己的工作流↓↓"
          />
          <Input.Search
            addonBefore="分享链接:"
            value={key}
            onSearch={async () => {
              await navigator.clipboard.writeText(key);
              Toast.show('复制成功!', Toast.Type.SUCCESS);
            }}
            enterButton="复制"
          />
          <Divider />
          <Alert
            style={{ padding: '2px 10px', marginBottom: 4 }}
            message="↓↓授权你的好友，在他的空间下，直接运行你的工作流，只读，好友不可修改↓↓"
          />
          <Input.Search
            addonBefore="只读链接:"
            value={readonlyKey}
            onSearch={async () => {
              await navigator.clipboard.writeText(readonlyKey);
              Toast.show('复制成功!', Toast.Type.SUCCESS);
            }}
            enterButton="复制"
          />
        </div>
      ),
      onOk() { },
    });
  }

  onImport = () => {
    Modal.info({
      width: 500,
      title: '导入',
      okText: '关闭',
      content: (
        <div>
          <Input.Search
            onSearch={async (e) => {
              await this.props.importWorkflow({ key: e });
              Toast.show('导入成功!', Toast.Type.SUCCESS);
            }}
            enterButton="导入"
          />
        </div>
      ),
      onOk() { },
    });
  }

  onChangeWorkflowGroup = async (groupId, data) => {
    await this.props.updateChatbotWorkflow({ uuid: data.uuid, groupId });
    Toast.show('操作成功!', Toast.Type.SUCCESS);
  }

  onChangeGroup = (e, key) => {
    const { value } = e.target;
    this.setState({ group: { ...this.state.group, [key]: value } });
  }

  onSubmitGroup = async () => {
    const { id, name } = this.state.group;
    if (_.isEmpty(name)) {
      Toast.show('请完善信息!', Toast.Type.WARNING);
      return;
    }

    if (_.isUndefined(id)) {
      await this.props.addGroup({ name });
    } else {
      await this.props.updateGroup(this.state.group);
    }

    this.setState({ addGroupOpen: false, group: {} });
    Toast.show('操作成功!', Toast.Type.SUCCESS);
  }

  onSaveInputSpec = async () => {
    const inputTemp = _.cloneDeep(this.state.inputTemp);
    if (_.isEmpty(inputTemp?.inputSpec)) {
      Toast.show('请完善信息!', Toast.Type.WARNING);
      return;
    }
    try {
      inputTemp.inputSpec = JSON.parse(inputTemp?.inputSpec);
    } catch (error) {
      Toast.show('输入协议必须是JSON对象!', Toast.Type.WARNING);
      return;
    }
    await this.props.updateWorkflowInputSpec(inputTemp);
    this.setState({ openInput: false, inputTemp: {} });
    Toast.show('操作成功!', Toast.Type.SUCCESS);
  }

  renderPublishModal = () => {
    const { open, publishData, columns } = this.state;
    const { data, dataSource } = publishData;
    const apiPath = `${Engine.getApiEndpoint()}/v2/proxy/workflow/${data?.uuid}/triggers/`;
    return (
      <Drawer
        open={open}
        title="发布"
        placement="right"
        contentWrapperStyle={{ width: '50vw' }}
        onClose={() => { return this.setState({ open: false }); }}
      >
        <Tabs>
          <Tabs.TabPane key="webhook" tab="Webhook">
            {
              TabKeys.map((x) => {
                return (
                  <Input
                    style={{ marginBottom: 10 }}
                    addonBefore={`${x.name}地址:`}
                    value={`${apiPath}${x.key}`}
                    addonAfter={
                      <Button type="text" onClick={() => { return this.onCopyLink(`${apiPath}${x.key}`); }}>
                        复制
                      </Button>
                    }
                  />
                );
              })
            }
          </Tabs.TabPane>
          <Tabs.TabPane key="list" tab="群聊">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Alert
                type="info"
                message={
                  <Typography.Paragraph style={{ marginBottom: 0 }} ellipsis={{ rows: 2, expandable: true }}>
                    1. 发布至企微群的workflow, 会在触发workflow的时候,
                    将聊天消息的 `rpa_chat_id`、消息发送人ID `rpa_message_sender`
                    以及群聊ID `room_id` 以变量传入。
                    <br />
                    2. 如果需要在workflow里触发消息回复,
                    则需要给workflow添加 `rpa_chat_id`、`rpa_message_sender`、`room_id` 三个常量,
                    并在workflow里加一个 `推企微` 的节点引用对应的值。
                  </Typography.Paragraph>
                }
              />
              <Button
                type="primary"
                style={{ float: 'right', marginLeft: 10 }}
                onClick={() => { return this.setState({ addGroupOpen: true }); }}
              >
                新增
              </Button>
            </div>
            <Table size="small" pagination={false} dataSource={dataSource} columns={columns} />
          </Tabs.TabPane>
        </Tabs>
      </Drawer>
    );
  }

  renderCreateModal = () => {
    const { addGroupOpen, group } = this.state;
    return (
      <Modal
        open={addGroupOpen}
        title="分组"
        onCancel={() => { return this.setState({ addGroupOpen: false, group: {} }); }}
        onOk={this.onSubmitGroup}
      >
        <Input
          placeholder="请输入分组名称"
          value={group?.name}
          onChange={(e) => { return this.onChangeGroup(e, 'name'); }}
        />
      </Modal>
    );
  }

  renderGroupDrawer = () => {
    const columns = [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '分组名', dataIndex: 'name', key: 'name', align: 'center' },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <a onClick={() => { return this.setState({ addGroupOpen: true, group: row }); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm title="是否删除?!" onConfirm={() => { return this.props.delGroup(row.id); }}>
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
    return (
      <Drawer
        open={this.state.groupOpen}
        title="分组"
        placement="right"
        contentWrapperStyle={{ width: '50vw' }}
        onClose={() => { return this.setState({ groupOpen: false }); }}
      >
        <div style={{ width: '100%', textAlign: 'end' }}>
          <Button type="primary" onClick={() => { return this.setState({ addGroupOpen: true }); }}>新增</Button>
        </div>
        <Table size="small" pagination={false} dataSource={this.props.groups || []} columns={columns} />
      </Drawer>
    );
  }

  renderInputDrawer = () => {
    const { inputTemp, openInput } = this.state;
    return (
      <Drawer
        open={openInput}
        title="输入协议"
        placement="right"
        contentWrapperStyle={{ width: '50vw' }}
        onClose={() => { return this.setState({ openInput: false }); }}
        extra={<Button type="primary" onClick={() => { return this.onSaveInputSpec(); }}>保存</Button>}
      >
        <Form labelCol={{ span: 4 }}>
          <Form.Item label="JSON对象">
            <CodeMirror
              id={inputTemp.uuid}
              style={{ marginTop: 10 }}
              value={inputTemp?.inputSpec}
              height="50vh"
              extensions={[json()]}
              onChange={(e) => { return this.setState({ inputTemp: { ...inputTemp, inputSpec: e } }); }}
            />
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
        render: (name, row) => {
          return <a onClick={() => { return this.$push(`/workflow/${row.uuid}`); }}>{name}</a>;
        },
      },
      {
        title: '分组',
        dataIndex: 'groupId',
        key: 'groupId',
        align: 'center',
        render: (groupId, row) => {
          const value = groupId === 0 ? undefined : groupId;
          return (
            <Select
              value={value}
              style={{ width: 160 }}
              placeholder="请选择"
              size="small"
              options={(this.props.groups || []).map((x) => { return { label: x.name, value: x.id }; })}
              onChange={(e) => { return this.onChangeWorkflowGroup(e, row); }}
            />
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <a onClick={() => { return this.$push(`/workflow/${row.uuid}/jobs`); }}>定时任务</a>
              <Divider type="vertical" />
              <a onClick={() => {
                return this.setState({
                  openInput: true,
                  inputTemp: {
                    uuid: row.uuid, inputSpec: JSON.stringify(row.inputSpec, null, 2),
                  },
                });
              }}
              >
                输入协议
              </a>
              <Divider type="vertical" />
              <a onClick={() => { return this.onShare(row.uuid); }}>分享</a>
              <Divider type="vertical" />
              <Popconfirm title="是否复制?!" onConfirm={() => { return this.onCopy(row); }}>
                <a>复制</a>
              </Popconfirm>
              <Divider type="vertical" />
              <a onClick={() => { return this.$push(`/workflow/${row.uuid}`); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm
                title="是否删除?!"
                onConfirm={() => { return this.props.delChatbotWorkflow(row.uuid); }}
              >
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  renderButtons = () => {
    return [
      <Button onClick={() => { return this.$replace('/workflow-v2'); }}>使用新版</Button>,
      <Divider type="vertical" />,
      <Button onClick={() => { return this.onImport(); }}>导入</Button>,
      <Button
        style={{ margin: '0 30px' }}
        onClick={() => { return this.setState({ groupOpen: true }); }}
      >
        分组
      </Button>,
      // <Button type="primary" onClick={() => { return this.onAdd(); }}>新增</Button>,
    ];
  }

  renderSelects = () => {
    return (
      <Select
        allowClear
        value={this.state.groupId}
        style={{ width: 160, marginBottom: 16 }}
        placeholder="请选择"
        onChange={(e) => { return this.setState({ groupId: e }); }}
      >
        {
          (this.props.groups || []).map((x) => {
            return <Select.Option value={x.id}>{x.name}</Select.Option>;
          })
        }
      </Select>
    );
  }

  render = () => {
    const { total, list, pagination } = this.props;

    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <FilterBar
          canAdd={false}
          placeholder="请输入关键字"
          searchKeyWords={this.state.name}
          onSearch={() => { return this.onSearch({ pageIndex: 1 }); }}
          onChange={(e) => { return this.setState({ name: e }); }}
          renderSelects={this.renderSelects}
          renderButtons={this.renderButtons}
        />
        <PaginationTable
          totalDataCount={total}
          dataSource={list}
          pagination={pagination}
          columns={this.renderColumns()}
          onPaginationChange={(e) => { return this.onSearch(e); }}
        />

        {this.state.open && this.renderPublishModal()}
        {this.state.groupOpen && this.renderGroupDrawer()}
        {this.state.addGroupOpen && this.renderCreateModal()}
        {this.state.openInput && this.renderInputDrawer()}
      </div>
    );
  }
}

export {
  reducer,
};
