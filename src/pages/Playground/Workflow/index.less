.workflow-container {
  position: relative;
  height: calc(100vh - 72px);

  .model-list {
    display: inline-block;
    width: 200px;
    height: calc(100vh - 110px);
    overflow: auto;
    vertical-align: top;

    .ant-collapse-content-box {
      padding: 0 5px;
    }
  }

  .flow-wrap {
    display: inline-block;
    width: calc(100vw - 248px);
    height: calc(100vh - 64px);
  }

  .react-flow__attribution {
    display: none;
  }

  .ant-list-item-meta-avatar {
    margin-right: 8px;
  }

  .flow-node-card {
    .ant-card-head {
      padding: 0 10px;

      .ant-card-head-title {
        padding: 5px 0;
      }
    }
  }

  .flow-name {
    display: flex;
    position: absolute;
    top: 4px;
    right: 0;
    left: 200px;
    align-items: center;
    justify-content: center;
  }

  .btn-back {
    position: absolute;
    bottom: 20px;
    left: 0;
  }
}
