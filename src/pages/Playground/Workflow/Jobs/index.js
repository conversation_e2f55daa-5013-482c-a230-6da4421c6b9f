/* eslint-disable no-prototype-builtins */
import './index.less';

import { FilterBar, PaginationTable, Toast } from '~/components';
import { Button, DatePicker, Divider, Form, Input, Modal, Popconfirm, Radio } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import Cron from 'qnn-react-cron';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import MessageDrawer from '../components/MessageDrawer';
import reducer, * as actions from './state';

const CRON_PANES = { second: true, minute: true, hour: true, day: true, month: true, week: false, year: false };
@connect(
  (state) => {
    return state.chatBotWorkflowJobs;
  },
  actions,
)
export default class ChatBotWorkflowJobs extends Component {
  static propTypes = {
    total: PropTypes.number,
    jobs: PropTypes.array,
    pagination: PropTypes.object,
    fetchLogs: PropTypes.func.isRequired,
    fetchWorkflow: PropTypes.func.isRequired,
    fetchConversations: PropTypes.func.isRequired,
    fetchJobs: PropTypes.func.isRequired,
    getJob: PropTypes.func.isRequired,
    addJob: PropTypes.func.isRequired,
    updateJob: PropTypes.func.isRequired,
    delJob: PropTypes.func.isRequired,
    match: PropTypes.object,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    msgs: [],
    logs: [],
    flowParams: {},
    workflow: {},
  }

  componentDidMount = async () => {
    const { id } = this.props.match.params;
    this.props.fetchJobs({ flowId: id });
    const workflow = await this.props.fetchWorkflow(id);
    const { params } = JSON.parse(workflow?.content || '{}');
    this.setState({ workflow, flowParams: params });
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onSearch = async (e) => {
    const { id } = this.props.match.params;
    this.props.fetchJobs({ ...e, flowId: id });
  }

  onChangeJobValue = (e, key, isParams = false) => {
    const job = _.cloneDeep(this.state.job);
    const value = e?.target ? e.target.value : e;
    if (isParams) {
      job.params = job.params || {};
      job.params[key] = value;
    } else {
      job[key] = value;
    }
    this.setState({ job });
  }

  onShowEdit = async (row) => {
    const job = await this.props.getJob(row.id);
    this.setState({ open: true, job: { ...job, params: JSON.parse(job?.params || '{}') } });
  }

  onShowLog = async (job) => {
    const msgs = await this.props.fetchConversations(job.runId);
    const logs = await this.props.fetchLogs(job.runId);
    this.setState({ msgOpen: true, msgs, logs });
  }

  onSave = async () => {
    const allKeys = ['historyMode', 'name', 'userMessage'];
    const { id, params, historyMode, name, runAt, userMessage } = this.state.job;
    if (!_.isEmpty(this.state.flowParams)) {
      allKeys.push('params');
    }
    let cron = this.fns.getValue().replace(/\?/, '*');
    cron = cron.slice(0, -4);
    cron = cron === '* * * * *' ? undefined : cron;

    const data = {
      name,
      cron,
      runAt,
      historyMode,
      userMessage,
      flowUuid: this.state.workflow.uuid,
      params: JSON.stringify(params),
    };
    const hasKeyAndValue = (key) => { return data.hasOwnProperty(key) && Boolean(data[key]); };
    if (!allKeys.every(hasKeyAndValue)) {
      Toast.show('请检查参数!', Toast.Type.WARNING);
      return;
    }

    if (_.isUndefined(runAt) && _.isUndefined(cron)) {
      Toast.show('请选择执行时间 或 执行周期!', Toast.Type.WARNING);
      return;
    }

    if (_.isUndefined(id)) {
      await this.props.addJob(data);
    } else {
      await this.props.updateJob({ ...data, id });
    }
    this.setState({ job: {}, open: false });
    Toast.show('操作成功!', Toast.Type.SUCCESS);
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '名称', dataIndex: 'name', key: 'name', align: 'center' },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        align: 'center',
        render: (txt) => { return txt || '-'; },
      },
      {
        title: '类型',
        dataIndex: 'status',
        key: 'status',
        align: 'center',
        render: (txt, row) => {
          if (!_.isEmpty(row.runAt)) {
            return '定时';
          } else if (!_.isEmpty(row.cron)) {
            return '周期';
          }
          return '-';
        },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <Button
                type="link"
                disabled={!_.isEmpty(row.status)}
                onClick={() => { return this.onShowEdit(row); }}
              >
                编辑
              </Button>
              <Divider type="vertical" />
              <Button
                type="link"
                disabled={_.isEmpty(row.status)}
                onClick={() => { return this.onShowLog(row); }}
              >
                记录
              </Button>
              <Divider type="vertical" />
              <Popconfirm
                title="是否删除?!"
                onConfirm={() => { return this.props.delJob(row); }}
              >
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  renderCreateModal = () => {
    const { job, open, flowParams } = this.state;
    const cron = _.isEmpty(job?.cron) ? '* * * * * * *' : `${job?.cron} * *`;

    return (
      <Modal
        className="workflow-job-modal"
        width={1000}
        title="新增定时任务"
        open={open}
        maskClosable={false}
        onCancel={() => { return this.setState({ open: false }); }}
        onOk={this.onSave}
      >
        <Form labelCol={{ span: 4 }}>
          <Form.Item label="任务名称">
            <Input value={job?.name} onChange={(e) => { return this.onChangeJobValue(e, 'name'); }} />
          </Form.Item>
          <Form.Item label="运行配置">
            <Radio.Group
              value={job.historyMode}
              onChange={(e) => { return this.onChangeJobValue(e, 'historyMode'); }}
            >
              <Radio value="none">忽略节点历史消息</Radio>
              <Radio value="autofit">启用并自动压缩节点消息</Radio>
              <Radio value="all">启用全部节点历史消息</Radio>
            </Radio.Group>
          </Form.Item>
          {
            _.keys(flowParams).map((key) => {
              return (
                <Form.Item label={key}>
                  <Input.TextArea
                    autoSize={{ minRows: 2 }}
                    value={(job?.params || {})[key]}
                    onChange={(e) => { return this.onChangeJobValue(e, key, true); }}
                  />
                </Form.Item>
              );
            })
          }
          <Form.Item label="执行时间">
            <DatePicker
              value={_.isUndefined(job.runAt) ? null : moment(job.runAt)}
              showTime={{ format: 'HH:mm' }}
              format="YYYY-MM-DD HH:mm"
              onChange={(e) => { return this.onChangeJobValue(e.toISOString(), 'runAt'); }}
              onOk={(e) => { return this.onChangeJobValue(e.toISOString(), 'runAt'); }}
            />
          </Form.Item>
          <Form.Item label="执行周期">
            <Cron
              footer={null}
              panesShow={CRON_PANES}
              value={cron}
              getCronFns={(fns) => { this.fns = fns; }}
            />
          </Form.Item>
          <Form.Item label="输入">
            <Input.TextArea
              autoSize={{ minRows: 2 }}
              value={job.userMessage}
              onChange={(e) => { return this.onChangeJobValue(e, 'userMessage'); }}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  render = () => {
    const { jobs, total, pagination } = this.props;
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <FilterBar
          canAdd
          shouldShowSearchInput={false}
          onAdd={() => { return this.setState({ open: true, job: {} }); }}
        />
        <PaginationTable
          totalDataCount={total}
          pagination={pagination}
          dataSource={jobs}
          columns={this.renderColumns()}
          onPaginationChange={(e) => { return this.onSearch(e); }}
        />

        {this.state.open && this.renderCreateModal()}
        {
          this.state.msgOpen &&
          <MessageDrawer
            isJob
            logs={this.state.logs}
            messages={this.state.msgs}
            open={this.state.msgOpen}
            onChange={() => { }}
            onClose={() => { return this.setState({ msgOpen: false }); }}
          />
        }
      </div>
    );
  }
}

export {
  reducer,
};
