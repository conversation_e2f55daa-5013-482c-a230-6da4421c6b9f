import Configs from '~/consts';
import { ChatBot } from '~/engine';
import _ from 'lodash';

import { EVENT_TYPE } from '../../Configs';

const SET_STATE = 'CHAT_KNOWLEDGE/SET_STATE';
const CLEAR_STATE = 'CHAT_KNOWLEDGE/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchWorkflow = (uuid) => {
  return async () => {
    const data = await ChatBot.getChatbotWorkflow(uuid);
    return data;
  };
};

export const fetchLogs = (runId) => {
  return async () => {
    const { items } = await ChatBot.fetchWorkflowLogs({ ...Configs.ALL_PAGE_PARAMS, runId });
    const logs = [];
    _.reverse(items).forEach((x) => {
      if (x.logType !== EVENT_TYPE.FINAL_RESULT) {
        const obj = JSON.parse(x.content);
        logs.push({ message: obj?.output || obj?.prompt || obj?.msg });
      }
    });
    return logs;
  };
};

export const fetchConversations = (runId) => {
  return async () => {
    const { items } = await ChatBot.fetchChatbotSessionConversations({ runId });
    const messages = [];
    items.forEach((x) => { messages.push({ ...x, message: x.content, isClient: x.role !== 'USER' }); });
    return messages;
  };
};

export const fetchJobs = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().chatBotWorkflowJobs;
    const searchParams = {
      uuid: params?.flowId,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };

    const { items, total } = await ChatBot.fetchChatbotWorkflowJobs(searchParams);
    dispatch(
      setState({
        total,
        jobs: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const getJob = (id) => {
  return async () => {
    const job = await ChatBot.fetchChatbotWorkflowJob(id);
    return job;
  };
};

export const addJob = (params) => {
  return async (dispatch) => {
    await ChatBot.addChatbotWorkflowJob(params);
    dispatch(fetchJobs(params.flowUuid));
  };
};
export const updateJob = (params) => {
  return async (dispatch) => {
    await ChatBot.updateChatbotWorkflowJob(params);
    dispatch(fetchJobs(params.flowUuid));
  };
};

export const delJob = (job) => {
  return async (dispatch) => {
    await ChatBot.delChatbotWorkflowJob(job.id);
    dispatch(fetchJobs(job.flowUuid));
  };
};

const _getInitState = () => {
  return {
    jobs: [],
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
