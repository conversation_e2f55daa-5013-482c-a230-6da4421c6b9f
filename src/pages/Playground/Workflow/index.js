/* eslint-disable max-lines */
import 'reactflow/dist/style.css';

/* eslint-disable no-case-declarations, camelcase */
import './index.less';

import {
  CheckOutlined,
  ClockCircleOutlined,
  CloseOutlined,
  EditOutlined,
  FieldStringOutlined,
  FunctionOutlined,
  MessageOutlined,
  SaveOutlined,
  VerticalAlignTopOutlined,
} from '@ant-design/icons';
import { IconFont, Toast } from '~/components';
import Engine, { Sessions } from '~/engine';
import { Platform, StringExtension, Timer } from '~/plugins';
import { Button, Divider, Drawer, Form, Input, Modal, Popconfirm, Select, Tooltip, Typography } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import ReactFlow, { Background, Controls, ReactFlowProvider } from 'reactflow';

import { EVENT_TYPE, EVENT_TYPE_ZH } from '../Configs';
import Utils from '../Utils';
import ReconnectingWebSocket from '../WebSocket';
import ConstantDrawer from './components/ConstantDrawer';
import CreateJobModal from './components/CreateJobModal';
import { CloseEdge } from './components/CustomEdges';
import {
  CommonNode,
  DecisionNode,
  EndNode,
  FlowNode,
  KnowledgeNode,
  ProcessTxtNode,
  StartNode,
  SubflowNode,
} from './components/CustomNodes';
import { BASE_NODES, COMMON_NODE_ENUM, PROMPT_KEYS } from './components/CustomNodes/Configs';
import ExtraSidebar from './components/ExtraSidebar';
import FunctionDrawer from './components/FunctionDrawer';
import MessageDrawer from './components/MessageDrawer';
import SettingDrawer from './components/SettingDrawer';
import reducer, * as actions from './state';

const NodeTypes = {
  start: StartNode,
  end: EndNode,
  common: CommonNode,
  assistant: FlowNode,
  decision: DecisionNode,
  processTxt: ProcessTxtNode,
  subflow: SubflowNode,
  knowledge: KnowledgeNode,
};

@connect(
  (state) => {
    return {
      ...state.workflowDetail,
      fullFuncs: state.commonLayout.fullFuncs,
      globalFuncs: state.commonLayout.globalFuncs,
      globalApiFuncs: state.commonLayout.globalApiFuncs,
    };
  },
  actions,
)
export default class WorkflowDetail extends Component {
  static propTypes = {
    assistants: PropTypes.array,
    fullFuncs: PropTypes.array,
    globalFuncs: PropTypes.array,
    innerParams: PropTypes.object,
    globalApiFuncs: PropTypes.object,
    subflows: PropTypes.array,
    addJob: PropTypes.func,
    addWorkflow: PropTypes.func,
    fetchWorkflow: PropTypes.func,
    updateWorkflow: PropTypes.func,
    publishWorkflow: PropTypes.func,
    cancelWorkflow: PropTypes.func,
    fetchLibraries: PropTypes.func,
    delAssistant: PropTypes.func,
    addAssistant: PropTypes.func,
    updateAssistant: PropTypes.func,
    fetchAssistants: PropTypes.func,
    fetchRunLogs: PropTypes.func,
    fetchConversations: PropTypes.func,
    createConversation: PropTypes.func,
    updateConversation: PropTypes.func,
    deleteConversation: PropTypes.func,
    fetchChatbotWorkflows: PropTypes.func,
    fetchPublishInfo: PropTypes.func,
    fetchWorkflowGroups: PropTypes.func,
    createWorkflowGroup: PropTypes.func,
    fetchInnerParams: PropTypes.func,
    match: PropTypes.object,
    history: PropTypes.object,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    logs: [],
    messages: [],
    edges: [],
    nodes: [],
    params: {},
    targetNode: {},
    saveAsOpen: false,
    settingOpen: false,
    msgOpen: false,
    isEditName: false,
    flow: { name: '新工作流', type: 'workflow' },
    nodeCount: 0,
    nextStep: undefined,
    isBeta: true,
    canSendMsg: true,
  }

  constructor(props) {
    super(props);
    this.ws = { close: () => { } };
  }

  componentDidMount = async () => {
    const { id } = this.props.match.params;
    await this.props.fetchAssistants();
    await this.props.fetchChatbotWorkflows(id);
    this.props.fetchInnerParams();
    if (!_.isUndefined(id)) {
      await this.initFlow(id);
    } else {
      this.setState({
        nodes: [
          { id: 'start', name: '开始', data: {}, position: { x: 150, y: 420 }, type: 'start' },
          { id: 'end', name: '结束', data: {}, position: { x: 1200, y: 420 }, type: 'end' },
        ],
      });
    }
    this.onFulScreen();
    Platform.addEventListener('UPDATE_WORKFLOW_HOOK', this.onSaveHook);
    Platform.addEventListener('WORKFLOW_ADD_CONSTANT', this.onSaveParams);
    Platform.addEventListener('SHOW_NODE_ID', this.onShowNodeDrawer);
  }

  componentWillUnmount = () => {
    this.onFulScreen(false);
    this.ws?.close();
    this.props.clearState();
    Timer.clearInterval(this.pingTimer);
  }

  initFlow = async (id, workflow) => {
    const commonNodeTypes = _.keys(COMMON_NODE_ENUM);
    const knowledgeNodeTypes = _.flatten(_.values(PROMPT_KEYS));
    const optFuncObj = { onDel: this.onDelNode, onOpenSetting: this.onShowSetting };
    const {
      name, uuid, type, content, betaContent, successHook, failureHook,
    } = (workflow || await this.props.fetchWorkflow(id));
    const contentStr = this.state.isBeta ? betaContent : content;
    const { edges, nodes, params, history_mode } = JSON.parse(contentStr);
    let nodeIds = [];
    const newNodes = nodes.map((x) => {
      const extraParams = _.cloneDeep(x.data?.llm_setting?.extra_params);
      const flowData = _.cloneDeep(x.data.flow_data);
      const data = StringExtension.snakeToCamelObj(x.data);
      // 驼峰来回转, 此处hard code 处理 extraParams
      if (!_.isUndefined(extraParams)) {
        data.llmSetting.extraParams = Utils.formatExtraParams(extraParams, false);
      }
      if (x.type === COMMON_NODE_ENUM.saveData && !_.isUndefined(flowData)) {
        data.flowData = flowData;
      }
      if (knowledgeNodeTypes.includes(x.type)) {
        data.oType = data.oType || x.type;
        x.type = 'knowledge'; // eslint-disable-line
      }
      if (commonNodeTypes.includes(x.type)) {
        data.oType = data.oType || x.type;
        x.type = 'common'; // eslint-disable-line
      }
      nodeIds.push(_.last(Utils.formatSubtype(x.id)));
      return { ...x, name: data?.name, data: { ...data, ...optFuncObj } };
    });
    const newEdges = (edges || []).map((x) => { return StringExtension.snakeToCamelObj(x); });
    const logs = await this.props.fetchRunLogs(id);
    const messages = await this.props.fetchConversations(id);
    nodeIds = nodeIds.filter((x) => { return _.isInteger(+x); }).map((x) => { return _.toSafeInteger(x); });
    const maxNodeId = _.toSafeInteger(_.max(nodeIds)) + 2;
    await this.setState({
      logs: logs || [],
      messages: messages || [],
      params: params || {},
      historyMode: history_mode || 'autofit',
      nodes: newNodes || [],
      edges: newEdges || [],
      flow: { name, uuid, type },
      nodeCount: _.max([maxNodeId, nodes?.length]),
      hookObj: { successHook, failureHook },
    });
    setTimeout(() => { this.initWebSocket(); }, 0);
  }

  initWebSocket = () => {
    const { id } = this.props.match.params;
    if (this.ws) {
      this.ws.close();
    }
    const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflows/run/${id}`;
    const query = { access_token: Sessions.getToken() };
    this.ws = new ReconnectingWebSocket(`${path}?${qs.stringify(query)}`, [], this.onReceiveMsg);
    this.pingTimer = Timer.setInterval(() => { this.ws.send(JSON.stringify({ type: 'ping' })); }, 1000 * 30);
  }

  formatLogHtml = (style, type, msg) => {
    const typeName = EVENT_TYPE_ZH[type];
    return `<div style="background:#eee;padding:4px 6px;"><b style="${style}">[${typeName}]:</b>
<pre style="white-space: pre-wrap;margin-bottom:0;">${msg}</pre>
</div>\n`;
  }

  onSaveHook = (e) => {
    this.setState({ hookObj: e });
  }

  onShowNodeDrawer = () => {
    const { nodes } = this.state;
    const newNodes = [];
    nodes.forEach((x, idx) => {
      if (x.id !== 'start' && x.id !== 'end') {
        newNodes.push({ key: x.id, value: x.data.name, idx });
      }
    });

    this.setState({ openNodeDrawer: true, nodeList: newNodes });
  }


  onChangeNodeList = (e, key, idx) => {
    const value = e?.target ? e?.target.value : e;
    const nodeList = this.state.nodeList.map((x) => {
      if (x.idx === idx) {
        return { ...x, [key]: value };
      }
      return x;
    });
    this.setState({ nodeList });
  }

  onSaveNodeList = async () => {
    const nodes = _.cloneDeep(this.state.nodes);
    const edges = _.cloneDeep(this.state.edges);
    this.state.nodeList.forEach((x) => {
      edges.forEach((e) => {
        if (e.source === nodes[x.idx].id) {
          e.source = x.key;
        }
        if (e.target === nodes[x.idx].id) {
          e.target = x.key;
        }
      });
      nodes[x.idx].id = x.key;
      nodes[x.idx].name = x.value;
      nodes[x.idx].data.name = x.value;
    });
    await this.setState({ nodes, edges, openNodeDrawer: false, settingOpen: false, setting: {} });
    await this.onSave(false);
    await this.initFlow(this.props.match.params.id);
  }


  onSwitchVersion = async () => {
    const { id } = this.props.match.params;
    const data = await this.props.fetchWorkflow(id);
    const key = this.state.isBeta ? 'content' : 'betaContent';
    if (_.isEmpty(JSON.parse(data[key]))) {
      Toast.show('请先发布!', Toast.Type.WARNING);
      return;
    }
    await this.setState({ isBeta: !this.state.isBeta });
    await this.initFlow(id, data);
  }

  onPredecessors = (nodeId, edges) => {
    const predecessors = [];
    const findPredecessors = (id) => {
      edges.forEach((edge) => {
        if (edge.target === id) {
          if (edge.source === 'start') {
            predecessors.push({ id: '#<Start>#', value: edge.name });
          } else {
            predecessors.push({ id: `#<${edge.source}>#`, value: edge.name });
          }
          findPredecessors(edge.source);
        }
      });
    };
    findPredecessors(nodeId);
    return predecessors;
  };

  onFulScreen = (isHidden = true) => {
    const domChat = document.getElementsByClassName('chat-container')[0] || {};
    const domSider = document.getElementsByClassName('bzy-sider')[0] || {};
    (domChat.style || {}).height = isHidden ? 'calc(100vh - 50px)' : 'calc(100vh - 120px)';
    (domSider.style || {}).display = isHidden ? 'none' : 'unset';
  }

  onReceiveMsg = (e) => {
    if (e?.data !== 'pong') {
      const originData = JSON.parse(e.data);
      if (originData?.job_id) {
        this.setState({ jobId: originData?.job_id });
      }

      const { type, data } = StringExtension.snakeToCamelObj(originData);
      const nodeIdStr = _.trim(data?.nodeId, ' ');
      if (type === EVENT_TYPE.LLM_STEP_RESPONSE && _.isEmpty(data?.token)) {
        return;
      }

      switch (type) {
        case EVENT_TYPE.EXEC_STEP:
          const createdAt = moment().format('YYYY-MM-DD HH:mm');
          if (['Start', 'Done'].includes(nodeIdStr)) {
            this.setState({ logs: [...this.state.logs, { nodeId: nodeIdStr, message: '', createdAt }] });
          } else {
            const nodeId = _.last(nodeIdStr.split('@'));
            const node = this.state.nodes.find((x) => { return x.id === nodeId; });
            this.setState({
              logs: [
                ...this.state.logs,
                { message: '', nodeId: node?.data?.name || data.nodeName, createdAt },
              ],
            });
          }
          break;
        case EVENT_TYPE.OP_RESULT:
        case EVENT_TYPE.EXEC_FAILED:
        case EVENT_TYPE.TOOL_INPUT:
        case EVENT_TYPE.TOOL_OUTPUT:
        case EVENT_TYPE.LLM_REQUEST:
        case EVENT_TYPE.LLM_TOKEN_USAGE:
          this.llmStepResponse = '';
          this.beforeStepResponse = '';
          if (type === EVENT_TYPE.EXEC_FAILED) {
            this.setState({ jobId: undefined, canSendMsg: true });
          }
          const logs = _.clone(this.state.logs);
          const lastLog = logs.pop();
          const style = type === EVENT_TYPE.EXEC_FAILED ? 'color:red' : '';
          let msg = data?.output || data?.msg || data?.prompt;
          if ([EVENT_TYPE.OP_RESULT].includes(type)) {
            try {
              const msgObj = JSON.parse(msg);
              delete msgObj.raw_content;
              msg = JSON.stringify(msgObj);
            } catch (error) {
              // nothing
            }
          }
          if ([EVENT_TYPE.LLM_TOKEN_USAGE].includes(type)) {
            try { msg = JSON.stringify(JSON.parse(msg), null, 2); } catch (error) { /* nothing*/ }
          }
          lastLog.message += this.formatLogHtml(style, type, msg);
          lastLog.message = _.trimStart(lastLog.message, '\n');
          this.setState({ logs: [...logs, lastLog] });
          break;
        case EVENT_TYPE.LLM_STEP_RESPONSE:
          const stepLogs = _.clone(this.state.logs);
          const lastSetpLog = stepLogs.pop();
          if (!_.isEmpty(lastSetpLog.message) && _.isEmpty(this.llmStepResponse)) {
            this.beforeStepResponse = lastSetpLog.message;
          }

          this.llmStepResponse += data?.token;
          if (!_.isEmpty(this.llmStepResponse)) {
            lastSetpLog.message = this.formatLogHtml(style, type, this.llmStepResponse);
            lastSetpLog.message = this.beforeStepResponse + _.trimStart(lastSetpLog.message, '\n');
            this.setState({ logs: [...stepLogs, lastSetpLog] });
          }
          break;
        case EVENT_TYPE.EXEC_LOG:
          if (data.msg !== 'Workflow run start') {
            const execLogs = _.clone(this.state.logs);
            const lastExecLog = execLogs.pop();
            lastExecLog.message += this.formatLogHtml(style, type, data.msg);
            lastExecLog.message = _.trimStart(lastExecLog.message, '\n');
            this.setState({ logs: [...execLogs, lastExecLog] });
          }
          break;
        case EVENT_TYPE.EXEC_ACTION_REQUIRED:
          this.setState({
            canSendMsg: true,
            messages: [
              ...this.state.messages,
              { message: (data?.output || data?.msg), isClient: true, createdAt: moment().format('YYYY-MM-DD HH:mm') },
            ],
          });
          break;
        case EVENT_TYPE.FINAL_RESULT:
          this.llmStepResponse = '';
          this.beforeStepResponse = '';
          this.setState({
            canSendMsg: true,
            jobId: undefined,
            messages: [
              ...this.state.messages,
              { message: (data?.output || data?.msg), isClient: true, createdAt: moment().format('YYYY-MM-DD HH:mm') },
            ],
          });
          break;
        default:
          break;
      }
    }
  }

  onDelNode = (nodeId) => {
    const edges = this.state.edges.filter((x) => { return !_.includes(x.id, nodeId); });
    let nodes = this.state.nodes.filter((x) => { return x.id !== nodeId; });
    const items = edges.map((x) => {
      const { name } = nodes.find((n) => { return n.id === x.source; });
      return { ...x, name };
    });
    nodes = (nodes || []).map((x) => {
      if (!_.isEmpty(x.data.inputs)) {
        const inputs = x.data.inputs.filter((s) => { return !_.includes(s.fixedStr, nodeId); });
        return { ...x, data: { ...x.data, inputs } };
      }
      x.data.controls = this.onPredecessors(x.id, items); // eslint-disable-line
      return x;
    });
    this.setState({ nodes, edges });
  }

  onShowSetting = async (nodeId) => {
    const info = this.state.nodes.find((x) => { return x.id === nodeId; });
    const subType = info.data.oType || _.head(Utils.formatSubtype(nodeId));
    const items = this.state.edges.map((x) => {
      const { name } = this.state.nodes.find((n) => { return n.id === x.source; });
      return { ...x, name };
    });
    let flowId = this.props.match.params.id;
    let messages = [];
    let libraries = [];
    if (info.type === 'subflow') {
      flowId = `${flowId}@${info.data.uuid}`;
      messages = await this.props.fetchConversations(flowId);
      const subflow = await this.props.fetchWorkflow(info.data.uuid);
      const { params } = JSON.parse(subflow?.content);
      info.data.params = params;
    } else if (subType === COMMON_NODE_ENUM.switchCase) {
      const cases = items.filter((x) => { return x.source === nodeId; });
      const defaultConditions = [];
      cases.forEach((x) => {
        const sc = info?.data?.switchConditions.find((s) => { return s.output === x.target; });
        const item = sc || { output: x.target, conditions: [{}], type: x.sourceHandle || 'case' };
        defaultConditions.push(item);
      });
      info.data.switchConditions = defaultConditions;
    } else if (
      [
        COMMON_NODE_ENUM.saveVector, 'searchKb', 'searchCiteKb', 'summaryOutline',
        'articleMaterialExtract', 'articleMaterialSearchAndMerge',
      ].includes(subType)) {
      libraries = await this.props.fetchLibraries();
    } else if (subType === COMMON_NODE_ENUM.text2video) {
      info.publishInfo = await this.props.fetchPublishInfo();
    } else if (subType === 'assistant') {
      libraries = await this.props.fetchLibraries();
      messages = await this.props.fetchConversations(flowId, nodeId);
    } else {
      messages = await this.props.fetchConversations(flowId, nodeId);
    }

    const controls = this.onPredecessors(nodeId, items).map((x) => {
      if (x.id === '#<Start>#') {
        return { ...x, value: 'start' };
      }
      return x;
    });
    info.data.controls = _.uniqBy(controls || [], 'id');
    info.data.inputs = (info.data.inputs || []).map((x) => {
      const o = info.data.controls.find((c) => { return c.id === x.fixedStr; });
      return _.isUndefined(o) ? x : { ...x, showStr: o.value };
    });
    this.setState({ settingOpen: true, setting: { ...info, messages, libraries } });
  }

  onChangeNodes = async (e) => {
    const { id, position, dimensions } = e || {};
    if (!_.isUndefined(position)) {
      const nodes = this.state.nodes.map((x) => { return x.id === id ? { ...x, position } : x; });
      await this.setState({ nodes });
    }

    if (!_.isUndefined(dimensions)) {
      const nodes = this.state.nodes.map((x) => {
        if (x.id === id) {
          return {
            ...x,
            data: { ...(x.data || {}), ...(dimensions || {}) },
            style: { ...(x.style || {}), ...(dimensions || {}) },
          };
        }
        return x;
      });
      await this.setState({ nodes });
    }
  }

  onNodeDrag = (node) => {
    const centerX = node.position.x + node.width / 2;
    const centerY = node.position.y + node.height / 2;
    const targetNode = this.state.nodes.find((n) => {
      return centerX > n.position.x &&
        centerX < (n.position.x + n.data.width) &&
        centerY > n.position.y &&
        centerY < (n.position.y + n.data.height) &&
        n.id !== node.id;
    });
    this.setState({ targetNode });
  }

  onNodeDragStop = (node) => {
    if (_.isUndefined(this.state.targetNode)) return;
    const { id, position, type } = this.state.targetNode;
    if (type !== 'group') return;

    const nodes = this.state.nodes.map((n) => {
      if (n.id === node.id) {
        return {
          ...n,
          position: { x: node.position.x - position.x, y: node.position.y - position.y },
          parentNode: id,
          zIndex: (n?.zIndex || 0) + 10,
          extent: 'parent',
        };
      }
      return n;
    });
    this.setState({ nodes });
  }

  onUpdateEdge = async (o, n) => {
    const edges = this.state.edges.filter((x) => { return x.id !== o.id; });
    await this.setState({ edges });
    this.onConnect(n);
  }

  onDelEdge = async (id) => {
    const n = this.state.edges.find((x) => { return x.id === id; });
    const edges = this.state.edges.filter((x) => { return x.id !== n.id; });
    const items = edges.map((x) => {
      const { name } = this.state.nodes.find((m) => { return m.id === x.source; });
      return { ...x, name };
    });
    let nodes = this.state.nodes.map((x) => {
      if (x.id === n.target) {
        const inputs = (x.data.inputs || []).filter((i) => {
          const str = i.showStr === 'Start' ? `#<${_.lowerCase(i.fixedStr)}>#` : i.fixedStr;
          return !_.startsWith(str, `#<${n.source}>#`);
        });
        return { ...x, data: { ...x.data, inputs } };
      }
      x.data.controls = this.onPredecessors(x.id, items); // eslint-disable-line
      return x;
    });

    // 移除 decision edge, 将下一级 的outputs移除
    if (n.target.indexOf('decision') >= 0) {
      const { inputs } = this.state.nodes.find((x) => { return x.id === n.target; })?.data;
      const nodeIds = _.map(this.state.edges.filter((x) => { return x.source === n.target; }), 'target');
      const inputIds = _.map(inputs, 'fixedStr');
      nodes = nodes.map((x) => {
        if (nodeIds.includes(x.id)) {
          const newInputs = (x.data.inputs || []).filter((i) => { return !inputIds.includes(i.fixedStr); });
          return { ...x, data: { ...x.data, inputs: newInputs } };
        }
        return x;
      });
    }

    await this.setState({ edges, nodes });
  }

  onConnect = (e) => {
    const { nodes, edges } = this.state;
    const newEdge = { id: `edge-${e.source}-${e.target}`, ...e, type: 'step', style: { strokeWidth: 2 } };
    const targetNode = nodes.find((x) => { return x.id === e.target; });
    const sourceNode = nodes.find((x) => { return x.id === e.source; });
    let newNodes = nodes;
    const { type, data, id } = sourceNode;
    const isStartNode = type === 'start';
    const fixedStr = isStartNode ? '#<Start>#' : `#<${id}>#`;
    let inputs = (targetNode?.data?.inputs || []).filter((x) => { return x.fixedStr !== fixedStr; });

    if (['start', 'assistant', 'processTxt', 'common', 'knowledge', 'subflow'].includes(type)) {
      inputs.push({
        fixedStr,
        content: fixedStr,
        showStr: isStartNode ? 'Start' : `${data?.name}`,
        type: e.targetHandle || 'assistant',
      });
    }
    if (type === 'decision') {
      inputs = [...inputs, ...(sourceNode?.data?.inputs || [])];
    }

    inputs = _.uniqBy(inputs || [], 'fixedStr');
    const newEdges = [...edges, newEdge];
    const items = newEdges.map((x) => {
      const { name } = newNodes.find((n) => { return n.id === x.source; });
      return { ...x, name };
    });
    newNodes = nodes.map((x) => {
      x.data.controls = this.onPredecessors(x.id, items); // eslint-disable-line
      return x.id === targetNode.id ? { ...x, data: { ...x.data, inputs } } : x;
    });

    this.setState({ edges: newEdges, nodes: newNodes });
  }

  onDrop = (e) => {
    e.preventDefault();
    const { nodes, nodeCount } = this.state;
    const reactFlowBounds = this.reactFlowWrapper.getBoundingClientRect();
    const objStr = e.dataTransfer.getData('application/reactflow');
    if (typeof objStr === 'undefined' || !objStr) return;
    let nodeObj = {};
    try { nodeObj = JSON.parse(objStr); } catch { return; }
    const position = this.reactFlowInstance.project({
      x: e.clientX - reactFlowBounds.left - 200,
      y: e.clientY - reactFlowBounds.top,
    });
    const optFuncObj = { onDel: this.onDelNode, onOpenSetting: this.onShowSetting };
    const name = `${nodeObj.name || nodeObj.nodeName}-${nodeCount + 1}`;
    let node = { position, id: nodeObj?.type, type: nodeObj?.nodeType || nodeObj?.type, name };
    if (_.isUndefined(nodeObj?.type)) { // 角色模块 无 type
      node = {
        ...node,
        type: 'assistant',
        id: `assistant_${nodeObj?.id}_${nodeCount}`,
        data: { ...nodeObj, ...optFuncObj, name, assistantId: nodeObj?.id },
      };
    }
    // 非 开始/结束 节点
    if (nodeObj?.type && !_.map(BASE_NODES, 'type').includes(nodeObj?.type)) {
      node = { ...node, id: `${nodeObj?.type}_${nodeCount}`, data: optFuncObj };
    }
    if (node?.type === 'subflow') {
      node = { ...node, data: { ...node.data, uuid: nodeObj?.uuid, name } };
    }
    if (node?.type === 'knowledge' || node?.type === 'common') {
      node = { ...node, data: { ...node?.data, ...nodeObj?.data, name } };
    }

    this.setState({ nodes: [...nodes, node], nodeCount: nodeCount + 1 });
  }

  onDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  }

  onChangeFlow = (e, type) => {
    const value = e?.target ? e?.target.value : e;
    this.setState({ flow: { ...this.state.flow, [type]: value } });
  }

  onSaveSetting = async (node) => {
    const originNode = this.state.nodes.find((x) => { return x.id === node.id; });
    let nodes = this.state.nodes.map((x) => { return x.id === node.id ? node : x; });
    if (!_.isEqual(originNode?.data?.name, node?.data?.name)) {
      const selectEdges = this.state.edges.filter((x) => { return x.source === node.id; });
      const nodeIds = _.map(selectEdges, 'target');
      nodes = nodes.map((x) => {
        if (nodeIds.includes(x.id)) {
          const inputs = x?.data?.inputs.map((i) => {
            if (_.startsWith(i.fixedStr, node.id)) {
              const showStr = node.data.name;
              return { ...i, showStr };
            }
            return i;
          });
          return { ...x, data: { ...x.data, inputs } };
        }
        x.name = x?.data?.name || x.name; // eslint-disable-line
        return x;
      });
    }
    await this.setState({ nodes, settingOpen: false, setting: {} });
  }

  onSaveParams = async (params, type = 'params') => {
    await this.setState({ [type]: params });
    await this.onSave(false);
  }

  onSubmitJob = async (params) => {
    await this.props.addJob(params);
    Toast.show('操作成功!', Toast.Type.SUCCESS);
    this.setState({ jobOpen: false });
  }

  onRun = async () => {
    if (_.isUndefined(this.state.flow?.uuid)) {
      this.setState({ saveAsOpen: true, nextStep: 'run' });
    } else {
      await this.onSave(false);
      this.setState({ msgOpen: true });
    }
  }
  onCancelWorkflow = async (flowId, jobId) => {
    await this.props.cancelWorkflow(flowId, jobId);
    this.setState({ jobId: undefined, canSendMsg: true }, () => {
      this.initWebSocket();
    });
  }

  onSendMsg = async (text) => {
    if (!this.state.canSendMsg) {
      Toast.show('当前WorkFlow执行中');
      return;
    }

    this.ws.send(JSON.stringify({
      text,
      type: _.isUndefined(this.state.jobId) ? 'message' : 'action_confirm',
      is_beta: this.state.isBeta,
    }));
    this.outputing = true;
    await this.setState({
      canSendMsg: false,
      messages: [
        ...this.state.messages,
        { message: text, isClient: false, createdAt: moment().format('YYYY-MM-DD HH:mm') },
      ],
    });
  }

  onSave = async (showTips = true) => {
    const { flow, nodes, edges, params, historyMode, nextStep, hookObj } = this.state;
    if (_.isEmpty(nodes) || _.isEmpty(edges)) {
      Toast.show('当前WORKFLOW规范', Toast.Type.WARNING);
      return;
    }
    if (_.isEmpty(flow.name)) {
      Toast.show('请完善信息', Toast.Type.WARNING);
      return;
    }
    const newNodes = _.cloneDeep(nodes).map((x) => {
      const extraParams = _.cloneDeep(x.data?.llmSetting?.extraParams);
      const flowData = _.cloneDeep(x.data?.flowData);
      const { onDel, onOpenSetting, inputs, llmSetting, ...otherData } = x.data;
      const data = StringExtension.camelToSnakeObj({ ...otherData, llmSetting });
      // 驼峰来回转, 此处hard code 处理 extraParams
      if (!_.isUndefined(extraParams)) {
        data.llm_setting.extra_params = Utils.formatExtraParams(extraParams, true);
      }
      if (!_.isUndefined(flowData)) {
        data.flow_data = flowData;
      }

      delete x.messages; // eslint-disable-line
      if (x.type === 'knowledge' || x.type === 'common') {
        x.type = x.data.oType || _.head(Utils.formatSubtype(x.id)); // eslint-disable-line
      }
      return { ...x, data: { ...data, inputs } };
    });

    const newEdges = edges.map((x) => { return StringExtension.camelToSnakeObj(x); });
    const contentObj = { nodes: newNodes, edges: newEdges, params: params || {}, history_mode: historyMode };
    const obj = { ...flow, ...hookObj, betaContent: JSON.stringify(contentObj) };
    if (_.isUndefined(flow?.uuid)) {
      const result = await this.props.addWorkflow(obj);
      await this.initFlow(result.uuid);
      this.$replace(`/workflow/${result.uuid}`);
    } else {
      await this.props.updateWorkflow(obj);
    }
    this.setState({ saveAsOpen: false, isEditName: false, nextStep: undefined });
    if (nextStep === 'run') { this.onRun(); }
    if (showTips && _.isUndefined(nextStep)) {
      Toast.show('操作成功!', Toast.Type.SUCCESS);
    }
  }

  onSwitchFlow = async (id) => {
    Modal.confirm({
      title: '是否切换工作流?',
      content: '切换前, 请确认是否已保存当前工作流',
      onOk: async () => {
        this.props.fetchChatbotWorkflows(id);
        await this.initFlow(id);
        this.$replace(`/workflow/${id}`);
      },
    });
  }

  onPublish = async () => {
    await this.onSave();
    await this.props.publishWorkflow(this.props.match.params.id);
    Toast.show('发布成功!', Toast.Type.SUCCESS);
  }

  onBack = () => {
    this.onFulScreen(false);
    this.props.history.goBack();
  }

  renderName = () => {
    const { flow } = this.state;
    if (_.isUndefined(flow?.uuid)) return null;
    let content = null;

    if (this.state.isEditName) {
      content = (
        <>
          <Input
            value={flow?.name}
            style={{ width: 200 }}
            onChange={(e) => { return this.onChangeFlow(e, 'name'); }}
          />&nbsp;
          <CheckOutlined
            style={{ color: 'green', fontSize: 24 }}
            onClick={() => { return this.onSave(); }}
          />&nbsp;
          <CloseOutlined
            style={{ color: 'red', fontSize: 24 }}
            onClick={() => { return this.setState({ isEditName: false }); }}
          />
        </>
      );
    } else {
      content = (
        <>
          <Typography.Title level={3} style={{ marginBottom: 0 }}>{flow?.name}</Typography.Title>
          &nbsp;
          <EditOutlined
            style={{ color: '#000', fontSize: 24 }}
            onClick={() => { return this.setState({ isEditName: true }); }}
          />
        </>
      );
    }
    return (<div className="flow-name">{content}</div>);
  }

  renderSwitchFlow = (flow) => {
    return (
      <div className="btn-back">
        <Select
          showSearch
          filterOption={(input, option) => { return option.children.includes(input); }}
          style={{ width: 200 }}
          size="large"
          value={flow.uuid}
          onChange={this.onSwitchFlow}
        >
          <Select.Option value={flow.uuid}>{flow?.name}</Select.Option>
          {this.props.subflows.map((x) => { return <Select.Option value={x.uuid}>{x?.name}</Select.Option>; })}
        </Select>
      </div>
    );
  }

  renderSaveAsModal = () => {
    const { flow, saveAsOpen } = this.state;
    return (
      <Modal
        title="保存"
        visible={saveAsOpen}
        onOk={this.onSave}
        onCancel={() => { return this.setState({ saveAsOpen: false }); }}
      >
        <Form labelCol={{ span: 4 }}>
          <Form.Item label="名称">
            <Input
              onChange={(e) => { return this.onChangeFlow(e, 'name'); }}
              value={flow?.name}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  renderBtn = (params = {}, isTooltip = false, isConfirm = false) => {
    let content = (
      <Button type="primary" shape={params?.name ? 'round' : 'circle'} {...params}>{params.name}</Button>
    );
    const title = params?.confirmTitle || '请确认是否已保存!?';
    const onConfirm = params?.onConfirm || this.onBack;
    content = isTooltip ? <Tooltip title={params.tip} placement="bottom" >{content}</Tooltip> : content;
    return isConfirm ? <Popconfirm onConfirm={onConfirm} title={title}>{content}</Popconfirm> : content;
  }

  renderBtns = () => {
    const { flow, isBeta } = this.state;
    const btns = [
      {
        icon: <ClockCircleOutlined />,
        style: { margin: '0 10px' },
        onClick: () => { return this.setState({ jobOpen: true }); },
        tip: '定时',
        isTooltip: true,
      },
      { icon: <FunctionOutlined />, tip: '函数', onClick: () => { return this.setState({ funcOpen: true }); } },
      isBeta ?
        {
          icon: <SaveOutlined />,
          style: { margin: '0 10px' },
          onClick: () => { return _.isUndefined(flow?.uuid) ? this.setState({ saveAsOpen: true }) : this.onSave(); },
          tip: '保存',
          isTooltip: true,
        } : null,
      isBeta ?
        {
          name: '发布',
          icon: <VerticalAlignTopOutlined />,
          style: { margin: '0 10px' },
          isConfirm: true,
          confirmTitle: '发布后，将会覆盖线上所有使用该工作流的场景。确保测试通过后发布。',
          onConfirm: () => { return this.onPublish(); },
        } : null,
      {
        name: '常量',
        icon: <FieldStringOutlined />,
        style: { margin: '0 10px' },
        onClick: () => { return this.setState({ constantOpen: true }); },
      },
      {
        name: '运行',
        icon: <MessageOutlined />,
        onClick: this.onRun,
      },
    ];
    return (
      <div style={{ position: 'absolute', top: 4, right: 10, zIndex: 10 }}>
        {
          btns.filter((x) => { return !_.isEmpty(x); }).map(({ isTooltip, isConfirm, ...x }) => {
            return this.renderBtn(x, isTooltip, isConfirm);
          })
        }
      </div>
    );
  }

  renderCopyBtn = (value) => {
    return (
      <Button
        type="text"
        onClick={async () => {
          await navigator.clipboard.writeText(`{{ ${value} }}`);
          Toast.show('复制成功!', Toast.Type.SUCCESS);
        }}
      >复制
      </Button>
    );
  }

  renderNodeDrawer = () => {
    const { nodeList } = this.state;
    const { filters, runtimeParams } = this.props.innerParams;
    return (
      <Drawer
        title="节点信息"
        placement="right"
        closable={false}
        width="30vw"
        onClose={() => { return this.setState({ openNodeDrawer: false }); }}
        visible={this.state.openNodeDrawer}
        extra={<Button onClick={() => { return this.onSaveNodeList(); }}>保存</Button>}
      >
        <>
          <Form labelCol={{ span: 4 }}>
            {
              nodeList.map((x) => {
                return (
                  <Form.Item label={`节点:${x.idx}`}>
                    <Input
                      value={x.key}
                      style={{ marginBottom: 4 }}
                      addonBefore={<div style={{ width: 80 }}>节点ID</div>}
                      addonAfter={this.renderCopyBtn(x.key)}
                      onChange={(e) => { return this.onChangeNodeList(e, 'key', x.idx); }}
                    />
                    <Input
                      value={x.value}
                      addonBefore={<div style={{ width: 80 }}>节点名称</div>}
                      onChange={(e) => { return this.onChangeNodeList(e, 'value', x.idx); }}
                    />
                  </Form.Item>
                );
              })
            }
          </Form>
          <Divider orientation="left">内置函数</Divider>
          {
            (filters || []).map((x) => {
              return <Input value={x.key} addonAfter={this.renderCopyBtn(x.key)} />;
            })
          }
          <Divider orientation="left">运行时参数</Divider>
          {
            (runtimeParams || []).map((x) => {
              return <Input value={x} addonAfter={this.renderCopyBtn(x)} />;
            })
          }
        </>
      </Drawer>
    );
  }

  render = () => {
    const { assistants, subflows } = this.props;
    const { nodes, edges, flow, isBeta, hookObj } = this.state;
    const flowEdges = edges.map((x) => { return { ...x, type: 'default' }; });

    return (
      <div className="workflow-container">
        <ReactFlowProvider>
          <div ref={(v) => { this.reactFlowWrapper = v; }} style={{ display: 'flex' }}>
            <div className="model-list">
              <ExtraSidebar
                assistants={assistants}
                subflows={subflows}
                onDel={this.props.delAssistant}
                onUpdate={this.props.updateAssistant}
              />
            </div>
            <div className="flow-wrap">
              <ReactFlow
                nodes={nodes}
                edges={flowEdges}
                nodeTypes={NodeTypes}
                edgeTypes={{ default: (e) => { return <CloseEdge {...e} onDel={this.onDelEdge} />; } }}
                onNodesChange={([e]) => { return this.onChangeNodes(e); }}
                onNodeDrag={(e, node) => { return this.onNodeDrag(node); }}
                onNodeDragStop={(e, node) => { return this.onNodeDragStop(node); }}
                onEdgeUpdate={(o, n) => { return this.onUpdateEdge(o, n); }}
                onInit={(e) => { this.reactFlowInstance = e; }}
                onConnect={(e) => { return this.onConnect(e); }}
                onDrop={(e) => { return this.onDrop(e); }}
                onDragOver={(e) => { return this.onDragOver(e); }}
              >
                <Background variant="cross" />
                <Controls />
              </ReactFlow>
            </div>
          </div>
        </ReactFlowProvider>
        {this.renderBtns()}
        {this.state.saveAsOpen && this.renderSaveAsModal()}
        {
          this.state.settingOpen &&
          <SettingDrawer
            flowId={this.state.flow?.uuid}
            open={this.state.settingOpen}
            node={this.state.setting}
            params={this.state.params}
            historyMode={this.state.historyMode}
            fullFuncs={this.props.fullFuncs}
            globalFuncs={this.props.globalFuncs}
            globalApiFuncs={this.props.globalApiFuncs}
            nodes={nodes}
            subflows={subflows}
            hookObj={hookObj}
            onSave={this.onSaveSetting}
            onSaveParams={this.onSaveParams}
            addAssistant={this.props.addAssistant}
            createConversation={this.props.createConversation}
            updateConversation={this.props.updateConversation}
            deleteConversation={this.props.deleteConversation}
            fetchWorkflowGroups={this.props.fetchWorkflowGroups}
            createWorkflowGroup={this.props.createWorkflowGroup}
            onClose={() => { return this.setState({ settingOpen: false, setting: {} }); }}
          />
        }
        {
          this.state.constantOpen &&
          <ConstantDrawer
            open={this.state.constantOpen}
            params={this.state.params}
            onSave={this.onSaveParams}
            onClose={() => { return this.setState({ constantOpen: false }); }}
          />
        }
        {
          this.state.msgOpen &&
          <MessageDrawer
            flow={flow}
            nodes={nodes}
            logs={this.state.logs}
            jobId={this.state.jobId}
            messages={this.state.messages}
            historyMode={this.state.historyMode}
            params={this.state.params}
            open={this.state.msgOpen}
            onSend={this.onSendMsg}
            onChange={this.onSaveParams}
            onCancel={this.onCancelWorkflow}
            onClose={() => { return this.setState({ msgOpen: false }); }}
          />
        }
        {
          this.state.jobOpen &&
          <CreateJobModal
            open={this.state.jobOpen}
            workflow={this.state.flow}
            flowParams={this.state.params}
            onClose={() => { return this.setState({ jobOpen: false }); }}
            onSave={this.onSubmitJob}
          />
        }
        {
          this.state.funcOpen &&
          <FunctionDrawer
            open={this.state.funcOpen}
            params={this.state.params}
            workflow={this.state.flow}
            onClose={() => { return this.setState({ funcOpen: false }); }}
          />
        }
        {this.renderSwitchFlow(flow)}
        {this.renderName()}
        <IconFont
          type={isBeta ? 'beta' : 'release'}
          onClick={() => { return this.onSwitchVersion(); }}
          style={{ fontSize: 108, position: 'absolute', right: 0, bottom: 0 }}
        />
        {this.state.openNodeDrawer && this.renderNodeDrawer()}
      </div>
    );
  }
}

export {
  reducer,
};
