import { DeleteFilled, PlusOutlined } from '@ant-design/icons';
import { InputUpload, PaginationTable, Toast } from '~/components';
import { Button, Divider, Drawer, Form, Input, Popconfirm, Select } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

const DEFAULT_RULE = {
  keyword: '',
  type: 'simple',
  contents: [{ item: { simple: '', content: '', fileUrl: '', list: [{}] }, msgtype: 'simple' }],
};
@connect(
  (state) => {
    return state.transforms;
  },
  actions,
)
export default class Transforms extends Component {
  static propTypes = {
    list: PropTypes.array.isRequired,
    total: PropTypes.number.isRequired,
    pagination: PropTypes.object.isRequired,
    fetchGroups: PropTypes.func.isRequired,
    createGroup: PropTypes.func.isRequired,
    updateGroup: PropTypes.func.isRequired,
    deleteGroup: PropTypes.func.isRequired,
    fetchRules: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    openGroup: false,
    group: {},
  }

  componentDidMount = async () => {
    await this.props.fetchGroups();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  // idx: rule's idx  // cIdx: content's idx  // index: menu's idx
  onChangeKFMenu = (e, key, idx, cIdx, index) => {
    const value = e?.target ? e?.target.value : e;
    const rules = _.cloneDeep(this.state.group.rules);
    if (index !== undefined) {
      rules[idx].contents[cIdx].item.list[index][key] = value;
    } else {
      rules[idx].contents[cIdx].item[key] = value;
    }
    this.setState({ group: { ...this.state.group, rules } });
  }

  onAddKFMenus = (idx, cIdx) => {
    const rules = _.cloneDeep(this.state.group.rules);
    rules[idx].contents[cIdx].item.list.push({});
    this.setState({ group: { ...this.state.group, rules } });
  }

  onDeleteKFMenus = (idx, cIdx, index) => {
    const rules = _.cloneDeep(this.state.group.rules);
    if (rules[idx].contents[cIdx].item.list.length === 1) {
      return;
    }
    rules[idx].contents[cIdx].item.list.splice(index, 1);
    this.setState({ group: { ...this.state.group, rules } });
  }

  onChangeRuleValue = (e, idx, key) => {
    const value = e?.target ? e?.target.value : e;
    const rules = _.cloneDeep(this.state.group.rules);
    rules[idx][key] = value;

    if (key === 'type') {
      const contents = _.cloneDeep(DEFAULT_RULE.contents);
      if (value === 'wework_kf') {
        contents[0].msgtype = 'text';
      }
      rules[idx].contents = contents;
    }

    this.setState({ group: { ...this.state.group, rules } });
  }

  onChangeRuleContentValue = (e, idx, cIdx, key) => {
    const value = e?.target ? e?.target.value : e;
    const rules = _.cloneDeep(this.state.group.rules);
    if (key !== 'msgtype') {
      rules[idx].contents[cIdx].item[key] = value;
    } else {
      rules[idx].contents[cIdx][key] = value;
    }

    this.setState({ group: { ...this.state.group, rules } });
  }

  onAddRuleContent = (idx, cIdx) => {
    const rules = _.cloneDeep(this.state.group.rules);
    const { item } = _.head(_.cloneDeep(DEFAULT_RULE.contents));

    switch (rules[idx].type) {
      case 'simple':
        rules[idx].contents.splice(cIdx + 1, 0, { item, msgtype: 'simple' });
        break;
      case 'wework_kf':
        rules[idx].contents.splice(cIdx + 1, 0, { item, msgtype: 'text' });
        break;
      case 'ai':
        break;
      default:
        break;
    }
    this.setState({ group: { ...this.state.group, rules } });
  }

  onDeleteRuleContent = (idx, cIdx) => {
    const rules = _.cloneDeep(this.state.group.rules);
    rules[idx].contents.splice(cIdx, 1);
    this.setState({ group: { ...this.state.group, rules } });
  }

  onChangeGroupValue = (e, key) => {
    const value = e?.target ? e?.target.value : e;
    this.setState({ group: { ...this.state.group, [key]: value } });
  }

  onAddKeyword = (idx) => {
    const rules = _.cloneDeep(this.state.group.rules);
    rules.splice(idx + 1, 0, _.cloneDeep(DEFAULT_RULE));
    this.setState({ group: { ...this.state.group, rules } });
  }

  onDeleteKeyword = (idx) => {
    const rules = _.cloneDeep(this.state.group.rules);
    if (rules.length === 1) {
      return;
    }
    rules.splice(idx, 1);
    this.setState({ group: { ...this.state.group, rules } });
  }

  onShowGroupDrawer = async (record) => {
    let rules = await this.props.fetchRules({ groupId: record.id });
    const ruleIds = rules.map((x) => { return x.id; });
    if (_.isEmpty(rules)) {
      rules = [_.cloneDeep(DEFAULT_RULE)];
    }

    this.setState({ openGroup: true, group: { ...record, rules, ruleIds } });
  }

  onSubmit = async () => {
    const { group } = this.state;
    if (_.isEmpty(group.name)) {
      Toast.show('请完善信息', Toast.Type.WARNING);
      return;
    }

    if (group.id) {
      await this.props.updateGroup(group);
    } else {
      await this.props.createGroup(group);
    }
    this.setState({ openGroup: false });
  }

  renderKFMenu = (data, idx, cIdx) => {
    return (
      <Form labelCol={{ span: 2 }} wrapperCol={{ span: 22 }} className="common-form">
        <Form.Item label="起始">
          <Input.TextArea
            value={data.headContent}
            onChange={(e) => { return this.onChangeKFMenu(e, 'headContent', idx, cIdx); }}
          />
        </Form.Item>
        {
          data.list.map((item, index) => {
            return (
              <Form.Item label={`菜单${index + 1}`} className="common-form">
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Select
                    value={item.type}
                    style={{ width: 220 }}
                    onChange={(e) => { return this.onChangeKFMenu(e, 'type', idx, cIdx, index); }}
                  >
                    <Select.Option value="text">文本</Select.Option>
                    <Select.Option value="click">回复菜单</Select.Option>
                    <Select.Option value="view">超链菜单</Select.Option>
                  </Select>
                  <span>
                    <PlusOutlined onClick={() => { return this.onAddKFMenus(idx, cIdx); }} />
                    <Divider type="vertical" />
                    <DeleteFilled onClick={() => { return this.onDeleteKFMenus(idx, cIdx, index); }} />
                  </span>
                </div>
                <Input
                  style={{ margin: '5px 0' }}
                  addonBefore={item.type !== 'text' ? '菜单' : '内容'}
                  value={item?.content}
                  onChange={(e) => { return this.onChangeKFMenu(e, 'content', idx, cIdx, index); }}
                />
                {
                  item.type !== 'text' &&
                  <Input
                    addonBefore="内容"
                    value={item?.id || item?.url}
                    onChange={(e) => {
                      return this.onChangeKFMenu(e, item.type === 'click' ? 'id' : 'url', idx, cIdx, index);
                    }}
                  />
                }
              </Form.Item>
            );
          })
        }
      </Form>
    );
  }

  renderKFContent = (data, idx, cIdx) => {
    let content = null;
    switch (data.msgtype) {
      case 'text':
        content = (
          <Input
            value={data.item.content}
            onChange={(e) => { return this.onChangeRuleContentValue(e, idx, cIdx, 'content'); }}
          />
        );
        break;
      case 'image':
      case 'voice':
      case 'video':
      case 'file':
        content = (
          <InputUpload
            accept="*"
            url={data.item.fileUrl}
            onChange={(e) => { return this.onChangeRuleContentValue(e, idx, cIdx, 'fileUrl'); }}
          />
        );
        break;
      case 'msgmenu':
        content = this.renderKFMenu(data.item, idx, cIdx);
        break;
      case 'link':
        content = (
          <Form labelCol={{ span: 2 }} wrapperCol={{ span: 22 }} className="common-form">
            <Form.Item label="标题">
              <Input
                value={data.item.title}
                onChange={(e) => { return this.onChangeRuleContentValue(e, idx, cIdx, 'title'); }}
              />
            </Form.Item>
            <Form.Item label="描述">
              <Input
                value={data.item.desc}
                onChange={(e) => { return this.onChangeRuleContentValue(e, idx, cIdx, 'desc'); }}
              />
            </Form.Item>
            <Form.Item label="URL">
              <Input
                value={data.item.url}
                onChange={(e) => { return this.onChangeRuleContentValue(e, idx, cIdx, 'url'); }}
              />
            </Form.Item>
            <Form.Item label="图片">
              <InputUpload
                accept="*"
                url={data.item.fileUrl}
                onChange={(e) => { return this.onChangeRuleContentValue(e, idx, cIdx, 'fileUrl'); }}
              />
            </Form.Item>
          </Form>
        );
        break;
      case 'business_card':
        content = (
          <Form.Item label="UserId">
            <Input
              value={data.item.userid}
              onChange={(e) => { return this.onChangeRuleContentValue(e, idx, cIdx, 'userid'); }}
            />
          </Form.Item>
        );
        break;
      case 'ca_link':
        content = (
          <Form.Item label="URL">
            <Input
              value={data.item.linkUrl}
              plaacholder="https://work.weixin.qq.com/ca/xxxxxx"
              onChange={(e) => { return this.onChangeRuleContentValue(e, idx, cIdx, 'linkUrl'); }}
            />
          </Form.Item>
        );
        break;
      default:
        break;
    }
    return content;
  }

  renderContents = (type, contents, idx) => {
    let content = null;
    switch (type) {
      case 'simple':
        content = (
          <>
            {
              (contents || []).map((x, cIdx) => {
                return (
                  <Input
                    value={x.item.simple}
                    style={{ marginBottom: 5 }}
                    addonBefore={`${cIdx + 1}.`}
                    addonAfter={
                      <span>
                        <PlusOutlined onClick={() => { return this.onAddRuleContent(idx, cIdx); }} />
                        <Divider type="vertical" />
                        <DeleteFilled onClick={() => { return this.onDeleteRuleContent(idx, cIdx); }} />
                      </span>
                    }
                    onChange={(e) => { return this.onChangeRuleContentValue(e, idx, cIdx, 'simple'); }}
                  />
                );
              })
            }
          </>
        );
        break;
      case 'wework_kf':
        content = (
          <>
            {
              (contents || []).map((x, cIdx) => {
                return (
                  <div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                      <Select
                        value={x.msgtype}
                        style={{ width: 200 }}
                        onChange={(e) => { return this.onChangeRuleContentValue(e, idx, cIdx, 'msgtype'); }}
                      >
                        <Select.Option value="text">文本</Select.Option>
                        <Select.Option value="image">图片</Select.Option>
                        <Select.Option value="voice">语音</Select.Option>
                        <Select.Option value="video">视频</Select.Option>
                        <Select.Option value="file">文件</Select.Option>
                        <Select.Option value="link">图文</Select.Option>
                        <Select.Option value="msgmenu">菜单</Select.Option>
                        <Select.Option value="business_card">名片</Select.Option>
                        <Select.Option value="ca_link">获客</Select.Option>
                      </Select>
                      <span>
                        <PlusOutlined onClick={() => { return this.onAddRuleContent(idx, cIdx); }} />
                        <Divider type="vertical" />
                        <DeleteFilled onClick={() => { return this.onDeleteRuleContent(idx, cIdx); }} />
                      </span>
                    </div>
                    {this.renderKFContent(x, idx, cIdx)}
                    <Divider style={{ margin: '10px 0' }} />
                  </div>
                );
              })

            }
          </>
        );
        break;
      case 'ai':
        break;
      default:
        break;
    }
    return content;
  }

  renderRuleItem = (item, idx) => {
    return (
      <>
        <Form.Item label="关键词">
          <Input
            value={item.keyword}
            onChange={(e) => { return this.onChangeRuleValue(e, idx, 'keyword'); }}
            addonAfter={
              <span>
                <PlusOutlined onClick={() => { return this.onAddKeyword(idx); }} />
                <Divider type="vertical" />
                <DeleteFilled onClick={() => { return this.onDeleteKeyword(idx); }} />
              </span>
            }
          />
        </Form.Item>
        <Form.Item label="类型">
          <Select value={item.type} onChange={(e) => { return this.onChangeRuleValue(e, idx, 'type'); }}>
            <Select.Option value="simple">普通</Select.Option>
            <Select.Option value="wework_kf">微信客服</Select.Option>
            <Select.Option value="ai">AI</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label="回复">
          {this.renderContents(item.type, item.contents, idx)}
        </Form.Item>
        <Divider style={{ margin: '10px 0' }} />
      </>
    );
  }

  renderGroupDrawer = () => {
    const { openGroup, group } = this.state;
    return (
      <Drawer
        width="50vw"
        title="规则组"
        open={openGroup}
        onClose={() => { return this.setState({ openGroup: false }); }}
        extra={<Button type="primary" onClick={() => { return this.onSubmit(); }}>保存</Button>}
      >
        <Form labelCol={{ span: 2 }} wrapperCol={{ span: 20 }} className="common-form">
          <Form.Item label="名称">
            <Input
              value={group?.name}
              onChange={(e) => { return this.onChangeGroupValue(e, 'name'); }}
            />
          </Form.Item>
          <Form.Item label="描述">
            <Input
              value={group?.description}
              onChange={(e) => { return this.onChangeGroupValue(e, 'description'); }}
            />
          </Form.Item>
          <Form.Item label="规则">
            <Form labelCol={{ span: 2 }} wrapperCol={{ span: 22 }} className="common-form">
              {group.rules?.map((item, idx) => { return this.renderRuleItem(item, idx); })}
            </Form>
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderGroupColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', width: 100, align: 'center' },
      { title: '名称', dataIndex: 'name', key: 'name', align: 'center' },
      { title: '描述', dataIndex: 'description', key: 'description' },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (text) => { return moment(text).format('YYYY-MM-DD HH:mm'); },
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        render: (text, record) => {
          return (
            <div>
              <a onClick={() => { return this.onShowGroupDrawer(record); }}>
                编辑
              </a>
              <Divider type="vertical" />
              <Popconfirm title="确定删除吗?" onConfirm={() => { return this.props.deleteGroup(record.id); }}>
                <a>删除</a>
              </Popconfirm>
            </div>
          );
        },
      },
    ];
  }

  render = () => {
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <Button
          type="primary"
          style={{ float: 'right', marginBottom: 15 }}
          onClick={() => {
            return this.setState({ openGroup: true, group: { rules: [_.cloneDeep(DEFAULT_RULE)] } });
          }}
        >
          新增
        </Button>
        <PaginationTable
          dataSource={this.props.list}
          totalDataCount={this.props.total}
          pagination={this.props.pagination}
          columns={this.renderGroupColumns()}
        />

        {this.state.openGroup && this.renderGroupDrawer()}
      </div>
    );
  }
}

export {
  reducer,
};
