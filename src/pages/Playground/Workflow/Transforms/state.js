import Configs from '~/consts';
import { Transforms } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'TRANSFORMS/SET_STATE';
const CLEAR_STATE = 'TRANSFORMS/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchGroups = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().transforms;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
    };
    const { items, total } = await Transforms.fetchGroups(searchParams);
    dispatch(setState({
      list: items,
      total,
      pagination: {
        pageIndex: searchParams['pagination.pageIndex'],
        pageSize: searchParams['pagination.pageSize'],
      },
    }));
  };
};

export const createGroup = (params) => {
  return async (dispatch) => {
    const result = await Transforms.createGroup(params);
    const promises = [];
    if (!_.isEmpty(params.rules)) {
      params.rules.forEach((rule) => {
        promises.push(Transforms.createRule({ ...rule, groupId: result.id }));
      });
    }
    await Promise.all(promises);
    dispatch(fetchGroups());
  };
};

export const updateGroup = (params) => {
  return async (dispatch) => {
    await Transforms.updateGroup(params);
    const promises = [];
    const updateRules = (params?.rules || []).filter((rule) => { return rule.id; });
    const createRules = (params?.rules || []).filter((rule) => { return !rule.id; });
    updateRules.forEach((rule) => { promises.push(Transforms.updateRule(rule)); });
    createRules.forEach((rule) => { promises.push(Transforms.createRule({ ...rule, groupId: params.id })); });
    (params?.ruleIds || []).forEach((id) => {
      if (!updateRules.find((rule) => { return rule.id === id; })) {
        promises.push(Transforms.deleteRule(id));
      }
    });
    await Promise.all(promises);
    dispatch(fetchGroups());
  };
};

export const deleteGroup = (id) => {
  return async (dispatch) => {
    await Transforms.deleteGroup(id);
    dispatch(fetchGroups());
  };
};

export const fetchRules = (params = {}) => {
  return async () => {
    const { items } = await Transforms.fetchRules({ ...Configs.ALL_PAGE_PARAMS, ...params });
    return items;
  };
};

export const createRule = (params) => {
  return async (dispatch) => {
    await Transforms.createRule(params);
    dispatch(fetchRules());
  };
};

const _getInitState = () => {
  return {
    list: [],
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 10,
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
