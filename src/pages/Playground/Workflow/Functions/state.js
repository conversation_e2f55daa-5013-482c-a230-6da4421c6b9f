import { Accounts, ChatBot, WeworkKF, Workflow } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'WORKFLOW_FUNCTIONS/SET_STATE';
const CLEAR_STATE = 'WORKFLOW_FUNCTIONS/CLEAR_STATE';
export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchFuncTools = () => {
  return async (dispatch) => {
    const items = await Accounts.fetchFuncTools({});
    const list = [];
    _.map(items, (v, k) => { list.push({ id: +k + 1, ...v }); });
    dispatch(setState({ sysFuncs: list }));
  };
};

export const fetchPluginTools = () => {
  return async (dispatch) => {
    const items = await Workflow.fetchPluginTools();
    const list = [];
    _.map(items, (v, k) => {
      const params = [];
      _.map(v.inputSchema, (s, a) => {
        params.push({ name: _.snakeCase(a), description: s.description });
      });
      list.push({ id: +k + 1, ...v, params });
    });
    dispatch(setState({ plugins: list }));
  };
};
export const fetchWorkflowFuncs = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().workflowFunctions;
    const searchParams = {
      name: params?.name,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };

    const { items, total } = await ChatBot.fetchWorkflowFuncs(searchParams);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const delWorkflowFunc = (id) => {
  return async (dispatch) => {
    await ChatBot.delWorkflowFunc(id);
    dispatch(fetchWorkflowFuncs());
  };
};

export const updateWorkflowFunc = (params) => {
  return async (dispatch) => {
    await ChatBot.updateWorkflowFunc(params);
    dispatch(fetchWorkflowFuncs());
  };
};

export const fetchOpenApiFuncs = (params = {}) => {
  return async (dispatch, getState) => {
    const { apiPagination } = getState().workflowFunctions;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || apiPagination.pageIndex,
      'pagination.pageSize': params.pageSize || apiPagination.pageSize,
      'pagination.orderBy': params.orderBy || apiPagination.orderBy,
    };
    const { items, total } = await ChatBot.fetchOpenApiFuncs(searchParams);
    dispatch(setState({
      openApiList: items,
      openApiTotal: total,
      apiPagination: {
        pageIndex: searchParams['pagination.pageIndex'],
        pageSize: searchParams['pagination.pageSize'],
        orderBy: searchParams['pagination.orderBy'],
      },
    }));
  };
};

export const addOpenApiFunc = (params) => {
  return async (dispatch) => {
    await ChatBot.importOpenApiFunc(params);
    dispatch(fetchOpenApiFuncs());
  };
};

export const updateOpenApiFunc = (params) => {
  return async (dispatch) => {
    await ChatBot.updateOpenApiFunc(params);
    dispatch(fetchOpenApiFuncs());
  };
};

export const delOpenApiFunc = (id) => {
  return async (dispatch) => {
    await ChatBot.delOpenApiFunc(id);
    dispatch(fetchOpenApiFuncs());
  };
};

export const fetchKnowledgeLibraryFuncs = (params = {}) => {
  return async (dispatch, getState) => {
    const { klPagination } = getState().workflowFunctions;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || klPagination.pageIndex,
      'pagination.pageSize': params.pageSize || klPagination.pageSize,
      'pagination.orderBy': params.orderBy || klPagination.orderBy,
    };
    const { items, total } = await ChatBot.fetchKnowledgeLibraryFuncs(searchParams);
    dispatch(setState({
      klFuncTotal: total,
      klFuncs: items,
      klPagination: {
        pageIndex: searchParams['pagination.pageIndex'],
        pageSize: searchParams['pagination.pageSize'],
        orderBy: searchParams['pagination.orderBy'],
      },
    }));
  };
};

export const delKnowledgeFunc = (id) => {
  return async (dispatch) => {
    await ChatBot.delKnowledgeLibraryFunc(id);
    dispatch(fetchKnowledgeLibraryFuncs());
  };
};

export const updateKnowledgeFunc = (params) => {
  return async (dispatch) => {
    await ChatBot.updateKnowledgeLibraryFunc(params);
    dispatch(fetchKnowledgeLibraryFuncs());
  };
};

export const fetchActivityFuncs = (params = {}) => {
  return async (dispatch, getState) => {
    const { wiPagination } = getState().workflowFunctions;
    const searchParams = {
      'pagination.pageIndex': params.pageIndex || wiPagination.pageIndex,
      'pagination.pageSize': params.pageSize || wiPagination.pageSize,
      'pagination.orderBy': params.orderBy || wiPagination.orderBy,
    };
    const { items, total } = await WeworkKF.fetchActivityFuncs(searchParams);
    dispatch(setState({
      wiFuncTotal: total,
      wiFuncs: items,
      wiPagination: {
        pageIndex: searchParams['pagination.pageIndex'],
        pageSize: searchParams['pagination.pageSize'],
        orderBy: searchParams['pagination.orderBy'],
      },
    }));
  };
};

export const fetchActivityFunc = (id) => {
  return async () => {
    const item = await WeworkKF.fetchActivityFunc(id);
    const result = await WeworkKF.fetchInvitationConfig(item.invitationConfigId);
    return { ...item, activity: result };
  };
};

export const delActivityFunc = (id) => {
  return async (dispatch) => {
    await WeworkKF.delActivityFunc(id);
    dispatch(fetchActivityFuncs());
  };
};

export const updateActivityFunc = (params) => {
  return async (dispatch) => {
    await WeworkKF.updateActivityFunc(params);
    dispatch(fetchActivityFuncs());
  };
};

export const fetchCozeFuncs = (params = {}) => {
  return async (dispatch, getState) => {
    const { czPagination } = getState().workflowFunctions;
    const searchParams = {
      name: params?.name,
      'pagination.pageIndex': params.pageIndex || czPagination.pageIndex,
      'pagination.pageSize': params.pageSize || czPagination.pageSize,
      'pagination.orderBy': params.orderBy || czPagination.orderBy,
    };

    const result = await ChatBot.fetchCozeFuncs(searchParams);
    const items = _.values(result);
    dispatch(
      setState({
        czFuncTotal: items.length,
        czFuncs: items,
        czPagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const delCozeFunc = (id) => {
  return async (dispatch) => {
    await ChatBot.delCozeFunc(id);
    dispatch(fetchCozeFuncs());
  };
};

export const updateCozeFunc = (params) => {
  return async (dispatch) => {
    await ChatBot.updateCozeFunc(params);
    dispatch(fetchCozeFuncs());
  };
};

export const addCozeFunc = (params) => {
  return async (dispatch) => {
    await ChatBot.addCozeFunc(params);
    dispatch(fetchCozeFuncs());
  };
};

export const fetchCourseFuncs = (params = {}) => {
  return async (dispatch, getState) => {
    const { coursePagination } = getState().workflowFunctions;
    const searchParams = {
      name: params?.name,
      'pagination.pageIndex': params.pageIndex || coursePagination.pageIndex,
      'pagination.pageSize': params.pageSize || coursePagination.pageSize,
      'pagination.orderBy': params.orderBy || coursePagination.orderBy,
    };

    const { items, total } = await ChatBot.fetchCourseFuncs(searchParams);
    dispatch(
      setState({
        courseFuncs: items,
        courseFuncTotal: total,
        coursePagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const updateCourseFunc = (params) => {
  return async (dispatch) => {
    await ChatBot.updateCourseFunc(params);
    dispatch(fetchCourseFuncs());
  };
};

export const fetchFunctionToolsByGroup = () => {
  return async (dispatch) => {
    const datas = await ChatBot.fetchFunctionToolsByGroup();
    const obj = {};
    _.values(datas).forEach((x) => { obj[x.key] = x; });
    obj.system = {
      ...obj.system,
      tools: obj.system.tools.map((x, i) => { return { ...x, id: i + 1 }; }),
    };
    obj.plugin = {
      ...obj.plugin,
      tools: obj.plugin.tools.map((x, i) => {
        const params = [];
        _.map(x.inputSchema, (s, a) => {
          params.push({ name: _.snakeCase(a), description: s.description });
        });
        return { id: i + 1, ...x, params };
      }),
    };
    dispatch(setState({ toolDatas: obj }));
  };
};

export const delCourseFunc = (id) => {
  return async (dispatch) => {
    await ChatBot.delCourseFunc(id);
    dispatch(fetchCourseFuncs());
  };
};

const _getInitState = () => {
  return {
    toolDatas: {},
    plugins: [],
    sysFuncs: [],
    openApiList: [],
    openApiTotal: 0,
    apiPagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    total: 0,
    list: [],
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    klFuncTotal: 0,
    klFuncs: [],
    klPagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    wiFuncs: [],
    wiFuncTotal: 0,
    wiPagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    czFuncs: [],
    czFuncTotal: 0,
    czPagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    courseFuncs: [],
    courseFuncTotal: 0,
    coursePagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
