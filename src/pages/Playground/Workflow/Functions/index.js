import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { json } from '@codemirror/lang-json';
import CodeMirror from '@uiw/react-codemirror';
import { Toast } from '~/components';
import { ChatBot } from '~/engine';
import { Button, Divider, Drawer, Form, Input, Popconfirm, Radio, Table, Tabs } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import CozeDrawer from './CozeDrawer';
import reducer, * as actions from './state';

const TOOL_MAP = {
  system: 'sys',
  workflow: 'wf',
  knwoledge_library: 'kl',
  // wework_invitation: 'wi',
  api_actions: 'apiAction',
  coze: 'cz',
  // course: 'course',
  plugin: 'gpt',
};
@connect(
  (state) => {
    return state.workflowFunctions;
  },
  actions,
)
export default class WorkflowFunctions extends Component {
  static propTypes = {
    toolDatas: PropTypes.array,
    delWorkflowFunc: PropTypes.func.isRequired,
    updateWorkflowFunc: PropTypes.func.isRequired,
    updateOpenApiFunc: PropTypes.func.isRequired,
    addOpenApiFunc: PropTypes.func.isRequired,
    delOpenApiFunc: PropTypes.func.isRequired,
    updateKnowledgeFunc: PropTypes.func.isRequired,
    delKnowledgeFunc: PropTypes.func.isRequired,
    fetchActivityFunc: PropTypes.func.isRequired,
    updateActivityFunc: PropTypes.func.isRequired,
    delActivityFunc: PropTypes.func.isRequired,
    delCozeFunc: PropTypes.func.isRequired,
    updateCozeFunc: PropTypes.func.isRequired,
    addCozeFunc: PropTypes.func.isRequired,
    updateCourseFunc: PropTypes.func.isRequired,
    delCourseFunc: PropTypes.func.isRequired,
    fetchFunctionToolsByGroup: PropTypes.func.isRequired,
    location: PropTypes.object.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    openApi: {},
    activity: {},
    activeKey: 'sys',
  }

  componentDidMount = () => {
    this.props.fetchFunctionToolsByGroup();
    const { activeKey } = this.props.location.query || {};
    if (activeKey) {
      this.setState({ activeKey });
    }
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onSearch = () => {
  }

  onChangeOpenApiValue = (e, key) => {
    const { openApi } = this.state;
    const value = e?.target ? e?.target.value : e;
    this.setState({ openApi: { ...openApi, [key]: value } });
  }

  onChangeValue = (e, idx, key) => {
    const { funcData } = this.state;
    const value = e?.target ? e.target.value : e;
    funcData.params[idx][key] = value;
    this.setState({ funcData });
  }

  onImportOpenApi = async (e) => {
    const { title, summary, server } = this.state.openApi;
    const resp = await fetch(e);
    const data = await resp.json();
    const { info, servers } = data;
    this.setState({
      openApi: {
        ...this.state.openApi,
        title: title || info?.title,
        summary: summary || info?.description,
        server: server || servers?.[0]?.url,
        spec: JSON.stringify(data, null, 2),
      },
    });
  }

  onSaveOpenApi = async () => {
    const { openApi } = this.state;
    const { id, title, summary, server, spec, authData, authType } = openApi;
    const params = { title, summary, server, spec: JSON.parse(spec), authData, authType: authType || 'oauth2' };
    if (id) {
      await this.props.updateOpenApiFunc({ id, ...params });
    } else {
      await this.props.addOpenApiFunc(params);
    }

    Toast.show('操作成功!', Toast.Type.SUCCESS);
    this.setState({ open: false, openApi: {} });
  }

  onSaveFunc = async () => {
    const { funcData, type } = this.state;
    const { id, displayName, description, name, workflowUuid, params } = funcData;

    const data = { displayName, description, name, workflowUuid, params };
    if (id && type === 'kl') {
      await this.props.updateKnowledgeFunc({ id, ...data });
    }

    if (id && type === 'wi') {
      await this.props.updateActivityFunc({ id, ...data });
    }

    if (id && type === 'course') {
      await this.props.updateCourseFunc({ id, ...data });
    }

    if (id && type === 'wf') {
      await this.props.updateWorkflowFunc({ id, ...data });
    }

    Toast.show('操作成功!', Toast.Type.SUCCESS);
    this.setState({ openFunc: false, funcData: {}, activity: {}, type: '' });
  }

  onAddMetaData = (idx) => {
    const metadata_ = _.cloneDeep(this.state.funcData.metadata_) || [];
    metadata_.splice(idx + 1, 0, {});
    this.setState({ funcData: { ...this.state.funcData, metadata_ } });
  }

  onDelMetaData = (idx) => {
    const metadata_ = _.cloneDeep(this.state.funcData.metadata_) || [];
    if (metadata_?.length === 1) return;
    metadata_.splice(idx, 1);
    this.setState({ funcData: { ...this.state.funcData, metadata_ } });
  }

  onChangeMetaData = (e, key, idx) => {
    const value = e?.target ? e.target.value : e;
    const metadata_ = _.cloneDeep(this.state.funcData.metadata_) || [{}];
    metadata_[idx][key] = value;
    this.setState({ funcData: { ...this.state.funcData, metadata_ } });
  }

  onShowFuncDrawer = async (row, isSys = true, type = '') => {
    if (type === 'cz') {
      return this.setState({ openCoze: true, funcData: row, isSys });
    }

    if (type === 'kl') {
      const metadata_ = [];
      _.map(row.metadata_, (v, k) => {
        metadata_.push({ key: k, value: v });
      });
      return this.setState({ openKLFunc: true, type, funcData: { ...row, metadata_ } });
    }

    let constants = [];
    if (row?.workflowUuid) {
      const { betaContent, content } = await ChatBot.getChatbotWorkflow(row?.workflowUuid);
      const { params } = _.isEmpty(JSON.parse(content)) ? JSON.parse(betaContent) : JSON.parse(betaContent);
      constants = _.keys(params);
      constants.push('user_message');
    }

    if (type === 'wi') {
      const { activity } = await this.props.fetchActivityFunc(row.id);
      return this.setState({ openFunc: true, funcData: row, isSys, activity, type });
    }

    return this.setState({ openFunc: true, type, funcData: row, isSys, constants });
  }

  onSaveKLFunc = async () => {
    const { funcData } = this.state;
    const { id, libraryName, displayName, description, queryDescription, metadata_ } = funcData;
    const metadata = {};
    _.map(metadata_, (item) => {
      metadata[item.key] = item.value;
    });

    const data = { libraryName, displayName, description, queryDescription, metadata_: metadata };
    if (id) {
      await this.props.updateKnowledgeFunc({ id, ...data });
    }

    Toast.show('操作成功!', Toast.Type.SUCCESS);
    this.setState({ openKLFunc: false, funcData: {} });
  }

  renderFuncDrawer = () => {
    const { funcData, constants, isSys, activity, type } = this.state;
    return (
      <Drawer
        title="函数配置"
        placement="right"
        onClose={() => { return this.setState({ openFunc: false, activity: {} }); }}
        visible={this.state.openFunc}
        width="66vw"
        extra={
          isSys ? null : <Button type="primary" onClick={() => { return this.onSaveFunc(); }}>保存</Button>
        }
      >
        <Form className="common-form" labelCol={{ span: 3 }}>
          <Form.Item label="函数名">{funcData.displayName}</Form.Item>
          <Form.Item label="描述">
            <Input
              value={funcData.description}
              onChange={(e) => {
                return this.setState({ funcData: { ...funcData, description: e.target.value } });
              }}
            />
          </Form.Item>
          {
            (type === 'course') &&
            <>
              <Form.Item label="查询描述">
                <Input
                  value={funcData.queryDescription}
                  onChange={(e) => {
                    return this.setState({ funcData: { ...funcData, queryDescription: e.target.value } });
                  }}
                />
              </Form.Item>
              <Form.Item label="统计描述">
                <Input
                  value={funcData.countDescription}
                  onChange={(e) => {
                    return this.setState({ funcData: { ...funcData, countDescription: e.target.value } });
                  }}
                />
              </Form.Item>
            </>
          }
          <Form.Item label={(type === 'wi') ? '活动' : '参数'}>
            {
              !isSys && _.map(funcData.params, (item, idx) => {
                return (
                  <Input.Group>
                    <Input
                      addonBefore={`名称${idx + 1}`}
                      value={item.name}
                      disabled={constants.includes(item.name)}
                      onChange={(e) => { return this.onChangeValue(e, idx, 'name'); }}
                    />
                    <Input
                      addonBefore="类型"
                      value={item.type}
                      style={{ margin: '5px 0' }}
                      onChange={(e) => { return this.onChangeValue(e, idx, 'type'); }}
                    />
                    <Input
                      addonBefore="描述"
                      value={item.description}
                      onChange={(e) => { return this.onChangeValue(e, idx, 'description'); }}
                    />
                    <Input
                      addonBefore="默认值"
                      value={item.defaultValue}
                      style={{ margin: '5px 0 15px 0' }}
                      onChange={(e) => { return this.onChangeValue(e, idx, 'defaultValue'); }}
                    />
                  </Input.Group>
                );
              })
            }
            {
              isSys && _.map(funcData.params, (item) => {
                return (
                  <Input
                    readreadOnly
                    addonBefore={item.name}
                    value={item.description || ''}
                    style={{ margin: '10px 0' }}
                  />
                );
              })
            }
            {(type === 'wi') && activity.name}
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderKLFuncDrawer = () => {
    const { funcData } = this.state;
    return (
      <Drawer
        title="知识库插件"
        placement="right"
        onClose={() => { return this.setState({ openKLFunc: false }); }}
        visible={this.state.openKLFunc}
        width="66vw"
        extra={<Button type="primary" onClick={() => { return this.onSaveKLFunc(); }}>保存</Button>}
      >
        <Form className="common-form" labelCol={{ span: 3 }}>
          <Form.Item label="知识库">{funcData.libraryName}</Form.Item>
          <Form.Item label="函数名">{funcData.displayName}</Form.Item>
          <Form.Item label="描述">
            <Input
              value={funcData.description}
              onChange={(e) => { return this.setState({ funcData: { ...funcData, description: e.target.value } }); }}
            />
          </Form.Item>
          <Form.Item label="查询词描述">
            <Input
              value={funcData.queryDescription}
              onChange={(e) => {
                return this.setState({ funcData: { ...funcData, queryDescription: e.target.value } });
              }}
            />
          </Form.Item>
          <Form.Item label="元数据">
            {
              (funcData?.metadata_).map((item, idx) => {
                return (
                  <Input.Group compact style={{ display: 'flex', marginBottom: 5 }}>
                    <Input
                      addonBefore="key"
                      value={item?.key}
                      onChange={(e) => { return this.onChangeMetaData(e, 'key', idx); }}
                    />
                    <Input
                      addonBefore="value"
                      value={item?.value}
                      onChange={(e) => { return this.onChangeMetaData(e, 'value', idx); }}
                    />
                    <span style={{ width: 180, display: 'flex', justifyContent: 'space-around' }}>
                      <PlusCircleOutlined
                        style={{ fontSize: 20 }}
                        onClick={() => { return this.onAddMetaData(idx); }}
                      />
                      <DeleteOutlined
                        style={{ fontSize: 20 }}
                        onClick={() => { return this.onDelMetaData(idx); }}
                      />
                    </span>
                  </Input.Group>
                );
              })
            }
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderColumns = (isSys = true, type = '') => {
    const columns = [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center', width: 80 },
      {
        title: '函数名',
        dataIndex: 'displayName',
        key: 'displayName',
        align: 'center',
        width: '15%',
        render: (txt, row) => {
          if (isSys || type === 'wi') return txt;
          if (type === 'kl') {
            return <a onClick={() => { return this.$push(`/workflow/${row.workflowUuid}`); }}>{txt}</a>;
          }
          return txt;
        },
      },
      { title: '描述', dataIndex: 'description', key: 'description' },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        width: '10%',
        render: (txt, row) => {
          if (!row.editable) return null;

          return (
            <>
              <a onClick={() => { return this.onShowFuncDrawer(row, isSys, type); }}>
                {isSys ? '查看' : '编辑'}
              </a>
              {
                (!isSys && row.editable) &&
                <>
                  <Divider type="vertical" />
                  <Popconfirm
                    disabled={!row.editable}
                    title="是否删除?!"
                    onConfirm={() => {
                      if (type === 'kl') {
                        return this.props.delKnowledgeFunc(row.id);
                      }
                      if (type === 'wi') {
                        return this.props.delActivityFunc(row.id);
                      }
                      if (type === 'cz') {
                        return this.props.delCozeFunc(row.id);
                      }
                      if (type === 'course') {
                        return this.props.delCourseFunc(row.id);
                      }
                      return this.props.delWorkflowFunc(row.id);
                    }}
                  >
                    <a>删除</a>
                  </Popconfirm>
                </>
              }
            </>
          );
        },
      },
    ];

    return columns;
  }

  renderOpenApiColumns = () => {
    const columns = [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center', width: 80 },
      { title: '标题', dataIndex: 'title', key: 'title', align: 'center' },
      { title: '描述', dataIndex: 'summary', key: 'summary' },
      { title: '服务', dataIndex: 'server', key: 'server' },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        render: (txt) => { return moment(txt).format('YYYY-MM-DD HH:mm'); },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          if (!row.editable) return null;

          return (
            <>
              <a
                onClick={() => {
                  return this.setState({
                    open: true, openApi: { ...row, spec: JSON.stringify(row.spec, null, 2) },
                  });
                }}
              >
                编辑
              </a>
              <Divider type="vertical" />
              <Popconfirm
                disabled={!row.editable}
                title="是否删除?!"
                onConfirm={() => { return this.props.delOpenApiFunc(row.id); }}
              >
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];

    return columns;
  }

  renderBtns = (type) => {
    let content = null;

    if (['cz', 'apiAction'].includes(type)) {
      const key = type === 'cz' ? 'openCoze' : 'open';
      content = (
        <Button type="primary" style={{ float: 'right' }} onClick={() => { this.setState({ [key]: true }); }}>
          新增
        </Button>
      );
    }

    return content;
  }

  renderOpenApiDrawer = () => {
    const { open, openApi } = this.state;
    const { paths } = JSON.parse(openApi?.spec || '{}');
    const datas = _.keys(paths);
    const tableDatas = _.map(datas, (item) => {
      return {
        path: item,
        method: _.keys(paths[item])[0],
        operationId: paths[item][_.keys(paths[item])[0]]?.operationId,
      };
    });

    return (
      <Drawer
        title="API Action"
        placement="right"
        closable={false}
        onClose={() => { return this.setState({ open: false, openApi: {} }); }}
        visible={open}
        width="66vw"
        extra={<Button type="primary" onClick={this.onSaveOpenApi}>保存</Button>}
      >
        <Form labelCol={{ span: 3 }}>
          <Form.Item label="标题">
            <Input
              value={openApi.title}
              onChange={(e) => { return this.onChangeOpenApiValue(e, 'title'); }}
            />
          </Form.Item>
          <Form.Item label="描述">
            <Input
              value={openApi.summary}
              onChange={(e) => { return this.onChangeOpenApiValue(e, 'summary'); }}
            />
          </Form.Item>
          <Form.Item label="服务地址">
            <Input
              value={openApi.server}
              onChange={(e) => { return this.onChangeOpenApiValue(e, 'server'); }}
            />
          </Form.Item>
          <Form.Item label="Spec">
            <div style={{ textAlign: 'end' }}>
              <Input.Search
                style={{ width: 300 }}
                value={openApi.sourceUrl}
                onChange={(e) => { return this.onChangeOpenApiValue(e, 'sourceUrl'); }}
                onSearch={this.onImportOpenApi}
                enterButton="导入"
              />
            </div>
            <CodeMirror
              id="openApi"
              style={{ marginTop: 10 }}
              value={openApi?.spec}
              height="50vh"
              extensions={[json()]}
              onChange={(e) => { return this.onChangeOpenApiValue(e, 'spec'); }}
            />
          </Form.Item>
          <Form.Item label="鉴权">
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Radio checked>OAuth2</Radio>
              <Input
                value={openApi?.authData?.accessToken}
                onChange={(e) => { return this.onChangeOpenApiValue({ accessToken: e.target.value }, 'authData'); }}
              />
            </div>
          </Form.Item>
          <Form.Item label="Actions">
            <Table
              dataSource={tableDatas || []}
              pagination={false}
              columns={[
                { title: 'Name', dataIndex: 'operationId', key: 'operationId' },
                { title: 'Method', dataIndex: 'method', key: 'method' },
                { title: 'Path', dataIndex: 'path', key: 'path' },
              ]}
            />
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderCommonColumns = (type) => {
    if (['wf', 'kl', 'wi', 'cz', 'course'].includes(type)) {
      return this.renderColumns(false, type);
    }

    if (type === 'apiAction') {
      return this.renderOpenApiColumns();
    }

    return this.renderColumns();
  }

  render = () => {
    const { toolDatas } = this.props;

    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <Tabs
          activeKey={this.state.activeKey}
          onChange={(activeKey) => {
            this.$replace('/workflow/functions', { activeKey });
            return this.setState({ activeKey });
          }}
        >
          {
            _.map(TOOL_MAP, (v, k) => {
              return (
                <Tabs.TabPane key={v} tab={toolDatas[k]?.name}>
                  {this.renderBtns(v)}
                  <Table
                    dataSource={toolDatas[k]?.tools || []}
                    columns={this.renderCommonColumns(v)}
                    pagination={false}
                  />
                </Tabs.TabPane>
              );
            })
          }
        </Tabs>

        {this.state.open && this.renderOpenApiDrawer()}
        {this.state.openFunc && this.renderFuncDrawer()}
        {this.state.openKLFunc && this.renderKLFuncDrawer()}
        {
          this.state.openCoze &&
          <CozeDrawer
            open={this.state.openCoze}
            data={this.state.funcData}
            addFunc={this.props.addCozeFunc}
            updateFunc={this.props.updateCozeFunc}
            onClose={() => { return this.setState({ openCoze: false, funcData: {} }); }}
            title="扣子插件"
          />
        }
      </div>
    );
  }
}

export {
  reducer,
};
