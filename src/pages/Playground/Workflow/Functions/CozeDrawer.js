import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { Toast } from '~/components';
import { Platform } from '~/plugins';
import { <PERSON><PERSON>, <PERSON>vider, Drawer, Form, Input, Radio } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class CozeDrawer extends PureComponent {
  static propTypes = {
    data: PropTypes.object,
    open: PropTypes.bool,
    onClose: PropTypes.func,
    addFunc: PropTypes.func,
    updateFunc: PropTypes.func,
    title: PropTypes.node,
  };

  state = {
    data: { params: [{ name: 'query', type: 'str' }] },
  }

  componentDidMount = () => {
    this.setState({ data: this.props.data || { params: [{ name: 'query', type: 'str' }] } });
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onChangeParams = (e, key, idx) => {
    const value = e?.target ? e.target.value : e;
    const params = _.cloneDeep(this.state.data.params || {});
    params[idx][key] = value;
    this.setState({ data: { ...this.state.data, params } });
  }

  onAdd = (index = 0) => {
    const params = _.cloneDeep(this.state.data?.params) || [];
    params.splice(index + 1, 0, {});
    this.setState({ data: { ...this.state.data, params } });
  }

  onDel = (index = 0) => {
    const params = _.cloneDeep(this.state.data?.params) || [];
    if (params.length === 1) {
      return;
    }
    params.splice(index, 1);
    this.setState({ data: { ...this.state.data, params } });
  }

  onSubmit = async () => {
    const { data } = this.state;
    if (!data.name || !data.displayName || !data.description || !data.workflowId || !data.scope) {
      Toast.show('请填写完整信息', Toast.Type.WARNING);
      return;
    }
    data.region = 'inside';
    if (data.id) {
      await this.props.updateFunc(data);
    } else {
      await this.props.addFunc(data);
      Platform.emit(Platform.Event.RELOAD_FUNC_TOOLS);
    }
    Toast.show('保存成功', Toast.Type.SUCCESS);
    this.props.onClose();
  }

  render = () => {
    const { data } = this.state;

    return (
      <Drawer
        maskClosable
        closable={false}
        onClose={this.props.onClose}
        placement="right"
        title={this.props.title}
        visible={this.props.open}
        width="50vw"
        extra={<Button type="button" onClick={this.onSubmit}>保存</Button>}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} className="common-form">
          <Form.Item label="函数名" help="仅支持字母、下划线, eg.: A_b_c" required>
            <Input value={data?.name} onChange={(e) => { return this.onChangeValue(e, 'name'); }} />
          </Form.Item>
          <Form.Item label="别名" help="支持中文" required>
            <Input value={data?.displayName} onChange={(e) => { return this.onChangeValue(e, 'displayName'); }} />
          </Form.Item>
          <Form.Item label="描述" required>
            <Input.TextArea
              autoSize
              value={data?.description}
              onChange={(e) => { return this.onChangeValue(e, 'description'); }}
            />
          </Form.Item>
          <Form.Item label="参数" required>
            {
              data?.params?.map((item, index) => {
                return (
                  <>
                    <Input
                      addonBefore="名称"
                      value={item.name}
                      onChange={(e) => { return index ? this.onChangeParams(e, 'name', index) : null; }}
                      addonAfter={
                        <>
                          <PlusOutlined onClick={() => { return this.onAdd(index); }} />
                          {
                            !!index &&
                            <>
                              <Divider type="vertical" />
                              <MinusOutlined onClick={() => { return this.onDel(index); }} />
                            </>
                          }
                        </>
                      }
                    />
                    <Input
                      addonBefore="类型"
                      value={item.type}
                      style={{ margin: '5px 0' }}
                      onChange={(e) => { return index ? this.onChangeParams(e, 'type', index) : null; }
                      }
                    />
                    <Input
                      addonBefore="描述"
                      value={item?.description}
                      style={{ marginBottom: 15 }}
                      onChange={(e) => { return this.onChangeParams(e, 'description', index); }}
                    />
                  </>
                );
              })
            }

          </Form.Item>
          <Form.Item label="BotId">
            <Input value={data?.botId} onChange={(e) => { return this.onChangeValue(e, 'botId'); }} />
          </Form.Item>
          <Form.Item label="WorkflowId" required>
            <Input value={data?.workflowId} onChange={(e) => { return this.onChangeValue(e, 'workflowId'); }} />
          </Form.Item>
          <Form.Item label="范围" required>
            <Radio.Group value={data?.scope} onChange={(e) => { return this.onChangeValue(e, 'scope'); }}>
              <Radio value="public">公开</Radio>
              <Radio value="private">私有</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Drawer>
    );
  }
}
