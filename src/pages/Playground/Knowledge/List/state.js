import { ChatBot } from '~/engine';

const SET_STATE = 'CHAT_KNOWLEDGE_LIST/SET_STATE';
const CLEAR_STATE = 'CHAT_KNOWLEDGE_LIST/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchDetail = (id) => {
  return async (dispatch) => {
    const detail = await ChatBot.getKnowledgeLibrary(id);
    dispatch(setState({ detail }));
  };
};

export const fetchKnowledges = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination, libraryId, fileId, list } = getState().chatKnowledgeList;
    const searchParams = {
      libraryId,
      fileId: params?.fileId || fileId || undefined,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };

    const { items, total } = await ChatBot.searchKnowledges(searchParams);
    dispatch(setState({
      total,
      list: [...list, ...items],
      pagination: {
        pageIndex: searchParams['pagination.pageIndex'],
        pageSize: searchParams['pagination.pageSize'],
        orderBy: searchParams['pagination.orderBy'],
      },
    }));
  };
};

export const updateKnowledge = (params) => {
  return async (dispatch, getState) => {
    const { list } = getState().chatKnowledgeList;
    const result = await ChatBot.updateKnowledge(params);
    const items = list.map((x) => { return x.id === result.id ? result : x; });
    dispatch(setState({ list: items }));
  };
};

export const addKnowledge = (params, ignoreFetch = false) => {
  return async (dispatch) => {
    await ChatBot.addKnowledge(params);
    if (!ignoreFetch) {
      dispatch(fetchKnowledges());
    }
  };
};

export const importKnowledge = (params, ignoreFetch = false) => {
  return async (dispatch) => {
    await ChatBot.importKnowledge(params);
    if (!ignoreFetch) {
      dispatch(fetchKnowledges());
    }
  };
};

export const deleKnowledge = (id) => {
  return async (dispatch) => {
    await ChatBot.deleteKnowledge(id);
    dispatch(fetchKnowledges());
  };
};

export const addFiles = (params) => {
  return async (dispatch) => {
    await ChatBot.addKnowledgeFiles(params);
    dispatch(fetchKnowledges());
  };
};

export const searchEmbedding = (params = {}) => {
  return async (dispatch, getState) => {
    const { libraryId } = getState().chatKnowledgeList;
    const result = await ChatBot.searchKnowledgeEmbedding({ ...params, libraryId });
    return result;
  };
};

const _getInitState = () => {
  return {
    total: 0,
    detail: {},
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    list: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
