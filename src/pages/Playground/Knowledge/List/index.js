import './index.less';

import { DeleteOutlined } from '@ant-design/icons';
import { FilterBar } from '~/components';
import { <PERSON><PERSON>, Card, Col, Popconfirm, Row, Typography } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import EmbeddingDrawer from '../components/EmbeddingDrawer';
import KnowledgeDrawer from '../components/KnowledgeDrawer';
import UploadDrawer from '../components/UploadDrawer';
import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.chatKnowledgeList;
  },
  actions,
)
export default class ChatKnowledges extends Component {
  static propTypes = {
    list: PropTypes.array,
    total: PropTypes.number,
    detail: PropTypes.object,
    pagination: PropTypes.object,
    fetchDetail: PropTypes.func.isRequired,
    addFiles: PropTypes.func.isRequired,
    fetchKnowledges: PropTypes.func.isRequired,
    searchEmbedding: PropTypes.func.isRequired,
    updateKnowledge: PropTypes.func.isRequired,
    deleKnowledge: PropTypes.func.isRequired,
    match: PropTypes.object,
    location: PropTypes.object,
    setState: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    uploadOpen: false,
    embeddingOpen: false,
  }

  componentDidMount = async () => {
    const { fileId } = this.props.location.query;
    const { id } = this.props.match.params;
    await this.props.setState({ libraryId: id, fileId });
    this.props.fetchKnowledges({ fileId });
    this.props.fetchDetail(id);
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onLoadMore = () => {
    const pagination = _.cloneDeep(this.props.pagination);
    pagination.pageIndex += 1;
    this.props.fetchKnowledges(pagination);
  }

  renderButtons = () => {
    return [
      <Button
        type="primary"
        style={{ marginRight: 16 }}
        onClick={() => { return this.setState({ embeddingOpen: true }); }}
      >
        命中测试
      </Button>,
      <Button type="primary" onClick={() => { return this.setState({ uploadOpen: true }); }}>
        上传
      </Button>,
    ];
  }

  render = () => {
    const { total, list } = this.props;
    return (
      <div className="chat-knowledge-list">
        <FilterBar
          shouldShowSearchInput={false}
          searchKeyWords={this.state.field}
          renderButtons={this.renderButtons}
        />
        <div className="items">
          <Row gutter={16}>
            {
              (list || []).map((x) => {
                return (
                  <Col span={6} >
                    <Card
                      title={`#${x.id}`}
                      className="knowledge-item-card"
                      extra={[
                        <Popconfirm title="是否删除?!" onConfirm={() => { return this.props.deleKnowledge(x.id); }}>
                          <DeleteOutlined />
                        </Popconfirm>,
                      ]}
                    >
                      <Typography.Paragraph
                        ellipsis={{ rows: 4, expandable: false }}
                        onClick={() => { return this.setState({ open: true, editData: x }); }}
                      >
                        {_.isEmpty(x.question) ? '' : `${x.question}\n`}
                        {x.answer}
                      </Typography.Paragraph>
                    </Card>
                  </Col>
                );
              })
            }
          </Row>
          <div style={{ textAlign: 'center', display: list.length >= total ? 'none' : 'inherit' }}>
            <Button type="link" onClick={() => { return this.onLoadMore(); }}>加载更多...</Button>
          </div>
        </div>

        {
          this.state.uploadOpen &&
          <UploadDrawer
            data={this.props.detail}
            open={this.state.uploadOpen}
            onSubmit={this.props.addFiles}
            onClose={() => { return this.setState({ uploadOpen: false }); }}
          />
        }
        {
          this.state.open &&
          <KnowledgeDrawer
            type={this.props.detail.type}
            data={this.state.editData}
            open={this.state.open}
            onSubmit={this.props.updateKnowledge}
            onClose={() => { return this.setState({ open: false }); }}
          />
        }
        {
          this.state.embeddingOpen &&
          <EmbeddingDrawer
            open={this.state.embeddingOpen}
            onSearch={this.props.searchEmbedding}
            onClose={() => { return this.setState({ embeddingOpen: false }); }}
          />
        }
      </div>
    );
  }
}

export {
  reducer,
};
