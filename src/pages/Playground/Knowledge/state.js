import { ChatBot } from '~/engine';

const SET_STATE = 'CHAT_KNOWLEDGE/SET_STATE';
const CLEAR_STATE = 'CHAT_KNOWLEDGE/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const importKnowledge = (params) => {
  return async () => {
    await ChatBot.importKnowledge(params);
  };
};

export const addKnowledgeLibraryFunc = (params) => {
  return async () => {
    await ChatBot.addKnowledgeLibraryFunc(params);
  };
};

export const fetchKnowledgeLibraries = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().chatKnowledge;
    const searchParams = {
      field: params?.field,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };

    const { items, total } = await ChatBot.fetchKnowledgeLibraries(searchParams);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const searchKnowledgeEmbedding = (params = {}) => {
  return async () => {
    const result = await ChatBot.searchKnowledgeEmbedding(params);
    return result;
  };
};

export const upsertKnowledgeLibrary = (params = {}) => {
  return async (dispatch) => {
    await ChatBot.upsertKnowledgeLibrary(params);
    dispatch(fetchKnowledgeLibraries());
  };
};

export const updateKnowledgeLibrary = (params = {}) => {
  return async (dispatch) => {
    await ChatBot.updateKnowledgeLibrary(params);
    dispatch(fetchKnowledgeLibraries());
  };
};

export const delKnowledgeLibrary = (id) => {
  return async (dispatch) => {
    await ChatBot.delKnowledgeLibrary(id);
    dispatch(fetchKnowledgeLibraries());
  };
};

const _getInitState = () => {
  return {
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    list: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
