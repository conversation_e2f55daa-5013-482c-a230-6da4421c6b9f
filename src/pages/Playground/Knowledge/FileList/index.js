import { FilterBar, PaginationTable } from '~/components';
import { <PERSON><PERSON>, Divider, Popconfirm, Tag } from 'antd';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import UploadDrawer from '../components/UploadDrawer';
import reducer, * as actions from './state';

const STATUS = {
  pending: { name: '处理中', color: 'blue' },
  done: { name: '成功', color: 'green' },
  failed: { name: '失败', color: 'red' },
};
@connect(
  (state) => {
    return state.chatKnowledgeFileList;
  },
  actions,
)
export default class ChatKnowledgeFiles extends Component {
  static propTypes = {
    list: PropTypes.array,
    total: PropTypes.number,
    libraryId: PropTypes.string,
    detail: PropTypes.object,
    pagination: PropTypes.object,
    fetchDetail: PropTypes.func.isRequired,
    fetchFiles: PropTypes.func.isRequired,
    addFiles: PropTypes.func.isRequired,
    delFile: PropTypes.func.isRequired,
    match: PropTypes.object,
    setState: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
  }

  componentDidMount = async () => {
    const { id } = this.props.match.params;
    await this.props.setState({ libraryId: id });
    this.props.fetchFiles();
    this.props.fetchDetail(id);
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onSearch = (e) => {
    this.props.fetchFiles(e);
  }

  renderColumns = () => {
    return [
      {
        title: '文件名',
        dataIndex: 'name',
        key: 'name',
        render: (txt, row) => {
          return (
            <a
              onClick={() => { return this.$push(`/knowledge/${this.props.libraryId}`, { fileId: row.id }); }}
            >{txt}
            </a>
          );
        },
      },
      { title: '分片大小(Token)', dataIndex: 'splitToken', key: 'splitToken', align: 'center' },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        align: 'center',
        render: (txt) => {
          const { name, color } = STATUS[txt];
          return <Tag color={color}>{name}</Tag>;
        },
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (createdAt) => {
          return moment(createdAt).format('YYYY-MM-DD HH:mm');
        },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        render: (txt, row) => {
          return (
            <>
              {
                row.status !== 'pending' ?
                  <>
                    <a
                      onClick={() => { return this.$push(`/knowledge/${this.props.libraryId}`, { fileId: row.id }); }}
                    >数据
                    </a>
                    <Divider type="vertical" />
                    <Popconfirm title="是否删除?!" onConfirm={() => { return this.props.delFile(row.id); }}>
                      <a>删除</a>
                    </Popconfirm>
                  </> :
                  <a onClick={() => { return this.onSearch({}); }}>刷新</a>
              }
            </>
          );
        },
      },
    ];
  }

  renderButtons = () => {
    return [
      <Button
        type="primary"
        style={{ marginRight: 16 }}
        onClick={() => { return this.setState({ open: true }); }}
      >上传
      </Button>,
    ];
  }

  render = () => {
    const { total, list, pagination } = this.props;
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <FilterBar
          shouldShowSearchInput={false}
          searchKeyWords={this.state.field}
          renderButtons={this.renderButtons}
        />

        <PaginationTable
          totalDataCount={total}
          dataSource={list}
          pagination={pagination}
          columns={this.renderColumns()}
          onPaginationChange={(e) => { return this.onSearch(e); }}
        />

        {
          this.state.open &&
          <UploadDrawer
            data={this.props.detail}
            open={this.state.open}
            onSubmit={this.props.addFiles}
            onClose={() => { return this.setState({ open: false }); }}
          />
        }
      </div>
    );
  }
}

export {
  reducer,
};
