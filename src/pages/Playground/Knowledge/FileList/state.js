import { ChatBot } from '~/engine';

const SET_STATE = 'CHAT_KNOWLEDGE_FILE_LIST/SET_STATE';
const CLEAR_STATE = 'CHAT_KNOWLEDGE_FILE_LIST/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchFiles = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination, libraryId } = getState().chatKnowledgeFileList;
    const searchParams = {
      libraryId,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };

    const { items, total } = await ChatBot.fetchKnowledgeFiles(searchParams);
    dispatch(setState({
      total,
      list: items,
      pagination: {
        pageIndex: searchParams['pagination.pageIndex'],
        pageSize: searchParams['pagination.pageSize'],
        orderBy: searchParams['pagination.orderBy'],
      },
    }));
  };
};

export const fetchDetail = (id) => {
  return async (dispatch) => {
    const detail = await ChatBot.getKnowledgeLibrary(id);
    dispatch(setState({ detail }));
  };
};

export const addFiles = (params) => {
  return async (dispatch) => {
    await ChatBot.addKnowledgeFiles(params);
    dispatch(fetchFiles());
  };
};

export const delFile = (id) => {
  return async (dispatch) => {
    await ChatBot.delKnowledgeFile(id);
    dispatch(fetchFiles());
  };
};

const _getInitState = () => {
  return {
    detail: {},
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    list: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
