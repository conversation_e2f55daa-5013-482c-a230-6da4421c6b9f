import { <PERSON><PERSON><PERSON><PERSON>, Toast } from '~/components';
import { <PERSON><PERSON>, Drawer, Table } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class EmbeddingDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    onClose: PropTypes.func,
    onSearch: PropTypes.func,
  }

  state = {
    embeddings: [],
    searchData: {
      topK: 20,
      query: '',
      libraryId: 0,
    },
  }

  componentDidMount = async () => {
  }

  truncateString = (text, maxLength) => {
    if (text.length <= maxLength) {
      return text;
    }

    const truncatedStr = text.slice(0, maxLength); // 截取指定长度的子字符串
    return `${truncatedStr}...`; // 添加省略号
  }

  onChangeSearchValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ searchData: { ...this.state.searchData, [key]: value } });
  }

  onSearchEmbedding = async () => {
    if (_.isEmpty(this.state.searchData?.query)) {
      return;
    }
    const result = await this.props.onSearch(this.state.searchData);
    const embeddings = _.values(result).map((x) => { return { ...x.data, similarity: x.similarity }; });
    await this.setState({ embeddings });
  }

  onCopy = async (text) => {
    await navigator.clipboard.writeText(text);
    Toast.show('复制成功', Toast.Type.SUCCESS);
  }

  renderColumns = () => {
    return [
      {
        title: '内容',
        dataIndex: 'content',
        key: 'content',
        render: (text) => { return this.truncateString(text, 80); },
      },
      { title: '相似度', dataIndex: 'similarity', key: 'similarity', align: 'center' },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (<a onClick={() => { this.setState({ content: row.content, open: true }); }}>详情</a>);
        },
      },
    ];
  }

  renderDrawer = () => {
    return (
      <Drawer
        title="详情"
        open={this.state.open}
        width="30vw"
        extra={<Button type="primary" onClick={() => { return this.onCopy(this.state.content); }}>复制</Button>}
        onClose={() => { return this.setState({ open: false, content: '' }); }}
      >
        {this.state.content}
      </Drawer>
    );
  }

  render = () => {
    const { searchData, embeddings } = this.state;
    return (
      <Drawer
        title="命中测试"
        open={this.props.open}
        width="50vw"
        onClose={() => { return this.props.onClose(); }}
      >
        <div style={{ width: '100%' }}>

          <FilterBar
            canAdd={false}
            placeholder="请输入搜索词"
            searchKeyWords={searchData?.query || ''}
            onSearch={() => { return this.onSearchEmbedding(); }}
            onChange={(e) => { return this.onChangeSearchValue(e, 'query'); }}
          />
          <Table
            needPagination={false}
            dataSource={embeddings}
            columns={this.renderColumns()}
          />
        </div>

        {this.state.open && this.renderDrawer()}
      </Drawer>
    );
  }
}
