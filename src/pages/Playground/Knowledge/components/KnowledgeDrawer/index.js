import './index.less';

import { PlusOutlined } from '@ant-design/icons';
import { <PERSON>yunHelper } from '~/engine';
import { Button, Drawer, Input, Upload } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class KnowledgeDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    type: PropTypes.string,
    data: PropTypes.object,
    onClose: PropTypes.func,
    onSubmit: PropTypes.func,
  }

  state = {
    data: {},
  }

  componentDidMount = async () => {
    const { relatedImages } = this.props.data;
    const imgList = relatedImages?.map((x, idx) => { return { url: x, status: 'done', uid: idx }; });
    this.setState({ data: { ...this.props.data, relatedImages: imgList } });
  }

  onUpload = async (option) => {
    try {
      const url = await <PERSON><PERSON><PERSON><PERSON><PERSON>.clipsUploadImage(option.file, (progress) => {
        const percent = Math.round((progress.loaded / progress.total) * 100);
        option.onProgress({ percent });
      });
      option.onSuccess();
      const { uid, name } = option.file;
      const { relatedImages } = this.state.data;
      const img = { uid, name, status: 'done', url };
      await this.onChangeValue({ target: { value: [...relatedImages, img] } }, 'relatedImages');
    } catch (e) {
      option.onError();
    }
  }

  onDelImg = async (e) => {
    const { relatedImages } = this.state.data;
    const files = relatedImages.filter((x) => { return x.uid !== e.uid; });
    await this.onChangeValue({ target: { value: files } }, 'relatedImages');
  }

  onChangeValue = (e, key) => {
    const { value } = e.target;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onSubmit = async () => {
    const relatedImages = _.map(this.state.data.relatedImages, 'url');
    await this.props.onSubmit({ ...this.state.data, relatedImages });
    this.props.onClose();
  }

  render = () => {
    const { id, question, answer, relatedImages } = this.state.data;

    return (
      <Drawer
        title={`#${id}`}
        open={this.props.open}
        width="50vw"
        className="knowledge-drawer"
        onClose={() => { return this.props.onClose(); }}
      >
        {
          this.props.type === 'qa' &&
          <Input value={question} onChange={(e) => { return this.onChangeValue(e, 'question'); }} />
        }
        <Input.TextArea
          style={{ margin: '10px 0', maxHeight: 'calc(100vh - 240px)' }}
          autoSize={{ minRows: 20 }}
          value={answer}
          onChange={(e) => { return this.onChangeValue(e, 'answer'); }}
        />

        <Upload
          accept="image/*"
          listType="picture-card"
          fileList={relatedImages}
          customRequest={this.onUpload}
          onRemove={this.onDelImg}
        >
          <div>
            <PlusOutlined />
            <div style={{ marginTop: 8 }} >
              Upload
            </div>
          </div>
        </Upload>
        <div style={{ textAlign: 'center' }}>
          <Button type="primary" onClick={this.onSubmit}>保存</Button>
        </div>
      </Drawer>
    );
  }
}
