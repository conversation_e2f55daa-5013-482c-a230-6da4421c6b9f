import { DeleteFilled, InboxOutlined, PlusOutlined } from '@ant-design/icons';
import { Toast } from '~/components';
import { <PERSON><PERSON>Helper, Extentions } from '~/engine';
import { ObjectExtension } from '~/plugins';
import { Button, Divider, Drawer, Form, Input, InputNumber, Tabs, Upload } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

const ACCEPT_TYPES = [
  '.csv',
  '.doc',
  '.docx',
  '.pdf',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
];
const TEMPLATE_URL = 'https://video-clip.oss-cn-shanghai.aliyuncs.com/fe_data/%E9%97%AE%E7%AD%94%E6%A8%A1%E7%89%88.csv';
export default class UploadDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    data: PropTypes.object,
    onClose: PropTypes.func,
    onSubmit: PropTypes.func,
  }

  state = {
    urls: [''],
    files: [],
    splitToken: 800,
    accept: '.csv',
    tabKey: 'upload',
  }

  componentDidMount = async () => {
    const { type } = this.props.data;
    if (type === 'pdf') {
      this.setState({ accept: ACCEPT_TYPES.join(', ') });
    }
  }

  onUpload = async (option) => {
    try {
      const url = await AliyunHelper.clipsUploadImage(option.file, (progress) => {
        const percent = Math.round((progress.loaded / progress.total) * 100);
        option.onProgress({ percent });
      });
      option.onSuccess();
      const { uid, name } = option.file;
      await this.setState({ files: [...this.state.files, { url, uid, name, metadata: { '': '' } }] });
    } catch (e) {
      option.onError();
    }
  }

  onRemove = async (e) => {
    const files = this.state.files.filter((x) => { return x.uid !== e.uid; });
    await this.setState({ files });
  }

  onAddUrl = (index = 0) => {
    const urls = _.cloneDeep(this.state.urls) || [];
    urls.splice(index + 1, 0, '');
    this.setState({ urls });
  }

  onDelUrl = (index) => {
    const urls = _.cloneDeep(this.state.urls) || [];
    if (urls?.length === 1) return;
    urls.splice(index, 1);
    this.setState({ urls });
  }

  onChangeUrls = (e, i) => {
    const urls = _.cloneDeep(this.state.urls) || [];
    urls[i] = e.target.value;
    this.setState({ urls });
  }

  onChangeMetadata = (e, key, idx, fIdx) => {
    const files = _.cloneDeep(this.state.files) || [];
    const metadata = _.cloneDeep(files[fIdx]?.metadata) || {};
    const arrs = ObjectExtension.objToArray(metadata);
    arrs[idx][key] = e.target.value;
    files[fIdx] = {
      ...files[fIdx],
      metadata: arrs.reduce((acc, x) => { return { ...acc, [x.key]: x.value }; }, {}),
    };
    this.setState({ files });
  }

  onAddMetadata = (idx, fIdx) => {
    const files = _.cloneDeep(this.state.files) || [];
    const metadata = _.cloneDeep(files[fIdx]?.metadata) || {};
    const arrs = ObjectExtension.objToArray(metadata);
    metadata[`key_${arrs.length}`] = '';
    files[fIdx] = { ...files[fIdx], metadata };
    this.setState({ files });
  }

  onDelMetadata = (idx, fIdx) => {
    const files = _.cloneDeep(this.state.files) || [];
    const metadata = _.cloneDeep(files[fIdx]?.metadata) || {};
    const arrs = ObjectExtension.objToArray(metadata);
    if (arrs.length === 1) return;
    arrs.splice(idx, 1);
    files[fIdx] = {
      ...files[fIdx],
      metadata: arrs.reduce((acc, x) => { return { ...acc, [x.key]: x.value }; }, {}),
    };
    this.setState({ files });
  }


  onSubmit = async () => {
    const { files, urls, splitToken, tabKey } = this.state;
    const params = { files, splitToken, libraryId: this.props.data.id };
    if (_.isEmpty(files) && tabKey === 'upload') {
      Toast.show('请上传文件', Toast.Type.WARNING);
      return;
    }
    if (tabKey === 'link') {
      const links = urls.filter((x) => { return !_.isEmpty(x); });
      if (_.isEmpty(links)) {
        Toast.show('请输入链接', Toast.Type.WARNING);
        return;
      }
      params.files = links.map((x) => { return { url: x, name: '' }; });
    }

    await this.props.onSubmit(params);
    this.props.onClose();
  }

  render = () => {
    return (
      <Drawer
        title="上传文件"
        open={this.props.open}
        width="50vw"
        onClose={() => { return this.props.onClose(); }}
      >
        <Tabs activeKey={this.state.tabKey} onChange={(e) => { return this.setState({ tabKey: e }); }}>
          <Tabs.TabPane tab="上传" key="upload">
            <div>
              <Upload.Dragger
                multiple
                accept={this.state.accept}
                customRequest={(e) => { return this.onUpload(e); }}
                onRemove={(e) => { this.onRemove(e); }}
              >
                <p className="ant-upload-drag-icon"><InboxOutlined /></p>
                <p className="ant-upload-text">点击或拖动文件到这个区域来上传, 支持单个或批量上传。</p>
              </Upload.Dragger>
              {
                this.props.data?.type !== 'pdf' &&
                <Button
                  type="link"
                  style={{ float: 'right' }}
                  onClick={() => { Extentions.downloadFile(TEMPLATE_URL); }}
                >
                  模版下载
                </Button>
              }
            </div>
          </Tabs.TabPane>
          <Tabs.TabPane tab="网页" key="link">
            <div>
              {
                (this.state.urls).map((x, i) => {
                  return (
                    <Input.Group style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 5 }}>
                      <Input value={x} style={{ width: '80%' }} onChange={(e) => { return this.onChangeUrls(e, i); }} />
                      <div>
                        <Button
                          icon={<DeleteFilled />}
                          shape="circle"
                          type="primary"
                          danger
                          onClick={() => { return this.onDelUrl(i); }}
                        />
                        <Button
                          icon={<PlusOutlined />}
                          shape="circle"
                          type="primary"
                          style={{ marginLeft: 10 }}
                          onClick={() => { return this.onAddUrl(i); }}
                        />
                      </div>
                    </Input.Group>
                  );
                })
              }
            </div>
          </Tabs.TabPane>
        </Tabs>
        <Divider>MetaData</Divider>
        {

          !_.isEmpty(this.state.files) &&
          <>
            {
              (this.state.files || []).map((x, fIdx) => {
                return (
                  <Form.Item label={x.name}>
                    {
                      ObjectExtension.objToArray(x.metadata).map((m, idx) => {
                        return (
                          <Input.Group compact style={{ marginBottom: 5 }}>
                            <Input
                              value={m.key}
                              style={{ width: 200 }}
                              placeholder="metadata key"
                              onChange={(e) => { return this.onChangeMetadata(e, 'key', idx, fIdx); }}
                            />
                            <Input
                              value={m.value}
                              style={{ width: 200 }}
                              placeholder="metadata value"
                              onChange={(e) => { return this.onChangeMetadata(e, 'value', idx, fIdx); }}
                            />
                            <Divider type="vertical" />
                            <PlusOutlined
                              style={{ fontSize: 28 }}
                              onClick={() => { return this.onAddMetadata(idx, fIdx); }}
                            />
                            <Divider type="vertical" />
                            <DeleteFilled
                              style={{ fontSize: 28 }}
                              onClick={() => { return this.onDelMetadata(idx, fIdx); }}
                            />
                          </Input.Group>
                        );
                      })
                    }
                  </Form.Item>
                );
              })
            }
          </>
        }
        <Divider />
        <InputNumber
          min={150}
          addonBefore="分片大小:"
          addonAfter="token"
          onChange={(e) => { return this.onChangeValue(e, 'splitToken'); }}
          value={this.state.splitToken}
        />

        <div style={{ textAlign: 'center' }}>
          <Button type="primary" onClick={this.onSubmit}>确定</Button>
        </div>
      </Drawer>
    );
  }
}
