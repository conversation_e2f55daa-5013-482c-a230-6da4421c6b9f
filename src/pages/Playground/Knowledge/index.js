import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { FilterBar, PaginationTable, Toast } from '~/components';
import { Divider, Drawer, Form, Input, Modal, Popconfirm, Radio, Select } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { KNOWLEDGE_TYPE } from '../Configs';
import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.chatKnowledge;
  },
  actions,
)

export default class ChatKnowledge extends Component {
  static propTypes = {
    list: PropTypes.array,
    total: PropTypes.number,
    pagination: PropTypes.object,
    fetchKnowledgeLibraries: PropTypes.func.isRequired,
    upsertKnowledgeLibrary: PropTypes.func.isRequired,
    updateKnowledgeLibrary: PropTypes.func.isRequired,
    delKnowledgeLibrary: PropTypes.func.isRequired,
    addKnowledgeLibraryFunc: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    visible: false,
    links: [''],
  }

  componentDidMount = () => {
    this.props.fetchKnowledgeLibraries();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  truncateString = (text, maxLength) => {
    if (text.length <= maxLength) {
      return text;
    }

    const truncatedStr = text.slice(0, maxLength); // 截取指定长度的子字符串
    return `${truncatedStr}...`; // 添加省略号
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onChangeLink = (e, index) => {
    const value = e?.target ? e.target.value : e;
    const links = _.cloneDeep(this.state.links) || [];
    links[index] = value;
    this.setState({ links });
  }

  onAddLink = (idx = 0) => {
    const links = _.cloneDeep(this.state.links) || [];
    links.splice(idx + 1, 0, '');
    this.setState({ links });
  }

  onDelLink = (idx = 0) => {
    const links = _.cloneDeep(this.state.links) || [];
    if (links?.length === 1) return;
    links.splice(idx, 1);
    this.setState({ links });
  }

  onSubmit = async () => {
    const { id, field, type } = this.state.data;
    if (_.isEmpty(field) || _.isEmpty(type)) {
      Toast.show('请完善信息', Toast.Type.WARNING);
      return;
    }

    if (_.isUndefined(id)) {
      await this.props.upsertKnowledgeLibrary(this.state.data);
    } else {
      await this.props.updateKnowledgeLibrary({ id, field, type });
    }

    Toast.show('操作成功!', Toast.Type.SUCCESS);
    this.setState({ data: {}, visible: false });
  }

  onSearch = (params = {}) => {
    this.props.fetchKnowledgeLibraries({ ...params, field: this.state.field });
  }

  onAddMetaData = (idx) => {
    const metadata_ = _.cloneDeep(this.state.funcData.metadata_) || [];
    metadata_.splice(idx + 1, 0, {});
    this.setState({ funcData: { ...this.state.funcData, metadata_ } });
  }

  onDelMetaData = (idx) => {
    const metadata_ = _.cloneDeep(this.state.funcData.metadata_) || [];
    if (metadata_?.length === 1) return;
    metadata_.splice(idx, 1);
    this.setState({ funcData: { ...this.state.funcData, metadata_ } });
  }

  onChangeMetaData = (e, key, idx) => {
    const value = e?.target ? e.target.value : e;
    const metadata_ = _.cloneDeep(this.state.funcData.metadata_) || [{}];
    metadata_[idx][key] = value;
    this.setState({ funcData: { ...this.state.funcData, metadata_ } });
  }

  onChangeFuncValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ funcData: { ...this.state.funcData, [key]: value } });
  }

  onShowFunc = (row) => {
    this.setState({
      funcVisible: true,
      funcData: { libraryId: row.id, libraryName: row.field, metadata_: [{}] },
    });
  }

  onSaveFunc = async () => {
    const { name, displayName, description, queryDescription, metadata_ } = this.state.funcData;
    if (_.isEmpty(name) || _.isEmpty(displayName) || _.isEmpty(description) || _.isEmpty(queryDescription)) {
      Toast.show('请完善参数 !', Toast.Type.WARNING);
      return;
    }

    const obj = {};
    (metadata_ || []).forEach((item) => {
      obj[item.key] = item.value;
    });

    await this.props.addKnowledgeLibraryFunc({ ...this.state.funcData, metadata_: obj });
    Toast.show('操作成功!', Toast.Type.SUCCESS);
    this.setState({ funcVisible: false, funcData: {} });
  }

  renderFuncDrawer = () => {
    const { funcData } = this.state;
    return (
      <Drawer
        title="存为函数"
        placement="right"
        closable={false}
        width="40vw"
        onClose={() => { return this.setState({ funcVisible: false }); }}
        visible={this.state.funcVisible}
        extra={<a onClick={() => { return this.onSaveFunc(); }}>保存</a>}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item label="函数名" required>
            <Input value={funcData?.name} onChange={(e) => { return this.onChangeFuncValue(e, 'name'); }} />
          </Form.Item>
          <Form.Item label="别名" required>
            <Input
              value={funcData?.displayName}
              onChange={(e) => { return this.onChangeFuncValue(e, 'displayName'); }}
            />
          </Form.Item>
          <Form.Item label="描述" required>
            <Input
              value={funcData?.description}
              onChange={(e) => { return this.onChangeFuncValue(e, 'description'); }}
            />
          </Form.Item>
          <Form.Item label="查询词描述" required>
            <Input
              value={funcData?.queryDescription}
              onChange={(e) => { return this.onChangeFuncValue(e, 'queryDescription'); }}
            />
          </Form.Item>
          <Form.Item label="元数据">
            {
              (funcData?.metadata_).map((item, idx) => {
                return (
                  <Input.Group compact style={{ display: 'flex' }}>
                    <Input
                      addonBefore="key"
                      value={item?.key}
                      onChange={(e) => { return this.onChangeMetaData(e, 'key', idx); }}
                    />
                    <Input
                      addonBefore="value"
                      value={item?.value}
                      onChange={(e) => { return this.onChangeMetaData(e, 'value', idx); }}
                    />
                    <span style={{ width: 180, display: 'flex', justifyContent: 'space-around' }}>
                      <PlusCircleOutlined
                        style={{ fontSize: 20 }}
                        onClick={() => { return this.onAddMetaData(idx); }}
                      />
                      <DeleteOutlined
                        style={{ fontSize: 20 }}
                        onClick={() => { return this.onDelMetaData(idx); }}
                      />
                    </span>
                  </Input.Group>
                );
              })
            }
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderModal = () => {
    const { visible, data } = this.state;
    return (
      <Modal
        title="新增"
        visible={visible}
        onCancel={() => { return this.setState({ visible: false, data: {} }); }}
        onOk={this.onSubmit}
      >
        <Form>
          <Form.Item label="名称">
            <Input
              value={data?.field}
              onChange={(e) => { return this.onChangeValue(e, 'field'); }}
            />
          </Form.Item>
          <Form.Item label="类型">
            <Select
              value={data?.type}
              disabled={!_.isUndefined(data?.id)}
              onChange={(e) => { return this.onChangeValue(e, 'type'); }}
            >
              {_.map(KNOWLEDGE_TYPE, (val, key) => { return <Select.Option value={key}>{val}</Select.Option>; })}
            </Select>
          </Form.Item>
          <Form.Item label="Embedding">
            <Radio.Group
              value={data?.embeddingMethod}
              disabled={!_.isUndefined(data?.id)}
              onChange={(e) => { return this.onChangeValue(e, 'embeddingMethod'); }}
            >
              <Radio value="default">默认</Radio>
              <Radio value="bge">BGE</Radio>
              <Radio value="bce">BCE</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  renderColumns = () => {
    return [
      {
        title: '名称',
        dataIndex: 'field',
        key: 'field',
        align: 'center',
        render: (name, row) => {
          const { type, id, field } = row;
          return <a onClick={() => { return this.$push(`/knowledge/${id}`, { type, id, field }); }}>{name}</a>;
        },
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        align: 'center',
        render: (type) => { return KNOWLEDGE_TYPE[type] || '-'; },
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (createdAt) => { return moment(createdAt).format('YYYY-MM-DD HH:mm'); },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          const { type, id, field } = row;
          return (
            <>
              <a onClick={() => { return this.onShowFunc(row); }}>存为函数</a>
              <Divider type="vertical" />
              <a onClick={() => { return this.$push(`/knowledge/${id}/files`); }}>管理文件</a>
              <Divider type="vertical" />
              <a onClick={() => { return this.$push(`/knowledge/${id}`, { type, id, field }); }}>管理数据</a>
              <Divider type="vertical" />
              <a onClick={() => { return this.setState({ data: row, visible: true }); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm
                title="是否删除?!"
                onConfirm={() => { return this.props.delKnowledgeLibrary(id); }}
              >
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  render = () => {
    const { total, pagination, list } = this.props;
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <FilterBar
          canAdd
          placeholder="请输入关键字"
          searchKeyWords={this.state.field}
          onSearch={() => { return this.onSearch({ pageIndex: 1 }); }}
          onChange={(e) => { return this.setState({ field: e }); }}
          onAdd={() => { return this.setState({ visible: true }); }}
        />
        <PaginationTable
          totalDataCount={total}
          dataSource={list}
          pagination={pagination}
          columns={this.renderColumns()}
          onPaginationChange={(e) => { return this.onSearch(e); }}
        />

        {this.state.visible && this.renderModal()}
        {this.state.funcVisible && this.renderFuncDrawer()}
      </div>
    );
  }
}

export {
  reducer,
};
