/* eslint-disable no-loop-func, no-await-in-loop, react/no-array-index-key, max-lines */
import './index.less';

import {
  CloseOutlined,
  CopyOutlined,
  FilePdfOutlined,
  MinusCircleOutlined,
  PictureOutlined,
  PlusOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import { IconFont, Toast } from '~/components';
import Configs from '~/consts';
import Engine, { AliyunHelper, ChatBot, Sessions, TextCache } from '~/engine';
import { Platform, StringExtension } from '~/plugins';
import {
  Alert,
  Button,
  Divider,
  Dropdown,
  Input,
  Menu,
  Modal,
  Popconfirm,
  Select,
  Tabs,
  Typography,
  Upload,
} from 'antd';
import { CancelToken } from 'axios';
import _ from 'lodash';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { EVENT_TYPE, PREDICT_AI, PREDICT_AI_PROMPT, QUICK_REPLY, QUICK_REPLY_CONTENT } from '../Configs';
import Utils from '../Utils';
import ReconnectingWebSocket from '../WebSocket';
import AssistantPromptDrawer from './components/AssistantPromptDrawer';
import CreateSessionModal from './components/CreateSessionModal';
import ImportTextModal from './components/ImportTextModal';
import RunWorkflowModal from './components/RunWorkflowModal';
import SessionList from './components/SessionList';
import SettingDrawer from './components/SettingDrawer';
import ShareModal from './components/ShareModal';
import SysPromptDrawer from './components/SysPromptDrawer';
import SystemSetting from './components/SystemSetting';
import reducer, * as actions from './state';

@connect(
  (state) => {
    return {
      ...state.chatBotChat,
      globalFuncs: state.commonLayout.globalFuncs,
      globalApiFuncs: state.commonLayout.globalApiFuncs,
    };
  },
  actions,
)
export default class Chat extends Component {
  static propTypes = {
    groups: PropTypes.array,
    templates: PropTypes.array,
    assistants: PropTypes.array,
    globalFuncs: PropTypes.array,
    globalApiFuncs: PropTypes.object,
    addKnowledge: PropTypes.func.isRequired,
    fetchTokenUsage: PropTypes.func.isRequired,
    createChatbotSession: PropTypes.func.isRequired,
    updateChatbotSession: PropTypes.func.isRequired,
    delChatbotSession: PropTypes.func.isRequired,
    copyChatbotSession: PropTypes.func.isRequired,
    upsertChatbotSessionGroup: PropTypes.func.isRequired,
    fetchChatbotSessionGroups: PropTypes.func.isRequired,
    createChatbotAssistant: PropTypes.func.isRequired,
    fetchChatbotAssistants: PropTypes.func.isRequired,
    fetchChatbotPromptTemplates: PropTypes.func.isRequired,
    delChatbotLLMSetting: PropTypes.func.isRequired,
    updateChatbotLLMSetting: PropTypes.func.isRequired,
    selectChatbotLLMSetting: PropTypes.func.isRequired,
    fetchChatbotLLMSettings: PropTypes.func.isRequired,
    fetchChatbotConversations: PropTypes.func.isRequired,
    fetchKnowledgeLibraries: PropTypes.func.isRequired,
    fetchChatbotSessionConversations: PropTypes.func.isRequired,
    createChatbotSessionConversation: PropTypes.func.isRequired,
    updateChatbotSessionConversation: PropTypes.func.isRequired,
    deleteChatbotSessionConversation: PropTypes.func.isRequired,
    clearChatbotSessionConversation: PropTypes.func.isRequired,
    fetchChatbotSessionTopics: PropTypes.func.isRequired,
    createChatbotSessionTopic: PropTypes.func.isRequired,
    updateChatbotSessionTopic: PropTypes.func.isRequired,
    deleteChatbotSessionTopic: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    tokens: 0,
    tokenUsage: 0,
    groupId: undefined,
    chatId: undefined,
    isFullScreen: false,
    isOutputing: false,
    activeKey: 'prompt',
    libraries: [],
    histories: [],
    chatInfo: {},
    asstInfo: {},
    promptTmp: {},
    msgTmp: {},
    topicId: '',
    export: {},
    lastChatTokenUsage: {
      promptTokens: 0,
      completionTokens: 0,
      totalTokens: 0,
    },
    topic: {},
    topics: [],
    conversation: {},
    conversations: [],
    isSupportVision: false,
    isSupportDocument: false,
  }

  constructor(props) {
    super(props);
    this.outputing = {};
    this.topicMsgs = {};
    this.sources = {};
  }

  componentDidMount = async () => {
    this.cache = new TextCache();
    await this.cache.init();

    const { partnerType } = Sessions.getProfile();
    if (partnerType !== 'playground') {
      this.$replace(Configs.USER_HOMEPAGE[partnerType]);
      return;
    }

    await this.props.fetchChatbotAssistants();
    await this.props.fetchChatbotPromptTemplates();
    await this.props.fetchChatbotSessionGroups();
    let predictAssistantId = this.props.assistants.find((x) => { return x.role === PREDICT_AI; })?.id;
    if (_.isUndefined(predictAssistantId)) { // 不存在 predict_ai
      predictAssistantId = await this.props.createChatbotAssistant({
        name: '查询处理师',
        role: PREDICT_AI,
        llmSetting: {
          model: 'gpt-4',
          temperature: 0,
          maxTokens: 2048,
          systemPrompt: PREDICT_AI_PROMPT,
          version: '1',
          extra: '',
        },
      });
    }
    const { id, chats } = _.head(this.props.groups) || {};
    const tokenUsage = await this.props.fetchTokenUsage();
    const libraries = await this.props.fetchKnowledgeLibraries();

    const chatId = (_.head(chats) || {})?.id;
    await this.fetchSessionConversation(chatId);

    await this.setState({
      libraries,
      tokenUsage,
      predictAssistantId,
      groupId: id,
      chatId,
      chatInfo: _.head(chats || []),
      msgTmp: {},
    }, () => {
      setTimeout(() => {
        const objDiv = document.getElementById(`chat-${this.state.topicId}`);
        objDiv.scrollTop = objDiv?.scrollHeight;
      }, 500);
    });

    this.formatFeatures(this.state.chatInfo);
    this.onChangeScreen();
    Platform.addEventListener('RECEIVE-MSG', this.receiveMsg);
    const mainDom = document.querySelector('.chat-container') || {};
    const targetDom = document.querySelector('.right-wrap') || {};
    if (targetDom) {
      targetDom.style.width = `${mainDom.offsetWidth - 550}px`;
    }
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  formatFeatures = (chatInfo) => {
    const model = chatInfo?.llmSetting?.model;
    const modelConfigs = Sessions.getModelConfigs();
    const modelConfig = modelConfigs[model];
    const isSupportVision = modelConfig?.features.includes('vision');
    const isSupportDocument = modelConfig?.features.includes('document');
    return this.setState({ isSupportDocument, isSupportVision });
  }

  receiveMsg = (data) => {
    this.outputing[data.topicId] = data;
  }

  convertUnicode = (input, role) => {
    let parsedInput;
    try {
      parsedInput = JSON.parse(`"${input}"`);
    } catch (e) {
      parsedInput = input;
    }
    if (role === 'USER_MULTI') {
      try {
        parsedInput = JSON.parse(parsedInput).filter((x) => {
          return x.type === 'text';
        })[0]?.text;
      } catch (e) {
        // nothing
      }
    }
    return parsedInput.replace(/\\u([0-9a-fA-F]{4})/g, (match, charCode) => {
      return String.fromCharCode(parseInt(charCode, 16));
    });
  }

  fetchSessionConversation = async (sessionId) => {
    const topics = await this.props.fetchChatbotSessionTopics({ sessionId });
    if (_.isEmpty(topics)) {
      await this.props.createChatbotSessionTopic({ sessionId, name: '默认' });
    }
    const topicId = _.parseInt(_.head(topics).id);
    await this.setState({ topics, topicId });
    await this.fetchTopicConversation();
  }

  fetchTopicConversation = async (lastContent = '') => {
    const conversations = await this.props.fetchChatbotSessionConversations({
      sessionId: this.state.chatInfo.id,
      topicId: _.parseInt(this.state.topicId),
    });

    if (!_.isEmpty(lastContent)) {
      conversations[conversations.length - 1].content = lastContent;
    }

    for (let i = 0; i < conversations.length; i += 1) {
      const reasoning = await this.cache.getText(conversations[i].id);
      conversations[i].reasoning = reasoning || '';
    }

    await this.setState({ conversations });
    this.topicMsgs[_.parseInt(this.state.topicId)] = conversations;
    return conversations;
  }

  fetchTopics = async () => {
    const topics = await this.props.fetchChatbotSessionTopics({ sessionId: this.state.chatId });
    await this.setState({ topics });
  }

  clearTopicConversation = async (topicId) => {
    await this.props.clearChatbotSessionConversation(topicId);
    this.setState({ conversations: [] });
  };

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ [key]: value });
  }

  onChangeAsstValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ asstInfo: { ...this.state.asstInfo, [key]: value } });
  }

  onSelectChat = async (item) => {
    if (this.state.isOutputing) return;
    if (item.id === this.state.chatId) return;

    await this.setState({ chatId: item.id, chatInfo: item });
    await this.fetchSessionConversation(item.id);
  }

  onDelChat = async (sessionId) => {
    await this.props.delChatbotSession(sessionId);
    await this.props.fetchChatbotSessionGroups();
    if (sessionId === this.state.chatId) {
      const { id, chats } = _.head(this.props.groups);
      const messageGroups = await this.props.fetchChatbotConversations({ sessionId: _.head(chats).id });
      await this.setState({
        groupId: id,
        chatId: _.head(chats).id,
        chatInfo: _.head(chats),
        topicId: _.head(messageGroups).groupId,
        msgTmp: { [_.head(chats).id]: messageGroups },
      }, () => {
        setTimeout(() => {
          const { groupId } = _.head(messageGroups);
          const objDiv = document.getElementById(`chat-${groupId}`);
          objDiv.scrollTop = objDiv?.scrollHeight;
        }, 500);
      });
      const tokens = await this.countTokens(this.state.topicId);
      this.setState({ tokens });
    }
  }

  onChangeScreen = () => {
    const domSider = document.getElementsByClassName('bzy-sider')[0] || {};
    if (this.state.isFullScreen) {
      (domSider.style || {}).display = 'unset';
      this.setState({ isFullScreen: false });
    } else {
      (domSider.style || {}).display = 'none';
      this.setState({ isFullScreen: true });
    }
  }

  onChangeVersion = async (e) => {
    const { groupId, chatInfo } = this.state;
    await this.props.selectChatbotLLMSetting({
      chatbotSessionId: chatInfo.id,
      llmSettingId: e.target.value,
    });
    await this.props.fetchChatbotSessionGroups();
    const { chats } = this.props.groups.find((x) => { return x.id === groupId; });
    const info = chats.find((x) => { return x.id === chatInfo.id; });
    const promptTmp = _.cloneDeep(this.state.promptTmp);
    promptTmp[info.id] = info?.llmSetting?.systemPrompt;
    this.setState({ chatId: info.id, chatInfo: info, promptTmp });
  }

  onAddGroup = async (name) => {
    if (_.isEmpty(name)) {
      Toast.show('请完善信息!', Toast.Type.WARNING);
      return;
    }

    await this.props.upsertChatbotSessionGroup({ name });
    Toast.show('操作完成!', Toast.Type.SUCCESS);
  }

  onDelLLMSetting = async (x) => {
    await this.props.delChatbotLLMSetting(x.id);
    const histories = await this.props.fetchChatbotLLMSettings({ chatSessionId: this.state.chatInfo.id });
    this.setState({ histories });
  }

  onAddMsg = async () => {
    await this.props.createChatbotSessionConversation({
      sessionId: this.state.chatInfo.id,
      topicId: this.state.topicId,
      content: '',
      role: 'USER',
    });

    await this.fetchTopicConversation();
  }

  onDelMsg = async (msgId) => {
    if (!_.isUndefined(msgId)) {
      await this.props.deleteChatbotSessionConversation(msgId);
      await this.fetchTopicConversation();
    } else {
      const conversations = this.state.conversations.filter((x) => { return !_.isUndefined(x.id); });
      this.setState({ conversations });
    }
  }

  onCopyMsg = async (conversation) => {
    await navigator.clipboard.writeText(conversation.content);
    Toast.show('复制成功', Toast.Type.SUCCESS);
  }

  onSaveMsg = async (index) => {
    const { conversations } = this.state;
    const conversation = conversations[index];
    if (conversation?.edited) {
      await this.props.updateChatbotSessionConversation(conversation);
    }
  }

  onChangeMsgRole = async (role, index) => {
    const { conversations } = this.state;
    const conversation = conversations[index];
    let { content } = conversation;
    if (role === 'USER_MULTI' && conversation?.role !== 'USER_MULTI') {
      content = JSON.stringify([{
        type: 'text',
        text: content,
      }]);
    } else if (role !== 'USER_MULTI' && conversation?.role === 'USER_MULTI') {
      content = JSON.parse(content)[0]?.text;
    }
    await this.props.updateChatbotSessionConversation({ ...conversation, role, content });
    await this.fetchTopicConversation();
  }

  onChangeMsgContent = (e, index, role) => {
    let value = e?.target ? e.target.value : e;
    const { conversations } = this.state;
    let conversation = conversations[index];
    if (role === 'USER_MULTI') {
      try {
        const content = JSON.parse(conversation.content);
        content[0].text = value;
        value = JSON.stringify(content);
      } catch (e) { // eslint-disable-line
        value = JSON.stringify([{
          type: 'text',
          text: value,
        }]);
      }
    }
    conversation = { ...conversation, edited: true, content: value };
    conversations[index] = conversation;
    this.setState({ conversations });
  }

  onAddTopic = () => {
    Modal.info({
      title: '话题名称',
      closable: true,
      content: (
        <Input ref={(el) => { this.refTopic = el; }} />
      ),
      okText: '创建',
      onOk: async () => {
        const { chatInfo } = this.state;
        const name = this.refTopic?.input?.value;
        const topic = await this.props.createChatbotSessionTopic({ sessionId: chatInfo.id, name });
        const topics = await this.props.fetchChatbotSessionTopics({ sessionId: this.state.chatId });
        await this.setState({ conversations: [], topicId: topic.id, topics });
      },
    });
  }

  onEditTopic = (topicId) => {
    const { topics } = this.state;
    const topic = _.find(topics, { id: topicId });
    Modal.info({
      title: '修改话题名称',
      closable: true,
      content: (<Input defaultValue={topic.name} ref={(el) => { this.refTopic = el; }} />),
      okText: '修改',
      onOk: async () => {
        const name = this.refTopic?.input?.value;
        await this.props.updateChatbotSessionTopic({ ...topic, name });
        await this.fetchTopics();
      },
    });
  }

  onDelTopic = async (topicId) => {
    const { topics } = this.state;
    if (topics.length <= 1) {
      return;
    }
    await this.props.deleteChatbotSessionTopic(topicId);
    const result = topics.filter((x) => { return `${x.id}` === `${topicId}`; });
    await this.setState({ topics: result, topicId: _.head(result).id });
    await this.fetchTopicConversation();
  }

  onChangeTopic = async (e) => {
    const conversations = await this.props.fetchChatbotSessionConversations({
      sessionId: this.state.chatInfo.id, topicId: e, lastId: 0,
    });
    await this.setState({ topicId: e, conversations });
  }

  onUpdateAsstPrompt = async () => {
    const { asstInfo, chatInfo } = this.state;
    if (_.isEmpty(asstInfo?.prompt) || _.isEmpty(asstInfo?.assistantIds)) {
      Toast.show('请完善信息!', Toast.Type.WARNING);
      return;
    }

    const asst = chatInfo?.assistants?.find((x) => { return asstInfo.assistantIds === x.id; });
    const params = {
      ...asst.llmSetting,
      assistantId: asst.id,
      systemPrompt: asstInfo?.prompt,
      version: `${_.toSafeInteger(asst.llmSetting.version) + 1}`,
    };
    await this.props.updateChatbotLLMSetting(params);
    Toast.show('操作成功!', Toast.Type.SUCCESS);
    this.setState({ asstInfo: {}, asstPromptOpen: false });
  }

  onUpdateSetting = async (isPrompt = false) => {
    const { groupId, chatInfo } = this.state;
    const { llmSetting, id, name, sessionGroupId, llmLegacySettingIds } = chatInfo;

    if (isPrompt) {
      llmSetting.chatbotSessionId = id;
      llmSetting.version = `${(llmLegacySettingIds || []).length + 1}`;
    }

    const llmSettingId = await this.props.updateChatbotLLMSetting(llmSetting);
    if (!_.isUndefined(llmSettingId) && isPrompt) {
      await this.props.selectChatbotLLMSetting({
        chatbotSessionId: id,
        llmSettingId,
      });
      const { chats } = this.props.groups.find((x) => { return x.id === groupId; });
      const info = chats.find((x) => { return x.id === id; });
      await this.setState({
        chatId: info.id,
        chatInfo: { ...info, llmSettingId, llmSetting: { ...llmSetting, id: llmSettingId } },
      });
    }
    await this.props.updateChatbotSession({ id, name, sessionGroupId });
  }

  onCreateSession = async (newSession) => {
    Engine.showLoading();
    const { chatType, name, promptTplId, assistantIds, sessionGroupId, ...llmSetting } = newSession;
    llmSetting.version = '1';
    llmSetting.extra = '';
    await this.props.createChatbotSession({
      chatType, name, promptTplId, assistantIds, sessionGroupId, llmSetting,
    });

    if (chatType === 'p2p') {
      await this.props.fetchChatbotAssistants();
    }
    Toast.show('创建成功!', Toast.Type.SUCCESS);
    this.setState({ createOpen: false });
    Engine.hideLoading();
  }

  onCopySession = async (item) => {
    await this.props.copyChatbotSession({ sessionId: item.id, copyType: '-Copy' });
    Toast.show('复制成功', Toast.Type.SUCCESS);
  }

  onExportToKnowledge = async (e, topicId, index) => {
    const value = QUICK_REPLY_CONTENT[e];
    const { conversations, libraries } = this.state;
    const isSearch = ['searchKb', 'searchBaike'].includes(e);
    const isWorkflow = ['workflow'].includes(e);
    if (_.isUndefined(value) && !isSearch && !isWorkflow) {
      await this.props.addKnowledge({
        libraryId: _.head(libraries)?.id,
        question: '-',
        answer: conversations[index].content,
      });
      this.setState({ [`${topicId}-${index}-Flag`]: false });
      Toast.show('保存成功!', Toast.Type.SUCCESS);
    } else if (isSearch) {
      this.setState({ baikeOpen: true, conversationIdx: index });
    } else if (isWorkflow) {
      this.setState({ workflowOpen: true, conversationIdx: index });
    } else {
      conversations[index].content = _.isEmpty(conversations[index].content) ?
        value : `${conversations[index].content}\n${value}`;
      await this.props.updateChatbotSessionConversation(conversations[index]);
      this.setState({ [`${topicId}-${index}-Flag`]: false, conversations });
    }
  }

  onApplyTxt = async (value) => {
    const { conversations, conversationIdx: index, topicId } = this.state;
    conversations[index].content = value;
    await this.props.updateChatbotSessionConversation(conversations[index]);
    this.setState({ [`${topicId}-${index}-Flag`]: false, conversations, baikeOpen: false });
  }

  onReceiveMsg = async (e) => {
    const path = _.head(e.target?.url?.split('?'));
    const topicId = _.toSafeInteger(_.last(path.split('/')));
    const { type, data } = StringExtension.snakeToCamelObj(JSON.parse(e.data));

    let content = '';
    if (this.state.topicId === topicId &&
      [EVENT_TYPE.TOOL_INPUT, EVENT_TYPE.TOOL_OUTPUT, EVENT_TYPE.LLM_STEP_RESPONSE].includes(type)) {
      let msg = '';
      switch (type) {
        case EVENT_TYPE.TOOL_INPUT:
          const input = JSON.parse(data.msg); // eslint-disable-line
          msg = `[${input.tool_name}]:\n [input]: ${input.code || data.msg}\n`;
          break;
        case EVENT_TYPE.TOOL_OUTPUT:
          msg = `[output]: ${data.msg}\n`;
          break;
        case EVENT_TYPE.LLM_STEP_RESPONSE:
          msg = data.token;
          break;
        default:
          break;
      }

      const objDiv = document.getElementById(`chat-${this.state.topicId}`);
      const newMsgs = _.cloneDeep(this.state.conversations);
      newMsgs[newMsgs.length - 1].content += msg;
      content = newMsgs[newMsgs.length - 1].content; // eslint-disable-line
      this.setState({ conversations: newMsgs }, () => { objDiv.scrollTop = objDiv?.scrollHeight; });
      Platform.emit('RECEIVE-MSG', { topicId, content, steps: type });
    }

    const isEnd = [EVENT_TYPE.EXEC_FAILED, EVENT_TYPE.FINAL_RESULT].includes(type);
    if (isEnd) {
      Platform.emit('RECEIVE-MSG', { topicId, content, steps: 'stop' });
      const newConversations = await this.fetchTopicConversation(_.last(this.state.conversations).content);
      await this.props.updateChatbotSessionConversation(_.last(newConversations));
      this.setState({ isOutputing: false });
      this.ws?.close();
    }
  }

  onReplyV1 = async () => {
    const { chatInfo, predictAssistantId, topicId, conversations } = this.state;
    if (!_.isUndefined(this.outputing[topicId]) && this.outputing[topicId].steps !== 'stop') {
      this.outputing[topicId] = undefined;
      this.sources[topicId].cancel();
      this.setState({ isOutputing: false });
      return;
    }
    this.sources[topicId] = CancelToken.source();

    await this.setState({ isOutputing: true, conversations: [...conversations, { role: 'ASSISTANT', content: '' }] });
    let reasoning = '';
    const replyData = await ChatBot.chatbotReply({
      sseMode: 1,
      sessionId: chatInfo.id,
      topicId,
      predictAssistantId,
      stream: true,
      assistantId: chatInfo?.assistantId,
      userMessage: _.last(conversations)?.content,
    }, {
      cancelToken: this.sources[topicId].token,
      onDownloadProgress: (e) => {
        let arrs = Utils.formatSSE(e?.event?.target?.response);
        const idnex = arrs.findIndex((x) => { return x.steps === 'restart'; });
        arrs = idnex === -1 ? arrs : arrs.slice(idnex);
        const { steps } = _.last(arrs);
        const content = steps === 'restart' ? '' : _.map(arrs, 'token').join('');

        if (this.state.topicId === topicId) {
          const objDiv = document.getElementById(`chat-${this.state.topicId}`);
          const newMsgs = _.cloneDeep(this.state.conversations);
          newMsgs[newMsgs.length - 1].content = content;
          this.setState({ conversations: newMsgs }, () => { objDiv.scrollTop = objDiv?.scrollHeight; });
        }
        if (steps !== 'processing') {
          reasoning = _.map(arrs, 'token').join('');
        }

        Platform.emit('RECEIVE-MSG', { topicId, content, steps });
      },
    });
    const sseResp = _.last(Utils.formatSSE(_.values(replyData).join('')));
    if (sseResp?.steps === 'stop') {
      const sseCamelResp = StringExtension.snakeToCamelObj(sseResp);
      if (!_.isUndefined(sseCamelResp?.tokenUsages)) {
        this.setState({ lastChatTokenUsage: sseCamelResp.tokenUsages });
      }

      if (!_.isUndefined(sseResp?.msg)) {
        Toast.show(sseResp?.msg, Toast.Type.ERROR);
        this.setState({ isOutputing: false });
        return;
      }
    }

    const items = await this.fetchTopicConversation();
    const lastItem = _.last(items);
    reasoning = reasoning.replace(lastItem.content, '');

    if (!_.isEmpty(reasoning) && lastItem?.role === 'ASSISTANT') {
      await this.cache.setText(lastItem.id, reasoning);
    }
    this.setState({ isOutputing: false });
  }

  onReplyV2 = async () => {
    const { chatInfo, topicId, conversations } = this.state;
    await this.setState({
      isOutputing: true,
      conversations: [...conversations, { role: 'ASSISTANT', content: '', outputing: true }],
    });
    const uri = Engine.getWssEndpoint();
    const path = `${uri}/v2/chatbot/sessions/chat/${chatInfo.id}/${topicId}`;
    const query = { access_token: Sessions.getToken() };
    this.ws = new ReconnectingWebSocket(`${path}?${qs.stringify(query)}`, [], this.onReceiveMsg);
    setTimeout(() => {
      this.ws.send(JSON.stringify({ type: 'message', text: '', assistant_id: chatInfo?.assistantId }));
    }, 500);
  }

  onClear = async (topicId) => {
    await this.clearTopicConversation(topicId);
    Toast.show('清空成功!', Toast.Type.SUCCESS);
  }

  onUpload = (key, index) => {
    return async (option) => {
      try {
        const url = await AliyunHelper.clipsUploadImage(option.file, (progress) => {
          const percent = Math.round((progress.loaded / progress.total) * 100);
          option.onProgress({ percent });
        });
        option.onSuccess();

        const fileKey = key === 'image' ? 'image_url' : 'file_url';
        const { conversations } = this.state;
        let conversation = conversations[index];
        let value;
        try {
          const content = JSON.parse(conversation.content);
          content.push({
            type: fileKey,
            [fileKey]: {
              url,
            },
          });
          value = JSON.stringify(content);
        } catch (e) {
          value = JSON.stringify([{
            type: 'text',
            text: '',
          }, {
            type: fileKey,
            [fileKey]: {
              url,
            },
          }]);
        }
        conversation = { ...conversation, edited: true, content: value };
        conversations[index] = conversation;
        await this.setState({ conversations });
        this.onSaveMsg(index);
      } catch (e) {
        option.onError();
      }
    };
  };

  renderMessage = () => {
    const { isOutputing, topicId, chatInfo, lastChatTokenUsage, topics,
      conversations, isSupportVision, isSupportDocument } = this.state;
    const extraBtn = (
      <Button size="small" onClick={() => { return this.setState({ shareOpen: true }); }}>分享/导入</Button>
    );
    const topicOutping = _.isUndefined(this.outputing[topicId]) ? false : this.outputing[topicId].steps !== 'stop';
    const replyVersion = _.isEmpty(chatInfo?.llmSetting?.functions) ? 'V1' : 'V2';

    return (
      <div className="right-wrap">
        <Tabs
          type="editable-card"
          activeKey={`${topicId}`}
          tabBarExtraContent={{ right: extraBtn }}
          onEdit={() => { if (isOutputing) return null; return this.onAddTopic(); }}
          onChange={(e) => { return this.onChangeTopic(e); }}
        >
          {
            (topics || []).map((topic) => {
              return (
                <Tabs.TabPane
                  key={`${topic.id}`}
                  closable={false}
                  tab={
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Button
                        type="text"
                        style={{ padding: 0 }}
                        onDoubleClick={() => { if (isOutputing) return null; return this.onEditTopic(topic.id); }}
                      >
                        {topic.name}
                      </Button>
                      {
                        `${topicId}` === `${topic.id}` &&
                        <Popconfirm title="是否删除?!" onConfirm={() => { return this.onDelTopic(topic.id); }}>
                          <CloseOutlined />
                        </Popconfirm>
                      }
                    </div>
                  }
                >
                  <div id={`chat-${topic.id}`} className="msg-tabpane">
                    {
                      (conversations || []).map((conversation, index) => {
                        return (
                          <div style={{ marginBottom: 5, paddingBottom: 5, borderBottom: '1px solid #ddd' }}>
                            <div>
                              {
                                !!(conversation?.reasoning || [])?.length &&
                                <Alert message={conversation.reasoning} type="info" showIcon />
                              }
                            </div>
                            <Input.Group compact className="msg-item">
                              <div>
                                <Select
                                  bordered={false}
                                  style={{ width: 130, maxHeight: 32 }}
                                  value={conversation.role}
                                  onChange={(e) => { return this.onChangeMsgRole(e, index); }}
                                >
                                  <Select.Option value="USER_MULTI">USER_MULTI</Select.Option>
                                  <Select.Option value="USER">USER</Select.Option>
                                  <Select.Option value="ASSISTANT">ASSISTANT</Select.Option>
                                </Select>
                                <div style={{ display: 'flex', justifyContent: 'space-evenly', alignItems: 'center' }}>
                                  {
                                    conversation.outputing ?
                                      <SyncOutlined spin style={{ color: '#1890ff' }} /> :
                                      <>
                                        <Button
                                          type="text"
                                          icon={<MinusCircleOutlined />}
                                          onClick={() => { return this.onDelMsg(conversation.id); }}
                                        />
                                        <Button
                                          type="text"
                                          icon={<CopyOutlined />}
                                          onClick={() => { return this.onCopyMsg(conversation); }}
                                        />
                                        <IconFont
                                          style={{ fontSize: 18, padding: '0 7px' }}
                                          type={Utils.formatModalIcon(conversation.model)}
                                        />
                                      </>
                                  }
                                </div>
                                <div style={{ padding: '0 11px' }}>
                                  字数: <b>{conversation.content.length}</b>
                                </div>
                              </div>

                              <Dropdown
                                overlay={() => {
                                  return (
                                    <Menu onClick={(e) => { return this.onExportToKnowledge(e.key, topic.id, index); }}>
                                      <Menu.Item key="export">导出至 知识库</Menu.Item>
                                      {_.map(QUICK_REPLY, (v, k) => { return <Menu.Item key={k}>{v}</Menu.Item>; })}
                                    </Menu>
                                  );
                                }}
                                trigger={['contextMenu']}
                                onVisibleChange={(e) => { return this.setState({ [`${topic.id}-${index}-Flag`]: e }); }}
                                visible={this.state[`${topic.id}-${index}-Flag`]}
                              >
                                <Input.TextArea
                                  autoSize
                                  bordered={false}
                                  value={this.convertUnicode(conversation.content, conversation.role)}
                                  onChange={(e) => { return this.onChangeMsgContent(e, index, conversation.role); }}
                                  onBlur={() => { return this.onSaveMsg(index); }}
                                />
                              </Dropdown>
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                {
                                  ((index === conversations.length - 1) && conversation.role === 'USER_MULTI')
                                  && isSupportVision &&
                                  <Upload
                                    accept="image/*"
                                    showUploadList={false}
                                    customRequest={this.onUpload('image', index)}
                                  >
                                    <PictureOutlined style={{ marginRight: 10, cursor: 'pointer' }} />
                                  </Upload>
                                }
                                {
                                  ((index === conversations.length - 1) && conversation.role === 'USER_MULTI')
                                  && isSupportDocument &&
                                  <Upload // eslint-disable-next-line
                                    accept=".csv, .doc, .docx, .pdf, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                    showUploadList={false}
                                    customRequest={this.onUpload('document', index)}
                                  >
                                    <FilePdfOutlined style={{ marginRight: 10, cursor: 'pointer' }} />
                                  </Upload>
                                }
                              </div>
                            </Input.Group>

                            <div style={{ marginLeft: 130 }}>
                              {
                                conversation.role === 'USER_MULTI' &&
                                <div style={{ marginTop: 8 }}>
                                  {
                                    (() => {
                                      try {
                                        const content = JSON.parse(conversation.content);
                                        return content.map((item, idx) => {
                                          if (item.type === 'image_url') {
                                            return (
                                              <img
                                                key={idx}
                                                src={item.image_url.url}
                                                style={{ width: 'auto', height: 32, marginRight: 8 }}
                                              />
                                            );
                                          }
                                          if (item.type === 'file_url') {
                                            return (
                                              <a
                                                key={idx}
                                                href={item.file_url.url}
                                                target="_blank"
                                                rel="noreferrer"
                                              >
                                                <FilePdfOutlined style={{ fontSize: 32, marginRight: 8 }} />
                                              </a>
                                            );
                                          }
                                          return null;
                                        });
                                      } catch (e) {
                                        return null;
                                      }
                                    })()
                                  }
                                </div>
                              }
                            </div>
                          </div>
                        );
                      })
                    }
                    <Button icon={<PlusOutlined />} type="text" onClick={() => { return this.onAddMsg(); }}>
                      新增信息
                    </Button>
                  </div>
                  <div className="btn-submit">
                    <Button />
                    <Button
                      ghost={!topicOutping}
                      disabled={replyVersion === 'V1' ? false : isOutputing}
                      type={topicOutping ? undefined : 'primary'}
                      onClick={() => { return this[`onReply${replyVersion}`](topic.id); }}
                    >
                      {replyVersion === 'V2' ? '提交' : <>{topicOutping ? '取消' : '提交'}</>}
                    </Button>
                    <Popconfirm title="是否清空" onConfirm={() => { return this.onClear(topic.id); }}>
                      <Button type="primary" ghost >清空</Button>
                    </Popconfirm>

                    <span style={{ position: 'absolute', right: 0, bottom: 0 }}>
                      <Typography.Title style={{ marginBottom: 0 }} level={5}>
                        <div style={{ textAlign: 'left' }}>输入: {lastChatTokenUsage.promptTokens}</div>
                        <div style={{ textAlign: 'left' }}>输出: {lastChatTokenUsage.completionTokens}</div>
                      </Typography.Title>
                    </span>
                  </div>
                </Tabs.TabPane>
              );
            })
          }
        </Tabs>
      </div>
    );
  }

  render = () => {
    const { createOpen, sysPromptOpen, asstPromptOpen, settingPromptOpen } = this.state;
    const { chats } = _.head(this.props?.groups || []) || {};

    return (
      <div className="chat-container">
        <div className="chat-list">
          <SessionList
            chatId={this.state.chatId}
            groupId={this.state.groupId}
            chatInfo={this.state.chatInfo}
            groups={this.props.groups}
            isFullScreen={this.state.isFullScreen}
            delChatbotSession={this.onDelChat}
            onCopy={this.onCopySession}
            onChange={this.onChangeValue}
            onSelectChat={this.onSelectChat}
            onChangeScreen={this.onChangeScreen}
          />
          <div className="btn-create" style={{ display: 'flex', justifyContent: 'center' }}>
            <Button
              ghost
              type="primary"
              onClick={() => { return this.setState({ createOpen: true }); }}
            >
              新建会话
            </Button>
          </div>
        </div>
        <Divider type="vertical" style={{ height: '100%' }} />
        <div className="chat-content">
          <SystemSetting
            chats={chats}
            aiModels={Sessions.getModels()}
            ref={(el) => { this.systemSettingRef = el; }}
            msgTmp={this.state.msgTmp}
            promptTmp={this.state.promptTmp}
            chatInfo={this.state.chatInfo || {}}
            libraries={this.state.libraries}
            topicId={this.state.topicId}
            globalFuncs={this.props.globalFuncs}
            globalApiFuncs={this.props.globalApiFuncs}
            fetchChatbotLLMSettings={this.props.fetchChatbotLLMSettings}
            fetchChatbotConversations={this.props.fetchChatbotConversations}
            onUpdateSetting={this.onUpdateSetting}
            onChange={(e) => {
              this.formatFeatures(e?.chatInfo);
              return this.setState(e);
            }}
            topics={this.state.topics}
          />
          <Divider type="vertical" style={{ height: '100%' }} />
          {this.renderMessage()}
        </div>

        {
          sysPromptOpen &&
          <SysPromptDrawer
            chatInfo={this.state.chatInfo}
            histories={this.state.histories}
            open={sysPromptOpen}
            onChangeVersion={this.onChangeVersion}
            onDelLLMSetting={this.onDelLLMSetting}
            onClose={() => { return this.setState({ sysPromptOpen: false }); }}
          />
        }
        {
          asstPromptOpen &&
          <AssistantPromptDrawer
            open={asstPromptOpen}
            chatInfo={this.state.chatInfo}
            asstInfo={this.state.asstInfo}
            onChangeAsstValue={this.onChangeAsstValue}
            onClose={() => { return this.setState({ asstPromptOpen: false }); }}
            onSave={this.onUpdateAsstPrompt}
          />
        }
        {
          settingPromptOpen &&
          <SettingDrawer
            open={settingPromptOpen}
            chatInfo={this.state.chatInfo}
            groups={this.props.groups}
            onChange={(e) => { return this.setState(e); }}
            onSave={this.onUpdateSetting}
            onClose={() => { return this.setState({ settingPromptOpen: false }); }}
          />
        }
        {
          createOpen &&
          <CreateSessionModal
            open={createOpen}
            groups={this.props.groups}
            templates={this.props.templates}
            assistants={this.props.assistants}
            chatInfo={this.state.chatInfo}
            onAddGroup={this.onAddGroup}
            onSubmit={this.onCreateSession}
            onClose={() => { return this.setState({ createOpen: false }); }}
          />
        }
        {
          this.state.shareOpen &&
          <ShareModal
            open={this.state.shareOpen}
            chats={chats}
            groups={this.props?.groups}
            onClose={() => { return this.setState({ shareOpen: false }); }}
            onCopy={this.props.copyChatbotSession}
            fetchGroups={this.props.fetchChatbotSessionGroups}
          />
        }
        {this.state.baikeOpen &&
          <ImportTextModal
            open={this.state.baikeOpen}
            onApplyTxt={this.onApplyTxt}
            onClose={() => { return this.setState({ baikeOpen: false }); }}
          />
        }
        {
          this.state.workflowOpen &&
          <RunWorkflowModal
            open={this.state.workflowOpen}
            onApplyTxt={this.onApplyTxt}
            onClose={() => { return this.setState({ workflowOpen: false }); }}
          />
        }
      </div>
    );
  }
}

export {
  reducer,
};
