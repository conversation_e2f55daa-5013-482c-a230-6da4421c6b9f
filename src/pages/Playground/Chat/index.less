.chat-container {
  display: flex;
  position: relative;
  height: calc(100vh - 72px);
  padding: 10px;
  background: #fff;

  .chat-list,
  .chat-setting {
    position: relative;
    width: 200px;
  }

  .custom-item {
    .ant-switch {
      background-color: #1890ff;
    }
  }

  .chat-list {
    .ant-list-item-meta-title {
      margin-bottom: 0;
    }

    .btn-create {
      position: absolute;
      bottom: 0;
      width: 100%;
      text-align: center;
    }
  }

  .chat-content {
    display: flex;
    width: calc(100% - 270px);

    .ant-tabs-tab {
      padding: 4px 6px;

      .anticon {
        margin-right: 0;
        margin-left: 10px;
        font-size: 12px;
      }
    }

    .ant-tabs-content {
      height: 100%;

      .ant-tabs-tabpane {
        height: 100%;
      }
    }

    .left-wrap {
      width: 300px;
      height: 100%;

      .system-prompt {
        width: 300px;
        height: 100%;

        .ant-card-body {
          height: calc(100% - 180px);
          padding: 5px 0;
        }
      }
    }

    .right-wrap {
      position: relative;
      width: calc(100% - 320px);

      .ant-tabs {
        height: 100%;
      }

      .msg-tabpane {
        max-height: calc(100% - 60px);
        overflow: auto;

        .msg-item {
          display: flex;
          padding: 2px;
        }
      }

      .btn-submit {
        display: flex;
        position: absolute;
        bottom: 0;
        left: 0;
        justify-content: space-around;
        width: 100%;
        text-align: center;
      }
    }
  }

  .history-form-item {
    .ant-form-item-label {
      padding-bottom: 0;
      line-height: 1;

      label {
        width: 100%;
      }
    }
  }
}
