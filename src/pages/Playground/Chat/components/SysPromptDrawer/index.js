import { Drawer, Form, Popconfirm, Radio, Typography } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class SysPromptDrawer extends PureComponent {
  static propTypes = {
    chatInfo: PropTypes.object,
    histories: PropTypes.array,
    open: PropTypes.bool,
    onChangeVersion: PropTypes.func,
    onDelLLMSetting: PropTypes.func,
    onClose: PropTypes.func,
  }

  componentDidMount = () => {
  }

  render = () => {
    const { open, histories, chatInfo } = this.props;
    return (
      <Drawer
        title="系统提示词版本"
        placement="right"
        size="large"
        getContainer={() => { return document.querySelector('.chat-container'); }}
        onClose={() => { return this.props.onClose(); }}
        open={open}
      >
        <Radio.Group
          style={{ width: '100%' }}
          value={chatInfo.llmSetting.id}
          onChange={(e) => { return this.props.onChangeVersion(e); }}
        >
          <Form layout="vertical">
            {
              (histories || []).map((x) => {
                return (
                  <Form.Item
                    className="history-form-item"
                    label={
                      <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
                        <Radio value={x.id}>{`v.${x?.version}.0`}</Radio>
                        {
                          chatInfo.llmSetting.id !== x.id &&
                          <Popconfirm title="是否删除?" onConfirm={() => { return this.props.onDelLLMSetting(x); }} >
                            <a style={{ width: 32 }}>删除</a>
                          </Popconfirm>
                        }
                      </div>
                    }
                  >
                    <Typography.Paragraph>
                      <pre style={{ margin: 0 }}>{x.systemPrompt}</pre>
                    </Typography.Paragraph>
                  </Form.Item>
                );
              })
            }
          </Form>
        </Radio.Group>
      </Drawer>
    );
  }
}
