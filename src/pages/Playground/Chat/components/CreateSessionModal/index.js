import { PlusOutlined } from '@ant-design/icons';
import { Sessions } from '~/engine';
import * as Conf from '~/pages/Playground/Configs';
import Utils from '~/pages/Playground/Utils';
import { Button, Divider, Form, Input, InputNumber, Modal, Select, Space, Switch } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import { OPENAI_PARAMS, OPENAI_PARAMS_MAX_VALUE, OPENAI_PARAMS_STEP } from '../../../Configs';

export default class CreateSessionModal extends PureComponent {
  static propTypes = {
    groups: PropTypes.array,
    templates: PropTypes.array,
    assistants: PropTypes.array,
    chatInfo: PropTypes.object,
    open: PropTypes.bool,
    onAddGroup: PropTypes.func,
    onSubmit: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    groupName: '',
    newSession: {
      chatType: 'p2p',
      model: 'gpt-3.5-turbo',
      promptTplId: 0,
      ...OPENAI_PARAMS,
    },
    aiModels: _.map(Sessions.getModels(), (v, k) => {
      return { label: k, options: v.map((x) => { return { label: Sessions.getModelNames()[x], value: x }; }) };
    }),
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ newSession: { ...this.state.newSession, [key]: value } });
  }

  renderOpenAIParams = (data = {}, onChange = () => { }) => {
    const params = Conf[`${_.toUpper(Utils.pairModel(data?.model))}_PARAMS`];
    return (
      <>
        {
          _.map(params, (v, k) => {
            const value = _.isUndefined(data[k]) ? params[k] : data[k];
            const numProps = OPENAI_PARAMS_MAX_VALUE[k] > 1 ?
              { min: 0, max: OPENAI_PARAMS_MAX_VALUE[k], step: OPENAI_PARAMS_STEP[k], value } :
              { min: 0, max: 1, step: 0.1, value };

            return (
              <Form.Item label={_.upperFirst(k)}>
                {
                  _.isBoolean(value) ?
                    <Switch checked={value} onChange={(e) => { return onChange(e, k); }} /> :
                    <InputNumber {...numProps} onChange={(e) => { return onChange(e, k); }} />
                }
              </Form.Item>
            );
          })
        }
        <Form.Item label="响应类型">
          <Select
            value={data?.responseFormat}
            onChange={(e) => { return onChange(e, 'responseFormat'); }}
          >
            <Select.Option value="text">文本</Select.Option>
            <Select.Option value="json_object">JSON</Select.Option>
          </Select>
        </Form.Item>
      </>
    );
  }

  render = () => {
    const { newSession, aiModels } = this.state;
    const { open, groups } = this.props;

    return (
      <Modal
        title="新建会话"
        open={open}
        onCancel={() => { return this.props.onClose(); }}
        onOk={() => { return this.props.onSubmit(this.state.newSession); }}
      >
        <Form labelCol={{ span: 6 }}>
          <Form.Item label="会话名称">
            <Input
              value={newSession?.name}
              onChange={(e) => { return this.onChangeValue(e, 'name'); }}
            />
          </Form.Item>
          <Form.Item label="分组">
            <Select
              value={newSession?.sessionGroupId}
              onChange={(e) => { return this.onChangeValue(e, 'sessionGroupId'); }}
              dropdownRender={(menu) => {
                return (
                  <>
                    {menu}
                    <Divider style={{ margin: '8px 0' }} />
                    <Space style={{ padding: '0 8px 4px' }}>
                      <Input
                        placeholder="输入分组名称"
                        onChange={(e) => { return this.setState({ groupName: e.target.value }); }}
                      />
                      <Button
                        type="text"
                        icon={<PlusOutlined />}
                        onClick={() => { return this.props.onAddGroup(this.state.groupName); }}
                      >
                        新增分组
                      </Button>
                    </Space>
                  </>
                );
              }}
              options={groups.map((item) => { return { label: item.name, value: item.id }; })}
            />
          </Form.Item>
          <Form.Item label="模型">
            <Select
              options={aiModels}
              value={newSession?.model}
              onChange={(e) => { return this.onChangeValue(e, 'model'); }}
            />
          </Form.Item>
          {this.renderOpenAIParams(newSession, this.onChangeValue)}
        </Form>
      </Modal>
    );
  }
}
