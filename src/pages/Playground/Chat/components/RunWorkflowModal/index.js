import { Toast } from '~/components';
import Configs from '~/consts';
import Engine, { ChatBot, Sessions } from '~/engine';
import { EVENT_TYPE } from '~/pages/Playground/Configs';
import ReconnectingWebSocket from '~/pages/Playground/WebSocket';
import { Form, Input, Modal, Radio, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { PureComponent } from 'react';

export default class RunWorkflowModal extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    onApplyTxt: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    workflows: [],
    data: {
      isBeta: false,
    },
    workflow: {},
    params: {},
  }

  componentDidMount = async () => {
    const { items: workflows } = await ChatBot.fetchChatbotWorkflows(Configs.ALL_PAGE_PARAMS);
    this.setState({ workflows });
  }

  initWebSocket = () => {
    if (this.ws) {
      this.ws.close();
    }
    const { workflow, data, params } = this.state;
    const { uuid, id } = workflow;
    const uri = Engine.getWssEndpoint();
    const path = `${uri}/v2/chatbot/open/workflows/run/${uuid}/${id}-mock`;
    const query = { access_token: Sessions.getToken() };
    this.ws = new ReconnectingWebSocket(`${path}?${qs.stringify(query)}`, [], this.onReceiveMsg);
    setTimeout(() => {
      this.ws.send(JSON.stringify({
        type: 'message',
        text: data?.text,
        is_beta: data.isBeta,
        ...params,
      }));
    }, 300);
  }

  onChangeFlow = (flowId) => {
    const workflow = this.state.workflows.find((x) => { return x.id === flowId; });
    // content.log();
    const content = JSON.parse((workflow.content) || '{}');
    return { workflow, params: content?.params || {} };
  }

  onChangeContent = (isBeta) => {
    const { workflow } = this.state;
    const content = JSON.parse((isBeta ? workflow.betaContent : workflow.content) || '{}');
    return { params: content?.params || {} };
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e?.target.value : e;
    const params = { data: { ...this.state.data, [key]: value } };
    let obj = {};
    switch (key) {
      case 'flowId':
        obj = this.onChangeFlow(value);
        break;
      case 'isBeta':
        obj = this.onChangeContent(value);
        break;
      default:
        break;
    }
    this.setState({ ...params, ...obj });
  }

  onReceiveMsg = async (e) => {
    if (e?.data !== 'pong') {
      const { type, data } = JSON.parse(e.data);
      if (type === EVENT_TYPE.EXEC_FAILED) {
        Toast.show('执行识别', Toast.Type.WARNING);
        this.setState({ confirmLoading: false });
        return;
      }

      if (type === EVENT_TYPE.FINAL_RESULT) {
        await this.props.onApplyTxt(data?.output);
        this.props.onClose();
      }
    }
  }

  onSubmit = () => {
    const { data, workflow } = this.state;
    if (_.isEmpty(workflow) || _.isEmpty(data?.text)) {
      Toast.show('请完善信息', Toast.Type.WARNING);
      return;
    }
    this.setState({ confirmLoading: true });
    this.initWebSocket();
  }

  render = () => {
    const { open } = this.props;
    const { workflows, data, params, confirmLoading } = this.state;

    return (
      <Modal
        title="插入内容"
        open={open}
        width={800}
        maskClosable={false}
        confirmLoading={confirmLoading}
        onCancel={() => { return this.props.onClose(); }}
        onOk={this.onSubmit}
        className="quotation-modal"
      >
        <Form labelCol={{ span: 4 }} >
          <Form.Item label="工作流">
            <Select
              showSearch
              filterOption={(input, option) => { return option.children.includes(input); }}
              value={data?.flowId}
              onChange={(e) => { return this.onChangeValue(e, 'flowId'); }}
            >
              {workflows.map((x) => { return <Select.Option value={x.id}>{x.name}</Select.Option>; })}
            </Select>
          </Form.Item>
          {
            !_.isUndefined(data?.flowId) &&
            <>
              <Form.Item label="输入">
                <Input.TextArea
                  value={data?.text}
                  onChange={(e) => { return this.onChangeValue(e, 'text'); }}
                />
              </Form.Item>
              <Form.Item label="版本">
                <Radio.Group value={data?.isBeta} onChange={(e) => { return this.onChangeValue(e, 'isBeta'); }}>
                  <Radio>预览版</Radio>
                  <Radio value={false}>生产版</Radio>
                </Radio.Group>
              </Form.Item>
              {
                _.map((params || {}), (v, k) => {
                  return (
                    <Form.Item label={k}>
                      <Input
                        value={v}
                        onChange={(e) => { return this.setState({ params: { ...params, [k]: e.target.value } }); }}
                      />
                    </Form.Item>
                  );
                })
              }
            </>
          }
        </Form>
      </Modal>
    );
  }
}
