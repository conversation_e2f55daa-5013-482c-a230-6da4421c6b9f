import { Toast } from '~/components';
import { Form, Modal, Radio, Select, Space, Tabs, Typography } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class QuotationModal extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    type: PropTypes.string,
    chats: PropTypes.array,
    libraries: PropTypes.array,
    fetchChatbotConversations: PropTypes.func,
    onSubmit: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    data: {},
    chats: [],
    libraries: [],
    messageGroups: [],
  }

  componentDidMount = async () => {
    const { type, chats, libraries } = this.props;
    this.setState({ data: { type: (type || 'knowledge') }, chats, libraries });
  }

  onChangeValue = async (e, key) => {
    const value = e?.target ? e.target.value : e;
    const params = { data: { ...this.state.data, [key]: value } };
    if (key === 'chatId') {
      const messageGroups = await this.props.fetchChatbotConversations({ sessionId: value });
      params.messageGroups = messageGroups;
    }

    this.setState(params);
  }

  onSubmit = () => {
    const { data } = this.state;
    if (_.isUndefined(data[data?.type])) {
      Toast.show('请选择信息!', Toast.Type.WARNING);
      return;
    }

    this.props.onSubmit(data[data?.type]);
  }

  renderKnowledge = () => {
    const { libraries } = this.state;
    const { libraryId, knowledge } = this.state?.data;
    const knowledges = libraries.find((x) => { return x.id === libraryId; })?.answers || [];

    return (
      <>
        <Form.Item label="知识库">
          <Select value={libraryId} onChange={(e) => { return this.onChangeValue(e, 'libraryId'); }}>
            {
              (libraries || []).map((x) => {
                return <Select.Option value={x.id}>{x.field}</Select.Option>;
              })
            }
          </Select>
        </Form.Item>
        {
          !_.isUndefined(libraryId) &&
          <Form.Item
            label="知识"
            style={{ maxHeight: '50vh', overflow: 'auto' }}
          >
            <Radio.Group
              value={knowledge}
              style={{ width: '100%' }}
              onChange={(e) => { return this.onChangeValue(e, 'knowledge'); }}
            >
              <Space direction="vertical">
                {
                  knowledges.map((x) => {
                    return (
                      <Radio value={x} style={{ width: '100%' }}>
                        {/* x === knowledge ? <Input.TextArea autoSize defaultValue={x} /> : */}
                        <Typography.Text>{x}</Typography.Text>
                      </Radio>
                    );
                  })
                }
              </Space>
            </Radio.Group>
          </Form.Item>
        }
      </>
    );
  }

  renderChat = () => {
    const { chats, messageGroups } = this.state;
    const { chatId, topicId, chat } = this.state?.data || {};

    return (
      <>
        <Form.Item label="对话名称">
          <Select value={chatId} onChange={(e) => { return this.onChangeValue(e, 'chatId'); }}>
            {
              (chats || []).map((x) => {
                return <Select.Option value={x.id}>{x.name}</Select.Option>;
              })
            }
          </Select>
        </Form.Item>
        {
          !_.isUndefined(chatId) &&
          <Form.Item label="对话内容">
            <Tabs
              activeKey={topicId}
              style={{ maxHeight: '50vh', overflow: 'auto' }}
              onChange={(e) => { return this.onChangeValue(e, 'topicId'); }}
            >
              {
                (messageGroups || []).map((x) => {
                  return (
                    <Tabs.TabPane key={x.groupId} tab={x.name}>
                      <Radio.Group
                        value={chat}
                        style={{ width: '100%' }}
                        onChange={(e) => { return this.onChangeValue(e, 'chat'); }}
                      >
                        <Space direction="vertical">
                          {
                            (x?.messages || []).map((m) => {
                              return (
                                <Radio value={m.content} style={{ width: '100%' }}>
                                  {/* m.content === msg ? <Input.TextArea autoSize defaultValue={m.content} /> : */}
                                  <Typography.Text>{m.content}</Typography.Text>
                                </Radio>
                              );
                            })
                          }
                        </Space>
                      </Radio.Group>
                    </Tabs.TabPane>
                  );
                })
              }
            </Tabs>
          </Form.Item>
        }
      </>
    );
  }

  render = () => {
    const { open } = this.props;
    const { type } = this.state?.data;
    return (
      <Modal
        title="插入内容"
        open={open}
        width={800}
        onCancel={() => { return this.props.onClose(); }}
        onOk={this.onSubmit}
        className="quotation-modal"
      >
        <Form labelCol={{ span: 3 }}>
          <Form.Item label="类型">
            <Select value={type} onChange={(e) => { return this.onChangeValue(e, 'type'); }}>
              <Select.Option value="knowledge">知识库</Select.Option>
              <Select.Option value="chat">对话</Select.Option>
            </Select>
          </Form.Item>
          {type === 'knowledge' && this.renderKnowledge()}
          {type === 'chat' && this.renderChat()}
        </Form>
      </Modal>
    );
  }
}
