import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer, Form, Input, Radio } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class AssistantPromptDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    chatInfo: PropTypes.object,
    asstInfo: PropTypes.object,
    onChangeAsstValue: PropTypes.func,
    onClose: PropTypes.func,
    onSave: PropTypes.func,
  }

  render = () => {
    const { open, chatInfo, asstInfo } = this.props;
    return (
      <Drawer
        title="群成员系统提示词"
        placement="right"
        size="large"
        onClose={() => { return this.props.onClose(); }}
        open={open}
      >
        <Form layout="vertical">
          <Alert message="粘贴提示词后, 勾选需要更新提示词的群成员" />
          <Form.Item className="提示词">
            <Input.TextArea
              autoSize={{ minRows: 16 }}
              value={asstInfo?.prompt}
              onChange={(e) => { return this.props.onChangeAsstValue(e, 'prompt'); }}
            />
          </Form.Item>
          <Form.Item className="群成员">
            <Radio.Group
              value={asstInfo?.assistantIds}
              onChange={(e) => { return this.props.onChangeAsstValue(e, 'assistantIds'); }}
            >
              {
                (chatInfo?.assistants || []).map((x) => {
                  return (<Radio value={x.id}>{x.name}</Radio>);
                })
              }
            </Radio.Group>
          </Form.Item>
          <div style={{ textAlign: 'center' }}>
            <Button onClick={() => { return this.props.onSave(); }}>保存</Button>
          </div>
        </Form>
      </Drawer>
    );
  }
}
