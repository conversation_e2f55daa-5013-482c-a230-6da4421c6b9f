import { MoreOutlined } from '@ant-design/icons';
import { IconFont } from '~/components';
import Utils from '~/pages/Playground/Utils';
import { Avatar, Button, Dropdown, Form, Input, List, Modal, Popconfirm, Select } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class SessionList extends PureComponent {
  static propTypes = {
    chatId: PropTypes.string,
    groupId: PropTypes.string,
    chatInfo: PropTypes.object,
    groups: PropTypes.array,
    isFullScreen: PropTypes.bool,
    delChatbotSession: PropTypes.func,
    onSelectChat: PropTypes.func,
    onCopy: PropTypes.func,
    onChange: PropTypes.func,
    onChangeScreen: PropTypes.func,
  }

  state = { visible: false }

  onSubmit = async () => {
    const { data } = this.state;
    await this.props.onCopy(data);
    this.setState({ visible: false, data: {} });
  }

  renderAvatar = (item) => {
    const type = Utils.formatModalIcon(item?.llmSetting?.model);

    return (
      <Avatar
        size="large"
        style={{ backgroundColor: '#fff' }}
        icon={<IconFont style={{ fontSize: 36, marginTop: 2 }} type={type} />}
      />
    );
  }

  renderDropdown = (item) => {
    const items = [
      {
        key: 'delete',
        label: (
          <Popconfirm
            title="是否删除?"
            onConfirm={() => { return this.props.delChatbotSession(item.id); }}
          >
            <a>删除</a>
          </Popconfirm>
        ),
      },
      {
        key: 'copy',
        label: <a onClick={() => { return this.setState({ visible: true, data: item }); }} >复制</a>,
      },
    ];

    return (
      <Dropdown menu={{ items }} trigger={['click']}>
        <MoreOutlined style={{ position: 'absolute', top: 24, right: 0, fontSize: 18 }} />
      </Dropdown>
    );
  }

  renderCopyModal = () => {
    const { visible, data } = this.state;
    return (
      <Modal
        visible={visible}
        onOk={this.onSubmit}
        onCancel={() => { return this.setState({ visible: false, data: {} }); }}
      >
        <Form>
          <Form.Item label="名称">
            <Input
              value={data.name}
              onChange={(e) => { this.setState({ data: { ...data, name: e.target.value } }); }}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  render = () => {
    const { groupId, chatId, chatInfo, isFullScreen } = this.props;
    const { chats } = this.props.groups.find((x) => { return x.id === groupId; }) || {};

    return (
      <>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 10 }}>
          <Button
            ghost
            type="primary"
            size="small"
            onClick={() => { return this.props.onChangeScreen(); }}
          >
            {isFullScreen ? '退出' : '全屏'}
          </Button>
          <Button
            ghost
            type="primary"
            size="small"
            onClick={() => { return this.props.onChange(true, 'settingPromptOpen'); }}
          >
            设置
          </Button>
          {
            false && chatInfo?.chatType !== 'p2p' &&
            <Button
              ghost
              type="primary"
              size="small"
              onClick={() => { return this.props.onChange(true, 'asstPromptOpen'); }}
            >
              成员设置
            </Button>
          }
        </div>
        <Select
          style={{ width: '100%' }}
          value={groupId}
          onChange={(e) => { return this.props.onChange(e, 'groupId'); }}
        >
          {
            (this.props.groups || []).map((x) => {
              return (<Select.Option value={x.id}>{x.name}</Select.Option>);
            })
          }
        </Select>
        <div style={{ height: '80%', overflow: 'auto' }}>
          <List
            dataSource={chats || []}
            renderItem={(item) => {
              return (
                <div style={{ position: 'relative' }}>
                  <List.Item
                    key={item.id}
                    onClick={() => { return this.props.onSelectChat(item); }}
                    style={item.id === chatId ? { backgroundColor: '#ddd' } : { borderBottom: '1px solid #ddd' }}
                  >
                    <List.Item.Meta
                      title={item.name}
                      style={{ alignItems: 'center' }}
                      avatar={this.renderAvatar(item)}
                    />
                  </List.Item>
                  {this.renderDropdown(item)}
                </div>
              );
            }}
          />
        </div>

        {this.state.visible && this.renderCopyModal()}
      </>
    );
  }
}
