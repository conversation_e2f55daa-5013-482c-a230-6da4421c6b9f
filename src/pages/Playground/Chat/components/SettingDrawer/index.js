import { Button, Drawer, Form, Input, InputNumber, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import { OPENAI_PARAMS, OPENAI_PARAMS_MAX_VALUE, OPENAI_PARAMS_STEP } from '../../../Configs';

export default class SettingDrawer extends PureComponent {
  static propTypes = {
    chatInfo: PropTypes.object,
    groups: PropTypes.object,
    open: PropTypes.bool,
    onChange: PropTypes.func,
    onSave: PropTypes.func,
    onClose: PropTypes.func,
  }

  componentDidMount = () => {
  }

  onChangeSetting = (e, key) => {
    const llmSetting = _.clone(this.props.chatInfo?.llmSetting) || {};
    const value = e?.target ? e.target.value : e;
    this.props.onChange({ chatInfo: { ...this.props.chatInfo, llmSetting: { ...llmSetting, [key]: value } } });
  }

  renderOpenAIParams = (data = {}, onChange = () => { }) => {
    return (
      <>
        {
          _.map(OPENAI_PARAMS, (v, k) => {
            const numProps = OPENAI_PARAMS_MAX_VALUE[k] > 1 ?
              { min: 0, max: OPENAI_PARAMS_MAX_VALUE[k], step: OPENAI_PARAMS_STEP[k], value: data[k] } :
              { min: 0, max: 1, step: 0.1, value: data[k] };

            return (
              <Form.Item label={_.upperFirst(k)}>
                <InputNumber
                  {...numProps}
                  onChange={(e) => { return onChange(e, k); }}
                />
              </Form.Item>
            );
          })
        }
        <Form.Item label="响应类型">
          <Select
            value={data?.responseFormat}
            onChange={(e) => { return onChange(e, 'responseFormat'); }}
          >
            <Select.Option value="text">文本</Select.Option>
            <Select.Option value="json_object">JSON</Select.Option>
          </Select>
        </Form.Item>
      </>
    );
  }

  render = () => {
    const { chatInfo, open } = this.props;
    const groups = this.props.groups.filter((x) => { return x.id >= 0; });

    return (
      <Drawer
        title="会话设置"
        placement="right"
        size="large"
        onClose={() => { return this.props.onClose(); }}
        open={open}
      >
        <Form layout="vertical">
          <Form.Item label="名称">
            <Input
              value={chatInfo?.name}
              onChange={(e) => { return this.props.onChange({ chatInfo: { ...chatInfo, name: e.target.value } }); }}
            />
          </Form.Item>
          <Form.Item label="分组">
            <Select
              value={chatInfo?.sessionGroupId}
              options={groups.map((item) => { return { label: item.name, value: item.id }; })}
              onChange={(e) => { return this.props.onChange({ chatInfo: { ...chatInfo, sessionGroupId: e } }); }}
            />
          </Form.Item>
          {this.renderOpenAIParams(chatInfo?.llmSetting, this.onChangeSetting)}
          <div style={{ textAlign: 'end' }}>
            <Button onClick={() => { return this.props.onSave(); }}>保存</Button>
          </div>
        </Form>
      </Drawer>
    );
  }
}
