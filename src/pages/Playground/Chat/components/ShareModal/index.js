/* eslint-disable no-await-in-loop */
import { CopyOutlined } from '@ant-design/icons';
import { Toast } from '~/components';
import Engine, { AliyunHelper } from '~/engine';
import { Button, Input, Modal, Select, Table, Tabs } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';
import { v4 as uuid } from 'uuid';

export default class ShareModal extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    chats: PropTypes.array,
    groups: PropTypes.array,
    onClose: PropTypes.func,
    onCopy: PropTypes.func,
    fetchGroups: PropTypes.func,
  }

  state = {
    groupId: -1,
    selectedRowKeys: [],
    shareUrl: '',
  }

  componentDidMount = async () => {
  }

  genShareUrl = async (obj = {}) => {
    const key = uuid();
    const blob = new Blob([JSON.stringify(obj)], { type: 'text/plain' });
    await <PERSON>yunHelper.clipsUploadImage(blob, () => { }, { filePath: 'fe_share', fileName: key, fileType: '.json' });
    return `${key}.json`;
  };

  onShare = async () => {
    const { selectedRowKeys } = this.state;
    if (_.isEmpty(selectedRowKeys)) {
      Toast.show('请选择!', Toast.Type.WARNING);
      return;
    }
    const { chats } = this.props;
    const selectedChats = chats.filter((x) => { return selectedRowKeys.includes(x.id); });
    const chatIds = _.map(selectedChats, 'id');
    const shareUrl = await this.genShareUrl({ sessionIds: chatIds });
    this.setState({ shareUrl });
  }

  onImport = async (url) => {
    Engine.showLoading();
    const resp = await fetch(`https://video-clip.oss-cn-shanghai.aliyuncs.com/fe_share/${url}`);
    const { sessionIds } = await resp.json();
    for (let i = 0; i < sessionIds.length; i++) {
      await this.props.onCopy({ sessionId: sessionIds[i], copyType: '-Share' });
    }
    await this.props.fetchGroups();
    Engine.hideLoading();
    this.props.onClose();
  }

  render = () => {
    const { shareUrl, groupId, selectedRowKeys } = this.state;
    const { open, groups } = this.props;
    const { chats } = groups.find((x) => { return x.id === groupId; });
    return (
      <Modal
        title="分享/导入"
        open={open}
        onCancel={() => { return this.props.onClose(); }}
        className="quotation-modal"
        footer={null}
      >
        <Tabs style={{ marginTop: -20 }}>
          <Tabs.TabPane tab="分享" key="share">
            <Select
              style={{ width: '100%' }}
              value={groupId}
              onChange={(e) => { return this.setState({ groupId: e, selectedRowKeys: [] }); }}
            >
              {groups.map((x) => { return <Select.Option value={x.id}>{x.name}</Select.Option>; })}
            </Select>
            <Table
              size="small"
              rowKey="id"
              rowSelection={{
                type: 'checkbox',
                selectedRowKeys,
                onChange: (keys) => { return this.setState({ selectedRowKeys: keys }); },
              }}
              pagination={false}
              dataSource={chats || []}
              scroll={{ y: 300 }}
              columns={[{ title: 'Name', dataIndex: 'name' }]}
            />
            <div style={{ marginTop: 10, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              {
                !_.isEmpty(shareUrl) ?
                  <Input.Search
                    value={shareUrl}
                    enterButton={
                      <CopyToClipboard text={shareUrl} onCopy={() => { Toast.show('复制成功!', Toast.Type.SUCCESS); }}>
                        <CopyOutlined />
                      </CopyToClipboard>
                    }
                  /> :
                  <>&nbsp;</>
              }
              <Button type="primary" onClick={this.onShare}>分享</Button>
            </div>
          </Tabs.TabPane>
          <Tabs.TabPane tab="导入" key="import">
            <Input.Search enterButton="导入" onSearch={this.onImport} />
          </Tabs.TabPane>
        </Tabs>
      </Modal>
    );
  }
}
