/* eslint-disable max-len */
/* eslint-disable react/no-array-index-key */
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { AutoPrompt } from '~/components';
import Engine, { Sessions } from '~/engine';
import { LLM_PRICE_ENUM } from '~/pages/Market/Partners/configs';
import {
  OPENAI_PARAMS_MAX_VALUE,
  OPENAI_PARAMS_STEP,
} from '~/pages/Playground/Configs';
import * as Conf from '~/pages/Playground/Configs';
import Utils from '~/pages/Playground/Utils';
import {
  Card,
  Dropdown,
  Form,
  Input,
  InputNumber,
  Menu,
  Popover,
  Radio,
  Select,
  Switch,
  Tabs,
} from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { StringExtension } from '~/plugins';

import QuotationModal from '../QuotationModal';

export default class SystemSetting extends PureComponent {
  static propTypes = {
    topicId: PropTypes.string,
    chats: PropTypes.array,
    topics: PropTypes.array,
    aiModels: PropTypes.array,
    libraries: PropTypes.array,
    globalFuncs: PropTypes.array,
    globalApiFuncs: PropTypes.object,
    chatInfo: PropTypes.object,
    fetchChatbotLLMSettings: PropTypes.func,
    fetchChatbotConversations: PropTypes.func,
    onUpdateSetting: PropTypes.func,
    onChange: PropTypes.func,
  }

  state = {
    funcTools: Sessions.getFuncTools(),
    activeKey: 'prompt',
    quotation: {},
  }

  componentWillReceiveProps = (nextProps) => {
    if (this.props?.chatInfo?.id !== nextProps?.chatInfo?.id) {
      this.systemPrompt = nextProps.chatInfo?.llmSetting?.systemPrompt;
    }
  }

  formatContent = (str) => {
    if (_.isObject(str)) {
      return str;
    }

    let items = [];
    try {
      items = JSON.parse(str);
    } catch {
      items = [{ text: str, checked: true }];
    }
    return items;
  }

  formatCustomList = () => {
    const { chatInfo, topicId } = this.props;
    const extra = JSON.parse(chatInfo?.llmSetting?.extra || '{}');
    const { session, system } = _.cloneDeep(extra[+topicId] || {});
    const list = [];
    (session?.content || []).forEach((x) => { list.push({ ...x, value: session.value || 'sys' }); });
    (system?.content || []).forEach((x) => { list.push({ ...x, value: system.value || 'sys' }); });
    return list?.length ? list : [{}];
  }

  onShowQuotationModal = (key, idx) => {
    const { activeKey } = this.state;
    let refKey = `${activeKey}`;
    if (!_.isUndefined(idx)) {
      refKey = `txt${idx}`;
    }
    const textarea = this[`${refKey}Ref`].resizableTextArea.textArea;
    let cursorPos = 0;
    if (document.selection) {
      const selectRange = document.selection.createRange();
      selectRange.moveStart('character', -textarea?.value?.length);
      cursorPos = selectRange.text.length;
    } else if (textarea.selectionStart || textarea.selectionStart === '0') {
      cursorPos = textarea.selectionStart;
    }

    this.setState({ quotation: { open: true, type: key, cursorPos }, [`${refKey}Flag`]: false, refKey });
  }

  onQuotationMsg = async (txt) => {
    Engine.showLoading();
    const { activeKey, quotation, refKey } = this.state;
    const textarea = this[`${refKey}Ref`].resizableTextArea.textArea;
    if (textarea.selectionStart || textarea.selectionStart === '0') {
      const endPos = textarea.selectionEnd;
      const { value } = textarea;
      textarea.value = `${value.substring(0, quotation?.cursorPos)}\n${txt}\n${value.substring(endPos, value.length)}`;
    } else {
      textarea.value = txt;
    }

    switch (activeKey) {
      case 'prompt':
        await this.onChangeSetting(textarea.value, 'systemPrompt');
        break;
      default:
        await this.onChangeCustomValue(textarea.value, 'text', +_.trimStart(refKey, 'txt'));
        break;
    }

    this.setState({ quotation: { open: false }, [`${activeKey}Flag`]: false });
    await this.onUpdateSetting();
    Engine.hideLoading();
  }

  onOpenPrompts = async () => {
    const { id } = this.props.chatInfo;
    const histories = await this.props.fetchChatbotLLMSettings({ chatSessionId: id });
    this.props.onChange({ histories, sysPromptOpen: true });
  }

  onChangeSetting = async (e, key) => {
    const llmSetting = _.clone(this.props.chatInfo?.llmSetting) || {};
    const value = e?.target ? e.target.value : e;
    const rules = {};

    if (key === 'model') {
      const modelConfigs = Sessions.getModelConfigs();
      const totalParameterRules = Sessions.getParameterRules();
      const parameterRules = modelConfigs[value]?.parameterRules || [];
      parameterRules.forEach((rule) => {
        rules[rule.name] = rule.default;
      });

      Object.keys(totalParameterRules).forEach((rule) => {
        delete llmSetting[rule];
      });
    }

    await this.props.onChange({
      chatInfo: { ...this.props.chatInfo, llmSetting: { ...llmSetting, [key]: value, ...StringExtension.snakeToCamelObj(rules) } },
    });

    if (key !== 'systemPrompt') {
      this.onUpdateSetting();
    }
  }

  onBlurSystemPrompt = (e) => {
    if (!_.isEqual(e.target.value, this.systemPrompt)) {
      this.onUpdateSetting();
      this.systemPrompt = e.target.value;
    }
  }

  onChangeCustomValue = async (e, key, index) => {
    const value = e?.target ? e.target.value : e;
    const { chatInfo, topicId } = this.props;
    const extra = JSON.parse(chatInfo?.llmSetting?.extra || '{}');
    const list = _.cloneDeep(extra[+topicId] || [{}]);
    list[index][key] = value;
    extra[+topicId] = list;
    await this.props.onChange({
      chatInfo: { ...chatInfo, llmSetting: { ...chatInfo.llmSetting, extra: JSON.stringify(extra) } },
    });

    if (key !== 'text') {
      this.props.onUpdateSetting();
    }
  }

  onAddCustomItem = async (idx) => {
    const { chatInfo, topicId } = this.props;
    const extra = JSON.parse(chatInfo?.llmSetting?.extra || '{}');
    const list = _.cloneDeep(extra[+topicId] || [{}]);
    list.splice(idx + 1, 0, { text: '', checked: false, value: 'sys' });
    extra[+topicId] = list;
    await this.props.onChange({
      chatInfo: { ...chatInfo, llmSetting: { ...chatInfo.llmSetting, extra: JSON.stringify(extra) } },
    });
  }

  onDelCustomItem = async (idx) => {
    const { chatInfo, topicId } = this.props;
    const extra = JSON.parse(chatInfo?.llmSetting?.extra || '{}');
    const list = _.cloneDeep(extra[+topicId] || [{}]);
    if (list?.length === 1) return;
    list.splice(idx, 1);
    extra[+topicId] = list;
    await this.props.onChange({
      chatInfo: { ...chatInfo, llmSetting: { ...chatInfo.llmSetting, extra: JSON.stringify(extra) } },
    });
  }

  onChangeRuleValue = async (e, type) => {
    const value = e?.target ? e.target.value : e;
    const llmSetting = _.clone(this.props.chatInfo?.llmSetting) || {};
    const obj = JSON.parse(llmSetting?.versionNotes || '{}');
    obj[type] = value;
    await this.props.onChange({
      chatInfo: {
        ...this.props.chatInfo,
        llmSetting: {
          ...llmSetting, versionNotes: JSON.stringify(obj),
        },
      },
    });
  }

  onUpdateSetting = async () => {
    const { id, llmSetting } = this.props.chatInfo;
    const histories = await this.props.fetchChatbotLLMSettings({ chatSessionId: id });
    const item = histories.find((x) => { return x.id === llmSetting.id; });
    const flag = _.isEqual(item?.systemPrompt, llmSetting?.systemPrompt);
    this.props.onUpdateSetting(!flag);
  }

  renderOpenAIParams = (data = {}, onChange = () => { }) => {
    const model = data?.model;
    const modelConfigs = Sessions.getModelConfigs();
    const parameterRules = StringExtension.snakeToCamelObj(modelConfigs[model]?.parameterRules || []);

    return (
      <>
        {
          _.map(parameterRules, (rule) => {
            const key = _.camelCase(rule?.name);
            const value = data[key];
            const numProps = rule.max > 1 ?
              { min: 0, max: rule.max, step: 1, value } :
              { min: 0, max: 1, step: 0.1, value };

            return (
              <Form.Item label={_.upperFirst(key)} tooltip={rule?.help?.zh_Hans}>
                {
                  (rule?.type === 'string') && (
                    <Select value={value} onChange={(e) => { return onChange(e, key); }}>
                      {
                        rule?.options.map((x) => {
                          return <Select.Option value={x} label={x} />;
                        })
                      }
                    </Select>
                  )
                }
                {
                  (rule?.type === 'float' || rule?.type === 'int') && (
                    <InputNumber {...numProps} style={{ width: '100%' }} onChange={(e) => { return onChange(e, key); }} />
                  )
                }
                {
                  (rule?.type === 'boolean') && (
                    <Switch checked={value} onChange={(e) => { return onChange(e, key); }} />
                  )
                }
                {
                  (rule?.type === 'text') && (
                    <Input.TextArea autoSize={{ minRows: 6 }} value={value} style={{ width: '100%' }} onChange={(e) => { return onChange(e, key); }} />
                  )
                }
              </Form.Item>
            );
          })
        }
      </>
    );
  }

  renderSysSetting = () => {
    const { chatInfo, aiModels } = this.props;
    const { versionNotes, knowledgeBaseEnabled, searchEngineEnabled,
      searchEngine, functions, openapiFuncs, model } = chatInfo?.llmSetting || {};
    const ruleObj = JSON.parse(versionNotes || '{}');
    const replyVersion = _.isEmpty(functions) ? 'V1' : 'V2';
    const showAll = !knowledgeBaseEnabled && !searchEngineEnabled && _.isEmpty(functions);
    const modelStr = Utils.pairModel(model);
    const modelOpts = _.map(aiModels, (v, k) => {
      return { label: k, options: v.map((x) => { return { label: Sessions.getModelNames()[x], value: x }; }) };
    });

    return (
      <div className="bottom-wrap" style={{ height: 'calc(50% + 100px)', marginTop: -100 }}>
        <Tabs style={{ marginTop: 10 }}>
          <Tabs.TabPane tab="模型" key="model">
            <Form labelCol={{ span: 12 }} className="common-form">
              <Form.Item label="模型">
                <Select
                  value={chatInfo?.llmSetting?.model}
                  onChange={(e) => { return this.onChangeSetting(e, 'model'); }}
                  optionLabelProp="label"
                >
                  {
                    _.map(modelOpts, (v) => {
                      return (
                        <Select.OptGroup key={v.label} label={v.label}>
                          {
                            v.options.map((x) => {
                              return (
                                <Select.Option value={x.value} label={x.label}>
                                  <Popover content={LLM_PRICE_ENUM[x.label]} placement="right">
                                    <div className="demo-option-label-item">
                                      {x.label}
                                      <span
                                        style={{ marginLeft: 20, color: '#ccc', fontSize: 14 }}
                                        aria-label={x.label}
                                      >
                                        {LLM_PRICE_ENUM[x.label]}
                                      </span>
                                    </div>
                                  </Popover>
                                </Select.Option>
                              );
                            })
                          }
                        </Select.OptGroup>
                      );
                    })
                  }
                </Select>
              </Form.Item>
              {this.renderOpenAIParams(chatInfo?.llmSetting, this.onChangeSetting)}
            </Form>
          </Tabs.TabPane>
          <Tabs.TabPane tab="外部知识" key="knowledge">
            <Form labelCol={{ span: 10 }}>
              {
                false && (showAll || replyVersion === 'V1') &&
                <>
                  <Form.Item label="开启知识库">
                    <Switch
                      checked={knowledgeBaseEnabled}
                      onChange={(e) => { return this.onChangeSetting(e, 'knowledgeBaseEnabled'); }}
                    />
                  </Form.Item>
                  <Form.Item label="开启搜索引擎">
                    <Switch
                      checked={searchEngineEnabled}
                      onChange={(e) => { return this.onChangeSetting(e, 'searchEngineEnabled'); }}
                    />
                  </Form.Item>
                  {
                    searchEngineEnabled &&
                    <Form.Item label="搜索引擎">
                      <Select
                        value={searchEngine}
                        onChange={(e) => { return this.onChangeSetting(e, 'searchEngine'); }}
                      >
                        <Select.Option value="tg">天宫搜索</Select.Option>
                        <Select.Option value="serper">Google</Select.Option>
                      </Select>
                    </Form.Item>
                  }
                </>
              }
              {
                (showAll || replyVersion === 'V2') && (modelStr === 'openai' || model === 'ernie-bot-4') &&
                <>
                  <Form.Item label="GPT函数">
                    <Select
                      mode="multiple"
                      value={functions}
                      onChange={(e) => { return this.onChangeSetting(e, 'functions'); }}
                    >
                      {
                        (this.props.globalFuncs || []).map((x, i) => {
                          return (
                            <Select.OptGroup key={i} label={i ? '自定义' : '系统'}>
                              {_.map(x, (v, k) => { return <Select.Option value={k}>{v}</Select.Option>; })}
                            </Select.OptGroup>
                          );
                        })
                      }
                    </Select>
                  </Form.Item>
                  <Form.Item label="API Action">
                    <Select
                      mode="multiple"
                      value={openapiFuncs}
                      onChange={(e) => { return this.onChangeSetting(e, 'openapiFuncs'); }}
                    >
                      {
                        _.map(this.props.globalApiFuncs || [], (v, k) => {
                          return <Select.Option value={+k}>{v}</Select.Option>;
                        })
                      }
                    </Select>
                  </Form.Item>
                </>
              }
            </Form>
          </Tabs.TabPane>
          {
            chatInfo?.chatType === 'p2p' &&
            <Tabs.TabPane tab="人物设定" key="character">
              <Input.TextArea placeholder="请输入角色性格" autoSize={{ minRows: 10 }} />
            </Tabs.TabPane>
          }
          {
            chatInfo?.chatType === 'group' &&
            <Tabs.TabPane tab="讨论规则" key="spkRules">
              <Form.Item label="多实体群">
                <Switch
                  checked={ruleObj?.isMulti}
                  onChange={(e) => { return this.onChangeRuleValue(e, 'isMulti'); }}
                />
              </Form.Item>
              <Form.Item label="发言规则">
                <Radio.Group
                  value={ruleObj?.spkType}
                  onChange={(e) => { return this.onChangeRuleValue(e, 'spkType'); }}
                >
                  <Radio value="cycle">循环</Radio>
                  <Radio value="custom" disabled>自定义</Radio>
                </Radio.Group>
              </Form.Item>
              <Form.Item label="循化次数">
                <InputNumber
                  step="1"
                  min={1}
                  max={10}
                  value={ruleObj?.times}
                  onChange={(e) => { return this.onChangeRuleValue(e, 'times'); }}
                />
              </Form.Item>
            </Tabs.TabPane>
          }
        </Tabs>
      </div>
    );
  }

  renderSysTabPane = (menu) => {
    const { chatInfo } = this.props;
    return (
      <Tabs.TabPane tab="系统" key="prompt" style={{ height: '100%' }}>
        <div className="left-wrap">
          <Card
            title={`v.${chatInfo?.llmSetting?.version || 0}.0`}
            bordered={false}
            extra={<a href="#" onClick={() => { return this.onOpenPrompts(); }}>历史版本</a>}
            className="system-prompt"
            style={{ height: '50%' }}
          >
            <AutoPrompt
              text={chatInfo?.llmSetting?.systemPrompt}
              onImprove={(text) => { return this.onChangeSetting(text, 'systemPrompt'); }}
            />
            <Dropdown
              overlay={menu}
              trigger={['contextMenu']}
              onVisibleChange={(flag) => { return this.setState({ promptFlag: flag }); }}
              visible={this.state.promptFlag}
            >
              <Input.TextArea
                id="prompt"
                style={{ height: '100%' }}
                placeholder="请输入系统提示词"
                ref={(el) => { this.promptRef = el; }}
                value={chatInfo?.llmSetting?.systemPrompt}
                onChange={(e) => { return this.onChangeSetting(e, 'systemPrompt'); }}
                onBlur={this.onBlurSystemPrompt}
              />
            </Dropdown>
          </Card>
          {this.renderSysSetting()}
        </div>
      </Tabs.TabPane>
    );
  }

  renderCustomTabPane = () => {
    const { chatInfo, topicId } = this.props;
    const extra = JSON.parse(chatInfo?.llmSetting?.extra || '{}');
    const list = extra[+topicId] || [{}];

    return (
      <Tabs.TabPane tab="自定义" key="custom">
        <div className="left-wrap">
          <Card bordered={false} className="system-prompt ">
            {
              (list.length ? list : [{}]).map((x, idx) => {
                const menu = (
                  <Menu onClick={({ key }) => { return this.onShowQuotationModal(key, idx); }}>
                    <Menu.Item key="knowledge">插入 知识库</Menu.Item>
                    <Menu.Item key="chat">插入 对话</Menu.Item>
                  </Menu>
                );
                return (
                  <div style={{ marginBottom: 10 }} className="custom-item">
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ marginBottom: 5 }}>
                        <Switch
                          style={{ marginRight: 30 }}
                          checkedChildren="开启"
                          unCheckedChildren="关闭"
                          checked={x.checked}
                          onChange={(e) => { return this.onChangeCustomValue(e, 'checked', idx); }}
                        />
                        <Switch
                          checkedChildren="系统"
                          unCheckedChildren="会话"
                          checked={x.value === 'sys'}
                          onChange={(e) => { return this.onChangeCustomValue(e ? 'sys' : 'user', 'value', idx); }}
                        />
                      </span>
                      <span style={{ marginBottom: 5 }}>
                        <PlusCircleOutlined
                          style={{ fontSize: 18, marginRight: 10 }}
                          onClick={() => { return this.onAddCustomItem(idx); }}
                        />
                        <MinusCircleOutlined
                          style={{ fontSize: 18 }}
                          onClick={() => { return this.onDelCustomItem(idx); }}
                        />
                      </span>
                    </div>
                    <Dropdown
                      overlay={menu}
                      trigger={['contextMenu']}
                      onVisibleChange={(flag) => { return this.setState({ [`txt${idx}Flag`]: flag }); }}
                      visible={this.state[`txt${idx}Flag`]}
                    >
                      <Input.TextArea
                        value={x.text}
                        autoSize={{ minRows: 4, maxRows: 6 }}
                        ref={(el) => { this[`txt${idx}Ref`] = el; }}
                        onChange={(e) => { return this.onChangeCustomValue(e, 'text', idx); }}
                        onBlur={this.props.onUpdateSetting}
                      />
                    </Dropdown>
                  </div>
                );
              })
            }
          </Card>
        </div>
      </Tabs.TabPane>
    );
  }

  render = () => {
    const { chats, libraries } = this.props;
    const { quotation, activeKey } = this.state;

    const menu = (
      <Menu onClick={({ key }) => { return this.onShowQuotationModal(key); }}>
        <Menu.Item key="knowledge">插入 知识库</Menu.Item>
        <Menu.Item key="chat">插入 对话</Menu.Item>
      </Menu>
    );

    return (
      <>
        <Tabs
          activeKey={activeKey}
          onChange={(e) => { return this.setState({ activeKey: e }); }}
        >
          {this.renderSysTabPane(menu)}
          {/* {this.renderCustomTabPane(menu)} */}
        </Tabs>

        {
          quotation?.open &&
          <QuotationModal
            {...quotation}
            chats={chats || []}
            libraries={libraries || []}
            fetchChatbotConversations={this.props.fetchChatbotConversations}
            onClose={() => { return this.setState({ quotation: {} }); }}
            onSubmit={this.onQuotationMsg}
          />
        }
      </>
    );
  }
}
