import { Toast } from '~/components';
import Configs from '~/consts';
import { ChatBot } from '~/engine';
import { Button, Input, Modal, Radio, Select, Table, Typography } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class ImportTextModal extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    onApplyTxt: PropTypes.func,
    onClose: PropTypes.func,
  }

  state = {
    libraryId: undefined,
    type: 'baike',
    libraries: [],
    dataSource: [],
    selectedRowKeys: [],
  }

  componentDidMount = async () => {
    const { items } = await ChatBot.fetchKnowledgeLibraries(Configs.ALL_PAGE_PARAMS);
    this.setState({ libraries: items });
  }

  onSearchBaike = async (e, type) => {
    if (_.isEmpty(e)) return;
    switch (type) {
      case 'baike':
        try {
          const bUrl = encodeURI(`https://baike.baidu.com/item/${e}`);
          const url = `https://fn.bzy.ai/baike/content?url=${encodeURIComponent(bUrl)}`;
          const resp = await fetch(url);
          const { content: data } = await resp.json();
          let value = `${data.title}\n\n${data.content}\n\n`;
          (data?.sections || []).forEach((x) => { value += `${x.subtitle}\n\n${x.content}`; });
          await this.props.onApplyTxt(value);
        } catch (error) {
          Toast.show('未找到相关内容!', Toast.Type.WARNING);
        }
        break;
      case 'kb': // eslint-disable-next-line
        const kbDatas = await ChatBot.searchKnowledgeEmbedding({ query: e, topK: 10, libraryId: this.state.libraryId });
        this.setState({ dataSource: _.map(_.values(kbDatas), 'data') });
        break;
      default:
        break;
    }
  }

  onApply = async () => {
    const { dataSource, selectedRowKeys } = this.state;
    const items = dataSource.filter((x) => { return selectedRowKeys.includes(x.knowledgeId); });
    if (_.isEmpty(items)) return;

    const value = _.map(items, 'content').join('\n\n');
    await this.props.onApplyTxt(value);
  }

  render = () => {
    const { type, libraries, dataSource, selectedRowKeys, libraryId } = this.state;
    return (
      <Modal
        title="搜索"
        width={800}
        open={this.props.open}
        maskClosable={false}
        onCancel={() => { return this.props.onClose(); }}
        footer={null}
      >
        <Radio.Group
          value={type}
          onChange={(e) => { return this.setState({ type: e.target.value }); }}
        >
          <Radio value="baike">百科</Radio>
          <Radio value="kb">知识库</Radio>
        </Radio.Group>
        {
          type === 'kb' &&
          <Select
            style={{ width: 200 }}
            value={libraryId}
            onChange={(e) => { return this.setState({ libraryId: e }); }}
          >
            {libraries.map((x) => { return <Select.Option value={x.id}>{x.field}</Select.Option>; })}
          </Select>
        }
        <Input.Search
          style={{ marginTop: 10 }}
          placeholder="静夜思"
          enterButton="搜索"
          onSearch={(e) => { return this.onSearchBaike(e, type); }}
        />
        {
          !_.isEmpty(dataSource) &&
          <div style={{ maxHeight: 720, overflow: 'auto', position: 'relative' }}>
            <Table
              pagination={false}
              dataSource={dataSource}
              rowKey="knowledgeId"
              rowSelection={{
                type: 'checkbox',
                selectedRowKeys,
                onChange: (keys) => { return this.setState({ selectedRowKeys: keys }); },
              }}
              columns={[
                {
                  title: '内容',
                  dataIndex: 'content',
                  key: 'content',
                  render: (content) => {
                    return (
                      <Typography.Paragraph ellipsis={{ rows: 2, expandable: true }} >
                        {content}
                      </Typography.Paragraph>
                    );
                  },
                },
              ]}
            />
            <Button
              type="primary"
              style={{ position: 'sticky', bottom: 0, float: 'right' }}
              onClick={() => { return this.onApply(); }}
            >
              应用
            </Button>
          </div>
        }
      </Modal>
    );
  }
}
