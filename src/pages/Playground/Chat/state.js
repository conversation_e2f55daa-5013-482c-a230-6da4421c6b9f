import Configs from '~/consts';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ChatBot, Sessions } from '~/engine';
import { Platform } from '~/plugins';
import _ from 'lodash';
import { v4 as uuid } from 'uuid';

const SET_STATE = 'CHAT/SET_STATE';
const CLEAR_STATE = 'CHAT/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchTokenUsage = () => {
  return async () => {
    return 0;
  };
};

export const updateTokenUsage = (count) => {
  return async () => {
    const { id } = Sessions.getPartner();
    const keys = `${Platform.isProd() ? 'prod' : 'stg'}-${id}`;
    const blob = new Blob([`${count}`], { type: 'text/plain' });
    await AliyunHelper.clipsUploadImage(blob, () => { }, {
      filePath: 'fe_data/token-usage',
      fileName: `${keys}`,
      fileType: '.json',
    });
  };
};

export const fetchChatbotSessions = (sessionGroupId = '') => {
  return async (dispatch) => {
    const { items } = await ChatBot.fetchChatbotSessions({ ...Configs.ALL_PAGE_PARAMS, sessionGroupId });
    dispatch(setState({ sessions: items }));
  };
};

export const fetchChatbotSessionGroups = () => {
  return async (dispatch) => {
    let { items } = await ChatBot.fetchChatbotSessionGroups(Configs.ALL_PAGE_PARAMS);
    items = _.orderBy(items, 'id', 'asc');
    const sessions = await ChatBot.fetchChatbotSessions({ ...Configs.ALL_PAGE_PARAMS, source: 'playground' });
    const groups = items.map((item) => {
      const chats = (sessions?.items || []).filter((x) => { return x.sessionGroupId === item.id; });
      return { ...item, chats };
    });
    groups.unshift({ name: '全部', id: -1, chats: (sessions.items || []) });
    dispatch(setState({ groups }));
  };
};

export const fetchChatbotSessionLogs = (sessionId) => {
  return async () => {
    const { items } = await ChatBot.fetchChatbotSessionLogs({ sessionId, skip: 0, limit: 10 });
    return _.orderBy(items, 'id', 'asc');
  };
};

export const upsertChatbotSessionGroup = (params = {}) => {
  return async (dispatch) => {
    await ChatBot.upsertChatbotSessionGroup(params);
    dispatch(fetchChatbotSessionGroups());
  };
};

export const fetchChatbotAssistants = () => {
  return async (dispatch) => {
    const { items } = await ChatBot.fetchChatbotAssistants(Configs.ALL_PAGE_PARAMS);
    dispatch(setState({ assistants: items }));
  };
};

export const fetchChatbotSessionTopics = (params) => {
  return async () => {
    const { items } = await ChatBot.fetchChatbotSessionTopics({ ...Configs.ALL_PAGE_PARAMS, ...params });
    return items;
  };
};

export const createChatbotSessionTopic = (params) => {
  return async () => {
    const result = await ChatBot.createChatbotSessionTopic(params);
    return result;
  };
};

export const updateChatbotSessionTopic = (params) => {
  return async () => {
    await ChatBot.updateChatbotSessionTopic(params);
  };
};

export const deleteChatbotSessionTopic = (id) => {
  return async () => {
    await ChatBot.deleteChatbotSessionTopic(id);
  };
};

export const fetchChatbotSessionConversations = (params) => {
  return async () => {
    const { items } = await ChatBot.fetchChatbotSessionConversations({ ...Configs.ALL_PAGE_PARAMS, ...params });
    return items;
  };
};

export const createChatbotSessionConversation = (params) => {
  return async () => {
    await ChatBot.createChatbotSessionConversation(params);
  };
};

export const updateChatbotSessionConversation = (params) => {
  return async () => {
    await ChatBot.updateChatbotSessionConversation(params);
  };
};

export const deleteChatbotSessionConversation = (id) => {
  return async () => {
    await ChatBot.deleteChatbotSessionConversation(id);
  };
};

export const clearChatbotSessionConversation = (topicId) => {
  return async () => {
    await ChatBot.clearChatbotSessionConversation(topicId);
  };
};

export const createChatbotAssistant = (params = {}) => {
  return async (dispatch) => {
    const { id } = await ChatBot.createChatbotAssistant(params);
    dispatch(fetchChatbotAssistants());
    return id;
  };
};

export const fetchChatbotPromptTemplates = () => {
  return async (dispatch) => {
    const { items } = await ChatBot.fetchChatbotPromptTemplates(Configs.ALL_PAGE_PARAMS);
    dispatch(setState({ templates: items }));
  };
};

export const createChatbotSession = (params = {}) => {
  return async (dispatch) => {
    const { id } = await ChatBot.createChatbotSession(params);
    dispatch(fetchChatbotSessionGroups());
    return id;
  };
};

export const updateChatbotSession = (params = {}) => {
  return async (dispatch) => {
    await ChatBot.updateChatbotSession(params);
    dispatch(fetchChatbotSessionGroups());
  };
};

export const delChatbotSession = (id) => {
  return async () => {
    await ChatBot.delChatbotSession(id);
  };
};

export const updateChatbotLLMSetting = (params = {}) => {
  return async () => {
    const { id } = await ChatBot.updateChatbotLLMSetting(params);
    return id;
  };
};

export const delChatbotLLMSetting = (id) => {
  return async () => {
    await ChatBot.delChatbotLLMSetting(id);
  };
};

export const selectChatbotLLMSetting = (params = {}) => {
  return async () => {
    await ChatBot.selectChatbotLLMSetting(params);
  };
};

export const fetchChatbotLLMSettings = (params = {}) => {
  return async () => {
    const { items } = await ChatBot.fetchChatbotLLMSettings(params);
    if (items.length === 1 && _.isEmpty(_.head(items).systemPrompt)) {
      return [];
    }

    return _.filter(items, (x) => { return !_.isEmpty(x.systemPrompt); });
  };
};

export const updateChatbotConversations = (params = {}) => {
  return async () => {
    await ChatBot.updateChatbotConversations(params);
  };
};

export const fetchChatbotConversations = (params = {}) => {
  return async (dispatch) => {
    let { messageGroups } = await ChatBot.fetchChatbotConversations(params);
    if (_.isEmpty(messageGroups)) {
      const groupId = uuid().replace(/-/g, '');
      messageGroups = [{ messages: [], name: '默认', groupId, summary: '' }];
      dispatch(updateChatbotConversations({ ...params, messageGroups }));
    }
    return messageGroups;
  };
};

export const fetchKnowledgeLibraries = () => {
  return async () => {
    const searchParams = { type: 'qa', ...Configs.ALL_PAGE_PARAMS };
    let { items } = await ChatBot.searchKnowledgeLibraries(searchParams);
    const promises = [];
    (items || []).forEach((item) => { promises.push(ChatBot.searchKnowledges({ libraryId: item.id })); });
    const datas = await Promise.all(promises);
    items = items.map((x, i) => {
      const answers = _.map(datas[i]?.items, 'answer');
      return { id: x.id, field: x.field, answers: _.filter(answers, (a) => { return !_.isEmpty(a); }) };
    });
    return items;
  };
};

export const addKnowledge = (params) => {
  return async () => {
    await ChatBot.addKnowledge(params);
  };
};

export const copyChatbotSession = (params) => {
  return async (dispatch) => {
    await ChatBot.copyChatbotSession(params);
    dispatch(fetchChatbotSessionGroups());
  };
};

export const fetchLibraries = () => {
  return async () => {
    const { items } = await ChatBot.fetchKnowledgeLibraries(Configs.ALL_PAGE_PARAMS);

    return items;
  };
};

export const searchKb = (params = {}) => {
  return async () => {
    const result = await ChatBot.searchKnowledgeEmbedding(params);
    return _.map(_.values(result), 'data');
  };
};

const _getInitState = () => {
  return {
    assistants: [],
    templates: [],
    groups: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
