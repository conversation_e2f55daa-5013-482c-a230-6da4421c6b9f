/* eslint-disable max-len */
export const FUNC_TYPES = {
  0: '系统插件',
  1: '工作流插件',
  2: '知识库插件',
  3: '裂变插件',
  4: '扣子插件',
  5: '课程插件',
  6: 'GPT函数',
};

export const TYPES = {
  knowledge_search: '知识库问答',
  template_reply: '模版回复',
};

export const KNOWLEDGE_TYPE = {
  pdf: '文档类型',
  qa: '问答类型',
};

export const OPENAI_PARAMS = {
  temperature: 0.8,
  topP: 1,
  maxTokens: 2048,
  presencePenalty: 0,
  frequencyPenalty: 0,
  // n: 1,
};

export const HUNYUAN_PARAMS = {
  temperature: 0.8,
  topP: 1,
  maxTokens: 2048,
};

export const CHATGLM_PARAMS = {
  temperature: 0.8,
  topP: 1,
};

export const QWEN_PARAMS = {
  topP: 0.8,
  topK: 1,
};

export const ERNIE_PARAMS = {
  temperature: 0.95,
  topP: 0.8,
  penaltyScore: 1,
};

export const BAICHUAN_PARAMS = {
  temperature: 0.3,
  topK: 5,
  topP: 0.85,
};

export const XFYUN_PARAMS = {
  temperature: 0.95,
  topK: 4,
  maxTokens: 2048,
};

export const CLAUDE_PARAMS = {
  maxTokens: 1000,
  temperature: 1.0,
  topP: 0.8,
  topK: 1,
};

export const OPENAI_PARAMS_MAX_VALUE = {
  maxTokens: 32000,
  topK: 100,
  n: 100,
};

export const OPENAI_PARAMS_STEP = {
  maxTokens: 1000,
  topK: 1,
  n: 1,
};

export const GPT4_MAX_TOKEN = 8192;

export const SUMMARY_PROMPT = '总结以上所有对话，提炼要点，注明哪些是我的反馈，我要删了，只带着提炼的要点开展接下去的对话，格式要简洁';

export const PREDICT_AI = 'predict_ai';

export const PREDICT_AI_PROMPT = "You are an assistant that checks if the given input is enough to answer the user's message, you aware that current time is [<time>].\n\nGiven the following information about the assistant, user, existing knowledge, chat history, and the latest user message, determine if the assistant needs to query additional information to answer the user. \n\nIf the assistant needs to query, provide a complete and standalone semantically valid phrase/sentence/question for the query in Chinese, considering the context of the conversation. \nIf the assistant does not need to query, the \"query\" field should be left empty.\nIf the assistant does not know what information to search for, ask proper clarify question.\nDo not make assumption, stick to the inputs.\nThe assistant needs to pay attention to general information that does not helpful and should not count as valid information.\nIf user is asking about a specific day, include the actual day in the query.\n\nYou should first go through a step-by-step thought process then output the result in a valid JSON format in a separated code block.\nFollow the following output format:\n\nOutput:\nThink: Is the answer contained within the input? [yes|no]\nThink: Is the information in the input correct? [yes|no]\nReasoning: [What information is needed to answer the message? How do I update the query if the input may be incorrect? Do I need to ask user clarify question? ]\nResults:\n```json\n{\n  \"need_query\": [true/false],\n  \"need_clarify\": [true/false],\n  \"query\": [detailed query_phrase/sentence/question],\n  \"clarify_question\": [clarify question to user]\n}\n```";

export const TABS = [
  { name: '背景', key: 'system' }, // 多用于系统prompt, 故为system
  { name: '用户', key: 'session' }, // 多用于当前对话, 故为session
];

export const QUICK_REPLY = {
  shortAnswer: '简短回答',
  thinkStep: '逐步思考',
  searchBaike: '百科搜索',
  searchKb: '知识搜索',
  workflow: '工作流',
};

export const QUICK_REPLY_CONTENT = {
  shortAnswer: '请用不超过50个单词的简短回答来解释',
  thinkStep: '请按照逻辑一步一步思考',
};

export const EVENT_TYPE = {
  EXEC_STEP: 'exec_step',
  EXEC_LOG: 'exec_log',
  EXEC_ACTION_REQUIRED: 'exec_action_required',
  EXEC_FAILED: 'exec_failed',
  OP_RESULT: 'op_result',
  LLM_TOKEN_USAGE: 'llm_token_usage',
  LLM_REQUEST: 'llm_request',
  TOOL_INPUT: 'tool_input',
  TOOL_OUTPUT: 'tool_output',
  LLM_STEP_RESPONSE: 'llm_step_response',
  LLM_RESPONSE: 'llm_response',
  FINAL_RESULT: 'final_result',
};

export const EVENT_TYPE_ZH = {
  op_result: '节点执行结果',
  llm_request: '大模型请求',
  llm_response: '大模型回复',
  llm_step_response: '大模型回复',
  tool_input: '插件请求',
  tool_output: '插件结果',
  exec_log: '运行日志',
  exec_failed: '运行异常',
  exec_action_required: '用户输入',
  final_result: '工作流结果',
  llm_token_usage: '大模型用量',
};
