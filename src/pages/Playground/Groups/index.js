import { FilterBar, PaginationTable, Toast } from '~/components';
import { Divider, Form, Input, Modal, Popconfirm } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.chatBotGroup;
  },
  actions,
)
export default class ChatBotGroups extends Component {
  static propTypes = {
    list: PropTypes.array,
    total: PropTypes.number,
    pagination: PropTypes.object,
    fetchChatbotSessionGroups: PropTypes.func.isRequired,
    upsertChatbotSessionGroup: PropTypes.func.isRequired,
    updateChatbotSessionGroup: PropTypes.func.isRequired,
    delChatbotSessionGroup: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    data: {},
  }

  componentDidMount = async () => {
    await this.props.fetchChatbotSessionGroups();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onSearch = () => {
  }

  onSubmit = async () => {
    const { name, id } = this.state.data || {};
    if (_.isEmpty(name)) {
      Toast.show('请完善信息!', Toast.Type.WARNING);
      return;
    }

    if (_.isUndefined(id)) {
      await this.props.upsertChatbotSessionGroup(this.state.data);
    } else {
      await this.props.updateChatbotSessionGroup({ id, name });
    }
    Toast.show('操作成功!', Toast.Type.SUCCESS);
    this.setState({ visible: false, data: {} });
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '名称', dataIndex: 'name', key: 'name', align: 'center' },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <a onClick={() => { return this.setState({ visible: true, data: row }); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm
                title="是否删除?!"
                onConfirm={() => { return this.props.delChatbotSessionGroup(row.id); }}
              >
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  renderModal = () => {
    const { visible, data } = this.state;
    return (
      <Modal
        title="新增"
        visible={visible}
        onOk={this.onSubmit}
        onCancel={() => { return this.setState({ visible: false, data: {} }); }}
      >
        <Form>
          <Form.Item label="名称">
            <Input
              value={data?.name}
              onChange={(e) => { return this.onChangeValue(e, 'name'); }}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  render = () => {
    const { total, list, pagination } = this.props;

    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <FilterBar
          canAdd
          shouldShowSearchInput={false}
          onAdd={() => { return this.setState({ visible: true, data: {} }); }}
        />
        <PaginationTable
          totalDataCount={total}
          dataSource={list}
          pagination={pagination}
          columns={this.renderColumns()}
          onPaginationChange={(e) => { return this.onSearch(e); }}
        />

        {this.state.visible && this.renderModal()}
      </div>
    );
  }
}

export {
  reducer,
};
