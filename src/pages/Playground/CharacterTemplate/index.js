import { ExpandOutlined } from '@ant-design/icons';
import { Toast } from '~/components';
import Configs from '~/consts';
import { Sessions } from '~/engine';
import { StringExtension } from '~/plugins';
import { <PERSON><PERSON>, Card, Col, Input, Modal, Popconfirm, Row, Tag } from 'antd';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';
import { connect } from 'react-redux';

import { OPENAI_PARAMS } from '../Configs';
import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.characterTemplate;
  },
  actions,
)
export default class CharacterTemplates extends Component {
  static propTypes = {
    fetchChatbotCharacterTemplates: PropTypes.func.isRequired,
    createChatbotSession: PropTypes.func.isRequired,
    list: PropTypes.array,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    data: {},
    detailVisible: false,
    searchParams: { name: '', orderBy: 'seq asc' },
  }

  componentDidMount = async () => {
    await this.props.fetchChatbotCharacterTemplates();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeSearchParams = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ searchParams: { ...this.state.searchParams, [key]: value } });
  }

  onSearch = async () => {
    await this.props.fetchChatbotCharacterTemplates(this.state.searchParams);
  }

  onCreateSession = async (data) => {
    const llmSetting = {
      ...OPENAI_PARAMS,
      systemPrompt: data.prompts,
      version: '1',
      extra: '',
      model: 'gpt-3.5-turbo',
    };
    const params = { chatType: 'p2p', name: data.name, llmSetting };
    await this.props.createChatbotSession(params);
    await this.$replace(Configs.ROUTE.HOMEPAGE);
    Sessions._eventEmitter.emit(Configs.SUB_MENU_CHANGE, Configs.ROUTE.HOMEPAGE);
  }

  onShowDetail = (data) => {
    this.setState({ data, detailVisible: true });
  }

  onHideDetail = () => {
    this.setState({ data: {}, detailVisible: false });
  }

  renderDetailModel = () => {
    const { detailVisible, data } = this.state;
    return (
      <Modal
        title={data.name}
        visible={detailVisible}
        onCancel={() => { return this.onHideDetail(); }}
        footer={[
          <CopyToClipboard text={data.prompts} onCopy={() => { Toast.show('复制成功!', Toast.Type.SUCCESS); }}>
            <Button type="primary">复制</Button>
          </CopyToClipboard>,
          <Popconfirm title="是否确认使用，这将会创建一个新的会话?!" onConfirm={() => { return this.onCreateSession(data); }}>
            <Button type="primary" style={{ marginLeft: 10 }} >使用</Button>,
          </Popconfirm>,
        ]}
      >
        <Input.TextArea style={{ maxHeight: '70vh' }} autoSize value={data.prompts} />
      </Modal>
    );
  }

  renderCharacters = () => {
    const { list } = this.props;
    const totalCards = list.length;
    const cardsPerRow = 4;
    const totalRows = Math.ceil(totalCards / cardsPerRow);
    const rows = [];
    for (let i = 0; i < totalRows; i++) {
      const startIndex = i * cardsPerRow;
      const endIndex = startIndex + cardsPerRow;
      const cardGroup = list.slice(startIndex, endIndex);
      const row = (
        <Row gutter={16} key={i}>
          {cardGroup.map((item) => {
            return (
              <Col span={24 / cardsPerRow} key={item.id}>
                <Card
                  title={item.name}
                  bordered={false}
                  extra={<ExpandOutlined onClick={() => { return this.onShowDetail(item); }} />}
                >
                  {StringExtension.truncateString(item.prompts, 60)}
                  {
                    item.tags.length > 0 &&
                    <div style={{ marginTop: 10 }}>
                      {(item.tags || []).map((tag) => { return <Tag color="blue">{tag}</Tag>; })}
                    </div>
                  }
                </Card>
              </Col>
            );
          })}
        </Row>
      );
      rows.push(row);
    }

    return rows;
  }

  render = () => {
    const { searchParams } = this.state;
    return (
      <div className="character-templates" style={{ padding: 30, background: '#fff' }}>
        <div style={{ padding: 10 }}>
          <Input
            style={{ display: 'inline-block', width: 200, marginRight: 10 }}
            placeholder="请输入搜索词"
            value={searchParams.name}
            onPressEnter={this.onSearch}
            onChange={(e) => { return this.onChangeSearchParams(e, 'name'); }}
          />
          <Button type="primary" onClick={this.onSearch}>搜素</Button>
        </div>
        <div style={{ height: 'calc(100vh - 180px)', overflow: 'auto' }}>
          {this.renderCharacters()}
        </div>
        {this.state.detailVisible && this.renderDetailModel()}
      </div>
    );
  }
}

export {
  reducer,
};
