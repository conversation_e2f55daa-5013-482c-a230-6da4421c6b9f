import { ChatBot } from '~/engine';

const SET_STATE = 'CHATBOT_GROUPS/SET_STATE';
const CLEAR_STATE = 'CHATBOT_GROUPS/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchChatbotCharacterTemplates = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().characterTemplate;
    const searchParams = {
      ...params,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };

    const { items, total } = await ChatBot.fetchChatbotCharacterTemplates(searchParams);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const createChatbotSession = (params = {}) => {
  return async () => {
    const { id } = await ChatBot.createChatbotSession(params);
    return id;
  };
};

const _getInitState = () => {
  return {
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 1000,
      orderBy: 'seq asc',
    },
    list: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
