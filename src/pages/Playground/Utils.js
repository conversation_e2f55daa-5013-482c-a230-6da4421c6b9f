import { StringExtension } from '~/plugins';
import _ from 'lodash';
import { Configuration, OpenAIApi } from 'openai';

const configuration = new Configuration({
  basePath: 'https://aiapi.chat2chat.net/v1',
  apiKey: process.env.OPENAI_API_KEY,
});
const openai = new OpenAIApi(configuration);

export default class Utils {
  static formatGroupUser = (assistants, assistantIds) => {
    const ass = assistants.filter((x) => { return assistantIds.includes(x.id); });
    const users = [];
    ass.forEach((x) => {
      const { frequencyPenalty, maxTokens, model, presencePenalty, systemPrompt, temperature, topP } = x.llmSetting;
      users.push({
        name: x.name,
        frequencyPenalty,
        maxTokens,
        model,
        presencePenalty,
        systemPrompt,
        temperature,
        topP,
      });
    });

    return users;
  }

  static formatMsgInfo = (msgTmp, chatInfo, name, groupId) => {
    const { messages } = msgTmp[chatInfo.id].find((x) => { return x.groupId === groupId; });
    const msgs = _.cloneDeep(messages).map((x) => { return { ...x, role: _.toLower(x.role) }; });
    const messageGroups = msgTmp[chatInfo.id].map((mg) => {
      if (mg.groupId === groupId) { // eslint-disable-next-line
        mg.messages = [...messages, { role: name, content: '' }];
      }
      return mg;
    });

    return { msgs, messageGroups };
  }

  static formatMultiUserParams = (name, params, systemPrompt, llmSetting, msgs) => {
    return {
      stream: true,
      ...StringExtension.camelToSnakeObj(params),
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: llmSetting.systemPrompt },
        ...(
          msgs.map((x) => {
            return {
              role: `${x.role === name ? 'assistant' : 'user'}`,
              content: `${x.role === name ? '' : (`${x.role}:`)}${x.content}`,
            };
          })
        ),
        { role: 'user', content: '请根据以上发言规则，分享您的观点或提问。请保持您的回答和提问中立、客观，避免使用带有情感倾向的词汇，确保无感情色彩。' },
      ],
    };
  }

  static formatStreamResp = (e) => {
    const objStr = e.target.response.split('\n\n');
    const arrs = objStr.map((x) => { return _.trimStart(x, '"data: '); });
    const txts = [];
    arrs.forEach((x) => {
      if (x.length && x !== '[DONE]') {
        txts.push(JSON.parse(x)?.choices[0]);
      }
    });

    return _.map(_.map(txts, 'delta'), 'content').join('');
  }

  static formatTemp = (data, chatInfo, groupId, resp) => {
    const msgTmpObj = _.clone(data);
    const item = msgTmpObj[chatInfo.id].find((x) => { return x.groupId === groupId; });
    const newMsgs = item.messages.map((x) => {
      let str = _.trimStart(x.content, 'you:');
      [':', '：'].forEach((m) => { str = _.trimStart(str, `${x.role}${m}`); });
      return { role: x.role, content: str };
    });
    newMsgs[newMsgs.length - 1].content = resp;
    msgTmpObj[chatInfo.id] = msgTmpObj[chatInfo.id].map((mg) => {
      return mg.groupId === groupId ? { ...mg, messages: newMsgs } : mg;
    });

    return msgTmpObj;
  }

  static checkIsAllAgree = async (params) => {
    const { data } = await openai.createCompletion(params, { timeout: 5 * 60 * 1000 });
    let { text } = data?.choices[0];
    text = _.trim(text, ' ');
    text = _.trim(text, '\n');
    return text;
  }

  static formatSSE = (e) => {
    const objStr = _.filter(e.split('\r\n'), (x) => {
      return !_.isEmpty(x) && _.startsWith(x, 'data:');
    });
    const arrs = [];
    objStr.forEach((x) => {
      const s = _.trimStart(x, 'data: ');
      if (s.indexOf('{') >= 0) {
        arrs.push(JSON.parse(s));
      }
    });

    return arrs;
  }

  static formatModalIcon = (str) => {
    if (_.isEmpty(str)) return '';
    let modelName = str;
    if (_.startsWith(str, 'ms')) {
      modelName = _.trim(modelName, 'ms').replace('35', '3.5');
    } else if (_.startsWith(modelName, 'chatglm')) {
      modelName = 'chatglm';
    } else if (_.startsWith(modelName, 'qwen')) {
      modelName = 'qwen';
    } else if (_.startsWith(modelName, 'ernie')) {
      modelName = 'ernie';
    } else if (_.startsWith(modelName, 'llama2')) {
      modelName = 'llama2';
    } else if (_.startsWith(modelName, 'hunyuan')) {
      modelName = 'hunyuan';
    } else if (_.startsWith(modelName, 'xfyun')) {
      modelName = 'xfyun';
    } else if (_.startsWith(modelName, 'Baichuan')) {
      modelName = 'Baichuan';
    } else if (_.startsWith(modelName, 'claude')) {
      modelName = 'Claude';
    } else if (_.startsWith(modelName, 'gemini')) {
      modelName = 'Gemini';
    } else if (_.startsWith(modelName, 'ep')) {
      modelName = 'Doubao';
    } else if (_.startsWith(modelName, 'grok')) {
      modelName = 'Grok';
    } else if (_.startsWith(modelName, 'bot')) {
      modelName = 'Volcengine';
    } else if (_.startsWith(modelName, 'anthropic')) {
      modelName = 'OpenRouter';
    } else if (_.startsWith(modelName, 'deepseek')) {
      modelName = 'Deepseek';
    }

    return _.head(_.chunk(modelName.split('-'), 2)).join('-').replace(/\./g, '-');
  }

  static pairModel = (str) => {
    if (_.startsWith(str, 'gpt') || _.startsWith(str, 'ms')) {
      return 'openai';
    } else if (_.startsWith(str, 'chatglm')) {
      return 'chatglm';
    } else if (_.startsWith(str, 'qwen')) {
      return 'qwen';
    } else if (_.startsWith(str, 'ernie')) {
      return 'ernie';
    } else if (_.startsWith(str, 'llama2')) {
      return 'llama2';
    } else if (_.startsWith(str, 'hunyuan')) {
      return 'hunyuan';
    } else if (_.startsWith(str, 'xfyun')) {
      return 'xfyun';
    } else if (_.startsWith(str, 'Baichuan')) {
      return 'Baichuan';
    } else if (_.startsWith(str, 'claude')) {
      return 'Claude';
    }

    return 'openai';
  }

  static formatSubtype = (str) => {
    if (_.isEmpty(str)) return '';
    const splitKey = str.split('_')?.length === 1 ? '-' : '_';
    return str.split(splitKey);
  }

  static formatExtraParams = (params = {}, toServer = false) => {
    const keys = [];
    _.map(params, (v, k) => {
      if (_.isObject(v)) {
        keys.push(k);
      }
    });
    const originObj = {};
    keys.forEach((k) => { originObj[k] = params[k]; });
    const result = toServer
      ? StringExtension.camelToSnakeObj(params)
      : StringExtension.snakeToCamelObj(params);

    return { ...result, ...originObj };
  }
}
