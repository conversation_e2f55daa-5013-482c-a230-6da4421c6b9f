import { ChatBot } from '~/engine';

const SET_STATE = 'CHAT_WEWORK_BOT/SET_STATE';
const CLEAR_STATE = 'CHAT_WEWORK_BOT/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const fetchFeedbacks = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().chatBotFeedback;
    const searchParams = {
      name: params?.name,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };

    const { items, total } = await ChatBot.fetchChatbotFeedbacks(searchParams);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const addFeedback = (params) => {
  return async (dispatch) => {
    await ChatBot.addChatbotFeedback(params);
    dispatch(fetchFeedbacks());
  };
};

export const importFeedback = (params) => {
  return async (dispatch) => {
    await ChatBot.importChatbotFeedback(params);
    dispatch(fetchFeedbacks());
  };
};

export const updateFeedback = (params) => {
  return async (dispatch) => {
    await ChatBot.updateChatbotFeedback(params);
    dispatch(fetchFeedbacks());
  };
};

export const delFeedback = (id) => {
  return async (dispatch) => {
    await ChatBot.delChatbotFeedback(id);
    dispatch(fetchFeedbacks());
  };
};

export const searchFeedbackEmbedding = (params = {}) => {
  return async () => {
    const result = await ChatBot.searchFeedbackEmbedding(params);
    return result;
  };
};

const _getInitState = () => {
  return {
    total: 0,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    list: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
