/* eslint-disable react/jsx-no-target-blank, max-len, no-prototype-builtins */
import { FilterBar, InputUpload, PaginationTable, Toast } from '~/components';
import { Button, Divider, Form, Input, InputNumber, Modal, Popconfirm, Select, Table, Tag } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

const ROLE_ENUM = {
  user: '用户',
  moderator: '管理',
};
const FEEDBACK_KEYS = {
  question: '问题',
  target: '内容',
  attitude: '态度',
  content: '意见',
};
@connect(
  (state) => {
    return state.chatBotFeedback;
  },
  actions,
)
export default class ChatBotFeedback extends Component {
  static propTypes = {
    list: PropTypes.array,
    total: PropTypes.number,
    pagination: PropTypes.object,
    fetchFeedbacks: PropTypes.func.isRequired,
    addFeedback: PropTypes.func.isRequired,
    delFeedback: PropTypes.func.isRequired,
    importFeedback: PropTypes.func.isRequired,
    updateFeedback: PropTypes.func.isRequired,
    searchFeedbackEmbedding: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    open: false,
    data: {},
    searchVisible: false,
    searchData: {},
    feedbackEmbeddings: [],
  }

  componentDidMount = () => {
    this.props.fetchFeedbacks();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onChangeSearchValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ searchData: { ...this.state.searchData, [key]: value } });
  }

  onSearch = (e) => {
    this.props.fetchFeedbacks(e);
  }

  onHide = () => {
    this.setState({ open: false, data: {} });
  }

  onSearchFeedbackEmbedding = async () => {
    if (_.isEmpty(this.state.searchData?.query)) {
      Toast.show('请输入查询条件', Toast.Type.WARNING);
      return;
    }
    const result = await this.props.searchFeedbackEmbedding(this.state.searchData);
    const feedbackEmbeddings = Object.keys(result).map((key) => {
      const item = result[key];
      return { ...item.data, similarity: item.similarity };
    });

    await this.setState({ feedbackEmbeddings });
  }

  onShowSearchModal = () => {
    this.setState({ searchVisible: true, searchData: { query: '', topK: 20, courseId: '' } });
  }

  onHideSearchModal = () => {
    this.setState({ searchVisible: false, searchData: {}, feedbackEmbeddings: [] });
  }

  onImport = async () => {
    const { fileUrl } = this.state;
    if (_.isEmpty(fileUrl)) {
      Toast.show('请上传!', Toast.Type.WARNING);
    }

    await this.props.importFeedback({ fileUrl });
    this.setState({ imOpen: false, fileUrl: '' });
  }

  onPreview = (text) => {
    Modal.info({
      title: '全文预览',
      width: '40vw',
      content: <Input.TextArea autoSize defaultValue={text} style={{ color: '#000' }} disabled bordered={false} />,
      onOk() { },
    });
  }

  onSubmit = async () => {
    const allKeys = ['attitude', 'content', 'question', 'target', 'userId', 'courseId', 'userRole'];
    const { id, attitude, content, question, target, userId, courseId, userRole, sourceId } = this.state.data;
    if (!_.isEmpty(this.state.flowParams)) {
      allKeys.push('params');
    }

    const data = { attitude, content, question, target, userId, courseId, userRole, sourceId };
    const hasKeyAndValue = (key) => { return data.hasOwnProperty(key) && Boolean(data[key]); };
    if (!allKeys.every(hasKeyAndValue)) {
      Toast.show('请检查参数!', Toast.Type.WARNING);
      return;
    }
    if (_.isUndefined(id)) {
      await this.props.addFeedback({ ...data, sourceId: 0 });
    } else {
      await this.props.updateFeedback({ ...data, id });
    }
    this.onHide();
    Toast.show('操作成功!', Toast.Type.SUCCESS);
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      ..._.map(FEEDBACK_KEYS, (title, key) => {
        return {
          title,
          dataIndex: key,
          key,
          width: '20%',
          render: (txt) => { return txt || '-'; },
        };
      }),
      {
        title: '身份',
        dataIndex: 'userRole',
        key: 'userRole',
        align: 'center',
        render: (role) => { return <Tag color={role === 'user' ? 'blue' : 'green'}>{ROLE_ENUM[role]}</Tag>; },
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (createdAt) => { return moment(createdAt).format('YYYY-MM-DD HH:mm'); },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <a onClick={() => { return this.setState({ open: true, data: row }); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm
                title="是否删除?!"
                onConfirm={() => { return this.props.delFeedback(row.id); }}
              >
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  renderSearchColumns = () => {
    return [
      { title: '内容', dataIndex: 'content', key: 'content', ellipsis: true, width: '50%' },
      {
        title: '角色',
        dataIndex: 'userRole',
        key: 'userRole',
        align: 'center',
        render: (role) => { return <Tag color={role === 'user' ? 'blue' : 'green'}>{ROLE_ENUM[role]}</Tag>; },
      },
      { title: '用户', dataIndex: 'nickname', key: 'nickname', align: 'center' },
      { title: '相似度', dataIndex: 'similarity', key: 'similarity', align: 'center' },
      {
        title: '全文',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return <a onClick={() => { return this.onPreview(row.content); }}>查看</a>;
        },
      },
    ];
  }

  renderSelects = () => {
    return [
      <Input
        style={{ width: 160, marginBottom: 15, marginLeft: 10 }}
        placeholder="TopK"
        onChange={(e) => { return this.onChangeSearchValue(e, 'topK'); }}
      />,
      <Input
        style={{ width: 160, marginBottom: 15, marginLeft: 10 }}
        placeholder="课程ID"
        onChange={(e) => { return this.onChangeSearchValue(e, 'courseId'); }}
      />,
      <Input
        style={{ width: 160, marginBottom: 15, marginLeft: 10 }}
        placeholder="角色"
        onChange={(e) => { return this.onChangeSearchValue(e, 'userRole'); }}
      />,
      <Input
        style={{ width: 160, marginBottom: 15, marginLeft: 10 }}
        placeholder="用户ID"
        onChange={(e) => { return this.onChangeSearchValue(e, 'userId'); }}
      />,
      <InputNumber
        style={{ width: 160, marginBottom: 15, marginLeft: 10 }}
        placeholder="最近X天反馈"
        min={0}
        max={30}
        onChange={(e) => { return this.onChangeSearchValue(e, 'maxDays'); }}
      />,
    ];
  }

  renderSearchModal = () => {
    const { searchVisible, searchData, feedbackEmbeddings } = this.state;
    return (
      <Modal
        title="验证数据"
        visible={searchVisible}
        onCancel={this.onHideSearchModal}
        onOk={this.onHideSearchModal}
        width="60vw"
        footer={null}
      >
        <div className="chat-feedback" style={{ padding: 30, background: '#fff' }}>
          <FilterBar
            canAdd={false}
            placeholder="请输入搜索词"
            searchKeyWords={searchData?.query || ''}
            renderSelects={this.renderSelects}
            onSearch={() => { return this.onSearchFeedbackEmbedding(); }}
            onChange={(e) => { return this.onChangeSearchValue(e, 'query'); }}
          />
          <Table dataSource={feedbackEmbeddings} columns={this.renderSearchColumns()} />
        </div>
      </Modal>
    );
  }

  renderCreateModal = () => {
    const { data, open } = this.state;
    return (
      <Modal title="用户反馈" open={open} onCancel={this.onHide} onOk={this.onSubmit}>
        <Form labelCol={{ span: 4 }}>
          {
            _.map(FEEDBACK_KEYS, (v, key) => {
              return (
                <Form.Item label={v}>
                  <Input.TextArea
                    value={data[key]}
                    onChange={(e) => { return this.onChangeValue(e, key); }}
                  />
                </Form.Item>
              );
            })
          }
          <Form.Item label="课程ID">
            <Input
              value={data?.courseId}
              onChange={(e) => { return this.onChangeValue(e, 'courseId'); }}
            />
          </Form.Item>
          <Form.Item label="身份ID">
            <Input
              value={data?.userId}
              onChange={(e) => { return this.onChangeValue(e, 'userId'); }}
            />
          </Form.Item>
          <Form.Item label="身份">
            <Select value={data?.userRole} onChange={(e) => { return this.onChangeValue(e, 'userRole'); }}>
              {
                _.map(ROLE_ENUM, (v, k) => {
                  return <Select.Option value={k}>{v}</Select.Option>;
                })
              }
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  renderImportModal = () => {
    const { imOpen, fileUrl } = this.state;
    return (
      <Modal
        title="用户反馈-导入"
        open={imOpen}
        onCancel={() => { return this.setState({ imOpen: false, fileUrl: '' }); }}
        onOk={this.onImport}
      >
        <Form>
          <Form.Item label="导入">
            <InputUpload
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              url={fileUrl}
              onChange={(x) => { return this.setState({ fileUrl: x }); }}
            />
          </Form.Item>
          <Form.Item label="模版">
            <a target="_blank" href="https://video-clip.oss-cn-shanghai.aliyuncs.com/fe_data/feedback_template.csv">下载模版</a>
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  render = () => {
    const { total, list, pagination } = this.props;

    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <FilterBar
          canAdd
          shouldShowSearchInput={false}
          renderButtons={() => {
            return [
              <Button onClick={() => { this.setState({ imOpen: true, fileUrl: '' }); }}>导入</Button>,
              <Button style={{ marginLeft: 15 }} onClick={this.onShowSearchModal}>命中测试</Button>,
            ];
          }}
          onAdd={() => { return this.setState({ open: true, data: {} }); }}
        />
        <PaginationTable
          totalDataCount={total}
          dataSource={list}
          pagination={pagination}
          columns={this.renderColumns()}
          onPaginationChange={(e) => { return this.onSearch(e); }}
        />

        {this.state.open && this.renderCreateModal()}
        {this.state.imOpen && this.renderImportModal()}
        {this.state.searchVisible && this.renderSearchModal()}
      </div>
    );
  }
}

export {
  reducer,
};
