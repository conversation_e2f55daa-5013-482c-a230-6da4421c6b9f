/* eslint-disable no-case-declarations */

import { EditOutlined } from '@ant-design/icons';
import { json } from '@codemirror/lang-json';
import CodeMirror from '@uiw/react-codemirror';
import { Toast } from '~/components';
import Configs from '~/consts';
import Engine, { ChatBot, Sessions } from '~/engine';
import { EVENT_TYPE, EVENT_TYPE_ZH } from '~/pages/Playground/Configs';
import Utils from '~/pages/Playground/Utils';
import ReconnectingWebSocket from '~/pages/Playground/WebSocket';
import MessageDrawer from '~/pages/Playground/Workflow/components/MessageDrawer';
import { StringExtension } from '~/plugins';
import { Button, Checkbox, Col, Drawer, Empty, Form, Input, InputNumber, Row, Select } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { PureComponent } from 'react';

export default class RunFlowDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    copyData: PropTypes.array,
    onClose: PropTypes.func,
  }

  state = {
    openMsg: false,
    canSendMsg: true,
    workflow: {},
    data: {
      group: '全部',
      isBeta: true,
    },
    msgObj: {},
    flowData: {},
    messages: [],
    logs: [],
  }

  componentDidMount = async () => {
    const { items: groups } = await ChatBot.fetchChatbotWorkflowGroups(Configs.ALL_PAGE_PARAMS);
    const { items: flows } = await ChatBot.fetchChatbotWorkflows(Configs.ALL_PAGE_PARAMS);
    let shareFlows = await ChatBot.fetchSharedWorkflows({});
    shareFlows = _.values(shareFlows);

    const mergedFlows = _.uniqBy([...flows, ...shareFlows], 'uuid');
    const newFlows = mergedFlows.map(({ uuid, name, content, betaContent, groupId, inputSpec }) => {
      return { uuid, name, content, betaContent, groupId, inputSpec };
    });
    const workflow = { 全部: newFlows };
    groups.forEach((x) => {
      workflow[x.name] = newFlows.filter((y) => { return +y.groupId === +x.id; });
    });

    this.setState({ workflow });
  }

  initWebSocket = (id) => {
    if (this.ws) {
      this.ws.close();
    }

    const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflows/run/${id}`;
    const query = { access_token: Sessions.getToken() };
    this.ws = new ReconnectingWebSocket(`${path}?${qs.stringify(query)}`, [], this.onReceiveMsg);
  }

  formatLogHtml = (style, type, msg) => {
    const typeName = EVENT_TYPE_ZH[type];
    return `<div style="background:#eee;padding:4px 6px;"><b style="${style}">[${typeName}]:</b>
<pre style="white-space: pre-wrap;margin-bottom:0;">${msg}</pre>
</div>\n`;
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const data = _.cloneDeep(this.state.data);
    data[key] = value;
    if (key === 'group') {
      data.flowId = undefined;
    }
    this.setState({ data });
  }

  onChangeFlowData = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const flowData = _.cloneDeep(this.state.flowData);
    flowData[key] = value;
    this.setState({ flowData });
  }

  onSaveCopyData = () => {
    const { selectedCopyValues, pasteType, flowData } = this.state;
    let value = _.cloneDeep(flowData[pasteType]) || '';
    value += (selectedCopyValues || []).join('\n');
    this.onChangeFlowData(value, pasteType);
    this.setState({ openCopy: false, selectedCopyValues: [] });
  }

  onReceiveMsg = (e) => {
    if (e?.data !== 'pong') {
      const originData = JSON.parse(e.data);
      if (originData?.job_id) {
        this.setState({ jobId: originData?.job_id });
      }

      const { type, data } = StringExtension.snakeToCamelObj(originData);
      const nodeIdStr = _.trim(data?.nodeId, ' ');
      if (type === EVENT_TYPE.LLM_STEP_RESPONSE && _.isEmpty(data?.token)) {
        return;
      }

      switch (type) {
        case EVENT_TYPE.EXEC_STEP:
          const createdAt = moment().format('YYYY-MM-DD HH:mm');
          if (['Start', 'Done'].includes(nodeIdStr)) {
            this.setState({ logs: [...this.state.logs, { nodeId: nodeIdStr, message: '', createdAt }] });
          } else {
            const nodeId = _.last(nodeIdStr.split('@'));
            const node = this.state.nodes.find((x) => { return x.id === nodeId; });
            this.setState({
              logs: [
                ...this.state.logs,
                { message: '', nodeId: node?.data?.name || data.nodeName, createdAt },
              ],
            });
          }
          break;
        case EVENT_TYPE.OP_RESULT:
        case EVENT_TYPE.EXEC_FAILED:
        case EVENT_TYPE.TOOL_INPUT:
        case EVENT_TYPE.TOOL_OUTPUT:
        case EVENT_TYPE.LLM_REQUEST:
        case EVENT_TYPE.LLM_TOKEN_USAGE:
          this.llmStepResponse = '';
          this.beforeStepResponse = '';
          if (type === EVENT_TYPE.EXEC_FAILED) {
            this.setState({ jobId: undefined, canSendMsg: true });
          }
          const logs = _.clone(this.state.logs);
          const lastLog = logs.pop();
          const style = type === EVENT_TYPE.EXEC_FAILED ? 'color:red' : '';
          let msg = data?.output || data?.msg || data?.prompt;
          if ([EVENT_TYPE.OP_RESULT].includes(type)) {
            try {
              const msgObj = JSON.parse(msg);
              delete msgObj.raw_content;
              msg = JSON.stringify(msgObj);
            } catch (error) {
              // nothing
            }
          }
          if ([EVENT_TYPE.LLM_TOKEN_USAGE].includes(type)) {
            try { msg = JSON.stringify(JSON.parse(msg), null, 2); } catch (error) { /* nothing*/ }
          }
          lastLog.message += this.formatLogHtml(style, type, msg);
          lastLog.message = _.trimStart(lastLog.message, '\n');
          this.setState({ logs: [...logs, lastLog] });
          break;
        case EVENT_TYPE.LLM_STEP_RESPONSE:
          const stepLogs = _.clone(this.state.logs);
          const lastSetpLog = stepLogs.pop();
          if (!_.isEmpty(lastSetpLog.message) && _.isEmpty(this.llmStepResponse)) {
            this.beforeStepResponse = lastSetpLog.message;
          }

          this.llmStepResponse += data?.token;
          if (!_.isEmpty(this.llmStepResponse)) {
            lastSetpLog.message = this.formatLogHtml(style, type, this.llmStepResponse);
            lastSetpLog.message = this.beforeStepResponse + _.trimStart(lastSetpLog.message, '\n');
            this.setState({ logs: [...stepLogs, lastSetpLog] });
          }
          break;
        case EVENT_TYPE.EXEC_LOG:
          if (data.msg !== 'Workflow run start') {
            const execLogs = _.clone(this.state.logs);
            const lastExecLog = execLogs.pop();
            lastExecLog.message += this.formatLogHtml(style, type, data.msg);
            lastExecLog.message = _.trimStart(lastExecLog.message, '\n');
            this.setState({ logs: [...execLogs, lastExecLog] });
          }
          break;
        case EVENT_TYPE.EXEC_ACTION_REQUIRED:
          this.setState({
            canSendMsg: true,
            messages: [
              ...this.state.messages,
              { message: (data?.output || data?.msg), isClient: true, createdAt: moment().format('YYYY-MM-DD HH:mm') },
            ],
          });
          break;
        case EVENT_TYPE.FINAL_RESULT:
          this.llmStepResponse = '';
          this.beforeStepResponse = '';
          this.setState({
            canSendMsg: true,
            jobId: undefined,
            messages: [
              ...this.state.messages,
              { message: (data?.output || data?.msg), isClient: true, createdAt: moment().format('YYYY-MM-DD HH:mm') },
            ],
          });
          break;
        default:
          break;
      }
    }
  }

  onSendMsg = async (text) => {
    if (!this.state.canSendMsg) {
      Toast.show('当前WorkFlow执行中');
      return;
    }

    this.ws.send(JSON.stringify({
      text,
      type: _.isUndefined(this.state.jobId) ? 'message' : 'action_confirm',
      is_beta: this.state.isBeta,
    }));
    this.outputing = true;
    await this.setState({
      canSendMsg: false,
      messages: [
        ...this.state.messages,
        { message: text, isClient: false, createdAt: moment().format('YYYY-MM-DD HH:mm') },
      ],
    });
  }

  onRun = () => {
    const { group, flowId, isBeta } = this.state.data;
    if (!group || !flowId) {
      Toast.show('请选择工作流', Toast.Type.WARNING);
      return;
    }
    const flow = this.state.workflow[group].find((x) => { return x.uuid === flowId; });
    const { nodes } = JSON.parse(isBeta ? flow.betaContent : flow.content);
    const newNodes = nodes.map((x) => {
      const extraParams = _.cloneDeep(x.data?.llm_setting?.extra_params);
      const data = StringExtension.snakeToCamelObj(x.data);
      if (!_.isUndefined(extraParams)) {
        data.llmSetting.extraParams = Utils.formatExtraParams(extraParams, false);
      }
      return { ...x, name: data?.name, data };
    });
    const flowObj = { name: flow.name, uuid: flow.uuid };
    const msgObj = { flow: flowObj, nodes: newNodes, params: {} };
    this.initWebSocket(flow.uuid);
    this.setState({ msgObj, openMsg: true });

    setTimeout(async () => { await this.onSendMsg(JSON.stringify(this.state.flowData)); }, 300);
  }

  renderCopyDrawer = () => {
    const { copyData } = this.props;
    const { openCopy, selectedCopyValues } = this.state;
    return (
      <Drawer
        title="插入数据"
        open={openCopy}
        width="50vw"
        placement="right"
        onClose={() => { return this.setState({ openCopy: false }); }}
        extra={<Button type="primary" onClick={() => { return this.onSaveCopyData(); }}>确定</Button>}
      >
        <div style={{ height: '70vh', overflow: 'auto' }}>
          {
            _.isEmpty(copyData) ?
              <Empty /> :
              <Checkbox.Group
                value={selectedCopyValues}
                onChange={(e) => { return this.setState({ selectedCopyValues: e }); }}
              >
                <Row>
                  {_.map(copyData, (x) => { return <Col span={24}><Checkbox value={x}>{x}</Checkbox></Col>; })}
                </Row>
              </Checkbox.Group>
          }
        </div>
      </Drawer>
    );
  }

  renderFlowInput = () => {
    const { data, flowData, workflow } = this.state;
    const flow = ((workflow || {})[data.group] || []).find((x) => { return x.uuid === data.flowId; });
    if (_.isUndefined(flow)) return null;
    const { properties } = flow?.inputSpec;
    const contents = [];
    _.map(properties, (v, k) => {
      contents.push(
        <Form.Item label={v.description}>
          <div style={{ position: 'relative' }}>
            {
              v.type === 'string' &&
              <Input.TextArea
                value={flowData[k]}
                autoSize={{ minRows: 4 }}
                onChange={(e) => { this.onChangeFlowData(e, k); }}
              />
            }
            {
              v.type === 'number' &&
              <InputNumber
                value={flowData[k]}
                onChange={(e) => { this.onChangeFlowData(e, k); }}
              />
            }
            {
              v.type === 'array' &&
              <Select
                value={flowData[k]}
                open={false}
                mode="tags"
                onChange={(e) => { this.onChangeFlowData(e, k); }}
              />
            }
            {
              v.type === 'object' &&
              <CodeMirror
                id="code"
                height="10vh"
                extensions={[json()]}
                value={flowData[k]}
                onChange={(e) => { this.onChangeFlowData(e, k); }}
              />
            }
            <Button
              style={{ position: 'absolute', bottom: 4, right: 4 }}
              size="small"
              icon={<EditOutlined />}
              onClick={() => { return this.setState({ openCopy: true, pasteType: k }); }}
              bordered={false}
            />
          </div>
        </Form.Item>,
      );
    });

    return contents;
  }

  render = () => {
    const { data, workflow } = this.state;
    return (
      <Drawer
        maskClosable={false}
        title="执行工作流"
        open={this.props.open}
        width="50vw"
        placement="right"
        onClose={this.props.onClose}
        extra={<Button type="primary" onClick={this.onRun}>运行</Button>}
      >
        <Form labelCol={{ span: 2 }} >
          <Form.Item label="工作流">
            <Select
              value={data?.group}
              style={{ width: '30%' }}
              placeholder="请选择工作流分组"
              onChange={(e) => { this.onChangeValue(e, 'group'); }}
            >
              {_.map(workflow, (v, k) => { return <Select.Option value={k}>{k}</Select.Option>; })}
            </Select>
            <Select
              value={data?.flowId}
              style={{ width: '40%', margin: '0px 10px' }}
              disabled={!data?.group}
              placeholder="请选择工作流"
              onChange={(e) => { this.onChangeValue(e, 'flowId'); }}
            >
              {_.map(workflow[data?.group], (x) => { return <Select.Option value={x.uuid}>{x.name}</Select.Option>; })}
            </Select>
            <Checkbox checked={data?.isBeta} onChange={(e) => { this.onChangeValue(e, 'isBeta'); }}>测试版</Checkbox>
          </Form.Item>
          {this.renderFlowInput()}

        </Form>
        {
          this.state.openMsg &&
          <MessageDrawer
            {...this.state.msgObj}
            open={this.state.openMsg}
            logs={this.state.logs}
            messages={this.state.messages}
            onClose={() => { return this.setState({ openMsg: false }); }}
          />
        }
        {this.state.openCopy && this.renderCopyDrawer()}
      </Drawer>
    );
  }
}
