import { Toast } from '~/components';
import { Prompts } from '~/engine';
import { Platform } from '~/plugins';
import { Button, Drawer, Form, Input, Popconfirm, Radio, Select, Tabs } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class PromptsDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    currentPrompt: PropTypes.string,
    prompts: PropTypes.array,
    onClose: PropTypes.func,
  }

  state = {
    selectId: null,
    data: { prompt: '', public: false },
  }

  componentDidMount = () => {
    this.setState({ data: { content: this.props.currentPrompt, public: false } });
  }

  onChangeValue = (key, value) => {
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onUpdate = async () => {
    const data = { id: this.state.selectId, content: this.props.currentPrompt };
    await Prompts.updateVersion(data);
    Toast.show('更新成功', Toast.Type.SUCCESS);
    Platform.emit(Platform.Event.FETCH_PROMPT);
  }

  onSave = async () => {
    const { data } = this.state;
    if (_.isEmpty(data.name) || _.isEmpty(data.content)) {
      Toast.show('请填写完整', Toast.Type.WARNING);
      return;
    }

    await Prompts.create(data);
    Toast.show('保存成功', Toast.Type.SUCCESS);
    Platform.emit(Platform.Event.FETCH_PROMPT);
  }

  renderDetail = () => {
    const prompt = _.find(this.props.prompts, { id: this.state.selectId });
    if (_.isUndefined(prompt) || _.isNull(prompt)) { return null; }

    return (
      <div>
        <Input.TextArea
          disabled
          value={prompt?.content}
          style={{ color: 'rgba(0, 0, 0, 0.85)' }}
          autoSize={{ minRows: 20, maxRows: 50 }}
        />
        <Button
          style={{ float: 'right', marginTop: 10 }}
          onClick={async () => {
            await navigator.clipboard.writeText(prompt?.content);
            Toast.show('复制成功', Toast.Type.SUCCESS);
          }}
        >
          复制
        </Button>
      </div>
    );
  }

  render = () => {
    return (
      <Drawer
        title="Prompt 库"
        width="50vw"
        placement="right"
        open={this.props.open}
        onClose={this.props.onClose}
      >
        <Tabs>
          <Tabs.TabPane tab="编辑/复制" key="1">
            <div>
              <Form.Item label="Prompt 库" style={{ marginBottom: 10 }}>
                <Select
                  showSearch
                  placeholder="请选择"
                  style={{ width: 200, marginBottom: 15 }}
                  value={this.state.selectId}
                  filterOption={(input, option) => { return option.children.includes(input); }}
                  onChange={(value) => { return this.setState({ selectId: value }); }}
                >
                  {
                    this.props.prompts.map((x) => {
                      return (
                        <Select value={x.id} key={x.id}>{x.name}</Select>
                      );
                    })
                  }
                </Select>

                {
                  !_.isNull(this.state.selectId) &&
                  <>
                    <Popconfirm title="是否将提示词更新至该库" onConfirm={() => { return this.onUpdate(); }}>
                      <a style={{ float: 'right' }}>更新提示词</a>
                    </Popconfirm>
                  </>
                }
              </Form.Item>
            </div>
            {this.renderDetail()}
          </Tabs.TabPane>
          <Tabs.TabPane tab="新增" key="2">
            <Form labelCol={{ span: 4 }} className="common-form">
              <Form.Item label="名称">
                <Input
                  value={this.state.data.name}
                  onChange={(e) => { return this.onChangeValue('name', e.target.value); }}
                />
              </Form.Item>
              <Form.Item label="Prompt">
                <Input.TextArea
                  autoSize={{ minRows: 10, maxRows: 20 }}
                  value={this.state.data.content}
                  onChange={(e) => { return this.onChangeValue('content', e.target.value); }}
                />
              </Form.Item>
              <Form.Item label="公开">
                <Radio.Group
                  value={this.state.data.public}
                  onChange={(e) => { return this.onChangeValue('public', e.target.value); }}
                >
                  <Radio value>是</Radio>
                  <Radio value={false}>否</Radio>
                </Radio.Group>
              </Form.Item>
              <Form.Item colon={false} label={' '}>
                <Button onClick={this.onSave}>保存</Button>
              </Form.Item>
            </Form>
          </Tabs.TabPane>
        </Tabs>
      </Drawer>
    );
  }
}
