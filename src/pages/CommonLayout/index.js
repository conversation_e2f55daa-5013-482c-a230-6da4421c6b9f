import './index.less';

import { SlackOutlined, createFromIconfontCN } from '@ant-design/icons';
import { GlobalHeader } from '~/components';
import Consts from '~/consts';
import { Sessions } from '~/engine';
import { Platform } from '~/plugins';
import { Affix, Breadcrumb, Button, Layout, Menu } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { matchPath } from 'react-router';
import { v4 as uuid } from 'uuid';

import PromptsDrawer from './components/PromptsDrawer';
import RunFlowDrawer from './components/RunFlowDrawer';
import reducer, * as actions from './state';

const { Content, Sider } = Layout;
const { SubMenu } = Menu;

const defaultOpenKeys = Consts.ROUTE.HOMEPAGE;
const defaultSelectedKeys = '';
const IconFont = createFromIconfontCN({ scriptUrl: Consts.ALI_ICON_URL });

@connect(
  (state) => { return state.commonLayout; },
  actions,
)
export default class CommonLayout extends Component {
  static propTypes = {
    location: PropTypes.object,
    collapsed: PropTypes.bool.isRequired,
    partners: PropTypes.array.isRequired,
    copyData: PropTypes.array.isRequired,
    menusData: PropTypes.array.isRequired,
    menuSetting: PropTypes.array.isRequired,
    prompts: PropTypes.array.isRequired,
    children: PropTypes.element.isRequired,
    imageSrc: PropTypes.string.isRequired,
    breadCrumbParams: PropTypes.object.isRequired,
    fetchPartners: PropTypes.func.isRequired,
    switchPartner: PropTypes.func.isRequired,
    fetchAllPrompts: PropTypes.func.isRequired,
    setSiderCollapsed: PropTypes.func.isRequired,
    setBreadCrumbParams: PropTypes.func.isRequired,
    fetchFunctionToolsByGroup: PropTypes.func.isRequired,
    setState: PropTypes.func.isRequired,
  }

  state = {
    open: false,
    logoname: '',
    partnerType: '',
  }

  constructor(props) {
    super(props);
    this.menusData = this.getNavMenuItems();
    this.state = { openKeys: null };
    this.props.fetchFunctionToolsByGroup();
    this.props.fetchAllPrompts();

    Component.prototype.$setBreadCrumbParams = props.setBreadCrumbParams;
    Sessions.addEventListener(Consts.SUB_MENU_CHANGE, this.onUpdateSubMenus);
    Platform.addEventListener(Platform.Event.OPEN_PROMPT_DRAWER, this.updatePrompt);
    Platform.addEventListener(Platform.Event.FETCH_PROMPT, this.props.fetchAllPrompts);
    Platform.addEventListener(Platform.Event.RELOAD_FUNC_TOOLS, this.props.fetchFunctionToolsByGroup);
  }

  componentDidMount = () => {
    this.props.fetchPartners();
    let { location: { pathname } } = this.props;
    const { partnerType } = Sessions.getPartner();
    if (partnerType !== 'playground' && pathname.indexOf(Consts.USER_HOMEPAGE[partnerType]) < 0) {
      this.setState({ pathname: '/playground' });
      this.$replace(Consts.USER_HOMEPAGE[partnerType]);
      return;
    }

    const { location: { query: { name } } } = this.props;
    pathname = `/${_.head(pathname.split('/').filter((i) => { return i; }))}`;
    const menuSetting = Sessions.getMenuSetting();
    if (pathname === '/') {
      pathname = Consts.ROUTE.HOMEPAGE;
    }
    let data = menuSetting.find((x) => { return x.routeKey === pathname; });
    if (_.isUndefined(data)) { // 子菜单
      const index = _.map(menuSetting, 'subMenus').findIndex((x) => {
        return (x || []).findIndex((s) => { return s.routeKey === pathname; }) >= 0;
      });
      if (index >= 0) {
        data = menuSetting[index];
        pathname = menuSetting[index].routeKey;
      }
    }
    this.updateMenuSetting(data?.subMenus || []);
    this.setState({ pathname, logoname: name, partnerType });

    document.body.addEventListener('copy', this.copyData);
  }

  componentWillUnmount = () => {
    Sessions.removeEventListener(Consts.SUB_MENU_CHANGE, this.onUpdateSubMenus);
    Platform.removeEventListener(Platform.Event.OPEN_PROMPT_DRAWER, this.updatePrompt);
    Platform.removeEventListener(Platform.Event.FETCH_PROMPT, this.props.fetchAllPrompts);
    Platform.removeEventListener(Platform.Event.RELOAD_FUNC_TOOLS, this.props.fetchFunctionToolsByGroup);
  }

  copyData = () => {
    const { copyData } = this.props;
    const selection = document.getSelection().toString();
    if (selection && !_.includes(copyData, selection)) {
      copyData.push(selection);
      this.props.setState({ copyData });
    }
  }

  updateMenuSetting = (menuSetting) => {
    const menusData = this.getNavMenuItems(menuSetting || []);
    this.props.setState({ menusData, menuSetting });
  }

  updatePrompt = ({ prompt }) => {
    this.setState({ openPrompt: !this.state.openPrompt, prompt });
  }

  getNavMenuItems = (menusData = [], isSub = false) => {
    const { allowSearchActor } = Sessions.getPartner();
    let menus = menusData;
    if (!allowSearchActor) {
      menus = menusData.filter((i) => { return !i.isBeta; });
    }

    return menus.map((item) => {
      if (!item.name) {
        return null;
      }

      if (item.subMenus && !_.isEmpty(item.subMenus)) {
        return (
          <SubMenu key={uuid()} title={this.renderMenuItemContent(item.icon, item.name)}>
            {this.getNavMenuItems(item.subMenus, true)}
          </SubMenu>
        );
      }

      return (
        <Menu.Item key={item.routeKey}>
          {this.renderMenuItemContent(item.icon, item.name, isSub)}
        </Menu.Item>
      );
    });
  }

  getBreadCrumb = () => {
    const extraBreadcrumbItems = [];
    const routes = this.props.children.props.children.filter((r) => { return r.props.path; });
    let wholeMatch = null;
    for (const route of routes) {
      wholeMatch = matchPath(this.props.location.pathname, route.props);
      if (wholeMatch) {
        break;
      }
    }

    if (!wholeMatch) {
      return extraBreadcrumbItems;
    }

    const pathSnippets = _.tail(wholeMatch.path.split('/'));
    const urlSnippets = _.tail(this.props.location.pathname.split('/'));
    pathSnippets.forEach((item, index) => {
      if (/:.*$/.test(item)) {
        const paramKey = _.trimStart(item, ':');
        const existsCrumbs = this.props.breadCrumbParams[paramKey];
        if (!_.isEmpty(existsCrumbs)) {
          if (_.isArray(existsCrumbs)) {
            extraBreadcrumbItems.push(...existsCrumbs);
          } else {
            extraBreadcrumbItems.push(existsCrumbs);
          }
          return;
        }
      }

      const url = `/${urlSnippets.slice(0, index + 1).join('/')}`;
      let crumbText = this.$i18n(`router.title[${url}]`);
      let match = null;
      for (const route of routes) {
        match = matchPath(url, route.props);
        if (match) {
          break;
        }
      }

      if (match && !crumbText) {
        crumbText = this.$i18n(`router.title[${match.path}]`);
      }

      if (match || crumbText) {
        const breadCrumb = {};
        if (match) {
          breadCrumb.url = url + this.props.location.search;
        }

        if (crumbText) {
          breadCrumb.title = crumbText;
        }

        extraBreadcrumbItems.push(breadCrumb);
      }
    });

    return extraBreadcrumbItems;
  }

  getSelectedKeys = () => {
    const { location: { pathname } } = this.props;
    return [pathname];
  }

  onCollapse = () => {
    this.props.setSiderCollapsed(!this.props.collapsed);
  }

  onOpenChange = (openKeys) => {
    if (_.isEmpty(openKeys) || openKeys.length === 1) {
      this.setState({ openKeys });
    } else {
      const lastOpenKey = _.takeRight(openKeys);
      this.setState({ openKeys: lastOpenKey });
    }
  }

  onSelectMenu = (item) => {
    this.$replace(item.key);
  }

  onUpdateSubMenus = (pathname) => {
    const menuSetting = Sessions.getMenuSetting();
    const data = menuSetting.find((x) => { return x.routeKey === pathname; });
    this.updateMenuSetting(data?.subMenus || []);
    this.setState({ pathname });
  }

  renderBreadCrumb = () => {
    const breadCrumbs = this.getBreadCrumb();
    return breadCrumbs.map((breadCrumb, index) => {
      const params = {};
      if (breadCrumb.url && index < breadCrumbs.length - 1) {
        params.href = `#${breadCrumb.url}`;
      }

      return (
        <Breadcrumb.Item {...params} key={breadCrumb.url || index}>
          {breadCrumb.title}
        </Breadcrumb.Item>
      );
    });
  }

  renderMenuItemContent = (iconType, title, isSub = false) => {
    return [
      <IconFont type={iconType} style={{ fontSize: 24, verticalAlign: isSub ? 'middle' : 'bottom' }} />,
      <input type="button" value={title} key="2" />,
    ];
  }

  renderSider = () => {
    if (_.isEmpty(this.props.menuSetting)) return <div className="bzy-sider" />;

    const pathSnippets = this.props.location.pathname.split('/').filter((i) => { return i; });
    const openKeys = `/${pathSnippets[0]}` || defaultOpenKeys;
    const selectedKeys = this.props.location.pathname || defaultSelectedKeys;
    const menuProps = this.props.collapsed ? {} : { openKeys: this.state.openKeys || [openKeys] };

    return (
      <Sider
        collapsible
        className="bzy-sider"
        width={Consts.sideMenuWidth}
        trigger={null}
        breakpoint="lg"
        onCollapse={this.onCollapse}
        style={{ display: _.isEmpty(this.props.menuSetting) ? 'none' : 'unset' }}
      >
        <Menu
          theme="dark"
          mode="inline"
          {...menuProps}
          defaultOpenKeys={[openKeys]}
          defaultSelectedKeys={[selectedKeys]}
          selectedKeys={this.getSelectedKeys()}
          onSelect={this.onSelectMenu}
          onOpenChange={this.onOpenChange}
        >
          {this.props.menusData}
        </Menu>
      </Sider>
    );
  }

  render = () => {
    const { logoname } = this.state;
    return (
      <Layout className="common-layout">
        <Layout style={{ flexDirection: 'inherit', height: '100vh' }}>
          {
            !Platform.isMai() &&
            <GlobalHeader
              partners={this.props.partners}
              pathname={this.state.pathname}
              collapsed={this.props.collapsed}
              switchPartner={this.props.switchPartner}
              menuSetting={Sessions.getMenuSetting()}
              onToggle={this.onCollapse}
              onUpdateSubMenus={this.onUpdateSubMenus}
              logoname={logoname}
            />
          }
          <div style={{ display: 'flex', height: '100%' }}>
            {(!logoname && !Platform.isMai()) && this.renderSider()}
            <Content className="page-container" ref={(el) => { this.refPage = el; }}>
              {this.props.children}
            </Content>
          </div>
        </Layout>
        {
          (!Platform.isMai() && this.state.partnerType === 'playground') &&
          <Affix style={{ position: 'absolute', bottom: 80, right: 20 }}>
            <Button
              onClick={() => { return this.setState({ open: true }); }}
              icon={<SlackOutlined style={{ fontSize: 24 }} />}
              type="primary"
              shape="circle"
              size="large"
            />
          </Affix>
        }
        {
          this.state.open &&
          <RunFlowDrawer
            open={this.state.open}
            copyData={this.props.copyData}
            onClose={() => { return this.setState({ open: false }); }}
          />
        }

        {
          this.state.openPrompt &&
          <PromptsDrawer
            open={this.state.openPrompt}
            prompts={this.props.prompts}
            currentPrompt={this.state.prompt}
            onClose={() => { return this.setState({ openPrompt: false }); }}
          />
        }
      </Layout>
    );
  }
}

export {
  reducer,
};
