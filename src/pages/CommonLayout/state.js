import Configs from '~/consts';
import { Accounts, ChatBot, Prompts, Sessions, WeworkKF, Workflow } from '~/engine';
import { Platform } from '~/plugins';
import _ from 'lodash';

const SET_STATE = 'commonLayout/SET_STATE';
const SET_SIDER_COLLAPSED = 'commonLayout/SET_SIDER_COLLAPSED';
const SET_BREAD_CRUMB_PARAMS = 'commonLayout/SET_BREAD_CRUMB_PARAMS';
const ImageSrc = {
  NORMAL: '/static/logo.png',
  BIG: '/static/logoBig.png',
};
const toolMap = {
  system: '',
  workflow: 'wf',
  knwoledge_library: 'kl',
  wework_invitation: 'wi',
  coze: 'coze',
  course: 'course',
  plugin: 'plugin',
};
export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const switchPartner = (params) => {
  return async () => {
    const { token } = await Accounts.switchPartner(params);
    window.parent.postMessage({ token }, '*');
    Sessions.login({ token, isTokenLogin: true }, true);
    const { quotas, partners, ...profile } = await Accounts.getMemberInfo();
    Sessions.setProfile(profile);
    Sessions.setModels(quotas);
    Sessions.postLogin(profile?.permissions);
    let route = Configs.ROUTE.HOMEPAGE;
    if (profile?.partnerType !== 'playground') {
      route = Configs.USER_HOMEPAGE[profile?.partnerType];
    }
    window.location.replace(window.location.origin + route);
  };
};

export const fetchPartners = () => {
  return async (dispatch) => {
    const partners = await Accounts.fetchPartners();
    dispatch(setState({ partners: _.isEmpty(partners) ? [] : partners }));
  };
};

export const setSiderCollapsed = (collapsed) => {
  const imageSrc = collapsed ? ImageSrc.NORMAL : ImageSrc.BIG;
  Platform.emit(Platform.Event.SIDE_MENU_COLLAPSED, collapsed);

  return { type: SET_SIDER_COLLAPSED, payload: { collapsed, imageSrc } };
};

export const setBreadCrumbParams = (params) => {
  return { type: SET_BREAD_CRUMB_PARAMS, payload: params };
};

export const fetchFuncTools = () => {
  return async (dispatch) => {
    const funcs = await Accounts.fetchFuncTools({});
    const fullFuncs = {};
    const sysFuncs = {};
    _.map(funcs, (v) => {
      sysFuncs[v.name] = v.displayName;
      fullFuncs[v.name] = v;
    });
    const { items } = await ChatBot.fetchWorkflowFuncs(Configs.ALL_PAGE_PARAMS);
    const userFuncs = {};
    _.map(items, (v) => {
      userFuncs[`wf@${v.name}`] = v.displayName;
      fullFuncs[`wf@${v.name}`] = { ...v, name: `wf@${v.name}` };
    });
    const klResult = await ChatBot.fetchKnowledgeLibraryFuncs(Configs.ALL_PAGE_PARAMS);
    const klFuncs = {};
    _.map(klResult.items, (v) => {
      klFuncs[`kl@${v.name}`] = v.displayName;
      fullFuncs[`kl@${v.name}`] = { ...v, name: `kl@${v.name}` };
    });
    const wiResult = await WeworkKF.fetchActivityFuncs(Configs.ALL_PAGE_PARAMS);
    const wiFuncs = {};
    _.map(wiResult.items, (v) => {
      wiFuncs[`wi@${v.name}`] = v.displayName;
      fullFuncs[`wi@${v.name}`] = { ...v, name: `wi@${v.name}` };
    });
    const czResult = await ChatBot.fetchCozeFuncs(Configs.ALL_PAGE_PARAMS);
    const czFuncs = {};
    _.map(_.values(czResult), (v) => {
      czFuncs[`coze@${v.name}`] = v.displayName;
      fullFuncs[`coze@${v.name}`] = { ...v, name: `coze@${v.name}` };
    });
    const courseResult = await ChatBot.fetchCourseFuncs(Configs.ALL_PAGE_PARAMS);
    const courseFuncs = {};
    _.map(courseResult.items, (v) => {
      courseFuncs[`course@${v.name}`] = v.displayName;
      fullFuncs[`course@${v.name}`] = { ...v, name: `course@${v.name}` };
    });
    const pluginResult = await Workflow.fetchPluginTools();
    const pluginFuncs = {};
    _.map(pluginResult, (v) => {
      pluginFuncs[`plugin@${v.name}`] = v.displayName;
      fullFuncs[`plugin@${v.name}`] = { ...v, name: `plugin@${v.name}` };
    });
    dispatch(setState({
      fullFuncs,
      globalFuncs: [sysFuncs, userFuncs, klFuncs, wiFuncs, czFuncs, courseFuncs, pluginFuncs],
    }));
  };
};

export const fetchFunctionToolsByGroup = () => {
  return async (dispatch) => {
    const datas = await ChatBot.fetchFunctionToolsByGroup();
    const obj = {};
    _.values(datas).forEach((x) => { obj[x.key] = x; });
    const fullFuncs = {};
    const globalFuncs = [];
    _.map(toolMap, (v, k) => {
      const subFuncs = {};
      _.map(obj[k].tools, (t) => {
        const subKey = v?.length > 0 ? `${v}@${t.name}` : `${t.name}`;
        fullFuncs[subKey] = { ...t, name: subKey };
        subFuncs[subKey] = t.displayName;
      });
      globalFuncs.push(subFuncs);
    });

    const globalApiFuncs = {};
    _.map((obj.api_actions || {}).tools, (v) => { globalApiFuncs[v.id] = v.title; });
    dispatch(setState({ fullFuncs, globalFuncs, globalApiFuncs }));
  };
};

export const fetchOpenApiFuncs = () => {
  return async (dispatch) => {
    const { items } = await ChatBot.fetchOpenApiFuncs(Configs.ALL_PAGE_PARAMS);
    const openApiFuncs = {};
    _.map(items, (v) => { openApiFuncs[v.id] = v.title; });
    dispatch(setState({ globalApiFuncs: openApiFuncs }));
  };
};

export const fetchAllPrompts = () => {
  return async (dispatch) => {
    const { items } = await Prompts.fetchAll(Configs.ALL_PAGE_PARAMS);
    dispatch(setState({ prompts: items }));
  };
};

const _getInitState = () => {
  return {
    collapsed: false,
    menusData: [],
    menuSetting: [],
    partners: [],
    copyData: [],
    prompts: [],
    imageSrc: ImageSrc.BIG,
    breadCrumbParams: { activityId: '裂变名' },
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
    case SET_SIDER_COLLAPSED:
      return { ...state, ...action.payload };
    case SET_BREAD_CRUMB_PARAMS:
      return { ...state, breadCrumbParams: action.payload };
    default:
      return state;
  }
};
