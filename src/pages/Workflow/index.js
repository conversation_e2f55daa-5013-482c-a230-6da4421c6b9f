/* eslint-disable camelcase */
import './index.less';

import { FunctionOutlined, MessageOutlined, SaveOutlined, VerticalAlignTopOutlined } from '@ant-design/icons';
import { IconFont, Toast } from '~/components';
import { Platform, StringExtension } from '~/plugins';
import { <PERSON><PERSON>, Popconfirm, Tooltip } from 'antd';
import md5 from 'blueimp-md5';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import ReactFlow, { Background, Controls, ReactFlowProvider } from 'reactflow';

import { CloseEdge } from '../Playground/Workflow/components/CustomEdges';
import ExtraSidebar from './components/ExtraSidebar';
import FlowName from './components/FlowName';
import FunctionDrawer from './components/FunctionDrawer';
import MessageDrawer from './components/MessageDrawer/';
import {
  Code,
  Condition,
  Default,
  End,
  Plugin,
  SearchKb,
  Start,
  Subflow,
  TextFormat,
  Variable,
} from './components/Nodes';
import SettingDrawer from './components/Settings';
import BotConf from './Configs';
import reducer, * as actions from './state';
import Utils from './Utils';

const NodeTypes = {
  end: End,
  start: Start,
  bot: Default,
  plugin: Plugin,
  wework_sender: Plugin,
  switch_case: Condition,
  variable: Variable,
  kb: SearchKb,
  code: Code,
  sub_workflow: Subflow,
  text_combiner: TextFormat,
  text_splitter: TextFormat,
};

@connect(
  (state) => {
    return {
      ...state.workflowv2,
      fullFuncs: state.commonLayout.fullFuncs,
      globalFuncs: state.commonLayout.globalFuncs,
      globalApiFuncs: state.commonLayout.globalApiFuncs,
    };
  },
  actions,
)
export default class Workflow extends Component {
  static propTypes = {
    subflows: PropTypes.array,
    assistants: PropTypes.array,
    innerParams: PropTypes.array,
    typeMap: PropTypes.object,
    nodeTypes: PropTypes.object,
    fullFuncs: PropTypes.object,
    globalFuncs: PropTypes.object,
    globalApiFuncs: PropTypes.object,
    fetchAssistants: PropTypes.func.isRequired,
    fetchLibraries: PropTypes.func.isRequired,
    fetchWorkflow: PropTypes.func.isRequired,
    updateWorkflow: PropTypes.func.isRequired,
    cancelWorkflow: PropTypes.func.isRequired,
    fetchNodes: PropTypes.func.isRequired,
    fetchRunLogs: PropTypes.func.isRequired,
    fetchConversations: PropTypes.func.isRequired,
    fetchInnerParams: PropTypes.func.isRequired,
    createConversation: PropTypes.func.isRequired,
    updateConversation: PropTypes.func.isRequired,
    deleteConversation: PropTypes.func.isRequired,
    publishWorkflow: PropTypes.func.isRequired,
    fetchWorkflows: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
    match: PropTypes.object.isRequired,
  }

  state = {
    nodes: [],
    edges: [],
    libraries: [],
    isBeta: true,
    isPublished: true,
    reactFlowId: 'reactflow',
    initialHash: '',
    pubVersion: '',
  }

  constructor(props) {
    super(props);
    this.EdgeTypes = {
      default: (e) => { return <CloseEdge {...e} onDel={this.onDelEdge} />; },
    };
  }

  componentDidMount = async () => {
    const { id } = this.props.match.params;
    await this.props.fetchNodes();
    await this.props.fetchWorkflows(id);
    await this.props.fetchInnerParams();
    const libraries = await this.props.fetchLibraries();
    await this.setState({ libraries });
    window.PLUGINS = (this.props?.nodeTypes?.plugin || []);
    window.CODE_INTERPRETER = this.props.nodeTypes.code_interpreter;

    await this.initFlow(id);
    await this.props.fetchAssistants();
    this.onFulScreen();

    Platform.addEventListener('ADD_NODE', this.onAddNode);
    Platform.addEventListener('DROP_NODE', this.onDrop);
    Platform.addEventListener('UPDATE_EDGE_PATH', this.updateEdgePath);
  }

  componentWillUnmount = () => {
    this.props.clearState();
    Platform.removeEventListener('ADD_NODE', this.onAddNode);
    Platform.removeEventListener('DROP_NODE', this.onDrop);
    Platform.removeEventListener('UPDATE_EDGE_PATH', this.updateEdgePath);
    window.PLUGINS = null;
    window.CODE_INTERPRETER = null;
  }

  // 生成流程图数据的MD5哈希
  generateFlowHash = (nodes, edges) => {
    // 创建一个简化的数据结构，只保留关键信息
    const simpleNodes = (nodes || []).map((node) => {
      return {
        id: node.id,
        type: node.type,
        position: node.position,
        data: _.omit(node.data, ['onChange', 'onDel', 'onShowSetting', 'quoteSchemas']),
      };
    });

    const simpleEdges = (edges || []).map((edge) => {
      return {
        id: edge.id,
        source: edge.source,
        target: edge.target,
        sourceHandle: edge.sourceHandle,
        targetHandle: edge.targetHandle,
      };
    });

    // 生成JSON字符串并计算MD5
    const dataStr = JSON.stringify({ nodes: simpleNodes, edges: simpleEdges });
    return md5(dataStr);
  }

  initFlow = async (id, workflow) => { // eslint-disable-next-line
    const { name, uuid, type, isPublished, pubVersion, content, betaContent, successHook, failureHook, editable } =
      workflow || await this.props.fetchWorkflow(id);
    const contentStr = this.state.isBeta ? betaContent : content;
    let { edges, nodes, params, history_mode } = JSON.parse(contentStr); // eslint-disable-line

    nodes = nodes.map((x) => {
      delete x.style; // eslint-disable-line
      return {
        ...x,
        data: {
          ...StringExtension.snakeToCamelObj(x.data),
          name: x.name,
          type: x.type,
          onChange: this.onChangeNodeValue,
          onDel: this.onDelNode,
          onShowSetting: this.onShowSetting,
        },
      };
    });
    const newNodes = this.formatQuoteSchemas(nodes, edges);
    const newEdges = (edges || []).map((x) => { return StringExtension.snakeToCamelObj(x); });

    // 计算初始MD5哈希值
    const initialHash = this.generateFlowHash(newNodes, newEdges);

    await this.setState({
      isPublished,
      editable: true, // editable || false,
      params: params || {},
      historyMode: history_mode || 'autofit', // eslint-disable-line
      nodes: newNodes || [],
      edges: newEdges || [],
      flow: { name, uuid, type },
      hookObj: { successHook, failureHook },
      initialHash,
      pubVersion,
    });
  }

  calculateVariables = (nodes = []) => {
    let variables = [];
    (nodes || []).filter((x) => { return x.type === 'variable'; }).forEach((x) => {
      variables = variables.concat(x.data.configs.variables);
    });

    const vPrarms = variables.map((x) => {
      return { label: x.name, value: x.name, type: x.type };
    });
    return [...this.props.innerParams, ...vPrarms];
  }

  formatQuoteSchemas = (nodes, edges) => {
    const nodeNameMap = {};
    nodes.forEach((x) => { nodeNameMap[x.id] = x.data?.displayName || x.displayName; });
    const innerParams = this.calculateVariables(nodes);
    const nodeSchemas = this.calculateNodePredecessors(nodes, edges);
    const newNodes = nodes.map((x) => {
      const quoteSchemas = [];
      _.map(nodeSchemas[x.id], (v, k) => {
        if (!_.isUndefined(v)) {
          let globalArrs = [];
          let children = Utils.schemaToObject(v);
          children = children.map((child) => {
            if (child.type === 'array') { // 设置: 可以选择 到数组 / 对象
              globalArrs.push({ value: child.value, label: `${child.label}[]` });
              child.value = `${child.value}[0]`; // eslint-disable-line
            }
            // if (child.type === 'object') { // 设置: 可以选择 到数组 / 对象
            //   globalArrs.push({ value: `${child.value}{}`, label: `${child.label}{}` });
            // }
            return child;
          });
          children = [...globalArrs, ...children];
          globalArrs = [];
          quoteSchemas.push({ value: k, type: k, label: nodeNameMap[k] || k, children });
        }
      });

      if (x.type === 'kb') {
        x.data.libraries = this.state.libraries; // eslint-disable-line
      }
      if (x.type !== 'start') {
        quoteSchemas.push({ value: '__inner__', type: '__inner__', label: '变量', children: innerParams });
      }
      return { ...x, data: { ...x.data, quoteSchemas } };
    });
    return newNodes;
  }

  calculateNodePredecessors = (nodes, edges) => {
    const nodePredecessors = {};
    const nodeSchemas = {};

    const dfs = (nodeId, visited, predecessors) => {
      if (!visited.has(nodeId)) {
        visited.add(nodeId);
        const directPredecessors = edges.filter((edge) => {
          return edge.target === nodeId;
        }).map((edge) => { return edge.source; });
        directPredecessors.forEach((predecessor) => {
          predecessors.add(predecessor);
          dfs(predecessor, visited, predecessors);
        });
      }
    };

    nodes.forEach((node) => {
      const predecessors = new Set();
      dfs(node.id, new Set(), predecessors);
      nodePredecessors[node.id] = Array.from(predecessors);
    });
    nodes.forEach((node) => {
      nodeSchemas[node.id] = {};
      nodePredecessors[node.id].forEach((predecessorId) => {
        const predecessorNode = nodes.find((n) => { return n.id === predecessorId; });
        let { outputSchema } = predecessorNode.data;
        if (predecessorNode.data?.batch?.batchEnable) { // 批处理 构造外层 outputs
          outputSchema = {
            outputs: {
              default: [],
              description: '',
              itemProperties: outputSchema,
              itemType: 'object',
              required: false,
              type: 'array',
            },
          };
        }

        if (predecessorNode.data.type !== 'variable') {
          nodeSchemas[node.id][predecessorId] = outputSchema;
        }
      });
    });
    return nodeSchemas;
  }

  updateEdgePath = async () => {
    await this.setState({ reactFlowId: StringExtension.randomString(6) });
  }

  onSwitchVersion = async () => {
    const { id } = this.props.match.params;
    const data = await this.props.fetchWorkflow(id);
    const key = this.state.isBeta ? 'content' : 'betaContent';
    if (_.isEmpty(JSON.parse(data[key]))) {
      Toast.show('请先发布!', Toast.Type.WARNING);
      return;
    }

    await this.setState({ isBeta: !this.state.isBeta });
    await this.initFlow(id, data);
  }

  onPublish = async () => {
    await this.onSave();
    const { pubVersion } = await this.props.publishWorkflow(this.props.match.params.id);
    this.setState({ isPublished: true, pubVersion });
    Toast.show('发布成功!', Toast.Type.SUCCESS);
  }

  onFulScreen = (isHidden = true) => {
    const domChat = document.getElementsByClassName('chat-container')[0] || {};
    const domSider = document.getElementsByClassName('bzy-sider')[0] || {};
    (domChat.style || {}).height = isHidden ? 'calc(100vh - 50px)' : 'calc(100vh - 120px)';
    (domSider.style || {}).display = isHidden ? 'none' : 'unset';
  }

  onConnect = async (e) => {
    const { source, target, sourceHandle } = e;
    const edges = [...this.state.edges];
    let newNodes = [...this.state.nodes];
    newNodes = newNodes.map((x) => {
      if (source === x.id && !_.isNull(sourceHandle)) {
        const idx = +_.last(sourceHandle.split('_')) - 1;
        if (!_.isNaN(idx)) {
          x.data.configs.events[idx].output = e.sourceHandle; // eslint-disable-line
          x.data.configs.events[idx].target = e.target; // eslint-disable-line
        } else {
          const nIdx = x.data.configs.events.findIndex((n) => { return n.output === sourceHandle; });
          if (nIdx !== -1) {
            x.data.configs.events[nIdx].target = target; // eslint-disable-line
          } else {
            x.data.configs.events.push({ output: sourceHandle, target, config: { conditions: {} } });
          }
        }
      }
      return x;
    });

    const id = sourceHandle ? `edge-${source}-${sourceHandle}-${target}` : `edge-${source}-${target}`;
    edges.push({ id, ...e, type: 'step', style: { strokeWidth: 2 } });
    const nodes = this.formatQuoteSchemas(newNodes, edges);
    this.setState({ edges, nodes });
  }

  onChangeNodeValue = (id, data) => {
    let nodes = this.state.nodes.map((x) => { return x.id === id ? { ...x, data } : x; });
    nodes = this.formatQuoteSchemas(nodes, this.state.edges);
    this.setState({ nodes });
  }

  onChangeNodes = async (e) => {
    const { id, position, dimensions } = e || {};
    if (!_.isUndefined(position)) {
      const nodes = this.state.nodes.map((x) => { return x.id === id ? { ...x, position } : x; });
      await this.setState({ nodes });
    }

    if (!_.isUndefined(dimensions)) {
      const nodes = this.state.nodes.map((x) => {
        if (x.id === id) {
          return {
            ...x,
            data: { ...(x.data || {}), ...(dimensions || {}) },
            style: { ...(x.style || {}), ...(dimensions || {}) },
          };
        }
        return x;
      });
      await this.setState({ nodes });
    }
  }

  onNodeDrag = (node) => {
    const centerX = node.position.x + node.width / 2;
    const centerY = node.position.y + node.height / 2;
    const targetNode = this.state.nodes.find((n) => {
      return centerX > n.position.x &&
        centerX < (n.position.x + n.data.width) &&
        centerY > n.position.y &&
        centerY < (n.position.y + n.data.height) &&
        n.id !== node.id;
    });
    this.setState({ targetNode });
  }

  onNodeDragStop = (node) => {
    if (_.isUndefined(this.state.targetNode)) return;
    const { id, position, type } = this.state.targetNode;
    if (type !== 'group') return;

    const nodes = this.state.nodes.map((n) => {
      if (n.id === node.id) {
        return {
          ...n,
          position: { x: node.position.x - position.x, y: node.position.y - position.y },
          parentNode: id,
          zIndex: (n?.zIndex || 0) + 10,
          extent: 'parent',
        };
      }
      return n;
    });
    this.setState({ nodes });
  }

  onShowSetting = async (nodeId) => {
    const info = this.state.nodes.find((x) => { return x.id === nodeId; });
    const libraries = await this.props.fetchLibraries();
    this.setState({ openSetting: true, setting: { ...info, libraries } });
  }

  onDelNode = (nodeId) => {
    const edges = this.state.edges.filter((x) => { return !_.includes(x.id, nodeId); });
    let nodes = this.state.nodes.filter((x) => { return x.id !== nodeId; });
    nodes = (nodes || []).map((x) => {
      if (!_.isEmpty(x.data.inputs)) {
        const inputs = x.data.inputs.filter((s) => { return !_.includes(s.fixedStr, nodeId); });
        return { ...x, data: { ...x.data, inputs } };
      }
      return x;
    });
    this.setState({ nodes, edges });
  }

  onDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  }

  onDrop = (e) => {
    let nodeObj = e;
    let position = { x: 100, y: 100 };
    if (!_.isUndefined(e.preventDefault)) {
      e.preventDefault();
      const reactFlowBounds = this.reactFlowWrapper.getBoundingClientRect();
      const objStr = e.dataTransfer.getData('application/reactflowv2');
      if (typeof objStr === 'undefined' || !objStr) return;
      try { nodeObj = JSON.parse(objStr); } catch { return; }
      position = this.reactFlowInstance.project({
        x: e.clientX - reactFlowBounds.left - 200,
        y: e.clientY - reactFlowBounds.top,
      });
    }
    const { libraries } = this.state;
    const optFuncObj = { onChange: this.onChangeNodeValue, onDel: this.onDelNode, onShowSetting: this.onShowSetting };
    const node = {
      ...nodeObj,
      position,
      type: nodeObj?.type,
      name: this.props.typeMap[nodeObj?.type],
      id: `${nodeObj?.type}_${StringExtension.randomString(3)}`,
      data: { ...nodeObj, ...optFuncObj },
    };

    if (nodeObj?.type === 'kb') {
      node.data.libraries = libraries;
    }
    this.setState({ nodes: [...this.state.nodes, node] });
  }

  onAddNode = (nodeObj) => {
    const optFuncObj = { onChange: this.onChangeNodeValue, onDel: this.onDelNode, onShowSetting: this.onShowSetting };
    const position = { x: 100, y: 100 };
    const displayName = `${nodeObj?.displayName || nodeObj.name || nodeObj.nodeName}`;
    let node = { position, id: nodeObj?.type, name: nodeObj.name, type: nodeObj?.type, displayName };
    console.log(nodeObj); // eslint-disable-line
    let configs = nodeObj?.configs || {};
    if (nodeObj?.type === 'bot') {
      configs = {
        otherConfig: { assistantId: nodeObj?.id },
        conversationSettings: { inputs: [], backgroundInputs: [] },
        llmSetting: nodeObj?.llmSetting,
      };
      optFuncObj.outputSchema = BotConf.BOT_DEFAULT_OUTPUT.schema;
      optFuncObj.outputs = BotConf.BOT_DEFAULT_OUTPUT.arrs;
    }
    node = {
      ...node,
      type: nodeObj?.type,
      id: `${nodeObj?.type}_${StringExtension.randomString(3)}`,
      data: { ...nodeObj, ...optFuncObj, configs, displayName, assistantId: nodeObj?.id },
    };
    console.log('add.node', node); // eslint-disable-line
    this.setState({ nodes: [...this.state.nodes, node] });
  }

  onDelEdge = async (id) => {
    const n = this.state.edges.find((x) => { return x.id === id; });
    const edges = this.state.edges.filter((x) => { return x.id !== n.id; });
    await this.setState({ edges });
  }

  onSaveSetting = async (node) => {
    const originNode = this.state.nodes.find((x) => { return x.id === node.id; });
    let nodes = this.state.nodes.map((x) => { return x.id === node.id ? node : x; });
    if (!_.isEqual(originNode?.data?.name, node?.data?.name)) {
      const selectEdges = this.state.edges.filter((x) => { return x.source === node.id; });
      const nodeIds = _.map(selectEdges, 'target');
      nodes = nodes.map((x) => {
        if (nodeIds.includes(x.id)) {
          const inputs = x?.data?.inputs.map((i) => {
            if (_.startsWith(i.fixedStr, node.id)) {
              const showStr = node.data.name;
              return { ...i, showStr };
            }
            return i;
          });
          return { ...x, data: { ...x.data, inputs } };
        }
        x.name = x?.data?.name || x.name; // eslint-disable-line
        return x;
      });
    }
    await this.setState({ nodes, openSetting: false, setting: {} });
  }

  onSaveParams = async (params, type = 'params') => {
    await this.setState({ [type]: params });
    await this.onSave(false);
  }

  onSave = async () => {
    const { nodeTypes, typeMap } = this.props;
    const noCustomOutputTypes = _.map(_.filter(nodeTypes, (x) => { return !x.needCustomOutput; }), 'type');
    const customInputTypes = _.map(_.filter(nodeTypes, (x) => {
      return x.needCustomInput && x.type !== 'start';
    }), 'type');
    const { nodes, edges, params, historyMode, hookObj, flow } = this.state;
    const newEdges = edges.map((x) => { return StringExtension.camelToSnakeObj(x); });
    const newNodes = _.cloneDeep(nodes).map((x) => {
      ['messages', 'libraries'].forEach((key) => { delete x[key]; }); // eslint-disable-line
      ['onDel', 'onShowSetting', 'onChange'].forEach((key) => { return delete x.data[key]; }); // eslint-disable-line
      const configs = StringExtension.camelToSnakeObj(x.data.configs, {}) || {};
      const flowObj = _.cloneDeep(x.data?.flowData);
      const data = StringExtension.camelToSnakeObj(x.data, {});
      if (!_.isUndefined(flowObj)) {
        data.flow_data = flowObj;
      }

      const isPluginCustomInput = (data.type === 'plugin' && data.need_custom_input);
      if (customInputTypes.includes(data.type) || isPluginCustomInput) {
        const input_schema = {};
        const output_schema = {};
        const quoteSchema = Utils.getQuoteSchemas(data);
        const newInputs = Utils.fillInputTypes((data.inputs || []), quoteSchema);
        (newInputs || []).forEach((xi) => {
          const schemaDefaultValue = xi.fieldType === 'bool' ? false : '';
          if (_.isUndefined(xi.fieldType)) {
            const label = Utils.findValueByPath(data?.quote_schemas, xi.value);
            if (_.endsWith(label, '[]')) {
              xi.fieldType = 'array'; // eslint-disable-line
            }
          }
          input_schema[xi.name] = {
            type: xi.fieldType || 'str', required: false, default: schemaDefaultValue, description: '',
          };
        });
        data.input_schema = input_schema;
        (data?.outputs || []).forEach((xo) => {
          output_schema[xo.name] = { type: 'str', required: false, default: '', description: '' };
        });

        if (_.isEmpty(data.output_schema) || _.isUndefined(data.output_schema) || ['end'].includes(data.type)) {
          data.output_schema = input_schema;
        }
      }
      delete x.data.quote_schemas; // eslint-disable-line
      if (noCustomOutputTypes.includes(data.type)) {
        let { outputSchema } = nodeTypes[data.type] || {};
        if (_.isUndefined(outputSchema)) {
          outputSchema = _.find(nodeTypes, (nx) => { return nx.type === data.type; })?.outputSchema;
        }
        data.output_schema = outputSchema;
      }

      if (data.type === 'end' && data?.configs?.use_tpl) {
        data.output_schema = { output: { default: '', description: '', required: false, type: 'str' } };
      }
      return { ...x, name: typeMap[data.type] || data.name, data: { ...data, configs } };
    });
    const contentObj = { nodes: newNodes, edges: newEdges, params: params || {}, history_mode: historyMode };
    const obj = { ...flow, ...hookObj, betaContent: JSON.stringify(contentObj) };
    await this.props.updateWorkflow(obj);

    // 保存后重新计算MD5哈希值
    const initialHash = this.generateFlowHash(nodes, edges);
    this.setState({ isPublished: false, initialHash });
  }

  renderBtn = (params = {}, isTooltip = false, isConfirm = false) => {
    let content = (
      <Button type="primary" shape={params?.name ? 'round' : 'circle'} {...params}>{params.name}</Button>
    );
    const title = params?.confirmTitle || '请确认是否已保存!?';
    const onConfirm = params?.onConfirm || this.onBack;
    content = isTooltip ? <Tooltip title={params.tip} placement="bottom" >{content}</Tooltip> : content;
    return isConfirm ? <Popconfirm onConfirm={onConfirm} title={title}>{content}</Popconfirm> : content;
  }

  renderBtns = () => {
    const { isBeta, editable, nodes, edges, initialHash } = this.state;

    // 检查是否有变更
    const currentHash = this.generateFlowHash(nodes, edges);

    let btns = [
      { icon: <FunctionOutlined />, tip: '另存为函数', onClick: () => { return this.setState({ openFunc: true }); } },
      isBeta ?
        {
          name: '发布',
          icon: <VerticalAlignTopOutlined />,
          style: { margin: '0 10px' },
          isConfirm: true,
          disabled: this.state.isPublished,
          confirmTitle: '发布后，将会覆盖线上所有使用该工作流的场景。确保测试通过后发布。',
          onConfirm: () => { return this.onPublish(); },
        } : null,
      {
        icon: <SaveOutlined />,
        style: { margin: '0 10px' },
        onClick: this.onSave,
        disabled: currentHash === initialHash,
        name: '保存',
      },
      {
        name: '运行',
        icon: <MessageOutlined />,
        onClick: () => { return this.setState({ openMsg: true }); },
      },
    ];

    if (!editable) {
      btns = [{ name: '运行', icon: <MessageOutlined />, onClick: () => { return this.setState({ openMsg: true }); } }];
    }

    return (
      <div style={{ position: 'absolute', top: 4, right: 10, zIndex: 10 }}>
        {
          btns.filter((x) => { return !_.isEmpty(x); }).map(({ isTooltip, isConfirm, ...x }) => {
            return this.renderBtn(x, isTooltip, isConfirm);
          })
        }
      </div>
    );
  }

  render = () => {
    const { nodes, edges, flow } = this.state;
    return (
      <div className="workflow-container workflow-v2-container">
        <ReactFlowProvider>
          <div ref={(v) => { this.reactFlowWrapper = v; }} style={{ display: 'flex' }}>
            {
              _.isUndefined(this.props.nodeTypes) ? null : (
                <ExtraSidebar
                  subflows={this.props.subflows}
                  nodeTypes={this.props.nodeTypes}
                  assistants={this.props.assistants}
                />
              )
            }
            <div className="flow-wrap">
              <ReactFlow
                key={this.state.reactFlowId}
                nodes={nodes}
                noDragClassName="nodrag"
                edges={edges.map((x) => { return { ...x, type: 'default' }; })}
                nodeTypes={NodeTypes}
                edgeTypes={this.EdgeTypes}
                onInit={(e) => { this.reactFlowInstance = e; }}
                onDrop={(e) => { return this.onDrop(e); }}
                onDragOver={(e) => { return this.onDragOver(e); }}
                onConnect={(e) => { return this.onConnect(e); }}
                onNodesChange={([e]) => { return this.onChangeNodes(e); }}
                onNodeDrag={(e, node) => { return this.onNodeDrag(node); }}
                onNodeDragStop={(e, node) => { return this.onNodeDragStop(node); }}
              >
                <Background variant="cross" />
                <Controls />
              </ReactFlow>
            </div>
          </div>
        </ReactFlowProvider>
        {this.renderBtns()}

        {
          this.state.openSetting &&
          <SettingDrawer
            flowId={this.state.flow?.uuid}
            open={this.state.openSetting}
            node={this.state.setting}
            historyMode={this.state.historyMode}
            fullFuncs={this.props.fullFuncs}
            globalFuncs={this.props.globalFuncs}
            globalApiFuncs={this.props.globalApiFuncs}
            nodes={nodes}
            onSave={this.onSaveSetting}
            onClose={() => { return this.setState({ openSetting: false, setting: {} }); }}
            fetchConversations={this.props.fetchConversations}
            createConversation={this.props.createConversation}
            updateConversation={this.props.updateConversation}
            deleteConversation={this.props.deleteConversation}
          />
        }
        {
          this.state.openMsg &&
          <MessageDrawer
            flow={flow}
            nodes={nodes}
            open={this.state.openMsg}
            onCancel={this.props.cancelWorkflow}
            fetchRunLogs={this.props.fetchRunLogs}
            fetchConversations={this.props.fetchConversations}
            onClose={() => { return this.setState({ openMsg: false }); }}
          />
        }

        {
          flow &&
          <FlowName
            flow={flow}
            pubVersion={this.state.pubVersion}
            onChange={(e) => { return this.setState({ flow: e }); }}
            onSave={this.onSave}
          />
        }
        <IconFont
          type={this.state.isBeta ? 'beta' : 'release'}
          onClick={() => { return this.onSwitchVersion(); }}
          style={{ fontSize: 108, position: 'absolute', right: 0, bottom: 0 }}
        />
        {
          this.state.openFunc &&
          <FunctionDrawer
            workflow={flow}
            nodes={nodes}
            open={this.state.openFunc}
            onClose={() => { return this.setState({ openFunc: false }); }}
          />
        }
      </div>
    );
  }
}

export {
  reducer,
};
