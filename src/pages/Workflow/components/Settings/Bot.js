import { DeleteFilled, PlusCircleFilled } from '@ant-design/icons';
import { python } from '@codemirror/lang-python';
import CodeMirror from '@uiw/react-codemirror';
import { ChatBot, Sessions } from '~/engine';
import { FUNC_TYPES } from '~/pages/Playground/Configs';
import * as Conf from '~/pages/Playground/Configs';
import Utils from '~/pages/Playground/Utils';
import ModelSelect from '~/pages/Playground/Workflow/components/ModelSelect';
import MsgTabpane from '~/pages/Playground/Workflow/components/SettingDrawer/MsgTabpane';
import { Platform, StringExtension } from '~/plugins';
import {
  Button,
  Divider,
  Drawer,
  Form,
  Input,
  InputNumber,
  Mentions,
  Popconfirm,
  Radio,
  Select,
  Switch,
  Table,
  Tabs,
} from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

const MENTION_PREFIX = '{';

export default class Bot extends PureComponent {
  static propTypes = {
    nodeId: PropTypes.string,
    flowId: PropTypes.string,
    node: PropTypes.object,
    params: PropTypes.object,
    fullFuncs: PropTypes.object,
    libraries: PropTypes.array,
    globalFuncs: PropTypes.array,
    globalApiFuncs: PropTypes.array,
    onChange: PropTypes.func,
    onChangeState: PropTypes.func,
    fetchConversations: PropTypes.func,
    createConversation: PropTypes.func,
    updateConversation: PropTypes.func,
    deleteConversation: PropTypes.func,
  }

  state = {
    functions: [],
    sessionList: [],
    conversations: [],
    showFull: false,
    fullMentionIds: [],
  }

  constructor(props) {
    super(props);

    this.cacheParams = {};
  }

  componentDidMount = async () => {
    const { flowId, nodeId, node } = this.props;
    const messages = await this.props.fetchConversations(flowId, nodeId);
    const flowResult = await ChatBot.fetchWorkflowSessions({ flowId, nodeId });
    const obj = { sessionList: flowResult.items, conversations: messages };
    if (node?.configs?.llmSetting?.functions?.length) {
      const tempFunc = node?.configs?.llmSetting?.functions[0];
      try {
        JSON.parse(tempFunc);
        const funcs = node?.configs?.llmSetting?.functions.map((x) => {
          return JSON.parse(x);
        });
        obj.functions = _.keyBy(funcs, 'name');
        this.props.onChangeState({ functions: _.keyBy(funcs, 'name') });
      } catch (error) {
        this.onChangeFuncSelect(node?.configs?.llmSetting?.functions);
      }
    }

    if (!node?.configs?.historySettings?.historyMode) {
      this.onChangeHistorySetting('none', 'historyMode');
    }

    this.setState(obj);
  }

  updateNodeConfig = (configKey, key, value) => {
    this.props.onChange({
      ...this.props.node,
      configs: {
        ...this.props.node.configs,
        [configKey]: { ...this.props.node.configs[configKey], [key]: value },
      },
    });
  }

  cacheModelParams = (data) => {
    const modelKey = Utils.pairModel(data.model);
    if (_.isUndefined(this.cacheParams[modelKey])) {
      this.cacheParams[modelKey] = {};
    }

    _.map(Conf[`${_.toUpper(modelKey)}_PARAMS`], (v, k) => {
      this.cacheParams[modelKey][k] = data[k];
    });
  }

  onAddInput = (key) => {
    const items = this.props.node.configs?.conversationSettings?.[key] || [];
    items.push({ type: 'user', content: '' });
    this.updateNodeConfig('conversationSettings', key, items);
  }

  onDelInput = (idx, key) => {
    const items = this.props.node.configs?.conversationSettings?.[key] || [];
    items.splice(idx, 1);
    this.updateNodeConfig('conversationSettings', key, items);
  }

  onChangeInputValue = (e, type, key, idx) => {
    const value = e?.target ? e.target.value : e;
    const items = this.props.node.configs?.conversationSettings?.[key] || [];
    items[idx][type] = value;
    this.updateNodeConfig('conversationSettings', key, items);
  }

  onChangeFuncSelect = async (e) => {
    let newFunctions = { ...this.state.functions };

    if (e && e.length > 0) {
      Object.keys(newFunctions).forEach((key) => {
        if (!e.includes(key)) {
          delete newFunctions[key];
        }
      });

      e.forEach((x) => {
        if (!newFunctions[x]) {
          newFunctions[x] = this.props.fullFuncs[x];
        }
      });
    } else {
      newFunctions = {};
    }

    await this.setState({ functions: newFunctions });
    this.props.onChangeState({ functions: newFunctions });
    this.onChangeLLMSetting(e, 'functions');
  }

  onChangeFuncValue = async (e, key, funcKey, pKey) => {
    const functions = _.cloneDeep(this.state.functions);
    const value = e?.target ? e.target.value : e;
    if (pKey) {
      functions[funcKey][key] = functions[funcKey][key].map((x) => {
        return x.name === pKey ? { ...x, description: value } : x;
      });
    } else {
      functions[funcKey][key] = value;
    }
    this.setState({ functions });
    this.props.onChangeState({ functions });
  }

  onChangeFuncParams = async (e, key, idx, fKey) => {
    const functions = _.cloneDeep(this.state.functions);
    const value = e?.target ? e.target.value : e;
    if (_.isEmpty(functions[fKey].params)) {
      functions[fKey].params = [{}];
    }
    functions[fKey].params[idx][key] = value;
    this.setState({ functions });
    this.props.onChangeState({ functions });
  }

  onChangeOtherSetting = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.updateNodeConfig('otherConfig', key, value);
  }

  onChangeHistorySetting = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.updateNodeConfig('historySettings', key, value);
  }

  onChangeLLMSetting = (e, key) => {
    let llmSetting = _.cloneDeep(this.props.node.configs.llmSetting);
    const value = e?.target ? e.target.value : e;
    const rules = {};

    if (key === 'model') {
      const modelConfigs = Sessions.getModelConfigs();
      const totalParameterRules = Sessions.getParameterRules();
      const parameterRules = modelConfigs[value]?.parameterRules || [];
      parameterRules.forEach((rule) => {
        rules[rule.name] = rule.default;
      });

      Object.keys(totalParameterRules).forEach((rule) => {
        delete llmSetting[rule];
      });
    }


    if (key === 'model' && (Utils.pairModel(value) !== Utils.pairModel(llmSetting.model))) {
      const modelKey = Utils.pairModel(value);

      this.cacheModelParams(llmSetting);
      if (!_.isUndefined(this.cacheParams[modelKey])) {
        _.map(Conf[`${_.toUpper(modelKey)}_PARAMS`], (v, k) => {
          llmSetting[k] = this.cacheParams[modelKey][k];
        });
      }
    }

    const originLLMSetting = _.cloneDeep(this.props.node.configs.llmSetting);
    llmSetting[key] = value;
    llmSetting = { ...originLLMSetting, ...llmSetting };
    this.props.onChange({
      ...this.props.node,
      configs: {
        ...this.props.node.configs,
        llmSetting: { ...llmSetting, ...StringExtension.snakeToCamelObj(rules) },
      },
    });
  }

  onClickPrompts = (prompt) => {
    Platform.emit(Platform.Event.OPEN_PROMPT_DRAWER, { prompt });
  }

  renderOpenAIParams = (data = {}, onChange = () => { }, opts) => { // eslint-disable-line
    const model = data?.model;
    const modelConfigs = Sessions.getModelConfigs();
    const parameterRules = StringExtension.snakeToCamelObj(modelConfigs[model]?.parameterRules || []);

    return (
      <>
        {
          _.map(parameterRules, (rule) => {
            const key = _.camelCase(rule?.name);
            const value = data[key];
            const numProps = rule.max > 1 ?
              { min: 0, max: rule.max, step: 1, value } :
              { min: 0, max: 1, step: 0.1, value };

            return (
              <Form.Item label={_.upperFirst(key)} tooltip={rule?.help?.zh_Hans}>
                {
                  (rule?.type === 'string') && (
                    <Select value={value} onChange={(e) => { return onChange(e, key); }}>
                      {
                        rule?.options.map((x) => {
                          return <Select.Option value={x} label={x} />;
                        })
                      }
                    </Select>
                  )
                }
                {
                  (rule?.type === 'float' || rule?.type === 'int') && (
                    <InputNumber
                      {...numProps}
                      style={{ width: '100%' }}
                      onChange={(e) => { return onChange(e, key); }}
                    />
                  )
                }
                {
                  (rule?.type === 'boolean') && (
                    <Switch checked={value} onChange={(e) => { return onChange(e, key); }} />
                  )
                }
                {
                  (rule?.type === 'text') && (
                    <Input.TextArea
                      autoSize={{ minRows: 6 }}
                      value={value}
                      style={{ width: '100%' }}
                      onChange={(e) => { return onChange(e, key); }}
                    />
                  )
                }
              </Form.Item>
            );
          })
        }
      </>
    );
  }

  renderFunParams = (v, k) => {
    if (_.startsWith(k, 'kl@')) {
      return (
        <Form.Item label="查询词描述">
          <Input.TextArea
            autoSize
            value={v.queryDescription}
            onChange={(e) => { return this.onChangeFuncValue(e, 'queryDescription', k); }}
          />
        </Form.Item>
      );
    }

    if (!_.isEmpty(v.params) && _.isEmpty(v.argsSchemaType)) {
      return (
        <Form.Item label="参数">
          {
            _.map(v.params, (p) => {
              return (
                <Form.Item label={<div style={{ width: 100 }}>{p.name}</div>}>
                  <Input.TextArea
                    autoSize
                    value={p.description}
                    onChange={(e) => { return this.onChangeFuncValue(e, 'params', k, p.name); }}
                  />
                </Form.Item>
              );
            })
          }
        </Form.Item>
      );
    }

    if (v.argsSchemaType === 'custom') {
      const params = _.isEmpty(v.params) ? [{ name: '', description: '', type: '' }] : v.params;
      return (
        <Form.Item label="参数">
          {
            _.map(params, (p, i) => {
              return (
                <div style={{ marginBottom: 10 }}>
                  <div style={{ display: 'flex' }}>
                    <Input
                      addonBefore="参名"
                      value={p.name}
                      onChange={(e) => { return this.onChangeFuncParams(e, 'name', i, k); }}
                    />
                    <div style={{ width: 120, display: 'flex', justifyContent: 'space-evenly' }}>
                      <PlusCircleFilled
                        fontSize={24}
                        onClick={() => {
                          const newParams = _.cloneDeep(v.params);
                          newParams.push({ name: '', description: '', type: '' });
                          const functions = { ...this.state.functions, [k]: { ...v, params: newParams } };
                          this.setState({ functions });
                          this.props.onChangeState({ functions });
                        }}
                      />
                      <DeleteFilled
                        fontSize={24}
                        onClick={() => {
                          const newParams = _.cloneDeep(v.params);
                          if (newParams.length === 1) return;
                          newParams.splice(i, 1);
                          const functions = { ...this.state.functions, [k]: { ...v, params: newParams } };
                          this.setState({ functions });
                          this.props.onChangeState({ functions });
                        }}
                      />
                    </div>
                  </div>
                  <Input
                    addonBefore="类型"
                    value={p.type}
                    style={{ margin: '5px 0' }}
                    onChange={(e) => { return this.onChangeFuncParams(e, 'type', i, k); }}
                  />
                  <Input
                    addonBefore="描述"
                    value={p.description}
                    onChange={(e) => { return this.onChangeFuncParams(e, 'description', i, k); }}
                  />
                </div>
              );
            })
          }
        </Form.Item>
      );
    }

    return null;
  }

  renderGPTFuncDrawer = () => {
    return (
      <Drawer
        title="GPT函数"
        width="50%"
        open={this.state.openFunc}
        onClose={() => { return this.setState({ openFunc: false }); }}
      >
        <Form labelCol={{ span: 3 }} className="common-form">
          {
            _.map(this.state.functions, (v, k) => {
              return (
                <>
                  <Divider orientation="left">{v.displayName}</Divider>
                  <Form.Item label="描述">
                    <Input.TextArea
                      autoSize
                      value={v.description}
                      onChange={(e) => { return this.onChangeFuncValue(e, 'description', k); }}
                    />
                  </Form.Item>
                  {this.renderFunParams(v, k)}
                </>
              );
            })
          }
        </Form>
      </Drawer>
    );
  }

  renderSortableContainer = (tips = '', key, opts = []) => {
    const items = this.props.node.configs?.conversationSettings?.[key] || [];
    return (
      <>
        <Divider style={{ margin: '5px 0' }}>
          <span style={{ fontSize: 14, color: '#ccc' }}>{tips}</span>
        </Divider>
        <div style={{ textAlign: 'end', marginBottom: 5 }}>
          <Button size="small" onClick={() => { return this.onAddInput(key); }}>新增</Button>
        </div>
        {
          items.map((x, i) => {
            return (
              <div style={{ display: 'flex', flexDirection: 'column', width: '100%', marginBottom: 10, zIndex: 1001 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Select
                    value={x.type}
                    onChange={(e) => { this.onChangeInputValue(e, 'type', key, i); }}
                    style={{ width: 200 }}
                    options={[
                      { label: 'ASSISTANT', value: 'assistant' },
                      { label: 'USER', value: 'user' },
                    ]}
                  />
                  <div>
                    <Button
                      size="small"
                      type="link"
                      onClick={() => {
                        return this.setState({
                          fullMentionIds: this.state.fullMentionIds.includes(i) ?
                            _.without(this.state.fullMentionIds, i) : [...this.state.fullMentionIds, i],
                        });
                      }}
                    >
                      {
                        this.state.fullMentionIds.includes(i) ? '退出' : ''}全屏
                    </Button>
                    <Divider type="vertical" />
                    <DeleteFilled
                      style={{ marginRight: 30, fontSize: 18 }}
                      onClick={() => { return this.onDelInput(i, key); }}
                    />
                  </div>
                </div>

                <Mentions
                  style={{ width: '100%' }}
                  placeholder="请输入消息内容"
                  value={x.content}
                  prefix={MENTION_PREFIX}
                  autoSize={this.state.fullMentionIds.includes(i) ? { minRows: 50 } : { minRows: 3, maxRows: 3 }}
                  onChange={(e) => { this.onChangeInputValue(e, 'content', key, i); }}
                  options={opts}
                />
              </div>
            );
          })
        }
      </>
    );
  }

  renderSetting = () => {
    const obj = this.props.node;
    const opts = (obj.inputs || []).map((x) => { return { value: `{${x.name}}}`, label: x.name }; });

    const data = obj?.configs;
    const model = Utils.pairModel(data?.llmSetting?.model);
    const showFunc = ['ernie-bot-4'].includes(data?.llmSetting?.model) || model === 'openai';

    return (
      <Form labelCol={{ span: 4 }} className="common-form">
        <Form.Item label="系统提示词">
          <div style={{ float: 'right' }}>
            <Button
              size="small"
              type="link"
              onClick={() => { return this.setState({ showFull: !this.state.showFull }); }}
            >
              {this.state.showFull ? '退出' : ''}全屏
            </Button>
            <Divider type="vertical" />
            <Button
              size="small"
              type="link"
              onClick={() => { return this.onClickPrompts(data?.llmSetting?.systemPrompt); }}
            >
              Prompt库
            </Button>
          </div>
          <Mentions
            value={data?.llmSetting?.systemPrompt}
            prefix={MENTION_PREFIX}
            autoSize={this.state.showFull ? { minRows: 50 } : { minRows: 3, maxRows: 3 }}
            onChange={(e) => { this.onChangeLLMSetting(e, 'systemPrompt'); }}
            options={opts}
          />
        </Form.Item>
        <Form.Item label="名称">
          <Input
            value={obj?.displayName}
            onChange={(e) => { return this.props.onChange({ ...obj, displayName: e.target.value }); }}
          />
        </Form.Item>
        <Form.Item label="运行时禁止蹦字日志">
          <Switch
            checked={data?.llmSetting?.disableStepToken}
            onChange={(e) => { return this.onChangeLLMSetting(e, 'disableStepToken'); }}
          />
        </Form.Item>
        <Form.Item label="模型">
          <ModelSelect
            params={this.props.params}
            value={data?.llmSetting?.model}
            onChange={(e) => { return this.onChangeLLMSetting(e, 'model'); }}
          />
        </Form.Item>
        {
          showFunc &&
          <>
            <Form.Item label="GPT函数">
              <div style={{ display: 'flex' }}>
                <Select
                  mode="multiple"
                  value={_.keys(this.state.functions)}
                  onChange={(e) => { return this.onChangeFuncSelect(e); }}
                >
                  {
                    (this.props.globalFuncs || []).map((x, i) => {
                      return (
                        <Select.OptGroup key={x.length} label={FUNC_TYPES[i]}>
                          {_.map(x, (v, k) => { return <Select.Option value={k}>{v}</Select.Option>; })}
                        </Select.OptGroup>
                      );
                    })
                  }
                </Select>
                <Button onClick={() => { return this.setState({ openFunc: true }); }}>修改函数 Prompt</Button>
              </div>
            </Form.Item>
            {
              (_.keys(this.state.functions) || []).includes('search_knowledge') &&
              <>
                <Form.Item label="知识库">
                  <Select
                    value={data?.llmSetting?.libraryId}
                    onChange={(e) => { return this.onChangeLLMSetting(e, 'libraryId'); }}
                  >
                    {
                      (this.props?.libraries || []).map((x) => {
                        return <Select.Option value={x.id}>{x.field}</Select.Option>;
                      })
                    }
                  </Select>
                </Form.Item>
                <Form.Item label="输出模版">
                  <CodeMirror
                    id="sourceCode"
                    value={obj?.configs?.otherConfig?.resultTpl}
                    height="100px"
                    extensions={[python()]}
                    onChange={(e) => { return this.onChangeOtherSetting(e, 'resultTpl'); }}
                  />
                </Form.Item>
              </>
            }
            <Form.Item label="API Action">
              <Select
                mode="multiple"
                value={data?.llmSetting?.openapiFuncs}
                onChange={(e) => { return this.onChangeLLMSetting(e, 'openapiFuncs'); }}
              >
                {
                  _.map(this.props.globalApiFuncs || [], (v, k) => {
                    return <Select.Option value={+k}>{v}</Select.Option>;
                  })
                }
              </Select>
            </Form.Item>
          </>
        }
        <Form.Item label="运行配置">
          <Radio.Group
            value={obj?.configs?.historySettings?.historyMode || 'none'}
            onChange={(e) => { return this.onChangeHistorySetting(e, 'historyMode'); }}
          >
            <Radio value="none">忽略节点历史消息</Radio>
            <Radio value="autofit">启用并自动压缩节点消息</Radio>
            <Radio value="all">启用全部节点历史消息</Radio>
          </Radio.Group>
          {
            (obj?.configs?.historySettings?.historyMode || 'none') === 'autofit' &&
            <div style={{ marginTop: 10 }}>
              <span style={{ marginRight: 30 }}>历史MaxTokens:&nbsp;
                <InputNumber
                  value={obj?.configs?.historySettings?.historyMaxTokens}
                  onChange={(e) => { return this.onChangeHistorySetting(e, 'historyMaxTokens'); }}
                />
              </span>
              <InputNumber
                addonBefore="最近的"
                addonAfter="条消息"
                value={obj?.configs?.historySettings?.historyMaxMessages}
                onChange={(e) => { return this.onChangeHistorySetting(e, 'historyMaxMessages'); }}
              />
            </div>
          }
        </Form.Item>
        {this.renderOpenAIParams(data?.llmSetting, this.onChangeLLMSetting, opts)}
        {this.renderSortableContainer('初始聊天历史消息', 'backgroundInputs', opts)}
        {this.renderSortableContainer('新聊天消息', 'inputs', opts)}
      </Form>
    );
  }

  renderMsgTabpane = () => {
    return (
      <Tabs.TabPane tab="消息" key="msg">
        <MsgTabpane
          messages={this.state.conversations}
          createConversation={this.props.createConversation}
          deleteConversation={this.props.deleteConversation}
          updateConversation={this.props.updateConversation}
        />
      </Tabs.TabPane>
    );
  }

  renderMsgDrawer = () => {
    const { chatHistories } = this.state;

    return (
      <Drawer
        title="消息历史"
        width="45vw"
        open={this.state.openMsg}
        onClose={() => { return this.setState({ openMsg: false }); }}
      >
        {
          chatHistories.map((x) => {
            return (
              <Input.Group
                compact
                style={{ display: 'flex', marginBottom: 5, padding: 2, borderBottom: '1px solid #ddd' }}
              >
                <Select bordered={false} style={{ width: 130, maxHeight: 32 }} value={x.role} disabled>
                  <Select.Option value="USER">USER</Select.Option>
                  <Select.Option value="ASSISTANT">ASSISTANT</Select.Option>
                </Select>
                <Input.TextArea autoSize bordered={false} value={x.content} />
              </Input.Group>
            );
          })
        }
      </Drawer>
    );
  }

  renderSessionTabpane = () => {
    return (
      <Tabs.TabPane tab="管理" key="session">
        <Table
          dataSource={this.state.sessionList}
          columns={[
            { title: 'UID', dataIndex: 'uid' },
            {
              title: '操作',
              dataIndex: 'id',
              render: (id, row) => {
                return (
                  <>
                    <a
                      onClick={async () => {
                        const res = await ChatBot.fetchWorkflowConversations({
                          lastId: 0,
                          topicId: row.topicId,
                          nodeId: this.props.nodeId,
                          sessionId: row.id,
                        });
                        this.setState({ chatHistories: res.items, openMsg: true });
                      }}
                    >
                      查看历史
                    </a>
                    <Divider type="vertical" />
                    <Popconfirm
                      title="确认清空?"
                      onConfirm={async () => { return ChatBot.clearChatbotSessionConversation(row.topicId); }}
                    >
                      <a>清空记录</a>
                    </Popconfirm>
                  </>
                );
              },
            },
          ]}
        />
      </Tabs.TabPane>
    );
  }

  render = () => {
    return (
      <>
        <Tabs>
          <Tabs.TabPane tab="输入" key="input">
            {this.renderSetting()}
          </Tabs.TabPane>
          {this.renderMsgTabpane()}
          {this.renderSessionTabpane()}
        </Tabs>
        {this.state.openFunc && this.renderGPTFuncDrawer()}
        {this.state.openMsg && this.renderMsgDrawer()}
      </>
    );
  }
}
