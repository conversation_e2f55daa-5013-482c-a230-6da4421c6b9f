import { <PERSON><PERSON>, Drawer } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import <PERSON><PERSON> from './Bot';

export default class SettingDrawer extends PureComponent {
  static propTypes = {
    flowId: PropTypes.string,
    params: PropTypes.object,
    node: PropTypes.object,
    fullFuncs: PropTypes.array,
    globalFuncs: PropTypes.array,
    globalApiFuncs: PropTypes.object,
    onSave: PropTypes.func,
    open: PropTypes.bool,
    onClose: PropTypes.func,
    fetchConversations: PropTypes.func,
    createConversation: PropTypes.func,
    updateConversation: PropTypes.func,
    deleteConversation: PropTypes.func,
  };

  state = {
    node: {},
    libraries: [],
  }

  componentDidMount = () => {
    const { node } = this.props;
    const obj = { node: node?.data, libraries: node?.libraries };
    if (!node?.data?.configs?.historySettings?.historyMode) {
      obj.node.configs.historySettings = {
        historyMode: 'none',
      };
    }
    this.setState(obj);
  }

  onSave = () => {
    const data = this.state.node;
    if (!_.isEmpty(data?.configs?.llmSetting?.functions)) {
      const functions = _.values(this.state.functions).map((x) => { return JSON.stringify(x); });
      data.configs.llmSetting.functions = functions;
    }
    if (!data?.configs?.historySettings?.historyMode) {
      data.configs.historySettings = {
        historyMode: 'none',
      };
    }

    const params = { ...this.props.node, data };
    delete params.publishInfo;
    this.props.onSave(params);
  }

  render = () => {
    const { node, open } = this.props;
    const { data, id, type } = node;

    return (
      <>
        <Drawer
          maskClosable={false}
          title={`${data?.displayName} - (${id})`}
          onClose={this.props.onClose}
          placement="right"
          open={open}
          width="50vw"
        >
          {
            type === 'bot' &&
            <Bot
              nodeId={id}
              node={this.state.node}
              flowId={this.props.flowId}
              params={this.props.params}
              libraries={this.state.libraries}
              fullFuncs={this.props.fullFuncs}
              globalFuncs={this.props.globalFuncs}
              globalApiFuncs={this.props.globalApiFuncs}
              fetchConversations={this.props.fetchConversations}
              createConversation={this.props.createConversation}
              updateConversation={this.props.updateConversation}
              deleteConversation={this.props.deleteConversation}
              onChange={(e) => { return this.setState({ node: e }); }}
              onChangeState={(e) => { return this.setState(e); }}
            />
          }
          <div style={{ textAlign: 'center', position: 'absolute', left: 0, right: 0, bottom: 10, zIndex: 100 }}>
            <Button type="primary" shape="round" onClick={this.onSave}>保存</Button>
          </div>
        </Drawer>
      </>
    );
  }
}
