import { Platform } from '~/plugins';
import { <PERSON><PERSON>, Card, Drawer, List, Typography } from 'antd';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class PluginDrawer extends PureComponent {
  static propTypes = {
    plugins: PropTypes.array,
    open: PropTypes.bool,
    onClose: PropTypes.func,
  };

  state = {
  }

  componentDidMount = () => {
  }

  onSelectBot = (item) => {
    Platform.emit('ADD_NODE', item);
    this.props.onClose();
  }

  render = () => {
    const { plugins, open } = this.props;
    return (
      <Drawer
        maskClosable
        closable={false}
        title="Plugins"
        onClose={this.props.onClose}
        placement="right"
        open={open}
        width="50vw"
      >
        <List
          style={{ marginTop: 10 }}
          dataSource={plugins}
          grid={{ gutter: 16, column: 2 }}
          renderItem={(item) => {
            return (
              <div>
                <List.Item key={item.name}>
                  <Card
                    size="small"
                    title={item.displayName}
                    bodyStyle={{ padding: 12 }}
                    extra={
                      <Button
                        type="link"
                        size="small"
                        onClick={() => { return this.onSelectBot(item); }}
                      >选择
                      </Button>
                    }
                  >
                    <Typography.Paragraph style={{ marginBottom: 0 }}>
                      {item?.description}
                    </Typography.Paragraph>
                  </Card>
                </List.Item>
              </div>
            );
          }
          }
        />
      </Drawer>
    );
  }
}
