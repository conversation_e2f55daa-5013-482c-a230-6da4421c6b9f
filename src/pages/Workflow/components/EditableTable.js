import { DeleteFilled, QuestionCircleOutlined } from '@ant-design/icons';
import { Badge, Button, Cascader, Input, Select, Table, Tooltip } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class EditableTable extends PureComponent {
  static propTypes = {
    noDel: PropTypes.bool,
    isCascader: PropTypes.bool,
    datas: PropTypes.array,
    quoteSchemas: PropTypes.array,
    inputSchema: PropTypes.object,
    inputEnums: PropTypes.object,
    onChange: PropTypes.func,
  }

  static defaultProps = {
    inputEnums: {},
    inputSchema: {},
    noDel: false,
    isCascader: false,
  }

  state = {
    dependencyMap: {},
  }

  componentWillReceiveProps = (nextProps) => {
    if (_.isEmpty(this.state.dependencyMap) || nextProps.inputEnums !== this.props.inputEnums) {
      const { inputEnums } = nextProps;
      const dependencyMap = {};
      _.keys(inputEnums).forEach((key) => {
        if (!_.isEmpty(inputEnums[key]?.dependency)) {
          const { dependency } = inputEnums[key];
          if (!dependencyMap[dependency]) {
            dependencyMap[dependency] = [];
          }
          dependencyMap[dependency].push(key);
        }
      });
      this.setState({ dependencyMap });
    }
  }

  onChangeValue = (e, key, idx) => {
    const value = e.target ? e.target.value : e;
    const datas = _.cloneDeep(this.props.datas);
    datas[idx][key] = value;
    this.props.onChange(datas);
  }

  onChangeOptionValue = (e, key, idx, name) => {
    const value = e.target ? e.target.value : e;
    const datas = _.cloneDeep(this.props.datas);
    datas[idx][key] = value;
    const dependencyKeys = this.state.dependencyMap[name];

    if (!_.isEmpty(dependencyKeys)) {
      dependencyKeys.forEach((dependencyKey) => {
        const dIdx = datas.findIndex((x) => { return x.name === dependencyKey; });
        if (dIdx !== -1) {
          const dependency = this.props.inputEnums[_.camelCase(dependencyKey)];
          if (dependency?.enums?.[value]?.[0]?.value) {
            datas[dIdx].value = dependency.enums[value][0].value;
          }
        }
      });
    }
    this.props.onChange(datas);
  }

  onAdd = () => {
    const datas = _.cloneDeep(this.props.datas) || [];
    if (this.props.isCascader) {
      datas.push({ name: '', type: 'reference', path: '' });
    } else {
      datas.push({ name: '', type: 'value', value: '' });
    }
    this.props.onChange(datas);
  }

  onDel = (idx) => {
    const datas = _.cloneDeep(this.props.datas);
    if (datas.length === 1) {
      return;
    }
    datas.splice(idx, 1);
    this.props.onChange(datas);
  }

  renderCascade = (record, index, key = 'value') => {
    return (
      <Cascader
        size="small"
        style={{ width: '70%', maxWidth: 200 }}
        value={(record[key] || '').split('.')}
        options={this.props.quoteSchemas}
        expandTrigger="hover"
        onChange={(values) => { return this.onChangeValue(values.join('.'), key, index); }}
      />
    );
  }

  renderOptions = (record, idx) => {
    const item = this.props.inputEnums[_.camelCase(record.name)];
    let opts = [];
    if (_.isEmpty(item.dependency)) {
      opts = item.enums.default;
    } else {
      const { value } = this.props.datas.find((x) => { return x.name === item.dependency; });
      opts = item.enums[value];
    }

    return (
      <Select
        showSearch
        value={record?.value}
        style={{ width: '100%' }}
        options={_.cloneDeep(opts)}
        onChange={(e) => { return this.onChangeOptionValue(e, 'value', idx, record.name); }}
        filterOption={(input, option) => { return option?.value?.includes(input); }}
        size="small"
      />
    );
  }

  renderColumns = () => {
    return [
      {
        title: '参数名',
        dataIndex: 'name',
        key: 'name',
        render: (text, row, idx) => {
          if (this.props.noDel) {
            const schema = this.props?.inputSchema[_.camelCase(text)] || {};
            return (
              <div style={{
                width: 120,
                fontSize: 12,
                color: '#000',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
              >
                <span>
                  {
                    schema?.required ?
                      <Badge offset={[5, 5]} count={<span style={{ color: '#f5222d', fontSize: 20 }}>*</span>}>
                        {text}
                      </Badge> :
                      text
                  }
                </span>
                <Tooltip title={schema?.description}>
                  <QuestionCircleOutlined style={{ marginLeft: 20, color: '#ccc' }} />
                </Tooltip>
              </div>
            );
          }

          return (
            <Input
              size="small"
              value={text}
              style={{ width: 120 }}
              onChange={(e) => { return this.onChangeValue(e, 'name', idx); }}
            />
          );
        },
      },
      {
        title: '参数值',
        dataIndex: 'type',
        key: 'type',
        render: (text, record, index) => {
          if (this.props.isCascader) {
            return this.renderCascade(record, index, 'path');
          }
          if (this.props.inputEnums[_.camelCase(record.name)]) {
            return this.renderOptions(record, index);
          }

          return (
            <Input.Group size="small" compact style={{ display: 'flex', alignItems: 'center' }}>
              <Select
                size="small"
                defaultValue={text}
                style={{ width: '30%' }}
                onChange={(value) => { return this.onChangeValue(value, 'type', index); }}
              >
                <Select.Option value="value">输入</Select.Option>
                <Select.Option value="reference">引用</Select.Option>
              </Select>
              {
                text === 'value' ? (
                  <Input
                    style={{ width: '70%' }}
                    value={record.value}
                    onChange={(e) => { return this.onChangeValue(e, 'value', index); }}
                  />
                ) : this.renderCascade(record, index)
              }
              {
                !this.props.noDel
                && <DeleteFilled style={{ marginLeft: 5 }} onClick={() => { return this.onDel(index); }} />
              }
            </Input.Group>
          );
        },
      },
    ];
  }

  render = () => {
    return (
      <div className="nodrag">
        <Table
          size="small"
          pagination={false}
          locale={{ emptyText: <div style={{ paddingTop: 10 }}>请添加参数</div> }}
          dataSource={this.props.datas}
          columns={this.renderColumns()}
        />
        {
          !this.props.noDel &&
          <Button
            shape="round"
            size="small"
            style={{ marginTop: 5 }}
            onClick={() => { return this.onAdd(); }}
          >
            新增
          </Button>
        }
      </div>
    );
  }
}
