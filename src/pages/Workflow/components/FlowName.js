import { CheckOutlined, CloseOutlined, EditOutlined } from '@ant-design/icons';
import { Input, Typography } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class FlowName extends PureComponent {
  static propTypes = {
    flow: PropTypes.object,
    onChange: PropTypes.func,
    onSave: PropTypes.func,
    pubVersion: PropTypes.string,
  }

  state = {
    isEdit: false,
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.props.onChange({ ...this.props.flow, [key]: value });
  }

  onChangeEdit = () => {
    this.setState({ isEdit: !this.state.isEdit });
  }

  renderName = () => {
    const { flow } = this.props;
    if (_.isUndefined(flow?.uuid)) return null;
    let content = null;

    if (this.state.isEdit) {
      content = (
        <>
          <Input
            value={flow?.name}
            style={{ width: 200 }}
            onChange={(e) => { return this.onChangeValue(e, 'name'); }}
          />&nbsp;
          <CheckOutlined
            style={{ color: 'green', fontSize: 24 }}
            onClick={() => {
              this.onChangeEdit();
              return this.props.onSave();
            }}
          />&nbsp;
          <CloseOutlined
            style={{ color: 'red', fontSize: 24 }}
            onClick={() => { return this.onChangeEdit(); }}
          />
        </>
      );
    } else {
      content = (
        <>
          <Typography.Title level={3} style={{ marginBottom: 0 }}>{flow?.name}</Typography.Title>
          &nbsp;
          <EditOutlined
            style={{ color: '#000', fontSize: 24 }}
            onClick={() => { return this.onChangeEdit(); }}
          />
          [{this.props.pubVersion}]
        </>
      );
    }
    return content;
  }

  render = () => {
    return (
      <div className="flow-name">
        {this.renderName()}
      </div>
    );
  }
}
