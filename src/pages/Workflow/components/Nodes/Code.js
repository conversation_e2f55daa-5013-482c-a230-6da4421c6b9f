import { javascript } from '@codemirror/lang-javascript';
import { python } from '@codemirror/lang-python';
import CodeMirror from '@uiw/react-codemirror';
import { <PERSON><PERSON>, Card, Collapse, Divider, Drawer, Input, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

import EditableTable from '../EditableTable';
import VariableTable from '../VariableTable';
import Header from './Header';

export default class Code extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
  }

  state = {
    openIDE: false,
    defaultCodeMap: {},
  }

  componentDidMount = () => {
    const defaultCodeMap = {};
    const { inputEnums } = window.CODE_INTERPRETER;
    _.map(inputEnums.defaultCode.enums, (v, k) => {
      if (!_.isEmpty(v)) {
        defaultCodeMap[k] = v[0].value;
      }
    });
    this.setState({ defaultCodeMap });
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const updatedData = { ...this.props.data };
    updatedData[key] = value;
    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
  }

  onChangeConfig = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const updatedData = { ...this.props.data };
    if (key === 'runtime') {
      updatedData.configs.code = this.state.defaultCodeMap[value];
    }
    updatedData.configs[key] = value;
    const { id, data } = this.props;
    data.onChange(id, updatedData);
  }

  onChangeOutput = ({ arrs, schema }) => {
    const updatedData = { ...this.props.data };
    updatedData.outputs = arrs;
    updatedData.outputSchema = schema;
    const { id, data } = this.props;
    data.onChange(id, updatedData);
  }

  onDelNode = (e) => {
    e.stopPropagation();
    const { id, data } = this.props;
    data.onDel(id);
  }

  onClickSetting = (e) => {
    e.stopPropagation();
    const { id, data } = this.props;
    data.onShowSetting(id);
  }

  renderHeader = () => {
    const { id, data } = this.props;
    return (<Header id={id} name="代码" data={data} />);
  }

  renderIDEDrawer = () => {
    const { code, runtime } = this.props.data.configs;
    return (
      <Drawer
        title="代码编辑器"
        width="70vw"
        onClose={() => { return this.setState({ openIDE: false }); }}
        open={this.state.openIDE}
      >
        <CodeMirror
          id="sourceCode"
          theme="dark"
          value={code}
          height="80vh"
          extensions={runtime === 'ipynb' ? [python()] : [javascript()]}
          onChange={(e) => { return this.onChangeConfig(e, 'code'); }}
        />
      </Drawer>
    );
  }

  render = () => {
    const { data } = this.props;
    return (
      <div className="end-node-wrap">
        <Collapse defaultActiveKey={['1']} ghost>
          <Collapse.Panel header={this.renderHeader()} key="1">
            <Card size="small" title="输入" bordered={false}>
              <EditableTable
                datas={data?.inputs}
                quoteSchemas={data?.quoteSchemas}
                onChange={(e) => { this.onChangeValue(e, 'inputs'); }}
              />
            </Card>
            <Divider style={{ margin: '5px 0' }} />
            <Input.Group className="nodrag" style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span>
                <Button type="text">语言</Button>
                <Select
                  size="small"
                  style={{ width: 100 }}
                  value={data?.configs?.runtime}
                  onChange={(e) => { this.onChangeConfig(e, 'runtime'); }}
                >
                  <Select.Option value="ipynb">Python3</Select.Option>
                  <Select.Option value="nodejs">Nodejs</Select.Option>
                  {/* <Select.Option value="pyodide">Pyodide</Select.Option>
                  <Select.Option value="python3">Python3</Select.Option>
                  <Select.Option value="nodejs">Nodejs</Select.Option> */}
                </Select>
              </span>
              <Button type="link" onClick={() => { return this.setState({ openIDE: true }); }}>
                打开IDE
              </Button>
            </Input.Group>
            <Divider style={{ margin: '5px 0' }} />
            <Card size="small" title="输出" bordered={false}>
              <VariableTable
                datas={data?.outputs}
                quoteSchemas={data?.quoteSchemas}
                onChange={(e) => { this.onChangeOutput(e, 'outputs'); }}
              />
            </Card>
          </Collapse.Panel>
        </Collapse>

        <Handle
          type="target"
          position={Position.Left}
          className="base-handle"
          style={{ left: -10, color: '#69f' }}
        />
        <Handle
          type="source"
          position={Position.Right}
          className="base-handle"
          style={{ right: -10 }}
        />

        {this.state.openIDE && this.renderIDEDrawer()}
      </div>
    );
  }
}
