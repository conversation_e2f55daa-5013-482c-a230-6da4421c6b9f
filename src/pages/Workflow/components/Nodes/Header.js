import {
  CheckOutlined,
  DeleteOutlined,
  EditOutlined,
  <PERSON>Outlined,
  SettingOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { json } from '@codemirror/lang-json';
import CodeMirror from '@uiw/react-codemirror';
import { Toast } from '~/components';
import { Avatar, Button, Drawer, Form, Input, Popconfirm, Switch } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import Utils from '../../Utils';

const BOUNDARY_NODES = ['start', 'end'];

export default class Header extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
    type: PropTypes.string,
    name: PropTypes.string,
    onDel: PropTypes.func,
    onShowSetting: PropTypes.func,
    isEditName: PropTypes.bool,
    onChangeName: PropTypes.func,
  }

  state = {
    openErr: false,
    errorConfig: {},
    isEditingName: false,
  }

  setNestedValue = (obj, path, value) => {
    const keys = path.split('.');
    let current = obj;
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
    return obj;
  }

  onDelNode = (e) => {
    e.stopPropagation();
    const { id, data } = this.props;
    data.onDel(id);
  }

  onClickSetting = (e) => {
    e.stopPropagation();
    const { data, id } = this.props;
    data.onShowSetting(id);
  }

  onOpenErrDrawer = () => {
    const { errorConfig } = this.props.data;
    const config = errorConfig || {
      skipError: false, defaultResult: Utils.transformFormat(this.props.data?.outputSchema),
    };

    if (!_.isString(config.defaultResult)) {
      try {
        config.defaultResult = JSON.stringify(config.defaultResult, null, 2);
      } catch (e) {
        config.defaultResult = '';
      }
    }

    this.setState({ openErr: true, errorConfig: config });
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ errorConfig: { ...this.state.errorConfig, [key]: value } });
  }

  onChangeEditName = (e) => {
    e.stopPropagation();
    this.setState({ isEditingName: !this.state.isEditingName });
  }

  onOpenWorkflow = (e) => {
    e.stopPropagation();
    const { data } = this.props;
    const workflowUuid = data?.configs?.workflowUuid || data?.uuid;

    if (!workflowUuid) {
      Toast.show('工作流UUID不存在', Toast.Type.WARNING);
      return;
    }

    // 智能判断工作流版本并跳转到正确路由
    // 优先尝试新版路由，如果不存在则使用旧版路由
    window.open(`${window.location.origin}/workflow-v2/${workflowUuid}`, '_blank');
  }

  onSave = () => {
    const errorConfig = _.cloneDeep(this.state.errorConfig);

    try {
      errorConfig.defaultResult = JSON.parse(errorConfig.defaultResult);
    } catch (e) {
      Toast.show('默认输出必须为JSON格式', Toast.Type.ERROR);
      return;
    }

    const { id, data } = this.props;
    data.onChange(id, { ...data, errorConfig });
    this.setState({ openErr: false });
  }

  renderErrDrawer = () => {
    const { errorConfig } = this.state;
    return (
      <Drawer
        width="30vw"
        title="异常设置"
        placement="right"
        maskClosable={false}
        open={this.state.openErr}
        onClose={() => { return this.setState({ openErr: false }); }}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item label="异常忽略" help="忽略异常并在异常发生时使用默认输出替代">
            <Switch
              checked={errorConfig?.skipError}
              onChange={(e) => { return this.onChangeValue(e, 'skipError'); }}
            />
          </Form.Item>
          {
            errorConfig?.skipError &&
            <Form.Item label="默认输出" required>
              <CodeMirror
                id="err"
                value={errorConfig?.defaultResult}
                height="60vh"
                extensions={[json()]}
                onChange={(e) => { return this.onChangeValue(e, 'defaultResult'); }}
              />
            </Form.Item>
          }
          <Form.Item colon={false} style={{ textAlign: 'center' }}>
            <Button type="primary" onClick={() => { return this.onSave(); }}>保存</Button>
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  render() {
    const { data, name } = this.props;
    const type = this.props.type || data.type;
    return (
      <div
        onClick={(e) => { return e.stopPropagation(); }}
        style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
      >
        <span style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar
            size={16}
            shape="square"
            style={{ marginRight: 10, flexShrink: 0 }}
            src={`/static/v2/icon-${type}.png`}
          />
          {
            this.props.isEditName && this.state.isEditingName ?
              <Input
                size="small"
                bordered={false}
                value={name}
                onChange={(e) => { return this.props.onChangeName(e); }}
                onClick={(e) => { e.stopPropagation(); }}
              />
              : <>{name || 'Bot'}</>
          }
          {
            this.props.isEditName && (
              <span onClick={(e) => { return this.onChangeEditName(e); }} style={{ marginLeft: 10, cursor: 'pointer' }}>
                {this.state.isEditingName ? <CheckOutlined /> : <EditOutlined />}
              </span>
            )
          }
        </span>
        <span>
          <WarningOutlined onClick={() => { return this.onOpenErrDrawer(); }} />
          {
            BOUNDARY_NODES.includes(type) ?
              null :
              <>
                {type === 'sub_workflow' && (
                  <Popconfirm
                    title="是否打开工作流？"
                    onConfirm={(e) => { return this.onOpenWorkflow(e); }}
                  >
                    <LinkOutlined style={{ margin: '0 10px' }} />
                  </Popconfirm>
                )}
                <Popconfirm title="确认删除该节点吗？" onConfirm={(e) => { return this.onDelNode(e); }}>
                  <DeleteOutlined style={{ margin: '0 10px' }} />
                </Popconfirm>
                {type === 'bot' && <SettingOutlined onClick={(e) => { return this.onClickSetting(e); }} />}
              </>
          }
        </span>

        {this.state.openErr && this.renderErrDrawer()}
      </div>
    );
  }
}
