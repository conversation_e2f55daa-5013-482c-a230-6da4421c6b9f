import { StringExtension } from '~/plugins';
import { Collapse, Menu } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

import Utils from '../../Utils';
import EditableTable from '../EditableTable';
import VariableTable from '../VariableTable';
import Header from './Header';

export default class Plugin extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
  }

  state = {
    datas: [],
    inputEnums: {},
  }

  componentDidMount = () => {
    const { inputs, inputSchema, name } = this.props.data;
    const { inputEnums } = (window.PLUGINS || []).find((x) => { return x.name === name; }) || {};

    const stateParams = { inputEnums };
    if (!_.isUndefined(inputs)) {
      stateParams.datas = inputs;
    } else if (!_.isEmpty(inputSchema)) {
      stateParams.datas = Object.keys(inputSchema).map((key) => {
        const value = inputSchema[key].default;
        return { name: StringExtension.camelToSnake(key), type: 'value', value };
      });
    }

    if (!_.isUndefined(this.props.data.outputSchema)) {
      const o = Utils.schemaToObject(this.props.data.outputSchema);
      const menus = o.map((x) => {
        let children;
        if (x.children) {
          children = x.children.map((y) => {
            return { key: y.value, label: `${y.value} [${y.type}]` };
          });
        }
        return { key: x.value, label: `${x.value} [${x.type}]`, children };
      });
      stateParams.menus = menus;
    }
    this.setState(stateParams);
  }

  onChangeValueV2 = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const updatedData = { ...this.props.data };
    updatedData[key] = value;
    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
  }

  onChangeData = ({ arrs, schema }, key = 'output') => {
    const updatedData = { ...this.props.data };
    const baseKey = key.replace(/s$/, '');
    const arrKey = `${baseKey}s`;
    const schemaKey = `${baseKey}Schema`;

    updatedData[arrKey] = arrs;
    updatedData[schemaKey] = schema;

    const { id, data } = this.props;
    data.onChange(id, updatedData);
  }

  onDelNode = (e) => {
    e.stopPropagation();
    const { id, data } = this.props;
    data.onDel(id);
  }

  onClickSetting = (e) => {
    e.stopPropagation();
    const { id, data } = this.props;
    data.onShowSetting(id);
  }

  renderHeader = () => {
    const { id, data } = this.props;
    return (
      <Header
        id={id}
        name={data?.displayName || data?.name}
        data={data}
        isEditName
        onChangeName={(e) => {
          this.onChangeValueV2(e, 'displayName');
        }}
      />);
  }

  render = () => {
    const { datas, inputEnums, menus } = this.state;
    return (
      <div className="start-node-wrap">
        <Collapse defaultActiveKey={['1']} ghost>
          <Collapse.Panel header={this.renderHeader()} key="1">
            <Collapse defaultActiveKey={['input', 'output']} bordered={false}>
              <Collapse.Panel header="输入" key="input">
                <div className="nodrag">
                  {
                    this.props.data.needCustomInput ?
                      <EditableTable
                        datas={this.props.data?.inputs}
                        quoteSchemas={this.props.data?.quoteSchemas}
                        onChange={(e) => {
                          this.setState({ datas: e });
                          this.onChangeValueV2(e, 'inputs');
                        }}
                      /> :
                      <EditableTable
                        noDel
                        datas={datas}
                        onChange={(e) => {
                          this.setState({ datas: e });
                          this.onChangeValueV2(e, 'inputs');
                        }}
                        inputEnums={inputEnums}
                        inputSchema={this.props.data?.inputSchema}
                        quoteSchemas={this.props.data?.quoteSchemas}
                      />
                  }
                </div>
              </Collapse.Panel>
              <Collapse.Panel header="输出" key="output">
                <div className="nodrag">
                  {this.props.data.needCustomOutput ?
                    <VariableTable
                      datas={this.props.data?.outputs}
                      quoteSchemas={this.props.data?.quoteSchemas}
                      onChange={(e) => { this.onChangeData(e, 'outputs'); }}
                    /> :
                    <Menu mode="inline" items={menus} />
                  }
                </div>
              </Collapse.Panel>
            </Collapse>
          </Collapse.Panel>
        </Collapse>

        <Handle
          type="target"
          position={Position.Left}
          className="base-handle"
          style={{ left: -10, color: '#69f' }}
        />
        <Handle
          type="source"
          position={Position.Right}
          className="base-handle"
          style={{ right: -10 }}
        />
      </div>
    );
  }
}
