import { BranchesOutlined, DeleteOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>se, Divider, Input, Select, Table } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { <PERSON>le, Position } from 'reactflow';

import Configs from '../../Configs';
import Utils from '../../Utils';
import Header from './Header';

export default class Start extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
  }

  state = {
    datas: [],
  }

  componentDidMount = () => {
    const { data } = this.props;
    if (!_.isEmpty(data?.inputSchema)) {
      const inputSchema = {};
      _.map(data.inputSchema, (v, k) => { inputSchema[_.snakeCase(k)] = v; });
      let datas = Utils.schemaToObject(inputSchema);
      datas = Utils.initPath(datas);
      this.setState({ datas });
    }
  }

  onChangeSchema = (datas) => {
    const inputSchema = Utils.objectToSchema(datas);
    const { id, data } = this.props;
    data.onChange(id, { ...data, inputSchema, outputSchema: inputSchema });
  }

  onChangeValue = (e, innerPath, key) => {
    const value = e?.target ? e.target.value : e;
    const updateData = (data, path) => {
      if (path.length === 0) return { ...data, [key]: value };

      const [current, ...rest] = path;
      if (!data.children || !data.children[current]) {
        return data;
      }

      return {
        ...data,
        children: data.children.map((child, index) => {
          return (index === current ? updateData(child, rest) : child);
        }),
      };
    };

    const datas = this.state.datas.map((data, index) => {
      return (index === innerPath[0] ? updateData(data, innerPath.slice(1)) : data);
    });

    this.setState({ datas });
    this.onChangeSchema(datas);
  }

  onAdd = () => {
    const datas = _.cloneDeep(this.state.datas);
    datas.push({ value: '', type: '', desc: '', path: [datas.length] });
    this.setState({ datas });
    this.onChangeSchema(datas);
  }

  onAddChild = (parentPath = []) => {
    const newData = _.cloneDeep(this.state.datas);
    const addChild = (data, path) => {
      if (path.length === 0) {
        data.push({ value: '', type: '', desc: '', path: parentPath.concat(data.length) });
        return;
      }

      const index = path[0];
      if (!data[index].children) {
        data[index].children = []; // eslint-disable-line
      }
      addChild(data[index].children, path.slice(1));
    };
    addChild(newData, parentPath);
    this.setState({ datas: newData });
    this.onChangeSchema(newData);
  }

  onDel = (path = []) => {
    let current = _.cloneDeep(this.state.datas);

    for (let i = 0; i < path.length - 1; i++) {
      if (current[path[i]].children) {
        current = current[path[i]].children;
      }
    }

    current.splice(path[path.length - 1], 1);
    this.setState({ datas: current });
    this.onChangeSchema(current);
  }

  renderHeader = () => {
    return (<Header name="开始" id={this.props.id} data={this.props.data} />);
  }

  renderColumns = () => {
    return [
      {
        title: '变量名',
        dataIndex: 'value',
        key: 'value',
        align: 'center',
        render: (text, row) => {
          return (
            <Input
              size="small"
              value={text}
              placeholder="请输入变量名"
              onChange={(e) => { return this.onChangeValue(e, row.path, 'value'); }}
            />
          );
        },
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        align: 'center',
        render: (text, row) => {
          return (
            <Select
              size="small"
              value={text}
              style={{ width: 100 }}
              onChange={(e) => { return this.onChangeValue(e, row.path, 'type'); }}
            >
              {_.map(Configs.TYPES, (v, k) => { return <Select.Option key={k} value={k}>{v}</Select.Option>; })}
            </Select>
          );
        },
      },
      {
        title: '描述',
        dataIndex: 'desc',
        key: 'desc',
        align: 'center',
        render: (text, row) => {
          return (
            <Input
              size="small"
              value={text}
              placeholder="请输入描述"
              onChange={(e) => { return this.onChangeValue(e, row.path, 'desc'); }}
            />
          );
        },
      },
      {
        title: ' ',
        dataIndex: 'opet',
        key: 'opet',
        align: 'center',
        render: (text, row, idx) => {
          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <BranchesOutlined
                style={{
                  visibility: (['array', 'object'].includes(row?.type)) ? 'visible' : 'hidden',
                  transform: 'rotate(180deg)',
                }}
                onClick={() => { return this.onAddChild(row.path); }}
              />
              <Divider type="vertical" />
              <DeleteOutlined
                style={{ visibility: idx ? 'visible' : 'hidden' }}
                onClick={() => { return this.onDel(row.path); }}
              />
            </div>
          );
        },
      },
    ];
  }

  render = () => {
    const { datas } = this.state;

    return (
      <div className="start-node-wrap">
        <Collapse defaultActiveKey={['1']} ghost>
          <Collapse.Panel header={this.renderHeader()} key="1">
            <Card size="small" title="输入" bordered={false}>
              <div className="nodrag">
                <Table
                  size="small"
                  pagination={false}
                  columns={this.renderColumns()}
                  dataSource={datas}
                />

                <Button shape="round" size="small" onClick={() => { return this.onAdd(); }}>
                  新增
                </Button>
              </div>
            </Card>
          </Collapse.Panel>
        </Collapse>
        <Handle type="source" position={Position.Right} className="base-handle" style={{ right: -8 }} />
      </div>
    );
  }
}
