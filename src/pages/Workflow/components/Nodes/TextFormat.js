import { StringExtension } from '~/plugins';
import { <PERSON>, <PERSON><PERSON><PERSON>, Di<PERSON>r, <PERSON><PERSON>, <PERSON>u, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

import Utils from '../../Utils';
import EditableTable from '../EditableTable';
import Header from './Header';

const MENTION_PREFIX = '{';

export default class TextFormat extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
  }

  state = {
    subType: '',
  }

  componentDidMount = () => {
    const { inputs, name, inputSchema, outputSchema } = this.props.data;

    const stateParams = { subType: name, isSplit: name !== 'text_combiner' };

    if (!_.isEmpty(inputs)) {
      stateParams.datas = inputs;
    } else if (!_.isEmpty(inputSchema)) {
      stateParams.datas = Object.keys(inputSchema).map((key) => {
        const value = inputSchema[key].default;
        return { name: StringExtension.camelToSnake(key), type: 'value', value };
      });
    }

    if (!_.isUndefined(outputSchema)) {
      const o = Utils.schemaToObject(outputSchema);
      const menus = o.map((x) => {
        let children;
        if (x.children) {
          children = x.children.map((y) => {
            return { key: y.value, label: `${y.value} [${y.type}]` };
          });
        }
        return { key: x.value, label: `${x.value} [${x.type}]`, children };
      });
      stateParams.menus = menus;
    }
    this.setState(stateParams);
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const updatedData = { ...this.props.data };
    updatedData[key] = value;
    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
  }

  onChangeConfig = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const updatedData = { ...this.props.data };
    updatedData.configs[key] = value;
    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
  }

  onChangeOutput = ({ arrs, schema }) => {
    const updatedData = { ...this.props.data };
    updatedData.outputs = arrs;
    updatedData.outputSchema = schema;
    const { id, data } = this.props;
    data.onChange(id, updatedData);
  }

  onDelNode = (e) => {
    e.stopPropagation();
    const { id, data } = this.props;
    data.onDel(id);
  }

  onClickSetting = (e) => {
    e.stopPropagation();
    const { id, data } = this.props;
    data.onShowSetting(id);
  }

  renderHeader = () => {
    const { id, data } = this.props;
    return (
      <Header
        id={id}
        name={`文本${this.state.subType === 'text_combiner' ? '拼接' : '分隔'}`}
        data={data}
      />
    );
  }

  renderFormat = () => {
    const { name, configs, inputs } = this.props.data;
    const isSplit = name !== 'text_combiner';
    const opts = _.map(inputs, 'name').map((x) => { return { value: `{${x}}}`, label: x }; });

    return (
      <div>
        <Card size="small" title={isSplit ? '分隔符' : '拼接模版'} bordered={false}>
          {
            isSplit ?
              <Select
                mode="tags"
                style={{ width: '100%' }}
                placeholder="分隔符"
                options={[
                  { label: '换行(\\n)', value: '\n' },
                  { label: '制表符(\\t)', value: '\t' },
                  { label: '句号(。)', value: '。' },
                  { label: '逗号(，)', value: '，' },
                  { label: '分号(；)', value: '；' },
                  { label: '空格( )', value: ' ' },
                ]}
                value={configs?.separators}
                onChange={(e) => { this.onChangeConfig(e, 'separators'); }}
              /> :
              <Mentions
                autoSize={{ minRows: 3, maxRows: 5 }}
                style={{ width: '100%' }}
                placeholder="请输入拼接模版"
                value={configs.template}
                prefix={MENTION_PREFIX}
                onChange={(e) => { this.onChangeConfig(e, 'template'); }}
                options={opts}
              />
          }
        </Card>
      </div>
    );
  }

  render = () => {
    const { data } = this.props;
    return (
      <div className="end-node-wrap">
        <Collapse defaultActiveKey={['1']} ghost>
          <Collapse.Panel header={this.renderHeader()} key="1">
            <Card size="small" title="输入" bordered={false}>
              {
                this.state.isSplit ?
                  <EditableTable
                    noDel
                    datas={this.state.datas}
                    onChange={(e) => {
                      this.setState({ datas: e });
                      this.onChangeValue(e, 'inputs');
                    }}
                    inputSchema={data?.inputSchema}
                    quoteSchemas={data?.quoteSchemas}
                  /> :
                  <EditableTable
                    datas={data?.inputs}
                    quoteSchemas={data?.quoteSchemas}
                    onChange={(e) => { this.onChangeValue(e, 'inputs'); }}
                  />
              }
            </Card>
            <Divider style={{ margin: '5px 0' }} />
            {this.renderFormat()}
            <Divider style={{ margin: '5px 0' }} />
            <Card size="small" title="输出" bordered={false}>
              <div className="nodrag">
                <Menu mode="inline" items={this.state.menus} />
              </div>
            </Card>
          </Collapse.Panel>
        </Collapse>

        <Handle
          type="target"
          position={Position.Left}
          className="base-handle"
          style={{ left: -10, color: '#69f' }}
        />
        <Handle
          type="source"
          position={Position.Right}
          className="base-handle"
          style={{ right: -10 }}
        />
      </div>
    );
  }
}
