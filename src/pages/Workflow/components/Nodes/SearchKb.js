import { StringExtension } from '~/plugins';
import { <PERSON><PERSON>, Card, Collapse, Input, InputNumber, Menu, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

import Utils from '../../Utils';
import EditableTable from '../EditableTable';
import Header from './Header';

export default class SearchKb extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
  }

  state = {
    menus: [],
  }

  componentDidMount = () => {
    const { inputs, inputSchema, outputSchema } = this.props.data;

    const stateParams = {};
    if (!_.isEmpty(inputs)) {
      stateParams.datas = inputs;
    } else if (!_.isEmpty(inputSchema)) {
      stateParams.datas = Object.keys(inputSchema).map((key) => {
        const value = inputSchema[key].default;
        return { name: StringExtension.camelToSnake(key), type: 'value', value };
      });
    }

    if (!_.isUndefined(outputSchema)) {
      const o = Utils.schemaToObject(outputSchema);
      const menus = o.map((x) => {
        let children = [];
        if (x.children) {
          children = x.children.map((y) => {
            return { key: y.value, label: `${y.value} [${y.type}]` };
          });
        }
        return { key: x.value, label: `${x.value} [${x.type}]`, children };
      });
      stateParams.menus = menus;
    }
    this.setState(stateParams);
  }

  setNestedValue = (obj, path, value) => {
    const keys = path.split('.');
    let current = obj;
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
    return obj;
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const updatedData = { ...this.props.data };
    updatedData[key] = value;
    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
  }

  onChangeConfig = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const updatedData = this.setNestedValue({ ...this.props.data }, key, value);
    const { id, data } = this.props;
    data.onChange(id, updatedData);
  }

  onDelNode = (e) => {
    e.stopPropagation();
    const { id, data } = this.props;
    data.onDel(id);
  }

  onClickSetting = (e) => {
    e.stopPropagation();
    const { id, data } = this.props;
    data.onShowSetting(id);
  }

  renderHeader = () => {
    const { id, data } = this.props;
    return (<Header id={id} name="知识库" data={data} />);
  }

  render = () => {
    const { configs, libraries, quoteSchemas, inputSchema } = this.props.data;
    return (
      <div className="end-node-wrap kb-node-wrap">
        <Collapse defaultActiveKey={['1']} ghost>
          <Collapse.Panel header={this.renderHeader()} key="1">
            <Card size="small" title="输入" bordered={false}>
              <EditableTable
                noDel
                datas={this.state.datas}
                onChange={(e) => {
                  this.setState({ datas: e });
                  this.onChangeValue(e, 'inputs');
                }}
                inputSchema={inputSchema}
                quoteSchemas={quoteSchemas}
              />
            </Card>
            <Card size="small" title="参数" bordered={false} className="nodrag">
              <InputNumber
                size="small"
                addonBefore={<div style={{ width: 80 }}>最大召回数</div>}
                min={1}
                max={100}
                step={1}
                value={configs?.topK}
                onChange={(e) => { return this.onChangeConfig(e, 'configs.topK'); }}
                placeholder="最大召回数"
                style={{ margin: '5px 0' }}
              />
              <InputNumber
                size="small"
                addonBefore={<div style={{ width: 80 }}>最小匹配度</div>}
                min={0.1}
                max={1}
                step={0.1}
                value={configs?.threshold}
                onChange={(e) => { return this.onChangeConfig(e, 'configs.threshold'); }}
                placeholder="最小匹配度"
              />
              <Input.Group style={{ margin: '5px 0', width: '100%' }}>
                <Button size="small" style={{ width: 103 }}>知识库</Button>
                <Select
                  size="small"
                  value={configs?.libraryId}
                  style={{ width: 'calc(100% - 105px)' }}
                  onChange={(e) => { return this.onChangeConfig(e, 'configs.libraryId'); }}
                >
                  {
                    (libraries || []).map((x) => {
                      return <Select.Option value={x.id}>{x.field}</Select.Option>;
                    })
                  }
                </Select>
              </Input.Group>
            </Card>
            <Card size="small" title="输出" bordered={false}>
              <div className="nodrag">
                <Menu mode="inline" items={this.state.menus} />
              </div>
            </Card>
          </Collapse.Panel>
        </Collapse>

        <Handle
          type="target"
          position={Position.Left}
          className="base-handle"
          style={{ left: -10, color: '#69f' }}
        />
        <Handle
          type="source"
          position={Position.Right}
          className="base-handle"
          style={{ right: -10 }}
        />
      </div>
    );
  }
}
