/* eslint-disable guard-for-in */
import { CheckOutlined, DeleteOutlined, EditOutlined, SettingOutlined, WarningOutlined } from '@ant-design/icons';
import { json } from '@codemirror/lang-json';
import CodeMirror from '@uiw/react-codemirror';
import { Toast } from '~/components';
import Utils from '~/pages/Playground/Utils';
import { Avatar, Button, Card, Collapse, Drawer, Form, Input, InputNumber, Popconfirm, Select, Switch } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

import Const from '../../Configs';
import NUtil from '../../Utils';
import EditableTable from '../EditableTable';
import VariableTable from '../VariableTable';

export default class Default extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
    onChange: PropTypes.func,
  }

  state = {
    openErr: false,
    isEditName: false,
    className: '',
    errorConfig: {},
  }

  componentDidMount = () => {
    const { id, data } = this.props;
    const iconName = data.oType || _.head(Utils.formatSubtype(id));
    let className = iconName.split(/(?=[A-Z])/).map((x) => { return _.lowerCase(x); }).join('-');
    className = _.replace(className, / /g, '');
    this.setState({ className });
  }

  setNestedValue = (obj, path, value) => {
    const keys = path.split('.');
    let current = obj;
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
    return obj;
  }

  processObject = (obj) => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) { // eslint-disable-line
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          this.processObject(obj[key]);
        }
        if (key === 'default') {
          delete obj[key]; // eslint-disable-line
        }
        if (key === 'required') {
          obj[key] = true;// eslint-disable-line
        }
      }
    }
    return obj;
  }

  findNodeByPath = (data, path) => {
    const pathParts = path.split('.');
    let currentNode = data.find((node) => { return node.value === pathParts[0]; });

    for (let i = 1; i < pathParts.length; i++) {
      if (!currentNode || !currentNode.children) {
        return null;
      }
      currentNode = currentNode.children.find((node) => { return node.label === pathParts[i]; });
    }

    return currentNode;
  }

  getQuoteSchemas = () => {
    const { batch, quoteSchemas } = this.props.data;
    if (!batch?.batchEnable) {
      return quoteSchemas;
    }
    const arrs = quoteSchemas.map((x) => {
      const children = x.children.filter((xc) => { return !_.endsWith(xc.label, '[]'); });
      return { ...x, children };
    });

    const quotes = [...arrs];
    for (const item of (batch?.items || [])) {
      const data = this.findNodeByPath(arrs, item.path);
      quotes.push({
        value: item.name,
        label: item.name,
        type: data?.type,
        desc: '',
        children: data?.children || [],
      });
    }
    return quotes;
  }

  onDelNode = (e) => {
    e.stopPropagation();
    const { id, data } = this.props;
    data.onDel(id);
  }

  onClickSetting = (e) => {
    e.stopPropagation();
    const { id, data } = this.props;
    data.onShowSetting(id);
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const updatedData = this.setNestedValue({ ...this.props.data }, key, value);
    const { id, data } = this.props;
    data.onChange(id, updatedData);
  }

  onChangeOutput = ({ arrs, schema }, baseData = null) => {
    const updatedData = { ...(baseData || this.props.data) };
    updatedData.outputs = arrs;
    updatedData.outputSchema = schema;

    const jsonSchema = NUtil.transformFormat(schema);
    if (jsonSchema) {
      delete jsonSchema.llmAdditionalInfo;
      delete jsonSchema.llm_additional_info;
    }
    const jsonPrompt = `${Const.JSON_PROMPT_PREFIX}\n${JSON.stringify(jsonSchema, null, 2)}`;
    const newData = this.setNestedValue(updatedData, 'configs.llmSetting.jsonPrompt', jsonPrompt);
    updatedData.onChange(this.props.id, newData);
  }

  onChangeEditName = (e) => {
    e.stopPropagation();
    this.setState({ isEditName: !this.state.isEditName });
  }

  onChangeResponseFormat = (e) => {
    if (e === 'text') {
      const { arrs, schema } = this.props.data.configs.otherConfig.needAdditionalInfo
        ? _.cloneDeep(Const.BOT_ADDITIONAL_DEFAULT_OUTPUT)
        : _.cloneDeep(Const.BOT_DEFAULT_OUTPUT);
      this.outputTable.refresh(arrs);
      this.onChangeOutput({ arrs, schema });
    }

    this.onChangeValue(e, 'configs.llmSetting.responseFormat');
  }

  onChangeNeedAdditionalInfo = (e) => {
    const { data } = this.props;
    const updatedData = { ...data };
    updatedData.configs.otherConfig.needAdditionalInfo = e;

    // 获取当前的输出配置
    const currentOutputs = _.cloneDeep(data.outputs || []);
    const currentSchema = _.cloneDeep(data.outputSchema || {});

    let newArrs; let
      newSchema;

    if (e) {
      // 启用额外信息：检查是否已存在 llm_additional_info
      const hasAdditionalInfo = currentOutputs.some((item) => { return item.value === 'llm_additional_info'; });

      if (!hasAdditionalInfo) {
        // 如果没有现有配置，使用默认配置
        if (currentOutputs.length === 0 || (currentOutputs.length === 1 && !currentOutputs[0].value)) {
          const { arrs, schema } = _.cloneDeep(Const.BOT_ADDITIONAL_DEFAULT_OUTPUT);
          newArrs = arrs;
          newSchema = schema;
        } else {
          // 保留现有配置，只添加额外信息字段
          newArrs = [...currentOutputs, {
            value: 'llm_additional_info',
            type: 'object',
            desc: '额外信息',
            path: [currentOutputs.length],
          }];
          newSchema = {
            ...currentSchema,
            llm_additional_info: {
              description: '额外信息',
              required: false,
              default: '',
              type: 'object',
            },
          };
        }
      } else {
        // 已存在，不做修改
        newArrs = currentOutputs;
        newSchema = currentSchema;
      }
    } else {
      // 禁用额外信息：移除 llm_additional_info
      newArrs = currentOutputs.filter((item) => { return item.value !== 'llm_additional_info'; });
      newSchema = { ...currentSchema };
      delete newSchema.llm_additional_info;

      // 重新计算 path
      newArrs = newArrs.map((item, index) => { return { ...item, path: [index] }; });

      // 如果移除后没有配置了，使用默认的基础配置
      if (newArrs.length === 0) {
        const { arrs, schema } = _.cloneDeep(Const.BOT_DEFAULT_OUTPUT);
        newArrs = arrs;
        newSchema = schema;
      }
    }

    this.outputTable.refresh(newArrs);
    this.onChangeOutput({ arrs: newArrs, schema: newSchema }, updatedData);
  }

  onOpenErrDrawer = () => {
    const { errorConfig, outputSchema } = this.props.data;
    const config = errorConfig || {
      skipError: false, defaultResult: NUtil.transformFormat(outputSchema),
    };

    if (!_.isString(config.defaultResult)) {
      try {
        config.defaultResult = JSON.stringify(config.defaultResult, null, 2);
      } catch (e) {
        config.defaultResult = '';
      }
    }
    this.setState({ openErr: true, errorConfig: config });
  }

  onChangeErrValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ errorConfig: { ...this.state.errorConfig, [key]: value } });
  }

  onSave = () => {
    const errorConfig = _.cloneDeep(this.state.errorConfig);

    try {
      errorConfig.defaultResult = JSON.parse(errorConfig.defaultResult);
    } catch (e) {
      Toast.show('默认输出必须为JSON格式', Toast.Type.ERROR);
      return;
    }

    const { id, data } = this.props;
    data.onChange(id, { ...data, errorConfig });
    this.setState({ openErr: false });
  }

  renderErrDrawer = () => {
    const { errorConfig } = this.state;
    return (
      <Drawer
        width="30vw"
        title="异常设置"
        placement="right"
        maskClosable={false}
        open={this.state.openErr}
        onClose={() => { return this.setState({ openErr: false }); }}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item label="异常忽略" help="忽略异常并在异常发生时使用默认输出替代">
            <Switch
              checked={errorConfig?.skipError}
              onChange={(e) => { return this.onChangeErrValue(e, 'skipError'); }}
            />
          </Form.Item>
          {
            errorConfig?.skipError &&
            <Form.Item label="默认输出" required>
              <CodeMirror
                id="err"
                value={errorConfig?.defaultResult}
                height="60vh"
                extensions={[json()]}
                onChange={(e) => { return this.onChangeErrValue(e, 'defaultResult'); }}
              />
            </Form.Item>
          }
          <Form.Item colon={false} style={{ textAlign: 'center' }}>
            <Button type="primary" onClick={() => { return this.onSave(); }}>保存</Button>
          </Form.Item>
        </Form>
      </Drawer>
    );
  }

  renderHeader = () => {
    return (
      <div
        onClick={(e) => { e.stopPropagation(); }}
        style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
      >
        <span style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar size={16} shape="square" style={{ marginRight: 10 }} src="/static/v2/icon-bot.png" />
          {
            this.state.isEditName ?
              <Input
                size="small"
                bordered={false}
                value={this.props.data?.displayName}
                onChange={(e) => { return this.onChangeValue(e, 'displayName'); }}
                onClick={(e) => { e.stopPropagation(); }}
              />
              : <>{this.props.data?.displayName || this.props.data?.name || 'Bot'}</>
          }
          <span onClick={(e) => { return this.onChangeEditName(e); }} style={{ marginLeft: 10, cursor: 'pointer' }}>
            {this.state.isEditName ? <CheckOutlined /> : <EditOutlined />}
          </span>
        </span>

        <span>
          <WarningOutlined onClick={() => { return this.onOpenErrDrawer(); }} />
          <Popconfirm title="确认删除该节点吗？" onConfirm={(e) => { return this.onDelNode(e); }}>
            <DeleteOutlined style={{ margin: '0 10px' }} />
          </Popconfirm>
          <SettingOutlined onClick={(e) => { return this.onClickSetting(e); }} />
        </span>
      </div>
    );
  }

  renderOutputHeader = (data) => {
    return (
      <div
        className="nodrag"
        style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
      >
        <span>输出{data?.batch?.batchEnable ? '[outputs]' : ''}</span>
        <div onClick={(e) => { e.stopPropagation(); }} style={{ display: 'flex', alignItems: 'center' }}>
          <Switch
            size="small"
            checked={data?.configs?.otherConfig?.needAdditionalInfo}
            onChange={(e) => { return this.onChangeNeedAdditionalInfo(e); }}
          />
          <Select
            size="small"
            bordered={false}
            value={data?.configs?.llmSetting?.responseFormat}
            onClick={(e) => { e.stopPropagation(); }}
            onChange={(e) => { return this.onChangeResponseFormat(e); }}
          >
            <Select.Option value="text">文本</Select.Option>
            <Select.Option value="json_object">JSON</Select.Option>
          </Select>
        </div>
      </div>
    );
  }

  renderCardContent = (data) => {
    return (
      <Collapse defaultActiveKey={['1', '2', '3']} bordered={false}>
        {
          data?.batch?.batchEnable &&
          <Collapse.Panel header="批处理" key="4">
            <Input.Group compact style={{ display: 'flex', marginBottom: 5 }} className="nodrag">
              <InputNumber
                size="small"
                addonBefore="运行次数"
                value={data?.batch?.batchSize}
                onChange={(e) => { this.onChangeValue(e, 'batch.batchSize'); }}
              />
              <InputNumber
                size="small"
                addonBefore="并发"
                value={data?.batch?.concurrentSize}
                onChange={(e) => { this.onChangeValue(e, 'batch.concurrentSize'); }}
              />
            </Input.Group>
            <EditableTable
              isCascader
              datas={data?.batch?.items}
              onChange={(e) => { this.onChangeValue(e, 'batch.items'); }}
              quoteSchemas={data?.quoteSchemas}
            />
          </Collapse.Panel>
        }
        <Collapse.Panel header="输入" key="2">
          <EditableTable
            datas={data?.inputs || Const.DEFAULT_OUTPUT}
            onChange={(e) => { this.onChangeValue(e, 'inputs'); }}
            quoteSchemas={this.getQuoteSchemas()}
          />
        </Collapse.Panel>
        <Collapse.Panel header="角色" key="1">
          <Input.Group compact style={{ display: 'flex', alignItems: 'center' }} className="nodrag">
            名称:
            <Button type="link" onClick={(e) => { return this.onClickSetting(e); }} >
              {data?.displayName}
            </Button>
          </Input.Group>
          <Input.Group compact style={{ display: 'flex', alignItems: 'center' }} className="nodrag">
            模型:
            <Button type="link" onClick={(e) => { return this.onClickSetting(e); }} >
              {data?.configs?.llmSetting?.model}
            </Button>
          </Input.Group>
        </Collapse.Panel>
        <Collapse.Panel header={this.renderOutputHeader(data)} key="3">
          <VariableTable
            key={data?.batch?.batchEnable ? 'batch' : 'single'}
            isBatch={data?.batch?.batchEnable}
            ref={(ref) => { this.outputTable = ref; }}
            canAdd={data?.configs?.llmSetting?.responseFormat === 'json_object'}
            datas={data?.outputs}
            onChange={(e) => { return this.onChangeOutput(e); }}
          />
        </Collapse.Panel>
      </Collapse>
    );
  }

  render = () => {
    const { data } = this.props;
    const { className } = this.state;
    return (
      <div className={`common-node-wrap ${className}`}>
        <Collapse defaultActiveKey={['1']} ghost>
          <Collapse.Panel header={this.renderHeader()} key="1">
            <Card
              size="small"
              bordered={false}
              style={{ width: '100%' }}
              tabList={[
                { tab: '单次', key: 'single' },
                { tab: '批处理', key: 'batch' },
              ]}
              activeTabKey={data?.batch?.batchEnable ? 'batch' : 'single'}
              onTabChange={() => { this.onChangeValue(!data?.batch?.batchEnable, 'batch.batchEnable'); }}
            >
              {this.renderCardContent(data)}
            </Card>
          </Collapse.Panel>
        </Collapse>

        <Handle
          type="target"
          position={Position.Left}
          className="base-handle"
          style={{ left: -10, color: '#69f' }}
        />
        <Handle
          type="source"
          position={Position.Right}
          className="base-handle"
          style={{ right: -10 }}
        />
        {this.state.openErr && this.renderErrDrawer()}
      </div>
    );
  }
}
