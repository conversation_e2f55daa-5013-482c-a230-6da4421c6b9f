import { Card, Collapse, Form, Mentions, Switch } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

import EditableTable from '../EditableTable';
import Header from './Header';

const MENTION_PREFIX = '{';

export default class End extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
  }

  setNestedValue = (obj, path, value) => {
    const keys = path.split('.');
    let current = obj;
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
    return obj;
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const updatedData = { ...this.props.data };
    updatedData[key] = value;
    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
  }

  onChangeConfig = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const updatedData = this.setNestedValue({ ...this.props.data }, key, value);
    const { id, data } = this.props;
    data.onChange(id, updatedData);
  }

  renderHeader = () => {
    return (<Header name="结束" id={this.props.id} data={this.props.data} />);
  }

  render = () => {
    const { data } = this.props;
    const opts = _.map(data?.inputs, 'name').map((x) => { return { value: `{${x}}}`, label: x }; });

    return (
      <div className="end-node-wrap">
        <Collapse defaultActiveKey={['1']} ghost >
          <Collapse.Panel header={this.renderHeader()} key="1">
            <Form.Item label="使用模版输出">
              <Switch
                size="small"
                checked={data?.configs?.useTpl}
                onChange={(e) => { return this.onChangeConfig(e, 'configs.useTpl'); }}
              />
            </Form.Item>
            <Card size="small" title="输出" bordered={false}>
              <EditableTable
                datas={data?.inputs}
                quoteSchemas={data?.quoteSchemas}
                onChange={(e) => { this.onChangeValue(e, 'inputs'); }}
              />
            </Card>
            {
              data?.configs?.useTpl &&
              <Card size="small" title="模版" bordered={false}>
                <Mentions
                  prefix={MENTION_PREFIX}
                  autoSize={{ minRows: 3, maxRows: 5 }}
                  defaultValue={data?.configs?.resultTpl}
                  onChange={(e) => { return this.onChangeConfig(e, 'configs.resultTpl'); }}
                  options={opts}
                />
              </Card>
            }
          </Collapse.Panel>
        </Collapse>

        <Handle
          type="target"
          position={Position.Left}
          className="base-handle"
          style={{ left: -4, top: 23, color: '#69f' }}
        />
      </div>
    );
  }
}
