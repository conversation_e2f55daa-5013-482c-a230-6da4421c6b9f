import { DeleteOutlined } from '@ant-design/icons';
import { StringExtension } from '~/plugins';
import {
  <PERSON>ton,
  Card,
  Checkbox,
  Collapse,
  Divider,
  Input,
  Mentions,
  Menu,
  Popconfirm,
  Select,
  Table,
  Typography,
} from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

import Utils from '../../Utils';
import EditableTable from '../EditableTable';
import Header from './Header';

export default class Variable extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
  }

  state = {
    types: [],
    menus: [],
  }

  componentDidMount = () => {
    const stateParams = {};
    stateParams.types = 'str/int/float/bool/json'.split('/');

    if (!_.isUndefined(this.props.data.outputSchema)) {
      const o = Utils.schemaToObject(this.props.data.outputSchema);
      const menus = o.map((x) => {
        const menu = { key: x.value, label: `${x.value} [${x.type}]` };
        let children = [];
        if (x.children) {
          children = x.children.map((y) => {
            return { key: y.value, label: `${y.value} [${y.type}]` };
          });
        }
        if (!_.isEmpty(children)) {
          menu.children = children;
        }
        return menu;
      });
      stateParams.menus = menus;
    }
    this.setState(stateParams);
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const updatedData = { ...this.props.data };
    updatedData[key] = value;
    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
  }

  onDelNode = (e) => {
    e.stopPropagation();
    const { id, data } = this.props;
    data.onDel(id);
  }

  onClickSetting = (e) => {
    e.stopPropagation();
    const { id, data } = this.props;
    data.onShowSetting(id);
  }

  onChangeVariable = (e, key, idx) => {
    const value = _.trim(e?.target ? e.target.value : e);
    const updatedData = { ...this.props.data };
    updatedData.configs.variables[idx][key] = value;

    if (key === 'value' && _.startsWith(value, '{{') && _.endsWith(value, '}}')) {
      const schemaKey = StringExtension.snakeToCamel(_.trim(value, '{{}}'));
      const schema = updatedData.inputSchema;
      if (_.isEmpty(schema)) {
        const newInputs = Utils.fillInputTypes(updatedData.inputs, updatedData.quoteSchemas);
        (newInputs || []).forEach((xi) => { schema[xi.name] = { type: xi.fieldType || 'str' }; });
      }
      const { type: schemaType } = schema[schemaKey];
      updatedData.configs.variables[idx].type = schemaType;
    }

    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
  }

  onAddVariable = () => {
    const updatedData = { ...this.props.data };
    updatedData.configs.variables.push({
      name: '',
      type: 'str',
      value: '',
      persist: false,
    });
    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
  }

  onDelVariable = (idx) => {
    const updatedData = { ...this.props.data };
    updatedData.configs.variables.splice(idx, 1);
    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
  }

  renderHeader = () => {
    const { id, data } = this.props;
    return (<Header id={id} name="变量" data={data} />);
  }

  renderColumns = () => {
    return [
      {
        title: '变量名',
        dataIndex: 'name',
        key: 'name',
        render: (text, row, idx) => {
          return (
            <Input size="small" value={text} onChange={(e) => { this.onChangeVariable(e, 'name', idx); }} />
          );
        },
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        render: (text, row, idx) => {
          return (
            <Select
              size="small"
              value={text}
              style={{ width: 80 }}
              onChange={(e) => { this.onChangeVariable(e, 'type', idx); }}
            >
              {
                this.state.types.map((type) => {
                  return (
                    <Select.Option key={type} value={type}>{type}</Select.Option>
                  );
                })
              }
            </Select>
          );
        },
      },
      {
        title: '值',
        dataIndex: 'value',
        key: 'value',
        render: (text, row, idx) => {
          return (
            <Mentions
              size="small"
              value={text}
              prefix="{"
              options={_.map(this.props.data?.inputs, 'name').map((x) => {
                return { label: x, value: `{${x}}}` };
              })}
              onChange={(e) => { this.onChangeVariable(_.trim(e), 'value', idx); }}
            />
          );
        },
      },
      {
        title: '持久化',
        dataIndex: 'persist',
        key: 'persist',
        align: 'center',
        width: 50,
        render: (text, row, idx) => {
          return (
            <div style={{ display: 'flex' }}>
              <Checkbox
                checked={text}
                onChange={(e) => { this.onChangeVariable(e.target.checked, 'persist', idx); }}
              />
              <Divider type="vertical" />
              <Popconfirm title="确认删除?" onConfirm={() => { this.onDelVariable(idx); }}>
                <DeleteOutlined />
              </Popconfirm>
            </div>
          );
        },
      },
    ];
  }

  renderTable = () => {
    return (
      <div className="nodrag">
        <Table
          pagination={false}
          columns={this.renderColumns()}
          dataSource={[...this.props.data?.configs.variables]}
          size="small"
        />

        <Button
          shape="round"
          size="small"
          style={{ marginTop: 5 }}
          onClick={() => { return this.onAddVariable(); }}
        >
          新增
        </Button>
      </div>
    );
  }

  render = () => {
    return (
      <div className="end-node-wrap">
        <Collapse defaultActiveKey={['1']} ghost >
          <Collapse.Panel header={this.renderHeader()} key="1">
            <Card size="small" title={<Typography.Title level={5}>输入</Typography.Title>} bordered={false}>
              <EditableTable
                datas={this.props.data?.inputs}
                quoteSchemas={this.props.data?.quoteSchemas}
                onChange={(e) => { this.onChangeValue(e, 'inputs'); }}
              />
            </Card>
            <Typography.Title level={5}>变量定义</Typography.Title>
            {this.renderTable()}
            <Card size="small" title="输出" bordered={false}>
              <div className="nodrag">
                <Menu mode="inline" items={this.state.menus} />
              </div>
            </Card>
          </Collapse.Panel>
        </Collapse>

        <Handle
          type="target"
          position={Position.Left}
          className="base-handle"
          style={{ left: -10, color: '#69f' }}
        />
        <Handle
          type="source"
          position={Position.Right}
          className="base-handle"
          style={{ right: -10 }}
        />
      </div>
    );
  }
}
