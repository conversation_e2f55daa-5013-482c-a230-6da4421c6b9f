/* eslint-disable react/no-array-index-key */
import { DeleteOutlined } from '@ant-design/icons';
import { Platform, StringExtension } from '~/plugins'; // eslint-disable-line
import { Button, Card, Collapse, Input, Select, Typography } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { Handle, Position } from 'reactflow';

import Utils from '../../Utils';
import EditableTable from '../EditableTable';
import Header from './Header';

const CONDITIONS = [
  { value: 'equal_to', label: '等于' },
  { value: 'greater_than', label: '大于' },
  { value: 'less_than', label: '小于' },
  { value: 'greater_than_or_equal_to', label: '大于等于' },
  { value: 'less_than_or_equal_to', label: '小于等于' },
  { value: 'starts_with', label: '开始于' },
  { value: 'ends_with', label: '结束于' },
  { value: 'contains', label: '包含' },
  { value: 'matches_regex', label: '匹配正则' },
  { value: 'non_empty', label: '非空' },
  { value: 'is_true', label: '为真' },
  { value: 'is_false', label: '为假' },
  { value: 'does_not_contain', label: '不包含' },
  { value: 'contain', label: '包含值' },
];

export default class Condition extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    data: PropTypes.object,
  }

  state = {
    activeKey: [],
  }

  componentDidMount = () => {
    const { configs } = this.props.data;
    let events = _.isEmpty(configs.events) ? [{}] : configs.events;
    events = events.filter((x) => { return !_.isEmpty(x.config?.conditions); });
    const activeKey = ['1'];
    events.forEach((x, idx) => {
      activeKey.push(String(idx + 2));
    });
    this.setState({ activeKey });
  }

  getHeader = (idx) => {
    const text = idx === 0 ? '如果' : '否则如果';
    return (
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <span>{text}</span>
        {!!idx &&
          <DeleteOutlined
            style={{ cursor: 'pointer', marginRight: 20 }}
            onClick={(e) => { return this.onDelCondition(e, idx); }}
          />
        }
      </div>
    );
  };

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    const updatedData = { ...this.props.data };
    updatedData[key] = value;
    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
  }

  onDelNode = (e) => {
    e.stopPropagation();
    const { id, data } = this.props;
    data.onDel(id);
  }

  onClickSetting = (e) => {
    e.stopPropagation();
    const { id, data } = this.props;
    data.onShowSetting(id);
  }

  onChangeConditionValue = (e, key, type, idx, sIdx) => {
    const value = e?.target ? e.target.value : e;
    const updatedData = { ...this.props.data };
    updatedData.configs.events[sIdx].config.conditions[type][idx][key] = value;
    if (key === 'value') {
      const schemaKey = StringExtension.snakeToCamel(_.trim(value, '{{}}'));
      const schema = updatedData.inputSchema;
      if (_.isEmpty(schema)) {
        const newInputs = Utils.fillInputTypes(updatedData.inputs, updatedData.quoteSchemas);
        (newInputs || []).forEach((xi) => { schema[xi.name] = { type: xi.fieldType || 'str' }; });
      }

      const { type: schemaType } = schema[schemaKey];
      const currentObj = updatedData.configs.events[sIdx].config.conditions[type][idx];
      updatedData.configs.events[sIdx].config.conditions[type][idx] = { ...currentObj, type: schemaType };
    }
    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
  }

  onDelCondition = (e, idx) => {
    e.stopPropagation();
    const updatedData = { ...this.props.data };
    updatedData.configs.events.splice(idx, 1);
    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
  }

  onAddCondition = () => {
    const updatedData = { ...this.props.data };
    updatedData.configs.events.push({ config: { conditions: { all: [], any: [] } } });
    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
    Platform.emit('UPDATE_EDGE_PATH', '');
  }

  onChangeConditionEventType = (type, sIdx) => {
    const updatedData = { ...this.props.data };
    const oldKey = type === 'all' ? 'any' : 'all';
    const oldVal = _.cloneDeep(updatedData.configs.events[sIdx].config.conditions[oldKey]);
    updatedData.configs.events[sIdx].config.conditions[type] = oldVal;
    updatedData.configs.events[sIdx].config.conditions[oldKey] = undefined;
    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
  }

  onDelConditionEvent = (key, idx, sIdx) => {
    const updatedData = { ...this.props.data };
    updatedData.configs.events[sIdx].config.conditions[key].splice(idx, 1);
    const { id } = this.props;
    this.props.data.onChange(id, updatedData);
  }

  onAddConditionEvent = (key, sIdx) => {
    const updatedData = { ...this.props.data };
    const events = updatedData.configs.events.filter((x) => { return !_.isEmpty(x.config?.conditions); });
    events[sIdx].config.conditions[key].push({ name: '', operator: '', value: '', type: 'str' });
    const defaultEvent = updatedData.configs.events.find((x) => { return _.isEmpty(x.config?.conditions); });
    if (defaultEvent) {
      events.push(defaultEvent);
    }
    updatedData.configs.events = events;
    const { id, data } = this.props;
    data.onChange(id, updatedData);
    Platform.emit('UPDATE_EDGE_PATH', '');
  }

  renderHeader = () => {
    const { id, data } = this.props;
    return (
      <div onClick={(e) => { e.stopPropagation(); }} style={{ padding: '5px 10px' }}>
        <Header id={id} name="条件分支" data={data} />
      </div>
    );
  }

  render = () => {
    if (_.isEmpty(this.state.activeKey)) return null;
    const { inputs, quoteSchemas, configs } = this.props.data;
    const inputKeys = _.map(inputs, 'name');
    let events = _.isEmpty(configs.events) ? [{}] : configs.events;
    events = events.filter((x) => { return !_.isEmpty(x.config?.conditions); });
    return (
      <div className="end-node-wrap condition-node-wrap" >
        <Card size="small" title={this.renderHeader()} bordered={false}>
          <Collapse ghost defaultActiveKey={this.state.activeKey}>
            <Collapse.Panel header="输入" key="1" disabled>
              <EditableTable
                datas={inputs}
                quoteSchemas={quoteSchemas}
                onChange={(e) => { this.onChangeValue(e, 'inputs'); }}
              />
            </Collapse.Panel>
            <div style={{ display: 'flex', justifyContent: 'space-between', padding: '0 20px' }}>
              <Typography.Title level={5}>条件分支</Typography.Title>
              <Button size="small" type="link" onClick={this.onAddCondition}>添加分支</Button>
            </div>
            {
              events.map((x, idx) => {
                const key = _.isEmpty(x?.config?.conditions?.all || []) ? 'any' : 'all';
                return (
                  <Collapse.Panel
                    disabled
                    header={this.getHeader(idx)}
                    key={idx + 2}
                    extra={
                      <Handle
                        id={`case_${idx + 1}`}
                        type="source"
                        className="base-handle"
                        style={{ right: 0, left: 'auto', top: 16 }}
                        position={Position.Right}
                      />
                    }
                  >
                    <div
                      className="nodrag"
                      style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
                    >
                      <Select
                        value={key}
                        style={{ marginRight: 10 }}
                        onChange={(e) => { return this.onChangeConditionEventType(e, idx); }}
                      >
                        <Select.Option value="any">或</Select.Option>
                        <Select.Option value="all">且</Select.Option>
                      </Select>
                      <div style={{ width: '100%' }}>
                        {
                          ((x?.config?.conditions || {})[key] || []).map((y, yIdx) => {
                            return (
                              <Input.Group
                                compact
                                key={yIdx}
                                style={{ marginBottom: 5, display: 'flex', alignItems: 'center' }}
                              >
                                <Select
                                  size="small"
                                  value={y.name}
                                  style={{ width: 'calc(50% - 60px)' }}
                                  placeholder="请输入字段"
                                  options={inputKeys.map((z) => { return { label: z, value: z }; })}
                                  onChange={(e) => { return this.onChangeConditionValue(e, 'name', key, yIdx, idx); }}
                                />
                                <Select
                                  size="small"
                                  style={{ width: '100px' }}
                                  placeholder="操作符"
                                  options={CONDITIONS}
                                  value={y.operator}
                                  onChange={(e) => {
                                    return this.onChangeConditionValue(e, 'operator', key, yIdx, idx);
                                  }}
                                />
                                <Select
                                  size="small"
                                  style={{ width: 'calc(50% - 60px)' }}
                                  placeholder="请输入值"
                                  options={inputKeys.map((z) => { return { label: z, value: `{{${z}}}` }; })}
                                  value={y.value}
                                  onChange={(e) => { return this.onChangeConditionValue(e, 'value', key, yIdx, idx); }}
                                />
                                <DeleteOutlined
                                  style={{ marginLeft: 10 }}
                                  onClick={() => { return this.onDelConditionEvent(key, yIdx, idx); }}
                                />
                              </Input.Group>
                            );
                          })
                        }
                        <Button
                          size="small"
                          type="dashed"
                          block
                          style={{ width: 'calc(100% - 10px)' }}
                          onClick={() => { return this.onAddConditionEvent(key, idx); }}
                        >
                          添加条件
                        </Button>
                      </div>
                    </div>
                  </Collapse.Panel>
                );
              })
            }
            <Collapse.Panel
              showArrow={false}
              header="否则"
              key="default"
              extra={
                <Handle
                  id="default"
                  type="source"
                  className="base-handle"
                  // key={`${keyStr}-default`}
                  style={{ right: 0, left: 'auto', top: 16 }}
                  position={Position.Right}
                />
              }
            />
          </Collapse>

          <Handle
            type="target"
            position={Position.Left}
            // position={{ x: 100, y: 100 }}
            className="base-handle"
            style={{ left: -4, top: 23, color: '#69f' }}
          />
        </Card>
      </div>
    );
  }
}
