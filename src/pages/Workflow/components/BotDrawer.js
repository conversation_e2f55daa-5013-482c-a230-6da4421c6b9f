import { Toast } from '~/components';
import { Platform } from '~/plugins';
import { <PERSON><PERSON>, <PERSON>, Drawer, List, Typography } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class BotDrawer extends PureComponent {
  static propTypes = {
    type: PropTypes.string,
    assistants: PropTypes.array,
    open: PropTypes.bool,
    onClose: PropTypes.func,
  };

  state = {
  }

  componentDidMount = () => {
  }

  onSelectBot = async (item) => {
    let params = { ...item, type: this.props.type };
    if (this.props.type === 'sub_workflow') {
      const { nodes } = (JSON.parse(item.content) || {});
      if (_.isEmpty(nodes)) {
        Toast.show('子流程未发布!', Toast.Type.WARNING);
        return;
      }

      const start = _.find(nodes, { type: 'start' });
      const end = _.find(nodes, { type: 'end' });
      const inputSchema = start?.data?.input_schema;
      const outputSchema = end?.data?.output_schema;
      params = {
        configs: { workflowUuid: item.uuid },
        name: item.name,
        type: this.props.type,
        inputSchema,
        outputSchema,
      };
    }

    Platform.emit('ADD_NODE', params);
    this.props.onClose();
  }

  renderDrawerTitle = () => {
    const { type } = this.props; // bot, plugin, sub_workflow
    let title = '';
    switch (type) {
      case 'bot':
        title = 'Bots';
        break;
      case 'plugin':
        title = 'Plugins';
        break;
      case 'sub_workflow':
        title = 'Subflows';
        break;
      default:
        break;
    }
    return title;
  }

  renderItemTitle = (item) => {
    const { type } = this.props; // bot, plugin, sub_workflow
    let title = '';
    switch (type) {
      case 'bot':
        title = item.name;
        break;
      case 'plugin':
        title = item.displayName;
        break;
      case 'sub_workflow':
        title = item.name;
        break;
      default:
        break;
    }
    return title;
  }

  renderItemContent = (item) => {
    const { type } = this.props; // bot, plugin, sub_workflow
    let content = '';
    switch (type) {
      case 'bot':
        content = item?.llmSetting?.systemPrompt;
        break;
      case 'plugin':
        content = item?.description;
        break;
      case 'sub_workflow':
        content = item?.description;
        break;
      default:
        break;
    }
    return content;
  }

  render = () => {
    const { assistants, open } = this.props;

    return (
      <Drawer
        maskClosable
        title={this.renderDrawerTitle()}
        onClose={this.props.onClose}
        placement="right"
        open={open}
        width="50vw"
      >
        <List
          style={{ marginTop: 10 }}
          dataSource={assistants}
          grid={{ gutter: 16, column: 2 }}
          renderItem={(item) => {
            return (
              <div>
                <List.Item key={item.id}>
                  <Card
                    title={this.renderItemTitle(item)}
                    bodyStyle={{ padding: 12 }}
                    extra={
                      <Button
                        type="link"
                        size="small"
                        onClick={() => { return this.onSelectBot(item); }}
                      >选择
                      </Button>
                    }
                  >
                    <Typography.Paragraph
                      style={{ marginBottom: 0 }}
                      ellipsis={{ rows: 3, expandable: true, symbol: 'more' }}
                    >
                      {this.renderItemContent(item)}
                    </Typography.Paragraph>
                  </Card>
                </List.Item>
              </div>
            );
          }
          }
        />
      </Drawer>
    );
  }
}
