/* eslint-disable no-case-declarations */
import '@chatscope/chat-ui-kit-styles/dist/default/styles.min.css';

import { CopyOutlined, UploadOutlined } from '@ant-design/icons';
import {
  <PERSON><PERSON>utt<PERSON>,
  Avatar,
  ChatContainer,
  ConversationHeader,
  Message,
  MessageList,
  MessageSeparator,
} from '@chatscope/chat-ui-kit-react';
import { Toast } from '~/components';
import Engine, { AliyunHelper, Sessions } from '~/engine';
import { EVENT_TYPE, EVENT_TYPE_ZH } from '~/pages/Playground/Configs';
import ReconnectingWebSocket from '~/pages/Playground/WebSocket';
import { StringExtension, Timer } from '~/plugins';
import { Button, Drawer, Input, Spin } from 'antd';
import Upload from 'antd/lib/upload/Upload';
import _ from 'lodash';
import { marked } from 'marked';
import moment from 'moment';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { PureComponent } from 'react';

export default class MessageDrawer extends PureComponent {
  static propTypes = {
    nodes: PropTypes.array,
    flow: PropTypes.object,
    open: PropTypes.bool,
    onCancel: PropTypes.func,
    onClose: PropTypes.func,
    fetchRunLogs: PropTypes.func,
    fetchConversations: PropTypes.func,
  };

  state = {
    jobId: undefined,
    isBeta: true,
    canSendMsg: true,
    messages: [],
    logs: [{}],
    defaultInput: {},
    inputData: {},
  }

  componentDidMount = async () => {
    this.initWebSocket();
    const { inputSchema } = this.props.nodes.find((x) => { return x.type === 'start'; })?.data || {};
    const id = this.props.flow?.uuid;
    const logs = await this.props.fetchRunLogs(id);
    const messages = await this.props.fetchConversations(id);
    const schema = StringExtension.camelToSnakeObj(inputSchema);
    const defaultInput = {};
    _.keys(schema).forEach((key) => {
      defaultInput[key] = schema[key].default;
    });

    this.setState({ logs, messages, defaultInput });
  }

  initWebSocket = () => {
    if (this.ws) {
      this.ws.close();
    }
    const path = `${Engine.getWssEndpoint()}/v2/chatbot/workflow-v2/run/${this.props.flow?.uuid}`;
    const query = { access_token: Sessions.getToken() };
    this.ws = new ReconnectingWebSocket(`${path}?${qs.stringify(query)}`, [], this.onReceiveMsg);
    this.pingTimer = Timer.setInterval(() => { this.ws.send(JSON.stringify({ type: 'ping' })); }, 1000 * 30);
  }

  formatLogHtml = (style, type, msg) => {
    const typeName = EVENT_TYPE_ZH[type];
    return `<div style="background:#eee;padding:4px 6px;"><b style="${style}">[${typeName}]:</b>
<pre style="white-space: pre-wrap;margin-bottom:0;">${msg}</pre>
</div>\n`;
  }

  onReceiveMsg = (e) => {
    if (e?.data !== 'pong') {
      const originData = JSON.parse(e.data);
      if (originData?.job_id) {
        this.setState({ jobId: originData?.job_id });
      }

      const { type, data } = StringExtension.snakeToCamelObj(originData);
      const nodeIdStr = _.trim(data?.nodeId, ' ');
      if (type === EVENT_TYPE.LLM_STEP_RESPONSE && _.isEmpty(data?.token)) {
        return;
      }

      switch (type) {
        case EVENT_TYPE.EXEC_STEP:
          const createdAt = moment().format('YYYY-MM-DD HH:mm');
          if (['Start', 'Done'].includes(nodeIdStr)) {
            this.setState({ logs: [...this.state.logs, { nodeId: nodeIdStr, message: '', createdAt }] });
          } else {
            const nodeId = _.last(nodeIdStr.split('@'));
            const node = this.props.nodes.find((x) => { return x.id === nodeId; });
            this.setState({
              logs: [
                ...this.state.logs,
                { message: '', nodeId: node?.data?.name || data.nodeName, createdAt },
              ],
            });
          }
          break;
        case EVENT_TYPE.OP_RESULT:
        case EVENT_TYPE.EXEC_FAILED:
        case EVENT_TYPE.TOOL_INPUT:
        case EVENT_TYPE.TOOL_OUTPUT:
        case EVENT_TYPE.LLM_REQUEST:
        case EVENT_TYPE.LLM_TOKEN_USAGE:
          this.llmStepResponse = '';
          this.beforeStepResponse = '';
          if (type === EVENT_TYPE.EXEC_FAILED) {
            this.setState({ jobId: undefined, canSendMsg: true });
          }
          const logs = _.clone(this.state.logs);
          const lastLog = logs.pop();
          const style = type === EVENT_TYPE.EXEC_FAILED ? 'color:red' : '';
          let msg = data?.output || data?.msg || data?.prompt;
          if ([EVENT_TYPE.OP_RESULT].includes(type)) {
            try {
              const msgObj = JSON.parse(msg);
              delete msgObj.raw_content;
              msg = JSON.stringify(msgObj);
            } catch (error) {
              // nothing
            }
          }
          if ([EVENT_TYPE.LLM_TOKEN_USAGE].includes(type)) {
            try { msg = JSON.stringify(JSON.parse(msg), null, 2); } catch (error) { /* nothing*/ }
          }
          lastLog.message += this.formatLogHtml(style, type, msg);
          lastLog.message = _.trimStart(lastLog.message, '\n');
          this.setState({ logs: [...logs, lastLog] });
          break;
        case EVENT_TYPE.LLM_STEP_RESPONSE:
          const stepLogs = _.clone(this.state.logs);
          const lastSetpLog = stepLogs.pop();
          if (!_.isEmpty(lastSetpLog.message) && _.isEmpty(this.llmStepResponse)) {
            this.beforeStepResponse = lastSetpLog.message;
          }

          this.llmStepResponse += data?.token;
          if (!_.isEmpty(this.llmStepResponse)) {
            lastSetpLog.message = this.formatLogHtml(style, type, this.llmStepResponse);
            lastSetpLog.message = this.beforeStepResponse + _.trimStart(lastSetpLog.message, '\n');
            this.setState({ logs: [...stepLogs, lastSetpLog] });
          }
          break;
        case EVENT_TYPE.EXEC_LOG:
          if (data.msg !== 'Workflow run start') {
            const execLogs = _.clone(this.state.logs);
            const lastExecLog = execLogs.pop();
            lastExecLog.message += this.formatLogHtml(style, type, data.msg);
            lastExecLog.message = _.trimStart(lastExecLog.message, '\n');
            this.setState({ logs: [...execLogs, lastExecLog] });
          }
          break;
        case EVENT_TYPE.EXEC_ACTION_REQUIRED:
          this.setState({
            canSendMsg: true,
            messages: [
              ...this.state.messages,
              { message: (data?.output || data?.msg), isClient: true, createdAt: moment().format('YYYY-MM-DD HH:mm') },
            ],
          });
          break;
        case EVENT_TYPE.FINAL_RESULT:
          this.llmStepResponse = '';
          this.beforeStepResponse = '';
          this.setState({
            canSendMsg: true,
            jobId: undefined,
            messages: [
              ...this.state.messages,
              { message: (data?.output || data?.msg), isClient: true, createdAt: moment().format('YYYY-MM-DD HH:mm') },
            ],
          });
          break;
        default:
          break;
      }
    }
  }

  onCancelFlow = async (flowId, jobId) => {
    await this.props.onCancel(flowId, jobId);
    this.setState({ jobId: undefined, canSendMsg: true }, () => {
      this.initWebSocket();
    });
  }

  onSendMsg = async () => {
    // let checked = true;
    const { defaultInput, inputData } = this.state;
    // _.mapValues(defaultInput, (v, k) => {
    //   if (_.isEmpty(inputData[k])) {
    //     checked = false;
    //   }
    // });

    // if (!checked) {
    //   Toast.show('请填写完整信息', Toast.Type.ERROR);
    //   return;
    // }
    const datas = _.isEmpty(inputData) ? defaultInput : { ...defaultInput, ...inputData };
    const value = JSON.stringify(datas);
    if (!this.state.canSendMsg) {
      Toast.show('当前WorkFlow执行中');
      return;
    }

    this.ws.send(JSON.stringify({
      text: value,
      type: _.isUndefined(this.state.jobId) ? 'message' : 'action_confirm',
      is_beta: this.state.isBeta,
    }));
    this.outputing = true;
    await this.setState({
      inputData: {},
      canSendMsg: false,
      messages: [
        ...this.state.messages,
        { message: value, isClient: false, createdAt: moment().format('YYYY-MM-DD HH:mm') },
      ],
    });
  }

  onScroll = (domId, direction) => {
    const dom = _.head(document.getElementById(domId).children);
    dom.scrollTop = direction === 'up' ? 0 : dom.scrollHeight;
  }

  onUpload = () => {
    const vm = this;
    const input = document.createElement('input');
    input.type = 'file';
    input.style.display = 'none';
    input.click();
    input.addEventListener('change', async function () {
      const file = this.files[0];
      const url = await AliyunHelper.clipsUploadImage(file);
      vm.setState({ pdfUrl: url });
    });
  }

  renderMessages = () => {
    return (
      <MessageList id="msg-list">
        {
          this.state.messages.map((x) => {
            let msg = x.message;
            try {
              const msgObj = JSON.parse(msg);
              delete msgObj.raw_content;
              if (msgObj.image_url) {
                msg = `<img src="${msgObj.image_url}" style="max-width: 100%; margin: 0;" />`;
              } else {
                const content = JSON.stringify(msgObj, null, 2);
                msg = marked.parse(`\`\`\`json\n${content}\n\`\`\``);
              }
            } catch (error) {
              msg = marked.parse(msg);
            }
            return (
              <Message model={{ message: msg, direction: !x.isClient ? 'outgoing' : '' }}>
                {x.isClient && <Avatar src="/static/ai-avatar.jpg" />}
                <Message.Header>
                  <div style={{ width: '100%', position: 'relative' }}>
                    {x.createdAt}
                    {
                      x.isClient &&
                      <CopyOutlined
                        style={{ float: 'right' }}
                        onClick={
                          async () => {
                            let copyText = x.message;
                            try {
                              const msgObj = JSON.parse(x.message);
                              if (msgObj.image_url) {
                                copyText = msgObj.image_url;
                              }
                            } catch (error) {
                              // nothing
                            }
                            await navigator.clipboard.writeText(copyText);
                            Toast.show('复制成功', Toast.Type.SUCCESS);
                          }}
                      />
                    }
                  </div>
                </Message.Header>
              </Message>
            );
          })
        }
      </MessageList>
    );
  }

  renderAttachButton = () => {
    return (
      <Upload>
        <Button icon={<UploadOutlined />} />
      </Upload>
    );
  }

  renderInputWrap = () => {
    const { defaultInput, inputData } = this.state;
    return (
      <>
        {
          _.keys(defaultInput).map((x, idx) => {
            return (
              <Input
                value={inputData[x]}
                style={{ marginBottom: 5 }}
                onChange={(e) => {
                  this.setState({ inputData: { ...inputData, [x]: e.target.value } });
                }}
                addonBefore={<div style={{ width: 100 }}>{x}</div>}
                addonAfter={
                  _.keys(defaultInput).length - 1 === idx ?
                    <Button type="primary" onClick={() => { return this.onSendMsg(); }}>发送</Button>
                    : null
                }
              />
            );
          })
        }
      </>
    );
  }

  render = () => {
    const { open, flow } = this.props;
    const { jobId, defaultInput } = this.state;
    const inputCount = _.keys(defaultInput).length;

    return (
      <Drawer
        open={open}
        title="运行"
        placement="right"
        className="message-drawer"
        contentWrapperStyle={{ width: '60vw', transform: 'translateX(0px) !important' }}
        onClose={this.props.onClose}
      >
        <div className="chat-main-container">
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div
              className="chat-wrap"
              style={{ position: 'relative', height: `calc(100vh - 130px - ${inputCount * 32}px)` }}
            >
              <ChatContainer>
                <ConversationHeader>
                  <ConversationHeader.Content userName={flow?.name} />
                  <ConversationHeader.Actions>
                    <ArrowButton
                      direction="up"
                      style={{ marginRight: 30 }}
                      onClick={() => { return this.onScroll('msg-list', 'up'); }}
                    />
                    <ArrowButton direction="down" onClick={() => { return this.onScroll('msg-list', 'down'); }} />
                  </ConversationHeader.Actions>
                </ConversationHeader>
                {this.renderMessages()}
              </ChatContainer>
              {this.renderInputWrap()}
            </div>
            <div className="log-wrap" style={{ position: 'relative' }}>
              <ChatContainer>
                <ConversationHeader>
                  <ConversationHeader.Content userName="执行记录" />
                  <ConversationHeader.Actions>
                    <ArrowButton
                      direction="up"
                      style={{ marginRight: 30 }}
                      onClick={() => { return this.onScroll('log-list', 'up'); }}
                    />
                    <ArrowButton direction="down" onClick={() => { return this.onScroll('log-list', 'down'); }} />
                  </ConversationHeader.Actions>
                </ConversationHeader>
                <MessageList id="log-list">
                  {
                    this.state.logs.map((x) => {
                      return (
                        <>
                          <MessageSeparator>
                            <span style={{ fontSize: 18, fontWeight: 'bold' }}>{x.nodeId}</span>
                          </MessageSeparator>
                          <Message model={{ message: x.message }}>
                            <Message.Header>
                              <div style={{ width: '100%', position: 'relative' }}>
                                <CopyOutlined
                                  style={{ float: 'right' }}
                                  onClick={
                                    async () => {
                                      const dom = document.createElement('div');
                                      dom.innerHTML = x.message;
                                      await navigator.clipboard.writeText(dom.innerText);
                                      Toast.show('复制成功', Toast.Type.SUCCESS);
                                    }}
                                />
                              </div>
                            </Message.Header>
                            <Message.Footer sentTime={x.createdAt} />
                          </Message>
                        </>
                      );
                    })
                  }
                </MessageList>
              </ChatContainer>
              {
                !_.isUndefined(jobId) &&
                <div style={{ position: 'absolute', bottom: 0, left: 0, right: 0 }}>
                  <Button
                    type="link"
                    style={{ position: 'absolute', bottom: 0 }}
                    onClick={() => { return this.props.onCancel(flow.uuid, jobId); }}
                  >
                    取消
                  </Button>
                  <Spin
                    spinning={1}
                    tip={<span style={{ marginLeft: 30, color: '#1890ff' }}>Loading...</span>}
                    style={{ display: 'flex', width: '50%', justifyContent: 'end' }}
                  />
                </div>
              }
            </div>
          </div>
        </div>
      </Drawer>
    );
  }
}
