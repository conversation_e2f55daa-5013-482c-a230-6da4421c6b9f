import { BranchesOutlined, DeleteFilled } from '@ant-design/icons';
import { <PERSON><PERSON>, Divider, Input, Select, Table } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import Configs from '../Configs';
import Utils from '../Utils';

export default class VariableTable extends PureComponent {
  static propTypes = {
    datas: PropTypes.array,
    onChange: PropTypes.func,
    canAdd: PropTypes.bool,
    isBatch: PropTypes.bool, // eslint-disable-line
  }

  static defaultProps = {
    isBatch: false,
    canAdd: true,
  }

  state = {
    datas: [],
  }

  componentDidMount = () => {
    const { datas } = this.props;
    if (!_.isUndefined(datas)) {
      this.setState({ datas });
    }
  }

  refresh = (datas) => {
    this.setState({ datas });
  }

  onChangeValue = (e, innerPath, key) => {
    const value = e?.target ? e.target.value : e;
    const updateData = (data, path) => {
      if (path.length === 0) return { ...data, [key]: value };

      const [current, ...rest] = path;
      if (!data.children || !data.children[current]) {
        return data;
      }

      return {
        ...data,
        children: data.children.map((child, index) => {
          return (index === current ? updateData(child, rest) : child);
        }),
      };
    };

    const datas = this.state.datas.map((data, index) => {
      return (index === innerPath[0] ? updateData(data, innerPath.slice(1)) : data);
    });

    this.setState({ datas });
    const schema = Utils.objectToSchema(datas);
    this.props.onChange({ arrs: datas, schema });
  }

  onAdd = () => {
    const datas = _.cloneDeep(this.state.datas);
    datas.push({ value: '', type: '', desc: '', path: [datas.length] });
    this.setState({ datas });
    const schema = Utils.objectToSchema(datas);
    this.props.onChange({ arrs: datas, schema });
  }

  onAddChild = (parentPath = []) => {
    const newData = _.cloneDeep(this.state.datas);
    const addChild = (data, path) => {
      if (path.length === 0) {
        data.push({ value: '', type: '', desc: '', path: parentPath.concat(data.length) });
        return;
      }

      const index = path[0];
      if (!data[index].children) {
        data[index].children = []; // eslint-disable-line
      }
      addChild(data[index].children, path.slice(1));
    };
    addChild(newData, parentPath);
    this.setState({ datas: newData });
    const schema = Utils.objectToSchema(newData);
    this.props.onChange({ arrs: newData, schema });
  }

  onDel = (path = []) => {
    let current = _.cloneDeep(this.state.datas);

    for (let i = 0; i < path.length - 1; i++) {
      if (current[path[i]].children) {
        current = current[path[i]].children;
      }
    }

    current.splice(path[path.length - 1], 1);
    this.setState({ datas: current });
    const schema = Utils.objectToSchema(current);
    this.props.onChange({ arrs: current, schema });
  }

  renderColumns = () => {
    return [
      {
        title: '变量名',
        dataIndex: 'value',
        key: 'value',
        align: 'center',
        render: (text, row) => {
          return (
            <Input
              size="small"
              value={text}
              placeholder="请输入变量名"
              disabled={!this.props.canAdd}
              onChange={(e) => { return this.onChangeValue(e, row.path, 'value'); }}
            />
          );
        },
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        align: 'center',
        render: (text, row) => {
          return (
            <Select
              size="small"
              value={text}
              style={{ width: 100 }}
              disabled={!this.props.canAdd}
              onChange={(e) => { return this.onChangeValue(e, row.path, 'type'); }}
            >
              {_.map(Configs.TYPES, (v, k) => { return <Select.Option key={k} value={k}>{v}</Select.Option>; })}
            </Select>
          );
        },
      },
      {
        title: '描述',
        dataIndex: 'desc',
        key: 'desc',
        align: 'center',
        render: (text, row) => {
          return (
            <Input
              size="small"
              value={text}
              placeholder="请输入描述"
              onChange={(e) => { return this.onChangeValue(e, row.path, 'desc'); }}
            />
          );
        },
      },
      {
        title: ' ',
        dataIndex: 'opet',
        key: 'opet',
        align: 'center',
        render: (text, row) => {
          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <BranchesOutlined
                style={{
                  visibility: (['array', 'object'].includes(row?.type)) ? 'visible' : 'hidden',
                  transform: 'rotate(180deg)',
                }}
                onClick={() => { return this.onAddChild(row.path); }}
              />
              {
                this.props.canAdd &&
                <>
                  <Divider type="vertical" />
                  <DeleteFilled onClick={() => { return this.onDel(row.path); }} />
                </>
              }
            </div>
          );
        },
      },
    ];
  }

  render = () => {
    const { canAdd } = this.props;
    return (
      <div className="nodrag">
        <Table
          size="small"
          pagination={false}
          columns={this.renderColumns()}
          locale={{ emptyText: <div style={{ paddingTop: 10 }}>请添加参数</div> }}
          dataSource={this.state.datas}
        />

        {
          canAdd &&
          <Button shape="round" size="small" style={{ marginTop: 5 }} onClick={() => { return this.onAdd(); }}>
            新增
          </Button>
        }
      </div>
    );
  }
}
