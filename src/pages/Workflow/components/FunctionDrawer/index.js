import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { Toast } from '~/components';
import { ChatBot } from '~/engine';
import { StringExtension } from '~/plugins';
import { Button, Drawer, Form, Input, Radio } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

export default class FunctionDrawer extends PureComponent {
  static propTypes = {
    open: PropTypes.bool,
    nodes: PropTypes.array,
    workflow: PropTypes.object,
    onClose: PropTypes.func,
  }

  state = {
    defaultKeys: [],
    data: {
      params: [{}],
    },
  }

  componentDidMount = async () => {
    const params = [];
    const defaultKeys = [];
    const { inputSchema } = this.props.nodes.find((x) => { return x.type === 'start'; })?.data || {};
    const schema = StringExtension.camelToSnakeObj(inputSchema);
    _.keys(schema).forEach((key) => {
      const { type, description } = schema[key];
      defaultKeys.push(key);
      params.push({ name: key, type, description, value: '' });
    });
    this.setState({ data: { params }, defaultKeys });
  }

  onAdd = (index = 0) => {
    const params = _.cloneDeep(this.state.data?.params) || [];
    params.splice(index + 1, 0, {});
    this.setState({ data: { ...this.state.data, params } });
  }

  onDel = (index) => {
    const params = _.cloneDeep(this.state.data?.params) || [];
    if (params.length <= 1) return;
    params.splice(index, 1);
    this.setState({ data: { ...this.state.data, params } });
  }

  onChangeValue = (e, key) => {
    const value = e?.target ? e.target.value : e;
    this.setState({ data: { ...this.state.data, [key]: value } });
  }

  onChangeParamValue = (e, key, idx) => {
    const params = _.cloneDeep(this.state.data?.params) || [];
    params[idx][key] = e?.target ? e.target.value : e;
    this.onChangeValue(params, 'params');
  }

  onSubmit = async () => {
    const regex = /^[a-zA-Z_]+$/;
    const { uuid } = this.props.workflow;
    const { id, name, displayName, description, params } = this.state.data;

    if (_.isEmpty(name) || _.isEmpty(displayName) || _.isEmpty(params)) {
      Toast.show('请完善参数 !', Toast.Type.WARNING);
      return;
    }

    if (!regex.test(name)) {
      Toast.show('函数名称格式错误!', Toast.Type.ERROR);
      return;
    }

    const data = { workflowUuid: uuid, name, displayName, description, params };
    if (_.isUndefined(id)) {
      await ChatBot.addWorkflowFunc(data);
    } else {
      await ChatBot.updateWorkflowFunc({ id, ...data });
    }
    Toast.show('操作成功!', Toast.Type.SUCCESS);
    this.props.onClose();
  }

  renderParamItem = (data, idx) => {
    return (
      <div style={{ marginBottom: 10 }}>
        <Form.Item labelCol={{ span: 3 }} style={{ marginBottom: 5 }} label={`参数${idx + 1}`}>
          <Input.Group compact style={{ display: 'flex', alignItems: 'center' }}>
            <Input
              value={data?.name}
              disabled={this.state.defaultKeys.includes(data?.name)}
              onChange={(e) => { return this.onChangeParamValue(e, 'name', idx); }}
            />
            <div style={{ display: 'flex', width: 100, justifyContent: 'space-evenly' }}>
              <PlusOutlined onClick={() => { return this.onAdd(idx); }} />
              {
                !this.state.defaultKeys.includes(data?.name) &&
                <MinusOutlined onClick={() => { return this.onDel(idx); }} />
              }
            </div>
          </Input.Group>
        </Form.Item>
        <Form.Item labelCol={{ span: 3 }} style={{ marginBottom: 5 }} label="类型">
          <Input.Group compact style={{ display: 'flex', alignItems: 'center' }}>
            <Input value={data?.type} onChange={(e) => { return this.onChangeParamValue(e, 'type', idx); }} />
            <div style={{ width: 100, justifyContent: 'space-evenly', display: idx ? 'none' : 'flex' }}>
              <Button
                type="link"
                onClick={() => { return window.open('https://json-schema.org/understanding-json-schema/'); }}
              >查看帮助
              </Button>
            </div>
          </Input.Group>
        </Form.Item>
        <Form.Item labelCol={{ span: 3 }} style={{ marginBottom: 5 }} label="描述">
          <Input
            value={data?.description}
            onChange={(e) => { return this.onChangeParamValue(e, 'description', idx); }}
          />
        </Form.Item>
        <Form.Item labelCol={{ span: 3 }} style={{ marginBottom: 5 }} label="默认值">
          <Input
            value={data?.value}
            onChange={(e) => { return this.onChangeParamValue(e, 'value', idx); }}
          />
        </Form.Item>
      </div>
    );
  }

  render = () => {
    const { data } = this.state;

    return (
      <Drawer
        title="另存为函数"
        open={this.props.open}
        placement="right"
        contentWrapperStyle={{ width: '35vw' }}
        onClose={this.props.onClose}
      >
        <div style={{ height: '100%', width: '100%', position: 'relative' }}>
          <Form labelCol={{ span: 3 }} className="common-form">
            <Form.Item label="函数名" help="仅支持字母、下划线, eg.: A_b_c" required>
              <Input value={data?.name} onChange={(e) => { return this.onChangeValue(e, 'name'); }} />
            </Form.Item>
            <Form.Item label="别名" help="支持中文" required>
              <Input value={data?.displayName} onChange={(e) => { return this.onChangeValue(e, 'displayName'); }} />
            </Form.Item>
            <Form.Item label="描述" required>
              <Input value={data?.description} onChange={(e) => { return this.onChangeValue(e, 'description'); }} />
            </Form.Item>
            <Form.Item label="参数">
              {(data?.params || []).map((x, i) => { return this.renderParamItem(x, i); })}
            </Form.Item>
            <Form.Item label="范围" required>
              <Radio.Group value={data?.scope} onChange={(e) => { return this.onChangeValue(e, 'scope'); }}>
                <Radio value="global">公开</Radio>
                <Radio value="project">私有</Radio>
              </Radio.Group>
            </Form.Item>
          </Form>
          <div style={{ position: 'absolute', bottom: 0, left: 0, right: 0, textAlign: 'center' }}>
            <Button type="primary" onClick={() => { return this.onSubmit(); }}>保存</Button>
          </div>
        </div>
      </Drawer>
    );
  }
}
