import { MenuFoldOutlined, MenuUnfoldOutlined, PlusOutlined } from '@ant-design/icons';
import { Toast } from '~/components';
import { Platform } from '~/plugins';
import { Avatar, Card, List, Typography } from 'antd';
import classNames from 'classnames';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';

import Utils from '../Utils';
import BotDrawer from './BotDrawer';
import PluginDrawer from './PluginDrawer';

const NODES = ['variable', 'kb', 'code', 'text_combiner', 'text_splitter'];
export default class ExtraSidebar extends PureComponent {
  static propTypes = {
    subflows: PropTypes.array,
    nodeTypes: PropTypes.object,
    assistants: PropTypes.array,
  }

  state = {
    type: '',
    openBot: false,
    nodeTypes: {},
    datas: [],
    boundaryKeys: ['start', 'done'],
    nestedKeys: ['bot', 'plugin', 'sub_workflow'],
    nodeKeys: [],
    isCollapsed: false,
  }

  componentDidMount = () => {
    const { nodeTypes } = this.props;
    const { boundaryKeys } = this.state;
    const keys = Object.keys(nodeTypes || {});
    const nodeKeys = keys.filter((key) => { return ![...boundaryKeys].includes(key); });
    this.setState({
      nodeKeys,
      nodeTypes: { ...nodeTypes, plugin: { type: 'plugin', name: 'plugin', displayName: '插件' } },
    });
  }

  formatNodeData = (item) => {
    const { type, name, displayName, inputSchema, configSchema, outputSchema } = item;
    let data = {};
    if (type === 'switch_case') {
      const input = JSON.stringify(Utils.schemaToObject(configSchema));
      const configs = Utils.genDefaultVal(input);
      data = { type, name, displayName, configs, inputs: [], inputSchema: {} };
    }
    const configs = {};
    if (NODES.includes(type)) {
      _.keys(configSchema).forEach((key) => {
        const config = configSchema[key];
        configs[key] = config.default;
      });
      data = _.cloneDeep({
        type,
        name,
        displayName,
        configs,
        inputs: [],
        inputSchema,
        outputSchema,
      });
    }

    return data;
  }

  onDragStart = (e, item) => {
    if (this.state.nestedKeys.includes(item.type)) {
      this.setState({ [`open${_.upperFirst(item.type)}`]: true });
      return;
    }

    const data = this.onAddNode(item);
    if (_.isEmpty(data)) {
      Toast.show('该节点加急开发中，敬请期待！', Toast.Type.WARNING);
      return;
    }

    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('application/reactflowv2', JSON.stringify(data));
  }

  onAddNode = (item) => {
    if (this.state.nestedKeys.includes(item.type)) {
      let datas = [];
      switch (item.type) {
        case 'bot':
          datas = this.props.assistants;
          break;
        case 'plugin':
          datas = this.props.nodeTypes.plugin;
          break;
        case 'sub_workflow':
          datas = this.props.subflows.filter((x) => { return x.content !== '{}'; });
          break;
        default:
          break;
      }

      this.setState({ openBot: true, datas, type: item.type });
      return;
    }

    const data = this.formatNodeData(item);
    if (_.isEmpty(data)) {
      Toast.show('该节点加急开发中，敬请期待！', Toast.Type.WARNING);
      return;
    }
    Platform.emit('DROP_NODE', data);
  }

  onCollapse = () => {
    this.setState({ isCollapsed: !this.state.isCollapsed });
    const width = this.state.isCollapsed ? 'calc(100vw - 248px)' : 'calc(100vw - 100px)';
    document.querySelector('.flow-wrap').style.width = width;
  }

  render = () => {
    const { nodeKeys, nodeTypes, isCollapsed } = this.state;
    return (
      <>
        <div className={classNames('model-list', { 'collaps-model-list': isCollapsed })}>
          <Typography.Title
            level={4}
            style={{ marginBottom: 0, display: 'flex', justifyContent: 'space-between', marginTop: 10 }}
          >
            <span>{this.state.isCollapsed ? ' ' : '选择节点'}</span>
            {
              this.state.isCollapsed
                ? <MenuUnfoldOutlined style={{ fontSize: 20 }} onClick={() => { return this.onCollapse(); }} />
                : <MenuFoldOutlined style={{ fontSize: 20 }} onClick={() => { return this.onCollapse(); }} />
            }
          </Typography.Title>
          <List style={{ marginTop: 10 }}>
            {
              _.map(nodeKeys, (key) => {
                const item = nodeTypes[key];
                return (
                  <div draggable onDragStart={(e) => { return this.onDragStart(e, item); }}>
                    <Card
                      title={
                        <List.Item
                          key={item.id}
                          actions={[
                            <PlusOutlined
                              style={{ fontSize: 20, color: '#000', cursor: 'pointer' }}
                              onClick={() => { return this.onAddNode(item); }}
                            />,
                          ]}
                        >
                          <List.Item.Meta
                            avatar={<Avatar shape="square" src={`/static/v2/icon-${item.type}.png`} />}
                            title={
                              isCollapsed
                                ? null :
                                <Typography.Title level={4} style={{ marginBottom: 0 }}>
                                  {item.displayName}
                                </Typography.Title>
                            }
                          />
                        </List.Item>
                      }
                    />
                  </div>
                );
              })
            }
          </List>
        </div>
        {
          this.state.openBot &&
          <BotDrawer
            type={this.state.type}
            assistants={this.state.datas}
            open={this.state.openBot}
            onClose={() => { return this.setState({ openBot: false }); }}
          />
        }
        {
          this.state.openPlugin &&
          <PluginDrawer
            plugins={this.props.nodeTypes.plugin}
            open={this.state.openPlugin}
            onClose={() => { return this.setState({ openPlugin: false }); }}
          />
        }
      </>
    );
  }
}
