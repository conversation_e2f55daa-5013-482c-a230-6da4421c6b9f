export default class Configs {
  static TYPES = {
    array: 'Array',
    int: 'Int',
    object: 'Object',
    str: 'String',
    bool: 'Boolean',
  }

  static DEFAULT_OUTPUT = [{
    name: '',
    type: 'reference',
    value: '',
  }]

  static BOT_DEFAULT_OUTPUT = {
    arrs: [{ value: 'output', type: 'str', desc: '', path: [0] }],
    schema: { output: { description: '', required: false, default: '', type: 'str' } },
  }
  static BOT_ADDITIONAL_DEFAULT_OUTPUT = {
    arrs: [
      { value: 'output', type: 'str', desc: '', path: [0] },
      { value: 'llm_additional_info', type: 'object', desc: '', path: [1] },
    ],
    schema: {
      output: { description: '', required: false, default: '', type: 'str' },
      llm_additional_info: {
        description: '额外信息',
        required: false,
        default: '',
        type: 'object',
      },
    },
  }

  static JSON_PROMPT_PREFIX = '返回的内容为 JSON，确保能被 python 的 `json.loads` 解析，JSON 字段定义如下：\n'
}
