/* eslint-disable guard-for-in */
import { StringExtension } from '~/plugins';
import _ from 'lodash';

export default class Utils {
  static findUniquePath = (data, targetValue, currentPath = []) => {
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      if (item.value === targetValue) {
        return [...currentPath, i];
      }
      if (item.children) {
        const result = this.findUniquePath(item.children, targetValue, [...currentPath, i]);
        if (result) {
          return result;
        }
      }
    }
    return null;
  }

  static initPath = (data, currentPath = []) => {
    data.forEach((item, index) => {
      const newPath = [...currentPath, index];
      item.key = newPath.join('-'); // eslint-disable-line
      item.path = newPath; // eslint-disable-line
      if (item.children) {
        this.initPath(item.children, newPath);
      }
    });

    return data;
  }

  static schemaToObject(schema) {
    function reverseTransformNode(key, node) {
      const result = {
        value: _.snakeCase(key),
        label: _.snakeCase(key),
        type: node.type,
        desc: node.description || '',
      };
      if (node.type === 'object' && node.properties) {
        result.children = [];
        for (const [childKey, childNode] of Object.entries(node.properties)) {
          result.children.push(reverseTransformNode(childKey, childNode));
        }
      } else if (node.type === 'array' && node.itemProperties) {
        result.children = [];
        for (const [childKey, childNode] of Object.entries(node.itemProperties)) {
          result.children.push(reverseTransformNode(childKey, childNode));
        }
      }

      return result;
    }

    const reversed = [];
    for (const [key, node] of Object.entries(schema)) {
      reversed.push(reverseTransformNode(key, node));
    }

    return reversed;
  }

  static objectToSchemaV1(datas) {
    function transformNode(node) {
      const result = {
        description: node.desc || '',
        required: false,
        default: (() => {
          switch (node.type) {
            case 'int':
              return 0;
            case 'array':
              return [];
            case 'object':
              return {};
            default:
              return '';
          }
        })(),
        type: node.type,
      };

      if (node.children) {
        if (node.type === 'array') {
          result.item_type = node.children[0].type;
          if (node.children[0].children) {
            result.item_properties = transformNode(node.children[0]);
          }
        } else if (node.type === 'object') {
          result.properties = {};
          node.children.forEach((child) => {
            result.properties[child.value] = transformNode(child);
          });
        }
      }

      return result;
    }

    const transformed = {};
    datas.forEach((node) => {
      if (node.value) {
        transformed[node.value] = transformNode(node);
      }
    });

    return transformed;
  }

  static objectToSchema = (array) => {
    const result = {};

    array.forEach((item) => {
      const { value, desc, type, children } = item;
      const obj = {
        description: desc,
        required: false,
        default: (() => {
          switch (type) {
            case 'int':
              return 0;
            case 'array':
              return [];
            case 'object':
              return {};
            default:
              return '';
          }
        })(),
        type,
      };

      if (type === 'array' && children) {
        obj.itemType = 'object';
        obj.itemProperties = this.objectToSchema(children);
      } else if (type === 'object' && children) {
        obj.properties = this.objectToSchema(children);
      }

      result[value] = obj;
    });

    return result;
  }

  static predecessors = (nodeId, edges) => {
    const predecessors = [];
    const findPredecessors = (id) => {
      edges.forEach((edge) => {
        if (edge.target === id) {
          if (edge.source === 'start') {
            predecessors.push({ id: '#<Start>#', value: edge.name });
          } else {
            predecessors.push({ id: `#<${edge.source}>#`, value: edge.name });
          }
          findPredecessors(edge.source);
        }
      });
    };
    findPredecessors(nodeId);
    return predecessors;
  };

  static genDefaultVal = (input) => {
    const parsedInput = JSON.parse(input);
    const result = {};
    result.events = parsedInput.map(() => {
      const newEvent = { output: '', target: '', config: {} };
      newEvent.config.conditions = { all: [], any: [] };
      return newEvent;
    });
    return result;
  }

  static fillInputTypes = (inputs, schemas) => {
    return inputs.map((input) => {
      if (input.type === 'reference') {
        const valueParts = input.value.split('.');
        let currentSchema = schemas.find((schema) => { return schema.value === valueParts[0]; });

        for (let i = 1; i < valueParts.length && currentSchema; i++) {
          currentSchema = currentSchema.children ? currentSchema.children.find((child) => {
            return child.value === valueParts[i];
          }) : null;
        }

        if (currentSchema) {
          return { ...input, fieldType: currentSchema.type };
        }
      }
      return input;
    });
  }

  static findNodeByPath = (data, path) => {
    const pathParts = path.split('.');
    let currentNode = data.find((node) => { return node.value === pathParts[0]; });

    for (let i = 1; i < pathParts.length; i++) {
      if (!currentNode || !currentNode.children) {
        return null;
      }
      currentNode = currentNode.children.find((node) => { return node.label === pathParts[i]; });
    }

    return currentNode;
  }

  static getQuoteSchemas = (datas) => {
    const newData = StringExtension.snakeToCamelObj(datas);
    const { batch, quoteSchemas } = newData;
    if (!batch?.batchEnable) {
      return quoteSchemas;
    }
    const arrs = quoteSchemas.map((x) => {
      const children = x.children.filter((xc) => { return !_.endsWith(xc.label, '[]'); });
      return { ...x, children };
    });

    const quotes = [...arrs];
    for (const item of (batch?.items || [])) {
      const data = this.findNodeByPath(arrs, item.path);
      quotes.push({
        value: item.name,
        label: item.name,
        type: data?.type,
        desc: '',
        children: data?.children || [],
      });
    }
    return quotes;
  }

  static findValueByPath = (data, path) => {
    const paths = path.split('.');
    function traverse(innerData, pathArray) {
      if (pathArray.length === 0) {
        return null; // 如果路径数组为空，返回 null
      }
      const currentKey = pathArray[0];

      const remainingPath = pathArray.slice(1);

      for (const item of innerData) {
        if (item.value === currentKey) {
          if (remainingPath.length === 0) {
            return item.label;
          }
          if (item.children) {
            return traverse(item.children, remainingPath);
          }
        }
      }
      return null; // 如果在当前层次找不到对应的键，返回 null
    }

    return traverse(data, paths);
  }

  static transformFormat = (inputJson) => {
    function transformProperties(properties) {
      const output = {};
      for (const key in properties) {
        const property = properties[key];
        if (property.type === 'str') {
          output[key] = property.description || '';
        } else if (property.type === 'int') {
          output[key] = property.default || 0;
        } else if (property.type === 'object') {
          output[key] = transformProperties(property.properties);
        } else if (property.type === 'array' && property.itemType === 'object') {
          output[key] = [transformProperties(property.itemProperties)];
        } else if (property.type === 'bool') {
          output[key] = property.default || false;
        }
      }
      return output;
    }

    const output = {};
    for (const key in inputJson) {
      const property = inputJson[key];
      if (property.type === 'array' && property.itemType === 'object') {
        output[key] = [transformProperties(property.itemProperties)];
      } else if (property.type === 'object') {
        output[key] = transformProperties(property.properties);
      } else if (property.type === 'str') {
        output[key] = property.description || '';
      } else if (property.type === 'int') {
        output[key] = property.default || 0;
      } else if (property.type === 'bool') {
        output[key] = property.default || false;
      }
    }

    return output;
  }
}
