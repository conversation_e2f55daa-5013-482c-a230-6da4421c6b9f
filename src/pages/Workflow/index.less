.workflow-v2-container {
  .model-list {
    margin-right: 10px;
    margin-left: -20px;
  }

  .collaps-model-list {
    width: 80px !important;

    .ant-list-item-action {
      margin-left: 0 !important;
    }
  }

  .ant-table-tbody > tr > td {
    border: none;
  }

  .ant-table-cell-with-append {
    display: flex;
  }

  .ant-collapse-header {
    padding: 4px 8px !important;
  }

  .ant-collapse-content-box {
    padding-top: 0 !important;
  }

  .ant-tabs-tab {
    padding: 8px !important;
  }

  .system-handle,
  .user-handle,
  .base-handle {
    top: 23px;
    width: 16px;
    height: 16px;
    border: 3px solid;
    background: #fff;
    color: #030;
  }

  .system-handle {
    left: -8px;
    color: #fa8;
    z-index: 10;
  }

  .user-handle {
    top: 23px;
    bottom: 0;
    left: -8px;
    color: #9ce;
    z-index: 10;
  }

  .start-node-wrap,
  .end-node-wrap,
  .common-node-wrap {
    width: 480px;
    border: 2px solid #ccc;
    background: #fff;
    border-radius: 5px;

    .ant-table-cell {
      padding: 2px 4px !important;
      background: #fff;
    }
  }

  .condition-node-wrap {
    .ant-collapse-item {
      border-top: 1px solid #aaa;
    }
  }

  .kb-node-wrap {
    .ant-menu-item,
    .ant-menu-submenu-title {
      height: auto !important;
      line-height: 1.5 !important;
    }
  }

  .ant-list-item-meta {
    align-items: center;

    .ant-list-item-meta-title {
      margin-bottom: 0;
    }
  }

  .ant-card {
    margin-bottom: 10px;

    .ant-card-head {
      min-height: 20px;
      padding: 0;

      .ant-card-head-title {
        padding: 0;
      }
    }

    .ant-card-body {
      padding: 0;
    }
  }
}

.chat-log {
  .ChatFooter {
    display: none;
  }
}
