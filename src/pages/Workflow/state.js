import Configs from '~/consts';
import { ChatBot, Workflow } from '~/engine';
import { EVENT_TYPE, EVENT_TYPE_ZH } from '~/pages/Playground/Configs';
import _ from 'lodash';
import moment from 'moment';

const SET_STATE = 'WORKFLOWV2/SET_STATE';
const CLEAR_STATE = 'WORKFLOWV2/CLEAR_STATE';

const formatLogs = (arrs = []) => {
  let items = arrs;
  items = items.filter((x) => { return x.logType !== EVENT_TYPE.FINAL_RESULT; });
  items = items.filter((x) => {
    return !(
      x.logType === EVENT_TYPE.EXEC_LOG &&
      x.nodeId === 'start' &&
      x.content === '{"msg": "Workflow run start"}'
    );
  });
  items = _.reverse(items);

  const result = [];
  let temp = [];
  for (const x of items) {
    if (x.logType === EVENT_TYPE.EXEC_STEP) {
      if (temp.length > 0) {
        result.push(temp);
      }
      temp = [];
      temp.push(x);
    } else {
      temp.push(x);
    }
  }
  if (temp.length > 0) {
    result.push(temp);
  }

  const logs = [];
  result.forEach((arr) => {
    const [node, ...oth] = arr;
    let message = '';
    oth.forEach((x) => {
      const obj = JSON.parse(x.content);
      const style = x.logType === EVENT_TYPE.EXEC_FAILED ? 'color:red' : '';
      let msg = obj?.output || obj?.prompt || obj?.msg;
      try {
        const msgObj = JSON.parse(msg);
        delete msgObj.raw_content;
        msg = JSON.stringify(msgObj, null, 2);
      } catch (error) {
        // nothing
      }
      const typeName = EVENT_TYPE_ZH[x.logType];
      message += `<div style="background:#eee;padding:4px 6px;">
<b style="${style}">[${typeName}]:</b>
<pre style="white-space: pre-wrap;margin-bottom:0;">${msg || ''}</pre>
</div >\n`;
    });
    message = _.trimEnd(message, '\n');
    logs.push({
      message,
      nodeId: node?.nodeId,
      createdAt: moment(node.createdAt).format('YYYY-MM-DD HH:mm'),
    });
  });

  return logs.filter((x) => { return !_.isEmpty(x.message); });
};

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const publishWorkflow = (id) => {
  return async () => {
    const data = await ChatBot.publishChatbotWorkflow(id);
    return data;
  };
};

export const fetchAssistants = () => {
  return async (dispatch) => {
    const { items } = await ChatBot.fetchChatbotAssistants(Configs.ALL_PAGE_PARAMS);
    dispatch(setState({ assistants: items }));
  };
};

export const fetchInnerParams = () => {
  return async (dispatch) => {
    const result = await Workflow.fetchInnerParams();
    const innerParams = result?.runtimeParams.map((x) => { return { value: x, label: x }; });
    dispatch(setState({ innerParams }));
  };
};

export const fetchLibraries = () => {
  return async () => {
    const { items } = await ChatBot.searchKnowledgeLibraries(Configs.ALL_PAGE_PARAMS);
    return items;
  };
};

export const fetchNodes = () => {
  return async (dispatch) => {
    const { bot, other, plugin, subWorkflow } = await Workflow.fetchNodes();
    const typeMap = {};
    const nodes = { plugin };
    [...bot, ...other, ...subWorkflow].forEach((x) => {
      typeMap[x.type] = x.name;
      nodes[x.name] = x;
    });
    delete nodes.test;
    dispatch(setState({ nodeTypes: nodes, typeMap }));
  };
};

export const fetchWorkflow = (uuid) => {
  return async () => {
    const data = await ChatBot.getChatbotWorkflow(uuid);
    return data;
  };
};

export const updateWorkflow = (params) => {
  return async () => {
    await ChatBot.updateChatbotWorkflow(params);
  };
};

export const cancelWorkflow = (flowId, jobId) => {
  return async () => {
    await ChatBot.cancelWorkflow(flowId, jobId);
  };
};

export const fetchWorkflows = (uuid) => {
  return async (dispatch) => {
    let { items } = await Workflow.fetchWorkflows(Configs.ALL_PAGE_PARAMS);
    if (!_.isUndefined(uuid)) {
      items = items.filter((x) => { return x.uuid !== uuid; });
    }
    items = items.map((x) => { return { ...x, type: 'sub_workflow' }; });
    dispatch(setState({ subflows: items }));
  };
};

export const fetchRunLogs = (id) => {
  return async (dispatch, getState) => {
    const { nodeTypes } = getState().workflowv2;
    const { items } = await ChatBot.fetchWorkflowLogs({
      flowUuid: id,
      'pagination.pageIndex': 1,
      'pagination.pageSize': 100,
    });
    return formatLogs(items, nodeTypes);
  };
};

export const fetchConversations = (id, nodeId) => {
  return async () => {
    const { items } = await ChatBot.fetchWorkflowConversations({ flowId: id, nodeId, orderBy: 'id desc' });
    const messages = [];
    items.forEach((x) => {
      messages.push({
        ...x,
        message: x.content,
        isClient: x.role !== 'USER',
        createdAt: moment(x.createdAt).add(8, 'h').format('YYYY-MM-DD HH:mm'),
      });
    });
    return _.reverse(messages);
  };
};

export const createConversation = (params) => {
  return async () => {
    const result = await ChatBot.createChatbotSessionConversation(params);
    return result;
  };
};

export const updateConversation = (params) => {
  return async () => {
    await ChatBot.updateChatbotSessionConversation(params);
  };
};

export const deleteConversation = (id) => {
  return async () => {
    await ChatBot.deleteChatbotSessionConversation(id);
  };
};

const _getInitState = () => {
  return {
    typeMap: {},
    assistants: [],
    innerParams: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
