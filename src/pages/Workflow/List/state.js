import Configs from '~/consts';
import { ChatBot, Workflow } from '~/engine';
import _ from 'lodash';

const SET_STATE = 'mpArticle/SET_STATE';
const CLEAR_STATE = 'mpArticle/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const addWorkflow = (params) => {
  return async () => {
    const result = await Workflow.createWorkflow(params);
    return result;
  };
};

export const fetchWorkflows = (params = {}) => {
  return async (dispatch, getState) => {
    const { pagination } = getState().workflows;
    const searchParams = {
      name: params?.name,
      groupId: params?.groupId,
      'pagination.pageIndex': params.pageIndex || pagination.pageIndex,
      'pagination.pageSize': params.pageSize || pagination.pageSize,
      'pagination.orderBy': params.orderBy || pagination.orderBy,
    };

    const { items, total } = await Workflow.fetchWorkflows(searchParams);
    dispatch(
      setState({
        total,
        list: items,
        pagination: {
          pageIndex: searchParams['pagination.pageIndex'],
          pageSize: searchParams['pagination.pageSize'],
          orderBy: searchParams['pagination.orderBy'],
        },
      }),
    );
  };
};

export const fetchNodes = () => {
  return async (dispatch) => {
    const { other } = await Workflow.fetchNodes();
    const boundaryNodes = _.filter(other, (x) => { return x.type === 'start' || x.type === 'end'; });
    const nodeMap = {};
    boundaryNodes.forEach((item) => {
      let data = {};
      const configs = {};
      _.keys(item.configSchema).forEach((key) => {
        const config = item.configSchema[key];
        configs[key] = config.default;
      });
      data = _.cloneDeep({ configs, input_schema: item.inputSchema, output_schema: item.outputSchema });
      nodeMap[item.type] = data;
    });
    dispatch(setState({ nodeMap }));
  };
};

export const deltWorkflow = (id) => {
  return async (dispatch) => {
    await ChatBot.delChatbotWorkflow(id);
    dispatch(fetchWorkflows());
  };
};

export const copyWorkflow = (params) => {
  return async (dispatch) => {
    await Workflow.createWorkflow(params);
    dispatch(fetchWorkflows());
  };
};

export const fetchGroups = () => {
  return async (dispatch) => {
    const { items } = await ChatBot.fetchChatbotWorkflowGroups(Configs.ALL_PAGE_PARAMS);
    dispatch(setState({ groups: items }));
  };
};

export const addGroup = (params) => {
  return async (dispatch) => {
    await ChatBot.addWorkflowGroup(params);
    dispatch(fetchGroups());
  };
};

export const updateGroup = (params) => {
  return async (dispatch) => {
    await ChatBot.updateWorkflowGroup(params);
    dispatch(fetchGroups());
  };
};

export const delGroup = (id) => {
  return async (dispatch) => {
    await ChatBot.delWorkflowGroup(id);
    dispatch(fetchGroups());
  };
};

export const updateWorkflow = (params) => {
  return async (dispatch) => {
    await ChatBot.updateChatbotWorkflow(params);
    dispatch(fetchWorkflows());
  };
};

const _getInitState = () => {
  return {
    total: 0,
    groupId: undefined,
    pagination: {
      pageIndex: 1,
      pageSize: 20,
      orderBy: 'createdAt asc',
    },
    list: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
