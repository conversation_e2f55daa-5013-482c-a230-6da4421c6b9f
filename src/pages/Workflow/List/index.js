import { PaginationTable, Toast } from '~/components';
import { Sessions } from '~/engine';
import { StringExtension } from '~/plugins';
import { Button, Divider, Input, Modal, Popconfirm, Select, Table } from 'antd';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.workflows;
  },
  actions,
)
export default class Workflows extends Component {
  static propTypes = {
    total: PropTypes.number.isRequired,
    list: PropTypes.array.isRequired,
    pagination: PropTypes.object.isRequired,
    nodeMap: PropTypes.object.isRequired,
    groups: PropTypes.array,
    addWorkflow: PropTypes.func.isRequired,
    copyWorkflow: PropTypes.func.isRequired,
    deltWorkflow: PropTypes.func.isRequired,
    fetchWorkflows: PropTypes.func.isRequired,
    fetchNodes: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
    fetchGroups: PropTypes.func.isRequired,
    addGroup: PropTypes.func.isRequired,
    updateGroup: PropTypes.func.isRequired,
    delGroup: PropTypes.func.isRequired,
    updateWorkflow: PropTypes.func.isRequired,
  }

  state = {
    groupId: undefined,
    groupOpen: false,
    addGroupOpen: false,
    group: {},
  }

  componentDidMount = async () => {
    this.props.fetchNodes();
    this.props.fetchWorkflows();
    this.props.fetchGroups();
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onSearch = (e) => {
    const { groupId } = this.state;
    this.props.fetchWorkflows({ ...e, groupId });
  }

  onAdd = async () => {
    const { nickname } = Sessions.getPartner();
    const flow = { name: `${nickname}的-${StringExtension.randomString(4)}`, type: 'workflow' };
    const nodes = [
      {
        id: 'start',
        name: 'start',
        data: this.props.nodeMap.start,
        position: { x: 150, y: 420 },
        type: 'start',
      },
      {
        id: 'end',
        name: 'done',
        data: this.props.nodeMap.end,
        position: { x: 1200, y: 420 },
        type: 'end',
      },
    ];
    const contentObj = { nodes, edges: [], params: {}, history_mode: 'autofit' };
    const obj = { ...flow, betaContent: JSON.stringify(contentObj) };
    const result = await this.props.addWorkflow(obj);
    this.$push(`/workflow-v2/${result.uuid}`);
  }

  onCopy = async (data) => {
    const { name, type, content, betaContent } = data;
    await this.props.copyWorkflow({ name: `${name}-COPY`, type, content, betaContent });
    Toast.show('复制成功', Toast.Type.SUCCESS);
  }

  onChangeWorkflowGroup = async (groupId, row) => {
    await this.props.updateWorkflow({ uuid: row.uuid, groupId });
    Toast.show('操作成功!', Toast.Type.SUCCESS);
  }

  onChangeGroup = (e, key) => {
    const { value } = e.target;
    this.setState({ group: { ...this.state.group, [key]: value } });
  }

  onSubmitGroup = async () => {
    const { id, name } = this.state.group;
    if (!name) {
      Toast.show('请完善信息!', Toast.Type.WARNING);
      return;
    }

    if (id === undefined) {
      await this.props.addGroup({ name });
    } else {
      await this.props.updateGroup(this.state.group);
    }

    this.setState({ addGroupOpen: false, group: {} });
    Toast.show('操作成功!', Toast.Type.SUCCESS);
  }

  renderColumns = () => {
    return [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
        render: (name, row) => {
          return <a onClick={() => { return this.$push(`/workflow-v2/${row.uuid}`); }}>{name}</a>;
        },
      },
      {
        title: '分组',
        dataIndex: 'groupId',
        key: 'groupId',
        align: 'center',
        render: (groupId, row) => {
          const value = groupId === 0 ? undefined : groupId;
          return (
            <Select
              value={value}
              style={{ width: 160 }}
              placeholder="请选择"
              size="small"
              options={(this.props.groups || []).map((x) => { return { label: x.name, value: x.id }; })}
              onChange={(e) => { return this.onChangeWorkflowGroup(e, row); }}
            />
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <a onClick={() => { return this.$push(`/workflow-v2/${row.uuid}`); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm title="是否复制?!" onConfirm={() => { return this.onCopy(row); }}>
                <a>复制</a>
              </Popconfirm>
              <Divider type="vertical" />
              <Popconfirm
                title="是否删除?!"
                disabled={!row.editable}
                onConfirm={() => { return this.props.deltWorkflow(row.uuid); }}
              >
                <Button type="link" disabled={!row.editable}>删除</Button>
              </Popconfirm>
            </>
          );
        },
      },
    ];
  }

  renderGroupDrawer = () => {
    const columns = [
      { title: 'ID', dataIndex: 'id', key: 'id', align: 'center' },
      { title: '分组名', dataIndex: 'name', key: 'name', align: 'center' },
      {
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
        align: 'center',
        render: (txt, row) => {
          return (
            <>
              <a onClick={() => { return this.setState({ addGroupOpen: true, group: row }); }}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm title="是否删除?!" onConfirm={() => { return this.props.delGroup(row.id); }}>
                <a>删除</a>
              </Popconfirm>
            </>
          );
        },
      },
    ];
    return (
      <Modal
        open={this.state.groupOpen}
        title="分组"
        width={600}
        onCancel={() => { return this.setState({ groupOpen: false }); }}
        footer={[
          <Button key="close" onClick={() => { return this.setState({ groupOpen: false }); }}>
            关闭
          </Button>,
          <Button
            key="add"
            type="primary"
            onClick={() => { return this.setState({ addGroupOpen: true, group: {} }); }}
          >
            新增
          </Button>,
        ]}
      >
        <Table size="small" pagination={false} dataSource={this.props.groups || []} columns={columns} />
      </Modal>
    );
  }

  renderCreateModal = () => {
    const { addGroupOpen, group } = this.state;
    return (
      <Modal
        open={addGroupOpen}
        title="分组"
        onCancel={() => { return this.setState({ addGroupOpen: false, group: {} }); }}
        onOk={this.onSubmitGroup}
      >
        <Input
          placeholder="请输入分组名称"
          value={group?.name}
          onChange={(e) => { return this.onChangeGroup(e, 'name'); }}
        />
      </Modal>
    );
  }

  renderSelects = () => {
    return (
      <Select
        allowClear
        value={this.state.groupId}
        style={{ width: 160, marginBottom: 16 }}
        placeholder="请选择分组"
        onChange={(e) => { return this.setState({ groupId: e }, () => { return this.onSearch({ pageIndex: 1 }); }); }}
      >
        {
          (this.props.groups || []).map((x) => {
            return <Select.Option key={x.id} value={x.id}>{x.name}</Select.Option>;
          })
        }
      </Select>
    );
  }

  render = () => {
    return (
      <div className="chat-knowledge" style={{ padding: 30, background: '#fff' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 15 }}>
          <div>{this.renderSelects()}</div>
          <div>
            <Button
              style={{ marginRight: 10 }}
              onClick={() => { return this.setState({ groupOpen: true }); }}
            >
              分组管理
            </Button>
            <Button onClick={() => { return this.onAdd(); }}>新建</Button>
          </div>
        </div>
        <PaginationTable
          totalDataCount={this.props.total}
          dataSource={this.props.list}
          pagination={this.props.pagination}
          columns={this.renderColumns()}
          onPaginationChange={(e) => { return this.onSearch(e); }}
        />

        {this.state.groupOpen && this.renderGroupDrawer()}
        {this.state.addGroupOpen && this.renderCreateModal()}
      </div>
    );
  }
}

export {
  reducer,
};
