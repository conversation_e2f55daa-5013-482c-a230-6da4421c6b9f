/* eslint-disable no-await-in-loop */

const SET_STATE = 'DEMO_01/SET_STATE';
const CLEAR_STATE = 'DEMO_01/CLEAR_STATE';

import { Platform } from '~/plugins';
import _ from 'lodash';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

// 实现 func fetchBaseData 的方法
export const fetchBaseData = () => {
  return async (dispatch) => {
    const env = Platform.isProd() ? 'prod' : 'stg';
    const resp = await fetch(
      `https://video-clip.oss-cn-shanghai.aliyuncs.com/faas/frontend/demo-data-${env}.json?v=${new Date().valueOf()}`
      , {
        headers: { accept: 'application/json ' }, method: 'GET',
      },
    );
    const data = await resp.json();
    dispatch(setState({ base: data }));
  };
};

export const fetchConversations = (topicId) => {
  return async (dispatch, getState) => {
    const { base } = getState().demo;
    const resp = await fetch(`https://${base.env}.bzy.ai/v2/chatbot/session-conversations/search`, {
      headers: {
        accept: 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
        'content-type': 'application/json',
        'grpc-metadata-client-ver': '0.0.12',
        'grpc-metadata-token': base.token,

      },
      body: `{"topic_id":${topicId},"last_id":0,"pagination":{"skip":0,"limit":1000}}`,
      method: 'POST',

    });
    const data = await resp.json();
    return data.items;
  };
};

export const fetchTabs = () => {
  return async (dispatch, getState) => {
    const { env, token, sessions } = getState().demo.base;
    const tabs = [];
    for (let index = 0; index < sessions.length; index++) {
      const item = sessions[index];
      const resp = await fetch(`https://${env}.bzy.ai/v2/chatbot/session-topics/search`, {
        headers: {
          accept: 'application/json, text/plain, */*',
          'content-type': 'application/json',
          'grpc-metadata-client-ver': '0.0.12',
          'grpc-metadata-token': token,
        },
        method: 'POST',
        body: `{"session_id":${item.sessionId},"pagination":{"skip":0,"limit":1000}}`,
      });
      const data = await resp.json();
      tabs.push({ title: item.title, id: item.sessionId, examples: item.examples, topicId: _.head(data.items).id });
    }
    dispatch(setState({ tabs }));
  };
};

export const addUserMsg = (params, txt) => {
  return async (dispatch, getState) => {
    const { base } = getState().demo;
    const resp = await fetch(`https://${base.env}.bzy.ai/v2/chatbot/session-conversations`, {
      headers: {
        accept: 'application/json, text/plain, */*',
        'content-type': 'application/json',
        'grpc-metadata-client-ver': '0.0.12',
        'grpc-metadata-token': base.token,
      },
      body: JSON.stringify({ session_id: params.id, topic_id: params.topicId, content: txt, role: 'USER' }),
      method: 'POST',
    });
    const data = await resp.json();
    return data;
  };
};

export const sensMsg = async (topicId, txt) => {
  return async (dispatch, getState) => {
    const { base } = getState().demo;
    fetch(`https://${base.env}.ai/v2/chatbot/chat?sse_mode=1`, {
      headers: {
        accept: 'application/json, text/plain, */*',
        'content-type': 'application/json',
        'grpc-metadata-client-ver': '0.0.12',
        'grpc-metadata-token': base.token,
      },
      body: JSON.stringify({
        sse_mode: 1,
        session_id: base.sessionId,
        topic_id: topicId,
        predict_assistant_id: 189,
        stream: true,
        user_message: txt,
      }),
      method: 'POST',
    });
  };
};

const _getInitState = () => {
  return {
    base: {},
    tabs: [],
  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
