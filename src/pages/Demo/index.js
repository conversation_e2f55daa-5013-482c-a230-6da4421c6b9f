/* eslint-disable no-await-in-loop */
import '@chatscope/chat-ui-kit-styles/dist/default/styles.min.css';

import { Avatar, ChatContainer, Message, MessageList } from '@chatscope/chat-ui-kit-react';
import Engine, { ChatBot } from '~/engine';
import { Button, Divider, Input, Spin, Tabs, Typography } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import Utils from '../Playground/Utils';
import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.demo;
  },
  actions,
)
export default class DEMO extends Component {
  static propTypes = {
    fetchTabs: PropTypes.func.isRequired,
    fetchBaseData: PropTypes.func.isRequired,
    fetchConversations: PropTypes.func.isRequired,
    addUserMsg: PropTypes.func.isRequired,
    base: PropTypes.object.isRequired,
    tabs: PropTypes.array.isRequired,
    setState: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }
  state = {
    isOutping: false,
    activeKey: undefined,
  }

  componentDidMount = async () => {
    await this.props.fetchBaseData();
    Engine.setItem('token', this.props.base.token);
    await this.props.fetchTabs();

    const tabs = [];
    for (let index = 0; index < this.props.tabs.length; index++) {
      const element = this.props.tabs[index];
      const conversations = await this.props.fetchConversations(element.topicId);
      tabs.push({ ...element, conversations });
    }

    await this.props.setState({ tabs });
    this.setState({ activeKey: _.head(this.props.tabs)?.id });
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onClickSendBtn = async () => {
    this.onSend(this.state.txt);
    await this.setState({ txt: '' });
  }

  onSend = async (userMessage) => {
    const userMsg = _.trimEnd(userMessage, '\n');
    const { activeKey } = this.state;
    await this.setState({ isOutping: true });
    const tab = this.props.tabs.find((x) => { return +x.id === +activeKey; });
    const msgObj = await this.props.addUserMsg(tab, userMsg);
    const tabs = this.props.tabs.map((x) => {
      if (x.id === tab.id) {
        const conversations = _.cloneDeep(x.conversations);
        conversations.push(msgObj);
        conversations.push({ role: 'ASSISTANT', content: '' });
        return { ...x, conversations };
      }
      return x;
    });
    await this.props.setState({ tabs });

    await ChatBot.chatbotReply({
      sseMode: 1,
      sessionId: activeKey,
      topicId: tab.topicId,
      stream: true,
      tokenAutoFit: true,
      userMessage: userMsg,
    }, {
      responseType: 'stream',
      onDownloadProgress: async (e) => {
        let arrs = Utils.formatSSE(e?.event?.target?.response);
        const idnex = arrs.findIndex((x) => { return x.steps === 'restart'; });
        arrs = idnex === -1 ? arrs : arrs.slice(idnex);
        const { steps } = _.last(arrs);
        const content = steps === 'restart' ? '' : _.map(arrs, 'token').join('');
        const items = this.props.tabs.map((x) => {
          if (+x.id === +activeKey) {
            const conversations = _.cloneDeep(x.conversations);
            conversations[conversations.length - 1].content = content;
            return { ...x, conversations };
          }
          return x;
        });
        await this.props.setState({ tabs: items });
      },
    });

    await this.setState({ isOutping: false });
  }

  render = () => {
    const { base, tabs } = this.props;

    return (
      <div style={{ padding: 30 }}>
        <Typography.Title level={2} style={{ textAlign: 'center' }}>{base.title}</Typography.Title>
        <Tabs activeKey={`${this.state.activeKey}`} onChange={(w) => { return this.setState({ activeKey: w }); }}>
          {
            tabs.map((x) => {
              return (
                <Tabs.TabPane
                  disabled={this.state.isOutping}
                  tab={x.title}
                  key={`${x.id}`}
                  style={{ height: 'calc(100vh - 200px)' }}
                >
                  <ChatContainer style={{ height: 'calc(100% - 100px)' }}>
                    <MessageList id="log-list" >
                      {
                        (x.conversations || []).map((c) => {
                          return (
                            <Message model={{ message: c.content, direction: c.role !== 'USER' ? '' : 'outgoing' }} >
                              {c.role !== 'USER' && <Avatar src="/static/ai-avatar.jpg" />}
                              {
                                _.isEmpty(c.content) &&
                                <Message.CustomContent><Spin style={{ color: '#fff' }} /></Message.CustomContent>
                              }
                            </Message>
                          );
                        })
                      }
                    </MessageList>
                  </ChatContainer>
                  <div>
                    <Divider style={{ margin: '5px 0' }} />
                    <Input.TextArea
                      autoSize={{ minRows: 2, maxRows: 2 }}
                      value={this.state.txt}
                      onChange={(e) => { return this.setState({ txt: e.target.value }); }}
                    />
                    <div style={{ marginTop: 5, textAlign: 'end' }}>
                      {
                        (x.examples || []).map((tt, i) => {
                          return (
                            <Button
                              disabled={this.state.isOutping}
                              style={{ marginRight: 10 }}
                              onClick={() => { return this.setState({ txt: tt }); }}
                            >
                              示例{i + 1}
                            </Button>
                          );
                        })
                      }
                      <Button
                        loading={this.state.isOutping}
                        style={{ marginRight: 10 }}
                        type="primary"
                        onClick={() => { return this.onClickSendBtn(); }}
                      >
                        发送
                      </Button>
                    </div>
                  </div>
                </Tabs.TabPane>
              );
            })
          }
        </Tabs>
      </div>
    );
  }
}

export {
  reducer,
};
