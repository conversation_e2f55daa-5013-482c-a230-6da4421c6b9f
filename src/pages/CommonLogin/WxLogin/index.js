/* eslint-disable jsx-a11y/iframe-has-title */

import Configs from '~/consts';
import { Sessions } from '~/engine';
import { Platform } from '~/plugins';
import { Button, Checkbox, Input, Space } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.wxLogin;
  },
  actions,
)
export default class WxLogin extends Component {
  static propTypes = {
    location: PropTypes.object,
    getQr: PropTypes.func,
    login: PropTypes.func,
  }

  state = {
    name: '',
    url: '',
    invitationCode: '',
    showInvitationCode: false,
    logoUrl: Platform.isMai() ? '/static/maiLogo.png' : '/static/logoBig.png',
    bgUrl: Platform.isMai() ? '/static/mail_bg.png' : '/static/bg-eye.svg',
  }

  componentDidMount = async () => {
    const { token, name } = this.props.location.query;
    const { invitation_code: invitationCode } = this.props.location.query;
    this.setState({ invitationCode });
    if (_.isUndefined(token)) {
      this.login();
    } else {
      try {
        window.parent.postMessage({ token }, '*');
        await this.props.login(token);
        const { partnerType } = Sessions.getProfile();
        const route = partnerType === 'playground' ? Configs.ROUTE.HOMEPAGE : Configs.USER_HOMEPAGE[partnerType];
        this.$replace(route);
      } catch {
        this.login();
      }
    }

    this.setState({ name });
  }

  login = async () => {
    const { invitation_code } = this.props.location.query; // eslint-disable-line
    const { origin, pathname } = window.location;
    const queryObj = { platform: 'playground', invitation_code };
    const qryStr = qs.stringify(queryObj);
    const url = await this.props.getQr({ redirect: `${origin}${pathname}?${qryStr}`, ...queryObj });
    this.setState({ url });
  }

  handleSubmit = () => {
    // 在原有url基础上，添加invitation_code参数
    const { pathname } = window.location;
    const { invitationCode } = this.state;
    this.$replace(pathname, { ...this.props.location.query, invitation_code: invitationCode });
    // 延时100ms后，重新登录
    setTimeout(() => {
      this.login();
    }, 100);
  }

  render = () => {
    const { name } = this.state;
    return (
      <div
        style={{
          width: '100vw',
          height: '100vh',
          textAlign: 'center',
          // lineHeight: '100vh',
          backgroundImage: `url(${this.state.bgUrl})`,
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundSize: '100%',
        }}
      >
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
        >
          {
            name ? (
              <div style={{ fontSize: 60, lineHeight: '80px' }}>
                {name}
              </div>
            ) : (
              <img src={this.state.logoUrl} style={{ height: 80, marginBottom: '1rem' }} />
            )
          }
          <iframe
            style={{ width: '25vw', height: '45vh', border: 'none', verticalAlign: 'middle' }}
            src={this.state.url}
          />
          <div style={{ height: '80px' }}>
            <Space style={{ fontSize: '14px' }} direction="vertical">
              <Checkbox
                checked={this.state.showInvitationCode}
                onChange={(e) => { return this.setState({ showInvitationCode: e.target.checked }); }}
              >
                {this.state.showInvitationCode ? <div>注册邀请制，请输入激活码后扫码登录：</div> : <div>没有账号，注册激活</div>}
              </Checkbox>
              {
                this.state.showInvitationCode &&
                <Space>
                  <Input
                    value={this.state.invitationCode}
                    onChange={(e) => { return this.setState({ invitationCode: e.target.value }); }}
                  />
                  <Button onClick={this.handleSubmit}>提交</Button>
                </Space>
              }
            </Space>
          </div>
        </div>
      </div>
    );
  }
}

export {
  reducer,
};
