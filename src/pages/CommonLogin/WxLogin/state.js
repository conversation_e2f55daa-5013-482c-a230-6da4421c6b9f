import { Accounts, Sessions } from '~/engine';
import { StringExtension } from '~/plugins';
import _ from 'lodash';

const LOGIN_FAILURE = 'WX_LOGIN/LOGIN_FAILURE';
const SET_STATE = 'WX_LOGIN/SET_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const getQr = (params) => {
  return async () => {
    const url = await Accounts.getConnectQr(params);
    return url;
  };
};

export const login = (token) => {
  return async (dispatch) => {
    try {
      Sessions.login({ token, isTokenLogin: true }, true);
      const { quotas, partners, ...profile } = await Accounts.getMemberInfo();
      Sessions.setProfile(profile);
      Sessions.setModels(quotas);
      Sessions.postLogin(profile?.permissions);
      const funcs = await Accounts.fetchFuncTools();
      let tools = {};
      _.map(funcs, (v) => { tools = { ...tools, ...{ [v.name]: v.displayName } }; });
      Sessions.setFuncTools(StringExtension.camelToSnakeObj(tools));
    } catch (error) {
      dispatch({ type: LOGIN_FAILURE });
      throw error;
    }
  };
};

const _getInitState = () => {
  return {};
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    default:
      return state;
  }
};
