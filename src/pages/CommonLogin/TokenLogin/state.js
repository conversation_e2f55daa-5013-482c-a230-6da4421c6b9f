import { Accounts } from '~/engine';
import Sessions from '~/engine/Sessions';

const LOGIN_SUCCESS = 'accountLogin/LOGIN_SUCCESS';
const LOGIN_FAILURE = 'accountLogin/LOGIN_FAILURE';
const SET_STATE = 'accountLogin/SET_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const login = () => {
  return async (dispatch, getState) => {
    try {
      const { token } = getState().tokenLogin;
      Sessions.login({ token, isTokenLogin: true }, false);
      const profile = await Accounts.getProfile();
      Sessions.login({ token, profile: { ...profile }, partners: [], isTokenLogin: true }, false);
      const partner = await Accounts.getPartner();
      Sessions.setPartner(partner);
      Sessions.postLogin();
      dispatch({ type: LOGIN_SUCCESS });
    } catch (error) {
      dispatch({ type: LOGIN_FAILURE });
      throw error;
    }
  };
};

const _getInitState = () => {
  return {};
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    default:
      return state;
  }
};
