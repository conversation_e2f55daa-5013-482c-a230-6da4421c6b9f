import Consts from '~/consts';
import { Sessions } from '~/engine';
import { Spin } from 'antd';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.tokenLogin;
  },
  actions,
)
export default class TokenLogin extends Component {
  static propTypes = {
    login: PropTypes.func.isRequired,
    setState: PropTypes.func.isRequired,
    location: PropTypes.object,
  }

  componentDidMount = async () => {
    if (Sessions.getToken()) {
      this.$replace(Sessions.getHomePage());
      return;
    }
    const { token } = qs.parse(this.props.location.search, { ignoreQueryPrefix: true }) || {};
    await this.props.setState({ token });
    await this.props.login();

    this.$replace(Consts.ROUTE.HOMEPAGE);
  }

  render = () => {
    return (
      <div style={{ width: '100vw', height: '100vh', textAlign: 'center', lineHeight: '100vh' }}>
        <Spin spinning size="large" />
      </div>
    );
  }
}

export {
  reducer,
};
