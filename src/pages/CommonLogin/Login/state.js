import { Accounts, Sessions } from '~/engine';
import { StringExtension } from '~/plugins';
import _ from 'lodash';

const LOGIN_SUCCESS = 'accountLogin/LOGIN_SUCCESS';
const LOGIN_FAILURE = 'accountLogin/LOGIN_FAILURE';
const SET_STATE = 'accountLogin/SET_STATE';
const CLEAR_STATE = 'accountLogin/CLEAR_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const clearState = () => {
  return { type: CLEAR_STATE };
};

export const login = (data) => {
  return async (dispatch) => {
    try {
      // await Accounts.login(data);
      // const partner = await Accounts.getPartner();
      // Sessions.setPartner(partner);
      // const profile = await Accounts.getProfile();
      // Sessions.setProfile(profile);

      // dispatch({ type: LOGIN_SUCCESS });
      const { username, password } = data;
      if (username === 'administrator' && password === 'Gg$ttEuiK~DLp9&y') {
        const token = process.env.TOKEN;

        Sessions.login({ token, isTokenLogin: true }, true);
        const profile = await Accounts.getProfile();
        Sessions.setProfile(profile);

        const { enabledModels } = await Accounts.getQuota();
        Sessions.setModels(enabledModels);
        Sessions.postLogin(profile?.permissions);
        const funcs = await Accounts.fetchFuncTools();
        let tools = {};
        _.map(funcs, (v) => { tools = { ...tools, ...{ [v.name]: v.displayName } }; });
        Sessions.setFuncTools(StringExtension.camelToSnakeObj(tools));

        dispatch({ type: LOGIN_SUCCESS });
      } else {
        dispatch({ type: LOGIN_FAILURE });
      }
    } catch (error) {
      dispatch({ type: LOGIN_FAILURE });
      throw error;
    }
  };
};

const _getInitState = () => {
  return {

  };
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    case CLEAR_STATE:
      return _getInitState();
    default:
      return state;
  }
};
