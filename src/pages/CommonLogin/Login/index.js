import './index.less';

import { LockOutlined, UserOutlined } from '@ant-design/icons';
import Consts from '~/consts';
import Engine, { Sessions } from '~/engine';
import { Alert, Button, Checkbox, Form, Input } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import reducer, * as actions from './state';

const FormItem = Form.Item;
@connect(
  (state) => {
    return state.accountLogin;
  },
  actions,
)
export default class AccountLogin extends Component {
  static propTypes = {
    username: PropTypes.string.isRequired,
    password: PropTypes.string.isRequired,
    remember: PropTypes.bool.isRequired,
    login: PropTypes.func.isRequired,
    location: PropTypes.object.isRequired,
    setState: PropTypes.func.isRequired,
    clearState: PropTypes.func.isRequired,
  }

  state = {
    showLoginFailAlert: false,
    alertMsg: '',
  }

  componentDidMount = async () => {
    if (Sessions.getToken()) {
      this.$replace(Sessions.getHomePage());
      return;
    }

    const { host, query } = this.props.location;
    let devMode = host === 'localhost' || (query?.mode && query?.mode === 'pwd') || Engine.getItem('MODE');
    if (devMode && _.isUndefined(Engine.getItem('MODE'))) {
      Engine.setItem('MODE', query?.mode);
    }

    if (query?.mode && query?.mode === 'home') {
      devMode = true;
    }

    if (!devMode) {
      this.$replace(Consts.ROUTE.CLIPS_LOGIN, query);
    }

    if (query?.platform && query?.platform === 'desktop') {
      Engine.setItem('PLATFORM', query?.platform);
    }
  }

  componentWillUnmount = () => {
    this.props.clearState();
  }

  onChangeValue = (e, type) => {
    const value = e.target ? e.target.value : e;
    this.props.setState({ [type]: value });

    if (type !== 'remember') {
      this.setState({ showLoginFailAlert: false, alertMsg: '' });
    }
  }

  onSubmit = async () => {
    const { username, password } = this.props;
    if (_.isEmpty(username) || _.isEmpty(password)) {
      this.setState({ showLoginFailAlert: true, alertMsg: '用户名或密码不能为空' });
      return;
    }
    const remember = this.props.remember || Engine.getItem('PLATFORM') === 'desktop';

    try {
      await this.props.login({ username, password, remember });
      const redirectUrl = Engine.getItem('redirectUrl');
      if (_.isUndefined(redirectUrl) || Consts.LOGIN_ROUTE === decodeURIComponent(redirectUrl)) {
        this.$replace(Sessions.getHomePage());
      } else {
        const [url, query] = decodeURIComponent(redirectUrl).split('?');
        this.$replace(url, qs.parse(query));
      }

      Engine.removeItem('redirectUrl');
    } catch (error) {
      if (error.response.status === 403) {
        this.setState({ showLoginFailAlert: true, alertMsg: '用户名或密码错误' });
      }
    }
  }

  renderLoginFailAlert = () => {
    const { showLoginFailAlert, alertMsg } = this.state;
    if (!showLoginFailAlert) {
      return null;
    }

    return <Alert message={alertMsg} type="error" showIcon />;
  }

  render = () => {
    return (
      <div className="account-login">
        <div className="account-login-bg" />
        <div className="header">
          <img alt="" src="/static/logoBig.png" />
        </div>

        <div className="error-wrap">
          {this.renderLoginFailAlert()}
        </div>

        <div className="form">
          <FormItem>
            <Input
              size="large"
              value={this.props.username}
              prefix={<UserOutlined />}
              placeholder="请输入用户名"
              onChange={(e) => { return this.onChangeValue(e, 'username'); }}
            />
          </FormItem>
          <FormItem>
            <Input.Password
              size="large"
              value={this.props.password}
              prefix={<LockOutlined />}
              placeholder="请输入密码"
              onPressEnter={this.onSubmit}
              onChange={(e) => { return this.onChangeValue(e, 'password'); }}
            />
          </FormItem>


          <Checkbox
            checked={this.props.remember}
            onChange={(e) => { return this.onChangeValue(e.target.checked, 'remember'); }}
          >
            Remember Me
          </Checkbox>
          <a onClick={this.onClickForgetPassword}>
            Forget password
          </a>

          <Button size="large" type="primary" onClick={this.onSubmit}>
            Sign In
          </Button>
        </div>
        <div className="footer">
          <div style={{ marginRight: 10 }} dangerouslySetInnerHTML={{ __html: 'Copyright &copy;2022 JUSHUO' }} />
        </div>
      </div>
    );
  }
}

export {
  reducer,
};
