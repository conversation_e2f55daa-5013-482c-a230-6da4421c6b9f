@import "app.less";

.account-login {
  position: relative;
  width: 100%;
  min-height: 100vh;
  padding: 110px 0 144px 0;
  background: @gray-e6f;

  .account-login-bg {
    position: absolute;
    width: 100vw;
    height: 100vh;
    margin-top: -110px;
    background-image: url(/static/background.svg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100%;
  }

  .header {
    height: 44px;
    text-align: center;
    line-height: 44px;

    a {
      text-decoration: none;
    }

    img {
      height: 44px;
      margin-right: 10px;
      vertical-align: top;
    }

    .title {
      position: relative;
      top: 2px;
      font-family: 'Myriad Pro', 'Helvetica Neue', Arial, Helvetica, sans-serif;
      font-size: 33px;
      font-weight: 600;
      color: @black-1;
    }

    .divider {
      margin-right: 6px;
      font-weight: 300;
      color: @gray-3;
    }
  }

  .error-wrap {
    width: 24vw;
    min-width: 250px;
    min-height: 40px;
    margin: 50px auto 0;
  }

  .form {
    width: 24vw;
    min-width: 250px;
    margin: 20px auto 0;
    z-index: 10;

    a {
      float: right;
      font-size: 14px;
    }

    button {
      width: 100%;
      margin-top: 15px;
    }

    .alert {
      margin-bottom: 20px;
    }
  }

  .footer {
    display: flex;
    position: absolute;
    bottom: 0;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin: 48px 0 24px 0;
    padding: 0 16px;
    text-align: center;
    font-size: 14px;
    color: @black-4;
  }
}
