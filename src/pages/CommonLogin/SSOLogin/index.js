import { ROUTE, SSO_LOGIN_URL } from '~/consts';
import Engine, { Sessions } from '~/engine';
import { connect } from '~/plugins';
import { Spin } from 'antd';
import PropTypes from 'prop-types';
import qs from 'qs';
import React, { Component } from 'react';

import reducer, * as actions from './state';

@connect(
  (state) => {
    return state.ssoLogin;
  },
  actions,
)
export default class SSOLogin extends Component {
  static propTypes = {
    replace: PropTypes.func.isRequired,
    login: PropTypes.func.isRequired,
    location: PropTypes.object,
  }

  componentDidMount = async () => {
    if (Sessions.getToken()) {
      this.props.replace(ROUTE.HOMEPAGE);
      return;
    }

    const { code, loginUrl } = qs.parse(this.props.location.search, { ignoreQueryPrefix: true }) || {};
    if (loginUrl) {
      Engine.setItem(SSO_LOGIN_URL, loginUrl);
    } else {
      Engine.removeItem(SSO_LOGIN_URL, loginUrl);
    }

    await this.props.login(code);
  }

  render = () => {
    return (
      <div style={{ width: '100vw', height: '100vh', textAlign: 'center', lineHeight: '100vh' }}>
        <Spin spinning size="large" tip="Loading..." />
      </div>
    );
  }
}

export {
  reducer,
};
