import { ExpiringGroupsSessionKey, ROUTE } from '~/consts';
import Engine, { Accounts, Partner } from '~/engine';
import { routerActions } from 'connected-react-router';
import _ from 'lodash';

const LOGIN_SUCCESS = 'ssoLogin/LOGIN_SUCCESS';
const LOGIN_FAILURE = 'ssoLogin/LOGIN_FAILURE';
const SET_STATE = 'ssoLogin/SET_STATE';

export const setState = (state) => {
  return { type: SET_STATE, payload: state };
};

export const login = (code = '') => {
  return async (dispatch) => {
    try {
      const auth = await Accounts.ssoLogin(code);
      const { profile, partners } = auth || {};

      try {
        // 获取即将到期的群
        const expiringGroups = await Partner.fetchExpiringGroups();
        Engine.setItem(ExpiringGroupsSessionKey, expiringGroups);
      } catch (e) {
        console.error(e); // eslint-disable-line
      }

      if (_.isEmpty(partners)) {
        if (profile && !profile.isAgreed) {
          dispatch(routerActions.replace(ROUTE.AGREEMENT));
        } else {
          dispatch(routerActions.replace(ROUTE.HOMEPAGE));
        }
      } else {
        dispatch(routerActions.replace(ROUTE.PARTNER));
      }

      dispatch({ type: LOGIN_SUCCESS });
    } catch (error) {
      dispatch({ type: LOGIN_FAILURE });
      throw error;
    }
  };
};

const _getInitState = () => {
  return {};
};

export default (state = _getInitState(), action) => {
  switch (action.type) {
    case SET_STATE:
      return { ...state, ...action.payload };
    default:
      return state;
  }
};
