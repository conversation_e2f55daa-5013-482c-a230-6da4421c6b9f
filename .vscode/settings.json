{"diffEditor.ignoreTrimWhitespace": true, "editor.insertSpaces": true, "editor.renderWhitespace": "all", "editor.tabSize": 2, "editor.trimAutoWhitespace": true, "editor.wordWrap": "on", "editor.wordWrapColumn": 120, "files.associations": {".stylelintrc": "json"}, "files.eol": "\n", "files.exclude": {"**/.DS_Store": true, "**/.git": true, "**/.hg": true, "**/.svn": true, "**/CVS": true}, "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true, "git.autofetch": true, "git.confirmSync": false, "javascript.validate.enable": false, "search.exclude": {"**/bower_components": true, "**/node_modules": true, ".vscode": true}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}