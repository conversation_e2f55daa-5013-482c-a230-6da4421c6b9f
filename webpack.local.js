/* eslint-disable no-useless-escape */

const path = require('path');
const os = require('os');

const autoprefixer = require('autoprefixer');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');
const HappyPack = require('happypack');
const moment = require('moment');
const bundleConfig = require('./webpack-dll/build/bundle-config.json');
const manifest = require('./webpack-dll/build/manifest.json');

const happyThreadPool = HappyPack.ThreadPool({ size: os.cpus().length });

const PORT = process.env.PORT || 8080;
const {
  BACKEND_PROTOCOL = 'https',
  BACKEND_DOMAIN = 'staging-aiapi.bzy.ai',
  WSS_DOMAIN = 'wss://staging-aiapi.bzy.ai',
  // eslint-disable-next-line max-len
  TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwaWQiOjIwMTA1OSwic2NvcGUiOiJtZW1iZXIiLCJwbGF0Zm9ybSI6InBsYXlncm91bmQiLCJ1aWQiOiI3QUtqOHZHbXdVdTNvMWdHRmg4aU1lIiwiZXhwIjoxNzE3MTY0NzQ2fQ.Fsro_iO114VkmgEWtpz4s1um-Z4begOoMnVYZmgZSHM',
} = process.env;

module.exports = {
  mode: 'development',
  devServer: {
    host: '::',
    port: PORT,
    historyApiFallback: true,
    disableHostCheck: true,
  },
  devtool: 'cheap-module-eval-source-map',
  entry: [
    `webpack-dev-server/client?http://[::]:${PORT}`, // WebpackDevServer host and port
    'webpack/hot/only-dev-server', // "only" prevents reload on syntax errors
    path.resolve(__dirname, 'src/index.js'),
  ],
  output: {
    publicPath: '/',
    filename: 'bundle.js',
    chunkFilename: '[name].[chunkhash].js',
  },
  resolve: {
    alias: {
      '~': path.resolve(__dirname, 'src'),
    },
  },
  watchOptions: {
    poll: true,
    ignored: /node_modules/,
  },
  optimization: {
    noEmitOnErrors: true,
    runtimeChunk: {
      name: 'manifest',
    },
    splitChunks: {
      minSize: 30000,
      minChunks: 1,
      maxAsyncRequests: 5,
      maxInitialRequests: 3,
      name: false,
      cacheGroups: {
        commons: {
          chunks: 'initial',
          minChunks: 2,
          maxInitialRequests: 5,
          minSize: 0,
        },
        vendor: {
          name: 'vendor',
          chunks: 'initial',
          reuseExistingChunk: false,
          test: /node_modules\/(.*)\.js/,
          enforce: true,
        },
        styles: {
          name: 'styles',
          test: /\.(scss|css)$/,
          chunks: 'all',
          minChunks: 1,
          reuseExistingChunk: true,
          enforce: true,
        },
      },
    },
  },
  module: {
    rules: [{
      test: /\.mjs$/,
      type: 'javascript/auto',
      use: [],
    }, {
      test: /\.js$/,
      use: [
        { loader: 'happypack/loader?id=js' },
      ],
      exclude: /node_modules/,
    }, {
      test: /\.js$/,
      include: /node_modules[\\\/]@?reactflow/,
      use: {
        loader: 'babel-loader',
        options: {
          presets: ['@babel/preset-env', '@babel/preset-react'],
        },
      },
    }, {
      test: /\.(gif|png|jpe?g|svg)$/,
      loader: 'url-loader?limit=8192&name=static/images/[hash].[ext]',
    }, {
      test: /\.css$/,
      use: [{
        loader: 'style-loader',
      }, {
        loader: 'css-loader',
        options: {
          esModule: false,
        },
      }],
    }, {
      test: /\.less$/,
      use: [{
        loader: 'style-loader',
      }, {
        loader: 'css-loader',
        options: {
          esModule: false,
          sourceMap: true,
        },
      }, {
        loader: 'less-loader',
        options: {
          sourceMap: true,
          lessOptions: {
            javascriptEnabled: true,
            paths: [
              path.resolve(__dirname, 'node_modules'),
              path.resolve(__dirname, 'src'),
            ],
          },
        },
      }],
    }],
  },
  plugins: [
    new webpack.DllReferencePlugin({
      manifest,
    }),
    new webpack.LoaderOptionsPlugin({
      options: {
        postcss() {
          autoprefixer({ browsers: ['> 0.04%'] });
        },
        debug: true,
      },
    }),
    new webpack.DefinePlugin({
      'process.env': {
        BACKEND_PROTOCOL: JSON.stringify(BACKEND_PROTOCOL),
        BACKEND_DOMAIN: JSON.stringify(BACKEND_DOMAIN),
        WSS_DOMAIN: JSON.stringify(WSS_DOMAIN),
        BUILD_TIME: JSON.stringify(moment().format()),
        ENV: JSON.stringify('development'),
        TOKEN: JSON.stringify(TOKEN),
      },
    }),
    new webpack.HotModuleReplacementPlugin(),
    new HtmlWebpackPlugin({
      title: 'BZY·AI',
      template: path.resolve(__dirname, 'src/template.html'),
      filename: 'index.html',
      vendorJsName: bundleConfig.vendors.js,
    }),
    new HappyPack({
      id: 'js',
      threadPool: happyThreadPool,
      loaders: ['babel-loader?cacheDirectory'],
      verbose: true,
    }),
  ],
};
