{"dependencies": {"@chatscope/chat-ui-kit-react": "1.10.1", "@chatscope/chat-ui-kit-styles": "1.4.0", "@chatui/core": "1.0.2", "@codemirror/lang-javascript": "6.2.2", "@codemirror/lang-json": "6.0.1", "@codemirror/lang-markdown": "6.2.0", "@codemirror/lang-python": "6.1.3", "@emotion/hash": "0.9.1", "@microsoft/fetch-event-source": "2.0.1", "@uiw/react-codemirror": "4.21.7", "ant-design-pro": "2.2.1", "antd": "4.24.14", "array-move": "3.0.1", "axios": "1.4.0", "blueimp-md5": "2.19.0", "braft-editor": "2.3.9", "braft-extensions": "0.1.1", "braft-utils": "3.0.12", "classnames": "2.3.2", "connected-react-router": "6.9.3", "debug": "4.3.4", "docx": "9.5.1", "eventemitter3": "4.0.7", "file-saver": "2.0.5", "history": "4.10.1", "intl-messageformat": "9.9.4", "js-sls-logger": "2.0.2", "jsonc-parser": "3.0.0", "jszip": "3.10.1", "lodash": "4.17.21", "marked": "4.3.0", "moment": "2.29.4", "node-html-parser": "5.2.0", "openai": "3.2.1", "prop-types": "15.8.1", "pyodide": "0.23.4", "qhistory": "1.1.0", "qnn-react-cron": "1.0.5", "qrcode.react": "3.1.0", "qs": "6.11.2", "react": "17.0.2", "react-color": "2.19.3", "react-copy-to-clipboard": "5.1.0", "react-dom": "17.0.2", "react-markdown": "7.1.2", "react-mde": "11.5.0", "react-redux": "8.0.5", "react-router": "5.2.0", "react-router-dom": "5.2.0", "react-sortable-hoc": "2.0.0", "recorder-core": "1.3.24040900", "redux": "4.2.1", "redux-logger": "3.0.6", "redux-pagan": "0.2.0", "redux-thunk": "2.4.2", "remark-gfm": "3.0.0", "showdown": "2.1.0", "xlsx": "0.18.5"}, "devDependencies": {"@babel/core": "7.21.8", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-decorators": "7.21.0", "@babel/plugin-proposal-do-expressions": "7.18.6", "@babel/plugin-proposal-export-default-from": "7.18.10", "@babel/plugin-proposal-export-namespace-from": "7.18.9", "@babel/plugin-proposal-function-bind": "7.18.9", "@babel/plugin-proposal-function-sent": "7.18.6", "@babel/plugin-proposal-json-strings": "7.18.6", "@babel/plugin-proposal-logical-assignment-operators": "7.20.7", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-numeric-separator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-pipeline-operator": "7.18.9", "@babel/plugin-proposal-throw-expressions": "7.18.6", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-syntax-import-meta": "7.10.4", "@babel/plugin-transform-runtime": "7.21.4", "@babel/preset-env": "7.21.5", "@babel/preset-react": "7.18.6", "@babel/runtime-corejs2": "7.21.5", "@webpack-cli/serve": "1.6.1", "assets-webpack-plugin": "5.1.1", "autoprefixer": "10.4.2", "babel-eslint": "10.1.0", "babel-loader": "8.2.3", "babel-plugin-import": "1.13.6", "clean-webpack-plugin": "1.0.1", "copy-webpack-plugin": "6.2.1", "css-loader": "4.3.0", "eslint": "7.12.1", "eslint-config-airbnb": "16.1.0", "eslint-plugin-babel": "5.3.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-react": "7.32.2", "file-loader": "6.2.0", "happypack": "5.0.1", "html-webpack-plugin": "4.5.0", "less": "3.12.2", "less-loader": "7.1.0", "mermaid": "9.1.1", "mini-css-extract-plugin": "1.3.1", "obs-websocket-js": "5.0.5", "optimize-css-assets-webpack-plugin": "5.0.4", "react-iframe": "1.8.5", "react-mentions": "4.4.10", "react-quill": "2.0.0", "react-rnd": "10.3.4", "react-scripts": "5.0.1", "reactflow": "11.7.0", "style-loader": "2.0.0", "stylelint": "13.7.2", "stylelint-config-standard": "20.0.0", "stylelint-order": "4.1.0", "terser-webpack-plugin": "4.2.3", "url-loader": "4.1.1", "uuid": "9.0.0", "webpack": "4.44.1", "webpack-cli": "3.3.12", "webpack-dev-server": "3.11.0"}, "engines": {"node": "16.19.1"}, "importSort": {".js": {"parser": "babylon", "style": "module"}}, "keywords": ["react", "redux", "less", "webpack", "antd", "eslint", "stylelint"], "main": "index.js", "name": "llmbot-playground-frontend", "repository": {"type": "git", "url": "**************:bzy/llmbot-playground-frontend.git"}, "scripts": {"build": "webpack --profile --config webpack.production.js", "build-stg": "webpack --profile --config webpack.staging.js", "dll": "webpack -p --progress --config webpack.dll.config.js", "gen-dll-build-time": "rm -rf webpack-dll/build-time.json && echo `date +%Y%m%d%H%M%S` > webpack-dll/build-time.json", "fix-js-lint": "git diff --name-only --cached | egrep '.js$' | xargs eslint --fix || true", "fix-less-lint": "git diff --name-only --cached | egrep '.less$' | xargs stylelint --fix || true", "fix-lint": "npm run fix-js-lint && npm run fix-less-lint", "link-files": "ln -sf `pwd`/scripts/commit-msg .git/hooks/commit-msg && ln -sf `pwd`/scripts/pre-commit .git/hooks/pre-commit", "start": "npm run link-files && webpack-dev-server --progress --profile --config webpack.local.js", "start-prod": "npm run link-files && webpack-dev-server --progress --profile --config webpack.production.js"}, "version": "1.0.0"}