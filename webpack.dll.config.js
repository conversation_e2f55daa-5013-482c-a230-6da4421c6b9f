const path = require('path');
const webpack = require('webpack');
const CleanWebpackPlugin = require('clean-webpack-plugin');
const AssetsPlugin = require('assets-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');

const vendors = [
  'ant-design-pro',
  'antd',
  'axios',
  'classnames',
  'connected-react-router',
  'debug',
  'eventemitter3',
  'intl-messageformat',
  'lodash',
  'moment',
  'prop-types',
  'qhistory',
  'qrcode.react',
  'qs',
  'xlsx',
  'blueimp-md5',
  'react',
  'react-color',
  'react-copy-to-clipboard',
  'react-dom',
  'react-redux',
  'react-router',
  'react-router-dom',
  'redux',
  'redux-logger',
  'redux-pagan',
  'redux-thunk',
  'react-dom/server',
];

const dllConfig = {
  entry: {
    vendors,
  },
  output: {
    path: path.resolve(__dirname, 'static/scripts', 'vendors-dll'),
    filename: 'dll.[name].[hash].js',
    library: '[name]_[hash]',
  },
  optimization: {
    minimizer: [
      new TerserPlugin({
        cache: true,
        parallel: true,
        sourceMap: false,
        extractComments: false,
        exclude: /\/node_modules/,
      }),
    ],
  },
  plugins: [
    new CleanWebpackPlugin(['static/scripts/vendors-dll']),
    new CleanWebpackPlugin(['webpack-dll/build']),
    new webpack.DllPlugin({
      path: path.resolve(__dirname, 'webpack-dll/build', 'manifest.json'),
      name: '[name]_[hash]',
      context: __dirname,
    }),
    new AssetsPlugin({
      filename: 'bundle-config.json',
      path: path.resolve(__dirname, 'webpack-dll/build'),
    }),
    new webpack.DefinePlugin({
      'process.env': {
        NODE_ENV: JSON.stringify('production'),
      },
    }),
  ],
};

module.exports = dllConfig;
