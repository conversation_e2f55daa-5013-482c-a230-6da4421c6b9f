# Setup Guide
> install node (版本参考 package.json -> engines -> node)
-

> install git
- [key-questions](https://github.com/inetfuture/technote/blob/master/git.md#key-questions)

> install vscode

> install vscode extension

# Development
> 因 `braft-editor` 作者不在维护. 解决 `dependency conflict` 需执行
```js
  npm config set legacy-peer-deps true
```

> 启动 dev server

```sh
# 获取代码
<NAME_EMAIL>:bzy/toc/dushu-plan-frontend.git
cd dushu-plan-frontend

# 安装依赖
npm install

# 对第三方NPM包预编译, 生成 dll.*.js 文件, 提升编译速度(不需要每次都重新编译那些固定不变的NPM包, 只有当依赖的NPM包更新时，才需要重新预编译)
npm run dll

# 只有依赖的NPM包更新时, 才需要执行下列命令, 生成最新预编译DLL文件对应的时间戳文件($(pwd)/webpack-dll/build-time.json, 该文件需要提交到远程仓库)
npm run gen-dll-build-time

# 启动 dev server
npm start
```
> npm start 参数
- PORT: 指定端口, 默认为 8080

> 常见问题
- 依赖安装失败, 检查 node 版本,建议参照 `package.json->engines`
- 依赖安装慢, 使用淘宝源 `npm install --registry=https://registry.npm.taobao.org`
- 默认端口 8080 被占用, 使用 `PORT=xxx npm start` 启动 dev server

# Deployment
> 编译代码

```sh
# 获取代码
<NAME_EMAIL>:bzy/toc/dushu-plan-frontend.git
cd dushu-plan-frontend

# 安装依赖
npm install

# 对第三方NPM包预编译, 生成 dll.*.js 文件, 提升编译速度(不需要每次都重新编译那些固定不变的NPM包, 只有当依赖的NPM包更新时，才需要重新预编译)
npm run dll

# build 代码并输出到 /build 文件夹
npm run build
```
> npm run build 参数
- ENV: `dev|prod` 默认值 `dev`
- BACKEND_PROTOCOL: `http|https` 默认值 `http`
- BACKEND_DOMAIN:

# Deploy
> 本地部署
- 安装[ossutil](https://help.aliyun.com/zh/oss/developer-reference/install-ossutil)
- 部署
  ```
    $ ./scripts/localBuild.sh #部署 Staging
    $ ./scripts/localBuild.sh prod #部署 Prod
  ```

> 常见问题
- 依赖安装失败, 检查 node 版本,建议参照 `package.json->engines`
- 依赖安装慢, 使用淘宝源 `npm install --registry=https://registry.npm.taobao.org`
- 依赖找不到, 需要运行 `npm install`. 建议每次 build 前运行 `npm install`.
