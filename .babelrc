{"presets": [["@babel/preset-env", {"modules": false, "useBuiltIns": "usage", "corejs": 2}], "@babel/preset-react"], "plugins": [["import", {"libraryName": "antd", "style": "css"}], ["@babel/plugin-proposal-decorators", {"legacy": true}], ["@babel/plugin-transform-runtime", {"corejs": 2, "useESModules": true}], "@babel/plugin-syntax-dynamic-import", "@babel/plugin-syntax-import-meta", "@babel/plugin-proposal-class-properties", "@babel/plugin-proposal-json-strings", "@babel/plugin-proposal-function-sent", "@babel/plugin-proposal-export-namespace-from", "@babel/plugin-proposal-numeric-separator", "@babel/plugin-proposal-throw-expressions", "@babel/plugin-proposal-export-default-from", "@babel/plugin-proposal-logical-assignment-operators", "@babel/plugin-proposal-optional-chaining", ["@babel/plugin-proposal-pipeline-operator", {"proposal": "minimal"}], "@babel/plugin-proposal-nullish-coalescing-operator", "@babel/plugin-proposal-do-expressions", "@babel/plugin-proposal-function-bind"]}