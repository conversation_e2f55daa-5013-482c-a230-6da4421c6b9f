{"env": {"browser": true, "es6": true, "node": true}, "extends": "airbnb", "globals": {"app.less": true}, "parser": "babel-es<PERSON>", "parserOptions": {"ecmaFeatures": {"jsx": true, "legacyDecorators": true, "experimentalObjectRestSpread": true}, "sourceType": "module"}, "plugins": ["babel", "react"], "root": true, "rules": {"arrow-body-style": ["error", "always"], "comma-dangle": ["error", "always-multiline"], "function-paren-newline": ["error", "consistent"], "import/extensions": "off", "import/first": "off", "import/no-extraneous-dependencies": "off", "import/no-unresolved": ["error", {"ignore": ["^[~]", "^engine$"]}], "import/prefer-default-export": "off", "jsx-a11y/alt-text": "off", "jsx-a11y/anchor-is-valid": "off", "jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/media-has-caption": "off", "jsx-a11y/no-noninteractive-element-interactions": "off", "jsx-a11y/no-static-element-interactions": "off", "max-len": ["error", 120], "max-lines": ["error", {"max": 1000}], "no-console": "error", "no-mixed-operators": "off", "no-plusplus": "off", "no-restricted-syntax": ["error", "WithStatement", "BinaryExpression[operator='in']"], "no-underscore-dangle": "off", "object-curly-newline": "off", "prefer-destructuring": "warn", "react/forbid-prop-types": "off", "react/no-did-mount-set-state": "off", "react/jsx-filename-extension": "off", "react/no-danger": "off", "react/prefer-stateless-function": "off", "react/require-default-props": "off", "react/sort-comp": ["error", {"groups": {"lifecycle": ["displayName", "propTypes", "defaultProps", "i18nRootPath", "state", "constructor", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "shouldComponentUpdate", "componentWillUpdate", "componentDidUpdate", "componentWillUnmount"]}, "order": ["static-methods", "lifecycle", "everything-else", "/^_?on.+$/", "/^_?render.+$/", "render"]}], "spaced-comment": ["error", "always", {"block": {"exceptions": ["*"]}}]}}