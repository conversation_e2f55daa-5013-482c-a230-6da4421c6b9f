{"extends": "stylelint-config-standard", "plugins": ["stylelint-order"], "rules": {"at-rule-empty-line-before": null, "at-rule-name-space-after": null, "comment-empty-line-before": null, "declaration-bang-space-before": null, "declaration-empty-line-before": null, "function-comma-newline-after": null, "function-max-empty-lines": null, "function-name-case": null, "function-parentheses-newline-inside": null, "function-whitespace-after": null, "number-leading-zero": null, "number-no-trailing-zeros": null, "order/order": ["custom-properties", "dollar-variables", "declarations", "rules"], "order/properties-order": [["display", "position", "top", "right", "bottom", "left", "float", "clear", "flex-basis", "flex-direction", "flex-flow", "flex-grow", "flex-shrink", "flex-wrap", "align-items", "align-self", "justify-content", "width", "min-width", "max-width", "height", "min-height", "max-height", "overflow", "margin", "margin-top", "margin-right", "margin-bottom", "margin-left", "padding", "padding-top", "padding-right", "padding-bottom", "padding-left", "outline", "border", "border-top", "border-right", "border-bottom", "border-left", "background", "text-align", "vertical-align", "font-family", "font-size", "font-style", "font-variant", "font-weight", "line-height", "color"], {"unspecified": "bottomAlphabetical"}], "rule-empty-line-before": null, "selector-combinator-space-after": null, "selector-list-comma-newline-after": null, "selector-pseudo-class-no-unknown": null, "selector-pseudo-element-colon-notation": null, "shorthand-property-no-redundant-values": null, "unit-no-unknown": null, "value-list-max-empty-lines": null}}