/* eslint-disable no-useless-escape */
const path = require('path');

const autoprefixer = require('autoprefixer');
const CleanWebpackPlugin = require('clean-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const moment = require('moment');
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');

const webpack = require('webpack');
const bundleConfig = require('./webpack-dll/build/bundle-config.json');
const manifest = require('./webpack-dll/build/manifest.json');

const {
  BACKEND_PROTOCOL = 'https',
  BACKEND_DOMAIN = 'aiapi.bzy.ai',
  WSS_DOMAIN = 'wss://aiapi.bzy.ai',
  // eslint-disable-next-line max-len
  TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwaWQiOjIwMTA1OSwic2NvcGUiOiJtZW1iZXIiLCJwbGF0Zm9ybSI6InBsYXlncm91bmQiLCJ1aWQiOiI3QUtqOHZHbXdVdTNvMWdHRmg4aU1lIiwiZXhwIjoxNzE3MTY0NzQ2fQ.Fsro_iO114VkmgEWtpz4s1um-Z4begOoMnVYZmgZSHM',
} = process.env;

module.exports = {
  mode: 'production',
  devtool: 'cheap-module-source-map',
  stats: { children: false },
  entry: { app: path.resolve(__dirname, 'src/index.js') },
  output: {
    path: path.resolve(__dirname, 'build'),
    publicPath: '/',
    filename: '[name].[chunkhash].js',
    chunkFilename: '[chunkhash].js',
  },
  resolve: { alias: { '~': path.resolve(__dirname, 'src') } },
  optimization: {
    runtimeChunk: {
      name: 'manifest',
    },
    minimizer: [
      new TerserPlugin({
        cache: true,
        parallel: true,
        sourceMap: false,
        extractComments: false,
        exclude: /\/node_modules/,
      }),
      new OptimizeCSSAssetsPlugin({}),
    ],
    splitChunks: {
      chunks: 'async',
      minSize: 30000,
      minChunks: 1,
      maxAsyncRequests: 5,
      maxInitialRequests: 3,
      name: false,
      cacheGroups: {
        vendor: {
          name: 'vendor',
          chunks: 'initial',
          priority: -10,
          reuseExistingChunk: false,
          test: /node_modules\/(.*)\.js/,
        },
        styles: {
          name: 'styles',
          test: /\.(scss|css)$/,
          chunks: 'all',
          minChunks: 1,
          reuseExistingChunk: true,
          enforce: true,
        },
      },
    },
  },
  module: {
    rules: [{
      test: /\.mjs$/,
      type: 'javascript/auto',
      use: [],
    }, {
      test: /\.js$/,
      use: [
        { loader: 'babel-loader' },
      ],
      exclude: /node_modules/,
    }, {
      test: /\.js$/,
      include: /node_modules[\\\/]@?reactflow/,
      use: {
        loader: 'babel-loader',
        options: {
          presets: ['@babel/preset-env', '@babel/preset-react'],
        },
      },
    }, {
      test: /\.(gif|png|jpe?g|svg)$/,
      loader: 'url-loader?limit=8192&name=static/images/[hash].[ext]',
    }, {
      test: /\.css$/,
      use: [{
        loader: MiniCssExtractPlugin.loader,
      }, {
        loader: 'css-loader',
        options: {
          esModule: false,
        },
      }],
    }, {
      test: /\.less$/,
      use: [{
        loader: 'style-loader',
      }, {
        loader: 'css-loader',
        options: {
          esModule: false,
          sourceMap: false,
        },
      }, {
        loader: 'less-loader',
        options: {
          sourceMap: false,
          lessOptions: {
            javascriptEnabled: true,
            paths: [
              path.resolve(__dirname, 'node_modules'),
              path.resolve(__dirname, 'src'),
            ],
          },
        },
      }],
    }],
  },
  plugins: [
    new CleanWebpackPlugin(['build']),
    new webpack.DllReferencePlugin({
      manifest,
    }),
    new webpack.LoaderOptionsPlugin({
      options: {
        postcss() {
          autoprefixer({ browsers: ['> 0.04%'] });
        },
      },
    }),
    new webpack.DefinePlugin({
      'process.env': {
        // This can reduce react lib size and disable some dev feactures like props validation
        NODE_ENV: JSON.stringify('production'),
        ENV: JSON.stringify('production'),
        BACKEND_PROTOCOL: JSON.stringify(BACKEND_PROTOCOL),
        BACKEND_DOMAIN: JSON.stringify(BACKEND_DOMAIN),
        WSS_DOMAIN: JSON.stringify(WSS_DOMAIN),
        BUILD_TIME: JSON.stringify(moment().format()),
        TOKEN: JSON.stringify(TOKEN),
      },
    }),
    new MiniCssExtractPlugin({ filename: 'app.min.css' }),
    new CopyWebpackPlugin({
      patterns: [
        { from: 'static', to: 'static' },
        { from: 'static/robots.txt', to: 'robots.txt' },
      ],
    }),
    new HtmlWebpackPlugin({
      title: '...',
      filename: 'index.html',
      template: path.resolve(__dirname, 'src/template.html'),
      vendorJsName: bundleConfig.vendors.js,
    }),
  ],
};
