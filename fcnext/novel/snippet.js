/* eslint-disable no-await-in-loop */
/* eslint-disable camelcase */
const fs = require('fs');
const path = require('path');
const request = require('request');
const _ = require('lodash');
const OpenAI = require('openai');

const openai = new OpenAI({
  baseURL: 'https://aiapi.chat2chat.net/v1',
  apiKey: process.env.OPENAI_API_KEY,
});

const rootDir = '/home/<USER>/novels';
const globalData = { promptData: {}, keywordData: {} };
let currentSnippet = {};

const retrieveRelatedSettings = (data) => {
  data.snippet.final_content = ''; // eslint-disable-line
  const options = {
    method: 'POST',
    url: `https://${globalData.env}api.bzy.ai/v2/chatbot/retrieve-related-settings`,
    headers: {
      'grpc-metadata-token': globalData.token,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  };

  return new Promise((resolve, reject) => {
    request(options, (error, response, body) => {
      if (error) {
        reject(error);
      }
      resolve(JSON.parse(body));
    });
  });
};

const findExtractGuide = (data) => {
  data.snippet.final_content = ''; // eslint-disable-line
  const options = {
    method: 'POST',
    url: `https://${globalData.env}api.bzy.ai/v2/chatbot/find-extract-guide`,
    headers: {
      'grpc-metadata-token': globalData.token,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  };

  return new Promise((resolve, reject) => {
    request(options, (error, response, body) => {
      if (error) {
        reject(error);
      }
      resolve(JSON.parse(body));
    });
  });
};

const getSnippet = (pId, name, fileName, snippetId) => {
  const filePath = `${rootDir}/${pId}/${name}/${fileName}.json`;
  const fileObj = JSON.parse(fs.readFileSync(filePath, 'utf8'));
  return fileObj.snippets[snippetId];
};

const getChapters = async (folderPath) => {
  const files = [];
  if (fs.existsSync(folderPath)) {
    const dirContent = fs.readdirSync(folderPath);
    for (let index = 0; index < dirContent.length; index++) {
      const filePath = path.join(folderPath, dirContent[index]);
      const stats = await fs.promises.stat(filePath); // eslint-disable-line
      if (stats.isFile() && !['keyword.json', 'prompt.json'].includes(dirContent[index])) {
        files.push(dirContent[index]);
      }
    }
  }

  return _.sortBy(files);
};

const getChapter = (fileName) => {
  const filePath = `${globalData.basePath}/${fileName}`;
  return JSON.parse(fs.readFileSync(filePath, 'utf8'));
};

/**
   * 获取prompt data, 当前prompt 一定存在
   * requirement: '',
   * requirement_update_setting: '',
   * requirement_regen_setting: '',
   * system_author: '',
   * analyze_setting_cot: '',
   * analyze_setting: '',
   */
const getPrompt = (pId, name) => {
  const filePath = `${rootDir}/${pId}/${name}/prompt.json`;
  const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
  return data;
};

/**
   * 获取keywords data, 当前keywords 一定存在
   * include_words: [],
   * exclude_words: [],
   */
const getKeywords = (pId, name) => {
  const filePath = `${rootDir}/${pId}/${name}/keyword.json`;
  const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
  return data;
};

const chatCompletions = async (params, isSmall = false) => {
  const { system_author } = globalData.promptData;
  const completion = await openai.chat.completions.create({
    messages: [
      { role: 'system', content: system_author },
      { role: 'user', content: params.user_command },
    ],
    model: 'gpt-4-0613',
    temperature: 0,
    max_tokens: isSmall ? 32 : 3000,
  });
  return completion.choices[0].message.content;
};

const convertTextToSettings = (text) => {
  const lines = text.split('\n');
  const settings = [];
  let category = '';
  for (let line of lines) {
    line = line.trim();
    if (line) {
      if (line.startsWith('*')) {
        category = line.slice(1).trim();
      } else {
        const word = line.replace('- ', '');
        settings.push({ word, new_word: '', category });
      }
    }
  }
  return settings;
};

const convertSettingsToText = (settings) => {
  const groupedSettings = {};
  for (const setting of settings) {
    if (!_.has(groupedSettings, setting.category)) {
      groupedSettings[setting.category] = [];
    }
    groupedSettings[setting.category].push(setting.word);
  }

  let result = '';
  for (const category in groupedSettings) {
    if (_.has(groupedSettings, category)) {
      result += `* ${category}\n`;
      for (const word of groupedSettings[category]) {
        result += `  - ${word}\n`;
      }
    }
  }

  return result;
};

const convertSettingsWithValueToText = (settings) => {
  const groupedSettings = {};
  for (const setting of settings) {
    if (!_.has(groupedSettings, setting.category)) {
      groupedSettings[setting.category] = [];
    }
    groupedSettings[setting.category].push({ word: setting.word, new_word: setting.new_word || setting.newWord });
  }

  let result = '';
  for (const category in groupedSettings) {
    if (_.has(groupedSettings, category)) {
      result += `* ${category}\n`;
      for (const { word, new_word } of groupedSettings[category]) {
        result += `  - ${word}: ${new_word}\n`;
      }
    }
  }

  return result;
};

const convertTextToSettingsWithValue = (text) => {
  const lines = text.split('\n');
  const settings = [];
  let currentCategory = null;

  for (const line of lines) {
    if (line.startsWith('* ')) {
      currentCategory = line.slice(2);
    } else if (line.trim().startsWith('- ')) {
      const [word, new_word] = line.trim().slice(2).split(': ');
      settings.push({ word, category: currentCategory, new_word });
    }
  }

  return settings;
};

const updateSettingsRepository = (newSettings) => {
  const settingsRepository = {};
  for (const newSetting of newSettings) {
    settingsRepository[newSetting.word] = newSetting;
  }
  return settingsRepository;
};

const retrievePreviousSettings = () => {
  let settingsRepository = {};
  if (globalData.chapterId - 1 > 0) {
    for (let id = globalData.chapterId - 1; id < globalData.chapterId; id++) {
      const fileName = globalData.chapters[id];
      const chapter = getChapter(globalData.basePath, fileName);
      for (const snippet of chapter.snippets) {
        const settingObj = updateSettingsRepository(snippet.updated_settings);
        settingsRepository = { ...settingsRepository, ...settingObj };
      }
    }
  }
  return settingsRepository;
};

const retrievePreviousSettingsSnippet = (chapter, snippetId) => {
  const settingsRepository = { ...globalData.chapterSettingsRepository };
  if (snippetId - 1 >= 0) {
    for (let id = 0; id < snippetId; id++) {
      for (const setting of chapter.snippets[id].updated_settings) {
        settingsRepository[setting.word] = setting;
      }
    }
  }
  return settingsRepository;
};

const checkSameWord = async (context, new_word, words) => {
  let user_command = globalData.promptData.same_word_setting;
  user_command = user_command.replace(/<<context>>/g, context);
  user_command = user_command.replace(/<<new_word>>/g, new_word);
  user_command = user_command.replace(/<<words>>/g, words);

  const txt = await chatCompletions({ user_command }, true);
  return _.trim(txt) === '是';
};

const regenSetting = (context, related_settings, word) => {
  let user_command = globalData.promptData.requirement_regen_setting;
  user_command = user_command.replace(/<<context>>/g, context);
  user_command = user_command.replace(/<<related_settings>>/g, related_settings);
  user_command = user_command.replace(/<<word>>/g, word);

  const txt = chatCompletions({ user_command });
  return txt;
};

const getUsedSettings = (inputSetting, conflictSettings, settingsRepository) => {
  const allSettings = [inputSetting].concat(conflictSettings);
  const checkCategories = [...new Set(allSettings.map((s) => { return s.category; }))];
  const checkSettings = [];

  for (const setting of Object.values(settingsRepository)) {
    if (checkCategories.includes(setting.category)) {
      checkSettings.push(setting);
    }
  }
  return convertSettingsWithValueToText(checkSettings);
};

const getIncludeExcludeWords = (content) => {
  const { keywordData } = globalData;
  const manual_include = _.filter(keywordData?.include_words, (x) => { return content.includes(x); });
  const manaul_exclude = _.filter(keywordData?.exclude_words, (x) => { return content.includes(x); });
  return { manual_include, manaul_exclude };
};

const getExtractGuide = async (content) => {
  const { manual_include, manaul_exclude } = getIncludeExcludeWords(content);
  const { requirement, analyze_setting_cot } = globalData.promptData;

  let user_command = analyze_setting_cot;
  user_command = user_command.replace(/<<input_content>>/g, content);
  user_command = user_command.replace(/<<manual_include>>/g, manual_include);
  user_command = user_command.replace(/<<manual_exclude>>/g, manaul_exclude);
  user_command = user_command.replace(/<<requirement>>/g, requirement);
  const txt = await chatCompletions({ user_command });
  return txt;
};

const getExtractedSettings = async (snippet = {}) => {
  const { manual_include, manaul_exclude } = getIncludeExcludeWords(snippet?.content);
  const { requirement, analyze_setting } = globalData.promptData;
  let user_command = analyze_setting;
  user_command = user_command.replace(/<<input_content>>/g, snippet?.content);
  user_command = user_command.replace(/<<manual_include>>/g, manual_include);
  user_command = user_command.replace(/<<manual_exclude>>/g, manaul_exclude);
  user_command = user_command.replace(/<<requirement>>/g, requirement);
  user_command = user_command.replace(/<<cot>>/g, snippet?.extract_guide);
  const txt = await chatCompletions({ user_command });
  return convertTextToSettings(txt);
};

const getUpdatedSettings = async () => {
  const chapter = getChapter(globalData.chapters[globalData.chapterId]);
  const settingsRepository = retrievePreviousSettingsSnippet(chapter, globalData.snippetId);
  const relatedSettings = await retrieveRelatedSettings({
    snippet: currentSnippet, settings_repository: settingsRepository,
  });
  const extractedSettingsString = convertSettingsToText(currentSnippet.extracted_settings);
  const relatedSettingsString = convertSettingsWithValueToText(relatedSettings);
  const { requirement_update_setting, generate_new_settings } = globalData.promptData;
  let user_command = generate_new_settings;
  user_command = user_command.replace(/<<input_content>>/g, currentSnippet.content);
  user_command = user_command.replace(/<<extracted_settings>>/g, extractedSettingsString);
  user_command = user_command.replace(/<<related_settings>>/g, relatedSettingsString);
  user_command = user_command.replace(/<<requirement>>/g, requirement_update_setting);
  const txt = await chatCompletions({ user_command });

  return convertTextToSettingsWithValue(txt);
};

const checkDuplicatedSettings = async () => {
  const chapter = getChapter(globalData.chapters[globalData.chapterId]);
  const settingsRepository = retrievePreviousSettingsSnippet(chapter, globalData.snippetId);
  const settingsRepositoryReversedDict = {};
  _.values(settingsRepository).forEach((x) => {
    if (_.isUndefined(settingsRepositoryReversedDict[x.new_word])) {
      settingsRepositoryReversedDict[x.new_word] = [x];
    } else {
      settingsRepositoryReversedDict[x.new_word].push(x);
    }
  });

  const finalSettings = [];
  for (const setting of currentSnippet?.updated_settings) {
    let matched = false;
    if (_.has(settingsRepositoryReversedDict, setting.new_word)) {
      const repositoryNewWords = settingsRepositoryReversedDict[setting.new_word].map((s) => { return s.word; });
      for (const values of repositoryNewWords) {
        if (!matched && values.includes(setting.word)) {
          matched = true;
          break;
        }
      }

      let context = '';
      if (!matched) { // 直接匹配无法匹配
        const guides = await findExtractGuide({ snippet: currentSnippet, word: setting.word });
        context = guides.join('\n');
        matched = await checkSameWord(context, setting.word, `[${repositoryNewWords.join(', ')}]`);
      }

      if (matched) {
        finalSettings.push(setting);
        settingsRepositoryReversedDict[setting.new_word].push(setting);
        settingsRepository[setting.word] = setting;
      } else {
        const conflictSettings = settingsRepositoryReversedDict[setting.new_word];
        const relatedSettings = getUsedSettings(setting, conflictSettings, settingsRepository);
        const newSettingWord = regenSetting(context, relatedSettings, setting.word);
        setting.new_word = newSettingWord;

        if (!_.has(settingsRepositoryReversedDict, setting.new_word)) {
          settingsRepositoryReversedDict[setting.new_word] = [setting];
        } else {
          settingsRepositoryReversedDict[setting.new_word].push(setting);
        }
        settingsRepository[setting.word] = setting;
      }
    } else {
      finalSettings.push(setting);
      settingsRepositoryReversedDict[setting.new_word] = [setting];
      settingsRepository[setting.word] = setting;
    }
  }

  return finalSettings;
};

const generateSnippetContent = async () => {
  const finalSettings = await checkDuplicatedSettings();
  const updatedSettingsString = convertSettingsWithValueToText(finalSettings);
  const { requirement, rewrite_content_setting } = globalData.promptData;
  let user_command = rewrite_content_setting;
  user_command = user_command.replace(/<<input_content>>/g, currentSnippet.content);
  user_command = user_command.replace(/<<updated_settings>>/g, updatedSettingsString);
  user_command = user_command.replace(/<<requirement>>/g, requirement);
  const txt = await chatCompletions({ user_command });
  return txt;
};

const rewriteFile = (pId, name, fileName, data, snippetId) => {
  const filePath = `${rootDir}/${pId}/${name}/${fileName}.json`;
  const fileObj = JSON.parse(fs.readFileSync(filePath, 'utf8'));
  fileObj.snippets[snippetId] = { ...fileObj.snippets[snippetId], ...data };
  fs.writeFileSync(filePath, JSON.stringify(fileObj));
};

const initData = async (req) => {
  const { p_id: pId, name, file_name, idx, env } = JSON.parse(req.body.toString());
  const chapters = await getChapters(`${rootDir}/${pId}/${name}`);
  const chapterId = chapters.findIndex((x) => { return x === `${file_name}.json`; });
  globalData.chapters = chapters;
  globalData.chapterId = chapterId;
  globalData.basePath = `${rootDir}/${pId}/${name}`;
  globalData.snippetId = idx;
  globalData.env = env;

  const promptData = getPrompt(pId, name);
  const keywordData = getKeywords(pId, name);
  globalData.promptData = promptData;
  globalData.keywordData = keywordData;
  globalData.chapterSettingsRepository = retrievePreviousSettings();
  globalData.token = req.headers['grpc-metadata-token'];

  currentSnippet = getSnippet(pId, name, file_name, idx);
};

exports.handler = async (req, resp, context) => { // eslint-disable-line
  resp.setHeader('Content-Type', 'application/json');
  const routeType = _.last(_.head(req.url.split('?')).split('/'));
  const { p_id: pId, name, file_name, idx } = JSON.parse(req.body.toString());
  await initData(req);

  const result = {};
  switch (routeType) {
    case 'extract_guide':
      result.extractGuide = await getExtractGuide(currentSnippet?.content);
      break;
    case 'extracted_settings':
      result.extractedSettings = await getExtractedSettings(currentSnippet);
      break;
    case 'updated_settings':
      result.updatedSettings = await getUpdatedSettings(currentSnippet);
      break;
    case 'final_content':
      result.finalContent = await generateSnippetContent();
      break;
    default:
      break;
  }

  rewriteFile(pId, name, file_name, { [routeType]: result[_.camelCase(routeType)] }, idx);

  resp.send(JSON.stringify(result));
};
