const fs = require('fs');
const _ = require('lodash');

const rootDir = '/home/<USER>/novels';
exports.handler = async (req, resp, context) => { // eslint-disable-line
  resp.setHeader('Content-Type', 'application/json');
  const pId = _.last(_.head(req.url.split('?')).split('/'));

  const { name } = req.method === 'GET' ? req?.queries : JSON.parse(req.body.toString());
  const filePath = `${rootDir}/${pId}/${name}/prompt.json`;


  if (req.method === 'GET') {
    if (!fs.existsSync(filePath)) {
      const initData = JSON.stringify({
        requirement: '',
        requirement_update_setting: '',
        requirement_regen_setting: '',
        system_author: '',
        analyze_setting_cot: '',
      });
      fs.writeFileSync(filePath, JSON.stringify(initData));
      resp.send(JSON.stringify(initData));
      return;
    }

    const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    resp.send(JSON.stringify(data));
    return;
  }

  const localData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
  const newData = JSON.parse(req.body.toString());
  fs.writeFileSync(filePath, JSON.stringify({ ...localData, ...newData }));
  resp.send('ok');
};
