/* eslint-disable camelcase */
/* eslint-disable no-case-declarations */
const fs = require('fs');
const _ = require('lodash');

const rootDir = '/home/<USER>/novels';
exports.handler = async (req, resp, context) => { // eslint-disable-line
  resp.setHeader('Content-Type', 'application/json');
  const pId = _.last(_.head(req.url.split('?')).split('/'));

  const { name } = req.method === 'GET' ? req?.queries : JSON.parse(req.body.toString());
  const filePath = `${rootDir}/${pId}/${name}/keyword.json`;

  if (req.method === 'GET') {
    if (!fs.existsSync(filePath)) {
      fs.writeFileSync(filePath, JSON.stringify({ include_words: [], exclude_words: [] }));
      resp.send(JSON.stringify({ include_words: [], exclude_words: [] }));
      return;
    }

    const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    resp.send(JSON.stringify(data));
    return;
  }

  const { include_words, exclude_words } = JSON.parse(req.body.toString());
  fs.writeFileSync(filePath, JSON.stringify({ include_words, exclude_words }));
  resp.send('ok');
};
