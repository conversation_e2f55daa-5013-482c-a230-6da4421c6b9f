/* eslint-disable no-case-declarations, camelcase */
const fs = require('fs');
const _ = require('lodash');
// const path = require('path');
const tiktoken = require('tiktoken');

const enc = tiktoken.encoding_for_model('gpt-4');
const rootDir = '/home/<USER>/novels';
const CONTENT_SPLIT_LIMIT = 1500;
const tokenCount = (text) => {
  return enc.encode(text).length;
};

const splitContentLines = (inputLines, maxSize, sep = '\n') => {
  const blocks = [];
  for (const line of inputLines) {
    if (!blocks.length) {
      blocks.push(line);
      continue; // eslint-disable-line
    }

    const prevBlock = blocks[blocks.length - 1];
    if (tokenCount(prevBlock) + tokenCount(line) < maxSize) {
      blocks[blocks.length - 1] += sep + line;
    } else {
      blocks.push(line);
    }
  }

  return blocks;
};

const checkFileExists = (filePath) => {
  return new Promise((resolve, reject) => {
    fs.access(filePath, fs.constants.F_OK, (err) => {
      if (err) {
        reject(err);
      }
      resolve(true);
    });
  });
};

const saveJsonFile = (filePath, data) => {
  return new Promise((resolve, reject) => {
    fs.writeFile(filePath, JSON.stringify(data), (err) => {
      if (err) {
        reject(err);
      }
      resolve(true);
    });
  });
};

const mergeLocalData = async (filePath, data) => {
  const fileStr = fs.readFileSync(filePath, 'utf8');
  const obj = JSON.parse(fileStr);
  const params = { ...obj, ...data };
  await saveJsonFile(filePath, params);
  return params;
};

const loadTxtFile = (filePath) => {
  const fileStr = fs.readFileSync(filePath, 'utf8');
  const { content } = JSON.parse(fileStr);
  const lines = content.split('\n').map((l) => { return l.trim(); }).filter((l) => { return l; });
  return { lines, words: content.length, tokens: tokenCount(lines.join('\n')) };
};

const setupChapter = async (filePath) => {
  const { lines, tokens, words } = loadTxtFile(filePath);
  const blocks = splitContentLines(lines, CONTENT_SPLIT_LIMIT);
  const snippets = blocks.map((x) => {
    return {
      content: x,
      extract_guide: '',
      extracted_settings: [],
      updated_settings: [],
      final_content: '',
    };
  });

  const data = await mergeLocalData(filePath, { tokens, words, snippets });
  return data;
};


exports.handler = async (req, resp, context) => { // eslint-disable-line
  resp.setHeader('Content-Type', 'application/json');
  const pId = _.last(req.url.split('/'));

  if (!_.isInteger(_.toSafeInteger(pId))) {
    resp.send(JSON.stringify({ code: -1 }));
    return;
  }

  const { name, file, type, ...jsonData } = JSON.parse(req.body.toString());
  const filePath = `${rootDir}/${pId}/${name}/${file}.json`;

  const hasExist = await checkFileExists(filePath);
  if (!hasExist) {
    resp.send(JSON.stringify({ code: -1 }));
    return;
  }
  let result = {};

  switch (type) {
    case 'save':
      await saveJsonFile(filePath, jsonData);
      break;
    case 'setup':
      result = await setupChapter(filePath);
      break;
    default:
      break;
  }

  resp.send(JSON.stringify(result));
};
