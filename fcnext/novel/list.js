/* eslint-disable no-case-declarations, camelcase */
const fs = require('fs');
const _ = require('lodash');
const path = require('path');

const rootDir = '/home/<USER>/novels';

const getNovels = async (pId) => {
  const folderPath = `${rootDir}/${pId}`;
  if (!fs.existsSync(folderPath)) {
    fs.mkdirSync(folderPath);
    return [];
  }

  const items = [];
  const dirContent = fs.readdirSync(folderPath);
  for (let index = 0; index < dirContent.length; index++) {
    const filePath = path.join(folderPath, dirContent[index]);
    const stats = await fs.promises.stat(filePath); // eslint-disable-line
    if (stats.isDirectory()) {
      items.push({ name: dirContent[index], createdAt: stats.ctimeMs });
    }
  }
  return items;
};

const getChapters = async (cPath) => {
  const files = [];
  const folderPath = `${rootDir}/${cPath}`;
  if (fs.existsSync(folderPath)) {
    const dirContent = fs.readdirSync(folderPath);
    for (let index = 0; index < dirContent.length; index++) {
      const filePath = path.join(folderPath, dirContent[index]);
      const stats = await fs.promises.stat(filePath); // eslint-disable-line
      if (stats.isFile()) {
        files.push({ name: dirContent[index], createdAt: stats.ctimeMs });
      }
    }
  }

  return files;
};

exports.handler = async (req, resp, context) => { // eslint-disable-line
  resp.setHeader('Content-Type', 'application/json');
  const { p_id, name, file } = req?.queries;
  if (_.isEmpty(p_id)) {
    resp.send(JSON.stringify({ code: -1 }));
    return;
  }
  const pathname = _.last(_.head(req.url.split('?')).split('/'));

  if (pathname === 'detail') {
    const data = fs.readFileSync(`${rootDir}/${p_id}/${name}/${file}.json`, 'utf8');
    resp.send(data);
    return;
  }

  const result = {};
  switch (req.method) {
    case 'GET':
      result.items = _.isEmpty(name) ? await getNovels(p_id) : await getChapters(`${p_id}/${name}`);
      break;
    case 'POST':
      const { story_name, chapter_name } = JSON.parse(req.body.toString());
      const folderPath = `${p_id}/${story_name}`;
      if (_.isEmpty(chapter_name)) {
        result.items = await getNovels(folderPath);
      } else {
        const filePath = path.join(`${rootDir}/${folderPath}`, `${chapter_name}.json`);
        fs.writeFileSync(filePath, JSON.stringify({}, null, 2));
        result.items = await getChapters(folderPath);
      }
      break;
    default:
      break;
  }

  resp.send(JSON.stringify(result));
};
