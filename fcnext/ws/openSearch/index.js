const WebSocket = require('ws');
const crypto = require('crypto');
const _ = require('lodash');
const fs = require('fs');
const request = require('request');

const onSearch = (ws, txt) => {
  return new Promise((resolve, reject) => {
    try {
      ws.send(JSON.stringify({ content: txt }));
      ws.on('message', (data) => {
        const obj = JSON.parse(`${data}`);
        if (obj.type === 201) {
          ws.send(obj.action);
        } else if (obj.type === 1 && obj.card_type === 'search_result' && obj.target === 'finish') {
          const { messages } = _.head(obj.arguments);
          const { sourceAttributions } = _.head(messages);
          resolve(sourceAttributions);
        }
      });
    } catch (error) {
      reject(error);
    }
  });
};

const waitForOpen = async (ws) => {
  return new Promise((resolve, reject) => {
    ws.on('open', resolve);
    ws.on('error', reject);
  });
};

const login = async () => {
  const options = {
    method: 'POST',
    url: 'https://api.tiangong.cn/usercenter/v1/passport/login',
    headers: { 'K-Client-Id': '200004', 'Content-Type': 'application/json' },
    body: JSON.stringify({ phone: `${process.env.phone}`, password: `${process.env.pwd}` }),
  };
  const { data } = await new Promise((resolve, reject) => {
    request(options, (error, response) => {
      if (error) { reject(error); }
      resolve(JSON.parse(response.body));
    });
  });

  return data.token;
};


const getToken = async () => {
  const tokenFilePath = '/home/<USER>/open-search-token.json';
  const tokenStr = fs.readFileSync(tokenFilePath);
  const { token } = JSON.parse(tokenStr);
  const options = {
    method: 'GET',
    url: 'https://api-search.tiangong.cn/chat-user/api/user/info',
    headers: { Cookie: `k_sso_token=${token}` },
  };

  const data = await new Promise((resolve, reject) => {
    request(options, (error, response) => {
      if (error) { reject(error); }
      resolve(JSON.parse(response.body));
    });
  });
  if (data?.code === 200) {
    return token;
  }

  const newToken = await login();
  await fs.writeFileSync(tokenFilePath, JSON.stringify({ token: newToken }));
  return newToken;
};

exports.handler = async (req, resp, context) => { // eslint-disable-line
  const text = decodeURIComponent(req.queries.q);
  resp.setHeader('Content-Type', 'application/json');
  const token = await getToken();

  const ws = new WebSocket('wss://api-search.tiangong.cn/chat-user/wss/tg', {
    headers: {
      'Sec-Websocket-Extensions': 'permessage-deflate; client_max_window_bits',
      'Sec-Websocket-Key': crypto.randomBytes(16).toString('base64'),
      'Sec-Websocket-Version': 13,
      Cookie: `k_sso_token=${token};`,
    },
  });
  await waitForOpen(ws);
  const items = await onSearch(ws, text);
  resp.send(JSON.stringify({ items }));
};
