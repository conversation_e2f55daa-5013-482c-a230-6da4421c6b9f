# WebCast 数据交互规范

## 一、目的

本文档旨在规范 WebSocket 服务端和客户端 workflow、iframe 之间的数据交互格式和流程，以保证数据的正确性和一致性。

## 二、数据格式

WebSocket 服务端和客户端 workflow、iframe 之间的数据采用 JSON 格式，每个数据包含以下字段：

- status：表示当前的连接状态，可取 ready、waiting 或 done 三种值之一。
- type：表示数据的类型，可取 message 或其他自定义类型。
- content：表示数据的内容，可以是任意字符串。
- from：表示数据的来源，可取 workflow、iframe 或 server 三种值之一。

## 三、数据流程

WebSocket 服务端和客户端 workflow、iframe 之间的数据交互分为三个阶段：

- 等待期：在此阶段，服务端等待客户端 workflow、iframe 都发送 ready 状态，表示它们已经准备好进行数据交互。如果有任何一个客户端没有发送 ready 状态，服务端会向所有已连接的客户端发送 waiting 状态，表示需要等待其他客户端准备好。
- 连接期：在此阶段，服务端检测到客户端 workflow、iframe 都已发送 ready 状态，表示它们都已准备好进行数据交互。服务端会向客户端 workflow、iframe 发送 done 状态，表示可以开始数据交互。
- 数据交互期：在此阶段，服务端和客户端 workflow、iframe 可以自由地发送和接收数据。每个数据都需要指定 type 和 content 字段。

## 四、示例

以下是 WebSocket 服务端和客户端 workflow、iframe 之间的数据交互示例：

- 客户端 workflow 连接到服务端，并发送 `{"status":"ready", "from":"workflow"}` 表示已准备好。
- 客户端 iframe 连接到服务端，并发送 `{"status":"ready", "from":"iframe"}` 表示已准备好。
- 服务端收到两个客户端的 ready 状态，向它们发送 `{"status":"done"}` 表示可以开始数据交互。
- 客户端 workflow 发送 `{"type":"message", "content":"Hello, iframe", "from":"workflow"}` 表示向客户端 iframe 发送消息。
- 服务端收到客户端 workflow 的消息，转发给客户端 iframe `{"type":"message", "content":"Hello, iframe"}`。
- 客户端 iframe 收到客户端 workflow 的消息，并回复 `{"type":"message", "content":"Hi, workflow"}` 表示向客户端 workflow 发送消息。
- 服务端收到客户端 iframe 的消息，转发给客户端 workflow `{"type":"message", "content":"Hi, workflow"}`。
