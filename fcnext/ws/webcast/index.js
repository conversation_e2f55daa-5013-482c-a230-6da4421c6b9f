/* eslint-disable no-console */
const WebSocket = require('ws');

const WebSocketServer = WebSocket.Server;
const wss = new WebSocketServer({ host: '0.0.0.0', port: 9000 });

const clients = {};
let timer = null;

function checkClients() {
  if (clients.workflow && clients.iframe) {
    clearInterval(timer);
    timer = null;
    for (const client of Object.values(clients)) {
      client.send(JSON.stringify({ status: 'done' }));
    }
  } else {
    for (const client of Object.values(clients)) {
      client.send(JSON.stringify({ status: 'waiting' }));
    }
  }
}


wss.on('connection', (ws) => {
  ws.on('message', (message) => {
    console.log(`Received: ${message}`);
    const msg = JSON.parse(`${message}`);
    if (msg?.status === 'ready') {
      clients[msg?.from] = ws;
      if (timer === null) {
        timer = setInterval(() => { checkClients(); }, 500);
      }
    } else if (clients.workflow && clients.iframe) {
      clients[msg.to].send(JSON.stringify(msg));
    }
  });

  ws.on('close', () => {
    console.log('A client disconnected');
    for (const [key, value] of Object.entries(clients)) {
      if (value === ws) {
        delete clients[key];
        break;
      }
    }

    for (const client of Object.values(clients)) {
      client.send(JSON.stringify({ status: 'waiting' }));
    }

    if (timer === null) {
      timer = setInterval(() => { checkClients(); }, 500);
    }
  });
});
