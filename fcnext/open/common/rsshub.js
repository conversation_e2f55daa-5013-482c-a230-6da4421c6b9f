const fetch = require('node-fetch');
const _ = require('lodash');

const PATH_MAP = {
  toutiao: 'jinritoutiao/article',
  douyin: 'douyin/video',
  xigua: 'ixigua/video',
  bilibili: 'bilibili/search',
  ytb: 'ixigua/ytb',
};
const API_URL = 'http://106.14.180.141:1200/';

const fetchData = async (platform, topic, page = 0) => {
  const data = [];
  try {
    const url = `${API_URL}${PATH_MAP[platform]}/${topic}/${page}.json`;
    const resp = await fetch(url);
    const { items } = await resp.json();
    data.push(...items.map((x) => { return { ...x, ...x.tags, platform }; }));
  } catch (error) {
    console.log(error); // eslint-disable-line
  }
  return data;
};

const fetchYTBData = async (topic, pageInfo) => {
  let url = `${API_URL}ixigua/ytb/${topic}.json`;
  if (pageInfo.key && pageInfo.token) {
    const { key, token } = pageInfo;
    url = `${API_URL}ixigua/ytb/${topic}/${key}/${token}.json`;
  }

  const data = [];
  let result = {};
  try {
    const resp = await fetch(url);
    const { items, title, home_page_url } = await resp.json();// eslint-disable-line
    data.push(...items.map((x) => { return { ...x, ...x.tags, platform: 'ytb' }; }));
    result = { data, page: { key: title, token: home_page_url } };
  } catch (error) {
    console.log(error); // eslint-disable-line
  }
  return result;
};


exports.handler = async (req, resp, context) => { // eslint-disable-line
  const { topic, platforms, page, key, token } = JSON.parse(req.body.toString());
  if (_.isEmpty(topic) || _.isEmpty(platforms)) {
    resp.statusCode = 500;  // eslint-disable-line
    resp.send(JSON.stringify({ result: 0, message: '参数不能为空' }));
    return;
  }

  let keys = platforms;
  let ytbData = { data: [] };
  if (platforms.indexOf('ytb') > -1) {
    keys = platforms.filter((x) => { return x !== 'ytb'; });
    ytbData = await fetchYTBData(topic, { key, token });
  }
  const promises = keys.map((x) => { return fetchData(x, topic, page); });
  const results = await Promise.all(promises);
  const data = [];
  [...results, ytbData.data].forEach((x) => { data.push(...x); });
  resp.send(JSON.stringify({ items: data, ...ytbData.page }));
};
