const fetch = require('node-fetch');
const _ = require('lodash');

exports.handler = async (req, resp, context) => { // eslint-disable-line
  if (_.isUndefined(req?.queries?.id)) {
    resp.statusCode = 500;  // eslint-disable-line
    resp.send(JSON.stringify({ result: 0, message: '参数不能为空' }));
  }

  let data = {};
  try {
    const url = `http://106.14.180.141:1200/ixigua/detail/${req?.queries.id}.json`;
    const result = await fetch(url);
    data = await result.json();
  } catch (error) {
    console.log(error); // eslint-disable-line
  }
  resp.send(data.home_page_url);
};
