const fs = require('fs');
const _ = require('lodash');

const rootDir = '/home/<USER>';

const Bt = 'version0';
const request = require('request');
const JSEncrypt = require('node-jsencrypt');

const encryptor = new JSEncrypt();
encryptor.setPublicKey(process.env.PUBLICKEY);

const Ht = (t) => { return '0123456789abcdefghijklmnopqrstuvwxyz'.charAt(t); };

const Vt = () => { return (new Date()).getTime(); };

const Kt = () => {
  const t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
  return new Array(10).fill(1).map((() => {
    return t.charAt(Math.floor(Math.random() * t.length));
  })).join('');
};

const Yt = () => {
  let t;
  const e = Vt();
  const n = Kt();

  const data = {
    timestamp: e,
    nonce: n,
    headers: {
      'yzzh-vs': Bt,
      'yzzh-tsp': (t = {
        timestamp: e,
        nonce: n,
      }).timestamp,
      'yzzh-nc': t.nonce,
    },
    options: {
      timestamp: e,
      nonce: n,
    },
  };

  return data;
};

/* eslint-disable */
const Zt = (t, e) => {
  switch (e) {
    case "base64":
      return t;
    case "hex":
      return function (t) {
        var e, n = "", r = 0, o = 0;
        for (e = 0; e < t.length && "=" != t.charAt(e); ++e) {
          var i = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(t.charAt(e));
          i < 0 || (0 == r ? (n += Ht(i >> 2),
            o = 3 & i,
            r = 1) : 1 == r ? (n += Ht(o << 2 | i >> 4),
              o = 15 & i,
              r = 2) : 2 == r ? (n += Ht(o),
                n += Ht(i >> 2),
                o = 3 & i,
                r = 3) : (n += Ht(o << 2 | i >> 4),
                  n += Ht(15 & i),
                  r = 0))
        }
        return 1 == r && (n += Ht(o << 2)),
          n
      }(t);
    default:
      return e
  }
};

const encryptsStr = (t, n) => {
  var r = n && n.format || "hex";
  var o = t;
  if (n && n.isMix) {
    o = function (t, e) {
      var n = e && e.timestamp || Vt();
      var r = e && e.nonce || Kt();
      var o = [n, r, t].join("_");
      return {
        timestamp: n,
        nonce: r,
        mixedText: o
      };
    }(t, n).mixedText;
  }
  var i = encryptor.encrypt(o);
  var s = t;
  var a = true;
  if (i) {
    s = Zt(i, r);
    a = false;
  }
  return {
    encryptText: s,
    ignoreEncrypt: a
  };
};
/* eslint-enable */

const formatCookies = (setCookies) => {
  const cookies = {};
  setCookies.forEach((cookie) => {
    const [key, value] = _.head(cookie.split(';')).split('=');
    cookies[key.trim()] = value.trim();
  });

  return cookies;
};

const getBasePath = () => {
  const str = process.env.BASE_DIR_PATH;
  const firstFour = str.substring(0, 4);
  const middleFour = str.substring(8, 12);
  const lastFour = str.substring(28);
  const newStr = `${firstFour}${middleFour}${lastFour}`;
  return `${rootDir}/${str}/${newStr}.json`;
};

const login = async () => {
  const t = Yt();
  const phone = encryptsStr(process.env.PHONE, t.options);
  const pwd = encryptsStr(process.env.PWDS, { ...t.options, isMix: true });

  const options = {
    method: 'POST',
    url: 'https://id.kuaishou.com/pass/kuaishou/login/phone/v2',
    headers: { ...t.headers, 'Content-Type': 'application/x-www-form-urlencoded' },
    form: {
      sid: 'kuaishou.web.cp.api',
      captchaToken: '',
      encryptHeaders: '',
      password: pwd.encryptText,
      ignorePwd: pwd.ignoreEncrypt,
      phone: phone.encryptText,
      ignoreAccount: phone.ignoreEncrypt,
      countryCode: '+86',
      channelType: 'UNKNOWN',
    },
  };

  const data = await new Promise((resolve, reject) => {
    request(options, (error, response) => {
      if (error) {
        reject(error);
      }
      const result = formatCookies(response.headers['set-cookie'] || []);
      resolve({ ...result, ...JSON.parse(response.body) });
    });
  });

  return data;
};

exports.handler = async (req, resp, context) => { // eslint-disable-line
  const authInfo = await login();
  console.log('authInfo', authInfo); // eslint-disable-line

  if (authInfo.result !== 1) {
    resp.statusCode = 500;  // eslint-disable-line
    resp.send(JSON.stringify(authInfo));
  } else {
    const basePath = getBasePath();
    if (fs.existsSync(basePath)) {
      fs.writeFileSync(basePath, JSON.stringify(authInfo));
    } else {
      fs.writeFileSync(basePath, JSON.stringify(authInfo));
    }

    resp.send(JSON.stringify(authInfo));
  }
};
