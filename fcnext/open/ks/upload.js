/* eslint-disable camelcase */
/* eslint-disable max-len */
const fs = require('fs');
const path = require('path');
const _ = require('lodash');

const fetch = require('node-fetch');

const videoDir = '/home/<USER>/';
const request = require('request');
const JSEncrypt = require('node-jsencrypt');

const rootDir = '/home/<USER>';

const encryptor = new JSEncrypt();
encryptor.setPublicKey(process.env.PUBLICKEY);

const OSS_URL = 'https://video-clip.oss-cn-shanghai.aliyuncs.com/';
const CHUNK_SIZE = 4194304;
const api_ph = 'fd66d894c164afd1981e50aba0970238bdb2';
const commonHeaders = {
  accept: 'application/json',
  'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
  'content-type': 'application/json',
  'sec-ch-ua': '"Chromium";v="118", "Google Chrome";v="118", "Not=A?Brand";v="99"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"macOS"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  Referer: 'https://jigou.kuaishou.com/live/photo/publish',
  'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
};
const NS_SIG_MAP = {
  1262440397: '75652212af531c4342282b2aaaff13b8f54708573434363639383b21', // 张文自在说
  2683888074: 'c0d097a7fdebadf6029d9e9fa996f61baff9b9e2818183838c8d8e94', // 伟航评说
  3697139057: 'e0f0b787ddcb8dd623bdbebfa1b477b39fd999c2a1a1a3a3acadaeb4', // 魏魏V
};

const bytesToMB = (bytes) => {
  const megabytes = bytes / (1024 * 1024);
  return `${megabytes.toFixed(2)}MB`;
};

/* eslint-disable camelcase, max-len */
const genHeader = (authInfo) => {
  const { did, soft_did, userId } = authInfo;
  const api_st = authInfo['kuaishou.web.cp.api_st'];
  const cookie = `_did=web_667407615FBDF500; did=${did}; soft_did=${soft_did}; userId=${userId}; kuaishou.web.cp.api_st=${api_st}; kuaishou.web.cp.api_ph=${api_ph}`;
  return { cookie };
};

const getToken = async (cookie, memberId) => {
  const sig = NS_SIG_MAP[memberId];
  const options = {
    method: 'POST',
    url: `https://jigou.kuaishou.com/rest/org/publish/photo/generate/token?__NS_sig3=${sig}`,
    headers: { ...commonHeaders, cookie },
    body: JSON.stringify({ memberId: +memberId, 'kuaishou.web.cp.api_ph': api_ph }),
  };

  const { data } = await new Promise((resolve, reject) => {
    request(options, (error, response) => {
      if (error) {
        reject(error);
      }
      resolve(JSON.parse(response.body));
    });
  });

  return data;
};

const preUpload = async (headers) => {
  const header = {
    ...commonHeaders,
    accept: '*/*',
    'content-type': 'application/json',
    Referer: 'https://jigou.kuaishou.com/',
    ...headers,
  };

  const resp = await fetch('https://cp.kuaishou.com/rest/cp/works/v3/video/pc/upload/pre', {
    headers: header,
    body: JSON.stringify({ uploadType: 1, 'kuaishou.web.cp.api_ph': api_ph }),
    method: 'POST',
  });
  const { data } = await resp.json();
  return data;
};

const resumeUpload = async (token) => {
  const resp = await fetch(`https://upload.kuaishouzt.com/api/upload/resume?upload_token=${token}`, {
    headers: {
      ...commonHeaders,
      accept: 'application/json, text/plain, */*',
      Referer: 'https://jigou.kuaishou.com/',
    },
    method: 'GET',
  });
  const data = await resp.json();
  return data;
};

const fragmentUpload = async (token, chunk, index, fileSize) => {
  const resp = await fetch(`https://upload.kuaishouzt.com/api/upload/fragment?upload_token=${token}&fragment_id=${index}`, {
    headers: {
      ...commonHeaders,
      Accept: 'application/json, text/plain, */*',
      'Content-Range': `bytes ${index * CHUNK_SIZE}-${index * CHUNK_SIZE + chunk.length - 1}/${fileSize}`,
      'Content-Type': 'application/octet-stream',
      Host: 'upload.kuaishouzt.com',
      Origin: 'https://jigou.kuaishou.com',
      Referer: 'https://jigou.kuaishou.com/',
    },
    body: chunk,
    method: 'POST',
  });
  const data = await resp.json();
  return data;
};

const completeUpload = async (token, count) => {
  const resp = await fetch(`https://upload.kuaishouzt.com/api/upload/complete?fragment_count=${count}&upload_token=${token}`, {
    headers: {
      ...commonHeaders,
      Accept: 'application/json, text/plain, */*',
      Origin: 'https://jigou.kuaishou.com',
      Referer: 'https://jigou.kuaishou.com/',
    },
    method: 'POST',
  });

  const data = await resp.json();
  return data;
};

const finishUpload = async (headers, uploadToken, params) => {
  const resp = await fetch('https://cp.kuaishou.com/rest/cp/works/v3/video/pc/upload/finish', {
    headers: {
      ...commonHeaders,
      accept: '*/*',
      'content-type': 'application/json',
      Referer: 'https://jigou.kuaishou.com/',
      ...headers,
    },
    referrer: 'https://jigou.kuaishou.com/',
    body: JSON.stringify({
      token: uploadToken,
      fileName: params.fileName,
      fileType: 'video/mp4',
      fileLength: params.fileSize,
      'kuaishou.web.cp.api_ph': api_ph,
    }),
    method: 'POST',
  });
  const { data } = await resp.json();
  return data;
};

const saveSnapshot = async (headers, uploadToken, params, video) => {
  const resp = await fetch('https://cp.kuaishou.com/rest/cp/works/v3/video/pc/publishInfo/snapshot/save', {
    headers: {
      ...commonHeaders,
      Accept: '*/*',
      'Content-Type': 'application/json;charset=UTF-8',
      Origin: 'https://jigou.kuaishou.com',
      Referer: 'https://jigou.kuaishou.com/',
      ...headers,
    },
    referrer: 'https://jigou.kuaishou.com/',
    body: JSON.stringify({
      fileId: video.fileId,
      coverKey: video.coverKey,
      coverTimeStamp: -1,
      caption: video.caption || '',
      photoStatus: 2,
      coverType: 1,
      coverTitle: '',
      photoType: 0,
      collectionId: '',
      publishTime: 0,
      longitude: null,
      latitude: null,
      notifyResult: 0,
      domain: '',
      secondDomain: '',
      coverCropped: false,
      pkCoverKey: '',
      fileName: params.fileName,
      fileSize: bytesToMB(params.fileSize),
      downloadType: 1,
      disableNearbyShow: false,
      allowSameFrame: true,
      movieId: '',
      openPrePreview: false,
      fileType: 'video/mp4',
      projectId: '',
      videoSizeWidth: video.width,
      videoSizeHeight: video.height,
      recTagIdList: [],
      videoInfoMeta: '',
      previewUrlErrorMessage: '',
      needDeleteKey: [],
      videoFirstFrameURL: {},
      triggerH265: false,
      duration: video.videoDuration / 1000,
      width: video.width,
      height: video.height,
      mediaId: video.mediaId,
      coverMediaId: video.coverMediaId,
      photoIdStr: video.photoIdStr,
      videoDuration: video.videoDuration,
      fileToken: uploadToken,
      'kuaishou.web.cp.api_ph': api_ph,
    }),
    method: 'POST',
  });

  const data = await resp.json();
  return data;
};

const submit = async (headers, uploadToken, video) => {
  const resp = await fetch('https://cp.kuaishou.com/rest/cp/works/v3/video/pc/submit', {
    headers: {
      ...commonHeaders,
      Accept: '*/*',
      'Content-Type': 'application/json;charset=UTF-8',
      Origin: 'https://jigou.kuaishou.com',
      Referer: 'https://jigou.kuaishou.com/',
      ...headers,
    },
    referrer: 'https://jigou.kuaishou.com/',
    body: JSON.stringify(
      {
        fileId: video.fileId,
        coverKey: video.coverKey,
        coverTimeStamp: -1,
        caption: video.text || '',
        photoStatus: 2, // 1: 全部, 4:好友, 2:私密
        coverType: 1,
        coverTitle: '',
        photoType: 0,
        collectionId: '',
        publishTime: 0,
        longitude: '',
        latitude: '',
        notifyResult: 0,
        domain: '', // 领域
        secondDomain: '', // 领域
        coverCropped: false,
        pkCoverKey: '',
        downloadType: 1,
        disableNearbyShow: false,
        allowSameFrame: true,
        movieId: '',
        openPrePreview: false,
        projectId: '',
        recTagIdList: [],
        videoInfoMeta: '',
        previewUrlErrorMessage: '',
        triggerH265: false,
        mediaId: video.mediaId,
        photoIdStr: video.photoIdStr,
        videoDuration: video.videoDuration,
        'kuaishou.web.cp.api_ph': api_ph,
      },
    ),
    method: 'POST',
  });

  const data = await resp.json();
  return data;
};

const uploadCover = async (headers, imagePath) => {
  const options = {
    method: 'POST',
    url: 'https://cp.kuaishou.com/rest/cp/works/v3/video/pc/upload/cover/upload',
    headers: {
      ...commonHeaders,
      accept: '*/*',
      Origin: 'https://jigou.kuaishou.com',
      Referer: 'https://jigou.kuaishou.com/',
      'content-type': 'multipart/form-data',
      ...headers,
    },
    formData: {
      'kuaishou.web.cp.api_ph': api_ph,
      file: {
        value: fs.createReadStream(imagePath),
        options: {
          filename: path.basename(imagePath),
          contentType: null,
        },
      },
    },
  };
  const { data } = await new Promise((resolve, reject) => {
    request(options, (error, response, body) => {
      if (error) {
        reject(error);
      }
      resolve(JSON.parse(body));
    });
  });

  return data.coverKey;
};

const getBasePath = () => {
  const str = process.env.BASE_DIR_PATH;
  const firstFour = str.substring(0, 4);
  const middleFour = str.substring(8, 12);
  const lastFour = str.substring(28);
  const newStr = `${firstFour}${middleFour}${lastFour}`;
  return `${rootDir}/${str}/${newStr}.json`;
};

exports.handler = async (req, resp, context) => { // eslint-disable-line
  const { secUserId, videoUrl, imgUrl, tags, desc } = JSON.parse(req.body.toString());
  if (_.isUndefined(videoUrl) || _.isUndefined(imgUrl)) {
    resp.statusCode = 500; // eslint-disable-line
    resp.send('Server Internal Error');
    return;
  }

  if (!_.startsWith(videoUrl, OSS_URL) || !_.startsWith(imgUrl, OSS_URL)) {
    resp.statusCode = 500; // eslint-disable-line
    resp.send('Server Internal Error');
    return;
  }
  const text = `${desc || ''} ${(tags || []).map((item) => { return `#${item}`; }).join(' ')}`;
  let caption = tags.map((x) => { return `<span is-tag="true"><span class="at-tag-item" data-tag-name="${x}">#${x}</span></span>`; });
  caption = `${desc || ''} ${(caption || []).join(' ')}`;

  const basePath = getBasePath();
  const authInfo = JSON.parse(fs.readFileSync(basePath));
  console.log('authInfo', authInfo); // eslint-disable-line
  const headers = genHeader(authInfo);
  const { token } = await getToken(headers.cookie, secUserId);
  console.log('token', token);// eslint-disable-line

  if (_.isUndefined(token)) {
    resp.statusCode = 403; // eslint-disable-line
    resp.send('auth failed');
    return;
  }

  headers.Proxytoken = token;
  const uploadData = await preUpload(headers);
  console.log('uploadData', uploadData); // eslint-disable-line
  const resumeData = await resumeUpload(uploadData.token);
  console.log('resumeData', resumeData); // eslint-disable-line
  const videoPath = videoUrl.replace(`${OSS_URL}`, videoDir);
  const videoContent = fs.readFileSync(videoPath);
  const fileSize = videoContent.length;
  console.log(path.basename(videoPath), fileSize); // eslint-disable-line
  for (let i = 0, j = 0; i < fileSize; i += CHUNK_SIZE, j++) {
    const chunk = videoContent.slice(i, i + CHUNK_SIZE);
    const data = await fragmentUpload(uploadData.token, chunk, j, fileSize); // eslint-disable-line
    console.log(`upload ${j} success:`, data); // eslint-disable-line
  }

  const completeData = await completeUpload(uploadData.token, Math.ceil(fileSize / CHUNK_SIZE));
  console.log('completeData', completeData); // eslint-disable-line
  const videoParams = { fileSize, fileName: path.basename(videoPath) };
  const videoData = await finishUpload(headers, uploadData.token, videoParams);
  console.log('videoData', videoData); // eslint-disable-line
  const imagePath = imgUrl.replace(`${OSS_URL}`, videoDir);
  const coverKey = await uploadCover(headers, imagePath);
  console.log('coverKey', coverKey); // eslint-disable-line

  videoData.coverKey = coverKey;
  videoData.caption = caption;
  videoData.text = text;
  const snapshotData = await saveSnapshot(headers, uploadData.token, videoParams, videoData);
  console.log('snapshotData', snapshotData); // eslint-disable-line
  const submitData = await submit(headers, uploadData.token, videoData);
  resp.send(JSON.stringify(submitData));
};
