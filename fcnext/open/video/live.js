/* eslint-disable max-len, camelcase */
const qs = require('qs');
const request = require('request');
const _ = require('lodash');

const getRandomUA = () => {
  const osList = [
    '(Windows NT 10.0; WOW64)', '(Windows NT 10.0; WOW64)', '(Windows NT 10.0; Win64; x64)',
    '(Windows NT 6.3; WOW64)', '(Windows NT 6.3; Win64; x64)', '(Windows NT 6.1; Win64; x64)',
    '(Windows NT 6.1; WOW64)', '(X11; Linux x86_64)', '(<PERSON>; Intel Mac OS X 10_12_6)',
  ];
  const chromeVersionList = [
    '110.0.5481.77', '110.0.5481.30', '109.0.5414.74', '108.0.5359.71', '108.0.5359.22',
    '107.0.5304.62', '107.0.5304.18', '106.0.5249.61', '106.0.5249.21', '105.0.5195.52',
    '105.0.5195.19', '104.0.5112.79', '104.0.5112.29', '104.0.5112.20', '103.0.5060.134',
    '103.0.5060.53', '103.0.5060.24', '102.0.5005.61', '102.0.5005.27', '101.0.4951.41',
    '101.0.4951.15', '100.0.4896.60', '100.0.4896.20', '99.0.4844.51', '99.0.4844.35',
    '99.0.4844.17', '98.0.4758.102', '98.0.4758.80', '98.0.4758.48', '97.0.4692.71',
  ];
  const os = osList[Math.floor(Math.random() * osList.length)];
  const version = chromeVersionList[Math.floor(Math.random() * chromeVersionList.length)];
  return `"Mozilla/5.0 ${os} AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${version} Safari/537.36"`;
};

const genCookies = async () => {
  const url = 'https://live.douyin.com';
  const cookie = '__ac_nonce=0638733a400869171be51';
  const header = {
    accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36',
    cookie,
  };

  const data = await new Promise((resolve, reject) => {
    request({ url, headers: header }, (error, res) => {
      if (error) {
        reject(error);
      }
      const setCookies = res.headers['set-cookie'] || [];
      let cStr = '__ac_nonce=0638733a400869171be51';
      setCookies.forEach((x) => {
        const [key, value] = _.head(x.split(';')).split('=');
        cStr += `; ${key}=${value}`;
      });
      resolve(cStr);
    });
  });

  return data;
};

const getUserInfo = async (secUserId) => {
  const options = { method: 'GET', url: `http://**************:1200/jinritoutiao/douyin/${secUserId}.json` };
  const data = await new Promise((resolve, reject) => {
    request(options, (error, res) => {
      if (error) {
        reject(error);
      }
      resolve(JSON.parse(res.body));
    });
  });

  const wrid = data?.description?.split(' - ')[0];
  if (wrid !== 'TikTok') {
    return wrid;
  }
  return null;
};


exports.handler = async (req, resp, context) => { // eslint-disable-line
  const cookie = await genCookies();

  const url = decodeURIComponent(req.queries.q);
  const mainUrl = url.split('?')[0];
  let userId;

  if (_.startsWith(url, 'https://www.douyin.com/')) {
    userId = await getUserInfo(_.last(url.split('/')), cookie);
    if (_.isEmpty(userId)) {
      resp.send(JSON.stringify({ stream_url: {}, owner: {} }));
    }
  } else {
    userId = _.last(mainUrl.split('/'));
  }

  const qryObj = {
    aid: 6383,
    app_name: 'douyin_web',
    live_id: 1,
    device_platform: 'web',
    language: 'zh-CN',
    cookie_enabled: true,
    screen_width: 3008,
    screen_height: 1692,
    browser_language: 'zh-CN',
    browser_platform: 'MacIntel',
    browser_name: 'Chrome',
    browser_version: '*********',
    web_rid: userId,
    enter_source: '',
    is_need_double_stream: false,
  };
  const USER_POST_URL = `https://live.douyin.com/webcast/room/web/enter/?${qs.stringify(qryObj)}`;
  const options = {
    method: 'GET',
    url: USER_POST_URL,
    headers: { 'user-agent': getRandomUA(), Cookie: cookie },
  };

  const respStr = await new Promise((resolve, reject) => {
    request(options, (error, response) => {
      if (error) {
        reject(error);
      }
      resolve(response.body);
    });
  });

  const { data: { data: result } } = JSON.parse(respStr);
  const { id_str, status, stream_url, title, owner } = _.head(result);
  const { flv_pull_url, hls_pull_url_map } = stream_url || {};

  resp.send(JSON.stringify(
    {
      id_str,
      status,
      title,
      stream_url: _.isUndefined(stream_url) ? {} : { flv_pull_url, hls_pull_url: hls_pull_url_map },
      owner: _.isUndefined(owner) ? {} : { id_str: owner.id_str, sec_uid: owner.sec_uid, nickname: owner.nickname },
    },
  ));
};
