/* eslint-disable no-bitwise, max-len  */
const _ = require('lodash');
const qs = require('qs');
const zlib = require('zlib');
const request = require('request');
const fs = require('fs');
const Core = require('@alicloud/pop-core');

const rootDir = '/home/<USER>/';
const client = new Core({
  accessKeyId: process.env.ak,
  accessKeySecret: process.env.sk,
  endpoint: 'https://ecs.cn-shanghai.aliyuncs.com',
  apiVersion: '2014-05-26',
});
const encpyptwithXor = (param) => {
  const encoded = Buffer.from(param, 'utf8');
  const result = [];
  for (const i of encoded) {
    result.push(i ^ 0x5);
  }
  return result;
};

const byteToStr = (alist, bolean, length) => {
  const s = '0123456789abcdef';
  const res = new Array(length * 2).fill(0);
  let bo = 0;
  let i = 0;
  while (i < length) {
    const b = alist[i + 0] & 0xff;
    const bo1 = +bo + true;
    res[bo] = s[b >> 4];
    bo = bo1 + true;
    res[bo1] = s[b & 0xf];
    i += 1;
  }
  return res;
};

const getLoginParams = (param) => { // eslint-disable-line
  const xor = encpyptwithXor(param);
  return byteToStr(xor, false, xor.length).join('');
};

const setCookiesHelper = (setCookies) => {
  setCookies.forEach((cookie) => {
    const [key, value] = _.head(cookie.split(';')).split('=');
    if (!_.isUndefined(key)) {
      this._cookies[key.trim()] = (value || '').trim();
    }
  });
};

const getReqCookie = (data = {}) => {
  const obj = _.isEmpty(data) ? this._cookies : data;
  const cookieArr = [];
  Object.keys(obj).forEach((key) => {
    cookieArr.push(`${key}=${this._cookies[key]}`);
  });

  return cookieArr.join('; ');
};

const baseRequest = async (options) => {
  const data = await new Promise((resolve, reject) => {
    request(options, (error, response) => {
      if (error) {
        reject(error);
      }
      resolve(response.body);
    });
  });
  // 对 data 进行 JSON parse, 若无err 直接返回
  let result = data.toString();
  try {
    result = JSON.parse(result);
    result = JSON.stringify(result);
  } catch (err1) {
    result = await new Promise((resolve) => {
      zlib.brotliDecompress(data, (err, buffer) => {
        if (!err) {
          resolve(buffer.toString());
        } else {
          resolve(err);
        }
      });
    });
  }
  return result;
};

const getUserInfo = async () => {
  const qryObj = { msToken: this._cookies.msToken };
  const query = qs.stringify(qryObj);
  const options = {
    method: 'GET',
    encoding: null,
    url: `https://creator.douyin.com/web/api/media/user/info/?${query}`,
    headers: {
      accept: 'application/json, text/plain, */*',
      'accept-language': 'zh-CN,zh;q=0.9',
      'sec-ch-ua': '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      Cookie: getReqCookie(),
    },
  };

  const result = await baseRequest(options);
  const { user } = JSON.parse(result || '{}');
  if (_.isUndefined(user)) {
    console.log(result); // eslint-disable-line
  }
  return user;
};

const sendDingtalk = async (msg) => {
  const options = {
    method: 'POST',
    url: 'https://oapi.dingtalk.com/robot/send?access_token=7e61ecd6f88cad7dfa28b197473585c256e40613a71aafceea0e2fb7df7b78ec',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      msgtype: 'text',
      text: {
        content: `叮咚:\n${msg}`,
      },
    }),
  };

  await baseRequest(options);
};

const getBasePath = () => {
  const str = process.env.BASE_DIR_PATH;
  const firstFour = str.substring(0, 4);
  const middleFour = str.substring(8, 12);
  const lastFour = str.substring(28);
  const newStr = `${firstFour}${middleFour}${lastFour}`;
  return `${rootDir}/${str}/${newStr}.json`;
};

const closePc = async () => {
  const params = { InstanceId: process.env.pcId, StoppedMode: 'StopCharging' };
  const requestOption = { method: 'POST', formatParams: false };

  const data = await client.request('StopInstance', params, requestOption);
  console.log('closePc', data); // eslint-disable-line
};

exports.handler = async (req, resp, context) => { // eslint-disable-line
  const { items } = JSON.parse(req.body.toString());
  this._cookies = {};
  const setCookies = _.map(items, 'value');
  setCookiesHelper(setCookies);
  const info = await getUserInfo();
  console.log(info); // eslint-disable-line


  const basePath = getBasePath();
  if (fs.existsSync(basePath)) {
    fs.writeFileSync(basePath, JSON.stringify(this._cookies));
  } else {
    fs.writeFileSync(basePath, JSON.stringify(this._cookies));
  }

  if (!_.isUndefined(info)) {
    await sendDingtalk(`${info.nickname}(${info.bind_phone}) 上线了!`); // eslint-disable-line
  } else {
    await sendDingtalk('登录失败!\n登录失败!\n赶紧排查'); // eslint-disable-line
  }

  await closePc();
  resp.send(JSON.stringify(this._cookies));
};
