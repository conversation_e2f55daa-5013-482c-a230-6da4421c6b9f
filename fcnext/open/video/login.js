/* eslint-disable max-len */

const _ = require('lodash');
const qs = require('qs');
const request = require('request');
const fs = require('fs');

const BASE_COOKIE_OBJ = {
  bd_ticket_guard_client_data: 'eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCQVEzT0x1aWx6dVY1KzFqRE1FZURlamkvb0xpdmZ6NE1wMGFDU1hoaWx3Y1RDOG9xQVR6N1V2SituKytoQmZ0SG0yY05IZmhRSHdRdmlQVGIrYWNHS3c9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoxfQ==',
  bd_ticket_guard_web_domain: '2',
  passport_fe_beating_status: 'true',
  passport_csrf_token: 'b6ba381232ffd2115cd5e23e4aa46988',
  passport_csrf_token_default: 'b6ba381232ffd2115cd5e23e4aa46988',
  _bd_ticket_crypt_doamin: '2',
  __security_server_data_status: '1',
  s_v_web_id: 'lmt4tr78_xlDNGUrS_o05O_4LH7_9tr6_Xd8K1znShkiY',
  d_ticket: 'd41f67c5df2c3ee63c8e2c7aafd78abcaddc4',
  n_mh: '5SP7fKJIuvP5vlh4QyjlNmnGV1xXXaETHIaT7w7o1ps',
  sso_auth_status: '7f486408e82bbdc25a5374c79760f91e%2Cf93f2c29ddae10d8eb61a4dc56765730',
  sso_auth_status_ss: '7f486408e82bbdc25a5374c79760f91e%2Cf93f2c29ddae10d8eb61a4dc56765730',
  passport_auth_status: '42a9927570e4dd49dcdec000ff4ea4e3%2Ca85c857932fb0691fe0a4646669591a6',
  passport_auth_status_ss: '42a9927570e4dd49dcdec000ff4ea4e3%2Ca85c857932fb0691fe0a4646669591a6',
  toutiao_sso_user: 'ffcc66c3665e7053b4b1e73a6e6defa4',
  toutiao_sso_user_ss: 'ffcc66c3665e7053b4b1e73a6e6defa4',
  _bd_ticket_crypt_cookie: '47cc2a45e489e26eca9e7a0e31ea0bb4',
  sso_uid_tt: '6f75eaedd838cea010329bd981b03eaf',
  sso_uid_tt_ss: '6f75eaedd838cea010329bd981b03eaf',
  sid_ucp_sso_v1: '1.0.0-KDA0NWU3ZmJiNTQzZjEyMDc4ZTVkY2U1M2E1NGJiNDZkNjJlNmEzYjYKCBD1g7CoBhgNGgJsZiIgZmZjYzY2YzM2NjVlNzA1M2I0YjFlNzNhNmU2ZGVmYTQ',
  ssid_ucp_sso_v1: '1.0.0-KDA0NWU3ZmJiNTQzZjEyMDc4ZTVkY2U1M2E1NGJiNDZkNjJlNmEzYjYKCBD1g7CoBhgNGgJsZiIgZmZjYzY2YzM2NjVlNzA1M2I0YjFlNzNhNmU2ZGVmYTQ',
  odin_tt: 'ca1f3e87f58f8a1177583df20eb0533c57d70d50e7eb2557f357d8523d0a7046',
  sid_guard: '1c84353af5749e40a3f96a88c6b857d7%7C1695285749%7C21600%7CThu%2C+21-Sep-2023+14%3A42%3A29+GMT',
  uid_tt: '499475005f21c3f30a7866d0bdc23a39',
  uid_tt_ss: '499475005f21c3f30a7866d0bdc23a39',
  sid_tt: '1c84353af5749e40a3f96a88c6b857d7',
  sessionid: '1c84353af5749e40a3f96a88c6b857d7',
  sessionid_ss: '1c84353af5749e40a3f96a88c6b857d7',
  sid_ucp_v1: '1.0.0-KDU4ZjFiN2RkNjdmNWFkZTM3OWQ4MDA5ZmNlZDk4NTY4MTc3ZjJmMTEKCBD1g7CoBhgNGgJsZiIgMWM4NDM1M2FmNTc0OWU0MGEzZjk2YTg4YzZiODU3ZDc',
  ssid_ucp_v1: '1.0.0-KDU4ZjFiN2RkNjdmNWFkZTM3OWQ4MDA5ZmNlZDk4NTY4MTc3ZjJmMTEKCBD1g7CoBhgNGgJsZiIgMWM4NDM1M2FmNTc0OWU0MGEzZjk2YTg4YzZiODU3ZDc',
  ttwid: '1%7CInuGOOcgbu6-FtcKTMmzXiyD5_AHzWihQEUCGkq92L8%7C1695285750%7C7af83d8345bd05bc964af5730040c0655f954ef2a700c91a535897811592a175',
  msToken: 'sot4Tc4XqEt1hlAlAAREWhgTDdGfBhDCtZgRvWVYyN0ZfvRD855TdSK-mav6ovE29HPso4N-Pa972mUnTB0qmmuGeyC-17S5YUMACO6lDQ',
};

const BASE_HEADERS = {
  accept: 'application/json, text/plain, */*',
  'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
  'content-type': 'application/x-www-form-urlencoded',
  'sec-ch-ua': '"Google Chrome";v="117", "Not;A=Brand";v="8", "Chromium";v="117"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"macOS"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-site',
  'x-csrftoken': 'null',
  Referer: 'https://creator.douyin.com/',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
};

const _signature = '_02B4Z6wo00101Fgq5nwAAIDB1SsxOUWqshxYLuLAAHMI023veGRWyp9Ehn6a7b4-qTWu2lG88IrvVFIJtpnZfGerxd4YYo8NRfwxvbM3f7bO8FOjxR12vc8NBY7zL9URCyPuq56HIh0MTPOOee';

const BROWSER_NAME = 'Mozilla';
const BROWSER_VERSION = '5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36';

const BASE_QUERY = {
  cookie_enabled: 'true',
  screen_width: '3008',
  screen_height: '1692',
  browser_language: 'zh-CN',
  browser_platform: 'MacIntel',
  browser_name: BROWSER_NAME,
  browser_version: BROWSER_VERSION,
  browser_online: 'true',
  timezone_name: 'Asia/Shanghai',
  aid: '1128',
  a_bogus: 'x6lE6cgyMsm1HffnM7kz99vm7Uj0YW5cgZEP4E-sjzql',
};

const BASE_QUERY_S = {
  ...BASE_QUERY,
  aid: 2906,
  app_name: 'aweme_creator_platform',
  device_platform: 'web',
  _signature,
};

const rootDir = '/home/<USER>/';

const cookieObjToStr = (cookieObj) => {
  let cookieStr = '';
  Object.keys(cookieObj).forEach((key) => {
    cookieStr += `${key}=${cookieObj[key]};`;
  });

  return cookieStr;
};

const setCookiesHelper = (setCookies) => {
  setCookies.forEach((cookie) => {
    const [key, value] = _.head(cookie.split(';')).split('=');
    this._cookies[key.trim()] = value.trim();
  });
};

const baseRequest = async (options) => {
  const data = await new Promise((resolve, reject) => {
    request(options, (err, res, body) => {
      if (err) {
        reject(err);
      }
      const setCookies = res.headers['set-cookie'] || [];
      setCookiesHelper(setCookies);
      resolve(body);
    });
  });

  return data;
};

const getSecsdkCsrfToken = async () => {
  const options = { method: 'get', url: 'https://summon.bytedance.com/secsdk_csrf_token' };
  const result = await baseRequest(options);
  const { data } = JSON.parse(result);
  return data.token;
};

const login = async () => {
  const options = {
    url: `https://sso.douyin.com/account_login/v2/?_signature=${_signature}`,
    method: 'POST',
    headers: { ...BASE_HEADERS, cookie: cookieObjToStr(this._cookies) },
    form: {
      account: '2e3d3334363d343330343733313c',
      password: '7d7d3631323034344f5f',
      service: 'https://creator.douyin.com/?logintype=org&loginapp=douyin&jump=https://creator.douyin.com/',
      aid: '2906',
      mix_mode: '1',
      is_vcd: '1',
      fp: 'lmsub4hy_Nhbi7Guc_OiPQ_4iMP_8hsu_nMKg18KBst7v',
    },
  };

  const result = await baseRequest(options);
  return JSON.parse(result);
};

const curlRredirectUrl = async (url) => {
  const options = {
    url: `${url}&_signature=${_signature}`,
    method: 'POST',
    headers: { ...BASE_HEADERS, cookie: cookieObjToStr(this._cookies) },
  };

  const result = await baseRequest(options);
  return result;
};

const creatorLogin = async () => {
  const query = qs.stringify(BASE_QUERY_S);
  const options = {
    url: `https://creator.douyin.com/aweme/v1/creator/login/?${query}`,
    method: 'POST',
    headers: {
      ...BASE_HEADERS,
      cookie: cookieObjToStr(this._cookies),
      'Content-Type': 'application/json',
      'X-Secsdk-Csrf-Token': this._secsdkCsrfToken,
    },
    body: JSON.stringify({ login_type: 2, login_app: 2906 }),
  };

  const result = await baseRequest(options);
  return result;
};

const tokenBeat = async () => {
  const query = { device_platform: 'web_app', aid: '2906', scene: 'boot', sdk_version: '2.0.4', _signature };
  const options = {
    url: `https://creator.douyin.com/passport/token/beat/web/?${qs.stringify(query)}`,
    method: 'GET',
    headers: {
      ...BASE_HEADERS,
      cookie: cookieObjToStr(this._cookies),
      'Content-Type': 'application/json',
      'X-Secsdk-Csrf-Token': this._secsdkCsrfToken,
    },
    body: JSON.stringify({ login_type: 2, login_app: 2906 }),
  };

  const result = await baseRequest(options);
  return result;
};

const getCid = async () => {
  const options = {
    method: 'GET',
    url: 'https://xxbg.snssdk.com/websdk/v1/getInfo?q=A3VpXVFZ6Dhl4lZ5sXrhyNkzMSiP1ESW%2FUfal6AEVUV%2FgoIeT4ITVA5C888EK98aOjXBFFq8wuvXkK6j0zhyIVMargv%2BBkbBM2PkNPUZ8Edf908XhLGg3xCO4xaqBYXlLfkwUUooljDtdLEdqo979UjjyO51WPFRKTaMQKVf%2Fr6yCXZpeMmlhpOg8Z6G9iJWrW1J4NUGyRUpFCPoJzuro0VKJY8YZ%2FPGu9cN7uz4%2FkBkpP9SRuNdxNwpvv%2FsXB0qeLZlhM8iKkM6NSt0P2eWCXfElJ%2Fpw%2F8TNY2sauQEXLZYtfY2WVgY7aVZXGK9o%2BGRfCL6wuqFBLDw5%2FHx%2BxfglhO9fllV0jIWt61uoUhOsTkDagsJ38Tvey5ZtbqszqEmBn3pSUleDDn61eAJsiSUGYso3zWlLRm0NMVRYQHXPLSNQb2OjvhxjlfqYTB0po2n48mLCr1kCWV3tzMMVKB%2Fr%2BK%2Bl7qFmokf2Sq6W9r%2FD%2F5LBgREH1yTUQdYL5scFPgj%2BFC%2BnWjR2jFqdE05F%2BoQ0we%2FzPOoUEonVo6q0yzk8CsreFfNDqIwmhNURvBpePbw4RA2%2B7WMhTA28p8g%2BvSgWTTHxDLSp8h9vtjtRNwPj0oqtS7hJI4GV9Ua0UphknHUlSh7XBbrbjUJ4H7FwI4N5zPStdQCavGwNDegtQ8lPkEOZi0IP0sMemQLoME8Tk9bfOPIGrQByavBKGTi%2BN7YRBYx9ckNtxamPiBV%2BK2%2FS6LnBk8Htthp06UwqYDATYoI%2F8HAHM8%2FsSBEP%2FnEXLuHbcs5ivNw1kDGBaleehvim94XTZ%2FxyNHQXeQens7uKE4xWvDXTrheo0fQmaHB%2BbRU%2FaJyjQMg0hKx6RASY108srEg3oVhrmX7oh3pQ6GSXHwTTKNoRDS50xWNziiLeKcr2y4HLUcZPhTSCU%2FU0a256BjE4fhVufbmARr31WhMimR9%2Fouvsva%2FojxUUuxl%2Fp6f478NvEdvybClu9B3W2dU4rYQbicNbdPV6no8RzuIdKaFt6jKbHakH7kYj0yWLPimLscAwe8VNqnWzrZY3JkJ1KO0ev%2FnC7GTC1qwwKcawNEVgCVB8hRMUplPCkiXBFHt77GQaW%2BDp7%2Fa8YOVpOFKrHNdaPt5%2FZkaN6UNTc9IdgUJMOFgGVyNjF0Zthx9L%2BHYb3MlD4rRtPpCpBGITJE6alXDtiIBJcIVDviPhlE0tF9VdoEDLU7M5ZdyoaZgkka7N3Dl20ZTdoSkDPBjYJNLYLbW89o5R%2Bv%2BcRJ05ZyTSQ9l8PBjJWv0D%2B5VQaPoNMv22JrSVB6qptr7ONg%2B%2FVawyl9t7JQ3Dq6Se0HAmKyBHG3avaBaWCMnLQVLo%2B4YKAgCl%2B4EMOQ%2FPK68LcbwvV6ovXa79OJd0oXN236j03Ej8I2i040o0Is72pvo2NLH03KHRNgV246%3D&callback=_7641_1695306037682',
    headers: {
      Accept: '*/*',
      'Accept-Encoding': 'gzip, deflate, br',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
      Referer: 'https://creator.douyin.com/',
      'Sec-Ch-Ua': BASE_HEADERS['sec-ch-ua'],
      'Sec-Ch-Ua-Mobile': '?0',
      'Sec-Ch-Ua-Platform': '"macOS"',
      'Sec-Fetch-Dest': 'script',
      'Sec-Fetch-Mode': 'no-cors',
      'Sec-Fetch-Site': 'cross-site',
      'User-Agent': BASE_HEADERS['user-agent'],
    },
  };

  const result = await baseRequest(options);
  return result;
};

const getBasePath = () => {
  const str = process.env.BASE_DIR_PATH;
  const firstFour = str.substring(0, 4);
  const middleFour = str.substring(8, 12);
  const lastFour = str.substring(28);
  const newStr = `${firstFour}${middleFour}${lastFour}`;
  return `${rootDir}/${str}/${newStr}.json`;
};

exports.handler = async (req, resp, context) => { // eslint-disable-line
  try {
    this._cookies = BASE_COOKIE_OBJ;
    this._secsdkCsrfToken = await getSecsdkCsrfToken();
    const loginInfo = await login();
    console.log(loginInfo); // eslint-disable-line
    await curlRredirectUrl(loginInfo.redirect_url);
    await creatorLogin();
    await tokenBeat();
    await getCid();

    const basePath = getBasePath();
    if (fs.existsSync(basePath)) {
      fs.writeFileSync(basePath, JSON.stringify(this._cookies));
    } else {
      fs.writeFileSync(basePath, JSON.stringify(this._cookies));
    }

    resp.send(JSON.stringify(this._cookies));
  } catch (error) { // eslint-disable-next-line
    console.error(error); // log the error
    resp.statusCode = 500; // eslint-disable-line
    resp.send('Server Internal Error');
  }
};
