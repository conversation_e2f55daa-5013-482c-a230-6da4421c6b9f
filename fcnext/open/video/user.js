/* eslint-disable no-bitwise, no-multi-assign, max-len, no-param-reassign */
const request = require('request');

const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.0.0 Safari/537.36';

const generateRandomStr = (random = 16) => {
  const baseStr = 'ABCDEFGHIGKLMNOPQRSTUVWXYZabcdefghigklmnopqrstuvwxyz0123456789=';
  let randomStr = '';

  for (let i = 0; i < random; i++) {
    randomStr += baseStr[Math.floor(Math.random() * baseStr.length - 1)];
  }
  return randomStr;
};

const base36Encode = (number) => {
  const alphabet = '0123456789abcdefghijklmnopqrstuvwxyz';
  const base36 = [];

  while (number) {
    const i = number % 36;
    number = Math.floor(number / 36);
    base36.push(alphabet[i]);
  }
  return base36.reverse().join('');
};

const getSVWebId = () => {
  const e = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
  const t = e.length;
  const n = base36Encode(Date.now()); // 生成时间戳的36进制

  const r = Array(36).fill('');
  r[8] = r[13] = r[18] = r[23] = '_';
  r[14] = '4';

  for (let i = 0; i < 36; i++) {
    if (!r[i]) {
      const o = Math.floor(Math.random() * t);
      r[i] = e[i === 19 ? (o & 3) | 8 : o];
    }
  }

  return `verify_${n}_${r.join('')}`;
};

const generateHeader = async () => {
  const ssouid = await generateRandomStr(32);
  const toutiaosso = await generateRandomStr(32);
  const uidtt = await generateRandomStr(32);
  const sidtt = await generateRandomStr(32);
  const uuidToken = await generateRandomStr(32);
  const webId = await getSVWebId();
  const ttcid = await generateRandomStr(32);
  return {
    'user-agent': userAgent,
    referer: 'https://www.douyin.com/',
    Cookie: `sso_uid_tt=${ssouid}; sso_uid_tt_ss=${ssouid}; toutiao_sso_user=${toutiaosso}; toutiao_sso_user_ss=${toutiaosso}; uid_tt=${uidtt}; uid_tt_ss=${uidtt}; sid_tt=72a669422ccd5b13392f0e10db4ff5d8; sessionid=72a669422ccd5b13392f0e10db4ff5d8; sessionid_ss=72a669422ccd5b13392f0e10db4ff5d8; odin_tt=3c8429a79c339b6821982ec8787b909c09463cebd3135b7575c68a772d90e5b673aa23cf25d86358240a1d8ec1c21c64; home_can_add_dy_2_desktop=%220%22; strategyABtestKey=%************.297%22; passport_csrf_token=${uuidToken}; passport_csrf_token_default=${uuidToken}; FORCE_LOGIN=%7B%22videoConsumedRemainSeconds%22%3A180%7D; s_v_web_id=${webId}; csrf_session_id=${sidtt}; bd_ticket_guard_client_data=eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCQVEzT0x1aWx6dVY1KzFqRE1FZURlamkvb0xpdmZ6NE1wMGFDU1hoaWx3Y1RDOG9xQVR6N1V2SituKytoQmZ0SG0yY05IZmhRSHdRdmlQVGIrYWNHS3c9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoxfQ==; ttcid=${ttcid}; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A3008%2C%5C%22screen_height%5C%22%3A1692%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A8%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A10%2C%5C%22effective_type%5C%22%3A%5C%224g%5C%22%2C%5C%22round_trip_time%5C%22%3A50%7D%22; volume_info=%7B%22isUserMute%22%3Afalse%2C%22isMute%22%3Atrue%2C%22volume%22%3A0.5%7D; download_guide=%223%2F20230913%2F0%22; IsDouyinActive=true; __ac_nonce=06501747200df45603ded; msToken=Y42lYfkFnYUcNrBaJj7BW3miotpM_am3f6MsVODYV2cWi7up7p_NSA10btIsu7LxfTrhpd5lHlV8eOqTnbR32w5qv-ilft-k-awH1gY80IiI0i8oWsU=; tt_scid=.NXBCwuuUFBxYMVMvx9zgVPrjkY-HsqaCS8ACJQV0xNfCL66yo7Lu5-M1TIMp8-kb144; msToken=XW_c8MsdA8qgEBZwFxr2TPeizinA4J-SxC19uN0I8C82duDTGNsxWOUglOvSK7lK56kEOg2rG4A138Dk7Er_bCMjN4a7tbg4sEu0zNc-ogDp-Jw1Wtc=`,
  };
};

const generateXBogus = (query) => {
  const options = {
    method: 'POST',
    url: 'http://fn.bzy.ai/fe/sign',
    body: JSON.stringify({ query, userAgent }),
  };

  return new Promise((resolve, reject) => {
    request(options, (error, response) => {
      if (error) {
        reject(error);
      }
      resolve(encodeURIComponent(response.body));
    });
  });
};

exports.handler = async (req, resp, context) => { // eslint-disable-line
  const userId = decodeURIComponent(req.queries.q);
  const USER_POST_URL = 'https://www.douyin.com/aweme/v1/web/user/profile/other/?';
  const header = await generateHeader();

  const xBogusQuery = `device_platform=webapp&aid=6383&sec_user_id=${userId}&cookie_enabled=true&platform=PC&downlink=10`;
  const xBogusStr = await generateXBogus(xBogusQuery);
  const urlQuery = `${xBogusQuery}&X-Bogus=${xBogusStr}`;

  const options = {
    method: 'GET',
    url: `${USER_POST_URL}${urlQuery}`,
    headers: header,
  };

  const data = await new Promise((resolve, reject) => {
    request(options, (error, response) => {
      if (error) {
        reject(error);
      }
      resolve(response.body);
    });
  });

  const { user } = JSON.parse(data);
  const { nickname, signature } = user;
  resp.send(JSON.stringify({ nickname, signature }));
};
