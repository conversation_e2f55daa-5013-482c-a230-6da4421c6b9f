/* eslint-disable max-len, no-await-in-loop */

const request = require('request');
const qs = require('qs');
const zlib = require('zlib');
const crypto = require('crypto');
const _ = require('lodash');
const CRC32 = require('buffer-crc32');
const fs = require('fs');

const rootDir = '/home/<USER>/';
const videoDir = '/home/<USER>/';
const OSS_URL = 'https://video-clip.oss-cn-shanghai.aliyuncs.com/';
const BROWSER_NAME = 'Mozilla';
const BROWSER_VERSION = '5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
const _signature = '_02B4Z6wo00101Fgq5nwAAIDB1SsxOUWqshxYLuLAAHMI023veGRWyp9Ehn6a7b4-qTWu2lG88IrvVFIJtpnZfGerxd4YYo8NRfwxvbM3f7bO8FOjxR12vc8NBY7zL9URCyPuq56HIh0MTPOOee';

const UPLOAD_URL = 'https://vod.bytedanceapi.com/';

const baseHeaders = {
  Accept: 'application/json, text/plain, */*',
  Referer: 'https://creator.douyin.com/',
  'Accept-Encoding': 'gzip, deflate, br',
  'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
  'Sec-Ch-Ua': '"Google Chrome";v="117", "Not;A=Brand";v="8", "Chromium";v="117"',
  'Sec-Ch-Ua-Mobile': '?0',
  'Sec-Ch-Ua-Platform': '"macOS"',
  'Sec-Fetch-Dest': 'empty',
  'Sec-Fetch-Mode': 'cors',
  'Sec-Fetch-Site': 'same-origin',
  'User-Agent': `${BROWSER_NAME}/${BROWSER_VERSION}`,
};

const baseQuery = {
  cookie_enabled: 'true',
  screen_width: '3008',
  screen_height: '1692',
  browser_language: 'zh-CN',
  browser_platform: 'MacIntel',
  browser_name: BROWSER_NAME,
  browser_version: BROWSER_VERSION,
  browser_online: 'true',
  timezone_name: 'Asia/Shanghai',
  aid: '1128',
  a_bogus: 'd70mvOgFMsm1XXzVCwkz9tZ4c9g0YWRPgZENlW0y6twc',
};

const baseQueryS = {
  ...baseQuery,
  aid: 2906,
  app_name: 'aweme_creator_platform',
  device_platform: 'web',
  _signature,
};

const getBasePath = () => {
  const str = process.env.BASE_DIR_PATH;
  const firstFour = str.substring(0, 4);
  const middleFour = str.substring(8, 12);
  const lastFour = str.substring(28);
  const newStr = `${firstFour}${middleFour}${lastFour}`;
  return `${rootDir}/${str}/${newStr}.json`;
};

const getImageInfo = async (url) => {
  const options = {
    method: 'GET',
    url: `${url}?x-oss-process=image/info`,
    headers: {},
  };
  const result = await new Promise((resolve, reject) => {
    request(options, (error, response) => {
      if (error) {
        reject(error);
      }
      resolve(JSON.parse(response.body));
    });
  });
  const height = result.ImageHeight.value;
  const width = result.ImageWidth.value;
  return { height, width };
};

const randomString = (length) => {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  const maxPos = chars.length;
  let pwd = '';
  for (let i = 0; i < length; i++) {
    pwd += chars.charAt(Math.floor(Math.random() * maxPos));
  }
  return pwd;
};

const cookieObjToStr = (cookieObj) => {
  let cookieStr = '';
  Object.keys(cookieObj).forEach((key) => {
    cookieStr += `${key}=${cookieObj[key]};`;
  });

  return cookieStr;
};

const sign = (key, msg) => {
  return crypto.createHmac('sha256', key).update(msg, 'utf8').digest();
};

const getSignatureKey = (key, dateStamp, regionName, serviceName) => {
  const kDate = sign(`AWS4${key}`, dateStamp);
  const kRegion = sign(kDate, regionName);
  const kService = sign(kRegion, serviceName);
  const kSigning = sign(kService, 'aws4_request');
  return kSigning;
};

const AWSsignature = (secretKey, requestParameters, headers, method = 'GET', payload = '', region = 'cn-north-1', service = 'vod') => {
  const canonicalUri = '/';
  const canonicalQuerystring = requestParameters;
  const canonicalHeaders = `${Object.entries(headers).map(([key, value]) => { return `${key}:${value}`; }).join('\n')}\n`;
  const signedHeaders = Object.keys(headers).join(';');
  const payloadHash = crypto.createHash('sha256').update(payload, 'utf8').digest('hex');
  const canonicalRequest = `${method}\n${canonicalUri}\n${canonicalQuerystring}\n${canonicalHeaders}\n${signedHeaders}\n${payloadHash}`;
  const amzdate = headers['x-amz-date'];
  const datestamp = amzdate.split('T')[0];
  const algorithm = 'AWS4-HMAC-SHA256';
  const credentialScope = `${datestamp}/${region}/${service}/aws4_request`;
  const stringToSign = `${algorithm}\n${amzdate}\n${credentialScope}\n${crypto.createHash('sha256').update(canonicalRequest, 'utf8').digest('hex')}`;

  const signingKey = getSignatureKey(secretKey, datestamp, region, service);
  const signature = crypto.createHmac('sha256', signingKey).update(stringToSign, 'utf8').digest('hex');

  return signature;
};

const crc32 = (content) => {
  const crc = CRC32(content);
  return crc.toString('hex').padStart(8, '0');
};

const getReqCookie = (data = {}) => {
  const obj = _.isEmpty(data) ? this._cookies : data;
  const cookieArr = [];
  Object.keys(obj).forEach((key) => {
    cookieArr.push(`${key}=${this._cookies[key]}`);
  });
  return cookieArr.join('; ');
};

const setCookiesHelper = (setCookies = []) => {
  const obj = {};
  setCookies.forEach((cookie) => {
    const [key, value] = _.head(cookie.split(';')).split('=');
    obj[key.trim()] = value.trim();
  });
  return obj;
};

const baseRequest = async (options) => {
  const data = await new Promise((resolve, reject) => {
    request(options, (error, response) => {
      if (error) {
        reject(error);
      }
      resolve(response.body);
    });
  });
  // 对 data 进行 JSON parse, 若无err 直接返回
  let result = data.toString();
  try {
    result = JSON.parse(result);
    result = JSON.stringify(result);
  } catch (err1) {
    result = await new Promise((resolve) => {
      zlib.brotliDecompress(data, (err, buffer) => {
        if (!err) {
          resolve(buffer.toString());
        } else {
          resolve(err);
        }
      });
    });
  }
  return result;
};

const getSecsdkCsrfToken = async () => {
  const options = { method: 'get', url: 'https://summon.bytedance.com/secsdk_csrf_token' };
  const { data } = await new Promise((resolve, reject) => {
    request(options, (err, res, body) => {
      if (err) {
        reject(err);
      }
      resolve(JSON.parse(body));
    });
  });

  return data.token;
};

const conferLogin = async (secUserId) => {
  const options = {
    url: `https://creator.douyin.com/aweme/v1/creator/permission/confer/login/?${qs.stringify(baseQueryS)}`,
    method: 'POST',
    headers: {
      cookie: cookieObjToStr(this._cookies),
      ...baseHeaders,
      'Content-Type': 'application/json;charset=UTF-8',
      'X-Secsdk-Csrf-Token': this._secsdkCsrfToken,
    },
    body: JSON.stringify({ sec_user_id: secUserId }),
  };

  const data = await new Promise((resolve, reject) => {
    request(options, (err, res) => {
      if (err) {
        reject(err);
      }
      const setCookies = res.headers['set-cookie'];
      const obj = setCookiesHelper(setCookies);
      resolve(obj);
    });
  });

  return data;
};

const getAuth = async () => {
  const qryObj = { ...baseQuery, msToken: this._cookies.msToken };
  const query = qs.stringify(qryObj);
  const options = {
    method: 'GET',
    encoding: null,
    url: `https://creator.douyin.com/web/api/media/upload/auth/v5/?${query}`,
    headers: { Cookie: getReqCookie(), ...baseHeaders },
  };

  const result = await baseRequest(options);
  const { auth } = JSON.parse(result);
  return JSON.parse(auth);
};

const getUserInfo = async () => {
  const qryObj = { ...baseQuery, msToken: this._cookies.msToken };
  const query = qs.stringify(qryObj);
  const options = {
    method: 'GET',
    encoding: null,
    url: `https://creator.douyin.com/web/api/media/user/info/?${query}`,
    headers: { Cookie: getReqCookie(), ...baseHeaders },
  };

  const result = await baseRequest(options);
  const { user } = JSON.parse(result || '{}');
  if (_.isUndefined(user)) {
    console.log(result); // eslint-disable-line
  }
  return user;
};

const applyUploadInner = async (auth, fileSize) => {
  const accessKey = auth.AccessKeyID;
  const secretKey = auth.SecretAccessKey;

  const obj = {
    Action: 'ApplyUploadInner',
    Version: '2020-11-19',
    SpaceName: 'aweme',
    FileType: 'video',
    IsInner: '1',
    FileSize: fileSize,
    app_id: '2906',
    user_id: this._user.uid,
    s: 'p1a0yn7lte',
  };
  const sortedKeys = Object.keys(obj).sort();
  const sortedObj = {};
  sortedKeys.forEach((key) => { sortedObj[key] = obj[key]; });

  const requestParameters = qs.stringify(sortedObj);
  const t = new Date();
  const amzdate = t.toISOString().replace(/[:-]|\.\d{3}/g, '');
  const datestamp = amzdate.slice(0, 8);

  const headers = {
    'x-amz-date': amzdate,
    'x-amz-security-token': auth.SessionToken,
  };
  const signature = AWSsignature(secretKey, requestParameters, headers);
  const authorization = `AWS4-HMAC-SHA256 Credential=${accessKey}/${datestamp}/cn-north-1/vod/aws4_request, SignedHeaders=x-amz-date;x-amz-security-token, Signature=${signature}`;

  const options = {
    method: 'GET',
    encoding: null,
    url: `${UPLOAD_URL}?${requestParameters}`,
    headers: { ...headers, authorization, ...baseHeaders },
  };

  const result = await baseRequest(options);
  const { Result } = JSON.parse(result);
  return _.head(Result?.InnerUploadAddress?.UploadNodes);
};

const commitUploadInner = async (auth, node) => {
  const obj = {
    Action: 'CommitUploadInner',
    Version: '2020-11-19',
    SpaceName: 'aweme',
    app_id: '2906',
    user_id: this._user.uid,
  };
  const sortedKeys = Object.keys(obj).sort();
  const sortedObj = {};
  sortedKeys.forEach((key) => { sortedObj[key] = obj[key]; });
  const bodyData = JSON.stringify({
    SessionKey: node.SessionKey,
    Functions: [{ name: 'GetMeta' }, { name: 'Snapshot', input: { SnapshotTime: 0 } }],
  });
  const requestParameters = qs.stringify(sortedObj);
  const t = new Date();
  const amzdate = t.toISOString().replace(/[:-]|\.\d{3}/g, '');
  const datestamp = amzdate.slice(0, 8);

  const amzcontentsha256 = crypto.createHash('sha256').update(bodyData).digest('hex');
  const headers = {
    'x-amz-content-sha256': amzcontentsha256,
    'x-amz-date': amzdate,
    'x-amz-security-token': auth.SessionToken,
  };
  const signature = AWSsignature(auth.SecretAccessKey, requestParameters, headers, 'POST', bodyData);
  const authorization = `AWS4-HMAC-SHA256 Credential=${auth.AccessKeyID}/${datestamp}/cn-north-1/vod/aws4_request, SignedHeaders=x-amz-content-sha256;x-amz-date;x-amz-security-token, Signature=${signature}`;

  const options = {
    method: 'POST',
    encoding: null,
    url: `${UPLOAD_URL}?${requestParameters}`,
    headers: { ...headers, authorization, ...baseHeaders, 'Content-Type': 'text/plain;charset=UTF-8' },
    body: bodyData,
  };

  const result = await baseRequest(options);
  return JSON.parse(result);
};

const getUploadId = async (node) => {
  const storeUri = node.StoreInfos[0].StoreUri;
  const uploadHost = node.UploadHost;
  const rand = Array.from({ length: 30 }, () => { return Math.floor(Math.random() * 10); }).join('');
  const url = `https://${uploadHost}/${storeUri}?uploads`;
  const headers = {
    Authorization: node.StoreInfos[0].Auth,
    'Content-Type': `multipart/form-data; boundary=---------------------------${rand}`,
  };
  const data = `-----------------------------${rand}--`;
  const { payload } = await new Promise((resolve, reject) => {
    request.post({ url, headers, body: data }, (err, httpResponse, body) => {
      if (err) {
        reject(err);
      }
      resolve(JSON.parse(body));
    });
  });

  return payload.uploadID;
};

const uploadVideo = async (node, videoContent, uploadId) => {
  const storeUri = node.StoreInfos[0].StoreUri;
  const videoAuth = node.StoreInfos[0].Auth;
  const uploadHost = node.UploadHost;
  const fileSize = videoContent.length;

  const chunkSize = 5242880;
  const chunks = [];
  for (let i = 0; i < fileSize; i += chunkSize) {
    const chunk = videoContent.slice(i, i + chunkSize);
    chunks.push(chunk);
  }

  const crcs = [];
  let partOffset = 0;
  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    const crc = crc32(chunk);
    crcs.push(crc);
    const qryObj = { partNumber: i + 1, uploadID: uploadId, phase: 'transfer', part_offset: partOffset };
    const url = `https://${uploadHost}/${storeUri}?${qs.stringify(qryObj)}}`;
    const headers = {
      Authorization: videoAuth,
      'Content-Type': 'application/octet-stream',
      'Content-Disposition': 'attachment; filename="undefined"',
      'Content-Crc32': crc,
    };
    await new Promise((resolve, reject) => {
      request.post({ url, headers, body: chunk }, (err, httpResponse, body) => {
        if (err) {
          reject(err);
        }
        resolve(JSON.parse(body));
      });
    });
    partOffset += chunk.length;
    console.log(`upload part ${i + 1} success`); // eslint-disable-line
  }

  const data = crcs.map((crc, i) => { return `${i + 1}:${crc}`; }).join(',');
  const url = `https://${uploadHost}/${storeUri}?uploadmode=part&phase=finish&uploadID=${uploadId}`;
  const headers = { Authorization: videoAuth, 'Content-Type': 'text/plain;charset=UTF-8' };

  await new Promise((resolve, reject) => {
    request.post({ url, headers, body: data }, (err, httpResponse, body) => {
      if (err) {
        reject(err);
      }
      resolve(JSON.parse(body));
    });
  });
  console.log('upload success'); // eslint-disable-line
};

const finishUpload = async (videoId) => {
  const qryObj = { video_id: videoId, msToken: this._cookies.msToken, ...baseQuery };
  const query = qs.stringify(qryObj);

  const options = {
    method: 'GET',
    encoding: null,
    url: `https://creator.douyin.com/web/api/media/video/enable/?${query}`,
    headers: { Cookie: getReqCookie(), ...baseHeaders },
  };

  const enableData = await new Promise((resolve, reject) => {
    request(options, (error, response) => {
      if (error) {
        reject(error);
      }
      resolve(response.body);
    });
  });
  const enableResult = enableData.toString();
  options.url = `https://creator.douyin.com/web/api/media/video/transend/?${query}`;
  const transendData = await baseRequest(options);
  return { enableResult: JSON.parse(enableResult), transendResult: JSON.parse(transendData) };
};

const applyImageUpload = async (auth) => {
  const { SessionToken, AccessKeyID, SecretAccessKey } = auth;
  const qryObj = {
    Action: 'ApplyImageUpload',
    Version: '2018-08-01',
    ServiceId: 'jm8ajry58r',
    app_id: '2906',
    user_id: this._user.uid,
    s: randomString(11),
  };
  const sortedKeys = Object.keys(qryObj).sort();
  const sortedObj = {};
  sortedKeys.forEach((key) => { sortedObj[key] = qryObj[key]; });

  const requestParameters = qs.stringify(sortedObj);
  const t = new Date();
  const amzdate = t.toISOString().replace(/[:-]|\.\d{3}/g, '');
  const datestamp = amzdate.slice(0, 8);

  const headers = {
    'x-amz-date': amzdate,
    'x-amz-security-token': SessionToken,
  };
  const signature = AWSsignature(SecretAccessKey, requestParameters, headers, 'GET', '', 'cn-north-1', 'imagex');
  const authorization = `AWS4-HMAC-SHA256 Credential=${AccessKeyID}/${datestamp}/cn-north-1/imagex/aws4_request, SignedHeaders=x-amz-date;x-amz-security-token, Signature=${signature}`;

  const options = {
    method: 'GET',
    encoding: null,
    url: `https://imagex.bytedanceapi.com/?${requestParameters}`,
    headers: { ...headers, authorization, ...baseHeaders },
  };

  const result = await baseRequest(options);
  const { Result } = JSON.parse(result);
  return _.head(Result?.InnerUploadAddress?.UploadNodes);
};

const uploadImage = async (node, filename) => {
  const imgBuffer = fs.readFileSync(filename);
  const url = `https://${node.UploadHost}/${node.StoreInfos[0].StoreUri}`;

  const headers = {
    Authorization: node.StoreInfos[0].Auth,
    'Content-Type': 'application/octet-stream',
    'Content-Disposition': 'attachment; filename="undefined"',
    'Content-Crc32': crc32(imgBuffer),
  };
  await new Promise((resolve, reject) => {
    request.post({ url, headers, body: imgBuffer }, (err, httpResponse, body) => {
      if (err) {
        reject(err);
      }

      resolve(JSON.parse(body));
    });
  });
};

const commitImageUpload = async (auth, node) => {
  const obj = {
    Action: 'CommitImageUpload',
    Version: '2018-08-01',
    ServiceId: 'jm8ajry58r',
    app_id: '2906',
    user_id: this._user.uid,
  };
  const sortedKeys = Object.keys(obj).sort();
  const sortedObj = {};
  sortedKeys.forEach((key) => { sortedObj[key] = obj[key]; });
  const bodyData = JSON.stringify({ SessionKey: node.SessionKey });
  const requestParameters = qs.stringify(sortedObj);
  const t = new Date();
  const amzdate = t.toISOString().replace(/[:-]|\.\d{3}/g, '');
  const datestamp = amzdate.slice(0, 8);

  const amzcontentsha256 = crypto.createHash('sha256').update(bodyData).digest('hex');
  const headers = {
    'x-amz-content-sha256': amzcontentsha256,
    'x-amz-date': amzdate,
    'x-amz-security-token': auth.SessionToken,
  };

  const signature = AWSsignature(auth.SecretAccessKey, requestParameters, headers, 'POST', bodyData, 'cn-north-1', 'imagex');
  const authorization = `AWS4-HMAC-SHA256 Credential=${auth.AccessKeyID}/${datestamp}/cn-north-1/imagex/aws4_request, SignedHeaders=x-amz-content-sha256;x-amz-date;x-amz-security-token, Signature=${signature}`;

  const options = {
    method: 'POST',
    encoding: null,
    url: `https://imagex.bytedanceapi.com/?${requestParameters}`,
    headers: { ...headers, authorization, ...baseHeaders, 'Content-Type': 'text/plain;charset=UTF-8' },
    body: bodyData,
  };

  const result = await baseRequest(options);
  return JSON.parse(result);
};

const getCover = async (uri) => {
  const query = qs.stringify({ uri, ...baseQuery });

  const options = {
    method: 'GET',
    encoding: null,
    url: `https://creator.douyin.com/aweme/v1/creator/get/url/?${query}`,
    headers: { Cookie: getReqCookie(), ...baseHeaders },
  };

  const result = await baseRequest(options);
  return JSON.parse(result);
};

const getCsrftoken = async () => {
  const options = {
    method: 'HEAD',
    url: 'https://creator.douyin.com/web/api/media/aweme/create',
    headers: {
      'X-Secsdk-Csrf-Request': '1',
      'X-Secsdk-Csrf-Version': '1.2.7',
      Cookie: getReqCookie(),
    },
  };

  return new Promise((resolve, reject) => {
    request(options, (error, response) => {
      if (error) {
        reject(error);
      }
      const token = response.headers['x-ware-csrf-token'];
      resolve(token.split(',')[1]);
    });
  });
};

const createVideo = async (params) => {
  const qryObj = { ...baseQuery, msToken: this._cookies.msToken };
  const query = qs.stringify(qryObj);
  const { width, height } = params.imgInfo;
  const formData = {
    activity: '[]',
    challenges: '[]',
    chapter: '{ chapter_details: [] }',
    cover_optimized_uri: '',
    cover_text: undefined,
    cover_text_uri: undefined,
    cover_tools_info: `{"cover_width":${width},"cover_height":${height},"cover_type":"upload"}`,
    custom_cover_image_height: `${height}`,
    custom_cover_image_width: `${width}`,
    download: '1',
    hashtag_source: params.hashtagSource,
    hot_sentence: '',
    ifLongTitle: _.isEmpty(params.text || '') ? 'false' : 'true',
    interaction_stickers: '[]',
    is_post_assistant: '1',
    is_preview: '0',
    knowl_id: '',
    knowl_order: '0',
    mentions: '[]',
    music_id: 'null',
    music_source: '0',
    poster: params.imgUri,
    poster_delay: '33',
    poster_param: '{}',
    record: 'null',
    source_info: '{}',
    text: params.text || '',
    text_extra: JSON.stringify(params.textExtra),
    timing: '0',
    upload_source: '1',
    video_id: params.videoId,
    visibility_type: '0',
  };
  const options = {
    method: 'POST',
    url: `https://creator.douyin.com/web/api/media/aweme/create/?${query}`,
    encoding: null,
    headers: {
      ...baseHeaders,
      Cookie: getReqCookie(),
      'X-Secsdk-Csrf-Token': params.xSecsdkCsrfToken,
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
    },
    form: formData,
  };

  const result = await baseRequest(options);
  return result;
};

exports.handler = async (req, resp, context) => { // eslint-disable-line
  const { secUserId, videoUrl, imgUrl, tags, desc } = JSON.parse(req.body.toString());
  if (_.isUndefined(videoUrl) || _.isUndefined(imgUrl)) {
    resp.statusCode = 500; // eslint-disable-line
    resp.send('Server Internal Error');
    return;
  }

  if (!_.startsWith(videoUrl, OSS_URL) || !_.startsWith(imgUrl, OSS_URL)) {
    resp.statusCode = 500; // eslint-disable-line
    resp.send('Server Internal Error');
    return;
  }
  const text = `${desc || ''}\n${(tags || []).map((item) => { return `#${item}`; }).join(' ')}`;
  const textExtra = (tags || []).map((item) => {
    return {
      start: text.indexOf(`#${item}`),
      end: text.indexOf(`#${item}`) + `#${item}`.length,
      type: 1,
      hashtag_name: item,
      hashtag_id: 0,
      user_id: '',
    };
  });

  const hashtagSource = Array.from({ length: (tags || []).length }, () => { return 'search'; }).join('/');

  const basePath = getBasePath();
  this._cookies = JSON.parse(fs.readFileSync(basePath));
  this._secsdkCsrfToken = await getSecsdkCsrfToken();
  const conferData = await conferLogin(secUserId);
  this._cookies = { ...this._cookies, ...conferData };
  this._user = await getUserInfo();
  if (_.isUndefined(this._user)) {
    resp.statusCode = 403; // eslint-disable-line
    resp.send('auth failed');
    return;
  }
  console.log(`${this._user?.nickname}(${this._user.uid}): login success !`); // eslint-disable-line
  const auth = await getAuth();

  const imgInfo = await getImageInfo(imgUrl);
  const videoPath = videoUrl.replace(`${OSS_URL}`, videoDir);
  const videoContent = fs.readFileSync(videoPath);
  const fileSize = videoContent.length;
  const uploadVideoNode = await applyUploadInner(auth, fileSize);
  const videoUploadID = await getUploadId(uploadVideoNode);
  console.log('video UploadID:', videoUploadID); // eslint-disable-line
  await uploadVideo(uploadVideoNode, videoContent, videoUploadID);
  const commitData = await commitUploadInner(auth, uploadVideoNode);
  const videoId = _.head(commitData?.Result?.Results)?.Vid;
  console.log('videoId', videoId); // eslint-disable-line
  console.log('VideoMeta', _.head(commitData?.Result?.Results)?.VideoMeta); // eslint-disable-line
  const finishUploadInfo = await finishUpload(videoId);
  console.log('finishUploadInfo', finishUploadInfo); // eslint-disable-line

  const uploadImageNode = await applyImageUpload(auth);
  const imagePath = imgUrl.replace(`${OSS_URL}`, videoDir);
  await uploadImage(uploadImageNode, imagePath);
  const commitImgData = await commitImageUpload(auth, uploadImageNode);
  const imgUri = _.head(commitImgData?.Result?.Results)?.Uri;
  const coverData = await getCover(imgUri);
  console.log('coverData', coverData); // eslint-disable-line

  const xSecsdkCsrfToken = await getCsrftoken();
  const videoParams = { videoId, imgUri, imgInfo, text, textExtra, xSecsdkCsrfToken, hashtagSource };
  const createResult = await createVideo(videoParams);
  console.log('createResult', createResult); // eslint-disable-line
  resp.send(createResult);
};
