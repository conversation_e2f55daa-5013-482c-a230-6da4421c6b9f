const fs = require('fs');
const Core = require('@alicloud/pop-core');

const rootDir = '/home/<USER>/';
const client = new Core({
  accessKeyId: process.env.ak,
  accessKeySecret: process.env.sk,
  endpoint: 'https://ecs.cn-shanghai.aliyuncs.com',
  apiVersion: '2014-05-26',
});

exports.handler = async (req, resp, context) => { // eslint-disable-line
  const { phone, code, isSend } = JSON.parse(req.body.toString());
  if (isSend) {
    const params = { InstanceId: process.env.pcId };
    const requestOption = { method: 'POST', formatParams: false };
    await client.request('StartInstance', params, requestOption);
  }

  fs.writeFileSync(`${rootDir}/codes/${phone}.json`, code || '');
  resp.send('ok');
};
