const tencentcloud = require('tencentcloud-sdk-nodejs');

const AsrClient = tencentcloud.asr.v20190614.Client;

const clientConfig = {
  credential: { secretId: process.env.tak, secretKey: process.env.tsk },
  profile: { httpProfile: { endpoint: 'asr.ap-shanghai.tencentcloudapi.com' } },
};

const asrClient = new AsrClient(clientConfig);

// 录音识别 大于 60s
const recTask = async (url) => {
  const params = {
    EngineModelType: '16k_zh',
    ChannelNum: 1,
    ResTextFormat: 3,
    SourceType: 0,
    Url: url,
  };
  const { Data } = await asrClient.CreateRecTask(params);

  return new Promise((resolve, reject) => { // eslint-disable-line
    const timer = setInterval(async () => {
      const result = await asrClient.DescribeTaskStatus(Data);
      if (!['doing', 'waiting'].includes(result.Data.StatusStr)) {
        clearInterval(timer);
        resolve(result.Data);
      }
    }, 5 * 1000);
  });
};

exports.handler = async (req, resp, context) => { // eslint-disable-line
  const { Result, ResultDetail } = await recTask(decodeURIComponent(req.queries.q));
  resp.send(JSON.stringify({ content: Result, detail: ResultDetail }));
};
