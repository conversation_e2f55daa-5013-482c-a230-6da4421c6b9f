/* eslint-disable no-await-in-loop */
const request = require('request');

const sleep = (ms) => {
  return new Promise((resolve) => { return setTimeout(resolve, ms); });
};

const transcription = async (url) => {
  const options = {
    method: 'POST',
    url: 'https://dashscope.aliyuncs.com/api/v1/services/audio/asr/transcription',
    headers: {
      'X-DashScope-Async': 'enable',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${process.env.ak}`,
    },
    body: JSON.stringify({
      model: 'paraformer-v1',
      input: { file_urls: [url] },
    }),
  };

  return new Promise((resolve, reject) => {
    request(options, (error, response) => {
      if (error) reject(error);
      resolve(JSON.parse(response.body));
    });
  });
};

const queryTask = async (taskId) => {
  const options = {
    method: 'GET',
    url: `https://dashscope.aliyuncs.com/api/v1/tasks/${taskId}`,
    headers: {
      'user-agent': 'dashscope/1,6,0; python/3.9.0; platform/mz; processor/asdasd',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${process.env.ak}`,
    },
  };

  return new Promise((resolve, reject) => {
    request(options, (error, response) => {
      if (error) reject(error);
      resolve(JSON.parse(response.body));
    });
  });
};

const getResult = (url) => {
  const options = { method: 'GET', url };

  return new Promise((resolve, reject) => {
    request(options, (error, response) => {
      if (error) reject(error);
      resolve(JSON.parse(response.body));
    });
  });
};

exports.handler = async (req, resp, context) => { // eslint-disable-line
  const { output } = await transcription(decodeURIComponent(req.queries.q));
  let taskStatus = output?.task_status;
  let data = {};
  while (['PENDING', 'RUNNING'].includes(taskStatus)) {
    data = await queryTask(output?.task_id);
    taskStatus = data?.output?.task_status;
    await sleep(500);
  }

  if (taskStatus === 'SUCCEEDED') {
    const url = data?.output?.results[0]?.transcription_url;
    const result = await getResult(url);
    resp.send(JSON.stringify({ content: result?.transcripts[0].text }));
  } else {
    resp.send(JSON.stringify({ err: 'err' }));
  }
};
