/* eslint-disable no-await-in-loop */
const { RPCClient } = require('@alicloud/pop-core');
const request = require('request');
const ffmpeg = require('fluent-ffmpeg');
const Nls = require('alibabacloud-nls');

const URL = 'wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1';

const sleep = (ms) => {
  return new Promise((resolve) => { return setTimeout(resolve, ms); });
};

const client = new RPCClient({
  accessKeyId: process.env.ak,
  accessKeySecret: process.env.sk,
  endpoint: 'http://nls-meta.cn-shanghai.aliyuncs.com',
  apiVersion: '2019-02-28',
});

const getToken = async () => {
  const result = await client.request('CreateToken');
  return result.Token.Id;
};

const initNls = async () => {
  const token = await getToken();
  const st = new Nls.SpeechTranscription({ url: URL, appkey: process.env.appkey, token });
  await st.start({
    format: 'wav',
    sample_rate: 16000,
    enable_intermediate_result: false,
    enable_punctuation_prediction: true,
    enable_inverse_text_normalization: false,
  }, true, 1000);


  return st;
};

const sendAudioStream = (url) => {
  const stream = request(url);
  const proc = new ffmpeg({ source: stream }); // eslint-disable-line
  const audioStream = proc.outputFormat('wav').audioBitrate(16).audioChannels(1).audioFrequency(16000)
    .pipe();

  const b1 = [];

  audioStream.on('data', (chunk) => {
    b1.push(chunk);
  });

  return new Promise((resolve) => {
    audioStream.on('close', () => {
      resolve(b1);
    });
  });
};

exports.handler = async (req, resp, context) => { // eslint-disable-line
  const st = await initNls();
  const url = decodeURIComponent(req.queries.q);
  const streams = await sendAudioStream(url);
  let content = '';
  st.on('end', (msg) => {
    const { payload } = JSON.parse(msg);
    content += payload.result;
  });

  st.on('failed', (msg) => {
    resp.send(JSON.stringify({ content: msg }));
  });
  st.on('closed', () => {
    resp.send(JSON.stringify({ content }));
  });

  try {
    for (const b of streams) {
      if (!st.sendAudio(b)) {
        throw new Error('send audio failed');
      }
      await sleep(20);
    }
  } catch (error) {
    console.log('sendAudio failed:', error); // eslint-disable-line
  }

  resp.send(JSON.stringify({ content }));
};
