// ==UserScript==
// @name         抖音创作者中心 - 数据下载
// @namespace    http://tampermonkey.net/
// @version      3
// @description  在抖音创作者中心数据页面的数据下载，支持音频提取
// <AUTHOR>
// @match        https://creator.douyin.com/creator-micro/data-center/content*
// @grant        none
// ==/UserScript==

(function () {
  'use strict';

  // ==================== 音频提取相关函数 ====================

  // 从HTML字符串中提取带nonce属性的script标签内容
  function extractNonceScriptFromHTML(htmlString) {
    console.log('🔍 开始提取nonce script标签内容...');

    try {
      // 验证输入
      if (!htmlString || typeof htmlString !== 'string') {
        console.error('❌ 无效的HTML字符串');
        return null;
      }

      console.log(`📄 HTML字符串长度: ${htmlString.length} 字符`);

      let results = [];

      // 匹配所有带nonce属性的script标签
      console.log('🔍 查找所有带nonce属性的script标签...');
      const regex = /<script\s+nonce=["']([^"']+)["'][^>]*>(.*?)<\/script>/gis;
      let match;
      while ((match = regex.exec(htmlString)) !== null) {
        const nonceValue = match[1];
        const scriptContent = match[2].trim();

        results.push({
          nonce: nonceValue,
          content: scriptContent,
          fullMatch: match[0]
        });
      }

      // 去重处理
      const uniqueResults = [];
      const seen = new Set();
      for (const result of results) {
        const key = `${result.nonce}:${result.content.substring(0, 100)}`;
        if (!seen.has(key)) {
          seen.add(key);
          uniqueResults.push(result);
        }
      }

      if (uniqueResults.length === 0) {
        console.error('❌ 未找到带nonce属性的script标签');
        return null;
      }

      // 如果只有一个结果，直接返回内容；否则返回数组
      return uniqueResults.length === 1 ? uniqueResults[0] : uniqueResults;

    } catch (error) {
      console.error('❌ 提取nonce script时发生错误:', error.message);
      return null;
    }
  }

  // 解析特殊格式数据
  function parseSpecialFormat(rawData) {
    try {
      // 阶段1: 基础字符串清理
      let cleanedData = rawData
        // 处理转义的双引号
        .replace(/\\"/g, '"')
        // 处理转义的反斜杠
        .replace(/\\\\/g, '\\');

      // 阶段2: 处理特殊标记
      cleanedData = cleanedData
        // 将 "$undefined" 替换为 null (JSON中没有undefined)
        .replace(/"?\$undefined"?/g, 'null')
        // 将 "$L9" 等特殊标记替换为 null
        .replace(/"?\$L\d+"?/g, 'null')
        // 将 "$" 单独出现时替换为 null
        .replace(/"?\$"(?=,|\]|\})/g, 'null');

      // 阶段3: 尝试提取有效的JSON部分
      // 如果数据以数组格式开始，尝试提取第二个元素（通常是JSON字符串）
      if (cleanedData.startsWith('[')) {
        try {
          const parsed = JSON.parse(cleanedData);
          if (Array.isArray(parsed) && parsed.length > 1) {
            // 检查第二个元素是否是JSON字符串
            const secondElement = parsed[1];
            if (typeof secondElement === 'string' && secondElement.includes('{')) {
              // 尝试解析嵌套的JSON
              return JSON.parse(secondElement);
            }
          }
          return parsed;
        } catch (e) {
          // 如果直接解析失败，继续后续处理
        }
      }

      // 阶段4: 处理复杂的嵌套结构
      // 查找JSON对象的开始位置
      const jsonStart = cleanedData.indexOf('{');
      if (jsonStart !== -1) {
        let braceCount = 0;
        let jsonEnd = jsonStart;

        for (let i = jsonStart; i < cleanedData.length; i++) {
          if (cleanedData[i] === '{') braceCount++;
          if (cleanedData[i] === '}') braceCount--;
          if (braceCount === 0) {
            jsonEnd = i;
            break;
          }
        }

        if (jsonEnd > jsonStart) {
          const jsonPart = cleanedData.substring(jsonStart, jsonEnd + 1);
          return JSON.parse(jsonPart);
        }
      }

      // 阶段5: 最后尝试直接解析
      return JSON.parse(cleanedData);

    } catch (error) {
      console.error('数据转换失败:', error);
      console.log('原始数据:', rawData);
      console.log('清理后数据:', cleanedData);

      // 返回一个包含错误信息的对象
      return {
        error: true,
        message: error.message,
        rawData: rawData.substring(0, 200) + '...' // 只显示前200字符
      };
    }
  }

  // 通过itemId获取音频URL
  async function fetchAudioUrl(itemId) {
    try {
      console.log(`🎵 开始获取音频URL，itemId: ${itemId}`);

      // 验证itemId格式
      if (!itemId || !/^\d+$/.test(String(itemId))) {
        throw new Error(`无效的itemId格式: ${itemId}`);
      }

      const url = `https://www.douyin.com/light/${itemId}`;
      console.log(`🌐 请求URL: ${url}`);

      const response = await fetch(url, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (!response.ok) {
        throw new Error(`请求失败: ${response.status} ${response.statusText}`);
      }

      const htmlContent = await response.text();
      console.log(`📄 获取到HTML内容，长度: ${htmlContent.length} 字符`);

      // 提取nonce script标签
      const scriptResults = extractNonceScriptFromHTML(htmlContent);
      if (!scriptResults) {
        throw new Error('未找到nonce script标签');
      }

      // 查找包含特定内容的script
      const scripts = Array.isArray(scriptResults) ? scriptResults : [scriptResults];
      const targetScript = scripts.find(script =>
        script.content.indexOf("self.__pace_f.push") >= 0 &&
        script.content.indexOf('defaultData') > 0
      );

      if (!targetScript) {
        throw new Error('未找到包含defaultData的目标script');
      }

      console.log(`🎯 找到目标script，内容长度: ${targetScript.content.length} 字符`);

      // 解析script内容
      const parsedData = parseSpecialFormat(targetScript.content);
      if (parsedData.error) {
        throw new Error(`解析script内容失败: ${parsedData.message}`);
      }

      // 提取音频URL
      const audioUrl = parsedData?.defaultData?.music?.playUrl?.uri;
      if (!audioUrl) {
        throw new Error('未找到音频URL路径');
      }

      console.log(`✅ 成功获取音频URL: ${audioUrl}`);
      return audioUrl;

    } catch (error) {
      console.error(`❌ 获取音频URL失败 (itemId: ${itemId}):`, error);
      throw error;
    }
  }

  // 下载音频文件
  async function downloadAudio(audioUrl, fileName) {
    try {
      console.log(`🎵 开始下载音频: ${fileName}`);
      console.log(`🔗 音频URL: ${audioUrl}`);
      // 显示开始下载提示
      updateStatusIndicator(`🎵 正在下载音频文件: ${fileName}`);
      const response = await fetch(audioUrl, {
        method: 'GET',
        mode: 'cors'
      });

      if (!response.ok) {
        throw new Error(`下载失败: ${response.status} ${response.statusText}`);
      }
      // 显示正在处理文件提示
      updateStatusIndicator(`📦 正在处理音频文件: ${fileName}`);
      const audioBlob = await response.blob();
      const fileSizeMB = (audioBlob.size / 1024 / 1024).toFixed(2);

      console.log(`📦 音频文件大小: ${(audioBlob.size / 1024 / 1024).toFixed(2)} MB`);

      // 创建下载链接
      const url = URL.createObjectURL(audioBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 显示下载完成提示
      updateStatusIndicator(`✅ 音频下载完成: ${fileName} (${fileSizeMB} MB)`);

      // 延迟清理URL对象
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 3000);

      console.log(`✅ 音频下载完成: ${fileName}`);

    } catch (error) {
      console.error(`❌ 下载音频失败: ${fileName}`, error);
      throw error;
    }
  }

  // 批量下载音频文件
  async function downloadAudioFiles(itemIds) {
    console.log(`🎵 开始批量下载 ${itemIds.length} 个音频文件...`);

    let successCount = 0;
    let failCount = 0;

    for (let i = 0; i < itemIds.length; i++) {
      const itemId = itemIds[i];

      try {
        updateStatusIndicator(`🎵 正在下载音频 ${i + 1}/${itemIds.length}: ${itemId}`);

        // 获取音频URL
        const audioUrl = await fetchAudioUrl(itemId);

        // 生成文件名（与现有CSV文件命名规则保持一致）
        const itemDesc = getItemDescription(itemId);
        const descPrefix = itemDesc.substring(0, 20);
        const sanitizedDesc = sanitizeFileName(descPrefix);
        const fileName = `${sanitizedDesc}_音频.mp3`;

        // 下载音频文件
        await downloadAudio(audioUrl, fileName);

        successCount++;
        console.log(`✅ 音频下载成功 ${i + 1}/${itemIds.length}: ${fileName}`);

        // 每个文件之间等待1秒，避免请求过于频繁
        if (i < itemIds.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (error) {
        failCount++;
        console.error(`❌ 音频下载失败 ${i + 1}/${itemIds.length} (itemId: ${itemId}):`, error);

        // 即使失败也继续下载下一个
        continue;
      }
    }

    console.log(`🎉 批量音频下载完成！成功: ${successCount}个，失败: ${failCount}个`);

    if (failCount > 0) {
      updateStatusIndicator(`🎵 音频下载完成：成功${successCount}个，失败${failCount}个`);
    } else {
      updateStatusIndicator(`🎵 所有音频下载完成！共${successCount}个文件`);
    }

    // 显示2秒后继续
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  // ==================== 原有脚本代码 ====================

  // 全局变量
  let buttonInserted = false;
  let observer = null;
  let statusIndicator = null;

  // 创建状态指示器
  function createStatusIndicator() {
    if (statusIndicator) return;

    statusIndicator = document.createElement('div');
    statusIndicator.id = 'douyin-status-indicator';
    statusIndicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
            font-family: Arial, sans-serif;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            min-width: 200px;
            text-align: center;
        `;

    document.body.appendChild(statusIndicator);
  }

  // 更新状态指示器
  function updateStatusIndicator(message) {
    if (!statusIndicator) createStatusIndicator();
    statusIndicator.textContent = message;
  }

  // 移除状态指示器
  function removeStatusIndicator() {
    if (statusIndicator) {
      statusIndicator.remove();
      statusIndicator = null;
    }
  }

  // 查找所有标签页元素并返回最后一个
  function findLastTabElement() {
    const tabs = document.querySelectorAll('.douyin-creator-pc-tabs-tab-single');

    if (tabs.length > 0) {
      const lastTab = tabs[tabs.length - 1];
      return lastTab;
    }

    return null;
  }

  // 创建开始按钮
  function createStartButton() {
    const startBtn = document.createElement('button');
    startBtn.id = 'douyin-start-btn';
    startBtn.textContent = '开始';
    startBtn.style.cssText = `
            margin-top: 10px;
            margin-left: 15px;
            padding: 8px 16px;
            background: #ff6b35;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);
            position: relative;
            top: 0;
        `;

    // 鼠标悬停效果
    startBtn.onmouseover = () => {
      startBtn.style.background = '#e55a2b';
      startBtn.style.transform = 'translateY(-1px)';
      startBtn.style.boxShadow = '0 4px 8px rgba(255, 107, 53, 0.4)';
    };

    startBtn.onmouseout = () => {
      startBtn.style.background = '#ff6b35';
      startBtn.style.transform = 'translateY(0)';
      startBtn.style.boxShadow = '0 2px 4px rgba(255, 107, 53, 0.3)';
    };

    // 绑定点击事件
    startBtn.onclick = () => {
      clickSubmissionListButton();
    };

    return startBtn;
  }



  // 点击投稿列表按钮
  function clickSubmissionListButton() {
    try {
      updateStatusIndicator('� 正在启动Modal...');

      // 直接弹出modal并调用API
      showItemListModal();
    } catch (error) {
      console.error('❌ 启动Modal失败:', error);
      updateStatusIndicator('❌ 操作失败，请查看控制台了解详情');

      // 5秒后移除状态指示器
      setTimeout(() => {
        removeStatusIndicator();
      }, 5000);
    }
  }

  // 插入开始按钮
  function insertStartButton() {
    if (buttonInserted) {
      return true;
    }

    const lastTab = findLastTabElement();

    if (!lastTab) {
      return false;
    }

    try {
      const startBtn = createStartButton();

      // 在最后一个标签页元素后面插入按钮
      lastTab.parentElement.insertBefore(startBtn, lastTab.nextSibling);

      buttonInserted = true;

      // 更新状态指示器
      updateStatusIndicator('✅ 开始按钮已就绪！');
      setTimeout(() => {
        updateStatusIndicator('🎯 点击"开始"按钮执行操作');
        setTimeout(removeStatusIndicator, 3000);
      }, 1000);

      // 停止DOM观察器
      if (observer) {
        observer.disconnect();
        observer = null;
      }

      return true;
    } catch (error) {
      console.error('❌ 插入按钮失败:', error);
      updateStatusIndicator('❌ 按钮插入失败，请刷新页面重试');
      return false;
    }
  }

  // 监听DOM变化并插入按钮（延迟6秒启动）
  function observeDOM() {
    // 显示倒计时状态指示器
    let countdown = 6;
    updateStatusIndicator(`🎬 抖音按钮挂载器启动中... ${countdown}秒后挂载按钮`);

    // 倒计时显示
    const countdownInterval = setInterval(() => {
      countdown--;
      if (countdown > 0) {
        updateStatusIndicator(`🎬 抖音按钮挂载器启动中... ${countdown}秒后挂载按钮`);
      } else {
        clearInterval(countdownInterval);
        updateStatusIndicator('🚀 正在挂载开始按钮...');
      }
    }, 1000);

    // 延迟6秒后开始挂载按钮
    setTimeout(() => {

      // 先尝试直接插入
      const success = insertStartButton();

      // 如果直接插入失败，创建MutationObserver监听DOM变化
      if (!success) {
        updateStatusIndicator('👀 等待标签页元素出现...');

        observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (mutation.type === 'childList' && !buttonInserted) {
              insertStartButton();
            }
          });
        });

        // 开始观察
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      }
    }, 6000); // 6秒延迟
  }

  // 初始化
  function init() {
    // 立即开始10秒倒计时
    observeDOM();
  }

  // 清理函数
  function cleanup() {
    if (observer) {
      observer.disconnect();
      observer = null;
    }
    removeStatusIndicator();
  }

  // 页面卸载时清理
  window.addEventListener('beforeunload', cleanup);

  // ===== Modal和API相关功能 =====

  // 全局变量
  let currentModal = null;
  let allItems = [];
  let filteredItems = []; // 搜索过滤后的数据
  let selectedItems = new Set();
  let hasMoreData = false;
  let currentCursor = null;

  // 获取东八区时间范围（最近90天）
  function getTimeRange() {
    // 获取当前UTC时间
    const now = new Date();

    // 计算东八区时间（UTC+8）
    const chinaOffset = 8 * 60 * 60 * 1000; // 8小时的毫秒数
    const utcTime = now.getTime() + (now.getTimezoneOffset() * 60 * 1000); // 转换为UTC时间
    const chinaTime = new Date(utcTime + chinaOffset); // 转换为东八区时间

    // 设置为当天的23:59:59作为结束时间
    const endTime = new Date(chinaTime);
    endTime.setHours(23, 59, 59, 0);

    // 计算90天前的开始时间，设置为00:00:00
    const startTime = new Date(endTime.getTime() - (90 * 24 * 60 * 60 * 1000));
    startTime.setHours(0, 0, 0, 0);

    // 计算最终的时间戳（确保以000结尾）
    const finalStartTime = Math.floor(startTime.getTime() / 1000) * 1000;
    const finalEndTime = Math.floor(endTime.getTime() / 1000) * 1000;



    return {
      startTime: finalStartTime,
      endTime: finalEndTime
    };
  }

  // 构建API URL
  function buildApiUrl(cursor = null) {
    const { startTime, endTime } = getTimeRange();
    let url = `https://creator.douyin.com/web/api/creator/item/list?count=10&order_by=1&fields=metrics,review,visibility&need_cooperation=true&start_time=${startTime}&end_time=${endTime}`;

    if (cursor) {
      url += `&max_cursor=${cursor}`;
    }

    return url;
  }

  // 调用API获取数据
  async function fetchItemList(cursor = null) {
    try {
      const apiUrl = buildApiUrl(cursor);
      updateStatusIndicator('🔄 正在获取数据...');

      const response = await fetch(apiUrl, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Agw-Js-Conv': 'str'
        }
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('❌ API调用失败:', error);
      updateStatusIndicator('❌ API调用失败，请查看控制台');
      throw error;
    }
  }

  // 创建Modal弹窗
  function createModal() {
    // 如果已存在modal，先移除
    if (currentModal) {
      currentModal.remove();
    }

    // 创建modal容器
    const modal = document.createElement('div');
    modal.id = 'douyin-item-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 10001;
      display: flex;
      justify-content: center;
      align-items: center;
    `;

    // 创建modal内容
    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
      background: white;
      border-radius: 8px;
      width: 50vw;
      min-width: 800px;
      max-height: 80%;
      display: flex;
      flex-direction: column;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    `;

    // 创建标题栏
    const header = document.createElement('div');
    header.style.cssText = `
      padding: 20px;
      border-bottom: 1px solid #eee;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 8px 8px 0 0;
    `;

    // 创建顶部标题行
    const titleRow = document.createElement('div');
    titleRow.style.cssText = `
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    `;

    const title = document.createElement('h3');
    title.textContent = '投稿列表数据';
    title.style.cssText = `
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    `;

    const closeBtn = document.createElement('button');
    closeBtn.textContent = '×';
    closeBtn.style.cssText = `
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: white;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0.8;
      transition: opacity 0.2s;
    `;
    closeBtn.onmouseenter = () => closeBtn.style.opacity = '1';
    closeBtn.onmouseleave = () => closeBtn.style.opacity = '0.8';
    closeBtn.onclick = closeModal;

    titleRow.appendChild(title);
    titleRow.appendChild(closeBtn);

    // 创建搜索框容器
    const searchContainer = document.createElement('div');
    searchContainer.style.cssText = `
      display: flex;
      align-items: center;
      gap: 10px;
    `;

    // 创建搜索框
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.id = 'search-input';
    searchInput.placeholder = '搜索ID或描述内容...';
    searchInput.style.cssText = `
      flex: 1;
      padding: 8px 12px;
      border: none;
      border-radius: 20px;
      font-size: 14px;
      background: rgba(255, 255, 255, 0.9);
      color: #333;
      outline: none;
      transition: all 0.2s;
    `;
    searchInput.onfocus = () => {
      searchInput.style.background = 'white';
      searchInput.style.boxShadow = '0 0 0 2px rgba(255, 255, 255, 0.3)';
    };
    searchInput.onblur = () => {
      searchInput.style.background = 'rgba(255, 255, 255, 0.9)';
      searchInput.style.boxShadow = 'none';
    };

    // 创建清除按钮
    const clearBtn = document.createElement('button');
    clearBtn.textContent = '清除';
    clearBtn.style.cssText = `
      padding: 8px 16px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 15px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s;
    `;
    clearBtn.onmouseenter = () => {
      clearBtn.style.background = 'rgba(255, 255, 255, 0.3)';
    };
    clearBtn.onmouseleave = () => {
      clearBtn.style.background = 'rgba(255, 255, 255, 0.2)';
    };

    searchContainer.appendChild(searchInput);
    searchContainer.appendChild(clearBtn);

    header.appendChild(titleRow);
    header.appendChild(searchContainer);

    // 添加搜索事件监听器
    let searchTimeout;
    searchInput.oninput = (e) => {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        filterItems(e.target.value);
      }, 300); // 300ms防抖
    };

    // 清除按钮事件
    clearBtn.onclick = () => {
      searchInput.value = '';
      filterItems('');
      searchInput.focus();
    };

    // 创建内容区域
    const content = document.createElement('div');
    content.id = 'modal-content';
    content.style.cssText = `
      flex: 1;
      overflow-y: auto;
      padding: 20px;
    `;

    // 创建底部按钮区域
    const footer = document.createElement('div');
    footer.style.cssText = `
      padding: 20px;
      border-top: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
    `;

    const loadMoreBtn = document.createElement('button');
    loadMoreBtn.id = 'load-more-btn';
    loadMoreBtn.textContent = '手动加载更多';
    loadMoreBtn.style.cssText = `
      padding: 8px 16px;
      background: #52c41a;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      display: none;
    `;
    loadMoreBtn.onclick = loadMoreItems;

    const confirmBtn = document.createElement('button');
    confirmBtn.textContent = '确认选择';
    confirmBtn.style.cssText = `
      padding: 8px 16px;
      background: #52c41a;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    `;
    confirmBtn.onclick = confirmSelection;

    footer.appendChild(loadMoreBtn);
    footer.appendChild(confirmBtn);

    // 组装modal
    modalContent.appendChild(header);
    modalContent.appendChild(content);
    modalContent.appendChild(footer);
    modal.appendChild(modalContent);

    // 点击背景关闭modal
    modal.onclick = (e) => {
      if (e.target === modal) {
        closeModal();
      }
    };

    document.body.appendChild(modal);
    currentModal = modal;

    return modal;
  }

  // 渲染items列表
  function renderItems(items, append = false) {
    const content = document.getElementById('modal-content');
    if (!content) return;

    if (!append) {
      content.innerHTML = '';
    }

    if (!items || items.length === 0) {
      if (!append) {
        content.innerHTML = '<p style="text-align: center; color: #999; padding: 40px;">暂无数据</p>';
      }
      return;
    }

    items.forEach(item => {
      const itemDiv = document.createElement('div');
      itemDiv.style.cssText = `
        border: 1px solid #eee;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
      `;

      // 添加悬停效果
      itemDiv.onmouseenter = () => {
        itemDiv.style.transform = 'translateY(-2px)';
        itemDiv.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.15)';
      };
      itemDiv.onmouseleave = () => {
        itemDiv.style.transform = 'translateY(0)';
        itemDiv.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
      };

      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      // 确保ID作为字符串处理，避免大数精度丢失
      const itemIdStr = String(item.id);
      checkbox.id = `item-${itemIdStr}`;
      checkbox.value = itemIdStr;
      checkbox.style.cssText = `
        margin-right: 16px;
        transform: scale(1.3);
        cursor: pointer;
      `;

      // 检查是否已选中 - 使用字符串类型的ID
      checkbox.checked = selectedItems.has(itemIdStr);

      checkbox.onchange = (e) => {
        if (e.target.checked) {
          selectedItems.add(itemIdStr);
        } else {
          selectedItems.delete(itemIdStr);
        }
      };

      const contentContainer = document.createElement('div');
      contentContainer.style.cssText = `
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
      `;

      const label = document.createElement('label');
      label.htmlFor = `item-${itemIdStr}`;
      label.style.cssText = `
        flex: 1;
        cursor: pointer;
        font-size: 14px;
        line-height: 1.5;
        margin-right: 16px;
      `;

      const idSpan = document.createElement('span');
      // 使用字符串形式显示ID，确保完整精度
      idSpan.textContent = `ID: ${itemIdStr}`;
      idSpan.style.cssText = `
        font-weight: bold;
        color: #1890ff;
        display: block;
        margin-bottom: 6px;
        font-size: 15px;
      `;

      const descSpan = document.createElement('span');
      // 截断描述文本，最多显示50个字符
      const description = item.description || '无描述';
      const truncatedDesc = description.length > 50 ? description.substring(0, 50) + '...' : description;
      descSpan.textContent = truncatedDesc;
      descSpan.style.cssText = `
        color: #666;
        display: block;
        font-size: 13px;
      `;

      label.appendChild(idSpan);
      label.appendChild(descSpan);

      // 创建数据标签容器
      const metricsContainer = document.createElement('div');
      metricsContainer.style.cssText = `
        display: flex;
        gap: 8px;
        align-items: flex-end;
        min-width: 120px;
      `;

      // 获取播放量和点赞量数据
      const metrics = item.metrics || {};
      const viewCount = metrics.view_count || 0;
      const likeCount = metrics.like_count || 0;

      // 格式化数字显示
      const formatNumber = (num) => {
        if (num >= 10000) {
          return (num / 10000).toFixed(1) + 'w';
        } else if (num >= 1000) {
          return (num / 1000).toFixed(1) + 'k';
        }
        return num.toString();
      };

      // 创建播放量标签
      const playLabel = document.createElement('div');
      playLabel.style.cssText = `
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3);
        min-width: 80px;
        justify-content: center;
      `;
      playLabel.innerHTML = `
        <span style="margin-right: 4px;">▶</span>
        播放 ${formatNumber(viewCount)}
      `;

      // 创建点赞量标签
      const likeLabel = document.createElement('div');
      likeLabel.style.cssText = `
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        box-shadow: 0 2px 6px rgba(78, 205, 196, 0.3);
        min-width: 80px;
        justify-content: center;
      `;
      likeLabel.innerHTML = `
        <span style="margin-right: 4px;">👍</span>
        点赞 ${formatNumber(likeCount)}
      `;

      metricsContainer.appendChild(playLabel);
      metricsContainer.appendChild(likeLabel);

      contentContainer.appendChild(label);
      contentContainer.appendChild(metricsContainer);

      itemDiv.appendChild(checkbox);
      itemDiv.appendChild(contentContainer);

      content.appendChild(itemDiv);
    });

    // 更新手动加载按钮状态（默认隐藏，仅在自动加载失败时显示）
    const loadMoreBtn = document.getElementById('load-more-btn');
    if (loadMoreBtn) {
      // 默认隐藏按钮，因为使用自动加载模式
      loadMoreBtn.style.display = 'none';
      loadMoreBtn.disabled = false;
      loadMoreBtn.textContent = '手动加载更多';
    }
  }

  // 搜索过滤函数
  function filterItems(searchTerm) {
    if (!searchTerm.trim()) {
      filteredItems = [...allItems];
    } else {
      const term = searchTerm.toLowerCase();
      filteredItems = allItems.filter(item => {
        const itemIdStr = String(item.id);
        const description = item.description || '';

        return itemIdStr.includes(term) ||
          description.toLowerCase().includes(term);
      });
    }

    // 重新渲染过滤后的数据
    renderItems(filteredItems, false);

    // 更新搜索结果提示
    updateSearchResultsInfo(searchTerm, filteredItems.length, allItems.length);
  }

  // 更新搜索结果信息
  function updateSearchResultsInfo(searchTerm, filteredCount, totalCount) {
    let indicator = document.getElementById('search-results-indicator');

    if (!indicator) {
      // 创建搜索结果提示
      indicator = document.createElement('div');
      indicator.id = 'search-results-indicator';
      indicator.style.cssText = `
        padding: 8px 20px;
        background: #f0f8ff;
        border-bottom: 1px solid #e1e8ed;
        font-size: 13px;
        color: #666;
        text-align: center;
      `;

      const content = document.getElementById('modal-content');
      if (content && content.parentNode) {
        content.parentNode.insertBefore(indicator, content);
      }
    }

    if (searchTerm.trim()) {
      indicator.textContent = `🔍 搜索"${searchTerm}"：找到 ${filteredCount} 项，共 ${totalCount} 项`;
      indicator.style.display = 'block';
    } else {
      indicator.style.display = 'none';
    }
  }

  // 关闭Modal
  function closeModal() {
    if (currentModal) {
      currentModal.remove();
      currentModal = null;
    }
    // 重置状态
    allItems = [];
    filteredItems = [];
    selectedItems.clear();
    hasMoreData = false;
    currentCursor = null;
  }

  // 创建Modal内的加载状态提示
  function createModalLoadingIndicator() {
    const content = document.getElementById('modal-content');
    if (!content) return null;

    // 移除已存在的加载提示
    const existingIndicator = document.getElementById('modal-loading-indicator');
    if (existingIndicator) {
      existingIndicator.remove();
    }

    const indicator = document.createElement('div');
    indicator.id = 'modal-loading-indicator';
    indicator.style.cssText = `
      position: sticky;
      top: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 12px 20px;
      margin: -20px -20px 20px -20px;
      font-size: 14px;
      font-weight: 500;
      text-align: center;
      border-radius: 8px 8px 0 0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      animation: pulse 2s infinite;
    `;

    // 添加脉冲动画
    const style = document.createElement('style');
    style.textContent = `
      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
      }
    `;
    document.head.appendChild(style);

    content.insertBefore(indicator, content.firstChild);
    return indicator;
  }

  // 更新Modal内的加载状态
  function updateModalLoadingIndicator(message, isComplete = false) {
    let indicator = document.getElementById('modal-loading-indicator');
    if (!indicator) {
      indicator = createModalLoadingIndicator();
    }

    if (indicator) {
      indicator.textContent = message;

      if (isComplete) {
        indicator.style.background = 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)';
        indicator.style.animation = 'none';

        // 完成后10秒移除提示
        setTimeout(() => {
          if (indicator && indicator.parentNode) {
            indicator.remove();
          }
        }, 10000);
      }
    }
  }

  // 自动加载所有数据
  async function autoLoadAllItems() {
    try {
      let pageCount = 1;
      let totalItems = allItems.length;

      // 同时更新页面状态指示器和Modal内提示
      updateStatusIndicator(`🔄 自动加载第 ${pageCount} 页数据...`);
      updateModalLoadingIndicator(`🔄 正在自动加载第 ${pageCount} 页数据...`);

      while (hasMoreData) {
        const data = await fetchItemList(currentCursor);

        if (data && data.items) {
          allItems = allItems.concat(data.items);
          hasMoreData = data.has_more || false;
          currentCursor = data.max_cursor || null;
          totalItems += data.items.length;
          pageCount++;

          // 更新过滤数据
          filteredItems = [...allItems];

          // 渲染新数据
          renderItems(data.items, true); // append模式

          // 更新状态指示器
          if (hasMoreData) {
            const statusMsg = `🔄 自动加载第 ${pageCount} 页数据... (已加载 ${totalItems} 项)`;
            updateStatusIndicator(statusMsg);
            updateModalLoadingIndicator(statusMsg);
            // 添加延迟避免请求过于频繁
            await new Promise(resolve => setTimeout(resolve, 800));
          } else {
            const completeMsg = `✅ 自动加载完成！共加载 ${totalItems} 项数据`;
            updateStatusIndicator(completeMsg);
            updateModalLoadingIndicator(completeMsg, true);

            // 延长状态指示器显示时间
            setTimeout(() => {
              removeStatusIndicator();
            }, 8000);
          }
        } else {
          break; // 如果没有数据则退出循环
        }
      }

      // 隐藏加载更多按钮（因为已经全部加载完成）
      const loadMoreBtn = document.getElementById('load-more-btn');
      if (loadMoreBtn) {
        loadMoreBtn.style.display = 'none';
      }

    } catch (error) {
      console.error('❌ 自动加载数据失败:', error);
      updateStatusIndicator('❌ 自动加载失败，请查看控制台');
      updateModalLoadingIndicator('❌ 自动加载失败，请尝试手动加载', true);

      // 显示手动加载按钮作为备选方案
      const loadMoreBtn = document.getElementById('load-more-btn');
      if (loadMoreBtn) {
        loadMoreBtn.style.display = hasMoreData ? 'block' : 'none';
        loadMoreBtn.disabled = false;
        loadMoreBtn.textContent = '手动加载更多';
      }

      setTimeout(() => {
        removeStatusIndicator();
      }, 8000);
    }
  }

  // 手动加载更多数据（保留作为备选方案）
  async function loadMoreItems() {
    try {
      const loadMoreBtn = document.getElementById('load-more-btn');
      if (loadMoreBtn) {
        loadMoreBtn.disabled = true;
        loadMoreBtn.textContent = '加载中...';
      }

      const data = await fetchItemList(currentCursor);

      if (data && data.items) {
        allItems = allItems.concat(data.items);
        hasMoreData = data.has_more || false;
        currentCursor = data.max_cursor || null;

        // 更新过滤数据
        filteredItems = [...allItems];

        renderItems(data.items, true); // append模式
      }
    } catch (error) {
      console.error('❌ 加载更多数据失败:', error);
      const loadMoreBtn = document.getElementById('load-more-btn');
      if (loadMoreBtn) {
        loadMoreBtn.disabled = false;
        loadMoreBtn.textContent = '加载失败，重试';
      }
    }
  }

  // 显示投稿列表Modal并调用API（自动加载所有数据）
  async function showItemListModal() {
    try {
      updateStatusIndicator('🔄 正在创建Modal...');

      // 创建Modal
      createModal();

      // 调用API获取第一页数据
      const data = await fetchItemList();

      if (data && data.items) {
        allItems = data.items;
        hasMoreData = data.has_more || false;
        currentCursor = data.max_cursor || null;

        // 初始化过滤数据
        filteredItems = [...allItems];

        // 渲染第一页数据到Modal
        renderItems(data.items);

        updateStatusIndicator('✅ 第一页数据加载完成！');

        // 如果还有更多数据，自动加载所有剩余数据
        if (hasMoreData) {
          updateModalLoadingIndicator(`📋 第一页加载完成，共 ${data.items.length} 项，准备自动加载剩余数据...`);
          // 延迟1秒后开始自动加载，让用户看到第一页数据
          setTimeout(() => {
            autoLoadAllItems();
          }, 1000);
        } else {
          // 如果没有更多数据，显示完成状态
          updateModalLoadingIndicator(`✅ 数据加载完成！共 ${data.items.length} 项数据`, true);
          setTimeout(() => {
            removeStatusIndicator();
          }, 5000);
        }
      } else {
        updateStatusIndicator('⚠️ 暂无数据');

        // 初始化空的过滤数据
        filteredItems = [];

        // 仍然显示Modal，但显示空状态
        renderItems([]);

        setTimeout(() => {
          removeStatusIndicator();
        }, 3000);
      }
    } catch (error) {
      console.error('❌ 显示Modal失败:', error);
      updateStatusIndicator('❌ 加载数据失败，请查看控制台');

      // 即使出错也要显示Modal，让用户知道发生了什么
      if (!currentModal) {
        createModal();
        renderItems([]);
      }

      setTimeout(() => {
        removeStatusIndicator();
      }, 5000);
    }
  }

  // 封装抖音数据分析API调用
  async function fetchItemAnalytics(itemId) {
    // 确保itemId为字符串类型，避免大数精度丢失
    const itemIdStr = String(itemId);

    try {
      // 验证item_id格式（应该是数字字符串）
      if (!itemIdStr || !/^\d+$/.test(itemIdStr)) {
        throw new Error(`无效的item_id格式: ${itemIdStr}`);
      }

      const apiUrl = `https://creator.douyin.com/janus/douyin/creator/data/item_analysis/metrics_trend?aid=2906&app_name=aweme_creator_platform&device_platform=web&browser_online=true&timezone_name=Asia%2FShanghai&item_id=${itemIdStr}&trend_type=2&time_unit=2&metrics_group=0&metrics=view_count,cover_click_rate,like_count`;

      const response = await fetch(apiUrl, {
        referrer: `https://creator.douyin.com/creator-micro/work-management/work-detail/${itemIdStr}?enter_from=item_data`,
        body: null,
        method: "GET",
        mode: "cors",
        credentials: "include",
        headers: {
          'Accept': 'application/json',
          'Agw-Js-Conv': 'str'  // 关键头部：确保大数字ID以字符串形式返回
        }
      });

      if (!response.ok) {
        throw new Error(`数据分析API请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      return {
        itemId: itemIdStr,
        success: true,
        data: data
      };
    } catch (error) {
      console.error(`❌ 获取item ${itemIdStr} 数据分析失败:`, error);
      return {
        itemId: itemIdStr,
        success: false,
        error: error.message
      };
    }
  }

  // 封装抖音实时分析API调用（支持留存分析和点赞分析）
  async function fetchItemRealtimeAnalytics(itemId, analysisType = 1) {
    // 确保itemId为字符串类型，避免大数精度丢失
    const itemIdStr = String(itemId);
    const analysisTypeName = analysisType === 1 ? '留存分析' : '点赞分析';

    try {
      // 验证item_id格式（应该是数字字符串）
      if (!itemIdStr || !/^\d+$/.test(itemIdStr)) {
        throw new Error(`无效的item_id格式: ${itemIdStr}`);
      }

      // 验证analysis_type参数
      if (analysisType !== 1 && analysisType !== 2) {
        throw new Error(`无效的analysis_type: ${analysisType}，只支持1(留存分析)或2(点赞分析)`);
      }

      const apiUrl = `https://creator.douyin.com/janus/douyin/creator/data/realtime/analysis/data_center?aid=2906&app_name=aweme_creator_platform&device_platform=web&cookie_enabled=true&timezone_name=Asia%2FShanghai&user_id=&item_id=${itemIdStr}&analysis_type=${analysisType}&metrics_group=0`;

      const response = await fetch(apiUrl, {
        method: "GET",
        mode: "cors",
        credentials: "include",
        headers: {
          'Accept': 'application/json',
          'Agw-Js-Conv': 'str'  // 关键头部：确保大数字ID以字符串形式返回
        }
      });

      if (!response.ok) {
        throw new Error(`${analysisTypeName}API请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      return {
        itemId: itemIdStr,
        success: true,
        data: data,
        analysisType: analysisType
      };
    } catch (error) {
      console.error(`❌ 获取item ${itemIdStr} ${analysisTypeName}失败:`, error);
      return {
        itemId: itemIdStr,
        success: false,
        error: error.message,
        analysisType: analysisType
      };
    }
  }



  // 封装抖音播放进度分析API调用
  async function fetchItemProgressAnalytics(itemId) {
    // 确保itemId为字符串类型，避免大数精度丢失
    const itemIdStr = String(itemId);

    try {
      // 验证item_id格式（应该是数字字符串）
      if (!itemIdStr || !/^\d+$/.test(itemIdStr)) {
        throw new Error(`无效的item_id格式: ${itemIdStr}`);
      }

      const apiUrl = `https://creator.douyin.com/janus/douyin/creator/bff/data/progress/analysis/v2?aid=2906&app_name=aweme_creator_platform&device_platform=web&cookie_enabled=true&timezone_name=Asia%2FShanghai&item_id=${itemIdStr}`;

      const response = await fetch(apiUrl, {
        method: "GET",
        mode: "cors",
        credentials: "include",
        headers: {
          'Accept': 'application/json',
          'Agw-Js-Conv': 'str'  // 关键头部：确保大数字ID以字符串形式返回
        }
      });

      if (!response.ok) {
        throw new Error(`播放进度分析API请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      return {
        itemId: itemIdStr,
        success: true,
        data: data
      };
    } catch (error) {
      console.error(`❌ 获取item ${itemIdStr} 播放进度分析失败:`, error);
      return {
        itemId: itemIdStr,
        success: false,
        error: error.message
      };
    }
  }

  // 根据itemId获取item描述
  function getItemDescription(itemId) {
    const item = allItems.find(item => String(item.id) === String(itemId));
    return item ? item.description || '无描述' : '未知项目';
  }

  // 清理文件名中的特殊字符
  function sanitizeFileName(fileName) {
    // 移除或替换文件名中不允许的字符
    return fileName
      .replace(/[<>:"/\\|?*]/g, '_') // 替换特殊字符为下划线
      .replace(/\s+/g, '_') // 替换空格为下划线
      .replace(/_{2,}/g, '_') // 多个下划线合并为一个
      .trim();
  }

  // 下载趋势数据为CSV格式（合并版本）
  function downloadTrendDataAsCSV(results) {
    // 修正过滤条件：检查直接的trend_map和嵌套的data.trend_map
    const successResults = results.filter(r => {
      if (!r.success || !r.data) return false;

      // 检查直接的trend_map
      if (r.data.trend_map) return true;

      // 检查嵌套的data.trend_map
      if (r.data.data && r.data.data.trend_map) return true;

      return false;
    });

    if (successResults.length === 0) {
      return;
    }

    successResults.forEach(result => {
      const { itemId, data } = result;
      // 灵活获取trendMap：优先使用直接的trend_map，否则使用嵌套的
      const trendMap = data.trend_map || (data.data && data.data.trend_map);

      if (!trendMap) {
        return;
      }

      // 获取item描述并截取前20字符
      const itemDesc = getItemDescription(itemId);
      const descPrefix = itemDesc.substring(0, 20);

      // 检查是否有任何趋势数据
      const hasViewCount = trendMap.view_count && trendMap.view_count[0];
      const hasCoverClickRate = trendMap.cover_click_rate && trendMap.cover_click_rate[0];
      const hasLikeCount = trendMap.like_count && trendMap.like_count[0];

      if (!hasViewCount && !hasCoverClickRate && !hasLikeCount) {
        console.warn(`❌ 项目 ${itemId} 没有可用的趋势数据`);
        return;
      }

      // 创建时间点映射，合并所有指标的数据
      const timeDataMap = new Map();

      // 收集播放量数据
      if (hasViewCount) {
        trendMap.view_count[0].forEach(dataPoint => {
          const dateTime = dataPoint.date_time || '';
          if (dateTime) {
            if (!timeDataMap.has(dateTime)) {
              timeDataMap.set(dateTime, {});
            }
            const rawValue = dataPoint.value || 0;
            // 播放量格式化为整数
            const value = (typeof rawValue === 'number') ?
              Math.round(rawValue).toString() : (rawValue || '0');
            timeDataMap.get(dateTime).view_count = value;
          }
        });
      }

      // 收集封面点击率数据
      if (hasCoverClickRate) {
        trendMap.cover_click_rate[0].forEach(dataPoint => {
          const dateTime = dataPoint.date_time || '';
          if (dateTime) {
            if (!timeDataMap.has(dateTime)) {
              timeDataMap.set(dateTime, {});
            }
            const rawValue = dataPoint.value || 0;
            // 封面点击率格式化为百分比
            const value = (typeof rawValue === 'number') ?
              `${(rawValue * 100).toFixed(2)}%` : (rawValue || '0.00%');
            timeDataMap.get(dateTime).cover_click_rate = value;
          }
        });
      }

      // 收集点赞量数据
      if (hasLikeCount) {
        trendMap.like_count[0].forEach(dataPoint => {
          const dateTime = dataPoint.date_time || '';
          if (dateTime) {
            if (!timeDataMap.has(dateTime)) {
              timeDataMap.set(dateTime, {});
            }
            const rawValue = dataPoint.value || 0;
            // 点赞量格式化为整数
            const value = (typeof rawValue === 'number') ?
              Math.round(rawValue).toString() : (rawValue || '0');
            timeDataMap.get(dateTime).like_count = value;
          }
        });
      }

      // 生成合并的CSV内容
      let csvContent = '时间,播放量,封面点击率,点赞量\n';

      // 按时间排序并生成数据行
      const sortedTimes = Array.from(timeDataMap.keys()).sort();
      sortedTimes.forEach(dateTime => {
        const timeData = timeDataMap.get(dateTime);
        const viewCount = timeData.view_count || '';
        const coverClickRate = timeData.cover_click_rate || '';
        const likeCount = timeData.like_count || '';
        csvContent += `${dateTime},${viewCount},${coverClickRate},${likeCount}\n`;
      });

      // 生成文件名
      const sanitizedDesc = sanitizeFileName(descPrefix);
      const fileName = `${sanitizedDesc}_综合趋势数据.csv`;

      // 创建下载
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log(`✅ 已生成合并趋势数据CSV: ${fileName}`);
    });
  }

  // 下载实时分析数据为CSV格式（支持留存分析和点赞分析）
  function downloadRealtimeAnalysisDataAsCSV(results, analysisType = 1) {
    const analysisTypeName = analysisType === 1 ? '留存分析' : '点赞分析';

    // 过滤出成功的结果，并且匹配指定的分析类型
    const successResults = results.filter(result =>
      result.success &&
      result.data &&
      (result.analysisType === analysisType || !result.analysisType) // 兼容旧数据
    );

    if (successResults.length === 0) {
      return;
    }

    successResults.forEach(result => {
      const { itemId, data } = result;
      const analysisTrend = data.analysis_trend;

      if (!analysisTrend || !analysisTrend.current_item || !analysisTrend.similar_author) {
        return;
      }

      // 获取item描述并截取前20字符
      const itemDesc = getItemDescription(itemId);
      const descPrefix = itemDesc.substring(0, 20);

      // 根据分析类型生成不同的CSV表头
      let csvContent;
      if (analysisType === 1) {
        csvContent = '时间,留存率,同类作者留存率\n';
      } else if (analysisType === 2) {
        csvContent = '时间,点赞率,同类作者点赞率\n';
      } else {
        csvContent = '时间,数值,同类作者数值\n';
      }

      const currentItemData = analysisTrend.current_item;
      const similarAuthorData = analysisTrend.similar_author;

      // 创建时间点映射
      const timeMap = new Map();

      // 收集当前项目的数据
      currentItemData.forEach(point => {
        timeMap.set(point.key, { current: point.value, similar: null });
      });

      // 收集同类作者的数据
      similarAuthorData.forEach(point => {
        if (timeMap.has(point.key)) {
          timeMap.get(point.key).similar = point.value;
        } else {
          timeMap.set(point.key, { current: null, similar: point.value });
        }
      });

      // 按时间排序并生成CSV行
      const sortedTimes = Array.from(timeMap.keys()).sort();
      sortedTimes.forEach(time => {
        const data = timeMap.get(time);
        const currentRate = data.current !== null ? `${(data.current * 100).toFixed(2)}%` : '';
        const similarRate = data.similar !== null ? `${(data.similar * 100).toFixed(2)}%` : '';
        csvContent += `${time},${currentRate},${similarRate}\n`;
      });

      // 生成文件名
      const sanitizedDesc = sanitizeFileName(descPrefix);
      const fileName = `${sanitizedDesc}_${analysisTypeName}.csv`;

      // 创建下载
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    });
  }

  // 保持向后兼容的留存分析下载函数
  function downloadRetentionDataAsCSV(results) {
    return downloadRealtimeAnalysisDataAsCSV(results, 1);
  }

  // 新增点赞分析下载函数
  function downloadLikeAnalysisDataAsCSV(results) {
    return downloadRealtimeAnalysisDataAsCSV(results, 2);
  }

  // 新增合并实时分析数据下载函数（留存分析+点赞分析）
  function downloadCombinedRealtimeAnalysisDataAsCSV(retentionResults, likeResults) {
    console.log('📥 开始生成合并的留存点赞分析CSV下载文件...');
    console.log('🔍 调试：留存分析结果数量:', retentionResults.length);
    console.log('🔍 调试：点赞分析结果数量:', likeResults.length);

    // 过滤出成功的留存分析结果
    const successRetentionResults = retentionResults.filter(result =>
      result.success &&
      result.data &&
      result.data.analysis_trend &&
      result.data.analysis_trend.current_item &&
      result.data.analysis_trend.similar_author
    );

    // 过滤出成功的点赞分析结果
    const successLikeResults = likeResults.filter(result =>
      result.success &&
      result.data &&
      result.data.analysis_trend &&
      result.data.analysis_trend.current_item &&
      result.data.analysis_trend.similar_author
    );

    console.log('🔍 调试：成功的留存分析结果数量:', successRetentionResults.length);
    console.log('🔍 调试：成功的点赞分析结果数量:', successLikeResults.length);

    if (successRetentionResults.length === 0 && successLikeResults.length === 0) {
      console.log('⚠️ 没有有效的留存或点赞分析数据，跳过合并下载');
      return;
    }

    // 创建itemId到数据的映射
    const retentionDataMap = new Map();
    const likeDataMap = new Map();

    // 处理留存分析数据
    successRetentionResults.forEach(result => {
      const { itemId, data } = result;
      const analysisTrend = data.analysis_trend;

      // 创建时间点映射
      const timeMap = new Map();

      // 收集当前项目的留存数据
      analysisTrend.current_item.forEach(point => {
        timeMap.set(point.key, { retention: point.value, retentionSimilar: null });
      });

      // 收集同类作者的留存数据
      analysisTrend.similar_author.forEach(point => {
        if (timeMap.has(point.key)) {
          timeMap.get(point.key).retentionSimilar = point.value;
        }
      });

      retentionDataMap.set(itemId, timeMap);
    });

    // 处理点赞分析数据
    successLikeResults.forEach(result => {
      const { itemId, data } = result;
      const analysisTrend = data.analysis_trend;

      // 创建时间点映射
      const timeMap = new Map();

      // 收集当前项目的点赞数据
      analysisTrend.current_item.forEach(point => {
        timeMap.set(point.key, { like: point.value, likeSimilar: null });
      });

      // 收集同类作者的点赞数据
      analysisTrend.similar_author.forEach(point => {
        if (timeMap.has(point.key)) {
          timeMap.get(point.key).likeSimilar = point.value;
        }
      });

      likeDataMap.set(itemId, timeMap);
    });

    // 获取所有有数据的itemId
    const allItemIds = new Set([...retentionDataMap.keys(), ...likeDataMap.keys()]);

    // 为每个项目生成合并的CSV文件
    allItemIds.forEach(itemId => {
      const retentionData = retentionDataMap.get(itemId);
      const likeData = likeDataMap.get(itemId);

      // 获取item描述并截取前20字符
      const itemDesc = getItemDescription(itemId);
      const descPrefix = itemDesc.substring(0, 20);

      // CSV表头
      let csvContent = '时间,留存率,同类作者留存率,点赞率,同类作者点赞率\n';

      // 获取所有时间点
      const allTimePoints = new Set();
      if (retentionData) {
        retentionData.forEach((_, timePoint) => allTimePoints.add(timePoint));
      }
      if (likeData) {
        likeData.forEach((_, timePoint) => allTimePoints.add(timePoint));
      }

      // 按时间点排序
      const sortedTimePoints = Array.from(allTimePoints).sort();

      // 生成CSV数据行
      sortedTimePoints.forEach(timePoint => {
        const retentionInfo = retentionData ? retentionData.get(timePoint) : null;
        const likeInfo = likeData ? likeData.get(timePoint) : null;

        const retention = retentionInfo ? (retentionInfo.retention || 0) : 0;
        const retentionSimilar = retentionInfo ? (retentionInfo.retentionSimilar || 0) : 0;
        const like = likeInfo ? (likeInfo.like || 0) : 0;
        const likeSimilar = likeInfo ? (likeInfo.likeSimilar || 0) : 0;

        // 格式化数值（保留2位小数的百分比）
        const retentionPercent = (retention * 100).toFixed(2) + '%';
        const retentionSimilarPercent = (retentionSimilar * 100).toFixed(2) + '%';
        const likePercent = (like * 100).toFixed(2) + '%';
        const likeSimilarPercent = (likeSimilar * 100).toFixed(2) + '%';

        csvContent += `${timePoint},${retentionPercent},${retentionSimilarPercent},${likePercent},${likeSimilarPercent}\n`;
      });

      // 生成文件名
      const sanitizedDesc = sanitizeFileName(descPrefix);
      const fileName = `${sanitizedDesc}_留存点赞分析.csv`;

      console.log(`📥 正在生成合并CSV文件: ${fileName}`);

      // 创建下载
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log(`✅ 已下载合并分析文件: ${fileName}`);
    });

    console.log(`🎉 合并的留存点赞分析CSV文件下载完成！共处理 ${allItemIds.size} 个项目`);
  }

  // 下载播放进度分析数据为CSV格式
  function downloadProgressAnalysisDataAsCSV(results) {
    // 过滤出成功的结果，但放宽条件：只要success为true就尝试处理
    const successResults = results.filter(result => result.success);

    if (successResults.length === 0) {
      alert('⚠️ 播放进度分析：没有成功获取到数据，无法下载CSV文件');
      return;
    }

    let processedCount = 0; // 记录成功处理的项目数量
    let skippedCount = 0; // 记录跳过的项目数量
    let errorCount = 0; // 记录出错的项目数量

    successResults.forEach(result => {
      const { itemId, data } = result;

      // 检查是否有数据对象
      if (!data) {
        skippedCount++;

        try {
          // 即使没有数据也生成一个空的CSV文件，让用户知道这个项目被处理了
          const itemDesc = getItemDescription(itemId);
          const descPrefix = itemDesc.substring(0, 20);
          const sanitizedDesc = sanitizeFileName(descPrefix);
          const fileName = `${sanitizedDesc}_播放进度分析_无数据.csv`;

          const csvContent = '时间段,回看占比,跳过占比\n无数据,0%,0%\n';

          const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = fileName;
          link.style.display = 'none';

          document.body.appendChild(link);
          link.click();

          setTimeout(() => {
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          }, 3000);

          processedCount++;
        } catch (error) {
          console.error(`❌ 生成项目 ${itemId} 的空CSV文件时出错:`, error);
          errorCount++;
        }
        return;
      }

      // 检查是否有任何播放进度数据
      const hasBackward = data.jump_backward && Array.isArray(data.jump_backward) && data.jump_backward.length > 0;
      const hasForward = data.jump_forward && Array.isArray(data.jump_forward) && data.jump_forward.length > 0;

      if (!hasBackward && !hasForward) {
        try {
          // 生成一个表明无有效数据的CSV文件
          const itemDesc = getItemDescription(itemId);
          const descPrefix = itemDesc.substring(0, 20);
          const sanitizedDesc = sanitizeFileName(descPrefix);
          const fileName = `${sanitizedDesc}_播放进度分析_数据无效.csv`;

          const csvContent = '时间段,回看占比,跳过占比\n数据无效,0%,0%\n';

          const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = fileName;
          link.style.display = 'none';

          document.body.appendChild(link);
          link.click();

          setTimeout(() => {
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          }, 3000);

          processedCount++;
        } catch (error) {
          console.error(`❌ 生成项目 ${itemId} 的无效数据CSV文件时出错:`, error);
          errorCount++;
        }
        return;
      }

      try {
        // 获取item描述并截取前20字符
        const itemDesc = getItemDescription(itemId);
        const descPrefix = itemDesc.substring(0, 20);

        // 生成CSV表头
        let csvContent = '时间段,回看占比,跳过占比\n';

        // 创建时间点映射
        const timeMap = new Map();

        // 收集回看数据（如果存在）
        if (hasBackward) {
          data.jump_backward.forEach((point, index) => {
            try {
              const timeKey = parseInt(point.key);
              if (isNaN(timeKey)) {
                return;
              }
              timeMap.set(timeKey, { backward: point.value, forward: null });
            } catch (pointError) {
              console.error(`❌ 项目 ${itemId} 处理回看数据第${index}个点时出错:`, pointError, point);
            }
          });
        }

        // 收集跳过数据（如果存在）
        if (hasForward) {
          data.jump_forward.forEach((point, index) => {
            try {
              const timeKey = parseInt(point.key);
              if (isNaN(timeKey)) {
                return;
              }
              if (timeMap.has(timeKey)) {
                timeMap.get(timeKey).forward = point.value;
              } else {
                timeMap.set(timeKey, { backward: null, forward: point.value });
              }
            } catch (pointError) {
              console.error(`❌ 项目 ${itemId} 处理跳过数据第${index}个点时出错:`, pointError, point);
            }
          });
        }

        // 即使没有时间点数据也生成CSV文件，但添加说明
        if (timeMap.size === 0) {
          csvContent += '无有效数据,0%,0%\n';
        } else {

          // 按时间排序并生成CSV行
          const sortedTimes = Array.from(timeMap.keys()).sort((a, b) => a - b);
          sortedTimes.forEach(time => {
            try {
              const timeData = timeMap.get(time);
              // 生成时间段显示格式：key=5 -> "00:00 - 00:10", key=10 -> "00:10 - 00:20"
              // 根据需求，key=5对应0-10秒，key=10对应10-20秒，以此类推
              // 所以 startTime = (time - 5), endTime = (time + 5)
              const startTime = Math.max(0, time - 5); // 确保不为负数
              const endTime = time + 5;
              const startMinutes = Math.floor(startTime / 60).toString().padStart(2, '0');
              const startSeconds = (startTime % 60).toString().padStart(2, '0');
              const endMinutes = Math.floor(endTime / 60).toString().padStart(2, '0');
              const endSecondsValue = (endTime % 60).toString().padStart(2, '0');
              const timeRange = `${startMinutes}:${startSeconds} - ${endMinutes}:${endSecondsValue}`;

              // 将value转换为百分比格式，增加数据验证
              let backwardRate = '';
              let forwardRate = '';

              if (timeData.backward !== null && timeData.backward !== undefined) {
                const backwardValue = parseFloat(timeData.backward);
                if (!isNaN(backwardValue)) {
                  backwardRate = `${(backwardValue * 100).toFixed(2)}%`;
                }
              }

              if (timeData.forward !== null && timeData.forward !== undefined) {
                const forwardValue = parseFloat(timeData.forward);
                if (!isNaN(forwardValue)) {
                  forwardRate = `${(forwardValue * 100).toFixed(2)}%`;
                }
              }

              csvContent += `${timeRange},${backwardRate},${forwardRate}\n`;
            } catch (timeError) {
              console.error(`❌ 项目 ${itemId} 处理时间点 ${time} 时出错:`, timeError);
              // 即使单个时间点出错也继续处理其他时间点
            }
          });
        }

        // 生成文件名
        const sanitizedDesc = sanitizeFileName(descPrefix);
        const fileName = `${sanitizedDesc}_播放进度分析.csv`;

        // 创建下载
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();

        // 延迟清理，给下载更多时间
        setTimeout(() => {
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
        }, 3000);

        processedCount++; // 增加成功处理计数

      } catch (error) {
        console.error(`❌ 生成项目 ${itemId} 的播放进度分析CSV时出错:`, error);
        errorCount++;

        // 即使出错也尝试生成一个错误说明文件
        try {
          const itemDesc = getItemDescription(itemId);
          const descPrefix = itemDesc.substring(0, 20);
          const sanitizedDesc = sanitizeFileName(descPrefix);
          const fileName = `${sanitizedDesc}_播放进度分析_错误.csv`;

          const csvContent = `时间段,回看占比,跳过占比\n错误: ${error.message},0%,0%\n`;

          const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = fileName;
          link.style.display = 'none';

          document.body.appendChild(link);
          link.click();

          setTimeout(() => {
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          }, 3000);

          processedCount++; // 即使是错误文件也算作已处理
        } catch (fallbackError) {
          console.error(`❌ 生成项目 ${itemId} 的错误说明文件也失败:`, fallbackError);
        }
      }
    });

    // 给用户反馈
    if (processedCount === 0) {
      const message = `⚠️ 播放进度分析：虽然API调用成功，但没有生成任何CSV文件\n\n详细情况：\n- API成功调用: ${successResults.length} 个\n- 跳过处理: ${skippedCount} 个\n- 处理出错: ${errorCount} 个\n\n请检查控制台了解详细原因`;
      alert(message);
    } else if (processedCount < successResults.length) {
      const message = `⚠️ 播放进度分析：部分文件生成成功\n\n处理结果：\n- 成功生成: ${processedCount} 个CSV文件\n- 跳过处理: ${skippedCount} 个项目\n- 处理出错: ${errorCount} 个项目\n\n请检查控制台了解详细情况`;
      console.log(message);
    }
  }

  // 批量获取数据分析
  async function batchFetchItemAnalytics(itemIds) {
    const results = [];
    const total = itemIds.length;

    for (let i = 0; i < itemIds.length; i++) {
      // 确保itemId为字符串类型
      const itemId = String(itemIds[i]);
      const progress = i + 1;

      updateStatusIndicator(`📊 正在分析第 ${progress}/${total} 个项目 (ID: ${itemId})...`);

      try {
        const result = await fetchItemAnalytics(itemId);
        results.push(result);

        // 添加延迟避免请求过于频繁
        if (i < itemIds.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500)); // 500ms延迟
        }
      } catch (error) {
        console.error(`❌ 处理项目 ${itemId} 时出错:`, error);
        results.push({
          itemId: itemId,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  // 批量获取实时分析（支持留存分析和点赞分析）
  async function batchFetchItemRealtimeAnalytics(itemIds, analysisType = 1) {
    const analysisTypeName = analysisType === 1 ? '留存分析' : '点赞分析';
    const results = [];
    const total = itemIds.length;

    for (let i = 0; i < itemIds.length; i++) {
      // 确保itemId为字符串类型
      const itemId = String(itemIds[i]);
      const progress = i + 1;

      updateStatusIndicator(`📊 正在${analysisTypeName}第 ${progress}/${total} 个项目 (ID: ${itemId})...`);

      try {
        const result = await fetchItemRealtimeAnalytics(itemId, analysisType);
        results.push(result);

        // 添加延迟避免请求过于频繁
        if (i < itemIds.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500)); // 500ms延迟
        }
      } catch (error) {
        console.error(`❌ 处理项目 ${itemId} ${analysisTypeName}时出错:`, error);
        results.push({
          itemId: itemId,
          success: false,
          error: error.message,
          analysisType: analysisType
        });
      }
    }

    return results;
  }

  // 保持向后兼容的留存分析批量获取函数
  async function batchFetchItemRetentionAnalytics(itemIds) {
    return await batchFetchItemRealtimeAnalytics(itemIds, 1);
  }

  // 新增点赞分析批量获取函数
  async function batchFetchItemLikeAnalytics(itemIds) {
    return await batchFetchItemRealtimeAnalytics(itemIds, 2);
  }

  // 批量获取播放进度分析
  async function batchFetchItemProgressAnalytics(itemIds) {
    const results = [];
    const total = itemIds.length;

    for (let i = 0; i < itemIds.length; i++) {
      // 确保itemId为字符串类型
      const itemId = String(itemIds[i]);
      const progress = i + 1;

      updateStatusIndicator(`📊 正在播放进度分析第 ${progress}/${total} 个项目 (ID: ${itemId})...`);

      try {
        const result = await fetchItemProgressAnalytics(itemId);
        results.push(result);

        // 添加延迟避免请求过于频繁
        if (i < itemIds.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500)); // 500ms延迟
        }
      } catch (error) {
        console.error(`❌ 处理项目 ${itemId} 播放进度分析时出错:`, error);
        results.push({
          itemId: itemId,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  // // 确认选择
  // async function confirmSelection() {
  //   const selectedArray = Array.from(selectedItems);

  //   if (selectedArray.length === 0) {
  //     alert('请至少选择一个项目');
  //     return;
  //   }

  //   try {
  //     // 保存allItems的副本，因为closeModal会清空它
  //     const itemsBackup = [...allItems];

  //     // 关闭modal
  //     closeModal();

  //     // 恢复allItems，供后续CSV生成使用
  //     allItems = itemsBackup;

  //     // 直接执行音频下载
  //     updateStatusIndicator(`🎵 开始下载 ${selectedArray.length} 个项目的音频文件...`);
  //     await downloadAudioFiles(selectedArray);

  //     // 等待2秒再开始数据分析
  //     await new Promise(resolve => setTimeout(resolve, 2000));

  //     // 显示开始分析的状态
  //     updateStatusIndicator(`🚀 开始分析 ${selectedArray.length} 个项目的数据...`);

  //     // 同时批量获取四种数据分析
  //     const [trendResults, retentionResults, likeResults, progressResults] = await Promise.all([
  //       batchFetchItemAnalytics(selectedArray),
  //       batchFetchItemRetentionAnalytics(selectedArray),
  //       batchFetchItemLikeAnalytics(selectedArray),
  //       batchFetchItemProgressAnalytics(selectedArray)
  //     ]);

  //     // 统计结果
  //     const trendSuccessCount = trendResults.filter(r => r.success).length;
  //     const trendFailCount = trendResults.length - trendSuccessCount;
  //     const retentionSuccessCount = retentionResults.filter(r => r.success).length;
  //     const retentionFailCount = retentionResults.length - retentionSuccessCount;
  //     const likeSuccessCount = likeResults.filter(r => r.success).length;
  //     const likeFailCount = likeResults.length - likeSuccessCount;
  //     const progressSuccessCount = progressResults.filter(r => r.success).length;
  //     const progressFailCount = progressResults.length - progressSuccessCount;

  //     // 如果有成功的数据，自动下载CSV文件
  //     const hasSuccessData = trendSuccessCount > 0 || retentionSuccessCount > 0 || likeSuccessCount > 0 || progressSuccessCount > 0;

  //     if (hasSuccessData) {
  //       updateStatusIndicator(`📥 正在生成并下载所有CSV文件...`);

  //       // 延迟一下再下载，让用户看到状态更新
  //       setTimeout(async () => {
  //         // 下载趋势数据CSV
  //         if (trendSuccessCount > 0) {
  //           downloadTrendDataAsCSV(trendResults);
  //           // 等待2秒再下载下一个文件，避免浏览器下载限制
  //           await new Promise(resolve => setTimeout(resolve, 2000));
  //         }

  //         // 下载合并的留存点赞分析CSV（如果两种分析都有数据）
  //         if (retentionSuccessCount > 0 && likeSuccessCount > 0) {
  //           downloadCombinedRealtimeAnalysisDataAsCSV(retentionResults, likeResults);
  //           // 等待2秒再下载下一个文件
  //           await new Promise(resolve => setTimeout(resolve, 2000));
  //         } else {
  //           // 如果只有一种分析有数据，则分别下载
  //           if (retentionSuccessCount > 0) {
  //             downloadRetentionDataAsCSV(retentionResults);
  //             // 等待2秒再下载下一个文件
  //             await new Promise(resolve => setTimeout(resolve, 2000));
  //           }

  //           if (likeSuccessCount > 0) {
  //             downloadLikeAnalysisDataAsCSV(likeResults);
  //             // 等待2秒再下载下一个文件
  //             await new Promise(resolve => setTimeout(resolve, 2000));
  //           }
  //         }

  //         // 下载播放进度分析CSV
  //         if (progressSuccessCount > 0) {
  //           downloadProgressAnalysisDataAsCSV(progressResults);
  //         }

  //         // 显示完成状态
  //         const totalFail = trendFailCount + retentionFailCount + likeFailCount + progressFailCount;
  //         const hasCombinedAnalysis = retentionSuccessCount > 0 && likeSuccessCount > 0;

  //         if (totalFail === 0) {
  //           updateStatusIndicator(`🎉 所有数据分析和下载完成！成功处理 ${selectedArray.length} 个项目`);
  //         } else {
  //           updateStatusIndicator(`⚠️ 处理完成：趋势数据成功${trendSuccessCount}个，留存分析成功${retentionSuccessCount}个，点赞分析成功${likeSuccessCount}个，播放进度分析成功${progressSuccessCount}个，CSV已下载`);
  //         }

  //         // 弹出结果摘要
  //         let summaryMessage = `数据分析完成！\n\n📈 趋势数据：成功 ${trendSuccessCount} 个，失败 ${trendFailCount} 个\n📊 留存分析：成功 ${retentionSuccessCount} 个，失败 ${retentionFailCount} 个\n👍 点赞分析：成功 ${likeSuccessCount} 个，失败 ${likeFailCount} 个\n📺 播放进度分析：成功 ${progressSuccessCount} 个，失败 ${progressFailCount} 个\n\n📥 所有CSV文件已自动下载`;

  //         if (hasCombinedAnalysis) {
  //           summaryMessage += `\n🔗 留存和点赞分析已合并为一个CSV文件`;
  //         }

  //         summaryMessage += `\n详细结果请查看控制台 (F12)`;
  //         alert(summaryMessage);

  //       }, 1000);
  //     } else {
  //       // 没有成功的数据
  //       updateStatusIndicator(`❌ 分析完成但无可用数据：趋势数据失败${trendFailCount}个，留存分析失败${retentionFailCount}个，点赞分析失败${likeFailCount}个，播放进度分析失败${progressFailCount}个`);

  //       const summaryMessage = `数据分析完成！\n\n📈 趋势数据：成功 ${trendSuccessCount} 个，失败 ${trendFailCount} 个\n📊 留存分析：成功 ${retentionSuccessCount} 个，失败 ${retentionFailCount} 个\n👍 点赞分析：成功 ${likeSuccessCount} 个，失败 ${likeFailCount} 个\n📺 播放进度分析：成功 ${progressSuccessCount} 个，失败 ${progressFailCount} 个\n\n⚠️ 没有可下载的数据\n详细结果请查看控制台 (F12)`;
  //       alert(summaryMessage);
  //     }

  //   } catch (error) {
  //     console.error('❌ 批量数据分析过程中出错:', error);
  //     updateStatusIndicator('❌ 数据分析失败，请查看控制台');
  //     alert('数据分析过程中出现错误，请查看控制台了解详情');
  //   } finally {
  //     // 8秒后移除状态指示器（延长时间以便用户看到下载状态）
  //     setTimeout(() => {
  //       removeStatusIndicator();
  //     }, 8000);
  //   }
  // }

    // 确认选择
    async function confirmSelection() {
      const selectedArray = Array.from(selectedItems);

      if (selectedArray.length === 0) {
        alert('请至少选择一个项目');
        return;
      }

      try {
        // 保存allItems的副本，因为closeModal会清空它
        const itemsBackup = [...allItems];

        // 关闭modal
        closeModal();

        // 恢复allItems，供后续使用
        allItems = itemsBackup;

        // 直接执行音频下载
        updateStatusIndicator(`🎵 开始下载 ${selectedArray.length} 个项目的音频文件...`);
        await downloadAudioFiles(selectedArray);

        // 等待2秒再开始数据分析
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 显示开始分析的状态
        updateStatusIndicator(`🚀 开始分析 ${selectedArray.length} 个项目的数据...`);

        // 批量调用testMetricsTrendAPI生成Markdown文件
        for (let i = 0; i < selectedArray.length; i++) {
          const itemId = selectedArray[i];
          const progress = i + 1;

          updateStatusIndicator(`📊 正在生成第 ${progress}/${selectedArray.length} 个项目的Markdown报告 (ID: ${itemId})...`);

          try {
            await testMetricsTrendAPI(itemId);
            console.log(`✅ 项目 ${itemId} Markdown报告生成成功`);

            // 添加延迟避免请求过于频繁
            if (i < selectedArray.length - 1) {
              await new Promise(resolve => setTimeout(resolve, 2000)); // 2秒延迟
            }
          } catch (error) {
            console.error(`❌ 项目 ${itemId} Markdown报告生成失败:`, error);
          }
        }

        // 显示完成状态
        updateStatusIndicator(`🎉 所有Markdown报告生成完成！成功处理 ${selectedArray.length} 个项目`);

        // 弹出结果摘要
        const summaryMessage = `Markdown报告生成完成！\n\n📄 已为 ${selectedArray.length} 个项目生成完整的数据分析报告\n\n📥 所有Markdown文件已自动下载\n详细结果请查看控制台 (F12)`;
        alert(summaryMessage);

      } catch (error) {
        console.error('❌ 批量Markdown报告生成过程中出错:', error);
        updateStatusIndicator('❌ Markdown报告生成失败，请查看控制台');
        alert('Markdown报告生成过程中出现错误，请查看控制台了解详情');
      }
    }

  // 启动脚本
  init();

    /**
   * 获取作品播放量趋势数据
   * @param {string} itemIdStr - 作品ID
   * @param {number} trendType - 趋势类型，默认1
   * @param {number} timeUnit - 时间单位，默认2
   * @param {string} metrics - 指标，默认view_count
   * @returns {Promise<Object>} 播放量趋势数据
   */
    async function getItemMetricsTrend(itemIdStr, trendType = 1, timeUnit = 2, metrics = 'view_count') {
      try {
        // 验证item_id格式
        if (!itemIdStr || !/^\d+$/.test(itemIdStr)) {
          throw new Error(`无效的item_id格式: ${itemIdStr}`);
        }

        const apiUrl = `https://creator.douyin.com/janus/douyin/creator/data/item_analysis/metrics_trend?aid=2906&app_name=aweme_creator_platform&device_platform=web&browser_online=true&timezone_name=Asia%2FShanghai&item_id=${itemIdStr}&trend_type=${trendType}&time_unit=${timeUnit}&metrics_group=0,1,3&metrics=${metrics}`;

        const response = await fetch(apiUrl, {
          method: "GET",
          mode: "cors",
          credentials: "include",
          headers: {
            'Accept': 'application/json',
            'Agw-Js-Conv': 'str'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP错误: ${response.status}`);
        }

        const data = await response.json();

        if (data.status_code !== 0) {
          throw new Error(`API错误: ${data.status_msg || '未知错误'}`);
        }

        return data;
      } catch (error) {
        console.error('获取播放量趋势数据失败:', error);
        throw error;
      }
    }

     /**
     * 格式化播放量趋势数据为Markdown
     * @param {Object} trendData - 播放量趋势数据
     * @returns {string} Markdown格式的报告
     */
     function formatMetricsTrendAsMarkdown(trendData) {
      if (!trendData || !trendData.trend_map) {
        return '# 作品数据趋势分析\n\n暂无数据\n';
      }

      let markdown = '# 作品数据趋势分析\n\n';

      // 处理涨粉率数据
      if (trendData.trend_map.subscribe_rate) {
        markdown += '# 涨粉率趋势分析\n\n';
        const subscribeRateData = trendData.trend_map.subscribe_rate;
        const subscribeRateDataTypeMap = {
          '0': '总涨粉率',
          '1': '抖音涨粉率',
          '3': '精选App涨粉率',
        };

        Object.keys(subscribeRateData).forEach(key => {
          const dataType = subscribeRateDataTypeMap[key] || `类型${key}`;
          const trends = subscribeRateData[key];

          if (!trends || trends.length === 0) return;

          markdown += `## ${dataType}\n\n`;
          markdown += '| 时间 | 涨粉率 |\n';
          markdown += '|------|--------|\n';

          let totalRate = 0;
          trends.forEach(trend => {
            const time = trend.date_time;
            const rate = parseFloat(trend.value);
            totalRate += rate;
            const ratePercent = `${(rate * 100).toFixed(2)}%`;
            markdown += `| ${time} | ${ratePercent} |\n`;
          });

          const avgRate = totalRate / trends.length;
          markdown += `\n**平均涨粉率**: ${(avgRate * 100).toFixed(2)}%\n\n`;
        });
      }

      // 处理脱粉率数据
      if (trendData.trend_map.unsubscribe_rate) {
        markdown += '# 脱粉率趋势分析\n\n';
        const unsubscribeRateData = trendData.trend_map.unsubscribe_rate;
        const unsubscribeRateDataTypeMap = {
          '0': '总脱粉率',
          '1': '抖音脱粉率',
          '3': '精选App脱粉率',
        };

        Object.keys(unsubscribeRateData).forEach(key => {
          const dataType = unsubscribeRateDataTypeMap[key] || `类型${key}`;
          const trends = unsubscribeRateData[key];

          if (!trends || trends.length === 0) return;

          markdown += `## ${dataType}\n\n`;
          markdown += '| 时间 | 脱粉率 |\n';
          markdown += '|------|--------|\n';

          let totalRate = 0;
          trends.forEach(trend => {
            const time = trend.date_time;
            const rate = parseFloat(trend.value);
            totalRate += rate;
            const ratePercent = `${(rate * 100).toFixed(2)}%`;
            markdown += `| ${time} | ${ratePercent} |\n`;
          });

          const avgRate = totalRate / trends.length;
          markdown += `\n**平均脱粉率**: ${(avgRate * 100).toFixed(2)}%\n\n`;
        });
      }

      // 处理不感兴趣量数据
      if (trendData.trend_map.dislike_count) {
        markdown += '# 不感兴趣量趋势分析\n\n';
        const dislikeCountData = trendData.trend_map.dislike_count;
        const dislikeCountDataTypeMap = {
          '0': '总不感兴趣量',
          '1': '抖音不感兴趣量',
          '3': '精选App不感兴趣量',
        };

        Object.keys(dislikeCountData).forEach(key => {
          const dataType = dislikeCountDataTypeMap[key] || `类型${key}`;
          const trends = dislikeCountData[key];

          if (!trends || trends.length === 0) return;

          markdown += `## ${dataType}\n\n`;
          markdown += '| 时间 | 不感兴趣量 |\n';
          markdown += '|------|--------|\n';

          let totalDislikes = 0;
          trends.forEach(trend => {
            const time = trend.date_time;
            const value = parseFloat(trend.value).toLocaleString();
            totalDislikes += parseFloat(trend.value);
            markdown += `| ${time} | ${value} |\n`;
          });

          markdown += `\n**总计**: ${totalDislikes.toLocaleString()} 次不感兴趣\n\n`;
        });
      }

      // 处理不感兴趣率数据
      if (trendData.trend_map.dislike_rate) {
        markdown += '# 不感兴趣率趋势分析\n\n';
        const dislikeRateData = trendData.trend_map.dislike_rate;
        const dislikeRateDataTypeMap = {
          '0': '总不感兴趣率',
          '1': '抖音不感兴趣率',
          '3': '精选App不感兴趣率',
        };

        Object.keys(dislikeRateData).forEach(key => {
          const dataType = dislikeRateDataTypeMap[key] || `类型${key}`;
          const trends = dislikeRateData[key];

          if (!trends || trends.length === 0) return;

          markdown += `## ${dataType}\n\n`;
          markdown += '| 时间 | 不感兴趣率 |\n';
          markdown += '|------|--------|\n';

          let totalRate = 0;
          trends.forEach(trend => {
            const time = trend.date_time;
            const rate = parseFloat(trend.value);
            totalRate += rate;
            const ratePercent = `${(rate * 100).toFixed(2)}%`;
            markdown += `| ${time} | ${ratePercent} |\n`;
          });

          const avgRate = totalRate / trends.length;
          markdown += `\n**平均不感兴趣率**: ${(avgRate * 100).toFixed(2)}%\n\n`;
        });
      }

      // 处理播放量数据
      if (trendData.trend_map.view_count) {
        markdown += '# 播放量趋势分析\n\n';
        const viewCountData = trendData.trend_map.view_count;
        const dataTypeMap = {
          '0': '总播放量',
          '1': '抖音播放量',
          '3': '精选App播放量',
        };

        Object.keys(viewCountData).forEach(key => {
          const dataType = dataTypeMap[key] || `类型${key}`;
          const trends = viewCountData[key];

          if (!trends || trends.length === 0) return;

          markdown += `## ${dataType}\n\n`;
          markdown += '| 时间 | 播放量 |\n';
          markdown += '|------|--------|\n';

          let totalViews = 0;
          trends.forEach(trend => {
            const time = trend.date_time;
            const value = parseFloat(trend.value).toLocaleString();
            totalViews += parseFloat(trend.value);
            markdown += `| ${time} | ${value} |\n`;
          });

          markdown += `\n**总计**: ${totalViews.toLocaleString()} 次播放\n\n`;
        });

        // 添加播放量统计摘要
        markdown += '## 播放量统计摘要\n\n';
        Object.keys(viewCountData).forEach(key => {
          const dataType = dataTypeMap[key] || `类型${key}`;
          const trends = viewCountData[key];

          if (!trends || trends.length === 0) return;

          const totalViews = trends.reduce((sum, trend) => sum + parseFloat(trend.value), 0);
          const avgViews = totalViews / trends.length;
          const maxViews = Math.max(...trends.map(t => parseFloat(t.value)));
          const minViews = Math.min(...trends.map(t => parseFloat(t.value)));

          markdown += `### ${dataType}\n`;
          markdown += `- 总播放量: ${totalViews.toLocaleString()}\n`;
          markdown += `- 平均播放量: ${avgViews.toFixed(0).toLocaleString()}\n`;
          markdown += `- 最高播放量: ${maxViews.toLocaleString()}\n`;
          markdown += `- 最低播放量: ${minViews.toLocaleString()}\n\n`;
        });
      }

      // 处理关注数据
      if (trendData.trend_map.subscribe_count) {
        markdown += '# 关注数趋势分析\n\n';
        const subscribeCountData = trendData.trend_map.subscribe_count;
        const subscribeDataTypeMap = {
          '0': '总关注数',
          '1': '抖音关注数',
          '3': '精选App关注数',
        };

        Object.keys(subscribeCountData).forEach(key => {
          const dataType = subscribeDataTypeMap[key] || `类型${key}`;
          const trends = subscribeCountData[key];

          if (!trends || trends.length === 0) return;

          markdown += `## ${dataType}\n\n`;
          markdown += '| 时间 | 关注数 |\n';
          markdown += '|------|--------|\n';

          let totalSubscribes = 0;
          trends.forEach(trend => {
            const time = trend.date_time;
            const value = parseFloat(trend.value).toLocaleString();
            totalSubscribes += parseFloat(trend.value);
            markdown += `| ${time} | ${value} |\n`;
          });

          markdown += `\n**总计**: ${totalSubscribes.toLocaleString()} 次关注\n\n`;
        });

        // 添加关注数统计摘要
        markdown += '## 关注数统计摘要\n\n';
        Object.keys(subscribeCountData).forEach(key => {
          const dataType = subscribeDataTypeMap[key] || `类型${key}`;
          const trends = subscribeCountData[key];

          if (!trends || trends.length === 0) return;

          const totalSubscribes = trends.reduce((sum, trend) => sum + parseFloat(trend.value), 0);
          const avgSubscribes = totalSubscribes / trends.length;
          const maxSubscribes = Math.max(...trends.map(t => parseFloat(t.value)));
          const minSubscribes = Math.min(...trends.map(t => parseFloat(t.value)));

          markdown += `### ${dataType}\n`;
          markdown += `- 总关注数: ${totalSubscribes.toLocaleString()}\n`;
          markdown += `- 平均关注数: ${avgSubscribes.toFixed(0).toLocaleString()}\n`;
          markdown += `- 最高关注数: ${maxSubscribes.toLocaleString()}\n`;
          markdown += `- 最低关注数: ${minSubscribes.toLocaleString()}\n\n`;
        });
      }

      // 处理脱粉量数据
      if (trendData.trend_map.unsubscribe_count) {
        markdown += '# 脱粉量趋势分析\n\n';
        const unsubscribeCountData = trendData.trend_map.unsubscribe_count;
        const unsubscribeDataTypeMap = {
          '0': '总脱粉量',
          '1': '抖音脱粉量',
          '3': '精选App脱粉量',
        };

        Object.keys(unsubscribeCountData).forEach(key => {
          const dataType = unsubscribeDataTypeMap[key] || `类型${key}`;
          const trends = unsubscribeCountData[key];

          if (!trends || trends.length === 0) return;

          markdown += `## ${dataType}\n\n`;
          markdown += '| 时间 | 脱粉量 |\n';
          markdown += '|------|--------|\n';

          let totalUnsubscribes = 0;
          trends.forEach(trend => {
            const time = trend.date_time;
            const value = parseFloat(trend.value).toLocaleString();
            totalUnsubscribes += parseFloat(trend.value);
            markdown += `| ${time} | ${value} |\n`;
          });

          markdown += `\n**总计**: ${totalUnsubscribes.toLocaleString()} 次脱粉\n\n`;
        });

        // 添加脱粉量统计摘要
        markdown += '## 脱粉量统计摘要\n\n';
        Object.keys(unsubscribeCountData).forEach(key => {
          const dataType = unsubscribeDataTypeMap[key] || `类型${key}`;
          const trends = unsubscribeCountData[key];

          if (!trends || trends.length === 0) return;

          const totalUnsubscribes = trends.reduce((sum, trend) => sum + parseFloat(trend.value), 0);
          const avgUnsubscribes = totalUnsubscribes / trends.length;
          const maxUnsubscribes = Math.max(...trends.map(t => parseFloat(t.value)));
          const minUnsubscribes = Math.min(...trends.map(t => parseFloat(t.value)));

          markdown += `### ${dataType}\n`;
          markdown += `- 总脱粉量: ${totalUnsubscribes.toLocaleString()}\n`;
          markdown += `- 平均脱粉量: ${avgUnsubscribes.toFixed(0).toLocaleString()}\n`;
          markdown += `- 最高脱粉量: ${maxUnsubscribes.toLocaleString()}\n`;
          markdown += `- 最低脱粉量: ${minUnsubscribes.toLocaleString()}\n\n`;
        });
      }

      // 处理粉丝播放占比数据
      if (trendData.trend_map.fan_view_proportion) {
        markdown += '# 粉丝播放占比趋势分析\n\n';
        const fanViewProportionData = trendData.trend_map.fan_view_proportion;
        const fanViewDataTypeMap = {
          '0': '总粉丝播放占比',
          '1': '抖音粉丝播放占比',
          '3': '精选App粉丝播放占比',
        };

        Object.keys(fanViewProportionData).forEach(key => {
          const dataType = fanViewDataTypeMap[key] || `类型${key}`;
          const trends = fanViewProportionData[key];

          if (!trends || trends.length === 0) return;

          markdown += `## ${dataType}\n\n`;
          markdown += '| 时间 | 粉丝播放占比 |\n';
          markdown += '|------|--------|\n';

          let totalProportion = 0;
          trends.forEach(trend => {
            const time = trend.date_time;
            const proportion = parseFloat(trend.value);
            totalProportion += proportion;
            const proportionPercent = `${(proportion * 100).toFixed(2)}%`;
            markdown += `| ${time} | ${proportionPercent} |\n`;
          });

          const avgProportion = totalProportion / trends.length;
          markdown += `\n**平均占比**: ${(avgProportion * 100).toFixed(2)}%\n\n`;
        });

        // 添加粉丝播放占比统计摘要
        markdown += '## 粉丝播放占比统计摘要\n\n';
        Object.keys(fanViewProportionData).forEach(key => {
          const dataType = fanViewDataTypeMap[key] || `类型${key}`;
          const trends = fanViewProportionData[key];

          if (!trends || trends.length === 0) return;

          const totalProportion = trends.reduce((sum, trend) => sum + parseFloat(trend.value), 0);
          const avgProportion = totalProportion / trends.length;
          const maxProportion = Math.max(...trends.map(t => parseFloat(t.value)));
          const minProportion = Math.min(...trends.map(t => parseFloat(t.value)));

          markdown += `### ${dataType}\n`;
          markdown += `- 平均粉丝播放占比: ${(avgProportion * 100).toFixed(2)}%\n`;
          markdown += `- 最高粉丝播放占比: ${(maxProportion * 100).toFixed(2)}%\n`;
          markdown += `- 最低粉丝播放占比: ${(minProportion * 100).toFixed(2)}%\n\n`;
        });
      }

      // 处理平均播放时长数据
      if (trendData.trend_map.avg_view_second) {
        markdown += '# 平均播放时长趋势分析\n\n';
        const avgViewSecondData = trendData.trend_map.avg_view_second;
        const avgViewDataTypeMap = {
          '0': '总平均播放时长',
          '1': '抖音平均播放时长',
          '3': '精选App平均播放时长',
        };

        Object.keys(avgViewSecondData).forEach(key => {
          const dataType = avgViewDataTypeMap[key] || `类型${key}`;
          const trends = avgViewSecondData[key];

          if (!trends || trends.length === 0) return;

          markdown += `## ${dataType}\n\n`;
          markdown += '| 时间 | 平均播放时长 |\n';
          markdown += '|------|--------|\n';

          let totalSeconds = 0;
          trends.forEach(trend => {
            const time = trend.date_time;
            const seconds = parseFloat(trend.value);
            totalSeconds += seconds;

            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            const timeFormat = `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;

            markdown += `| ${time} | ${timeFormat} (${seconds.toFixed(1)}秒) |\n`;
          });

          const avgSeconds = totalSeconds / trends.length;
          const avgMinutes = Math.floor(avgSeconds / 60);
          const avgRemainingSecs = Math.floor(avgSeconds % 60);
          const avgTimeFormat = `${avgMinutes}:${avgRemainingSecs.toString().padStart(2, '0')}`;

          markdown += `\n**平均时长**: ${avgTimeFormat} (${avgSeconds.toFixed(1)}秒)\n\n`;
        });

        // 添加平均播放时长统计摘要
        markdown += '## 平均播放时长统计摘要\n\n';
        Object.keys(avgViewSecondData).forEach(key => {
          const dataType = avgViewDataTypeMap[key] || `类型${key}`;
          const trends = avgViewSecondData[key];

          if (!trends || trends.length === 0) return;

          const totalSeconds = trends.reduce((sum, trend) => sum + parseFloat(trend.value), 0);
          const avgSeconds = totalSeconds / trends.length;
          const maxSeconds = Math.max(...trends.map(t => parseFloat(t.value)));
          const minSeconds = Math.min(...trends.map(t => parseFloat(t.value)));

          const formatTime = (seconds) => {
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins}:${secs.toString().padStart(2, '0')} (${seconds.toFixed(1)}秒)`;
          };

          markdown += `### ${dataType}\n`;
          markdown += `- 平均播放时长: ${formatTime(avgSeconds)}\n`;
          markdown += `- 最长播放时长: ${formatTime(maxSeconds)}\n`;
          markdown += `- 最短播放时长: ${formatTime(minSeconds)}\n\n`;
        });
      }

      // 处理封面点击率数据
      if (trendData.trend_map.cover_click_rate) {
        markdown += '# 封面点击率趋势分析\n\n';
        const coverClickRateData = trendData.trend_map.cover_click_rate;
        const coverDataTypeMap = {
          '0': '总封面点击率',
          '1': '抖音封面点击率',
          '3': '精选App封面点击率',
        };

        Object.keys(coverClickRateData).forEach(key => {
          const dataType = coverDataTypeMap[key] || `类型${key}`;
          const trends = coverClickRateData[key];

          if (!trends || trends.length === 0) return;

          markdown += `## ${dataType}\n\n`;
          markdown += '| 时间 | 封面点击率 |\n';
          markdown += '|------|--------|\n';

          let totalRate = 0;
          trends.forEach(trend => {
            const time = trend.date_time;
            const rate = parseFloat(trend.value);
            totalRate += rate;
            const ratePercent = `${(rate * 100).toFixed(2)}%`;
            markdown += `| ${time} | ${ratePercent} |\n`;
          });

          const avgRate = totalRate / trends.length;
          markdown += `\n**平均点击率**: ${(avgRate * 100).toFixed(2)}%\n\n`;
        });

        // 添加封面点击率统计摘要
        markdown += '## 封面点击率统计摘要\n\n';
        Object.keys(coverClickRateData).forEach(key => {
          const dataType = coverDataTypeMap[key] || `类型${key}`;
          const trends = coverClickRateData[key];

          if (!trends || trends.length === 0) return;

          const totalRate = trends.reduce((sum, trend) => sum + parseFloat(trend.value), 0);
          const avgRate = totalRate / trends.length;
          const maxRate = Math.max(...trends.map(t => parseFloat(t.value)));
          const minRate = Math.min(...trends.map(t => parseFloat(t.value)));

          markdown += `### ${dataType}\n`;
          markdown += `- 平均点击率: ${(avgRate * 100).toFixed(2)}%\n`;
          markdown += `- 最高点击率: ${(maxRate * 100).toFixed(2)}%\n`;
          markdown += `- 最低点击率: ${(minRate * 100).toFixed(2)}%\n\n`;
        });
      }

      // 处理点赞量数据
      if (trendData.trend_map.like_count) {
        markdown += '# 点赞量趋势分析\n\n';
        const likeCountData = trendData.trend_map.like_count;
        const likeDataTypeMap = {
          '0': '总点赞量',
          '1': '抖音点赞量',
          '3': '精选App点赞量',
        };

        Object.keys(likeCountData).forEach(key => {
          const dataType = likeDataTypeMap[key] || `类型${key}`;
          const trends = likeCountData[key];

          if (!trends || trends.length === 0) return;

          markdown += `## ${dataType}\n\n`;
          markdown += '| 时间 | 点赞量 |\n';
          markdown += '|------|--------|\n';

          let totalLikes = 0;
          trends.forEach(trend => {
            const time = trend.date_time;
            const value = parseFloat(trend.value).toLocaleString();
            totalLikes += parseFloat(trend.value);
            markdown += `| ${time} | ${value} |\n`;
          });

          markdown += `\n**总计**: ${totalLikes.toLocaleString()} 次点赞\n\n`;
        });

        // 添加点赞量统计摘要
        markdown += '## 点赞量统计摘要\n\n';
        Object.keys(likeCountData).forEach(key => {
          const dataType = likeDataTypeMap[key] || `类型${key}`;
          const trends = likeCountData[key];

          if (!trends || trends.length === 0) return;

          const totalLikes = trends.reduce((sum, trend) => sum + parseFloat(trend.value), 0);
          const avgLikes = totalLikes / trends.length;
          const maxLikes = Math.max(...trends.map(t => parseFloat(t.value)));
          const minLikes = Math.min(...trends.map(t => parseFloat(t.value)));

          markdown += `### ${dataType}\n`;
          markdown += `- 总点赞量: ${totalLikes.toLocaleString()}\n`;
          markdown += `- 平均点赞量: ${avgLikes.toFixed(0).toLocaleString()}\n`;
          markdown += `- 最高点赞量: ${maxLikes.toLocaleString()}\n`;
          markdown += `- 最低点赞量: ${minLikes.toLocaleString()}\n\n`;
        });
      }

      // 处理评论量数据
      if (trendData.trend_map.comment_count) {
        markdown += '# 评论量趋势分析\n\n';
        const commentCountData = trendData.trend_map.comment_count;
        const commentDataTypeMap = {
          '0': '总评论量',
          '1': '抖音评论量',
          '3': '精选App评论量',
        };

        Object.keys(commentCountData).forEach(key => {
          const dataType = commentDataTypeMap[key] || `类型${key}`;
          const trends = commentCountData[key];

          if (!trends || trends.length === 0) return;

          markdown += `## ${dataType}\n\n`;
          markdown += '| 时间 | 评论量 |\n';
          markdown += '|------|--------|\n';

          let totalComments = 0;
          trends.forEach(trend => {
            const time = trend.date_time;
            const value = parseFloat(trend.value).toLocaleString();
            totalComments += parseFloat(trend.value);
            markdown += `| ${time} | ${value} |\n`;
          });

          markdown += `\n**总计**: ${totalComments.toLocaleString()} 条评论\n\n`;
        });

        // 添加评论量统计摘要
        markdown += '## 评论量统计摘要\n\n';
        Object.keys(commentCountData).forEach(key => {
          const dataType = commentDataTypeMap[key] || `类型${key}`;
          const trends = commentCountData[key];

          if (!trends || trends.length === 0) return;

          const totalComments = trends.reduce((sum, trend) => sum + parseFloat(trend.value), 0);
          const avgComments = totalComments / trends.length;
          const maxComments = Math.max(...trends.map(t => parseFloat(t.value)));
          const minComments = Math.min(...trends.map(t => parseFloat(t.value)));

          markdown += `### ${dataType}\n`;
          markdown += `- 总评论量: ${totalComments.toLocaleString()}\n`;
          markdown += `- 平均评论量: ${avgComments.toFixed(0).toLocaleString()}\n`;
          markdown += `- 最高评论量: ${maxComments.toLocaleString()}\n`;
          markdown += `- 最低评论量: ${minComments.toLocaleString()}\n\n`;
        });
      }

      // 处理分享量数据
      if (trendData.trend_map.share_count) {
        markdown += '# 分享量趋势分析\n\n';
        const shareCountData = trendData.trend_map.share_count;
        const shareDataTypeMap = {
          '0': '总分享量',
          '1': '抖音分享量',
          '3': '精选App分享量',
        };

        Object.keys(shareCountData).forEach(key => {
          const dataType = shareDataTypeMap[key] || `类型${key}`;
          const trends = shareCountData[key];

          if (!trends || trends.length === 0) return;

          markdown += `## ${dataType}\n\n`;
          markdown += '| 时间 | 分享量 |\n';
          markdown += '|------|--------|\n';

          let totalShares = 0;
          trends.forEach(trend => {
            const time = trend.date_time;
            const value = parseFloat(trend.value).toLocaleString();
            totalShares += parseFloat(trend.value);
            markdown += `| ${time} | ${value} |\n`;
          });

          markdown += `\n**总计**: ${totalShares.toLocaleString()} 次分享\n\n`;
        });

        // 添加分享量统计摘要
        markdown += '## 分享量统计摘要\n\n';
        Object.keys(shareCountData).forEach(key => {
          const dataType = shareDataTypeMap[key] || `类型${key}`;
          const trends = shareCountData[key];

          if (!trends || trends.length === 0) return;

          const totalShares = trends.reduce((sum, trend) => sum + parseFloat(trend.value), 0);
          const avgShares = totalShares / trends.length;
          const maxShares = Math.max(...trends.map(t => parseFloat(t.value)));
          const minShares = Math.min(...trends.map(t => parseFloat(t.value)));

          markdown += `### ${dataType}\n`;
          markdown += `- 总分享量: ${totalShares.toLocaleString()}\n`;
          markdown += `- 平均分享量: ${avgShares.toFixed(0).toLocaleString()}\n`;
          markdown += `- 最高分享量: ${maxShares.toLocaleString()}\n`;
          markdown += `- 最低分享量: ${minShares.toLocaleString()}\n\n`;
        });
      }

      // 处理收藏量数据
      if (trendData.trend_map.favorite_count) {
        markdown += '# 收藏量趋势分析\n\n';
        const favoriteCountData = trendData.trend_map.favorite_count;
        const favoriteDataTypeMap = {
          '0': '总收藏量',
          '1': '抖音收藏量',
          '3': '精选App收藏量',
        };

        Object.keys(favoriteCountData).forEach(key => {
          const dataType = favoriteDataTypeMap[key] || `类型${key}`;
          const trends = favoriteCountData[key];

          if (!trends || trends.length === 0) return;

          markdown += `## ${dataType}\n\n`;
          markdown += '| 时间 | 收藏量 |\n';
          markdown += '|------|--------|\n';

          let totalFavorites = 0;
          trends.forEach(trend => {
            const time = trend.date_time;
            const value = parseFloat(trend.value).toLocaleString();
            totalFavorites += parseFloat(trend.value);
            markdown += `| ${time} | ${value} |\n`;
          });

          markdown += `\n**总计**: ${totalFavorites.toLocaleString()} 次收藏\n\n`;
        });

        // 添加收藏量统计摘要
        markdown += '## 收藏量统计摘要\n\n';
        Object.keys(favoriteCountData).forEach(key => {
          const dataType = favoriteDataTypeMap[key] || `类型${key}`;
          const trends = favoriteCountData[key];

          if (!trends || trends.length === 0) return;

          const totalFavorites = trends.reduce((sum, trend) => sum + parseFloat(trend.value), 0);
          const avgFavorites = totalFavorites / trends.length;
          const maxFavorites = Math.max(...trends.map(t => parseFloat(t.value)));
          const minFavorites = Math.min(...trends.map(t => parseFloat(t.value)));

          markdown += `### ${dataType}\n`;
          markdown += `- 总收藏量: ${totalFavorites.toLocaleString()}\n`;
          markdown += `- 平均收藏量: ${avgFavorites.toFixed(0).toLocaleString()}\n`;
          markdown += `- 最高收藏量: ${maxFavorites.toLocaleString()}\n`;
          markdown += `- 最低收藏量: ${minFavorites.toLocaleString()}\n\n`;
        });
      }

      // 处理弹幕量数据
      if (trendData.trend_map.danmaku_count) {
        markdown += '# 弹幕量趋势分析\n\n';
        const danmakuCountData = trendData.trend_map.danmaku_count;
        const danmakuDataTypeMap = {
          '0': '总弹幕量',
          '1': '抖音弹幕量',
          '3': '精选App弹幕量',
        };

        Object.keys(danmakuCountData).forEach(key => {
          const dataType = danmakuDataTypeMap[key] || `类型${key}`;
          const trends = danmakuCountData[key];

          if (!trends || trends.length === 0) return;

          markdown += `## ${dataType}\n\n`;
          markdown += '| 时间 | 弹幕量 |\n';
          markdown += '|------|--------|\n';

          let totalDanmaku = 0;
          trends.forEach(trend => {
            const time = trend.date_time;
            const value = parseFloat(trend.value).toLocaleString();
            totalDanmaku += parseFloat(trend.value);
            markdown += `| ${time} | ${value} |\n`;
          });

          markdown += `\n**总计**: ${totalDanmaku.toLocaleString()} 条弹幕\n\n`;
        });

        // 添加弹幕量统计摘要
        markdown += '## 弹幕量统计摘要\n\n';
        Object.keys(danmakuCountData).forEach(key => {
          const dataType = danmakuDataTypeMap[key] || `类型${key}`;
          const trends = danmakuCountData[key];

          if (!trends || trends.length === 0) return;

          const totalDanmaku = trends.reduce((sum, trend) => sum + parseFloat(trend.value), 0);
          const avgDanmaku = totalDanmaku / trends.length;
          const maxDanmaku = Math.max(...trends.map(t => parseFloat(t.value)));
          const minDanmaku = Math.min(...trends.map(t => parseFloat(t.value)));

          markdown += `### ${dataType}\n`;
          markdown += `- 总弹幕量: ${totalDanmaku.toLocaleString()}\n`;
          markdown += `- 平均弹幕量: ${avgDanmaku.toFixed(0).toLocaleString()}\n`;
          markdown += `- 最高弹幕量: ${maxDanmaku.toLocaleString()}\n`;
          markdown += `- 最低弹幕量: ${minDanmaku.toLocaleString()}\n\n`;
        });
      }

      return markdown;
    }

    /**
     * 测试播放量趋势API功能
     * @param {string} itemId - 作品ID
     */
    async function testMetricsTrendAPI(itemId) {
      if (!itemId) {
        // 尝试从当前页面URL获取item_id
        const urlMatch = window.location.href.match(/work-detail\/(\d+)/);
        if (urlMatch) {
          itemId = urlMatch[1];
        } else {
          console.error('❌ 请提供有效的作品ID');
          return { success: false, error: '缺少作品ID' };
        }
      }

      console.log(`🧪 开始测试作品数据趋势API功能，作品ID: ${itemId}`);

      try {
        const [
          viewTrendData,
          subscribeRateData,
          unsubscribeRateData,
          dislikeCountData,
          dislikeRateData,
          subscribeTrendData,
          avgViewSecondData,
          coverClickRateData,
          likeCountData,
          commentCountData,
          shareCountData,
          favoriteCountData,
          danmakuCountData,
          unsubscribeCountData,
          fanViewProportionData,
          playSourceData,
          searchKeywordData,
          progressAnalysisData,
          diagnoseCompareData,
          fansPortraitData,
          audienceHotwordsData,
          commentWordCloudData,
          retentionAnalysisData,
          likeAnalysisData,
          exitAnalysisData,
          bulletAnalysisData
        ] = await Promise.all([
          getItemMetricsTrend(itemId, 1, 2, 'view_count'),
          getItemMetricsTrend(itemId, 1, 2, 'subscribe_rate'),
          getItemMetricsTrend(itemId, 1, 2, 'unsubscribe_rate'),
          getItemMetricsTrend(itemId, 2, 2, 'dislike_count'),
          getItemMetricsTrend(itemId, 2, 2, 'dislike_rate'),
          getItemMetricsTrend(itemId, 1, 2, 'subscribe_count'),
          getItemMetricsTrend(itemId, 2, 2, 'avg_view_second'),
          getItemMetricsTrend(itemId, 2, 2, 'cover_click_rate'),
          getItemMetricsTrend(itemId, 1, 2, 'like_count'),
          getItemMetricsTrend(itemId, 1, 2, 'comment_count'),
          getItemMetricsTrend(itemId, 1, 2, 'share_count'),
          getItemMetricsTrend(itemId, 1, 2, 'favorite_count'),
          getItemMetricsTrend(itemId, 1, 1, 'danmaku_count'),
          getItemMetricsTrend(itemId, 1, 2, 'unsubscribe_count'),
          getItemMetricsTrend(itemId, 2, 1, 'fan_view_proportion'),
          getItemPlaySource(itemId),
          getItemSearchKeyword(itemId),
          getItemProgressAnalysis(itemId),
          getItemDiagnoseCompare(itemId, 2),
          getItemFansPortrait(itemId),
          getItemAudienceHotwords(itemId),
          getItemCommentWordCloud(itemId),
          fetchItemRealtimeAnalytics(itemId, 1),
          fetchItemRealtimeAnalytics(itemId, 2),
          fetchItemExitAnalytics(itemId),
          getItemBulletAnalysis(itemId)
        ]);

                console.log('✅ 播放量趋势API调用成功:', viewTrendData);
                console.log('✅ 播放量趋势API调用成功:', viewTrendData);
                console.log('✅ 涨粉率趋势API调用成功:', subscribeRateData);
                console.log('✅ 脱粉率趋势API调用成功:', unsubscribeRateData);
                console.log('✅ 不感兴趣量趋势API调用成功:', dislikeCountData);
                console.log('✅ 不感兴趣率趋势API调用成功:', dislikeRateData);
                console.log('✅ 关注数趋势API调用成功:', subscribeTrendData);
                console.log('✅ 平均播放时长趋势API调用成功:', avgViewSecondData);
                console.log('✅ 封面点击率趋势API调用成功:', coverClickRateData);
                console.log('✅ 点赞量趋势API调用成功:', likeCountData);
                console.log('✅ 评论量趋势API调用成功:', commentCountData);
                console.log('✅ 分享量趋势API调用成功:', shareCountData);
                console.log('✅ 收藏量趋势API调用成功:', favoriteCountData);
                console.log('✅ 弹幕量趋势API调用成功:', danmakuCountData);
                console.log('✅ 脱粉量趋势API调用成功:', unsubscribeCountData);
                console.log('✅ 粉丝播放占比趋势API调用成功:', fanViewProportionData);
                console.log('✅ 播放来源API调用成功:', playSourceData);
                console.log('✅ 搜索关键词API调用成功:', searchKeywordData);
                console.log('✅ 播放进度分析API调用成功:', progressAnalysisData);
                console.log('✅ 作品诊断对比API调用成功:', diagnoseCompareData);
                console.log('✅ 粉丝画像API调用成功:', fansPortraitData);
                console.log('✅ 受众关注热词API调用成功:', audienceHotwordsData);
                console.log('✅ 评论热词API调用成功:', commentWordCloudData);

                // 合并所有数据源
                const combinedData = {
                  trend_map: {
                    ...viewTrendData.trend_map,
                    ...subscribeRateData.trend_map,
                    ...unsubscribeRateData.trend_map,
                    ...dislikeCountData.trend_map,
                    ...dislikeRateData.trend_map,
                    ...subscribeTrendData.trend_map,
                    ...avgViewSecondData.trend_map,
                    ...coverClickRateData.trend_map,
                    ...likeCountData.trend_map,
                    ...commentCountData.trend_map,
                    ...shareCountData.trend_map,
                    ...favoriteCountData.trend_map,
                    ...danmakuCountData.trend_map,
                    ...unsubscribeCountData.trend_map,
                    ...fanViewProportionData.trend_map
                  }
                };

                // 测试数据格式化（包含所有数据源）
                let markdownContent = formatMetricsTrendAsMarkdown(combinedData);
                markdownContent += formatPlaySourceAsMarkdown(playSourceData);
                markdownContent += formatSearchKeywordAsMarkdown(searchKeywordData);
                markdownContent += formatProgressAnalysisAsMarkdown(progressAnalysisData);
                markdownContent += formatItemDiagnoseCompareAsMarkdown(diagnoseCompareData);
                markdownContent += formatFansPortraitAsMarkdown(fansPortraitData);
                markdownContent += formatAudienceHotwordsAsMarkdown(audienceHotwordsData);
                markdownContent += formatCommentWordCloudAsMarkdown(commentWordCloudData);
                markdownContent += formatRetentionAnalysisAsMarkdown(retentionAnalysisData.data);
                markdownContent += formatLikeAnalysisAsMarkdown(likeAnalysisData.data);
                markdownContent += formatExitAnalysisAsMarkdown(exitAnalysisData.data);
                markdownContent += formatBulletAnalysisAsMarkdown(bulletAnalysisData);
                console.log('✅ 数据格式化成功');
                console.log('📄 生成的Markdown内容:');
                console.log(markdownContent);

                // 下载Markdown文件
                const itemDesc = getItemDescription(itemId);
                const descPrefix = itemDesc.substring(0, 20);
                const sanitizedDesc = sanitizeFileName(descPrefix);
                const filename = `${sanitizedDesc}_完整数据分析.md`;

                const blob = new Blob([markdownContent], { type: 'text/markdown;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                console.log(`📥 完整数据趋势文件已下载: ${filename}`);

                return {
                  success: true,
                  data: combinedData,
                  playSource: playSourceData,
                  searchKeyword: searchKeywordData,
                  progressAnalysis: progressAnalysisData,
                  diagnoseCompare: diagnoseCompareData,
                  fansPortrait: fansPortraitData,
                  audienceHotwords: audienceHotwordsData,
                  commentWordCloud: commentWordCloudData,
                  retentionAnalysis: retentionAnalysisData,
                  likeAnalysis: likeAnalysisData,
                  exitAnalysis: exitAnalysisData,
                  bulletAnalysis: bulletAnalysisData,
                  markdown: markdownContent
                };
      } catch (error) {
        console.error('❌ 作品数据趋势API测试失败:', error);
        return { success: false, error: error.message };
      }
    }

        /**
     * 获取作品播放来源数据
     * @param {string} itemIdStr - 作品ID字符串
     * @returns {Promise<Object>} 播放来源数据
     */
    async function getItemPlaySource(itemIdStr) {
      try {
        // 验证item_id格式（应该是数字字符串）
        if (!itemIdStr || !/^\d+$/.test(itemIdStr)) {
          throw new Error(`无效的item_id格式: ${itemIdStr}`);
        }

        const apiUrl = `https://creator.douyin.com/janus/douyin/creator/data/item/play/source?aid=2906&app_name=aweme_creator_platform&device_platform=web&referer=https:%2F%2Fcreator.douyin.com%2Fcreator-micro%2Fdata-center%2Fcontent&user_agent=Mozilla%2F5.0+(Macintosh%3B+Intel+Mac+OS+X+10_15_7)+AppleWebKit%2F537.36+(KHTML,+like+Gecko)+Chrome%2F138.0.0.0+Safari%2F537.36&cookie_enabled=true&screen_width=3360&screen_height=1890&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0+(Macintosh%3B+Intel+Mac+OS+X+10_15_7)+AppleWebKit%2F537.36+(KHTML,+like+Gecko)+Chrome%2F138.0.0.0+Safari%2F537.36&browser_online=true&timezone_name=Asia%2FShanghai&item_id=${itemIdStr}`;

        const response = await fetch(apiUrl, {
          method: "GET",
          mode: "cors",
          credentials: "include",
          headers: {
            'Accept': 'application/json',
            'Agw-Js-Conv': 'str'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP错误: ${response.status}`);
        }

        const data = await response.json();

        if (data.status_code !== 0) {
          throw new Error(`API错误: ${data.status_msg || '未知错误'}`);
        }

        return data;
      } catch (error) {
        console.error('获取播放来源数据失败:', error);
        throw error;
      }
    }

    /**
     * 格式化播放来源数据为Markdown
     * @param {Object} playSourceData - 播放来源数据
     * @returns {string} Markdown格式的播放来源报告
     */
    function formatPlaySourceAsMarkdown(playSourceData) {
      if (!playSourceData || !playSourceData.play_source) {
        return '';
      }

      let markdown = '# 播放来源分析\n\n';

      // 来源名称映射
      const sourceNameMap = {
        'homepage_hot': '推荐页',
        'follow': '关注页',
        'search': '搜索',
        'other': '其他',
        'homepage': '个人主页',
        'message': '消息页',
        'familiar': '熟人'
      };

      // 按播放占比排序
      const sortedSources = playSourceData.play_source.sort((a, b) => b.value - a.value);

      // 生成表格
      markdown += '## 流量来源分布\n\n';
      markdown += '| 来源 | 播放占比 | 对比7日 |\n';
      markdown += '|------|----------|----------|\n';

      let totalValue = 0;
      sortedSources.forEach(source => {
        const sourceName = sourceNameMap[source.key] || source.key;
        const percentage = (source.value * 100).toFixed(1) + '%';
        const historyDiff = source.history_difference;
        let diffDisplay = '';

        if (historyDiff > 0) {
          diffDisplay = `+${(historyDiff * 100).toFixed(1)}%`;
        } else if (historyDiff < 0) {
          diffDisplay = `${(historyDiff * 100).toFixed(1)}%`;
        } else {
          diffDisplay = '0%';
        }

        totalValue += source.value;
        markdown += `| ${sourceName} | ${percentage} | ${diffDisplay} |\n`;
      });

      // 添加统计摘要
      markdown += '\n## 播放来源统计摘要\n\n';

      // 找出最大和最小来源
      const maxSource = sortedSources[0];
      const minSource = sortedSources[sortedSources.length - 1];
      const maxSourceName = sourceNameMap[maxSource.key] || maxSource.key;
      const minSourceName = sourceNameMap[minSource.key] || minSource.key;

      markdown += `- **主要流量来源**: ${maxSourceName} (${(maxSource.value * 100).toFixed(1)}%)\n`;
      markdown += `- **最小流量来源**: ${minSourceName} (${(minSource.value * 100).toFixed(1)}%)\n`;
      markdown += `- **总来源数量**: ${sortedSources.length}个\n`;
      markdown += `- **数据覆盖率**: ${(totalValue * 100).toFixed(1)}%\n\n`;

      // 增长趋势分析
      const positiveGrowth = sortedSources.filter(s => s.history_difference > 0);
      const negativeGrowth = sortedSources.filter(s => s.history_difference < 0);

      if (positiveGrowth.length > 0 || negativeGrowth.length > 0) {
        markdown += '## 7日变化趋势\n\n';

        if (positiveGrowth.length > 0) {
          markdown += '### 增长来源\n\n';
          positiveGrowth.forEach(source => {
            const sourceName = sourceNameMap[source.key] || source.key;
            const growth = (source.history_difference * 100).toFixed(1);
            markdown += `- **${sourceName}**: +${growth}%\n`;
          });
          markdown += '\n';
        }

        if (negativeGrowth.length > 0) {
          markdown += '### 下降来源\n\n';
          negativeGrowth.forEach(source => {
            const sourceName = sourceNameMap[source.key] || source.key;
            const decline = (source.history_difference * 100).toFixed(1);
            markdown += `- **${sourceName}**: ${decline}%\n`;
          });
          markdown += '\n';
        }
      }

      return markdown;
    }


    /**
 * 获取作品搜索关键词数据
 * @param {string} itemIdStr - 作品ID字符串
 * @returns {Promise<Object>} 搜索关键词数据
 */
    async function getItemSearchKeyword(itemIdStr) {
      try {
        // 验证item_id格式（应该是数字字符串）
        if (!itemIdStr || !/^\d+$/.test(itemIdStr)) {
          throw new Error(`无效的item_id格式: ${itemIdStr}`);
        }

        const apiUrl = `https://creator.douyin.com/janus/douyin/creator/data/item_analysis/search/keyword?aid=2906&app_name=aweme_creator_platform&device_platform=web&referer=https:%2F%2Fcreator.douyin.com%2Fcreator-micro%2Fdata-center%2Fcontent&user_agent=Mozilla%2F5.0+(Macintosh%3B+Intel+Mac+OS+X+10_15_7)+AppleWebKit%2F537.36+(KHTML,+like+Gecko)+Chrome%2F138.0.0.0+Safari%2F537.36&cookie_enabled=true&screen_width=3360&screen_height=1890&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0+(Macintosh%3B+Intel+Mac+OS+X+10_15_7)+AppleWebKit%2F537.36+(KHTML,+like+Gecko)+Chrome%2F138.0.0.0+Safari%2F537.36&browser_online=true&timezone_name=Asia%2FShanghai&id=${itemIdStr}`;

        const response = await fetch(apiUrl, {
          method: "GET",
          mode: "cors",
          credentials: "include",
          headers: {
            'Accept': 'application/json',
            'Agw-Js-Conv': 'str'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP错误: ${response.status}`);
        }

        const data = await response.json();

        if (data.status_code !== 0) {
          throw new Error(`API错误: ${data.status_msg || '未知错误'}`);
        }

        return data;
      } catch (error) {
        console.error('获取搜索关键词数据失败:', error);
        throw error;
      }
    }

    /**
     * 格式化搜索关键词数据为Markdown
     * @param {Object} searchKeywordData - 搜索关键词数据
     * @returns {string} Markdown格式的搜索关键词报告
     */
    function formatSearchKeywordAsMarkdown(searchKeywordData) {
      if (!searchKeywordData || (!searchKeywordData.show_from && !searchKeywordData.inspire_search)) {
        return '';
      }

      let markdown = '# 搜索关键词分析\n\n';

      // 处理用户通过这些词看到作品的数据
      if (searchKeywordData.show_from && searchKeywordData.show_from.length > 0) {
        markdown += '## 用户通过这些词看到作品\n\n';
        markdown += '| 排名 | 关键词 | 占比 |\n';
        markdown += '|------|--------|------|\n';

        // 按占比排序
        const sortedShowFrom = [...searchKeywordData.show_from].sort((a, b) => b.percent - a.percent);

        sortedShowFrom.forEach((item, index) => {
          const rank = index + 1;
          const keyword = item.keyword;
          const percentage = (item.percent * 100).toFixed(1) + '%';
          markdown += `| ${rank} | ${keyword} | ${percentage} |\n`;
        });

        markdown += '\n';

        // 添加统计摘要
        const totalShowFromPercent = sortedShowFrom.reduce((sum, item) => sum + item.percent, 0);
        const topKeyword = sortedShowFrom[0];

        markdown += '### 统计摘要\n\n';
        markdown += `- **主要搜索词**: ${topKeyword.keyword} (${(topKeyword.percent * 100).toFixed(1)}%)\n`;
        markdown += `- **关键词数量**: ${sortedShowFrom.length}个\n`;
        markdown += `- **数据覆盖率**: ${(totalShowFromPercent * 100).toFixed(1)}%\n\n`;
      }

      // 处理用户看完作品后常搜的词
      if (searchKeywordData.inspire_search && searchKeywordData.inspire_search.length > 0) {
        markdown += '## 用户看完作品后常搜的词\n\n';
        markdown += '| 排名 | 关键词 | 占比 |\n';
        markdown += '|------|--------|------|\n';

        // 按占比排序
        const sortedInspireSearch = [...searchKeywordData.inspire_search].sort((a, b) => b.percent - a.percent);

        sortedInspireSearch.forEach((item, index) => {
          const rank = index + 1;
          const keyword = item.keyword;
          const percentage = (item.percent * 100).toFixed(1) + '%';
          markdown += `| ${rank} | ${keyword} | ${percentage} |\n`;
        });

        markdown += '\n';

        // 添加统计摘要
        const totalInspirePercent = sortedInspireSearch.reduce((sum, item) => sum + item.percent, 0);
        const topInspireKeyword = sortedInspireSearch[0];

        markdown += '### 统计摘要\n\n';
        markdown += `- **主要激发搜索词**: ${topInspireKeyword.keyword} (${(topInspireKeyword.percent * 100).toFixed(1)}%)\n`;
        markdown += `- **激发关键词数量**: ${sortedInspireSearch.length}个\n`;
        markdown += `- **数据覆盖率**: ${(totalInspirePercent * 100).toFixed(1)}%\n\n`;
      }

      // 添加综合分析
      if (searchKeywordData.show_from && searchKeywordData.inspire_search) {
        markdown += '## 搜索行为分析\n\n';

        // 找出共同关键词
        const showFromKeywords = new Set(searchKeywordData.show_from.map(item => item.keyword));
        const inspireKeywords = new Set(searchKeywordData.inspire_search.map(item => item.keyword));
        const commonKeywords = [...showFromKeywords].filter(keyword => inspireKeywords.has(keyword));

        if (commonKeywords.length > 0) {
          markdown += '### 共同关键词\n\n';
          markdown += '以下关键词既是用户发现作品的途径，也是观看后的搜索热点：\n\n';
          commonKeywords.forEach(keyword => {
            const showFromItem = searchKeywordData.show_from.find(item => item.keyword === keyword);
            const inspireItem = searchKeywordData.inspire_search.find(item => item.keyword === keyword);
            markdown += `- **${keyword}**: 发现占比${(showFromItem.percent * 100).toFixed(1)}%, 激发占比${(inspireItem.percent * 100).toFixed(1)}%\n`;
          });
          markdown += '\n';
        }

        // 分析搜索转化效果
        const avgShowFromPercent = searchKeywordData.show_from.reduce((sum, item) => sum + item.percent, 0) / searchKeywordData.show_from.length;
        const avgInspirePercent = searchKeywordData.inspire_search.reduce((sum, item) => sum + item.percent, 0) / searchKeywordData.inspire_search.length;

        markdown += '### 搜索转化分析\n\n';
        markdown += `- **平均发现占比**: ${(avgShowFromPercent * 100).toFixed(1)}%\n`;
        markdown += `- **平均激发占比**: ${(avgInspirePercent * 100).toFixed(1)}%\n`;
        markdown += `- **内容激发度**: ${avgInspirePercent > avgShowFromPercent ? '高' : '中等'} (${avgInspirePercent > avgShowFromPercent ? '作品激发了更多搜索行为' : '作品主要通过搜索被发现'})\n\n`;
      }

      return markdown;
    }

    /**
 * 获取作品播放进度分析数据
 * @param {string} itemIdStr - 作品ID字符串
 * @returns {Promise<Object>} 播放进度分析数据
 */
    async function getItemProgressAnalysis(itemIdStr) {
      try {
        // 验证item_id格式（应该是数字字符串）
        if (!itemIdStr || !/^\d+$/.test(itemIdStr)) {
          throw new Error(`无效的item_id格式: ${itemIdStr}`);
        }

        const apiUrl = `https://creator.douyin.com/janus/douyin/creator/bff/data/progress/analysis/v2?aid=2906&app_name=aweme_creator_platform&device_platform=web&referer=https:%2F%2Fcreator.douyin.com%2Fcreator-micro%2Fdata-center%2Fcontent&user_agent=Mozilla%2F5.0+(Macintosh%3B+Intel+Mac+OS+X+10_15_7)+AppleWebKit%2F537.36+(KHTML,+like+Gecko)+Chrome%2F138.0.0.0+Safari%2F537.36&cookie_enabled=true&screen_width=3360&screen_height=1890&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0+(Macintosh%3B+Intel+Mac+OS+X+10_15_7)+AppleWebKit%2F537.36+(KHTML,+like+Gecko)+Chrome%2F138.0.0.0+Safari%2F537.36&browser_online=true&timezone_name=Asia%2FShanghai&item_id=${itemIdStr}`;

        const response = await fetch(apiUrl, {
          method: "GET",
          mode: "cors",
          credentials: "include",
          headers: {
            'Accept': 'application/json',
            'Agw-Js-Conv': 'str'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP错误: ${response.status}`);
        }

        const data = await response.json();

        return data;
      } catch (error) {
        console.error('获取播放进度分析数据失败:', error);
        throw error;
      }
    }

    /**
     * 格式化播放进度分析数据为Markdown
     * @param {Object} progressData - 播放进度分析数据
     * @returns {string} Markdown格式的播放进度分析报告
     */
    function formatProgressAnalysisAsMarkdown(progressData) {
      if (!progressData || (!progressData.jump_backward && !progressData.jump_forward)) {
        return '';
      }

      let markdown = '# 播放进度分析\n\n';

      // 检查是否有回看数据和跳看数据
      const hasBackward = progressData.jump_backward && progressData.jump_backward.length > 0;
      const hasForward = progressData.jump_forward && progressData.jump_forward.length > 0;

      if (!hasBackward && !hasForward) {
        return '# 播放进度分析\n\n暂无播放进度分析数据\n\n';
      }

      // 生成表格
      markdown += '## 播放行为分析\n\n';
      markdown += '| 时间段(秒) | 回看占比 | 跳看占比 |\n';
      markdown += '|------------|----------|----------|\n';

      // 创建时间点映射
      const timeMap = new Map();

      // 收集回看数据（如果存在）
      if (hasBackward) {
        progressData.jump_backward.forEach(point => {
          const timeKey = parseInt(point.key);
          if (!isNaN(timeKey)) {
            timeMap.set(timeKey, { backward: point.value, forward: null });
          }
        });
      }

      // 收集跳看数据（如果存在）
      if (hasForward) {
        progressData.jump_forward.forEach(point => {
          const timeKey = parseInt(point.key);
          if (!isNaN(timeKey)) {
            const existing = timeMap.get(timeKey) || { backward: null, forward: null };
            existing.forward = point.value;
            timeMap.set(timeKey, existing);
          }
        });
      }

      // 按时间排序并生成表格行
      const sortedTimes = Array.from(timeMap.keys()).sort((a, b) => a - b);

      sortedTimes.forEach(time => {
        const data = timeMap.get(time);
        const backwardPercent = data.backward !== null ? (data.backward * 100).toFixed(2) + '%' : '-';
        const forwardPercent = data.forward !== null ? (data.forward * 100).toFixed(2) + '%' : '-';
        markdown += `| ${time} | ${backwardPercent} | ${forwardPercent} |\n`;
      });

      markdown += '\n';

      // 添加统计分析
      markdown += '## 播放行为统计\n\n';

      if (hasBackward) {
        const backwardValues = progressData.jump_backward.map(p => p.value).filter(v => v > 0);
        if (backwardValues.length > 0) {
          const avgBackward = backwardValues.reduce((sum, val) => sum + val, 0) / backwardValues.length;
          const maxBackward = Math.max(...backwardValues);
          const maxBackwardTime = progressData.jump_backward.find(p => p.value === maxBackward)?.key;

          markdown += '### 回看行为分析\n\n';
          markdown += `- **平均回看率**: ${(avgBackward * 100).toFixed(2)}%\n`;
          markdown += `- **最高回看率**: ${(maxBackward * 100).toFixed(2)}% (第${maxBackwardTime}秒)\n`;
          markdown += `- **有效回看点**: ${backwardValues.length}个\n\n`;
        }
      }

      if (hasForward) {
        const forwardValues = progressData.jump_forward.map(p => p.value).filter(v => v > 0);
        if (forwardValues.length > 0) {
          const avgForward = forwardValues.reduce((sum, val) => sum + val, 0) / forwardValues.length;
          const maxForward = Math.max(...forwardValues);
          const maxForwardTime = progressData.jump_forward.find(p => p.value === maxForward)?.key;

          markdown += '### 跳看行为分析\n\n';
          markdown += `- **平均跳看率**: ${(avgForward * 100).toFixed(2)}%\n`;
          markdown += `- **最高跳看率**: ${(maxForward * 100).toFixed(2)}% (第${maxForwardTime}秒)\n`;
          markdown += `- **有效跳看点**: ${forwardValues.length}个\n\n`;
        }
      }

      // 内容质量分析
      if (hasBackward && hasForward) {
        markdown += '## 内容质量洞察\n\n';

        // 找出高回看率的时间段（可能是精彩内容）
        const highBackwardPoints = progressData.jump_backward
          .filter(p => p.value > 0.005) // 回看率超过0.5%
          .sort((a, b) => b.value - a.value)
          .slice(0, 3);

        if (highBackwardPoints.length > 0) {
          markdown += '### 精彩内容时段\n\n';
          markdown += '以下时段回看率较高，可能包含精彩内容：\n\n';
          highBackwardPoints.forEach((point, index) => {
            markdown += `${index + 1}. **第${point.key}秒**: 回看率${(point.value * 100).toFixed(2)}%\n`;
          });
          markdown += '\n';
        }

        // 找出高跳看率的时间段（可能需要优化）
        const highForwardPoints = progressData.jump_forward
          .filter(p => p.value > 0.15) // 跳看率超过15%
          .sort((a, b) => b.value - a.value)
          .slice(0, 3);

        if (highForwardPoints.length > 0) {
          markdown += '### 需要优化的时段\n\n';
          markdown += '以下时段跳看率较高，建议优化内容：\n\n';
          highForwardPoints.forEach((point, index) => {
            markdown += `${index + 1}. **第${point.key}秒**: 跳看率${(point.value * 100).toFixed(2)}%\n`;
          });
          markdown += '\n';
        }

        // 计算整体观看质量
        const totalBackward = progressData.jump_backward.reduce((sum, p) => sum + p.value, 0);
        const totalForward = progressData.jump_forward.reduce((sum, p) => sum + p.value, 0);
        const engagementRatio = totalBackward / (totalForward + 0.001); // 避免除零

        markdown += '### 整体观看质量\n\n';
        if (engagementRatio > 0.1) {
          markdown += '- **观看质量**: 优秀 (用户经常回看精彩内容)\n';
        } else if (engagementRatio > 0.05) {
          markdown += '- **观看质量**: 良好 (有一定的回看行为)\n';
        } else {
          markdown += '- **观看质量**: 一般 (回看行为较少)\n';
        }

        markdown += `- **回看/跳看比**: ${engagementRatio.toFixed(3)}\n`;
        markdown += `- **总回看率**: ${(totalBackward * 100).toFixed(2)}%\n`;
        markdown += `- **总跳看率**: ${(totalForward * 100).toFixed(2)}%\n\n`;
      }

      return markdown;
    }

        /**
     * 获取作品诊断对比数据
     * @param {string} itemIdStr - 作品ID字符串
     * @param {number} selectedMetricCount - 选择的指标数量，默认为2
     * @returns {Promise<Object>} 作品诊断对比数据
     */
        async function getItemDiagnoseCompare(itemIdStr, selectedMetricCount = 2) {
          try {
            // 验证item_id格式（应该是数字字符串）
            if (!itemIdStr || !/^\d+$/.test(itemIdStr)) {
              throw new Error(`无效的item_id格式: ${itemIdStr}`);
            }

            const apiUrl = `https://creator.douyin.com/janus/douyin/creator/data/diagnose/item_compare?item_id=${itemIdStr}&selected_metric_count=${selectedMetricCount}`;

            const response = await fetch(apiUrl, {
              method: "GET",
              mode: "cors",
              credentials: "include",
              headers: {
                'Accept': 'application/json',
                'Agw-Js-Conv': 'str'
              }
            });

            if (!response.ok) {
              throw new Error(`HTTP错误: ${response.status}`);
            }

            const data = await response.json();

            if (data.status_code !== 0) {
              throw new Error(`API错误: ${data.status_msg || '未知错误'}`);
            }

            return data;
          } catch (error) {
            console.error('获取作品诊断对比数据失败:', error);
            throw error;
          }
        }

        /**
         * 格式化作品诊断对比数据为Markdown
         * @param {Object} diagnoseData - 作品诊断对比数据
         * @returns {string} Markdown格式的作品诊断报告
         */
        function formatItemDiagnoseCompareAsMarkdown(diagnoseData) {
          if (!diagnoseData || !diagnoseData.metrics) {
            return '';
          }

          let markdown = '# 作品诊断分析\n\n';

          // 内容吸引力指标
          const attractionMetrics = [
            'cover_click_rate',    // 封面点击率
            'avg_view_second',     // 平均播放时长
            'completion_rate',     // 完播率
            'bounce_rate_2s',      // 2秒跳出率
            'avg_view_proportion', // 平均播放占比
            'completion_rate_5s'   // 5秒完播率
          ];

          // 观众参与度指标
          const engagementMetrics = [
            'like_rate',      // 点赞率
            'comment_rate',   // 评论率
            'share_rate',     // 分享率
            'favorite_rate',  // 收藏率
            'subscribe_rate', // 吸粉率
            'dislike_rate'    // 不感兴趣率
          ];

          // 内容吸引力部分
          markdown += '## 内容吸引力\n\n';
          markdown += '| 指标名称 | 当前值 | 对比期变化 | 建议 |\n';
          markdown += '|---------|--------|------------|------|\n';

          attractionMetrics.forEach(metricName => {
            const metric = diagnoseData.metrics.find(m => m.name === metricName);
            if (metric) {
              const currentValue = formatMetricValue(metric.self_value, metricName);
              const changeText = formatChangeText(metric.change_ratio, metric.change_type);
              const suggestion = metric.suggestion || '-';

              markdown += `| ${metric.name_desc} | ${currentValue} | ${changeText} | ${suggestion} |\n`;
            }
          });

          markdown += '\n';

          // 观众参与度部分
          markdown += '## 观众参与度\n\n';
          markdown += '| 指标名称 | 当前值 | 对比期变化 | 建议 |\n';
          markdown += '|---------|--------|------------|------|\n';

          engagementMetrics.forEach(metricName => {
            const metric = diagnoseData.metrics.find(m => m.name === metricName);
            if (metric) {
              const currentValue = formatMetricValue(metric.self_value, metricName);
              const changeText = formatChangeText(metric.change_ratio, metric.change_type);
              const suggestion = metric.suggestion || '-';

              markdown += `| ${metric.name_desc} | ${currentValue} | ${changeText} | ${suggestion} |\n`;
            }
          });

          markdown += '\n';

          // 播放量对比
          if (diagnoseData.view_count_metric) {
            const viewMetric = diagnoseData.view_count_metric;
            markdown += '## 播放量表现\n\n';
            markdown += `- **当前播放量**: ${parseInt(viewMetric.self_value).toLocaleString()}\n`;
            markdown += `- **对比期平均**: ${parseFloat(viewMetric.compare_value).toLocaleString()}\n`;
            markdown += `- **变化幅度**: ${formatChangeText(viewMetric.change_ratio, viewMetric.change_type)}\n\n`;
          }

          // 重点关注指标
          if (diagnoseData.selected_metrics && diagnoseData.selected_metrics.length > 0) {
            markdown += '## 重点关注指标\n\n';

            diagnoseData.selected_metrics.forEach(metricName => {
              const metric = diagnoseData.metrics.find(m => m.name === metricName);
              if (metric) {
                const currentValue = formatMetricValue(metric.self_value, metricName);
                const changeText = formatChangeText(metric.change_ratio, metric.change_type);

                markdown += `### ${metric.name_desc}\n\n`;
                markdown += `- **当前值**: ${currentValue}\n`;
                markdown += `- **对比变化**: ${changeText}\n`;
                if (metric.suggestion) {
                  markdown += `- **优化建议**: ${metric.suggestion}\n`;
                }
                markdown += '\n';
              }
            });
          }

          // 对比作品信息
          if (diagnoseData.compare_items && diagnoseData.compare_items.length > 0) {
            markdown += '## 对比作品信息\n\n';
            markdown += `- **对比时间范围**: 近${diagnoseData.compare_hour}小时\n`;
            markdown += `- **对比作品数量**: ${diagnoseData.compare_items.length}个\n`;
            markdown += `- **对比作品ID**: ${diagnoseData.compare_item_ids.join(', ')}\n\n`;
          }

          // 作品基本信息
          if (diagnoseData.item) {
            const item = diagnoseData.item;
            markdown += '## 当前作品信息\n\n';
            markdown += `- **作品ID**: ${item.id}\n`;
            markdown += `- **发布时间**: ${new Date(parseInt(item.create_time) * 1000).toLocaleString()}\n`;
            if (item.description) {
              markdown += `- **作品描述**: ${item.description}\n`;
            }
            if (item.first_label && item.first_label.Name) {
              markdown += `- **一级标签**: ${item.first_label.Name}\n`;
            }
            if (item.second_label && item.second_label.Name) {
              markdown += `- **二级标签**: ${item.second_label.Name}\n`;
            }
            markdown += '\n';
          }

          return markdown;
        }

        /**
         * 格式化指标值
         * @param {string} value - 指标值
         * @param {string} metricName - 指标名称
         * @returns {string} 格式化后的值
         */
        function formatMetricValue(value, metricName) {
          const numValue = parseFloat(value);

          // 时长类指标（秒）
          if (metricName === 'avg_view_second') {
            return `${numValue.toFixed(0)}秒`;
          }

          // 比率类指标（百分比）
          if (metricName.includes('rate') || metricName.includes('proportion') || metricName.includes('completion')) {
            return `${(numValue * 100).toFixed(2)}%`;
          }

          // 数量类指标
          if (metricName.includes('count')) {
            return numValue.toLocaleString();
          }

          return value;
        }

        /**
         * 格式化变化文本
         * @param {number} changeRatio - 变化比例
         * @param {number} changeType - 变化类型 (0: 上升, 1: 下降, 2: 其他)
         * @returns {string} 格式化后的变化文本
         */
        function formatChangeText(changeRatio, changeType) {
          const percentage = (Math.abs(changeRatio) * 100).toFixed(2);

          if (changeType === 0) {
            return `↗ +${percentage}%`;
          } else if (changeType === 1 || changeType === 2) {
            return `↘ -${percentage}%`;
          } else {
            return `${changeRatio > 0 ? '↗ +' : '↘ '}${percentage}%`;
          }
        }

    /**
     * 获取作品粉丝画像数据
     * @param {string} itemIdStr - 作品ID字符串
     * @returns {Promise<Object>} 粉丝画像数据
     */
    async function getItemFansPortrait(itemIdStr) {
      try {
        // 验证item_id格式（应该是数字字符串）
        if (!itemIdStr || !/^\d+$/.test(itemIdStr)) {
          throw new Error(`无效的item_id格式: ${itemIdStr}`);
        }

        const apiUrl = `https://creator.douyin.com/janus/douyin/creator/data/fans/item/portrait?aid=2906&app_name=aweme_creator_platform&device_platform=web&referer=https:%2F%2Fcreator.douyin.com%2Fcreator-micro%2Fdata-center%2Fcontent&user_agent=Mozilla%2F5.0+(Macintosh%3B+Intel+Mac+OS+X+10_15_7)+AppleWebKit%2F537.36+(KHTML,+like+Gecko)+Chrome%2F138.0.0.0+Safari%2F537.36&cookie_enabled=true&screen_width=3360&screen_height=1890&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0+(Macintosh%3B+Intel+Mac+OS+X+10_15_7)+AppleWebKit%2F537.36+(KHTML,+like+Gecko)+Chrome%2F138.0.0.0+Safari%2F537.36&browser_online=true&timezone_name=Asia%2FShanghai&user_id=&item_id=${itemIdStr}`;

        const response = await fetch(apiUrl, {
          method: "GET",
          mode: "cors",
          credentials: "include",
          headers: {
            'Accept': 'application/json',
            'Agw-Js-Conv': 'str'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP错误: ${response.status}`);
        }

        const data = await response.json();

        if (data.status_code !== 0) {
          throw new Error(`API错误: ${data.status_msg || '未知错误'}`);
        }

        return data;
      } catch (error) {
        console.error('获取粉丝画像数据失败:', error);
        throw error;
      }
    }

    /**
     * 格式化粉丝画像数据为Markdown
     * @param {Object} portraitData - 粉丝画像数据
     * @returns {string} Markdown格式的粉丝画像报告
     */
    function formatFansPortraitAsMarkdown(portraitData) {
      if (!portraitData) {
        return '';
      }

      let markdown = '# 观看用户画像分析\n\n';

      // 性别分析
      if (portraitData.gender && portraitData.gender.ratio_list) {
        markdown += '## 性别分析\n\n';
        markdown += '| 性别 | 占比 | TGI指数 |\n';
        markdown += '|------|------|--------|\n';

        const genderMap = {
          'male': '男性',
          'female': '女性'
        };

        portraitData.gender.ratio_list.forEach(item => {
          const genderName = genderMap[item.key] || item.key;
          const ratio = (item.value * 100).toFixed(1) + '%';
          const tgiItem = portraitData.gender.tgi_list.find(t => t.key === item.key);
          const tgi = tgiItem ? tgiItem.value.toFixed(1) : '-';
          markdown += `| ${genderName} | ${ratio} | ${tgi} |\n`;
        });

        markdown += '\n';
      }

      // 年龄分析
      if (portraitData.age && portraitData.age.ratio_list) {
        markdown += '## 年龄分析\n\n';
        markdown += '| 年龄段 | 占比 | TGI指数 |\n';
        markdown += '|--------|------|--------|\n';

        portraitData.age.ratio_list.forEach(item => {
          const ageRange = item.key;
          const ratio = (item.value * 100).toFixed(1) + '%';
          const tgiItem = portraitData.age.tgi_list.find(t => t.key === item.key);
          const tgi = tgiItem ? tgiItem.value.toFixed(1) : '-';
          markdown += `| ${ageRange} | ${ratio} | ${tgi} |\n`;
        });

        // 找出主要年龄段
        const mainAgeGroup = portraitData.age.ratio_list.reduce((max, current) =>
          current.value > max.value ? current : max
        );
        markdown += `\n**主要年龄段**: ${mainAgeGroup.key} (${(mainAgeGroup.value * 100).toFixed(1)}%)\n\n`;
      }

      // 地域分析 - 省份
      if (portraitData.province && portraitData.province.ratio_list) {
        markdown += '## 地域分析\n\n';

        // 按占比排序，取前10名
        const topProvinces = [...portraitData.province.ratio_list]
          .sort((a, b) => b.value - a.value)
          .slice(0, 10);

        markdown += '### 主要省份分布（前10名）\n\n';
        markdown += '| 排名 | 省份 | 占比 | TGI指数 |\n';
        markdown += '|------|------|------|--------|\n';

        topProvinces.forEach((item, index) => {
          const rank = index + 1;
          const province = item.key;
          const ratio = (item.value * 100).toFixed(2) + '%';
          const tgiItem = portraitData.province.tgi_list.find(t => t.key === item.key);
          const tgi = tgiItem ? tgiItem.value.toFixed(1) : '-';
          markdown += `| ${rank} | ${province} | ${ratio} | ${tgi} |\n`;
        });

        markdown += '\n';
      }

      // 城市等级分析
      if (portraitData.city_level && portraitData.city_level.ratio_list) {
        markdown += '### 城市等级分布\n\n';
        markdown += '| 城市等级 | 占比 | TGI指数 |\n';
        markdown += '|----------|------|--------|\n';

        // 按占比排序
        const sortedCityLevels = [...portraitData.city_level.ratio_list]
          .sort((a, b) => b.value - a.value);

        sortedCityLevels.forEach(item => {
          const cityLevel = item.key;
          const ratio = (item.value * 100).toFixed(1) + '%';
          const tgiItem = portraitData.city_level.tgi_list.find(t => t.key === item.key);
          const tgi = tgiItem ? tgiItem.value.toFixed(1) : '-';
          markdown += `| ${cityLevel} | ${ratio} | ${tgi} |\n`;
        });

        markdown += '\n';
      }

      // 职业分析
      if (portraitData.career && portraitData.career.length > 0) {
        markdown += '## 职业分析\n\n';

        // 按占比排序，取前10名
        const topCareers = [...portraitData.career]
          .sort((a, b) => b.value - a.value)
          .slice(0, 10);

        const careerMap = {
          'white_collar': '白领',
          'blue_collar_industry': '蓝领工业',
          'blue_collar_service': '蓝领服务业',
          'retail': '零售业',
          'public_servant': '公务员',
          'driver': '司机',
          'restaurant': '餐饮业',
          'building_worker': '建筑工人',
          'repair_worker': '维修工人',
          'inhouse_student': '在校学生',
          'it': 'IT行业',
          'delivery_man': '快递员',
          'finance': '金融业',
          'not_work': '无业',
          'agriculture': '农业',
          'teacher': '教师',
          'medical_staff': '医护人员'
        };

        markdown += '### 主要职业分布（前10名）\n\n';
        markdown += '| 排名 | 职业 | 占比 |\n';
        markdown += '|------|------|------|\n';

        topCareers.forEach((item, index) => {
          const rank = index + 1;
          const career = careerMap[item.key] || item.key;
          const ratio = (item.value * 100).toFixed(2) + '%';
          markdown += `| ${rank} | ${career} | ${ratio} |\n`;
        });

        markdown += '\n';
      }

      // 活跃度分析
      if (portraitData.active && portraitData.active.length > 0) {
        markdown += '## 用户活跃度分析\n\n';
        markdown += '| 活跃度等级 | 占比 |\n';
        markdown += '|------------|------|\n';

        const activeMap = {
          '1': '低活跃',
          '2': '中低活跃',
          '3': '中等活跃',
          '4': '高活跃',
          '-1': '未知'
        };

        // 按活跃度等级排序
        const sortedActive = [...portraitData.active]
          .sort((a, b) => parseInt(a.key) - parseInt(b.key));

        sortedActive.forEach(item => {
          const activeLevel = activeMap[item.key] || `等级${item.key}`;
          const ratio = (item.value * 100).toFixed(1) + '%';
          markdown += `| ${activeLevel} | ${ratio} |\n`;
        });

        // 找出主要活跃度
        const mainActiveLevel = portraitData.active.reduce((max, current) =>
          current.value > max.value ? current : max
        );
        const mainActiveName = activeMap[mainActiveLevel.key] || `等级${mainActiveLevel.key}`;
        markdown += `\n**主要活跃度**: ${mainActiveName} (${(mainActiveLevel.value * 100).toFixed(1)}%)\n\n`;
      }

      // 新老用户分析
      if (portraitData.new_user && portraitData.new_user.length > 0) {
        markdown += '## 新老用户分析\n\n';
        markdown += '| 用户类型 | 占比 |\n';
        markdown += '|----------|------|\n';

        portraitData.new_user.forEach(item => {
          const userType = item.key;
          const ratio = (item.value * 100).toFixed(2) + '%';
          markdown += `| ${userType} | ${ratio} |\n`;
        });

        markdown += '\n';
      }

      // 用户画像总结
      markdown += '## 用户画像总结\n\n';

      // 性别总结
      if (portraitData.gender && portraitData.gender.ratio_list) {
        const maleRatio = portraitData.gender.ratio_list.find(g => g.key === 'male');
        const femaleRatio = portraitData.gender.ratio_list.find(g => g.key === 'female');
        if (maleRatio && femaleRatio) {
          const dominantGender = maleRatio.value > femaleRatio.value ? '男性' : '女性';
          const dominantRatio = Math.max(maleRatio.value, femaleRatio.value);
          markdown += `- **性别特征**: 以${dominantGender}为主 (${(dominantRatio * 100).toFixed(1)}%)\n`;
        }
      }

      // 年龄总结
      if (portraitData.age && portraitData.age.ratio_list) {
        const mainAge = portraitData.age.ratio_list.reduce((max, current) =>
          current.value > max.value ? current : max
        );
        markdown += `- **年龄特征**: 主要集中在${mainAge.key}岁 (${(mainAge.value * 100).toFixed(1)}%)\n`;
      }

      // 地域总结
      if (portraitData.province && portraitData.province.ratio_list) {
        const topProvince = portraitData.province.ratio_list.reduce((max, current) =>
          current.value > max.value ? current : max
        );
        markdown += `- **地域特征**: 主要来自${topProvince.key} (${(topProvince.value * 100).toFixed(1)}%)\n`;
      }

      // 城市等级总结
      if (portraitData.city_level && portraitData.city_level.ratio_list) {
        const topCityLevel = portraitData.city_level.ratio_list.reduce((max, current) =>
          current.value > max.value ? current : max
        );
        markdown += `- **城市特征**: 主要分布在${topCityLevel.key}城市 (${(topCityLevel.value * 100).toFixed(1)}%)\n`;
      }

      // 职业总结
      if (portraitData.career && portraitData.career.length > 0) {
        const topCareer = portraitData.career.reduce((max, current) =>
          current.value > max.value ? current : max
        );
        const careerMap = {
          'white_collar': '白领',
          'blue_collar_industry': '蓝领工业',
          'blue_collar_service': '蓝领服务业'
        };
        const careerName = careerMap[topCareer.key] || topCareer.key;
        markdown += `- **职业特征**: 主要为${careerName} (${(topCareer.value * 100).toFixed(1)}%)\n`;
      }

      markdown += '\n';

      return markdown;
    }

        /**
     * 获取作品受众关注热词数据
     * @param {string} itemIdStr - 作品ID字符串
     * @returns {Promise<Object>} 受众关注热词数据
     */
        async function getItemAudienceHotwords(itemIdStr) {
          try {
            // 验证item_id格式（应该是数字字符串）
            if (!itemIdStr || !/^\d+$/.test(itemIdStr)) {
              throw new Error(`无效的item_id格式: ${itemIdStr}`);
            }

            const apiUrl = `https://creator.douyin.com/janus/douyin/creator/data/fans/item/others?aid=2906&app_name=aweme_creator_platform&device_platform=web&referer=https:%2F%2Fcreator.douyin.com%2Fcreator-micro%2Fdata-center%2Fcontent&user_agent=Mozilla%2F5.0+(Macintosh%3B+Intel+Mac+OS+X+10_15_7)+AppleWebKit%2F537.36+(KHTML,+like+Gecko)+Chrome%2F138.0.0.0+Safari%2F537.36&cookie_enabled=true&screen_width=3360&screen_height=1890&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0+(Macintosh%3B+Intel+Mac+OS+X+10_15_7)+AppleWebKit%2F537.36+(KHTML,+like+Gecko)+Chrome%2F138.0.0.0+Safari%2F537.36&browser_online=true&timezone_name=Asia%2FShanghai&user_id=&item_id=${itemIdStr}`;

            const response = await fetch(apiUrl, {
              method: "GET",
              mode: "cors",
              credentials: "include",
              headers: {
                'Accept': 'application/json',
                'Agw-Js-Conv': 'str'
              }
            });

            if (!response.ok) {
              throw new Error(`HTTP错误: ${response.status}`);
            }

            const data = await response.json();

            if (data.status_code !== 0) {
              throw new Error(`API错误: ${data.status_msg || '未知错误'}`);
            }

            return data;
          } catch (error) {
            console.error('获取受众关注热词数据失败:', error);
            throw error;
          }
        }

        /**
         * 格式化受众关注热词数据为Markdown
         * @param {Object} hotwordsData - 受众关注热词数据
         * @returns {string} Markdown格式的受众关注热词报告
         */
        function formatAudienceHotwordsAsMarkdown(hotwordsData) {
          if (!hotwordsData) {
            return '';
          }

          let markdown = '# 受众关注热词分析\n\n';

          // 受众偏好相似作者分析
          if (hotwordsData.audience_prefer_similar_authors && hotwordsData.audience_prefer_similar_authors.length > 0) {
            markdown += '## 受众偏好相似作者\n\n';
            markdown += '> 观看该作品的用户还关注了以下相似作者\n\n';
            markdown += '| 排名 | 作者昵称 | 粉丝数 | 用户ID |\n';
            markdown += '|------|----------|--------|--------|\n';

            hotwordsData.audience_prefer_similar_authors.forEach((author, index) => {
              const rank = index + 1;
              const nickname = author.nickname || '未知';
              const followerCount = author.follower_count ? formatNumber(author.follower_count) : '0';
              const uid = author.uid || '-';
              markdown += `| ${rank} | ${nickname} | ${followerCount} | ${uid} |\n`;
            });

            markdown += '\n';
          }

          // 受众偏好话题分析
          if (hotwordsData.audience_prefer_topic && hotwordsData.audience_prefer_topic.length > 0) {
            markdown += '## 受众偏好话题\n\n';
            markdown += '> 观看该作品的用户还关注的热门话题\n\n';
            markdown += '| 排名 | 话题名称 | 话题ID | 7日热度 | 7日观看量 |\n';
            markdown += '|------|----------|--------|----------|----------|\n';

            hotwordsData.audience_prefer_topic.forEach((topic, index) => {
              const rank = index + 1;
              const topicName = topic.cha_name || '未知话题';
              const topicId = topic.cha_id || '-';
              const score7d = topic.score_7d ? formatNumber(parseInt(topic.score_7d)) : '0';
              const vv7d = topic.vv_7d ? formatNumber(parseInt(topic.vv_7d)) : '0';
              markdown += `| ${rank} | #${topicName} | ${topicId} | ${score7d} | ${vv7d} |\n`;
            });

            markdown += '\n';

            // 话题热度趋势分析
            if (hotwordsData.audience_prefer_topic[0] && hotwordsData.audience_prefer_topic[0].score_7d_list) {
              markdown += '### 热门话题趋势详情\n\n';

              // 取前3个话题进行详细分析
              const topTopics = hotwordsData.audience_prefer_topic.slice(0, 3);

              topTopics.forEach((topic, index) => {
                if (topic.score_7d_list && topic.score_7d_list.length > 0) {
                  markdown += `#### ${index + 1}. #${topic.cha_name}\n\n`;
                  markdown += '| 日期 | 热度值 | 占比 |\n';
                  markdown += '|------|--------|------|\n';

                  const totalScore = parseInt(topic.score_7d);
                  const sortedDays = topic.score_7d_list.sort((a, b) => a.key.localeCompare(b.key));

                  sortedDays.forEach(day => {
                    const date = formatDate(day.key);
                    const score = formatNumber(day.value);
                    const percentage = totalScore > 0 ? ((day.value / totalScore) * 100).toFixed(1) + '%' : '0%';
                    markdown += `| ${date} | ${score} | ${percentage} |\n`;
                  });

                  // 计算峰值日期
                  const peakDay = topic.score_7d_list.reduce((max, current) =>
                    current.value > max.value ? current : max
                  );
                  const peakDate = formatDate(peakDay.key);
                  const peakScore = formatNumber(peakDay.value);

                  markdown += `\n**热度峰值**: ${peakDate} (${peakScore})\n\n`;
                }
              });
            }
          }

          // 受众关注热词分析
          if (hotwordsData.audience_search_most_keywords && hotwordsData.audience_search_most_keywords.length > 0) {
            markdown += '## 受众关注热词排行\n\n';
            markdown += '> 观看该作品的用户近期搜索的热门关键词\n\n';
            markdown += '| 排名 | 关键词 | 7日搜索量 | 搜索趋势 |\n';
            markdown += '|------|--------|----------|----------|\n';

            hotwordsData.audience_search_most_keywords.forEach((keyword, index) => {
              const rank = index + 1;
              const keywordText = keyword.keywords || '未知';
              const searchCount = keyword.query_cnt_7d ? formatNumber(parseInt(keyword.query_cnt_7d)) : '0';

              // 分析搜索趋势
              let trendAnalysis = '';
              if (keyword.query_7d_list && keyword.query_7d_list.length > 0) {
                const sortedDays = keyword.query_7d_list.sort((a, b) => a.key.localeCompare(b.key));
                const firstDay = sortedDays[0].value;
                const lastDay = sortedDays[sortedDays.length - 1].value;

                if (lastDay > firstDay * 1.5) {
                  trendAnalysis = '📈 上升';
                } else if (lastDay < firstDay * 0.5) {
                  trendAnalysis = '📉 下降';
                } else {
                  trendAnalysis = '➡️ 平稳';
                }
              }

              markdown += `| ${rank} | ${keywordText} | ${searchCount} | ${trendAnalysis} |\n`;
            });

            markdown += '\n';

            // 热词详细趋势分析
            markdown += '### 热词搜索趋势详情\n\n';

            // 取前5个热词进行详细分析
            const topKeywords = hotwordsData.audience_search_most_keywords.slice(0, 5);

            topKeywords.forEach((keyword, index) => {
              if (keyword.query_7d_list && keyword.query_7d_list.length > 0) {
                markdown += `#### ${index + 1}. ${keyword.keywords}\n\n`;
                markdown += '| 日期 | 搜索量 | 占比 |\n';
                markdown += '|------|--------|------|\n';

                const totalSearches = parseInt(keyword.query_cnt_7d);
                const sortedDays = keyword.query_7d_list.sort((a, b) => a.key.localeCompare(b.key));

                sortedDays.forEach(day => {
                  const date = formatDate(day.key);
                  const searches = formatNumber(day.value);
                  const percentage = totalSearches > 0 ? ((day.value / totalSearches) * 100).toFixed(1) + '%' : '0%';
                  markdown += `| ${date} | ${searches} | ${percentage} |\n`;
                });

                // 计算峰值日期
                const peakDay = keyword.query_7d_list.reduce((max, current) =>
                  current.value > max.value ? current : max
                );
                const peakDate = formatDate(peakDay.key);
                const peakSearches = formatNumber(peakDay.value);

                markdown += `\n**搜索峰值**: ${peakDate} (${peakSearches}次搜索)\n\n`;
              }
            });
          }

          // 热词分类分析
          if (hotwordsData.audience_search_most_keywords && hotwordsData.audience_search_most_keywords.length > 0) {
            markdown += '## 热词分类分析\n\n';

            // 按搜索量分类
            const highVolumeKeywords = hotwordsData.audience_search_most_keywords.filter(k =>
              parseInt(k.query_cnt_7d) >= 10000000 // 1000万以上
            );
            const mediumVolumeKeywords = hotwordsData.audience_search_most_keywords.filter(k =>
              parseInt(k.query_cnt_7d) >= 1000000 && parseInt(k.query_cnt_7d) < 10000000 // 100万-1000万
            );
            const lowVolumeKeywords = hotwordsData.audience_search_most_keywords.filter(k =>
              parseInt(k.query_cnt_7d) < 1000000 // 100万以下
            );

            if (highVolumeKeywords.length > 0) {
              markdown += '### 🔥 超高热度关键词 (1000万+搜索)\n\n';
              highVolumeKeywords.forEach(keyword => {
                const searchCount = formatNumber(parseInt(keyword.query_cnt_7d));
                markdown += `- **${keyword.keywords}**: ${searchCount}次搜索\n`;
              });
              markdown += '\n';
            }

            if (mediumVolumeKeywords.length > 0) {
              markdown += '### 🌟 高热度关键词 (100万-1000万搜索)\n\n';
              mediumVolumeKeywords.forEach(keyword => {
                const searchCount = formatNumber(parseInt(keyword.query_cnt_7d));
                markdown += `- **${keyword.keywords}**: ${searchCount}次搜索\n`;
              });
              markdown += '\n';
            }

            if (lowVolumeKeywords.length > 0) {
              markdown += '### 💫 中等热度关键词 (100万以下搜索)\n\n';
              lowVolumeKeywords.forEach(keyword => {
                const searchCount = formatNumber(parseInt(keyword.query_cnt_7d));
                markdown += `- **${keyword.keywords}**: ${searchCount}次搜索\n`;
              });
              markdown += '\n';
            }
          }

          // 受众偏好分析
          if (hotwordsData.audience_preference) {
            markdown += '## 受众偏好分析\n\n';

            // 分析死忠粉数据
            if (hotwordsData.audience_preference.die_hard_fans_data) {
              markdown += '### 死忠粉偏好\n\n';

              if (hotwordsData.audience_preference.die_hard_fans_data.ratio_data) {
                markdown += '#### 内容偏好分布\n\n';
                markdown += '| 内容类型 | 偏好比例 | 细分标签 |\n';
                markdown += '|----------|----------|----------|\n';

                hotwordsData.audience_preference.die_hard_fans_data.ratio_data.forEach(item => {
                  const category = item.key || '未知';
                  const ratio = (item.value * 100).toFixed(2) + '%';

                  let subTags = '';
                  if (item.second_tag_data && item.second_tag_data.length > 0) {
                    subTags = item.second_tag_data.map(tag =>
                      `${tag.key}(${(tag.value * 100).toFixed(1)}%)`
                    ).join(', ');
                  }

                  markdown += `| ${category} | ${ratio} | ${subTags} |\n`;
                });
                markdown += '\n';
              }

              if (hotwordsData.audience_preference.die_hard_fans_data.tgi_data) {
                markdown += '#### TGI指数分析\n\n';
                markdown += '| 内容类型 | TGI指数 | 偏好程度 |\n';
                markdown += '|----------|---------|----------|\n';

                hotwordsData.audience_preference.die_hard_fans_data.tgi_data.forEach(item => {
                  const category = item.key || '未知';
                  const tgi = item.value.toFixed(1);
                  let preference = '';

                  if (item.value > 200) {
                    preference = '🔥 极强偏好';
                  } else if (item.value > 120) {
                    preference = '📈 强偏好';
                  } else if (item.value > 80) {
                    preference = '➡️ 正常偏好';
                  } else {
                    preference = '📉 弱偏好';
                  }

                  markdown += `| ${category} | ${tgi} | ${preference} |\n`;
                });
                markdown += '\n';
              }
            }

            // 分析粉丝数据
            if (hotwordsData.audience_preference.fans_data) {
              markdown += '### 粉丝偏好\n\n';

              if (hotwordsData.audience_preference.fans_data.ratio_data) {
                markdown += '#### 内容偏好分布\n\n';
                markdown += '| 内容类型 | 偏好比例 | 细分标签 |\n';
                markdown += '|----------|----------|----------|\n';

                // 按比例排序
                const sortedFansData = [...hotwordsData.audience_preference.fans_data.ratio_data]
                  .sort((a, b) => b.value - a.value);

                sortedFansData.forEach(item => {
                  const category = item.key || '未知';
                  const ratio = (item.value * 100).toFixed(2) + '%';

                  let subTags = '';
                  if (item.second_tag_data && item.second_tag_data.length > 0) {
                    subTags = item.second_tag_data.map(tag =>
                      `${tag.key}(${(tag.value * 100).toFixed(1)}%)`
                    ).join(', ');
                  }

                  markdown += `| ${category} | ${ratio} | ${subTags} |\n`;
                });
                markdown += '\n';
              }
            }

            // 分析总体数据
            if (hotwordsData.audience_preference.total_data) {
              markdown += '### 总体受众偏好\n\n';

              if (hotwordsData.audience_preference.total_data.ratio_data) {
                markdown += '#### 内容偏好分布\n\n';
                markdown += '| 内容类型 | 偏好比例 | 细分标签 |\n';
                markdown += '|----------|----------|----------|\n';

                // 按比例排序
                const sortedTotalData = [...hotwordsData.audience_preference.total_data.ratio_data]
                  .sort((a, b) => b.value - a.value);

                sortedTotalData.forEach(item => {
                  const category = item.key || '未知';
                  const ratio = (item.value * 100).toFixed(2) + '%';

                  let subTags = '';
                  if (item.second_tag_data && item.second_tag_data.length > 0) {
                    subTags = item.second_tag_data.map(tag =>
                      `${tag.key}(${(tag.value * 100).toFixed(1)}%)`
                    ).join(', ');
                  }

                  markdown += `| ${category} | ${ratio} | ${subTags} |\n`;
                });
                markdown += '\n';
              }
            }
          }

          // 受众兴趣洞察
          if (hotwordsData.audience_search_most_keywords && hotwordsData.audience_search_most_keywords.length > 0) {
            markdown += '## 受众兴趣洞察\n\n';

            // 计算总搜索量
            const totalSearchVolume = hotwordsData.audience_search_most_keywords.reduce((sum, keyword) =>
              sum + parseInt(keyword.query_cnt_7d), 0
            );

            // 找出最热门的关键词
            const topKeyword = hotwordsData.audience_search_most_keywords[0];
            const topKeywordSearches = formatNumber(parseInt(topKeyword.query_cnt_7d));
            const topKeywordPercentage = ((parseInt(topKeyword.query_cnt_7d) / totalSearchVolume) * 100).toFixed(1);

            markdown += `- **最受关注话题**: ${topKeyword.keywords} (${topKeywordSearches}次搜索，占${topKeywordPercentage}%)\n`;
            markdown += `- **关注话题总数**: ${hotwordsData.audience_search_most_keywords.length}个\n`;
            markdown += `- **总搜索热度**: ${formatNumber(totalSearchVolume)}次\n`;

            // 分析关键词类型
            const entertainmentKeywords = hotwordsData.audience_search_most_keywords.filter(k =>
              /演唱会|明星|娱乐|音乐|电影|综艺/.test(k.keywords)
            ).length;

            const newsKeywords = hotwordsData.audience_search_most_keywords.filter(k =>
              /新闻|事件|热点|社会|政治/.test(k.keywords)
            ).length;

            const lifestyleKeywords = hotwordsData.audience_search_most_keywords.filter(k =>
              /生活|美食|旅游|时尚|健康/.test(k.keywords)
            ).length;

            if (entertainmentKeywords > 0) {
              markdown += `- **娱乐类话题**: ${entertainmentKeywords}个\n`;
            }
            if (newsKeywords > 0) {
              markdown += `- **新闻热点类**: ${newsKeywords}个\n`;
            }
            if (lifestyleKeywords > 0) {
              markdown += `- **生活方式类**: ${lifestyleKeywords}个\n`;
            }

            markdown += '\n';
          }

          return markdown;
        }

        /**
         * 格式化日期字符串
         * @param {string} dateStr - 日期字符串 (YYYYMMDD)
         * @returns {string} 格式化后的日期
         */
        function formatDate(dateStr) {
          if (!dateStr || dateStr.length !== 8) return dateStr;

          const year = dateStr.substring(0, 4);
          const month = dateStr.substring(4, 6);
          const day = dateStr.substring(6, 8);

          return `${month}/${day}`;
        }

        /**
         * 格式化数字为可读格式
         * @param {number} num - 数字
         * @returns {string} 格式化后的数字字符串
         */
        function formatNumber(num) {
          if (num >= 100000000) {
            return (num / 100000000).toFixed(1) + '亿';
          } else if (num >= 10000) {
            return (num / 10000).toFixed(1) + '万';
          } else {
            return num.toLocaleString();
          }
        }

/**
     * 获取作品评论热词数据
     * @param {string} itemIdStr - 作品ID字符串
     * @returns {Promise<Object>} 评论热词数据
     */
async function getItemCommentWordCloud(itemIdStr) {
  try {
    // 验证item_id格式（应该是数字字符串）
    if (!itemIdStr || !/^\d+$/.test(itemIdStr)) {
      throw new Error(`无效的item_id格式: ${itemIdStr}`);
    }

    const apiUrl = `https://creator.douyin.com/aweme/v1/creator/data/item/wordCloud/?aid=2906&app_name=aweme_creator_platform&device_platform=web&referer=https%3A%2F%2Fcreator.douyin.com%2Fcreator-micro%2Fdata-center%2Fcontent&user_agent=Mozilla%2F5.0+%28Macintosh%3B+Intel+Mac+OS+X+10_15_7%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Chrome%2F138.0.0.0+Safari%2F537.36&cookie_enabled=true&screen_width=3360&screen_height=1890&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0+%28Macintosh%3B+Intel+Mac+OS+X+10_15_7%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Chrome%2F138.0.0.0+Safari%2F537.36&browser_online=true&timezone_name=Asia%2FShanghai&item_id=${itemIdStr}&mcn_type=0`;

    const response = await fetch(apiUrl, {
      method: "GET",
      mode: "cors",
      credentials: "include",
      headers: {
        'Accept': 'application/json',
        'Agw-Js-Conv': 'str'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`);
    }

    const data = await response.json();

    if (data.status_code !== 0) {
      throw new Error(`API错误: ${data.status_msg || '未知错误'}`);
    }

    return data;
  } catch (error) {
    console.error('获取评论热词数据失败:', error);
    throw error;
  }
}

    /**
     * 格式化评论热词数据为Markdown
     * @param {Object} wordCloudData - 评论热词数据
     * @returns {string} Markdown格式的评论热词报告
     */
    function formatCommentWordCloudAsMarkdown(wordCloudData) {
      if (!wordCloudData || !wordCloudData.word_cloud_list || wordCloudData.word_cloud_list.length === 0) {
        return '';
      }

      let markdown = '# 评论热词\n\n';

      const wordList = wordCloudData.word_cloud_list;

      // 热词列表
      markdown += '| 排名 | 热词 | 热度分值 |\n';
      markdown += '|------|------|----------|\n';

      wordList.forEach(word => {
        const rank = word.rank;
        const keyword = word.word;
        const score = word.score;

        markdown += `| ${rank} | ${keyword} | ${score} |\n`;
      });

      markdown += '\n';

      return markdown;
    }

        /**
     * 格式化点赞分析数据为Markdown
     * @param {Object} likeAnalysisData - 点赞分析数据
     * @returns {string} Markdown格式的点赞分析报告
     */
        function formatLikeAnalysisAsMarkdown(likeAnalysisData) {
          if (!likeAnalysisData || !likeAnalysisData.analysis_trend) {
            return '';
          }

          let markdown = '# 点赞分析\n\n';

          const analysisTrend = likeAnalysisData.analysis_trend;
          const currentItem = analysisTrend.current_item || [];
          const similarAuthor = analysisTrend.similar_author || [];

          if (currentItem.length === 0 && similarAuthor.length === 0) {
            return '';
          }

          // 创建对比表格
          markdown += '## 点赞率时间分布对比\n\n';
          markdown += '| 时间点 | 当前作品点赞率 | 同类作品点赞率 | 差异 |\n';
          markdown += '|--------|----------------|----------------|------|\n';

          // 创建时间点映射
          const timeMap = new Map();

          // 收集当前作品数据
          currentItem.forEach(point => {
            timeMap.set(point.key, { current: point.value, similar: null });
          });

          // 收集同类作品数据
          similarAuthor.forEach(point => {
            if (timeMap.has(point.key)) {
              timeMap.get(point.key).similar = point.value;
            } else {
              timeMap.set(point.key, { current: null, similar: point.value });
            }
          });

          // 按时间排序并生成表格
          const sortedTimes = Array.from(timeMap.keys()).sort();

          let currentTotal = 0;
          let similarTotal = 0;
          let validPoints = 0;

          sortedTimes.forEach(time => {
            const data = timeMap.get(time);
            const currentRate = data.current !== null ? (data.current * 100).toFixed(2) + '%' : '-';
            const similarRate = data.similar !== null ? (data.similar * 100).toFixed(2) + '%' : '-';

            let difference = '-';
            if (data.current !== null && data.similar !== null) {
              const diff = ((data.current - data.similar) * 100).toFixed(2);
              difference = diff > 0 ? `+${diff}%` : `${diff}%`;

              currentTotal += data.current;
              similarTotal += data.similar;
              validPoints++;
            }

            markdown += `| ${time} | ${currentRate} | ${similarRate} | ${difference} |\n`;
          });

          // 添加统计摘要
          if (validPoints > 0) {
            const avgCurrent = (currentTotal / validPoints * 100).toFixed(2);
            const avgSimilar = (similarTotal / validPoints * 100).toFixed(2);
            const avgDiff = ((currentTotal - similarTotal) / validPoints * 100).toFixed(2);

            markdown += '\n## 点赞分析摘要\n\n';
            markdown += `- **当前作品平均点赞率**: ${avgCurrent}%\n`;
            markdown += `- **同类作品平均点赞率**: ${avgSimilar}%\n`;
            markdown += `- **平均差异**: ${avgDiff > 0 ? '+' : ''}${avgDiff}%\n`;
            markdown += `- **数据点数量**: ${validPoints}个\n\n`;

            // 找出点赞高峰时段
            const peakPoints = sortedTimes
              .map(time => ({
                time,
                current: timeMap.get(time).current,
                similar: timeMap.get(time).similar
              }))
              .filter(point => point.current !== null)
              .sort((a, b) => b.current - a.current)
              .slice(0, 3);

            if (peakPoints.length > 0) {
              markdown += '### 点赞高峰时段\n\n';
              peakPoints.forEach((point, index) => {
                const rank = index + 1;
                const rate = (point.current * 100).toFixed(2);
                markdown += `${rank}. **${point.time}**: ${rate}%\n`;
              });
              markdown += '\n';
            }
          }

          return markdown;
        }

            /**
     * 获取作品弹幕分析数据
     * @param {string} itemIdStr - 作品ID字符串
     * @returns {Promise<Object>} 弹幕分析数据
     */
    async function getItemBulletAnalysis(itemIdStr) {
      try {
        // 验证item_id格式（应该是数字字符串）
        if (!itemIdStr || !/^\d+$/.test(itemIdStr)) {
          throw new Error(`无效的item_id格式: ${itemIdStr}`);
        }

        const apiUrl = `https://creator.douyin.com/janus/douyin/creator/bff/data/bullet/analysis/v2?aid=2906&app_name=aweme_creator_platform&device_platform=web&referer=https:%2F%2Fcreator.douyin.com%2Fcreator-micro%2Fdata-center%2Fcontent&user_agent=Mozilla%2F5.0+(Macintosh%3B+Intel+Mac+OS+X+10_15_7)+AppleWebKit%2F537.36+(KHTML,+like+Gecko)+Chrome%2F138.0.0.0+Safari%2F537.36&cookie_enabled=true&screen_width=3360&screen_height=1890&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0+(Macintosh%3B+Intel+Mac+OS+X+10_15_7)+AppleWebKit%2F537.36+(KHTML,+like+Gecko)+Chrome%2F138.0.0.0+Safari%2F537.36&browser_online=true&timezone_name=Asia%2FShanghai&item_id=${itemIdStr}`;

        const response = await fetch(apiUrl, {
          method: "GET",
          mode: "cors",
          credentials: "include",
          headers: {
            'Accept': 'application/json',
            'Agw-Js-Conv': 'str'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP错误: ${response.status}`);
        }

        const data = await response.json();

        return data;
      } catch (error) {
        console.error('获取弹幕分析数据失败:', error);
        throw error;
      }
    }

        /**
     * 格式化弹幕分析数据为Markdown
     * @param {Object} bulletAnalysisData - 弹幕分析数据
     * @returns {string} Markdown格式的弹幕分析报告
     */
        function formatBulletAnalysisAsMarkdown(bulletAnalysisData) {
          if (!bulletAnalysisData || !bulletAnalysisData.current_item || !bulletAnalysisData.similar_author) {
            return '';
          }

          let markdown = '# 弹幕分析\n\n';

          const currentItem = bulletAnalysisData.current_item || [];
          const similarAuthor = bulletAnalysisData.similar_author || [];

          if (currentItem.length === 0 && similarAuthor.length === 0) {
            return '';
          }

          // 创建对比表格
          markdown += '## 弹幕数量时间分布对比\n\n';
          markdown += '| 时间点(秒) | 当前作品弹幕数 | 同类作品弹幕数 | 差异 |\n';
          markdown += '|------------|----------------|----------------|------|\n';

          // 创建时间点映射
          const timeMap = new Map();

          // 收集当前作品数据
          currentItem.forEach(point => {
            timeMap.set(point.key, { current: point.value, similar: null });
          });

          // 收集同类作品数据
          similarAuthor.forEach(point => {
            if (timeMap.has(point.key)) {
              timeMap.get(point.key).similar = point.value;
            } else {
              timeMap.set(point.key, { current: null, similar: point.value });
            }
          });

          // 按时间排序并生成表格
          const sortedTimes = Array.from(timeMap.keys()).sort((a, b) => parseFloat(a) - parseFloat(b));

          let currentTotal = 0;
          let similarTotal = 0;
          let validPoints = 0;

          sortedTimes.forEach(time => {
            const data = timeMap.get(time);
            const currentCount = data.current !== null ? data.current : '-';
            const similarCount = data.similar !== null ? data.similar : '-';

            let difference = '-';
            if (data.current !== null && data.similar !== null) {
              const diff = data.current - data.similar;
              difference = diff > 0 ? `+${diff}` : `${diff}`;

              currentTotal += data.current;
              similarTotal += data.similar;
              validPoints++;
            }

            markdown += `| ${time} | ${currentCount} | ${similarCount} | ${difference} |\n`;
          });

          // 添加统计摘要
          if (validPoints > 0) {
            const avgCurrent = (currentTotal / validPoints).toFixed(1);
            const avgSimilar = (similarTotal / validPoints).toFixed(1);
            const avgDiff = ((currentTotal - similarTotal) / validPoints).toFixed(1);

            markdown += '\n## 弹幕分析摘要\n\n';
            markdown += `- **当前作品总弹幕数**: ${currentTotal}条\n`;
            markdown += `- **同类作品总弹幕数**: ${similarTotal}条\n`;
            markdown += `- **当前作品平均弹幕数**: ${avgCurrent}条/时间点\n`;
            markdown += `- **同类作品平均弹幕数**: ${avgSimilar}条/时间点\n`;
            markdown += `- **平均差异**: ${avgDiff > 0 ? '+' : ''}${avgDiff}条/时间点\n`;
            markdown += `- **数据点数量**: ${validPoints}个\n\n`;

            // 找出弹幕高峰时段
            const peakPoints = sortedTimes
              .map(time => ({
                time,
                current: timeMap.get(time).current,
                similar: timeMap.get(time).similar
              }))
              .filter(point => point.current !== null && point.current > 0)
              .sort((a, b) => b.current - a.current)
              .slice(0, 5);

            if (peakPoints.length > 0) {
              markdown += '### 弹幕高峰时段\n\n';
              peakPoints.forEach((point, index) => {
                const rank = index + 1;
                markdown += `${rank}. **${point.time}秒**: ${point.current}条弹幕\n`;
              });
              markdown += '\n';
            }

            // 弹幕活跃度分析
            const activePoints = currentItem.filter(point => point.value > 0).length;
            const activityRate = ((activePoints / currentItem.length) * 100).toFixed(1);

            markdown += '### 弹幕活跃度分析\n\n';
            markdown += `- **有弹幕的时间点**: ${activePoints}个\n`;
            markdown += `- **弹幕活跃度**: ${activityRate}%\n`;

            if (currentTotal > 0) {
              const maxBullet = Math.max(...currentItem.map(p => p.value));
              const peakTime = currentItem.find(p => p.value === maxBullet)?.key;
              markdown += `- **弹幕峰值**: ${maxBullet}条 (${peakTime}秒)\n`;
            }

            markdown += '\n';
          }

          return markdown;
        }

          // 格式化留存分析数据为Markdown
  function formatRetentionAnalysisAsMarkdown(retentionData) {
    if (!retentionData || !retentionData.analysis_trend) {
      return '';
    }

    let markdown = '# 留存分析\n\n';

    const analysisTrend = retentionData.analysis_trend;
    const currentItem = analysisTrend.current_item || [];
    const similarAuthor = analysisTrend.similar_author || [];

    if (currentItem.length === 0 && similarAuthor.length === 0) {
      return '';
    }

    // 创建对比表格
    markdown += '## 留存率时间分布对比\n\n';
    markdown += '| 时间点 | 当前作品留存率 | 同类作品留存率 | 差异 |\n';
    markdown += '|--------|----------------|----------------|------|\n';

    // 创建时间点映射
    const timeMap = new Map();

    // 收集当前作品数据
    currentItem.forEach(point => {
      timeMap.set(point.key, { current: point.value, similar: null });
    });

    // 收集同类作品数据
    similarAuthor.forEach(point => {
      if (timeMap.has(point.key)) {
        timeMap.get(point.key).similar = point.value;
      } else {
        timeMap.set(point.key, { current: null, similar: point.value });
      }
    });

    // 按时间排序并生成表格
    const sortedTimes = Array.from(timeMap.keys()).sort((a, b) => {
      const timeA = a.split(':').reduce((acc, time) => (60 * acc) + +time);
      const timeB = b.split(':').reduce((acc, time) => (60 * acc) + +time);
      return timeA - timeB;
    });

    let currentTotal = 0;
    let similarTotal = 0;
    let validPoints = 0;

    sortedTimes.forEach(time => {
      const data = timeMap.get(time);
      const currentRate = data.current !== null ? (data.current * 100).toFixed(2) + '%' : '-';
      const similarRate = data.similar !== null ? (data.similar * 100).toFixed(2) + '%' : '-';

      let difference = '-';
      if (data.current !== null && data.similar !== null) {
        const diff = (data.current - data.similar) * 100;
        difference = diff > 0 ? `+${diff.toFixed(2)}%` : `${diff.toFixed(2)}%`;

        currentTotal += data.current;
        similarTotal += data.similar;
        validPoints++;
      }

      markdown += `| ${time} | ${currentRate} | ${similarRate} | ${difference} |\n`;
    });


    return markdown;
  }

    // 封装抖音跳出分析API调用
    async function fetchItemExitAnalytics(itemId) {
      // 确保itemId为字符串类型，避免大数精度丢失
      const itemIdStr = String(itemId);

      try {
        // 验证item_id格式（应该是数字字符串）
        if (!itemIdStr || !/^\d+$/.test(itemIdStr)) {
          throw new Error(`无效的item_id格式: ${itemIdStr}`);
        }

        const apiUrl = `https://creator.douyin.com/janus/douyin/creator/data/realtime/analysis/data_center?aid=2906&app_name=aweme_creator_platform&device_platform=web&cookie_enabled=true&timezone_name=Asia%2FShanghai&user_id=&item_id=${itemIdStr}&analysis_type=7&metrics_group=0`;

        const response = await fetch(apiUrl, {
          method: "GET",
          mode: "cors",
          credentials: "include",
          headers: {
            'Accept': 'application/json',
            'Agw-Js-Conv': 'str'  // 关键头部：确保大数字ID以字符串形式返回
          }
        });

        if (!response.ok) {
          throw new Error(`跳出分析API请求失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        return {
          itemId: itemIdStr,
          success: true,
          data: data
        };
      } catch (error) {
        console.error(`❌ 获取item ${itemIdStr} 跳出分析失败:`, error);
        return {
          itemId: itemIdStr,
          success: false,
          error: error.message
        };
      }
    }

      // 格式化跳出分析数据为Markdown
  function formatExitAnalysisAsMarkdown(exitData) {
    if (!exitData || !exitData.analysis_trend) {
      return '';
    }

    let markdown = '# 跳出分析\n\n';

    const analysisTrend = exitData.analysis_trend;
    const currentItem = analysisTrend.current_item || [];
    const similarAuthor = analysisTrend.similar_author || [];

    if (currentItem.length === 0 && similarAuthor.length === 0) {
      return '';
    }

    // 创建对比表格
    markdown += '## 跳出率时间分布对比\n\n';
    markdown += '| 时间点 | 当前作品跳出率 | 同类作品跳出率 | 差异 |\n';
    markdown += '|--------|----------------|----------------|------|\n';

    // 创建时间点映射
    const timeMap = new Map();

    // 收集当前作品数据
    currentItem.forEach(point => {
      timeMap.set(point.key, { current: point.value, similar: null });
    });

    // 收集同类作品数据
    similarAuthor.forEach(point => {
      if (timeMap.has(point.key)) {
        timeMap.get(point.key).similar = point.value;
      } else {
        timeMap.set(point.key, { current: null, similar: point.value });
      }
    });

    // 按时间排序并生成表格
    const sortedTimes = Array.from(timeMap.keys()).sort((a, b) => {
      const timeA = a.split(':').reduce((acc, time) => (60 * acc) + +time);
      const timeB = b.split(':').reduce((acc, time) => (60 * acc) + +time);
      return timeA - timeB;
    });

    let currentTotal = 0;
    let similarTotal = 0;
    let validPoints = 0;

    sortedTimes.forEach(time => {
      const data = timeMap.get(time);
      const currentRate = data.current !== null ? (data.current * 100).toFixed(2) + '%' : '-';
      const similarRate = data.similar !== null ? (data.similar * 100).toFixed(2) + '%' : '-';

      let difference = '-';
      if (data.current !== null && data.similar !== null) {
        const diff = (data.current - data.similar) * 100;
        difference = diff > 0 ? `+${diff.toFixed(2)}%` : `${diff.toFixed(2)}%`;

        currentTotal += data.current;
        similarTotal += data.similar;
        validPoints++;
      }

      markdown += `| ${time} | ${currentRate} | ${similarRate} | ${difference} |\n`;
    });

    return markdown;
  }

    // 添加到全局作用域供控制台调用
    window.testMetricsTrendAPI = testMetricsTrendAPI;
    window.getItemMetricsTrend = getItemMetricsTrend;

})();
