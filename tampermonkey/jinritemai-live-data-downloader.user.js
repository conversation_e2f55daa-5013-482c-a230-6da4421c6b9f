// ==UserScript==
// @name         巨量百应直播数据下载器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  在巨量百应直播报表页面添加数据下载功能
// <AUTHOR>
// @match        https://compass.jinritemai.com/content_live/author/live_room_detail*
// @match        https://compass.jinritemai.com/talent/live-statement*
// @match        https://compass.jinritemai.com/talent/live-detail*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function () {
  'use strict';

  // ==================== 全局变量 ====================
  let downloadButtonInserted = false;
  let domObserver = null;
  let statusIndicator = null;

  // ==================== API 配置 ====================
  const API_CONFIG = {
    baseURL: 'https://compass.jinritemai.com/compass_api/',
    defaultHeaders: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    },
    timeout: 30000 // 30秒超时
  };

  // ==================== 映射配置 ====================
  const LEVEL_MAPPINGS = {
    // 质量等级映射（用于 quality_level 和 item.level）
    QUALITY_LEVEL: {
      1: '待提升',
      2: '良好',
      3: '优秀',
      default: '未知'
    },
    // 诊断结果映射（用于 diagnosis result）
    DIAGNOSIS_RESULT: {
      1: '上升',
      2: '稳定',
      3: '下降',
      default: '未知'
    }
  };

  // ==================== 基础DOM操作函数 ====================

  /**
   * 等待指定选择器的元素出现
   * @param {string} selector - CSS选择器
   * @param {number} timeout - 超时时间（毫秒）
   * @param {Element} container - 搜索容器，默认为document
   * @returns {Promise<Element|null>}
   */
  function waitForElement(selector, timeout = 10000, container = document) {
    return new Promise((resolve) => {
      const element = container.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver((mutations) => {
        const element = container.querySelector(selector);
        if (element) {
          observer.disconnect();
          resolve(element);
        }
      });

      observer.observe(container, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        resolve(null);
      }, timeout);
    });
  }

  /**
   * 查找包含指定文本的元素
   * @param {string} text - 要查找的文本
   * @param {string} tagName - 标签名，默认为'*'
   * @param {Element} container - 搜索容器，默认为document
   * @returns {Element|null}
   */
  function findElementByText(text, tagName = '*', container = document) {
    const elements = container.querySelectorAll(tagName);
    for (const element of elements) {
      if (element.textContent && element.textContent.trim().includes(text)) {
        return element;
      }
    }
    return null;
  }

  /**
   * 安全地插入元素到指定位置
   * @param {Element} newElement - 要插入的新元素
   * @param {Element} targetElement - 目标元素
   * @param {string} position - 插入位置：'before'|'after'|'prepend'|'append'
   * @returns {boolean} 是否插入成功
   */
  function safeInsertElement(newElement, targetElement, position = 'before') {
    try {
      if (!newElement || !targetElement) {
        console.error('❌ 插入元素失败：元素不存在');
        return false;
      }

      switch (position) {
        case 'before':
          targetElement.parentNode.insertBefore(newElement, targetElement);
          break;
        case 'after':
          targetElement.parentNode.insertBefore(newElement, targetElement.nextSibling);
          break;
        case 'prepend':
          targetElement.insertBefore(newElement, targetElement.firstChild);
          break;
        case 'append':
          targetElement.appendChild(newElement);
          break;
        default:
          console.error('❌ 无效的插入位置:', position);
          return false;
      }
      return true;
    } catch (error) {
      console.error('❌ 插入元素时发生错误:', error);
      return false;
    }
  }

  /**
   * 创建样式化的按钮元素
   * @param {string} text - 按钮文本
   * @param {string} id - 按钮ID
   * @param {Object} styles - 自定义样式对象
   * @returns {HTMLButtonElement}
   */
  function createStyledButton(text, id, styles = {}) {
    const button = document.createElement('button');
    button.id = id;
    button.textContent = text;

    const defaultStyles = {
      marginRight: '10px',
      padding: '8px 16px',
      background: '#1890ff',
      color: 'white',
      border: 'none',
      borderRadius: '6px',
      cursor: 'pointer',
      fontSize: '14px',
      fontWeight: '500',
      transition: 'all 0.2s ease',
      boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',
      position: 'relative'
    };

    const finalStyles = { ...defaultStyles, ...styles };

    Object.assign(button.style, finalStyles);

    // 添加悬停效果
    button.addEventListener('mouseenter', () => {
      button.style.background = '#40a9ff';
      button.style.transform = 'translateY(-1px)';
      button.style.boxShadow = '0 4px 8px rgba(24, 144, 255, 0.4)';
    });

    button.addEventListener('mouseleave', () => {
      button.style.background = finalStyles.background;
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = finalStyles.boxShadow;
    });

    return button;
  }

  // ==================== 状态指示器函数 ====================

  /**
   * 创建状态指示器
   */
  function createStatusIndicator() {
    if (statusIndicator) return;

    statusIndicator = document.createElement('div');
    statusIndicator.id = 'jinritemai-status-indicator';
    statusIndicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            min-width: 200px;
            text-align: center;
            transition: all 0.3s ease;
            display: block;
            opacity: 1;
            pointer-events: auto;
        `;

    document.body.appendChild(statusIndicator);
    console.log('📊 状态指示器已创建并添加到页面');
  }

  /**
   * 更新状态指示器内容
   * @param {string} message - 状态消息
   * @param {string} type - 消息类型：'info'|'success'|'error'|'warning'
   */
  function updateStatusIndicator(message, type = 'info') {
    if (!statusIndicator) createStatusIndicator();

    // 确保状态指示器可见
    statusIndicator.style.display = 'block';
    statusIndicator.textContent = message;

    const colors = {
      info: 'rgba(24, 144, 255, 0.9)',
      success: 'rgba(82, 196, 26, 0.9)',
      error: 'rgba(255, 77, 79, 0.9)',
      warning: 'rgba(250, 173, 20, 0.9)'
    };

    statusIndicator.style.background = colors[type] || colors.info;

    // 添加日志输出，确保状态更新被记录
    console.log(`📊 状态更新: ${message} (${type})`);
  }

  /**
   * 移除状态指示器
   */
  function removeStatusIndicator() {
    if (statusIndicator) {
      statusIndicator.remove();
      statusIndicator = null;
    }
  }

  // ==================== API 调用函数 ====================

  /**
   * 通用API调用函数
   * @param {string} endpoint - API端点路径
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} API响应数据
   */
  async function callAPI(endpoint, options = {}) {
    const {
      method = 'GET',
      params = {},
      headers = {},
      timeout = API_CONFIG.timeout
    } = options;

    try {
      // 构建完整URL
      const url = new URL(endpoint, API_CONFIG.baseURL);

      // 添加查询参数
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null) {
          url.searchParams.append(key, params[key]);
        }
      });

      console.log(`🌐 调用API: ${method} ${url.toString()}`);

      // 合并请求头
      const finalHeaders = {
        ...API_CONFIG.defaultHeaders,
        ...headers
      };

      // 创建请求配置
      const requestConfig = {
        method,
        headers: finalHeaders,
        credentials: 'include', // 包含cookies
        mode: 'cors'
      };

      // 创建超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      requestConfig.signal = controller.signal;

      try {
        const response = await fetch(url.toString(), requestConfig);
        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`✅ API调用成功:`, data);
        return data;

      } catch (fetchError) {
        clearTimeout(timeoutId);
        throw fetchError;
      }

    } catch (error) {
      console.error(`❌ API调用失败 [${endpoint}]:`, error);
      throw error;
    }
  }

    /**
   * 获取直播间流量来源数据
   * @param {string} roomId - 直播间ID
   * @param {number} timeType - 时间类型，默认为1（本场）
   * @returns {Promise<Object>} 直播间流量来源数据
   */
    async function getLiveRoomFlowSource(roomId, timeType = 1) {
      if (!roomId) {
        throw new Error('直播间ID不能为空');
      }

      try {
        const response = await callAPI('author/live/room_diagnosis/flow_source_v2', {
          params: {
            room_id: roomId,
            time_type: timeType
          }
        });

        if (response.st !== 0) {
          throw new Error(`API返回错误: ${response.msg || '未知错误'}`);
        }

        return response.data;
      } catch (error) {
        console.error(`❌ 获取直播间流量来源数据失败 [${roomId}]:`, error);
        throw error;
      }
    }

  /**
   * 获取直播间基础信息
   * @param {string} liveRoomId - 直播间ID
   * @returns {Promise<Object>} 直播间基础信息
   */
  async function getLiveRoomBasicInfo(liveRoomId) {
    if (!liveRoomId) {
      throw new Error('直播间ID不能为空');
    }

    try {
      const response = await callAPI('author/live/live_room_detail/basic_info', {
        params: {
          live_room_id: liveRoomId
        }
      });

      if (response.st !== 0) {
        throw new Error(`API返回错误: ${response.msg || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error(`❌ 获取直播间基础信息失败 [${liveRoomId}]:`, error);
      throw error;
    }
  }

  /**
   * 获取直播间交易信息
   * @param {string} liveRoomId - 直播间ID
   * @returns {Promise<Object>} 直播间交易信息
   */
  async function getLiveRoomTradeInfo(liveRoomId) {
    if (!liveRoomId) {
      throw new Error('直播间ID不能为空');
    }

    try {
      const response = await callAPI('author/live/live_room_detail/trade_info', {
        params: {
          live_room_id: liveRoomId
        }
      });

      if (response.st !== 0) {
        throw new Error(`API返回错误: ${response.msg || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error(`❌ 获取直播间交易信息失败 [${liveRoomId}]:`, error);
      throw error;
    }
  }

  /**
   * 获取直播间诊断结果
   * @param {string} roomId - 直播间ID
   * @returns {Promise<Object>} 直播间诊断结果
   */
  async function getLiveRoomDiagnosis(roomId) {
    if (!roomId) {
      throw new Error('直播间ID不能为空');
    }

    try {
      const response = await callAPI('author/live/room_diagnosis/result_v3', {
        params: {
          room_id: roomId
        }
      });

      if (response.st !== 0) {
        throw new Error(`API返回错误: ${response.msg || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error(`❌ 获取直播间诊断结果失败 [${roomId}]:`, error);
      throw error;
    }
  }

  /**
   * 获取直播间改进方向
   * @param {string} roomId - 直播间ID
   * @returns {Promise<Object>} 直播间改进方向数据
   */
  async function getLiveRoomImprovedDirection(roomId) {
    if (!roomId) {
      throw new Error('直播间ID不能为空');
    }

    try {
      const response = await callAPI('author/live/room_diagnosis/improved_direction', {
        params: {
          room_id: roomId
        }
      });

      if (response.st !== 0) {
        throw new Error(`API返回错误: ${response.msg || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error(`❌ 获取直播间改进方向失败 [${roomId}]:`, error);
      throw error;
    }
  }

  /**
   * 获取直播间流量结构数据
   * @param {string} roomId - 直播间ID
   * @returns {Promise<Object>} 直播间流量结构数据
   */
  async function getLiveRoomFlowStructure(roomId) {
    if (!roomId) {
      throw new Error('直播间ID不能为空');
    }

    try {
      const response = await callAPI('author/live/room_diagnosis/flow_structure_v2', {
        params: {
          room_id: roomId
        }
      });

      if (response.st !== 0) {
        throw new Error(`API返回错误: ${response.msg || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error(`❌ 获取直播间流量结构失败 [${roomId}]:`, error);
      throw error;
    }
  }

  /**
   * 获取直播间单小时曝光次数数据
   * @param {string} roomId - 直播间ID
   * @param {number} timeType - 时间类型，0表示单小时
   * @returns {Promise<Object>} 直播间单小时曝光次数数据
   */
  async function getLiveRoomHourlyExposure(roomId, timeType = 0) {
    if (!roomId) {
      throw new Error('直播间ID不能为空');
    }

    try {
      const response = await callAPI('author/live/room_diagnosis/flow_source_v2', {
        params: {
          room_id: roomId,
          time_type: timeType
        }
      });

      if (response.st !== 0) {
        throw new Error(`API返回错误: ${response.msg || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error(`❌ 获取直播间单小时曝光次数数据失败 [${roomId}]:`, error);
      throw error;
    }
  }

  /**
   * 获取直播间流量趋势数据
   * @param {string} roomId - 直播间ID
   * @param {number} compareType - 对比类型，默认为2
   * @param {number} timeType - 时间类型，默认为3
   * @param {string} channel - 渠道，默认为all_flow
   * @param {string} index - 指标，默认为watch_live_show_ucnt_ratio
   * @returns {Promise<Object>} 直播间流量趋势数据
   */
  async function getLiveRoomFlowTrend(roomId, compareType = 2, timeType = 3, channel = 'all_flow', index = 'watch_live_show_ucnt_ratio') {
    if (!roomId) {
      throw new Error('直播间ID不能为空');
    }

    try {
      const response = await callAPI('author/live/room_diagnosis/flow_trend', {
        params: {
          room_id: roomId,
          compare_type: compareType,
          time_type: timeType,
          channel: channel,
          index: index
        }
      });

      if (response.st !== 0) {
        throw new Error(`API返回错误: ${response.msg || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error(`❌ 获取直播间流量趋势数据失败 [${roomId}]:`, error);
      throw error;
    }
  }

  /**
   * 获取直播间流量分析数据
   * @param {string} roomId - 直播间ID
   * @returns {Promise<Object>} 流量分析数据
   */
  async function getLiveRoomFlowAnalysis(roomId) {
    try {
      const url = `${API_CONFIG.baseURL}author/live/live_room_detail/flow/flow_analysis?live_room_id=${roomId}`;
      const response = await fetch(url, {
        method: 'GET',
        headers: API_CONFIG.defaultHeaders,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      if (data.st !== 0) {
        throw new Error(`API返回错误: ${data.msg || '未知错误'}`);
      }

      return data.data;
    } catch (error) {
      console.error('获取直播间流量分析数据失败:', error);
      throw error;
    }
  }

    /**
   * 获取直播间渠道流量数据
   * @param {string} roomId - 直播间ID
   * @returns {Promise<Object>} 渠道流量数据
   */
    async function getLiveRoomChannelFlow(roomId) {
      try {
        const url = `${API_CONFIG.baseURL}content_live/author/live_room_detail/channel_flow?room_id=${roomId}`;
        const response = await fetch(url, {
          method: 'GET',
          headers: API_CONFIG.defaultHeaders,
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        if (data.st !== 0) {
          throw new Error(`API返回错误: ${data.msg || '未知错误'}`);
        }

        return data;
      } catch (error) {
        console.error('获取直播间渠道流量数据失败:', error);
        throw error;
      }
    }

    /**
     * 格式化渠道流量数据为Markdown
     * @param {Object} channelFlowData - 渠道流量数据
     * @returns {string} Markdown格式的渠道流量报告
     */
    function formatChannelFlowAsMarkdown(channelFlowData) {
      if (!channelFlowData || !channelFlowData.data || channelFlowData.data.length === 0) return '';

      let markdown = '# 渠道流量分析\n\n';

      // 从meta中获取表头信息
      const headers = [];
      if (channelFlowData.meta && channelFlowData.meta.length > 0) {
        channelFlowData.meta.forEach(meta => {
          if (!meta.hide) {
            headers.push(meta.index_display);
          }
        });
      } else {
        // 默认表头
        headers.push('渠道名称', '人均观看时长', '观看次数', '观看人数', '直播间观看-互动率(次数)',
                    '成交金额', '成交订单数', '笔单价', '直播间观看-成交率(次数)');
      }

      // 生成表格头部
      markdown += '| ' + headers.join(' | ') + ' |\n';
      markdown += '|' + headers.map(() => '---').join('|') + '|\n';

      // 处理数据行
      channelFlowData.data.forEach(item => {
        const cellInfo = item.cell_info;

        // 获取渠道名称
        const channelName = cellInfo.channel_name?.channel_name_value?.value?.value_str || '-';

        // 检查是否是主要渠道（自然流量或付费流量）
        const isMainChannel = channelName === '自然流量' || channelName === '付费流量' || channelName === '整体';

        // 获取其他指标值
        const avgWatchDuration = formatChannelFlowValue(cellInfo.avg_watch_duration?.avg_watch_duration_index_value);
        const watchCnt = formatChannelFlowValue(cellInfo.watch_cnt?.watch_cnt_index_value);
        const watchUcnt = formatChannelFlowValue(cellInfo.watch_ucnt?.watch_ucnt_index_value);
        const interactRatio = formatChannelFlowValue(cellInfo.interact_watch_cnt_ratio?.interact_watch_cnt_ratio_index_value);
        const payAmt = formatChannelFlowValue(cellInfo.pay_amt?.pay_amt_index_value);
        const payCnt = formatChannelFlowValue(cellInfo.pay_cnt?.pay_cnt_index_value);
        const avgPayOrderAmt = formatChannelFlowValue(cellInfo.avg_pay_order_amt?.avg_pay_order_amt_index_value);
        const watchPayRatio = formatChannelFlowValue(cellInfo.watch_pay_cnt_ratio?.watch_pay_cnt_ratio_index_value);

        // 添加主渠道行
        const prefix = isMainChannel ? '' : '  ';
        markdown += `| ${prefix}${channelName} | ${avgWatchDuration} | ${watchCnt} | ${watchUcnt} | ${interactRatio} | ${payAmt} | ${payCnt} | ${avgPayOrderAmt} | ${watchPayRatio} |\n`;

        // 处理子渠道
        if (cellInfo.second_channel_name && cellInfo.second_channel_name.second_channel_name_children) {
          const children = cellInfo.second_channel_name.second_channel_name_children.children;
          if (children && children.length > 0) {
            children.forEach(child => {
              const childCellInfo = child.cell_info;
              const childChannelName = childCellInfo.channel_name?.channel_name_value?.value?.value_str || '-';
              const childAvgWatchDuration = formatChannelFlowValue(childCellInfo.avg_watch_duration?.avg_watch_duration_index_value);
              const childWatchCnt = formatChannelFlowValue(childCellInfo.watch_cnt?.watch_cnt_index_value);
              const childWatchUcnt = formatChannelFlowValue(childCellInfo.watch_ucnt?.watch_ucnt_index_value);
              const childInteractRatio = formatChannelFlowValue(childCellInfo.interact_watch_cnt_ratio?.interact_watch_cnt_ratio_index_value);
              const childPayAmt = formatChannelFlowValue(childCellInfo.pay_amt?.pay_amt_index_value);
              const childPayCnt = formatChannelFlowValue(childCellInfo.pay_cnt?.pay_cnt_index_value);
              const childAvgPayOrderAmt = formatChannelFlowValue(childCellInfo.avg_pay_order_amt?.avg_pay_order_amt_index_value);
              const childWatchPayRatio = formatChannelFlowValue(childCellInfo.watch_pay_cnt_ratio?.watch_pay_cnt_ratio_index_value);

              // 只添加有观看次数的子渠道
              if (childWatchCnt !== '0' && childWatchCnt !== '-') {
                markdown += `|   - ${childChannelName} | ${childAvgWatchDuration} | ${childWatchCnt} | ${childWatchUcnt} | ${childInteractRatio} | ${childPayAmt} | ${childPayCnt} | ${childAvgPayOrderAmt} | ${childWatchPayRatio} |\n`;
              }
            });
          }
        }
      });

      return markdown;
    }

    /**
     * 格式化渠道流量值
     * @param {Object} valueObj - 值对象
     * @returns {string} 格式化后的值
     */
    function formatChannelFlowValue(valueObj) {
      if (!valueObj) return '-';

      // 处理空值
      if (valueObj.cell_type === 1 && (!valueObj.value || valueObj.value.unit === 6)) {
        return '-';
      }

      // 处理有值的情况
      if (valueObj.cell_type === 2 && valueObj.index_values && valueObj.index_values.value) {
        const value = valueObj.index_values.value;
        const unit = value.unit;

        // 根据单位格式化
        switch (unit) {
          case 1: // 字符串
            return value.value_str || '-';
          case 2: // 时间（秒）
            return formatSeconds(value.value);
          case 3: // 金额
            return `¥${value.value.toFixed(2)}`;
          case 4: // 比率
            return `${(value.value * 100).toFixed(2)}%`;
          case 5: // 数字
            return value.value.toLocaleString();
          default:
            return value.value !== undefined ? String(value.value) : '-';
        }
      }

      return '-';
    }

    /**
     * 将秒数格式化为分钟和秒
     * @param {number} seconds - 秒数
     * @returns {string} 格式化后的时间
     */
    function formatSeconds(seconds) {
      if (seconds === undefined || seconds === null) return '-';

      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);

      return `${minutes}分${remainingSeconds}秒`;
    }

    /**
     * 测试渠道流量API功能
     * @param {string} roomId - 直播间ID，默认使用示例ID
     */
    async function testChannelFlowAPI(roomId = '7524295568181463823') {
      console.log(`🧪 开始测试渠道流量API功能，房间ID: ${roomId}`);

      try {
        // 测试API调用
        const channelFlowData = await getLiveRoomChannelFlow(roomId);
        console.log('✅ 渠道流量API调用成功:', channelFlowData);

        // 测试数据格式化
        const markdownContent = formatChannelFlowAsMarkdown(channelFlowData);
        console.log('✅ 数据格式化成功');
        console.log('📄 生成的Markdown内容:');
        console.log(markdownContent);

        // 测试下载功能
        const testData = {
          extractedData: {
            apiData: {
              channelFlowInfo: channelFlowData
            }
          }
        };

        downloadAsMarkdown(testData, `channel-flow-test-${roomId}.md`);

        return { success: true, data: channelFlowData, markdown: markdownContent };
      } catch (error) {
        console.error('❌ 渠道流量API测试失败:', error);
        return { success: false, error: error.message };
      }
    }

  // ==================== 数据提取函数 ====================

  /**
   * 从页面提取直播数据
   * @returns {Object} 提取的数据对象
   */
  async function extractLiveData() {
    console.log('🔍 开始提取直播数据...');
    updateStatusIndicator('🔍 正在提取数据...', 'info');

    const data = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      extractedData: {}
    };

    try {
      // 尝试从页面API获取数据
      const apiData = await extractDataFromAPI();
      if (apiData) {
        data.extractedData.apiData = apiData;
      }

      // 从DOM提取可见数据
      const domData = extractDataFromDOM();
      if (domData) {
        data.extractedData.domData = domData;
      }

      console.log('✅ 数据提取完成:', data);
      return data;

    } catch (error) {
      console.error('❌ 数据提取失败:', error);
      updateStatusIndicator('❌ 数据提取失败', 'error');
      throw error;
    }
  }

  /**
   * 从API获取数据
   * @returns {Object|null} API数据
   */
  async function extractDataFromAPI() {
    try {
      // 尝试从URL中提取直播间ID
      const liveRoomId = extractLiveRoomIdFromURL();

      if (liveRoomId) {
        console.log(`🔍 检测到直播间ID: ${liveRoomId}`);

        // 获取直播间基础信息
        const basicInfo = await getLiveRoomBasicInfo(liveRoomId);

        // 获取直播间交易信息
        let tradeInfo = null;
        try {
          tradeInfo = await getLiveRoomTradeInfo(liveRoomId);
        } catch (error) {
          console.warn('⚠️ 获取交易信息失败，将跳过该部分:', error);
        }

        // 获取直播间诊断结果
        let diagnosisInfo = null;
        try {
          diagnosisInfo = await getLiveRoomDiagnosis(liveRoomId);
        } catch (error) {
          console.warn('⚠️ 获取诊断信息失败，将跳过该部分:', error);
        }

        // 获取直播间改进方向
        let improvedDirectionInfo = null;
        try {
          improvedDirectionInfo = await getLiveRoomImprovedDirection(liveRoomId);
        } catch (error) {
          console.warn('⚠️ 获取改进方向信息失败，将跳过该部分:', error);
        }

        // 获取直播间流量结构
        let flowStructureInfo = null;
        try {
          flowStructureInfo = await getLiveRoomFlowStructure(liveRoomId);
        } catch (error) {
          console.warn('⚠️ 获取流量结构信息失败，将跳过该部分:', error);
        }

        // 获取直播间流量来源数据
        let flowSourceInfo = null;
        try {
          flowSourceInfo = await getLiveRoomFlowSource(liveRoomId);
        } catch (error) {
          console.warn('⚠️ 获取流量来源数据失败，将跳过该部分:', error);
        }

        // 获取直播间单小时曝光次数数据
        let hourlyExposureInfo = null;
        try {
          hourlyExposureInfo = await getLiveRoomHourlyExposure(liveRoomId);
        } catch (error) {
          console.warn('⚠️ 获取单小时曝光次数数据失败，将跳过该部分:', error);
        }

        // 获取直播间流量趋势数据
        let flowTrendInfo = null;
        try {
          flowTrendInfo = await getLiveRoomFlowTrend(liveRoomId);
        } catch (error) {
          console.warn('⚠️ 获取流量趋势数据失败，将跳过该部分:', error);
        }

        // 获取流量分析信息
        const flowAnalysisInfo = await getLiveRoomFlowAnalysis(liveRoomId);

        // 获取短视频引流信息
        const videoSourceInfo = await getLiveRoomVideoSource(liveRoomId);

        // 获取渠道流量信息
        const channelFlowInfo = await getLiveRoomChannelFlow(liveRoomId);

        // 获取流量趋势分析信息
        const flowTrendAnalysisInfo = await getLiveRoomFlowTrendAnalysis(liveRoomId);

        // 获取核心数据分组信息
        let coreGroupInfo = null;
        try {
          coreGroupInfo = await getLiveRoomCoreGroupInfo(liveRoomId);
        } catch (error) {
          console.warn('⚠️ 获取核心数据分组信息失败，将跳过该部分:', error);
        }

        // 获取竞价广告数据
        let adBidInfo = null;
        try {
          adBidInfo = await getLiveRoomAdBidData(liveRoomId);
        } catch (error) {
          console.warn('⚠️ 获取竞价广告数据失败，将跳过该部分:', error);
        }

        // 获取千川竞价广告数据
        let qianchuanAdBidInfo = null;
        try {
          qianchuanAdBidInfo = await getLiveRoomQianchuanAdBidData(liveRoomId);
        } catch (error) {
          console.warn('⚠️ 获取千川竞价广告数据失败，将跳过该部分:', error);
        }

         // 获取趋势分析数据
        let trendAnalysisInfo = null;
        try {
          trendAnalysisInfo = await getLiveRoomTrendAnalysis(liveRoomId);
        } catch (error) {
          console.warn('⚠️ 获取趋势分析数据失败，将跳过该部分:', error);
        }

        // 获取评论分析数据
      let commentAnalysisInfo = null;
      try {
        commentAnalysisInfo = await getLiveRoomCommentAnalysis(liveRoomId);
      } catch (error) {
        console.warn('⚠️ 获取评论分析数据失败，将跳过该部分:', error);
      }

      // 获取所有评论数据（可选，根据需要启用）
      let allCommentsInfo = null;
      try {
        const allComments = await getAllLiveRoomComments(liveRoomId);
        allCommentsInfo = {
          comments: allComments,
          total: allComments.length
        };
      } catch (error) {
        console.warn('⚠️ 获取所有评论数据失败，将跳过该部分:', error);
      }

      // 获取所有评论数据（可选，根据需要启用）
      let allOtherCommentsInfo = null;
      try {
        const commentsInfo = await getAllLiveRoomAllComments(liveRoomId);
        allOtherCommentsInfo = {
          comments: commentsInfo,
          total: commentsInfo.length
        };
      } catch (error) {
        console.warn('⚠️ 获取所有评论数据失败，将跳过该部分:', error);
      }

      // 获取用户画像数据
      let userPortraitInfo = null;
      try {
        console.log('🔍 开始获取用户画像数据...');
        userPortraitInfo = await getAllUserPortraits(liveRoomId);
        console.log('✅ 用户画像数据获取成功');
      } catch (error) {
        console.warn('⚠️ 获取用户画像数据失败，将跳过该部分:', error);
      }

      // 获取商品列表数据
      let productsListInfo = null;
      try {
        console.log('🔍 开始获取商品列表数据...');
        productsListInfo = await getLiveRoomProductsList(liveRoomId);
        console.log('✅ 商品列表数据获取成功');
      } catch (error) {
        console.warn('⚠️ 获取商品列表数据失败，将跳过该部分:', error);
      }

      return {
        liveRoomId,
        basicInfo,
        tradeInfo,
        diagnosisInfo,
        improvedDirectionInfo,
        flowStructureInfo,
        flowSourceInfo,
        hourlyExposureInfo,
        flowTrendInfo,
        flowAnalysisInfo,
        videoSourceInfo,
        channelFlowInfo,
        flowTrendAnalysisInfo,
        coreGroupInfo,
        adBidInfo,
        qianchuanAdBidInfo,
        commentAnalysisInfo,
        allCommentsInfo,
        allOtherCommentsInfo,
        userPortraitInfo,
        productsListInfo,
        extractTime: new Date().toISOString()
      };
    } else {
      console.log('⚠️ 未能从URL中提取直播间ID');
      return null;
    }
  } catch (error) {
    console.error('❌ API数据提取失败:', error);
    return null;
  }
  }

  /**
   * 从当前URL中提取直播间ID
   * @returns {string|null} 直播间ID
   */
  function extractLiveRoomIdFromURL() {
    try {
      const url = new URL(window.location.href);

      // 尝试从查询参数中获取
      const liveRoomId = url.searchParams.get('live_room_id') ||
        url.searchParams.get('room_id') ||
        url.searchParams.get('id');

      if (liveRoomId) {
        return liveRoomId;
      }

      // 尝试从路径中提取（如果URL包含直播间ID）
      const pathMatch = url.pathname.match(/\/(\d+)/);
      if (pathMatch) {
        return pathMatch[1];
      }

      return null;
    } catch (error) {
      console.error('❌ 提取直播间ID失败:', error);
      return null;
    }
  }

  /**
   * 从DOM提取数据
   * @returns {Object} DOM数据
   */
  function extractDataFromDOM() {
    const domData = {
      pageTitle: document.title,
      tables: [],
      statistics: {}
    };

    // 提取表格数据
    const tables = document.querySelectorAll('table');
    tables.forEach((table, index) => {
      const tableData = extractTableData(table);
      if (tableData.rows.length > 0) {
        domData.tables.push({
          index: index,
          ...tableData
        });
      }
    });

    // 提取统计数据
    const statElements = document.querySelectorAll('[class*="stat"], [class*="metric"], [class*="number"]');
    statElements.forEach((element, index) => {
      const text = element.textContent.trim();
      if (text && /\d/.test(text)) {
        domData.statistics[`stat_${index}`] = text;
      }
    });

    return domData;
  }

  /**
   * 提取表格数据
   * @param {HTMLTableElement} table - 表格元素
   * @returns {Object} 表格数据
   */
  function extractTableData(table) {
    const data = {
      headers: [],
      rows: []
    };

    // 提取表头
    const headerRow = table.querySelector('thead tr, tr:first-child');
    if (headerRow) {
      const headers = headerRow.querySelectorAll('th, td');
      data.headers = Array.from(headers).map(header => header.textContent.trim());
    }

    // 提取数据行
    const rows = table.querySelectorAll('tbody tr, tr:not(:first-child)');
    rows.forEach(row => {
      const cells = row.querySelectorAll('td, th');
      const rowData = Array.from(cells).map(cell => cell.textContent.trim());
      if (rowData.some(cell => cell)) { // 只添加非空行
        data.rows.push(rowData);
      }
    });

    return data;
  }

  // ==================== 数据格式化函数 ====================

  /**
   * 格式化时间戳为可读格式
   * @param {number} timestamp - Unix时间戳（秒）
   * @returns {string} 格式化的时间字符串
   */
  function formatTimestamp(timestamp) {
    if (!timestamp) return '-';

    const date = new Date(timestamp * 1000);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}/${month}/${day} ${hours}:${minutes}`;
  }

  /**
   * 格式化时长（秒）为可读格式
   * @param {number} seconds - 时长（秒）
   * @returns {string} 格式化的时长字符串
   */
  function formatDuration(seconds) {
    if (!seconds || seconds === 0) return '0秒';
    try {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;

      if (hours > 0) {
        return `${hours}小时${minutes}分钟${remainingSeconds}秒`;
      } else if (minutes > 0) {
        return `${minutes}分钟${remainingSeconds}秒`;
      } else {
        return `${remainingSeconds}秒`;
      }
    } catch (error) {
      console.error('❌ 时长格式化失败:', error);
      return '格式错误';
    }
  }

  /**
   * 格式化价格（分转元）
   * @param {number} priceInCents - 价格（分）
   * @returns {string} 格式化的价格字符串
   */
  function formatPrice(priceInCents) {
    if (priceInCents === undefined || priceInCents === null) return '-';
    if (priceInCents === 0) return '¥0.00';
    try {
      const yuan = (priceInCents / 100).toFixed(2);
      return `¥${yuan}`;
    } catch (error) {
      console.error('❌ 价格格式化失败:', error);
      return '格式错误';
    }
  }

  /**
   * 格式化比例（小数转百分比）
   * @param {number} ratio - 比例（小数）
   * @returns {string} 格式化的百分比字符串
   */
  function formatRatio(ratio) {
    if (ratio === undefined || ratio === null) return '-';
    if (ratio === 0) return '0.00%';
    try {
      const percentage = (ratio * 100).toFixed(2);
      const sign = ratio > 0 ? '+' : '';
      return `${sign}${percentage}%`;
    } catch (error) {
      console.error('❌ 比例格式化失败:', error);
      return '格式错误';
    }
  }

  /**
   * 格式化诊断数据的数值
   * @param {Object} valueObj - 包含unit和value的对象
   * @returns {string} 格式化后的数值字符串
   */
  function formatDiagnosisValue(valueObj) {
    if (!valueObj || valueObj.value === undefined || valueObj.value === null) return '-';

    const { unit, value } = valueObj;

    try {
      switch (unit) {
        case 'price':
          return `¥${(value / 100).toFixed(2)}`;
        case 'ratio':
          return `${(value * 100).toFixed(2)}%`;
        case 'time':
          return formatDuration(value);
        case 'number':
          if (value >= 10000) {
            return `${(value / 10000).toFixed(2)}万`;
          }
          return value.toLocaleString();
        default:
          return value.toString();
      }
    } catch (error) {
      console.error('❌ 诊断数值格式化失败:', error);
      return '格式错误';
    }
  }

  /**
   * 通用的数字状态映射函数
   * @param {number} level - 状态级别
   * @param {Object} mapping - 映射配置
   * @returns {string} 映射后的文本
   */
  function mapLevelToText(level, mapping) {
    return mapping[level] || mapping.default || '未知';
  }

  /**
   * 获取诊断结果状态文本
   * @param {number} result - 结果状态码
   * @returns {string} 状态文本
   */
  function getDiagnosisResultText(result) {
    return mapLevelToText(result, LEVEL_MAPPINGS.DIAGNOSIS_RESULT);
  }

  /**
   * 格式化诊断数据为Markdown
   * @param {Object} diagnosisData - 诊断数据
   * @returns {string} Markdown格式的诊断报告
   */
  function formatDiagnosisAsMarkdown(diagnosisData) {
    if (!diagnosisData) return '';

    let markdown = '# 公式拆解\n\n';

    // 成交金额
    if (diagnosisData.pay_amt) {
      const payAmt = diagnosisData.pay_amt;
      markdown += '## 成交金额\n\n';
      markdown += `**${formatDiagnosisValue(payAmt.value)}** - ${getDiagnosisResultText(diagnosisData.result)}\n\n`;

      if (payAmt.change_value) {
        markdown += `对比变化：${formatDiagnosisValue(payAmt.change_value)}\n\n`;
      }

      // 行业对比
      if (diagnosisData.industry_reference && diagnosisData.industry_reference.length > 0) {
        const industryRef = diagnosisData.industry_reference.find(ref => ref.reference_type === 1);
        const excellentRef = diagnosisData.industry_reference.find(ref => ref.reference_type === 2);

        if (industryRef) {
          markdown += `超过同行${formatDiagnosisValue(industryRef.value)}直播间\n\n`;
        }
        if (excellentRef) {
          markdown += `同行同级优秀值为${formatDiagnosisValue(excellentRef.value)}\n\n`;
        }
      }
    }

    // 指标组数据
    if (diagnosisData.index_group && diagnosisData.index_group.length > 0) {
      markdown += '## 关键指标\n\n';

      diagnosisData.index_group.forEach(index => {
        markdown += `### ${index.index_display}\n\n`;
        markdown += `**${formatDiagnosisValue(index.value)}** - ${getDiagnosisResultText(index.result)}\n\n`;

        if (index.change_value) {
          markdown += `对比变化：${formatDiagnosisValue(index.change_value)}\n\n`;
        }

        if (index.industry_value) {
          markdown += `同行同级优秀值：${formatDiagnosisValue(index.industry_value)}\n\n`;
        }

        if (index.index_tip) {
          markdown += `说明：${index.index_tip}\n\n`;
        }

        markdown += '---\n\n';
      });
    }

    return markdown;
  }

  /**
   * 格式化改进方向数据为Markdown
   * @param {Object} improvedDirectionData - 改进方向数据
   * @returns {string} Markdown格式的改进方向报告
   */
  function formatImprovedDirectionAsMarkdown(improvedDirectionData) {
    if (!improvedDirectionData || !improvedDirectionData.improved_direction) return '';

    let markdown = '# 改进方向\n\n';
    const improvedDirection = improvedDirectionData.improved_direction;

    // 处理渠道数据
    const channelDataItem = improvedDirection.find(item => item.channel_data);
    if (channelDataItem && channelDataItem.channel_data && channelDataItem.channel_data.length > 0) {
      markdown += '## 流量渠道分析\n\n';
      markdown += '| 渠道名称 | 指标名称 | 变化幅度 | 趋势 |\n';
      markdown += '|----------|----------|----------|------|\n';

      channelDataItem.channel_data.forEach(channel => {
        const channelName = channel.channel_name || '未知渠道';
        const indexDisplay = channel.value ? channel.value.index_display : '未知指标';
        const changeValue = channel.value && channel.value.change_value
          ? formatDiagnosisValue(channel.value.change_value)
          : '-';
        const resultText = getDiagnosisResultText(channel.result);

        markdown += `| ${channelName} | ${indexDisplay} | ${changeValue} | ${resultText} |\n`;
      });

      markdown += '\n';
    }

    // 处理GPM数据
    const gpmDataItem = improvedDirection.find(item => item.gpm);
    if (gpmDataItem && gpmDataItem.gpm) {
      const gpm = gpmDataItem.gpm;
      markdown += '## GPM分析\n\n';

      if (gpm.value) {
        markdown += `**${gpm.value.index_display}**: `;
        if (gpm.value.value) {
          markdown += `${formatDiagnosisValue(gpm.value.value)} - ${getDiagnosisResultText(gpm.result)}\n\n`;
        } else {
          markdown += `${getDiagnosisResultText(gpm.result)}\n\n`;
        }
      }
    }

    return markdown;
  }

  /**
   * 格式化流量结构数据为Markdown
   * @param {Object} flowStructureData - 流量结构数据
   * @returns {string} Markdown格式的流量结构报告
   */
  function formatFlowStructureAsMarkdown(flowStructureData) {
    if (!flowStructureData) return '';

    let markdown = '# 流量结构分析\n\n';

    // 处理流量指标列表
    if (flowStructureData.item_list && flowStructureData.item_list.length > 0) {
      markdown += '## 流量指标\n\n';
      markdown += '| 指标名称 | 数值 | 等级 | 说明 |\n';
      markdown += '|----------|------|------|------|\n';

      flowStructureData.item_list.forEach(item => {
        const itemTitle = item.item_title || '未知指标';
        const value = item.value ? formatDiagnosisValue(item.value) : '-';

        // 使用全局映射配置处理等级
        const level = item.level !== undefined && item.level !== null
          ? mapLevelToText(item.level, LEVEL_MAPPINGS.QUALITY_LEVEL)
          : '-';

        const hover = item.hover || '-';

        markdown += `| ${itemTitle} | ${value} | ${level} | ${hover} |\n`;
      });

      markdown += '\n';
    }

    // 处理封面质量信息
    if (flowStructureData.room_cover_quality) {
      const coverQuality = flowStructureData.room_cover_quality;
      markdown += '## 封面质量\n\n';

      if (coverQuality.item_title) {
        markdown += `**${coverQuality.item_title}**\n\n`;
      }

      if (coverQuality.quality_level !== undefined) {
        const qualityText = mapLevelToText(coverQuality.quality_level, LEVEL_MAPPINGS.QUALITY_LEVEL);
        markdown += `质量等级：${qualityText} (${coverQuality.quality_level})\n\n`;
      }

      if (coverQuality.explain_text) {
        markdown += `说明：${coverQuality.explain_text}\n\n`;
      }

      if (coverQuality.is_show !== undefined) {
        markdown += `是否显示：${coverQuality.is_show ? '是' : '否'}\n\n`;
      }
    }

    return markdown;
  }

    /**
   * 格式化流量来源数据为Markdown
   * @param {Object} flowSourceData - 流量来源数据
   * @returns {string} Markdown格式的流量来源报告
   */
    function formatFlowSourceAsMarkdown(flowSourceData) {
      if (!flowSourceData) return '';

      let markdown = '# 流量来源分析\n\n';

      // 处理流量总览
      if (flowSourceData.analysis) {
        const analysis = flowSourceData.analysis;
        const natureNum = analysis.nature_num ? formatDiagnosisValue(analysis.nature_num) : '0';
        const payNum = analysis.pay_num ? formatDiagnosisValue(analysis.pay_num) : '0';
        const natureRatio = analysis.nature_ratio ? `${(analysis.nature_ratio * 100).toFixed(2)}%` : '0%';
        const payRatio = analysis.pay_ratio ? `${(analysis.pay_ratio * 100).toFixed(2)}%` : '0%';

        markdown += '## 流量总览\n\n';
        markdown += '| 流量类型 | 数量 | 占比 |\n';
        markdown += '|---------|------|------|\n';
        markdown += `| 自然流量 | ${natureNum} | ${natureRatio} |\n`;
        markdown += `| 付费流量 | ${payNum} | ${payRatio} |\n`;
        markdown += `| 总流量 | ${formatDiagnosisValue({unit: 'number', value: (analysis.nature_num?.value || 0) + (analysis.pay_num?.value || 0)})} | 100% |\n\n`;
      }

      // 处理渠道数据
      if (flowSourceData.channel && flowSourceData.channel.elements && flowSourceData.channel.elements.length > 0) {
        // 按display_name分组
        const groupedData = {};

        flowSourceData.channel.elements.forEach(item => {
          const displayName = item.display_name;
          if (!groupedData[displayName]) {
            groupedData[displayName] = [];
          }
          groupedData[displayName].push(item);
        });

        // 处理本场流量（自然）
        const naturalFlowData = groupedData['本场流量（自然）'] || [];
        if (naturalFlowData.length > 0) {
          markdown += '## 本场自然流量来源\n\n';
          markdown += '| 渠道 | 流量数 | 占比 |\n';
          markdown += '|------|--------|------|\n';

          // 按流量数量排序
          naturalFlowData.sort((a, b) => b.vertical - a.vertical);

          naturalFlowData.forEach(item => {
            if (item.vertical > 0) { // 只显示有流量的渠道
              markdown += `| ${item.horizontal} | ${item.vertical.toLocaleString()} | ${(item.ratio * 100).toFixed(2)}% |\n`;
            }
          });

          markdown += '\n';
        }

        // 处理本场流量（付费）
        const paidFlowData = groupedData['本场流量（付费）'] || [];
        const hasPaidFlow = paidFlowData.some(item => item.vertical > 0);

        if (hasPaidFlow) {
          markdown += '## 本场付费流量来源\n\n';
          markdown += '| 渠道 | 流量数 | 占比 |\n';
          markdown += '|------|--------|------|\n';

          // 按流量数量排序
          paidFlowData.sort((a, b) => b.vertical - a.vertical);

          paidFlowData.forEach(item => {
            if (item.vertical > 0) { // 只显示有流量的渠道
              markdown += `| ${item.horizontal} | ${item.vertical.toLocaleString()} | ${(item.ratio * 100).toFixed(2)}% |\n`;
            }
          });

          markdown += '\n';
        }

        // 处理历史对比数据
        const historyData = groupedData['近7天相同时间段本账号中位数'] || [];
        if (historyData.length > 0) {
          markdown += '## 与近7天相同时间段对比\n\n';
          markdown += '| 渠道 | 本场流量 | 近7天中位数 | 变化率 |\n';
          markdown += '|------|----------|------------|--------|\n';

          // 按本场流量数量排序
          const sortedChannels = [...new Set([...naturalFlowData, ...paidFlowData].map(item => item.horizontal))];

          sortedChannels.forEach(channel => {
            const currentItem = [...naturalFlowData, ...paidFlowData].find(item => item.horizontal === channel);
            const historyItem = historyData.find(item => item.horizontal === channel);

            if (currentItem && historyItem) {
              const currentValue = currentItem.vertical;
              const historyValue = historyItem.vertical;
              let changeRate = '0%';

              if (historyValue > 0) {
                const rate = ((currentValue - historyValue) / historyValue * 100).toFixed(2);
                changeRate = rate > 0 ? `+${rate}%` : `${rate}%`;
              } else if (currentValue > 0) {
                changeRate = '+∞%';
              }

              markdown += `| ${channel} | ${currentValue.toLocaleString()} | ${historyValue.toLocaleString()} | ${changeRate} |\n`;
            }
          });

          markdown += '\n';
        }
      }

      return markdown;
    }

  /**
   * 格式化单小时曝光次数数据为Markdown
   * @param {Object} hourlyExposureData - 单小时曝光次数数据
   * @returns {string} Markdown格式的单小时曝光次数报告
   */
  function formatHourlyExposureAsMarkdown(hourlyExposureData) {
    if (!hourlyExposureData) return '';

    let markdown = '# 单小时曝光次数分析\n\n';

    // 处理流量总览
    if (hourlyExposureData.analysis) {
      const analysis = hourlyExposureData.analysis;
      const natureNum = analysis.nature_num ? formatDiagnosisValue(analysis.nature_num) : '0';
      const payNum = analysis.pay_num ? formatDiagnosisValue(analysis.pay_num) : '0';
      const natureRatio = analysis.nature_ratio ? `${(analysis.nature_ratio * 100).toFixed(2)}%` : '0%';
      const payRatio = analysis.pay_ratio ? `${(analysis.pay_ratio * 100).toFixed(2)}%` : '0%';

      markdown += '## 单小时流量总览\n\n';
      markdown += '| 流量类型 | 数量 | 占比 |\n';
      markdown += '|---------|------|------|\n';
      markdown += `| 自然流量 | ${natureNum} | ${natureRatio} |\n`;
      markdown += `| 付费流量 | ${payNum} | ${payRatio} |\n`;
      markdown += `| 总流量 | ${formatDiagnosisValue({unit: 'number', value: (analysis.nature_num?.value || 0) + (analysis.pay_num?.value || 0)})} | 100% |\n\n`;
    }

    // 处理渠道数据
    if (hourlyExposureData.channel && hourlyExposureData.channel.elements && hourlyExposureData.channel.elements.length > 0) {
      // 按display_name分组
      const groupedData = {};

      hourlyExposureData.channel.elements.forEach(item => {
        const displayName = item.display_name;
        if (!groupedData[displayName]) {
          groupedData[displayName] = [];
        }
        groupedData[displayName].push(item);
      });

      // 处理本场流量（自然）
      const naturalFlowData = groupedData['本场流量（自然）'] || [];
      if (naturalFlowData.length > 0) {
        markdown += '## 单小时自然流量来源\n\n';
        markdown += '| 渠道 | 流量数 | 占比 |\n';
        markdown += '|------|--------|------|\n';

        // 按流量数量排序
        naturalFlowData.sort((a, b) => b.vertical - a.vertical);

        naturalFlowData.forEach(item => {
          if (item.vertical > 0) { // 只显示有流量的渠道
            markdown += `| ${item.horizontal} | ${item.vertical.toLocaleString()} | ${(item.ratio * 100).toFixed(2)}% |\n`;
          }
        });

        markdown += '\n';
      }

      // 处理本场流量（付费）
      const paidFlowData = groupedData['本场流量（付费）'] || [];
      const hasPaidFlow = paidFlowData.some(item => item.vertical > 0);

      if (hasPaidFlow) {
        markdown += '## 单小时付费流量来源\n\n';
        markdown += '| 渠道 | 流量数 | 占比 |\n';
        markdown += '|------|--------|------|\n';

        // 按流量数量排序
        paidFlowData.sort((a, b) => b.vertical - a.vertical);

        paidFlowData.forEach(item => {
          if (item.vertical > 0) { // 只显示有流量的渠道
            markdown += `| ${item.horizontal} | ${item.vertical.toLocaleString()} | ${(item.ratio * 100).toFixed(2)}% |\n`;
          }
        });

        markdown += '\n';
      }

      // 处理历史对比数据
      const historyData = groupedData['近7天相同时间段本账号中位数'] || [];
      if (historyData.length > 0) {
        markdown += '## 与近7天相同时间段对比\n\n';
        markdown += '| 渠道 | 本场流量 | 近7天中位数 | 变化率 |\n';
        markdown += '|------|----------|------------|--------|\n';

        // 按本场流量数量排序
        const sortedChannels = [...new Set([...naturalFlowData, ...paidFlowData].map(item => item.horizontal))];

        sortedChannels.forEach(channel => {
          const currentItem = [...naturalFlowData, ...paidFlowData].find(item => item.horizontal === channel);
          const historyItem = historyData.find(item => item.horizontal === channel);

          if (currentItem && historyItem) {
            const currentValue = currentItem.vertical;
            const historyValue = historyItem.vertical;
            let changeRate = '0%';

            if (historyValue > 0) {
              const rate = ((currentValue - historyValue) / historyValue * 100).toFixed(2);
              changeRate = rate > 0 ? `+${rate}%` : `${rate}%`;
            } else if (currentValue > 0) {
              changeRate = '+∞%';
            }

            markdown += `| ${channel} | ${currentValue.toLocaleString()} | ${historyValue.toLocaleString()} | ${changeRate} |\n`;
          }
        });

        markdown += '\n';
      }
    }

    return markdown;
  }

  /**
   * 格式化流量趋势数据为Markdown
   * @param {Object} flowTrendData - 流量趋势数据
   * @returns {string} Markdown格式的流量趋势报告
   */
  function formatFlowTrendAsMarkdown(flowTrendData) {
    if (!flowTrendData) return '';

    let markdown = '# 流量趋势分析\n\n';

    // 处理渠道趋势数据
    if (flowTrendData.channel_trend && flowTrendData.channel_trend.trends && flowTrendData.channel_trend.trends.length > 0) {
      markdown += '## 全部流量曝光趋势\n\n';
      markdown += '| 时间 | 曝光次数 |\n';
      markdown += '|------|----------|\n';

      flowTrendData.channel_trend.trends.forEach(trend => {
        const time = trend.horizontal || formatTimestamp(trend.date_time);
        const value = trend.vertical.toLocaleString();
        markdown += `| ${time} | ${value} |\n`;
      });

      markdown += '\n';
    }

    // 处理辅助指标趋势数据
    if (flowTrendData.auxiliary_index_trend && flowTrendData.auxiliary_index_trend.trends && flowTrendData.auxiliary_index_trend.trends.length > 0) {
      markdown += '## 直播间曝光-观看率趋势\n\n';
      markdown += '| 时间 | 观看率 |\n';
      markdown += '|------|--------|\n';

      flowTrendData.auxiliary_index_trend.trends.forEach(trend => {
        const time = trend.horizontal || formatTimestamp(trend.date_time);
        const value = `${(trend.right_vertical * 100).toFixed(2)}%`;
        markdown += `| ${time} | ${value} |\n`;
      });

      markdown += '\n';
    }

    return markdown;
  }

  /**
   * 格式化流量分析数据为Markdown
   * @param {Object} flowAnalysisData - 流量分析数据
   * @returns {string} Markdown格式的流量分析报告
   */
  function formatFlowAnalysisAsMarkdown(flowAnalysisData) {
    if (!flowAnalysisData) return '';

    let markdown = '# 直播间流量分析\n\n';

    // 处理成交转化漏斗
    if (flowAnalysisData.gmv_change && flowAnalysisData.gmv_change.length > 0) {
      markdown += '## 成交转化漏斗\n\n';
      markdown += '| 指标名称 | 数值 | 同行对比 | 对比率 |\n';
      markdown += '|---------|------|----------|--------|\n';

      flowAnalysisData.gmv_change.forEach(item => {
        const indexName = item.index_name || '未知指标';
        const value = item.value ? formatDiagnosisValue(item.value) : '-';
        const benchmarkValue = item.benchmark_value ? formatDiagnosisValue(item.benchmark_value) : '-';

        let benchmarkRate = '-';
        if (item.benchmark_rate && item.benchmark_rate.unit === 'ratio') {
          benchmarkRate = `${(item.benchmark_rate.value * 100).toFixed(2)}%`;
        }

        markdown += `| ${indexName} | ${value} | ${benchmarkValue} | ${benchmarkRate} |\n`;

        // 处理子指标
        if (item.sub_index && item.sub_index.length > 0) {
          item.sub_index.forEach(subItem => {
            const subIndexName = `  - ${subItem.index_name}`;
            const subValue = subItem.value ? formatDiagnosisValue(subItem.value) : '-';

            markdown += `| ${subIndexName} | ${subValue} | - | - |\n`;
          });
        }
      });

      markdown += '\n';
    }

    // 处理互动转化漏斗
    if (flowAnalysisData.interaction_change && flowAnalysisData.interaction_change.length > 0) {
      markdown += '## 互动转化漏斗\n\n';
      markdown += '| 指标名称 | 数值 | 同行对比 | 对比率 |\n';
      markdown += '|---------|------|----------|--------|\n';

      flowAnalysisData.interaction_change.forEach(item => {
        const indexName = item.index_name || '未知指标';
        const value = item.value ? formatDiagnosisValue(item.value) : '-';
        const benchmarkValue = item.benchmark_value ? formatDiagnosisValue(item.benchmark_value) : '-';

        let benchmarkRate = '-';
        if (item.benchmark_rate && item.benchmark_rate.unit === 'ratio') {
          benchmarkRate = `${(item.benchmark_rate.value * 100).toFixed(2)}%`;
        }

        markdown += `| ${indexName} | ${value} | ${benchmarkValue} | ${benchmarkRate} |\n`;

        // 处理子指标
        if (item.sub_index && item.sub_index.length > 0) {
          item.sub_index.forEach(subItem => {
            const subIndexName = `  - ${subItem.index_name}`;
            const subValue = subItem.value ? formatDiagnosisValue(subItem.value) : '-';

            markdown += `| ${subIndexName} | ${subValue} | - | - |\n`;
          });
        }
      });

      markdown += '\n';
    }

    return markdown;
  }


    /**
   * 获取直播间流量趋势分析数据
   * @param {string} roomId - 直播间ID
   * @param {Array<string>} indexSelected - 选择的指标，默认包含多个常用指标
   * @returns {Promise<Object>} 流量趋势分析数据
   */
    async function getLiveRoomFlowTrendAnalysis(roomId, indexSelected = [
      'online_user_cnt', 'watch_ucnt', 'leave_ucnt', 'viewing_rate', 'per_capita_viewing_time',
      'interaction_rate', 'attention_rate', 'negative_feedback_rate', 'negative_feedback_cnt',
      'incr_fans_cnt', 'fans_club_ucnt', 'comment_cnt', 'like_cnt', 'gpm', 'pay_amt',
      'viewing_click_rate', 'click_transaction_rate', 'product_show_ucnt', 'product_click_ucnt',
      'pay_cnt', 'pay_ucnt'
    ]) {
      try {
        const indexParam = indexSelected.join(',');
        const url = `${API_CONFIG.baseURL}author/live/live_room_detail/flow/trend_analysis_v2?index_selected=${indexParam}&live_room_id=${roomId}`;

        const response = await fetch(url, {
          method: 'GET',
          headers: API_CONFIG.defaultHeaders,
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        if (data.st !== 0) {
          throw new Error(`API返回错误: ${data.msg || '未知错误'}`);
        }

        return data.data;
      } catch (error) {
        console.error('获取直播间流量趋势分析数据失败:', error);
        throw error;
      }
    }

    /**
     * 格式化流量趋势分析数据为Markdown
     * @param {Object} flowTrendAnalysisData - 流量趋势分析数据
     * @returns {string} Markdown格式的流量趋势分析报告
     */
    function formatFlowTrendAnalysisAsMarkdown(flowTrendAnalysisData) {
      if (!flowTrendAnalysisData) return '';

      let markdown = '# 直播间流量趋势分析\n\n';

      // 添加调试信息
      // console.log('流量趋势分析数据结构:', JSON.stringify(flowTrendAnalysisData, null, 2));

      // 检查数据结构 - 更灵活的检查
      if (!flowTrendAnalysisData || typeof flowTrendAnalysisData !== 'object') {
        markdown += '数据为空或格式不正确\n\n';
        return markdown;
      }

      // 如果数据结构不符合预期，尝试显示原始数据
      if (!flowTrendAnalysisData.index_groups || !flowTrendAnalysisData.trend_map) {
        markdown += '## 原始数据结构\n\n';
        markdown += '数据结构不符合预期，以下是原始数据：\n\n';
        markdown += '```json\n';
        markdown += JSON.stringify(flowTrendAnalysisData, null, 2);
        markdown += '\n```\n\n';

        // 尝试处理其他可能的数据结构
        if (flowTrendAnalysisData.data || flowTrendAnalysisData.result) {
          const actualData = flowTrendAnalysisData.data || flowTrendAnalysisData.result;
          markdown += '## 尝试解析嵌套数据\n\n';
          markdown += '```json\n';
          markdown += JSON.stringify(actualData, null, 2);
          markdown += '\n```\n\n';
        }

        return markdown;
      }

      // 处理指标组数据
      if (flowTrendAnalysisData.index_groups && flowTrendAnalysisData.index_groups.length > 0) {
        // 遍历每个指标组
        flowTrendAnalysisData.index_groups.forEach(group => {
          markdown += `## ${group.group_display || group.group_name || '未命名指标组'}\n\n`;

          // 处理组内的每个指标 - 修正字段名为 list
          if (group.list && group.list.length > 0) {
            group.list.forEach(index => {
              const indexName = index.index_name;
              const indexDisplay = index.index_display || indexName || '未命名指标';
              markdown += `### ${indexDisplay}\n\n`;

              // 添加指标说明（如果有）
              if (index.hover_tip || index.index_tip) {
                markdown += `> ${index.hover_tip || index.index_tip}\n\n`;
              }

              // 添加跳转链接（如果有）
              if (index.jump_text && index.jump_url) {
                markdown += `📖 [${index.jump_text}](${index.jump_url})\n\n`;
              }

              // 获取趋势数据
              const dataType = flowTrendAnalysisData.data_type_map[indexName];
              let trendData = [];

              // 根据数据类型从trend_map中获取数据
              if (flowTrendAnalysisData.trend_map.number && dataType !== 1) {
                trendData = flowTrendAnalysisData.trend_map.number.filter(item =>
                  item.point_name === indexName
                );
              } else if (flowTrendAnalysisData.trend_map.price && dataType === 1) {
                trendData = flowTrendAnalysisData.trend_map.price.filter(item =>
                  item.point_name === indexName
                );
              }

              // 处理趋势数据
              if (trendData && trendData.length > 0) {
                markdown += '| 时间 | 数值 |\n';
                markdown += '|------|------|\n';

                // 按时间排序
                trendData.sort((a, b) => a.date_time - b.date_time);

                // 显示全部数据点，不进行截取
                trendData.forEach(point => {
                  const time = point.horizontal || formatTimestamp(point.date_time);
                  let value = '-';

                  // 根据不同的数据类型格式化值
                  if (point.vertical !== undefined && point.vertical !== null) {
                    if (dataType === 4) { // 比率
                      value = `${(point.vertical * 100).toFixed(2)}%`;
                    } else if (dataType === 1) { // 价格
                      value = `¥${point.vertical.toFixed(2)}`;
                    } else if (dataType === 2) { // 时长
                      value = formatDuration(point.vertical);
                    } else if (dataType === 5) { // 负反馈次数等特殊数字
                      value = Math.round(point.vertical).toLocaleString();
                    } else { // 普通数字
                      value = point.vertical.toLocaleString();
                    }
                  }

                  markdown += `| ${time} | ${value} |\n`;
                });

                markdown += '\n';
              } else {
                markdown += '暂无趋势数据\n\n';
              }

              // 添加对比数据（如果有）
              if (index.compare && index.compare.length > 0) {
                markdown += '#### 对比数据\n\n';
                markdown += '| 对比项 | 当前值 | 对比值 | 变化率 |\n';
                markdown += '|--------|--------|--------|--------|\n';

                index.compare.forEach(comp => {
                  const name = comp.compare_name || '未知';
                  let currentValue = '-';
                  let compareValue = '-';
                  let changeRate = '-';

                  // 格式化当前值
                  if (comp.current_value !== undefined && comp.current_value !== null) {
                    switch (index.unit) {
                      case 'ratio':
                        currentValue = `${(comp.current_value * 100).toFixed(2)}%`;
                        break;
                      case 'price':
                        currentValue = `¥${comp.current_value.toFixed(2)}`;
                        break;
                      case 'time':
                        currentValue = formatDuration(comp.current_value);
                        break;
                      default:
                        currentValue = comp.current_value.toLocaleString();
                    }
                  }

                  // 格式化对比值
                  if (comp.compare_value !== undefined && comp.compare_value !== null) {
                    switch (index.unit) {
                      case 'ratio':
                        compareValue = `${(comp.compare_value * 100).toFixed(2)}%`;
                        break;
                      case 'price':
                        compareValue = `¥${comp.compare_value.toFixed(2)}`;
                        break;
                      case 'time':
                        compareValue = formatDuration(comp.compare_value);
                        break;
                      default:
                        compareValue = comp.compare_value.toLocaleString();
                    }
                  }

                  // 格式化变化率
                  if (comp.change_rate !== undefined && comp.change_rate !== null) {
                    const rate = comp.change_rate * 100;
                    changeRate = rate > 0 ? `+${rate.toFixed(2)}%` : `${rate.toFixed(2)}%`;
                  }

                  markdown += `| ${name} | ${currentValue} | ${compareValue} | ${changeRate} |\n`;
                });

                markdown += '\n';
              }
            });
          } else {
            markdown += '该指标组暂无具体指标数据\n\n';
          }
        });
      } else {
        markdown += '暂无流量趋势分析数据\n\n';
      }

      return markdown;
    }

    /**
     * 格式化时长（秒）为可读格式
     * @param {number} seconds - 秒数
     * @returns {string} 格式化后的时长
     */
    function formatDuration(seconds) {
      if (seconds === undefined || seconds === null) return '-';

      if (seconds < 60) {
        return `${seconds.toFixed(0)}秒`;
      } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}分${remainingSeconds}秒`;
      } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${hours}时${minutes}分${remainingSeconds}秒`;
      }
    }

    /**
     * 测试流量趋势分析API功能
     * @param {string} roomId - 直播间ID，默认使用示例ID
     */
    async function testFlowTrendAnalysisAPI(roomId = '7524295568181463823') {
      console.log(`🧪 开始测试流量趋势分析API功能，房间ID: ${roomId}`);

      try {
        // 测试API调用
        const flowTrendAnalysisData = await getLiveRoomFlowTrendAnalysis(roomId);
        console.log('✅ 流量趋势分析API调用成功');
        console.log('📊 API返回数据类型:', typeof flowTrendAnalysisData);
        console.log('📊 API返回数据键:', Object.keys(flowTrendAnalysisData || {}));
        console.log('📊 完整API数据:', flowTrendAnalysisData);

        // 测试数据格式化
        const markdownContent = formatFlowTrendAnalysisAsMarkdown(flowTrendAnalysisData);
        console.log('✅ 数据格式化完成');
        console.log('📄 Markdown内容长度:', markdownContent.length);
        console.log('📄 生成的Markdown内容:');
        console.log(markdownContent);

        // 如果markdown为空，尝试生成调试版本
        if (!markdownContent || markdownContent.trim() === '' || markdownContent.length < 50) {
          console.warn('⚠️ Markdown内容为空或过短，生成调试版本');
          const debugMarkdown = `# 流量趋势分析调试信息\n\n## API返回数据\n\n\`\`\`json\n${JSON.stringify(flowTrendAnalysisData, null, 2)}\n\`\`\`\n\n`;

          const debugTestData = {
            extractedData: {
              apiData: {
                flowTrendAnalysisInfo: flowTrendAnalysisData
              }
            }
          };

          downloadAsMarkdown(debugTestData, `flow-trend-analysis-debug-${roomId}.md`);
          return { success: true, data: flowTrendAnalysisData, markdown: debugMarkdown, debug: true };
        }

        // 测试下载功能
        const testData = {
          extractedData: {
            apiData: {
              flowTrendAnalysisInfo: flowTrendAnalysisData
            }
          }
        };

        downloadAsMarkdown(testData, `flow-trend-analysis-test-${roomId}.md`);

        return { success: true, data: flowTrendAnalysisData, markdown: markdownContent };
      } catch (error) {
        console.error('❌ 流量趋势分析API测试失败:', error);
        return { success: false, error: error.message };
      }
    }

  /**
   * 获取直播间核心数据分组信息
   * @param {string} roomId - 直播间ID
   * @returns {Promise<Object>} 核心数据分组信息
   */
  async function getLiveRoomCoreGroupInfo(roomId) {
    try {
      const url = `https://compass.jinritemai.com/compass_api/author/live/live_room_detail/core_group_info?live_room_id=${roomId}`;
      const response = await fetch(url);
      const data = await response.json();

      if (data.st !== 0) {
        throw new Error(`获取核心数据分组信息失败: ${data.msg || '未知错误'}`);
      }

      return data.data;
    } catch (error) {
      console.error('获取直播间核心数据分组信息失败:', error);
      throw error;
    }
  }

  /**
   * 格式化核心数据分组为Markdown
   * @param {Object} coreGroupData - 核心数据分组数据
   * @returns {string} Markdown格式的核心数据分组报告
   */
  function formatCoreGroupInfoAsMarkdown(coreGroupData) {
    if (!coreGroupData) return '';

    let markdown = '# 核心数据分组\n\n';

    // 处理指标组数据
    if (coreGroupData.index_group && coreGroupData.index_group.length > 0) {
      markdown += '## 核心指标分组\n\n';

      // 创建表格
      markdown += '| 分组 | 指标名称 | 数值 |\n';
      markdown += '|------|---------|------|\n';

      // 遍历每个分组
      coreGroupData.index_group.forEach(group => {
        const groupName = group.group_name || '未命名分组';

        // 遍历分组内的指标
        if (group.index_group && group.index_group.length > 0) {
          group.index_group.forEach((index, i) => {
            const indexName = index.index_display || index.index_name || '未命名指标';
            const value = formatDiagnosisValue(index.value);

            // 只有第一行显示分组名称
            if (i === 0) {
              markdown += `| ${groupName} | ${indexName} | ${value} |\n`;
            } else {
              markdown += `|  | ${indexName} | ${value} |\n`;
            }
          });
        } else {
          markdown += `| ${groupName} | 无数据 | - |\n`;
        }
      });

      markdown += '\n';
    }

    // 处理流量指标列表
    if (coreGroupData.flow_index_list && coreGroupData.flow_index_list.length > 0) {
      markdown += '## 流量指标\n\n';
      markdown += '| 指标名称 | 当前值 | 历史值 | 变化率 |\n';
      markdown += '|---------|--------|--------|--------|\n';

      coreGroupData.flow_index_list.forEach(index => {
        const indexName = index.index_display || index.index_name || '未命名指标';
        const currentValue = formatDiagnosisValue(index.value);
        const historyValue = index.his_value ? formatDiagnosisValue(index.his_value) : '-';
        const changeRate = index.change_value ? formatRatio(index.change_value.value) : '-';

        markdown += `| ${indexName} | ${currentValue} | ${historyValue} | ${changeRate} |\n`;

        // 添加指标说明（如果有）
        if (index.index_tip) {
          markdown += `| | *${index.index_tip}* | | |\n`;
        }
      });

      markdown += '\n';
    }

    // 处理互动指标列表
    if (coreGroupData.interaty_index_list && coreGroupData.interaty_index_list.length > 0) {
      markdown += '## 互动指标\n\n';
      markdown += '| 指标名称 | 当前值 | 历史值 | 变化率 |\n';
      markdown += '|---------|--------|--------|--------|\n';

      coreGroupData.interaty_index_list.forEach(index => {
        const indexName = index.index_display || index.index_name || '未命名指标';
        const currentValue = formatDiagnosisValue(index.value);
        const historyValue = index.his_value ? formatDiagnosisValue(index.his_value) : '-';
        const changeRate = index.change_value ? formatRatio(index.change_value.value) : '-';

        markdown += `| ${indexName} | ${currentValue} | ${historyValue} | ${changeRate} |\n`;
      });

      markdown += '\n';
    }

    // 处理人群指标列表
    if (coreGroupData.portrait_index_list && coreGroupData.portrait_index_list.length > 0) {
      markdown += '## 人群指标\n\n';
      markdown += '| 指标名称 | 当前值 | 历史值 | 变化率 |\n';
      markdown += '|---------|--------|--------|--------|\n';

      coreGroupData.portrait_index_list.forEach(index => {
        const indexName = index.index_display || index.index_name || '未命名指标';
        const currentValue = formatDiagnosisValue(index.value);
        const historyValue = index.his_value ? formatDiagnosisValue(index.his_value) : '-';
        const changeRate = index.change_value ? formatRatio(index.change_value.value) : '-';

        markdown += `| ${indexName} | ${currentValue} | ${historyValue} | ${changeRate} |\n`;

        // 添加指标说明（如果有）
        if (index.index_tip) {
          markdown += `| | *${index.index_tip}* | | |\n`;
        }
      });

      markdown += '\n';
    }

    // 处理商品指标列表
    if (coreGroupData.product_index_list && coreGroupData.product_index_list.length > 0) {
      markdown += '## 商品指标\n\n';
      markdown += '| 指标名称 | 当前值 | 历史值 | 变化率 |\n';
      markdown += '|---------|--------|--------|--------|\n';

      coreGroupData.product_index_list.forEach(index => {
        const indexName = index.index_display || index.index_name || '未命名指标';
        const currentValue = formatDiagnosisValue(index.value);
        const historyValue = index.his_value ? formatDiagnosisValue(index.his_value) : '-';
        const changeRate = index.change_value ? formatRatio(index.change_value.value) : '-';

        markdown += `| ${indexName} | ${currentValue} | ${historyValue} | ${changeRate} |\n`;
      });

      markdown += '\n';
    }

    // 处理退款指标列表
    if (coreGroupData.refund_index_list && coreGroupData.refund_index_list.length > 0) {
      markdown += '## 售后指标\n\n';
      markdown += '| 指标名称 | 当前值 | 历史值 | 变化率 |\n';
      markdown += '|---------|--------|--------|--------|\n';

      coreGroupData.refund_index_list.forEach(index => {
        const indexName = index.index_display || index.index_name || '未命名指标';
        const currentValue = formatDiagnosisValue(index.value);
        const historyValue = index.his_value ? formatDiagnosisValue(index.his_value) : '-';
        const changeRate = index.change_value ? formatRatio(index.change_value.value) : '-';

        markdown += `| ${indexName} | ${currentValue} | ${historyValue} | ${changeRate} |\n`;

        // 添加指标说明（如果有）
        if (index.index_tip) {
          markdown += `| | *${index.index_tip}* | | |\n`;
        }
      });

      markdown += '\n';
    }

    return markdown;
  }

  /**
   * 测试核心数据分组API功能
   * @param {string} roomId - 直播间ID，默认使用示例ID
   */
  async function testCoreGroupInfoAPI(roomId = '7524295568181463823') {
    console.log(`🧪 开始测试核心数据分组API功能，房间ID: ${roomId}`);

    try {
      // 测试API调用
      const coreGroupData = await getLiveRoomCoreGroupInfo(roomId);
      console.log('✅ 核心数据分组API调用成功:', coreGroupData);

      // 测试数据格式化
      const markdownContent = formatCoreGroupInfoAsMarkdown(coreGroupData);
      console.log('✅ 数据格式化成功');
      console.log('📄 生成的Markdown内容:');
      console.log(markdownContent);

      // 测试下载功能
      const testData = {
        extractedData: {
          apiData: {
            coreGroupInfo: coreGroupData
          }
        }
      };

      downloadAsMarkdown(testData, `core-group-info-test-${roomId}.md`);

      return { success: true, data: coreGroupData, markdown: markdownContent };
    } catch (error) {
      console.error('❌ 核心数据分组API测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取直播间竞价广告数据
   * @param {string} roomId - 直播间ID
   * @returns {Promise<Object>} 竞价广告数据
   */
  async function getLiveRoomAdBidData(roomId) {
    try {
      const url = `https://compass.jinritemai.com/business_api/author/live_detail/live_room/ad?ad_type=bid&live_room_id=${roomId}`;
      const response = await fetch(url, {
        method: 'GET',
        headers: API_CONFIG.defaultHeaders,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      if (data.st !== 0) {
        throw new Error(`API返回错误: ${data.msg || '未知错误'}`);
      }

      return data.data;
    } catch (error) {
      console.error('获取直播间竞价广告数据失败:', error);
      throw error;
    }
  }

  /**
   * 格式化竞价广告数据为Markdown
   * @param {Object} adBidData - 竞价广告数据
   * @returns {string} Markdown格式的竞价广告报告
   */
  function formatAdBidDataAsMarkdown(adBidData) {
    if (!adBidData) return '';

    let markdown = '# 竞价广告核心数据\n\n';

    // 处理核心数据
    if (adBidData.core_data && adBidData.core_data.length > 0) {
      markdown += '## 竞价广告核心数据\n\n';

      // 创建表格
      markdown += '| 指标名称 | 数值 | 说明 |\n';
      markdown += '|---------|------|------|\n';

      adBidData.core_data.forEach(item => {
        const indexName = item.index_display || '未命名指标';
        const value = formatDiagnosisValue(item.value);
        const tip = item.index_tip || '';

        markdown += `| ${indexName} | ${value} | ${tip} |\n`;
      });

      markdown += '\n';
    }

    // 处理广告转化漏斗
    if (adBidData.ad_trans) {
      markdown += '## 竞价直播广告成单漏斗\n\n';

      // 创建表格
      markdown += '| 指标名称 | 数值 |\n';
      markdown += '|---------|------|\n';

      markdown += `| 进入直播间人次 | ${adBidData.ad_trans.watch_cnt || 0} |\n`;
      markdown += `| 商品点击次数 | ${adBidData.ad_trans.product_click_cnt || 0} |\n`;
      markdown += `| 直接创建订单数 | ${adBidData.ad_trans.create_order_cnt || 0} |\n`;
      markdown += `| 直接成交订单数 | ${adBidData.ad_trans.pay_order_cnt || 0} |\n`;

      // 计算转化率
      const watchCnt = adBidData.ad_trans.watch_cnt || 0;
      const clickCnt = adBidData.ad_trans.product_click_cnt || 0;
      const createOrderCnt = adBidData.ad_trans.create_order_cnt || 0;
      const payOrderCnt = adBidData.ad_trans.pay_order_cnt || 0;

      // 添加转化率信息
      markdown += '\n### 转化率\n\n';

      // 商品点击转化率
      const clickRate = watchCnt > 0 ? (clickCnt / watchCnt * 100).toFixed(2) + '%' : '0.00%';
      markdown += `- 商品点击转化率: ${clickRate}\n`;

      // 创建订单转化率
      const createOrderRate = clickCnt > 0 ? (createOrderCnt / clickCnt * 100).toFixed(2) + '%' : '0.00%';
      markdown += `- 创建订单转化率: ${createOrderRate}\n`;

      // 订单支付率
      const payOrderRate = createOrderCnt > 0 ? (payOrderCnt / createOrderCnt * 100).toFixed(2) + '%' : '0.00%';
      markdown += `- 订单支付率: ${payOrderRate}\n`;

      // 进入-成交转化率
      const watchToPayRate = watchCnt > 0 ? (payOrderCnt / watchCnt * 100).toFixed(2) + '%' : '0.00%';
      markdown += `- 进入-成交转化率: ${watchToPayRate}\n\n`;
    }

    // 处理成本数据
    if (adBidData.cost_data && adBidData.cost_data.length > 0 && adBidData.data_head) {
      markdown += '## 竞价直播广告成本数据\n\n';

      // 创建表格头部
      markdown += '| ';
      const headers = adBidData.data_head.map(header => header.index_display);
      markdown += headers.join(' | ');
      markdown += ' |\n';

      // 创建表格分隔行
      markdown += '|' + '---|'.repeat(headers.length) + '\n';

      // 创建表格数据行
      adBidData.cost_data.forEach(item => {
        markdown += '| ';
        const values = adBidData.data_head.map(header => item[header.index_name] || '-');
        markdown += values.join(' | ');
        markdown += ' |\n';
      });

      markdown += '\n';
    }

    return markdown;
  }

  /**
   * 测试竞价广告API功能
   * @param {string} roomId - 直播间ID，默认使用示例ID
   */
  async function testAdBidAPI(roomId = '7522830520200694564') {
    console.log(`🧪 开始测试竞价广告API功能，房间ID: ${roomId}`);

    try {
      // 测试API调用
      const adBidData = await getLiveRoomAdBidData(roomId);
      console.log('✅ 竞价广告API调用成功:', adBidData);

      // 测试数据格式化
      const markdownContent = formatAdBidDataAsMarkdown(adBidData);
      console.log('✅ 数据格式化成功');
      console.log('📄 生成的Markdown内容:');
      console.log(markdownContent);

      // 测试下载功能
      const testData = {
        extractedData: {
          apiData: {
            adBidInfo: adBidData
          }
        }
      };

      downloadAsMarkdown(testData, `ad-bid-test-${roomId}.md`);

      return { success: true, data: adBidData, markdown: markdownContent };
    } catch (error) {
      console.error('❌ 竞价广告API测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
 * 获取直播间用户画像数据
 * @param {string} roomId - 直播间ID
 * @param {string} fansType - 粉丝类型：all(全部)、fans(粉丝)、un_fans(非粉丝)
 * @returns {Promise<Object>} 用户画像数据
 */
async function getLiveRoomUserPortrait(roomId, fansType = 'all') {
  if (!roomId) {
    throw new Error('直播间ID不能为空');
  }

  // 验证粉丝类型参数
  if (!['all', 'fans', 'un_fans'].includes(fansType)) {
    throw new Error('粉丝类型参数无效，必须是 all、fans 或 un_fans');
  }

  try {
    const url = `https://compass.jinritemai.com/compass_api/content_live/author/live_room_detail/user_portrait?live_room_id=${roomId}&user_type=watch_user&fans_type=${fansType}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: API_CONFIG.defaultHeaders,
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    if (data.st !== 0) {
      throw new Error(`API返回错误: ${data.msg || '未知错误'}`);
    }

    return data.data;
  } catch (error) {
    console.error(`❌ 获取直播间用户画像数据失败 [粉丝类型:${fansType}]:`, error);
    throw error;
  }
}

/**
 * 格式化百分比值
 * @param {number} value - 小数形式的百分比值
 * @returns {string} 格式化后的百分比字符串
 */
function formatPercentage(value) {
  return (value * 100).toFixed(2) + '%';
}

/**
 * 格式化用户画像数据为Markdown
 * @param {Object} portraitData - 用户画像数据
 * @param {string} fansType - 粉丝类型：all(全部)、fans(粉丝)、un_fans(非粉丝)
 * @returns {string} Markdown格式的用户画像报告
 */
function formatUserPortraitAsMarkdown(portraitData, fansType) {
  if (!portraitData) {
    return '# 用户画像分析\n\n暂无用户画像数据\n\n';
  }

  // 根据粉丝类型设置标题
  const fansTypeText = {
    'all': '全部用户',
    'fans': '粉丝用户',
    'un_fans': '非粉丝用户'
  }[fansType] || '用户';

  let markdown = `# ${fansTypeText}画像分析\n\n`;

  // 粉丝占比
  if (portraitData.fans_dis && portraitData.fans_dis.length > 0) {
    markdown += '## 粉丝占比\n\n';
    markdown += '| 用户类型 | 占比 |\n';
    markdown += '|---------|------|\n';

    portraitData.fans_dis.forEach(item => {
      markdown += `| ${item.display_name} | ${formatPercentage(item.vertical)} |\n`;
    });

    markdown += '\n';
  }

  // 性别分布
  if (portraitData.sex_dis && portraitData.sex_dis.length > 0) {
    markdown += '## 性别分布\n\n';
    markdown += '| 性别 | 占比 |\n';
    markdown += '|------|------|\n';

    portraitData.sex_dis.forEach(item => {
      markdown += `| ${item.display_name} | ${formatPercentage(item.vertical)} |\n`;
    });

    markdown += '\n';
  }

  // 年龄分布
  if (portraitData.age_dis && portraitData.age_dis.length > 0) {
    markdown += '## 年龄分布\n\n';
    markdown += '| 年龄段 | 占比 |\n';
    markdown += '|--------|------|\n';

    portraitData.age_dis.forEach(item => {
      markdown += `| ${item.display_name} | ${formatPercentage(item.vertical)} |\n`;
    });

    markdown += '\n';
  }

  // 省份分布（Top 10）
  if (portraitData.province_dis && portraitData.province_dis.length > 0) {
    markdown += '## 省份排名Top10\n\n';
    markdown += '| 排名 | 省份 | 占比 |\n';
    markdown += '|------|------|------|\n';

    // 按占比排序并取前10
    const sortedProvinces = [...portraitData.province_dis]
      .sort((a, b) => b.vertical - a.vertical)
      .slice(0, 10);

    sortedProvinces.forEach((item, index) => {
      markdown += `| ${index + 1} | ${item.display_name} | ${formatPercentage(item.vertical)} |\n`;
    });

    markdown += '\n';
  }

  // 八大策略人群分布
  if (portraitData.consumer_dis && portraitData.consumer_dis.length > 0) {
    markdown += '## 八大策略人群分布\n\n';

    // 分离本场人群和近7天人群
    const currentAudience = portraitData.consumer_dis.filter(item => item.point_name === '本场人群');
    const last7DaysAudience = portraitData.consumer_dis.filter(item => item.point_name === '近7天人群');

    if (currentAudience.length > 0) {
      markdown += '### 本场人群\n\n';
      markdown += '| 人群类型 | 占比 |\n';
      markdown += '|----------|------|\n';

      // 按占比排序
      const sortedCurrentAudience = [...currentAudience].sort((a, b) => b.vertical - a.vertical);

      sortedCurrentAudience.forEach(item => {
        markdown += `| ${item.display_name} | ${formatPercentage(item.vertical)} |\n`;
      });

      markdown += '\n';
    }

    if (last7DaysAudience.length > 0) {
      markdown += '### 近7天人群\n\n';
      markdown += '| 人群类型 | 占比 |\n';
      markdown += '|----------|------|\n';

      // 按占比排序
      const sortedLast7DaysAudience = [...last7DaysAudience].sort((a, b) => b.vertical - a.vertical);

      sortedLast7DaysAudience.forEach(item => {
        markdown += `| ${item.display_name} | ${formatPercentage(item.vertical)} |\n`;
      });

      markdown += '\n';
    }

    // 添加本场人群与近7天人群对比
    if (currentAudience.length > 0 && last7DaysAudience.length > 0) {
      markdown += '### 本场人群与近7天人群对比\n\n';
      markdown += '| 人群类型 | 本场占比 | 近7天占比 | 变化 |\n';
      markdown += '|----------|----------|-----------|------|\n';

      // 获取所有唯一的人群类型
      const allTypes = [...new Set([
        ...currentAudience.map(item => item.display_name),
        ...last7DaysAudience.map(item => item.display_name)
      ])];

      allTypes.forEach(type => {
        const currentItem = currentAudience.find(item => item.display_name === type);
        const last7DaysItem = last7DaysAudience.find(item => item.display_name === type);

        const currentValue = currentItem ? currentItem.vertical : 0;
        const last7DaysValue = last7DaysItem ? last7DaysItem.vertical : 0;

        let change = '0%';
        if (last7DaysValue > 0) {
          const changeValue = ((currentValue - last7DaysValue) / last7DaysValue * 100).toFixed(2);
          change = changeValue > 0 ? `+${changeValue}%` : `${changeValue}%`;
        } else if (currentValue > 0) {
          change = '+∞%';
        }

        markdown += `| ${type} | ${formatPercentage(currentValue)} | ${formatPercentage(last7DaysValue)} | ${change} |\n`;
      });

      markdown += '\n';
    }
  }

  return markdown;
}

/**
 * 获取并格式化所有类型的用户画像数据
 * @param {string} roomId - 直播间ID
 * @returns {Promise<Object>} 包含所有类型用户画像的Markdown
 */
async function getAllUserPortraits(roomId) {
  try {
    // 获取三种类型的用户画像数据
    const allUsersData = await getLiveRoomUserPortrait(roomId, 'all');
    const fansData = await getLiveRoomUserPortrait(roomId, 'fans');
    const nonFansData = await getLiveRoomUserPortrait(roomId, 'un_fans');

    // 格式化为Markdown
    const allUsersMarkdown = formatUserPortraitAsMarkdown(allUsersData, 'all');
    const fansMarkdown = formatUserPortraitAsMarkdown(fansData, 'fans');
    const nonFansMarkdown = formatUserPortraitAsMarkdown(nonFansData, 'un_fans');

    // 合并所有Markdown
    const combinedMarkdown = `${allUsersMarkdown}\n---\n\n${fansMarkdown}\n---\n\n${nonFansMarkdown}`;

    return {
      data: {
        all: allUsersData,
        fans: fansData,
        nonFans: nonFansData
      },
      markdown: {
        all: allUsersMarkdown,
        fans: fansMarkdown,
        nonFans: nonFansMarkdown,
        combined: combinedMarkdown
      }
    };
  } catch (error) {
    console.error('获取所有类型用户画像数据失败:', error);
    throw error;
  }
}

/**
 * 测试用户画像API功能
 * @param {string} roomId - 直播间ID，默认使用示例ID
 */
async function testUserPortraitAPI(roomId = '7522830520200694564') {
  console.log(`🧪 开始测试用户画像API功能，房间ID: ${roomId}`);

  try {
    // 获取所有类型的用户画像数据
    console.log('🔍 开始获取所有类型的用户画像数据...');
    const result = await getAllUserPortraits(roomId);
    console.log('✅ 所有类型用户画像数据获取成功');

    // 下载合并的Markdown文件
    const testData = {
      extractedData: {
        apiData: {
          userPortraitInfo: result.data
        }
      }
    };

    downloadAsMarkdown(testData, `user-portrait-all-types-${roomId}.md`);

    // 也可以分别下载三种类型的Markdown
    const allUsersData = {
      extractedData: {
        apiData: {
          userPortraitInfo: result.data.all
        }
      }
    };

    const fansData = {
      extractedData: {
        apiData: {
          userPortraitInfo: result.data.fans
        }
      }
    };

    const nonFansData = {
      extractedData: {
        apiData: {
          userPortraitInfo: result.data.nonFans
        }
      }
    };

    downloadAsMarkdown(allUsersData, `user-portrait-all-users-${roomId}.md`);
    downloadAsMarkdown(fansData, `user-portrait-fans-${roomId}.md`);
    downloadAsMarkdown(nonFansData, `user-portrait-non-fans-${roomId}.md`);

    return { success: true, data: result };
  } catch (error) {
    console.error('❌ 用户画像API测试失败:', error);
    return { success: false, error: error.message };
  }
}

  /**
   * 获取直播间千川竞价广告数据
   * @param {string} roomId - 直播间ID
   * @returns {Promise<Object>} 千川竞价广告数据
   */
  async function getLiveRoomQianchuanAdBidData(roomId) {
    try {
      const url = `https://compass.jinritemai.com/business_api/author/live_detail/live_room/ad?ad_type=bid_qianchuan&live_room_id=${roomId}`;
      const response = await fetch(url, {
        method: 'GET',
        headers: API_CONFIG.defaultHeaders,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      if (data.st !== 0) {
        throw new Error(`API返回错误: ${data.msg || '未知错误'}`);
      }

      return data.data;
    } catch (error) {
      console.error('获取直播间千川竞价广告数据失败:', error);
      throw error;
    }
  }

  /**
   * 格式化千川竞价广告数据为Markdown
   * @param {Object} qianchuanAdBidData - 千川竞价广告数据
   * @returns {string} Markdown格式的千川竞价广告报告
   */
  function formatQianchuanAdBidDataAsMarkdown(qianchuanAdBidData) {
    if (!qianchuanAdBidData) return '';

    let markdown = '# 千川竞价广告核心数据\n\n';

    // 处理核心数据
    if (qianchuanAdBidData.core_data && qianchuanAdBidData.core_data.length > 0) {
      markdown += '## 千川竞价广告核心数据\n\n';

      // 创建表格
      markdown += '| 指标名称 | 数值 | 说明 |\n';
      markdown += '|---------|------|------|\n';

      qianchuanAdBidData.core_data.forEach(item => {
        const indexName = item.index_display || '未命名指标';
        const value = formatDiagnosisValue(item.value);
        const tip = item.index_tip || '';

        markdown += `| ${indexName} | ${value} | ${tip} |\n`;
      });

      markdown += '\n';
    }

    // 处理广告转化漏斗
    if (qianchuanAdBidData.ad_trans) {
      markdown += '## 千川竞价广告成单漏斗\n\n';

      // 创建表格
      markdown += '| 指标名称 | 数值 |\n';
      markdown += '|---------|------|\n';

      markdown += `| 进入直播间人次 | ${qianchuanAdBidData.ad_trans.watch_cnt || 0} |\n`;
      markdown += `| 商品点击次数 | ${qianchuanAdBidData.ad_trans.product_click_cnt || 0} |\n`;
      markdown += `| 直接创建订单数 | ${qianchuanAdBidData.ad_trans.create_order_cnt || 0} |\n`;
      markdown += `| 直接成交订单数 | ${qianchuanAdBidData.ad_trans.pay_order_cnt || 0} |\n`;

      // 计算转化率
      const watchCnt = qianchuanAdBidData.ad_trans.watch_cnt || 0;
      const clickCnt = qianchuanAdBidData.ad_trans.product_click_cnt || 0;
      const createOrderCnt = qianchuanAdBidData.ad_trans.create_order_cnt || 0;
      const payOrderCnt = qianchuanAdBidData.ad_trans.pay_order_cnt || 0;

      // 添加转化率信息
      markdown += '\n### 转化率\n\n';

      // 商品点击转化率
      const clickRate = watchCnt > 0 ? (clickCnt / watchCnt * 100).toFixed(2) + '%' : '0.00%';
      markdown += `- 商品点击转化率: ${clickRate}\n`;

      // 创建订单转化率
      const createOrderRate = clickCnt > 0 ? (createOrderCnt / clickCnt * 100).toFixed(2) + '%' : '0.00%';
      markdown += `- 创建订单转化率: ${createOrderRate}\n`;

      // 订单支付率
      const payOrderRate = createOrderCnt > 0 ? (payOrderCnt / createOrderCnt * 100).toFixed(2) + '%' : '0.00%';
      markdown += `- 订单支付率: ${payOrderRate}\n`;

      // 进入-成交转化率
      const watchToPayRate = watchCnt > 0 ? (payOrderCnt / watchCnt * 100).toFixed(2) + '%' : '0.00%';
      markdown += `- 进入-成交转化率: ${watchToPayRate}\n\n`;
    }

    return markdown;
  }

  /**
   * 测试千川竞价广告API功能
   * @param {string} roomId - 直播间ID，默认使用示例ID
   */
  async function testQianchuanAdBidAPI(roomId = '7522830520200694564') {
    console.log(`🧪 开始测试千川竞价广告API功能，房间ID: ${roomId}`);

    try {
      // 测试API调用
      const qianchuanAdBidData = await getLiveRoomQianchuanAdBidData(roomId);
      console.log('✅ 千川竞价广告API调用成功:', qianchuanAdBidData);

      // 测试数据格式化
      const markdownContent = formatQianchuanAdBidDataAsMarkdown(qianchuanAdBidData);
      console.log('✅ 数据格式化成功');
      console.log('📄 生成的Markdown内容:');
      console.log(markdownContent);

      // 测试下载功能
      const testData = {
        extractedData: {
          apiData: {
            qianchuanAdBidInfo: qianchuanAdBidData
          }
        }
      };

      downloadAsMarkdown(testData, `qianchuan-ad-bid-test-${roomId}.md`);

      return { success: true, data: qianchuanAdBidData, markdown: markdownContent };
    } catch (error) {
      console.error('❌ 千川竞价广告API测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
 * 获取直播间商品列表数据
 * @param {string} roomId - 直播间ID
 * @returns {Promise<Object>} 商品列表数据
 */
async function getLiveRoomProductsList(roomId) {
  try {
    // 定义需要获取的指标
    const selectedIndex = [
      'pay_amt', 'pay_combo_cnt', 'product_click_ucnt', 'pay_deposit_pre_order_cnt',
      'product_show_click_ucnt_ratio', 'product_click_pay_ucnt_ratio', 'gpm',
      'product_bind_time', 'price', 'explain_cnt', 'presale_depay_deamt',
      'pay_deposit_pre_order_amt', 'salingrefund_cnt', 'real_salingrefund_amt',
      'salingrefund_ucnt', 'salingrefund_cnt_ratio', 'saledrefund_cnt',
      'real_saledrefund_amt', 'saledrefund_ucnt', 'saledrefund_cnt_ratio'
    ].join('%2C');

    const url = `https://compass.jinritemai.com/compass_api/content_live/author/live_room_detail/products_list_detail?is_asc=false&page_no=1&page_size=50&live_room_id=${roomId}&selected_index=${selectedIndex}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: API_CONFIG.defaultHeaders,
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    if (data.st !== 0) {
      throw new Error(`API返回错误: ${data.msg || '未知错误'}`);
    }

    return data;
  } catch (error) {
    console.error('获取直播间商品列表数据失败:', error);
    throw error;
  }
}

/**
 * 格式化商品列表数据为Markdown
 * @param {Object} productsListData - 商品列表数据
 * @returns {string} Markdown格式的商品列表报告
 */
function formatProductsListAsMarkdown(productsListData) {
  if (!productsListData || !productsListData.data || !productsListData.data.data_result) {
    return '# 直播间商品列表\n\n暂无商品数据\n\n';
  }

  let markdown = '# 直播间商品列表\n\n';

  // 获取表头信息
  const headers = [];
  const tooltips = {};

  if (productsListData.data.data_head && productsListData.data.data_head.length > 0) {
    productsListData.data.data_head.forEach(header => {
      if (header.index_name !== 'ops') { // 排除"操作"列
        headers.push({
          display: header.index_display,
          name: header.index_name
        });

        // 保存提示信息
        if (header.hover_tip) {
          tooltips[header.index_name] = header.hover_tip;
        }
      }
    });
  }

  // 创建表格头部
  markdown += '| ' + headers.map(h => h.display).join(' | ') + ' |\n';
  markdown += '| ' + headers.map(() => '---').join(' | ') + ' |\n';

  // 添加表格数据
  productsListData.data.data_result.forEach(product => {
    const row = [];

    headers.forEach(header => {
      let value = product[header.name] || '-';

      // 特殊处理商品信息列
      if (header.name === 'product_info') {
        value = product.product_name || '-';
      }

      row.push(value);
    });

    markdown += '| ' + row.join(' | ') + ' |\n';
  });

  // 添加提示信息
  if (Object.keys(tooltips).length > 0) {
    markdown += '\n## 字段说明\n\n';

    for (const [field, tip] of Object.entries(tooltips)) {
      if (tip) {
        // 查找对应的显示名称
        const header = headers.find(h => h.name === field);
        const displayName = header ? header.display : field;

        markdown += `- **${displayName}**: ${tip}\n`;
      }
    }
  }

  // // 添加分页信息
  // if (productsListData.data.page_result) {
  //   const pageInfo = productsListData.data.page_result;
  //   markdown += `\n## 分页信息\n\n`;
  //   markdown += `- 当前页码: ${pageInfo.page_no}\n`;
  //   markdown += `- 每页条数: ${pageInfo.page_size}\n`;
  //   markdown += `- 总商品数: ${pageInfo.total}\n`;
  // }

  return markdown;
}

  /**
   * 将提取的数据格式化为Markdown格式
   * @param {Object} data - 提取的数据对象
   * @returns {string} Markdown格式的字符串
   */
  function formatDataAsMarkdown(data) {
    let markdown = '';

    // 添加标题
    markdown += '# 直播间基础信息\n\n';

    // 处理API数据
    if (data.extractedData && data.extractedData.apiData) {
      const apiData = data.extractedData.apiData;

      // 直播间基础信息
      if (apiData.basicInfo && apiData.basicInfo.live_room_base_info) {
        const baseInfo = apiData.basicInfo.live_room_base_info;

        markdown += '| 项目 | 值 |\n';
        markdown += '|------|----|\n';
        markdown += `| 直播标题 | ${baseInfo.title || '未知'} |\n`;
        markdown += `| 应用ID | ${baseInfo.app_id || '未知'} |\n`;
        markdown += `| 是否斗货 | ${baseInfo.is_douhuo ? '是' : '否'} |\n`;
        markdown += `| 直播状态 | ${baseInfo.is_living ? '直播中' : '已结束'} |\n`;

        // 时间信息
        if (baseInfo.start_time && baseInfo.start_time.value) {
          markdown += `| 开始时间 | ${formatTimestamp(baseInfo.start_time.value)} |\n`;
        }
        if (baseInfo.end_time && baseInfo.end_time.value) {
          markdown += `| 结束时间 | ${formatTimestamp(baseInfo.end_time.value)} |\n`;
        }
        if (baseInfo.live_duration && baseInfo.live_duration.value) {
          markdown += `| 直播时长 | ${formatDuration(baseInfo.live_duration.value)} |\n`;
        }

        markdown += '\n';
      }

      // 交易信息（如果存在）
      if (apiData.tradeInfo && apiData.tradeInfo.trade_list && apiData.tradeInfo.trade_list.length > 0) {
        markdown += '## 交易信息\n\n';
        markdown += '| 指标 | 当前值 | 近7天本账号中位数 | 变化幅度 | 说明 |\n';
        markdown += '|------|--------|------------------|----------|------|\n';

        apiData.tradeInfo.trade_list.forEach(trade => {
          const currentValue = trade.value && trade.value.unit === 'price'
            ? formatPrice(trade.value.value)
            : (trade.value ? trade.value.value : '-');

          let historyValue = '-';
          if (trade.his_value) {
            if (trade.his_value.unit === 'price') {
              historyValue = formatPrice(trade.his_value.value);
            } else {
              historyValue = trade.his_value.value;
            }
          }

          const changeValue = trade.change_value && trade.change_value.unit === 'ratio'
            ? formatRatio(trade.change_value.value)
            : (trade.change_value ? trade.change_value.value : '-');

          const tip = trade.index_tip || '';

          markdown += `| ${trade.index_display || trade.index_name} | ${currentValue} | ${historyValue} | ${changeValue} | ${tip} |\n`;
        });

        markdown += '\n';
      }

      // 诊断信息（如果存在）
      if (apiData.diagnosisInfo) {
        markdown += formatDiagnosisAsMarkdown(apiData.diagnosisInfo);
      }

      // 改进方向信息（如果存在）
      if (apiData.improvedDirectionInfo) {
        markdown += formatImprovedDirectionAsMarkdown(apiData.improvedDirectionInfo);
      }

      // 流量结构信息（如果存在）
      if (apiData.flowStructureInfo) {
        markdown += formatFlowStructureAsMarkdown(apiData.flowStructureInfo);
      }

      if (apiData.flowSourceInfo) {
        markdown += formatFlowSourceAsMarkdown(apiData.flowSourceInfo);
      }

      if (apiData.hourlyExposureInfo) {
        markdown += formatHourlyExposureAsMarkdown(apiData.hourlyExposureInfo);
      }

      if (apiData.flowTrendInfo) {
        markdown += formatFlowTrendAsMarkdown(apiData.flowTrendInfo);
      }

      if (apiData.flowAnalysisInfo) {
        markdown += formatFlowAnalysisAsMarkdown(apiData.flowAnalysisInfo);
      }

      // 添加短视频引流信息（如果存在）
      if (apiData.videoSourceInfo) {
        markdown += formatVideoSourceAsMarkdown(apiData.videoSourceInfo);
      }

      // 添加渠道流量信息（如果存在）
      if (apiData.channelFlowInfo) {
        markdown += formatChannelFlowAsMarkdown(apiData.channelFlowInfo);
      }

      // 添加流量趋势分析信息（如果存在）
      if (apiData.flowTrendAnalysisInfo) {
        markdown += formatFlowTrendAnalysisAsMarkdown(apiData.flowTrendAnalysisInfo);
      }

      // 核心数据分组信息（如果存在）
      if (apiData.coreGroupInfo) {
        markdown += formatCoreGroupInfoAsMarkdown(apiData.coreGroupInfo);
      }

      // 竞价广告数据
      if (apiData.adBidInfo) {
        markdown += formatAdBidDataAsMarkdown(apiData.adBidInfo);
      }

      // 千川竞价广告数据
      if (apiData.qianchuanAdBidInfo) {
        markdown += formatQianchuanAdBidDataAsMarkdown(apiData.qianchuanAdBidInfo);
      }

      // 趋势分析数据
      if (apiData.trendAnalysisInfo) {
        markdown += formatTrendAnalysisAsMarkdown(apiData.trendAnalysisInfo);
      }

      // 评论分析数据
      if (apiData.commentAnalysisInfo) {
        markdown += formatCommentAnalysisAsMarkdown(apiData.commentAnalysisInfo);
      }

      // 所有评论数据
      if (apiData.allCommentsInfo) {
        markdown += formatAllCommentsAsMarkdown(apiData.allCommentsInfo.comments);
      }

      // 所有评论数据
      if (apiData.allOtherCommentsInfo) {
        markdown += formatAllLiveRoomAllCommentsAsMarkdown(apiData.allOtherCommentsInfo.comments);
      }

      // 添加用户画像信息（如果存在）
      if (apiData.userPortraitInfo) {
        markdown += apiData.userPortraitInfo.markdown.combined;
      }

      // 添加商品列表信息（如果存在）
      if (apiData.productsListInfo) {
        markdown += formatProductsListAsMarkdown(apiData.productsListInfo);
      }

      // 表格数据（如果存在）
      if (data.extractedData && data.extractedData.domData && data.extractedData.domData.tables && data.extractedData.domData.tables.length > 0) {
        const domData = data.extractedData.domData;

        domData.tables.forEach((table, index) => {
          if (table.headers && table.headers.length > 0 && table.rows && table.rows.length > 0) {
            markdown += `## 表格数据 ${index + 1}\n\n`;

            // 生成表格头部
            markdown += '| ' + table.headers.join(' | ') + ' |\n';
            markdown += '|' + table.headers.map(() => '------').join('|') + '|\n';

            // 生成表格数据行
            table.rows.forEach(row => {
              markdown += '| ' + row.join(' | ') + ' |\n';
            });

            markdown += '\n';
          }
        });
      }
    }

    return markdown;
  }

  /**
   * 获取直播间趋势分析数据
   * @param {string} roomId - 直播间ID
   * @returns {Promise<Object>} 趋势分析数据
   */
  async function getLiveRoomTrendAnalysis(roomId) {
    try {
      // 定义需要获取的指标
      const indexSelected = [
        'online_user_cnt',  // 实时在线人数
        'watch_ucnt',       // 进入直播间人数
        'leave_ucnt',       // 直播间离开人数
        'comment_cnt',      // 评论次数
        'like_cnt',         // 点赞次数
        'share_cnt',        // 分享次数
        'send_gift_cnt',    // 打赏次数
        'incr_fans_cnt',    // 新增粉丝数
        'fans_club_ucnt'    // 新加直播团人数
      ].join('%2C');

      const url = `https://compass.jinritemai.com/compass_api/author/live/live_room_detail/content/trend_analysis?live_room_id=${roomId}&index_selected=${indexSelected}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: API_CONFIG.defaultHeaders,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      if (data.st !== 0) {
        throw new Error(`API返回错误: ${data.msg || '未知错误'}`);
      }

      return data.data;
    } catch (error) {
      console.error('获取直播间趋势分析数据失败:', error);
      throw error;
    }
  }

  /**
   * 格式化趋势分析数据为Markdown
   * @param {Object} trendAnalysisData - 趋势分析数据
   * @returns {string} Markdown格式的趋势分析报告
   */
  function formatTrendAnalysisAsMarkdown(trendAnalysisData) {
    if (!trendAnalysisData) return '';

    let markdown = '# 趋势分析\n\n';

    // 添加指标说明
    markdown += '## 指标说明\n\n';

    if (trendAnalysisData.index_groups && trendAnalysisData.index_groups.length > 0) {
      trendAnalysisData.index_groups.forEach(group => {
        markdown += `### ${group.group_display}\n\n`;

        if (group.list && group.list.length > 0) {
          markdown += '| 指标名称 | 指标代码 |\n';
          markdown += '|---------|----------|\n';

          group.list.forEach(item => {
            markdown += `| ${item.index_display} | ${item.index_name} |\n`;
          });

          markdown += '\n';
        }
      });
    }

    // 添加中位数数据
    if (trendAnalysisData.index_median && trendAnalysisData.index_median.length > 0) {
      markdown += '## 指标中位数\n\n';
      markdown += '| 指标名称 | 中位数值 |\n';
      markdown += '|---------|----------|\n';

      trendAnalysisData.index_median.forEach(item => {
        markdown += `| ${item.name} | ${item.value} |\n`;
      });

      markdown += '\n';
    }

    // 添加趋势数据
    if (trendAnalysisData.trends && trendAnalysisData.trends.length > 0) {
      // 按指标分组
      const trendsByIndex = {};

      trendAnalysisData.trends.forEach(trend => {
        const indexName = trend.point_name;
        const displayName = trend.display_name;

        if (!trendsByIndex[indexName]) {
          trendsByIndex[indexName] = {
            displayName: displayName,
            data: []
          };
        }

        trendsByIndex[indexName].data.push({
          time: trend.horizontal || formatTimestamp(trend.date_time),
          value: trend.vertical
        });
      });

      // 为每个指标创建趋势表格
      Object.keys(trendsByIndex).forEach(indexName => {
        const indexData = trendsByIndex[indexName];

        markdown += `## ${indexData.displayName}趋势\n\n`;
        markdown += '| 时间 | 数值 |\n';
        markdown += '|------|------|\n';

        // 按时间排序
        indexData.data.sort((a, b) => {
          return new Date(a.time) - new Date(b.time);
        });

        // 添加数据行
        indexData.data.forEach(point => {
          markdown += `| ${point.time} | ${point.value} |\n`;
        });

        markdown += '\n';
      });
    }

    // 添加统计摘要
    markdown += '## 统计摘要\n\n';

    // 成交金额
    if (trendAnalysisData.points && trendAnalysisData.points.length > 0) {
      const gmvPoint = trendAnalysisData.points.find(p => p.name === '成交金额');
      if (gmvPoint) {
        markdown += `- 成交金额: ¥${gmvPoint.value || '0.00'}\n`;
      } else {
        markdown += `- 成交金额: ¥0.00\n`;
      }

      // 直播间观看人数
      const watchPoint = trendAnalysisData.points.find(p => p.name === '直播间观看人数');
      if (watchPoint) {
        markdown += `- 直播间观看人数: ${watchPoint.value || '0'}\n`;
      }

      // 直播间离开人数
      const leavePoint = trendAnalysisData.points.find(p => p.name === '直播间离开人数');
      if (leavePoint) {
        markdown += `- 直播间离开人数: ${leavePoint.value || '0'}\n`;
      }
    }

    return markdown;
  }

  /**
   * 测试趋势分析API功能
   * @param {string} roomId - 直播间ID，默认使用示例ID
   */
  async function testTrendAnalysisAPI(roomId = '7522830520200694564') {
    console.log(`🧪 开始测试趋势分析API功能，房间ID: ${roomId}`);

    try {
      // 测试API调用
      const trendAnalysisData = await getLiveRoomTrendAnalysis(roomId);
      console.log('✅ 趋势分析API调用成功:', trendAnalysisData);

      // 测试数据格式化
      const markdownContent = formatTrendAnalysisAsMarkdown(trendAnalysisData);
      console.log('✅ 数据格式化成功');
      console.log('📄 生成的Markdown内容:');
      console.log(markdownContent);

      // 测试下载功能
      const testData = {
        extractedData: {
          apiData: {
            trendAnalysisInfo: trendAnalysisData
          }
        }
      };

      downloadAsMarkdown(testData, `trend-analysis-test-${roomId}.md`);

      return { success: true, data: trendAnalysisData, markdown: markdownContent };
    } catch (error) {
      console.error('❌ 趋势分析API测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
 * 获取直播间评论分析数据
 * @param {string} roomId - 直播间ID
 * @returns {Promise<Object>} 评论分析数据
 */
async function getLiveRoomCommentAnalysis(roomId) {
  try {
    const url = `https://compass.jinritemai.com/compass_api/author/live/live_room_detail/content/comment_analyse?live_room_id=${roomId}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: API_CONFIG.defaultHeaders,
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    if (data.st !== 0) {
      throw new Error(`API返回错误: ${data.msg || '未知错误'}`);
    }

    return data.data;
  } catch (error) {
    console.error('获取直播间评论分析数据失败:', error);
    throw error;
  }
}

/**
 * 格式化评论分析数据为Markdown
 * @param {Object} commentAnalysisData - 评论分析数据
 * @returns {string} Markdown格式的评论分析报告
 */
function formatCommentAnalysisAsMarkdown(commentAnalysisData) {
  if (!commentAnalysisData || !commentAnalysisData.comments || commentAnalysisData.comments.length === 0) {
    return '# 评论分析\n\n暂无评论分析数据\n\n';
  }

  let markdown = '# 评论分析\n\n';

  // 添加评论词云表格
  markdown += '## 评论词云\n\n';
  markdown += '| 关键词 | 出现次数 | 占比 |\n';
  markdown += '|--------|----------|------|\n';

  // 按出现次数排序
  const sortedComments = [...commentAnalysisData.comments].sort((a, b) => b.count - a.count);

  // 添加表格数据
  sortedComments.forEach(item => {
    const ratio = (item.ratio * 100).toFixed(2) + '%';
    markdown += `| ${item.comment} | ${item.count} | ${ratio} |\n`;
  });

  // 添加统计信息
  const totalComments = sortedComments.reduce((sum, item) => sum + item.count, 0);
  const uniqueKeywords = sortedComments.length;

  markdown += '\n## 统计信息\n\n';
  markdown += `- 总评论关键词数量: ${totalComments}\n`;
  markdown += `- 唯一关键词数量: ${uniqueKeywords}\n`;

  // 添加高频词分析
  markdown += '\n## 高频词分析\n\n';

  // 只取前10个高频词
  const topKeywords = sortedComments.slice(0, 10);

  markdown += '以下是出现频率最高的10个关键词：\n\n';

  topKeywords.forEach((item, index) => {
    const ratio = (item.ratio * 100).toFixed(2) + '%';
    markdown += `${index + 1}. **${item.comment}**: 出现${item.count}次，占比${ratio}\n`;
  });

  return markdown;
}

/**
 * 测试评论分析API功能
 * @param {string} roomId - 直播间ID，默认使用示例ID
 */
async function testCommentAnalysisAPI(roomId = '7522830520200694564') {
  console.log(`🧪 开始测试评论分析API功能，房间ID: ${roomId}`);

  try {
    // 测试API调用
    const commentAnalysisData = await getLiveRoomCommentAnalysis(roomId);
    console.log('✅ 评论分析API调用成功:', commentAnalysisData);

    // 测试数据格式化
    const markdownContent = formatCommentAnalysisAsMarkdown(commentAnalysisData);
    console.log('✅ 数据格式化成功');
    console.log('📄 生成的Markdown内容:');
    console.log(markdownContent);

    // 测试下载功能
    const testData = {
      extractedData: {
        apiData: {
          commentAnalysisInfo: commentAnalysisData
        }
      }
    };

    downloadAsMarkdown(testData, `comment-analysis-test-${roomId}.md`);

    return { success: true, data: commentAnalysisData, markdown: markdownContent };
  } catch (error) {
    console.error('❌ 评论分析API测试失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 获取直播间评论列表数据
 * @param {string} roomId - 直播间ID
 * @param {number} pageNo - 页码，默认为1
 * @param {number} pageSize - 每页条数，默认为10
 * @param {number} commentType - 评论类型，默认为1
 * @returns {Promise<Object>} 评论列表数据
 */
async function getLiveRoomCommentList(roomId, pageNo = 1, pageSize = 10, commentType = 1) {
  if (!roomId) {
    throw new Error('直播间ID不能为空');
  }

  try {
    const url = `https://compass.jinritemai.com/compass_api/author/live/live_room_detail/content/comment_list?page_size=${pageSize}&page_no=${pageNo}&comment_type=${commentType}&live_room_id=${roomId}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: API_CONFIG.defaultHeaders,
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    if (data.st !== 0) {
      throw new Error(`API返回错误: ${data.msg || '未知错误'}`);
    }

    return data.data;
  } catch (error) {
    console.error(`❌ 获取直播间评论列表数据失败 [页码:${pageNo}]:`, error);
    throw error;
  }
}

/**
 * 获取直播间所有评论列表数据（自动分页）
 * @param {string} roomId - 直播间ID
 * @param {number} commentType - 评论类型，默认为1
 * @returns {Promise<Array>} 所有评论列表数据
 */
async function getAllLiveRoomComments(roomId, commentType = 1) {
  try {
    let allComments = [];
    let pageNo = 1;
    const pageSize = 10; // 修改为10，与API默认值保持一致
    let hasMoreData = true;

    while (hasMoreData) {
      // 添加错误处理和重试逻辑
      let data;
      try {
        data = await getLiveRoomCommentList(roomId, pageNo, pageSize, commentType);
      } catch (error) {
        console.warn(`⚠️ 获取第${pageNo}页评论失败，尝试继续获取下一页:`, error);
        pageNo++;
        // 如果连续失败超过3次，则停止获取
        if (pageNo > 3 && allComments.length === 0) {
          throw new Error('连续多次获取评论失败，请检查参数或API状态');
        }
        continue;
      }

      if (data.comment_info && data.comment_info.length > 0) {
        allComments = [...allComments, ...data.comment_info];
      }

      // 检查是否还有更多数据
      if (!data.page_result ||
          !data.comment_info ||
          data.comment_info.length === 0 ||
          data.page_result.page_no * data.page_result.page_size >= data.page_result.total) {
        hasMoreData = false;
      } else {
        pageNo++;
      }

      // 添加延迟，避免请求过于频繁
      if (hasMoreData) {
        await new Promise(resolve => setTimeout(resolve, 300));
      }
    }

    return allComments;
  } catch (error) {
    console.error('获取直播间所有评论列表数据失败:', error);
    throw error;
  }
}

/**
 * 格式化评论列表数据为Markdown
 * @param {Object} commentListData - 评论列表数据
 * @returns {string} Markdown格式的评论列表报告
 */
function formatCommentListAsMarkdown(commentListData) {
  if (!commentListData || !commentListData.comment_info || commentListData.comment_info.length === 0) {
    return '# 评论列表\n\n暂无评论数据\n\n';
  }

  let markdown = '# 评论列表\n\n';

  // 添加评论列表表格
  markdown += '| 评论内容 | 评论时间 |\n';
  markdown += '|----------|----------|\n';

  // 按时间倒序排序
  const sortedComments = [...commentListData.comment_info].sort((a, b) => b.comment_time - a.comment_time);

  // 添加表格数据
  sortedComments.forEach(item => {
    const commentTime = formatTimestamp(item.comment_time * 1000); // 转换为毫秒
    markdown += `| ${item.comment_info} | ${commentTime} |\n`;
  });

  // 添加分页信息
  if (commentListData.page_result) {
    markdown += '\n## 分页信息\n\n';
    markdown += `- 当前页码: ${commentListData.page_result.page_no}\n`;
    markdown += `- 每页条数: ${commentListData.page_result.page_size}\n`;
    markdown += `- 总评论数: ${commentListData.page_result.total}\n`;
  }

  return markdown;
}

/**
 * 格式化所有评论数据为Markdown
 * @param {Array} allComments - 所有评论数据
 * @returns {string} Markdown格式的所有评论报告
 */
function formatAllCommentsAsMarkdown(allComments) {
  if (!allComments || allComments.length === 0) {
    return '# 直播间所有评论\n\n暂无评论数据\n\n';
  }

  let markdown = '# 直播间重要评论\n\n';

  // 添加评论总数
  markdown += `## 评论总数: ${allComments.length}\n\n`;

  // 添加评论列表表格
  markdown += '| 评论内容 | 评论时间 |\n';
  markdown += '|----------|----------|\n';

  // 按时间倒序排序
  const sortedComments = [...allComments].sort((a, b) => b.comment_time - a.comment_time);

  // 添加表格数据
  sortedComments.forEach(item => {
    const commentTime = formatTimestamp(item.comment_time * 1000); // 转换为毫秒
    markdown += `| ${item.comment_info} | ${commentTime} |\n`;
  });

  return markdown;
}

/**
 * 测试评论列表API功能
 * @param {string} roomId - 直播间ID，默认使用示例ID
 */
async function testCommentListAPI(roomId = '7522830520200694564') {
  console.log(`🧪 开始测试评论列表API功能，房间ID: ${roomId}`);

  try {
    // 测试单页API调用
    const commentListData = await getLiveRoomCommentList(roomId, 1, 10);
    console.log('✅ 评论列表API调用成功:', commentListData);

    // 测试数据格式化
    const markdownContent = formatCommentListAsMarkdown(commentListData);
    console.log('✅ 数据格式化成功');
    console.log('📄 生成的Markdown内容:');
    console.log(markdownContent);

    // 测试下载功能
    const testData = {
      extractedData: {
        apiData: {
          commentListInfo: commentListData
        }
      }
    };

    downloadAsMarkdown(testData, `comment-list-test-${roomId}.md`);

    // 测试获取所有评论
    console.log('🔍 开始获取所有评论...');

    // 添加确认对话框，避免不必要的大量请求
    const shouldFetchAll = confirm('是否获取所有评论？这可能需要发送多个请求。');

    if (shouldFetchAll) {
      const allComments = await getAllLiveRoomComments(roomId);
      console.log(`✅ 成功获取所有评论，共 ${allComments.length} 条`);

      // 格式化所有评论
      const allCommentsMarkdown = formatAllCommentsAsMarkdown(allComments);
      console.log('✅ 所有评论格式化成功');

      // 测试下载所有评论
      const allCommentsData = {
        extractedData: {
          apiData: {
            allCommentsInfo: {
              comments: allComments,
              total: allComments.length
            }
          }
        }
      };

      downloadAsMarkdown(allCommentsData, `all-comments-test-${roomId}.md`);

      return {
        success: true,
        data: {
          singlePage: commentListData,
          allComments: allComments
        },
        markdown: {
          singlePage: markdownContent,
          allComments: allCommentsMarkdown
        }
      };
    } else {
      console.log('⏭️ 跳过获取所有评论');
      return {
        success: true,
        data: {
          singlePage: commentListData
        },
        markdown: {
          singlePage: markdownContent
        }
      };
    }
  } catch (error) {
    console.error('❌ 评论列表API测试失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 获取直播间全部评论列表数据
 * @param {string} roomId - 直播间ID
 * @param {number} pageNo - 页码，默认为1
 * @param {number} pageSize - 每页条数，默认为10
 * @returns {Promise<Object>} 评论列表数据
 */
async function getLiveRoomAllCommentList(roomId, pageNo = 1, pageSize = 10) {
  if (!roomId) {
    throw new Error('直播间ID不能为空');
  }

  try {
    const url = `https://compass.jinritemai.com/compass_api/author/live/live_room_detail/content/comment_list?page_size=${pageSize}&page_no=${pageNo}&comment_type=0&live_room_id=${roomId}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: API_CONFIG.defaultHeaders,
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    if (data.st !== 0) {
      throw new Error(`API返回错误: ${data.msg || '未知错误'}`);
    }

    return data.data;
  } catch (error) {
    console.error(`❌ 获取直播间全部评论列表数据失败 [页码:${pageNo}]:`, error);
    throw error;
  }
}

/**
 * 获取直播间所有全部评论数据（自动分页）
 * @param {string} roomId - 直播间ID
 * @returns {Promise<Array>} 所有评论列表数据
 */
async function getAllLiveRoomAllComments(roomId) {
  try {
    let allComments = [];
    let pageNo = 1;
    const pageSize = 10; // 与API默认值保持一致
    let hasMoreData = true;

    while (hasMoreData) {
      // 添加错误处理和重试逻辑
      let data;
      try {
        data = await getLiveRoomAllCommentList(roomId, pageNo, pageSize);
      } catch (error) {
        console.warn(`⚠️ 获取第${pageNo}页全部评论失败，尝试继续获取下一页:`, error);
        pageNo++;
        // 如果连续失败超过3次，则停止获取
        if (pageNo > 3 && allComments.length === 0) {
          throw new Error('连续多次获取全部评论失败，请检查参数或API状态');
        }
        continue;
      }

      if (data.comment_info && data.comment_info.length > 0) {
        allComments = [...allComments, ...data.comment_info];
      }

      // 检查是否还有更多数据
      if (!data.page_result ||
          !data.comment_info ||
          data.comment_info.length === 0 ||
          data.page_result.page_no * data.page_result.page_size >= data.page_result.total) {
        hasMoreData = false;
      } else {
        pageNo++;
      }

      // 添加延迟，避免请求过于频繁
      if (hasMoreData) {
        await new Promise(resolve => setTimeout(resolve, 300));
      }
    }

    return allComments;
  } catch (error) {
    console.error('获取直播间所有全部评论数据失败:', error);
    throw error;
  }
}

/**
 * 格式化全部评论列表数据为Markdown
 * @param {Object} commentListData - 评论列表数据
 * @returns {string} Markdown格式的评论列表报告
 */
function formatAllCommentListAsMarkdown(commentListData) {
  if (!commentListData || !commentListData.comment_info || commentListData.comment_info.length === 0) {
    return '# 全部评论列表\n\n暂无评论数据\n\n';
  }

  let markdown = '# 直播间全部评论列表\n\n';

  // 添加评论总数
  if (commentListData.page_result && commentListData.page_result.total) {
    markdown += `## 评论总数: ${commentListData.page_result.total}\n\n`;
  }

  // 添加评论列表表格
  markdown += '| 评论内容 | 评论时间 |\n';
  markdown += '|----------|----------|\n';

  // 按时间倒序排序
  const sortedComments = [...commentListData.comment_info].sort((a, b) => b.comment_time - a.comment_time);

  // 添加表格数据
  sortedComments.forEach(item => {
    const commentTime = formatTimestamp(item.comment_time * 1000); // 转换为毫秒
    markdown += `| ${item.comment_info} | ${commentTime} |\n`;
  });

  // 添加分页信息
  if (commentListData.page_result) {
    markdown += '\n## 分页信息\n\n';
    markdown += `- 当前页码: ${commentListData.page_result.page_no}\n`;
    markdown += `- 每页条数: ${commentListData.page_result.page_size}\n`;
    markdown += `- 总评论数: ${commentListData.page_result.total}\n`;
  }

  return markdown;
}

/**
 * 格式化所有全部评论数据为Markdown
 * @param {Array} allComments - 所有评论数据
 * @returns {string} Markdown格式的所有评论报告
 */
function formatAllLiveRoomAllCommentsAsMarkdown(allComments) {
  if (!allComments || allComments.length === 0) {
    return '# 直播间所有全部评论\n\n暂无评论数据\n\n';
  }

  let markdown = '# 直播间其他评论\n\n';

  // 添加评论总数
  markdown += `## 评论总数: ${allComments.length}\n\n`;

  // 添加评论列表表格
  markdown += '| 评论内容 | 评论时间 |\n';
  markdown += '|----------|----------|\n';

  // 按时间倒序排序
  const sortedComments = [...allComments].sort((a, b) => b.comment_time - a.comment_time);

  // 添加表格数据
  sortedComments.forEach(item => {
    const commentTime = formatTimestamp(item.comment_time * 1000); // 转换为毫秒
    markdown += `| ${item.comment_info} | ${commentTime} |\n`;
  });

  return markdown;
}

/**
 * 测试全部评论列表API功能
 * @param {string} roomId - 直播间ID，默认使用示例ID
 */
async function testAllCommentListAPI(roomId = '7522830520200694564') {
  console.log(`🧪 开始测试全部评论列表API功能，房间ID: ${roomId}`);

  try {
    // 测试单页API调用
    const commentListData = await getLiveRoomAllCommentList(roomId, 1, 10);
    console.log('✅ 全部评论列表API调用成功:', commentListData);

    // 测试数据格式化
    const markdownContent = formatAllCommentListAsMarkdown(commentListData);
    console.log('✅ 数据格式化成功');
    console.log('📄 生成的Markdown内容:');
    console.log(markdownContent);

    // 测试下载功能
    const testData = {
      extractedData: {
        apiData: {
          allCommentListInfo: commentListData
        }
      }
    };

    downloadAsMarkdown(testData, `all-comment-list-test-${roomId}.md`);

    // 测试获取所有评论
    console.log('🔍 开始获取所有全部评论...');

    // 添加确认对话框，避免不必要的大量请求
    const shouldFetchAll = confirm('是否获取所有全部评论？这可能需要发送多个请求。');

    if (shouldFetchAll) {
      const allComments = await getAllLiveRoomAllComments(roomId);
      console.log(`✅ 成功获取所有全部评论，共 ${allComments.length} 条`);

      // 格式化所有评论
      const allCommentsMarkdown = formatAllLiveRoomAllCommentsAsMarkdown(allComments);
      console.log('✅ 所有全部评论格式化成功');

      // 测试下载所有评论
      const allCommentsData = {
        extractedData: {
          apiData: {
            allLiveRoomAllCommentsInfo: {
              comments: allComments,
              total: allComments.length
            }
          }
        }
      };

      downloadAsMarkdown(allCommentsData, `all-live-room-all-comments-test-${roomId}.md`);

      return {
        success: true,
        data: {
          singlePage: commentListData,
          allComments: allComments
        },
        markdown: {
          singlePage: markdownContent,
          allComments: allCommentsMarkdown
        }
      };
    } else {
      console.log('⏭️ 跳过获取所有全部评论');
      return {
        success: true,
        data: {
          singlePage: commentListData
        },
        markdown: {
          singlePage: markdownContent
        }
      };
    }
  } catch (error) {
    console.error('❌ 全部评论列表API测试失败:', error);
    return { success: false, error: error.message };
  }
}

  // ==================== 文件下载函数 ====================

  /**
   * 下载数据为Markdown文件
   * @param {Object} data - 要下载的数据
   * @param {string} filename - 文件名
   */
  function downloadAsMarkdown(data, filename = 'live-data.md') {
    try {
      const markdownContent = formatDataAsMarkdown(data);
      const blob = new Blob([markdownContent], { type: 'text/markdown;charset=utf-8' });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      updateStatusIndicator('✅ Markdown文件下载成功', 'success');
      setTimeout(removeStatusIndicator, 3000);
    } catch (error) {
      console.error('❌ Markdown文件下载失败:', error);
      updateStatusIndicator('❌ Markdown文件下载失败', 'error');
    }
  }

  // ==================== 测试函数 ====================

  /**
   * 测试诊断API功能
   * @param {string} roomId - 直播间ID，默认使用示例ID
   */
  async function testDiagnosisAPI(roomId = '752280992053467828') {
    console.log(`🧪 开始测试诊断API功能，房间ID: ${roomId}`);

    try {
      // 测试API调用
      const diagnosisData = await getLiveRoomDiagnosis(roomId);
      console.log('✅ 诊断API调用成功:', diagnosisData);

      // 测试数据格式化
      const markdownContent = formatDiagnosisAsMarkdown(diagnosisData);
      console.log('✅ 数据格式化成功');
      console.log('📄 生成的Markdown内容:');
      console.log(markdownContent);

      // 测试下载功能
      const testData = {
        extractedData: {
          apiData: {
            diagnosisInfo: diagnosisData
          }
        }
      };

      downloadAsMarkdown(testData, `diagnosis-test-${roomId}.md`);

      return { success: true, data: diagnosisData, markdown: markdownContent };
    } catch (error) {
      console.error('❌ 诊断API测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 测试改进方向API功能
   * @param {string} roomId - 直播间ID，默认使用示例ID
   */
  async function testImprovedDirectionAPI(roomId = '7522809920534678287') {
    console.log(`🧪 开始测试改进方向API功能，房间ID: ${roomId}`);

    try {
      // 测试API调用
      const improvedDirectionData = await getLiveRoomImprovedDirection(roomId);
      console.log('✅ 改进方向API调用成功:', improvedDirectionData);

      // 测试数据格式化
      const markdownContent = formatImprovedDirectionAsMarkdown(improvedDirectionData);
      console.log('✅ 数据格式化成功');
      console.log('📄 生成的Markdown内容:');
      console.log(markdownContent);

      // 测试下载功能
      const testData = {
        extractedData: {
          apiData: {
            improvedDirectionInfo: improvedDirectionData
          }
        }
      };

      downloadAsMarkdown(testData, `improved-direction-test-${roomId}.md`);

      return { success: true, data: improvedDirectionData, markdown: markdownContent };
    } catch (error) {
      console.error('❌ 改进方向API测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 测试流量结构API功能
   * @param {string} roomId - 直播间ID，默认使用示例ID
   */
  async function testFlowStructureAPI(roomId = '7522809920534678287') {
    console.log(`🧪 开始测试流量结构API功能，房间ID: ${roomId}`);

    try {
      // 测试API调用
      const flowStructureData = await getLiveRoomFlowStructure(roomId);
      console.log('✅ 流量结构API调用成功:', flowStructureData);

      // 测试数据格式化
      const markdownContent = formatFlowStructureAsMarkdown(flowStructureData);
      console.log('✅ 数据格式化成功');
      console.log('📄 生成的Markdown内容:');
      console.log(markdownContent);

      // 测试下载功能
      const testData = {
        extractedData: {
          apiData: {
            flowStructureInfo: flowStructureData
          }
        }
      };

      downloadAsMarkdown(testData, `flow-structure-test-${roomId}.md`);

      return { success: true, data: flowStructureData, markdown: markdownContent };
    } catch (error) {
      console.error('❌ 流量结构API测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 测试单小时曝光次数API功能
   * @param {string} roomId - 直播间ID，默认使用示例ID
   */
  async function testHourlyExposureAPI(roomId = '7522809920534678287') {
    console.log(`🧪 开始测试单小时曝光次数API功能，房间ID: ${roomId}`);

    try {
      // 测试API调用
      const hourlyExposureData = await getLiveRoomHourlyExposure(roomId);
      console.log('✅ 单小时曝光次数API调用成功:', hourlyExposureData);

      // 测试数据格式化
      const markdownContent = formatHourlyExposureAsMarkdown(hourlyExposureData);
      console.log('✅ 数据格式化成功');
      console.log('📄 生成的Markdown内容:');
      console.log(markdownContent);

      // 测试下载功能
      const testData = {
        extractedData: {
          apiData: {
            hourlyExposureInfo: hourlyExposureData
          }
        }
      };

      downloadAsMarkdown(testData, `hourly-exposure-test-${roomId}.md`);

      return { success: true, data: hourlyExposureData, markdown: markdownContent };
    } catch (error) {
      console.error('❌ 单小时曝光次数API测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 测试流量趋势API功能
   * @param {string} roomId - 直播间ID，默认使用示例ID
   */
  async function testFlowTrendAPI(roomId = '7522809920534678287') {
    console.log(`🧪 开始测试流量趋势API功能，房间ID: ${roomId}`);

    try {
      // 测试API调用
      const flowTrendData = await getLiveRoomFlowTrend(roomId);
      console.log('✅ 流量趋势API调用成功:', flowTrendData);

      // 测试数据格式化
      const markdownContent = formatFlowTrendAsMarkdown(flowTrendData);
      console.log('✅ 数据格式化成功');
      console.log('📄 生成的Markdown内容:');
      console.log(markdownContent);

      // 测试下载功能
      const testData = {
        extractedData: {
          apiData: {
            flowTrendInfo: flowTrendData
          }
        },
        url: window.location.href
      };

      // 使用 formatDataAsMarkdown 函数处理数据
      const fullMarkdown = formatDataAsMarkdown(testData);

      // 下载完整的 Markdown 文件
      downloadAsMarkdown({extractedData: {markdownContent: fullMarkdown}}, `flow-trend-test-${roomId}.md`);

      return { success: true, data: flowTrendData, markdown: markdownContent };
    } catch (error) {
      console.error('❌ 流量趋势API测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 测试流量分析API功能
   * @param {string} roomId - 直播间ID，默认使用示例ID
   */
  async function testFlowAnalysisAPI(roomId = '7524295568181463823') {
    console.log(`🧪 开始测试流量分析API功能，房间ID: ${roomId}`);

    try {
      // 测试API调用
      const flowAnalysisData = await getLiveRoomFlowAnalysis(roomId);
      console.log('✅ 流量分析API调用成功:', flowAnalysisData);

      // 测试数据格式化
      const markdownContent = formatFlowAnalysisAsMarkdown(flowAnalysisData);
      console.log('✅ 数据格式化成功');
      console.log('📄 生成的Markdown内容:');
      console.log(markdownContent);

      // 测试下载功能
      const testData = {
        extractedData: {
          apiData: {
            flowAnalysisInfo: flowAnalysisData
          }
        }
      };

      downloadAsMarkdown(testData, `flow-analysis-test-${roomId}.md`);

      return { success: true, data: flowAnalysisData, markdown: markdownContent };
    } catch (error) {
      console.error('❌ 流量分析API测试失败:', error);
      return { success: false, error: error.message };
    }
  }


    /**
   * 获取直播间回放视频链接
   * @param {string} roomId - 直播间ID
   * @returns {Promise<Object>} 回放视频链接数据
   */
    async function getLiveRoomReviewVideoLink(roomId) {
      try {
        const url = `https://compass.jinritemai.com/compass_api/content_live/author/live_screen/review_video_link?room_id=${roomId}`;

        const response = await fetch(url, {
          method: 'GET',
          headers: API_CONFIG.defaultHeaders,
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        if (data.st !== 0) {
          throw new Error(`API返回错误: ${data.msg || '未知错误'}`);
        }

        console.log('📹 直播回放视频链接数据:', data);
        return data.data;
      } catch (error) {
        console.error('❌ 获取直播间回放视频链接失败:', error);
        throw error;
      }
    }

    /**
   * 记录直播流数据
   * @param {string} anchorName - 主播名称
   * @param {string} liveTitle - 直播标题
   * @param {string} liveTime - 直播时间
   * @param {Array<string>} liveDataUrls - 直播数据URL列表
   * @param {string} liveReplayUrl - 直播回放URL
   * @returns {Promise<Object>} API响应
   */
  async function recordLiveStreamData(liveId, anchorName, liveTitle, liveTime, liveDataUrls, liveReplayUrl) {
    try {
      const url = 'https://aiapi.bzy.ai/v2/anon/chatbot/live-stream-records';

      const payload = {
        live_id: liveId,
        anchor_name: anchorName,
        live_title: liveTitle,
        live_time: liveTime,
        live_data_urls: liveDataUrls,
        live_replay_url: liveReplayUrl,
        live_replay_audio_url: ''
      };

      console.log('📝 记录直播数据请求:', payload);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ 记录直播数据成功:', data);
      return data;
    } catch (error) {
      console.error('❌ 记录直播数据失败:', error);
      throw error;
    }
  }

    /**
     * 测试直播回放视频链接API功能
     * @param {string} roomId - 直播间ID，默认使用示例ID
     */
    async function testReviewVideoLinkAPI(roomId = '7526530816311184143') {
      console.log(`🧪 开始测试直播回放视频链接API功能，房间ID: ${roomId}`);

      try {
        // 测试API调用
        const videoLinkData = await getLiveRoomReviewVideoLink(roomId);
        console.log('✅ 直播回放视频链接API调用成功:', videoLinkData);

        // 如果有视频链接，尝试打印出来
        if (videoLinkData && videoLinkData.video_url) {
          console.log('🔗 视频链接:', videoLinkData.video_url);
        }

        if (videoLinkData && videoLinkData.video_list && videoLinkData.video_list.length > 0) {
          console.log('📋 视频列表:');
          videoLinkData.video_list.forEach((video, index) => {
            console.log(`  ${index + 1}. ${video.title || '无标题'}: ${video.url || '无链接'}`);
          });
        }

        return { success: true, data: videoLinkData };
      } catch (error) {
        console.error('❌ 直播回放视频链接API测试失败:', error);
        return { success: false, error: error.message };
      }
    }

      /**
   * 获取账户信息
   * @returns {Promise<Object>} 账户信息
   */
  async function getAccountInfo() {
    try {
      const url = 'https://compass.jinritemai.com/ecomauth/loginv1/get_account_info?login_source=compass';

      const response = await fetch(url, {
        method: 'GET',
        headers: API_CONFIG.defaultHeaders,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      if (data.st !== 0) {
        throw new Error(`API返回错误: ${data.msg || '未知错误'}`);
      }

      console.log('👤 账户信息:', data.data);
      return data.data;
    } catch (error) {
      console.error('❌ 获取账户信息失败:', error);
      // 如果获取失败，返回空对象，让程序继续使用备选方案
      return {};
    }
  }

  /**
   * 获取直播间短视频引流数据
   * @param {string} roomId - 直播间ID
   * @param {number} pageNo - 页码，默认为1
   * @param {number} pageSize - 每页数量，默认为10
   * @returns {Promise<Object>} 短视频引流数据
   */
  async function getLiveRoomVideoSource(roomId, pageNo = 1, pageSize = 10) {
    try {
      const url = `https://compass.jinritemai.com/business_api/author/live_detail/live_room/video/source?page_size=${pageSize}&page_no=${pageNo}&live_room_id=${roomId}`;
      const response = await fetch(url, {
        method: 'GET',
        headers: API_CONFIG.defaultHeaders,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      if (data.st !== 0) {
        throw new Error(`API返回错误: ${data.msg || '未知错误'}`);
      }

      return data.data;
    } catch (error) {
      console.error('获取直播间短视频引流数据失败:', error);
      throw error;
    }
  }

  /**
   * 格式化短视频引流数据为Markdown
   * @param {Object} videoSourceData - 短视频引流数据
   * @returns {string} Markdown格式的短视频引流报告
   */
  function formatVideoSourceAsMarkdown(videoSourceData) {
    if (!videoSourceData) return '';

    let markdown = '# 短视频引流分析\n\n';

    // 处理表头和数据
    if (videoSourceData.data_head && videoSourceData.data_result && videoSourceData.data_result.length > 0) {
      // 创建表头映射
      const headerMap = {};
      videoSourceData.data_head.forEach(header => {
        headerMap[header.index_name] = header.index_display;
      });

      // 生成表格头部
      markdown += '| ';
      markdown += [
        headerMap.title || '短视频',
        headerMap.publish_time || '发布时间',
        headerMap.show_in_live_cnt || '直播入口曝光次数',
        headerMap.drainage_in_live_cnt || '引流直播间次数',
        headerMap.show_to_drainage_rate || '直播入口点击率',
        '视频ID'
      ].join(' | ');
      markdown += ' |\n';

      // 生成表格分隔行
      markdown += '|' + '---|'.repeat(6) + '\n';

      // 生成表格数据行
      videoSourceData.data_result.forEach(item => {
        markdown += `| ${item.title || '-'} | ${item.publish_time || '-'} | ${item.show_in_live_cnt || '-'} | ${item.drainage_in_live_cnt || '-'} | ${item.show_to_drainage_rate || '-'} | ${item.video_id || '-'} |\n`;
      });

      // 添加分页信息
      if (videoSourceData.page_result) {
        const { page_no, page_size, total } = videoSourceData.page_result;
        markdown += `\n**页码**: ${page_no}/${Math.ceil(total / page_size)}，**总数**: ${total}\n\n`;
      }
    } else {
      markdown += '暂无短视频引流数据\n\n';
    }

    return markdown;
  }

  /**
   * 测试短视频引流API功能
   * @param {string} roomId - 直播间ID，默认使用示例ID
   */
  async function testVideoSourceAPI(roomId = '7524295568181463823') {
    console.log(`🧪 开始测试短视频引流API功能，房间ID: ${roomId}`);

    try {
      // 测试API调用
      const videoSourceData = await getLiveRoomVideoSource(roomId);
      console.log('✅ 短视频引流API调用成功:', videoSourceData);

      // 测试数据格式化
      const markdownContent = formatVideoSourceAsMarkdown(videoSourceData);
      console.log('✅ 数据格式化成功');
      console.log('📄 生成的Markdown内容:');
      console.log(markdownContent);

      // 测试下载功能
      const testData = {
        extractedData: {
          apiData: {
            videoSourceInfo: videoSourceData
          }
        }
      };

      downloadAsMarkdown(testData, `video-source-test-${roomId}.md`);

      return { success: true, data: videoSourceData, markdown: markdownContent };
    } catch (error) {
      console.error('❌ 短视频引流API测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 调试流量趋势分析数据结构
   * @param {string} roomId - 直播间ID
   */
  async function debugFlowTrendAnalysisData(roomId = '7524295568181463823') {
    console.log(`🔍 开始调试流量趋势分析数据结构，房间ID: ${roomId}`);

    try {
      const data = await getLiveRoomFlowTrendAnalysis(roomId);
      console.log('🔍 原始数据:', data);
      console.log('🔍 数据类型:', typeof data);
      console.log('🔍 是否为数组:', Array.isArray(data));
      console.log('🔍 数据键:', Object.keys(data || {}));

      if (data && typeof data === 'object') {
        console.log('🔍 检查关键字段:');
        console.log('  - index_groups:', !!data.index_groups, data.index_groups);
        console.log('  - trend_map:', !!data.trend_map, data.trend_map);
        console.log('  - data_type_map:', !!data.data_type_map, data.data_type_map);

        // 检查嵌套结构
        if (data.data) {
          console.log('🔍 发现嵌套data字段:', data.data);
        }
        if (data.result) {
          console.log('🔍 发现嵌套result字段:', data.result);
        }
      }

      return data;
    } catch (error) {
      console.error('❌ 调试失败:', error);
      return null;
    }
  }

  // 将测试函数暴露到全局作用域，方便控制台调用
  window.testDiagnosisAPI = testDiagnosisAPI;
  window.testImprovedDirectionAPI = testImprovedDirectionAPI;
  window.testFlowStructureAPI = testFlowStructureAPI;
  window.testHourlyExposureAPI = testHourlyExposureAPI;
  window.testFlowTrendAPI = testFlowTrendAPI;
  window.testFlowAnalysisAPI = testFlowAnalysisAPI;
  window.testVideoSourceAPI = testVideoSourceAPI;
  window.testFlowTrendAPI = testFlowTrendAPI;
  window.testFlowAnalysisAPI = testFlowAnalysisAPI;
  window.testChannelFlowAPI = testChannelFlowAPI;
  window.testFlowTrendAnalysisAPI = testFlowTrendAnalysisAPI;
  window.debugFlowTrendAnalysisData = debugFlowTrendAnalysisData;
  window.testAdBidAPI = testAdBidAPI;
  window.testQianchuanAdBidAPI = testQianchuanAdBidAPI;
  window.testTrendAnalysisAPI = testTrendAnalysisAPI;
  window.testCommentAnalysisAPI = testCommentAnalysisAPI;
  window.testCommentListAPI = testCommentListAPI;
  window.testAllCommentListAPI = testAllCommentListAPI;

  window.testReviewVideoLinkAPI = testReviewVideoLinkAPI;
  // ==================== 主要功能函数 ====================


  /**
 * 上传Markdown文件到阿里云OSS
 * @param {string} markdownContent - Markdown内容
 * @param {string} filename - 文件名
 * @returns {Promise<string>} 上传后的文件URL
 */
async function uploadToOSS(markdownContent, filename) {
  try {
    updateStatusIndicator('📤 正在上传到OSS...', 'info');

    // 创建Blob对象
    const blob = new Blob([markdownContent], { type: 'text/markdown;charset=utf-8' });

    // 准备表单数据
    const formData = new FormData();
    formData.append('OSSAccessKeyId', 'LTAIM23ZKZHBJoDn');
    formData.append('policy', 'eyJleHBpcmF0aW9uIjogIjIwMjUtMDctMTRUMDM6MzE6MzJaIiwgImNvbmRpdGlvbnMiOiBbWyJzdGFydHMtd2l0aCIsICIka2V5IiwgIiJdXX0=');
    formData.append('signature', 'PfHOJUITIy49XMFrg5ij8PZ4zo0=');
    formData.append('key', `upload/${filename}`);
    formData.append('success_action_status', '200');
    formData.append('file', blob);

    // 上传到OSS
    const uploadResponse = await fetch('https://video-clip.oss-cn-shanghai.aliyuncs.com', {
      method: 'POST',
      body: formData
    });

    if (!uploadResponse.ok) {
      throw new Error(`上传到OSS失败: ${uploadResponse.status} ${uploadResponse.statusText}`);
    }

    // 构建文件URL
    const fileUrl = `https://video-clip.oss-cn-shanghai.aliyuncs.com/upload/${filename}`;

    updateStatusIndicator('✅ 上传成功', 'success');
    setTimeout(removeStatusIndicator, 3000);
    console.log(fileUrl);

    return fileUrl;
  } catch (error) {
    console.error('❌ 上传到OSS失败:', error);
    updateStatusIndicator('❌ 上传失败', 'error');
    throw error;
  }
}
const SIGNATURE_EXPIRE_TIME = 2 * 60 * 1000; // 2分钟

// 生成GMT ISO8601格式的时间字符串
function formatGMTISO8601(date) {
  const pad = (n) => (n < 10 ? '0' + n : n);
  return (
    `${date.getUTCFullYear()}-${pad(date.getUTCMonth() + 1)}-${pad(date.getUTCDate())}` +
    `T${pad(date.getUTCHours())}:${pad(date.getUTCMinutes())}:${pad(date.getUTCSeconds())}Z`
  );
}

// 生成Policy配置
function generatePolicyConfig() {
  const expireEnd = new Date(Date.now() + SIGNATURE_EXPIRE_TIME);
  const expiration = formatGMTISO8601(expireEnd);

  const conditions = [
    ["starts-with", "$key", ""],
    ["content-length-range", 0, 1024 * 1024 * 1024 * 5], // 5GB
  ];

  return {
    expiration,
    conditions,
  };
}

// 使用 Web Crypto API 计算 HMAC-SHA1
async function hmacSha1(key, message) {
  const encoder = new TextEncoder();
  const cryptoKey = await window.crypto.subtle.importKey(
    "raw",
    encoder.encode(key),
    { name: "HMAC", hash: "SHA-1" },
    false,
    ["sign"]
  );

  const signature = await window.crypto.subtle.sign(
    "HMAC",
    cryptoKey,
    encoder.encode(message)
  );

  return btoa(String.fromCharCode(...new Uint8Array(signature)));
}

// 获取CDN签名（异步版本，因为 Web Crypto API 是异步的）
async function getCDNSignature(accessKeySecret) {
  try {
    const config = generatePolicyConfig();
    const policy = btoa(JSON.stringify(config));

    const signature = await hmacSha1(accessKeySecret, policy);

    return {
      policy,
      signature,
      error: null,
    };
  } catch (err) {
    return {
      policy: null,
      signature: null,
      error: err,
    };
  }
}


  /**
   * 创建数据URL并打开新窗口显示内容
   * @param {string} markdownContent - Markdown内容
   * @param {string} filename - 文件名
   * @returns {Promise<void>}
   */
  async function showMarkdownInNewTab(markdownContent, filename) {
    try {
      updateStatusIndicator('📄 正在准备数据...', 'info');

      // 创建一个完整的HTML文档
      const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>${filename}</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      padding: 20px;
      max-width: 900px;
      margin: 0 auto;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
    .controls {
      position: fixed;
      top: 10px;
      right: 10px;
      background: white;
      padding: 10px;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      z-index: 1000;
    }
    button {
      background: #1890ff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover {
      background: #40a9ff;
    }
    button.success {
      background: #52c41a;
    }
    button.warning {
      background: #faad14;
    }
    button.error {
      background: #f5222d;
    }
    #markdown-content {
      white-space: pre-wrap;
      word-break: break-word;
    }
    #upload-status {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      display: none;
    }
    #upload-status.info {
      display: block;
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
    }
    #upload-status.success {
      display: block;
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;
    }
    #upload-status.error {
      display: block;
      background-color: #fff2f0;
      border: 1px solid #ffccc7;
    }
    .progress-bar {
      height: 20px;
      background-color: #e9ecef;
      border-radius: 4px;
      margin-top: 10px;
      overflow: hidden;
    }
    .progress-bar-fill {
      height: 100%;
      background-color: #1890ff;
      width: 0%;
      transition: width 0.3s ease;
    }
  </style>
</head>
<body>
  <div class="controls">
    <button id="copy-btn">复制内容</button>
    <button id="download-btn">下载文件</button>
    <button id="upload-btn">上传到OSS</button>
    <button id="close-btn">关闭窗口</button>
    <div id="upload-status"></div>
  </div>
  <h1>${filename}</h1>
  <pre id="markdown-content">${markdownContent.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
</body>
</html>`;

      // 创建Blob对象
      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });

      // 创建URL
      const url = URL.createObjectURL(blob);

      // 打开新窗口
      const newWindow = window.open(url, '_blank');
      if (!newWindow) {
        throw new Error('无法打开新窗口，请检查是否启用了弹窗拦截');
      }

      // 在新窗口加载完成后添加JavaScript
      newWindow.addEventListener('load', function() {
        // 添加复制功能
        const copyBtn = newWindow.document.getElementById('copy-btn');
        if (copyBtn) {
          copyBtn.addEventListener('click', function() {
            const content = newWindow.document.getElementById('markdown-content').textContent;
            newWindow.navigator.clipboard.writeText(content)
              .then(() => newWindow.alert('内容已复制到剪贴板'))
              .catch(err => newWindow.alert('复制失败: ' + err));
          });
        }

        // 添加下载功能
        const downloadBtn = newWindow.document.getElementById('download-btn');
        if (downloadBtn) {
          downloadBtn.addEventListener('click', function() {
            const content = newWindow.document.getElementById('markdown-content').textContent;
            const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = newWindow.document.createElement('a');
            a.href = url;
            a.download = filename;
            newWindow.document.body.appendChild(a);
            a.click();
            newWindow.document.body.removeChild(a);
            URL.revokeObjectURL(url);
          });
        }

        // 添加上传到OSS功能
        const uploadBtn = newWindow.document.getElementById('upload-btn');
        if (uploadBtn) {
          uploadBtn.addEventListener('click', async function() {
            const statusEl = newWindow.document.getElementById('upload-status');
            statusEl.textContent = '正在上传到OSS...';
            statusEl.className = 'info';

            // 创建进度条
            const progressBar = newWindow.document.createElement('div');
            progressBar.className = 'progress-bar';
            const progressBarFill = newWindow.document.createElement('div');
            progressBarFill.className = 'progress-bar-fill';
            progressBar.appendChild(progressBarFill);
            statusEl.appendChild(progressBar);

            try {
              const content = newWindow.document.getElementById('markdown-content').textContent;
              const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });

              const { policy, signature } = await getCDNSignature("6OSB76zlj8l2Hk7hhrnP8QUyUn6amP");

              // 准备表单数据
              const formData = new FormData();
              formData.append('OSSAccessKeyId', 'LTAIM23ZKZHBJoDn');
              formData.append('policy', policy);
              formData.append('signature', signature);
              formData.append('key', `upload/${filename}`);
              formData.append('success_action_status', '200');
              formData.append('file', blob);

              // 使用XMLHttpRequest上传，以便跟踪进度
              const xhr = new XMLHttpRequest();
              xhr.open('POST', 'https://video-clip.oss-cn-shanghai.aliyuncs.com', true);

              // 跟踪上传进度
              xhr.upload.onprogress = function(e) {
                if (e.lengthComputable) {
                  const percentComplete = (e.loaded / e.total) * 100;
                  progressBarFill.style.width = percentComplete + '%';
                }
              };

              // 处理上传完成
              xhr.onload = function() {
                if (xhr.status === 200) {
                  const fileUrl = `https://video-clip.oss-cn-shanghai.aliyuncs.com/upload/${filename}`;
                  statusEl.innerHTML = '<strong>✅ 上传成功!</strong><br>文件URL: <a href="' + fileUrl + '" target="_blank">' + fileUrl + '</a>';
                  statusEl.className = 'success';
                } else {
                  statusEl.textContent = '❌ 上传失败: ' + xhr.statusText;
                  statusEl.className = 'error';
                }
              };

              // 处理上传错误
              xhr.onerror = function() {
                statusEl.textContent = '❌ 上传失败: 网络错误';
                statusEl.className = 'error';
              };

              // 发送请求
              xhr.send(formData);
            } catch (error) {
              statusEl.textContent = '❌ 上传失败: ' + error.message;
              statusEl.className = 'error';
            }
          });
        }

        // 添加关闭窗口功能
        const closeBtn = newWindow.document.getElementById('close-btn');
        if (closeBtn) {
          closeBtn.addEventListener('click', function() {
            newWindow.close();
          });
        }
      });

      // 释放URL对象
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 5000);

      updateStatusIndicator('✅ 数据已准备完成', 'success');
      setTimeout(removeStatusIndicator, 3000);

    } catch (error) {
      console.error('❌ 显示Markdown内容失败:', error);
      updateStatusIndicator('❌ 显示内容失败', 'error');
      throw error;
    }
  }
    /**
   * 创建上传按钮
   * @returns {HTMLButtonElement} 上传按钮
   */
  function createUploadButton() {
    // 创建上传按钮
    const uploadBtn = createStyledButton('批量上传复盘数据', 'jinritemai-upload-btn', {
      background: '#1890ff',
      boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',
      marginLeft: '15px',
      marginRight: '0'
    });

    // 添加点击事件
    uploadBtn.addEventListener('click', async () => {
      try {
        uploadBtn.disabled = true;
        uploadBtn.textContent = '处理中...';

        // 获取选中的行数据
        const selectedRows = getSelectedRows();
        const uploadRows = selectedRows.uploadData;
        const audioRows = selectedRows.downloadAudio;

        if (uploadRows.length === 0) {
          updateStatusIndicator('❌ 请先选择要上传的直播数据', 'error');
          uploadBtn.disabled = false;
          uploadBtn.textContent = '批量上传复盘数据';
          return;
        }

        console.log(`📊 开始批量处理 ${uploadRows.length} 个直播间的数据`);
        updateStatusIndicator(`🔄 开始批量处理 ${uploadRows.length} 个直播间...`, 'info');

        let successCount = 0;
        let failCount = 0;

        // 逐个处理选中的直播间
        for (let i = 0; i < uploadRows.length; i++) {
          const roomId = uploadRows[i];
          const shouldDownloadAudio = audioRows.includes(roomId);

          try {
            updateStatusIndicator(`🔄 处理第 ${i + 1}/${uploadRows.length} 个直播间: ${roomId}`, 'info');
            console.log(`📝 开始处理直播间 ${roomId}，下载音频: ${shouldDownloadAudio}`);

            // 提取单个直播间的数据
            const data = await extractSingleLiveData(roomId);
            if (!data) {
              throw new Error('数据提取失败');
            }

            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `live-review-${roomId}-${timestamp}.md`;

            // 格式化为Markdown
            const markdownContent = formatDataAsMarkdown({extractedData:{apiData:data}});

            // 上传到OSS
            const { policy, signature } = await getCDNSignature("6OSB76zlj8l2Hk7hhrnP8QUyUn6amP");
            const blob = new Blob([markdownContent], { type: 'text/markdown;charset=utf-8' });

            const formData = new FormData();
            formData.append('OSSAccessKeyId', 'LTAIM23ZKZHBJoDn');
            formData.append('policy', policy);
            formData.append('signature', signature);
            formData.append('key', `upload/${filename}`);
            formData.append('success_action_status', '200');
            formData.append('file', blob);

            const response = await fetch('https://video-clip.oss-cn-shanghai.aliyuncs.com', {
              method: 'POST',
              body: formData
            });

            if (response.status !== 200) {
              throw new Error(`上传失败: ${response.status}`);
            }

            const fileUrl = `https://video-clip.oss-cn-shanghai.aliyuncs.com/upload/${filename}`;
            console.log(`✅ 直播间 ${roomId} 数据上传成功: ${fileUrl}`);

            // 获取直播回放链接（仅当勾选下载音频时）
            let liveReplayUrl = '';
            if (shouldDownloadAudio) {
              try {
                const videoLinkData = await getLiveRoomReviewVideoLink(roomId);
                liveReplayUrl = videoLinkData.url || '';
                console.log(`📹 直播间 ${roomId} 回放链接: ${liveReplayUrl}`);
              } catch (error) {
                console.warn(`⚠️ 获取直播间 ${roomId} 回放链接失败:`, error);
              }
            }

            // 获取直播间基础信息
            const basicInfo = await getLiveRoomBasicInfo(roomId);
            const accountInfo = await getAccountInfo();

            const anchorName = accountInfo.account_name || basicInfo.anchor_name || '';
            const liveTitle = basicInfo.live_room_base_info.title || '';
            const liveTime = basicInfo.live_room_base_info.start_time.value ?
              new Date(basicInfo.live_room_base_info.start_time.value * 1000).toISOString() :
              new Date().toISOString();

            // 记录直播数据
            await recordLiveStreamData(roomId, anchorName, liveTitle, liveTime, [fileUrl], liveReplayUrl);

            successCount++;
            console.log(`✅ 直播间 ${roomId} 处理完成 (${i + 1}/${uploadRows.length})`);

          } catch (error) {
            failCount++;
            console.error(`❌ 处理直播间 ${roomId} 失败:`, error);
          }

          // 添加延迟避免请求过于频繁
          if (i < uploadRows.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        // 显示最终结果
        const resultMessage = `✅ 批量处理完成！成功: ${successCount}，失败: ${failCount}`;
        updateStatusIndicator(resultMessage, successCount > 0 ? 'success' : 'error');
        console.log(resultMessage);

        uploadBtn.textContent = '批量上传复盘数据';
        setTimeout(removeStatusIndicator, 5000);

      } catch (error) {
        console.error('❌ 批量处理过程中发生错误:', error);
        updateStatusIndicator('❌ 批量处理失败，请重试', 'error');
        uploadBtn.textContent = '批量上传复盘数据';
      } finally {
        uploadBtn.disabled = false;
      }
    });

    return uploadBtn;
  }

  /**
   * 提取单个直播间的数据
   * @param {string} roomId - 直播间ID
   * @returns {Promise<Object>} 直播间数据
   */
  async function extractSingleLiveData(roomId) {
    try {
      console.log(`🔍 开始提取直播间 ${roomId} 的数据...`);
      updateStatusIndicator(`🔍 正在提取直播间 ${roomId} 数据...`, 'info');

      // 获取基础信息
      updateStatusIndicator(`📋 获取基础信息 - ${roomId}`, 'info');
      const basicInfo = await getLiveRoomBasicInfo(roomId);

      // 获取交易信息
      updateStatusIndicator(`💰 获取交易信息 - ${roomId}`, 'info');
      const tradeInfo = await getLiveRoomTradeInfo(roomId);

      // 获取诊断信息
      let diagnosisInfo = null;
      try {
        updateStatusIndicator(`🔍 获取诊断信息 - ${roomId}`, 'info');
        diagnosisInfo = await getLiveRoomDiagnosis(roomId);
      } catch (error) {
        console.warn('⚠️ 获取诊断信息失败，将跳过该部分:', error);
      }

      // 获取改进方向信息
      let improvedDirectionInfo = null;
      try {
        updateStatusIndicator(`📈 获取改进方向 - ${roomId}`, 'info');
        improvedDirectionInfo = await getLiveRoomImprovedDirection(roomId);
      } catch (error) {
        console.warn('⚠️ 获取改进方向信息失败，将跳过该部分:', error);
      }

      // 获取流量结构信息
      updateStatusIndicator(`🌊 获取流量结构 - ${roomId}`, 'info');
      const flowStructureInfo = await getLiveRoomFlowStructure(roomId);

      // 获取流量来源信息
      updateStatusIndicator(`📊 获取流量来源 - ${roomId}`, 'info');
      const flowSourceInfo = await getLiveRoomFlowSource(roomId);

      // 获取小时曝光信息
      updateStatusIndicator(`⏰ 获取小时曝光 - ${roomId}`, 'info');
      const hourlyExposureInfo = await getLiveRoomHourlyExposure(roomId);

      // 获取流量趋势信息
      updateStatusIndicator(`📈 获取流量趋势 - ${roomId}`, 'info');
      const flowTrendInfo = await getLiveRoomFlowTrend(roomId);

      // 获取流量分析信息
      updateStatusIndicator(`📊 获取流量分析 - ${roomId}`, 'info');
      const flowAnalysisInfo = await getLiveRoomFlowAnalysis(roomId);

      // 获取短视频引流信息
      updateStatusIndicator(`🎬 获取短视频引流 - ${roomId}`, 'info');
      const videoSourceInfo = await getLiveRoomVideoSource(roomId);

      // 获取渠道流量信息
      updateStatusIndicator(`🔀 获取渠道流量 - ${roomId}`, 'info');
      const channelFlowInfo = await getLiveRoomChannelFlow(roomId);

      // 获取流量趋势分析信息
      updateStatusIndicator(`📈 获取趋势分析 - ${roomId}`, 'info');
      const flowTrendAnalysisInfo = await getLiveRoomFlowTrendAnalysis(roomId);

      // 获取核心数据分组信息
      let coreGroupInfo = null;
      try {
        updateStatusIndicator(`🎯 获取核心数据 - ${roomId}`, 'info');
        coreGroupInfo = await getLiveRoomCoreGroupInfo(roomId);
      } catch (error) {
        console.warn('⚠️ 获取核心数据分组信息失败，将跳过该部分:', error);
      }

      // 获取竞价广告数据
      let adBidInfo = null;
      try {
        updateStatusIndicator(`📢 获取竞价广告 - ${roomId}`, 'info');
        adBidInfo = await getLiveRoomAdBidData(roomId);
      } catch (error) {
        console.warn('⚠️ 获取竞价广告数据失败，将跳过该部分:', error);
      }

      // 获取千川竞价广告数据
      let qianchuanAdBidInfo = null;
      try {
        updateStatusIndicator(`🎯 获取千川广告 - ${roomId}`, 'info');
        qianchuanAdBidInfo = await getLiveRoomQianchuanAdBidData(roomId);
      } catch (error) {
        console.warn('⚠️ 获取千川竞价广告数据失败，将跳过该部分:', error);
      }

      // 获取评论分析数据
      let commentAnalysisInfo = null;
      try {
        updateStatusIndicator(`💬 获取评论分析 - ${roomId}`, 'info');
        commentAnalysisInfo = await getLiveRoomCommentAnalysis(roomId);
      } catch (error) {
        console.warn('⚠️ 获取评论分析数据失败，将跳过该部分:', error);
      }

      // 获取评论列表数据
      let allCommentsInfo = null;
      try {
        updateStatusIndicator(`📝 获取评论列表 - ${roomId}`, 'info');
        allCommentsInfo = await getLiveRoomCommentList(roomId);
      } catch (error) {
        console.warn('⚠️ 获取评论列表数据失败，将跳过该部分:', error);
      }

      // 获取其他评论数据
      let allOtherCommentsInfo = null;
      try {
        updateStatusIndicator(`📄 获取其他评论 - ${roomId}`, 'info');
        allOtherCommentsInfo = await getLiveRoomAllCommentList(roomId);
      } catch (error) {
        console.warn('⚠️ 获取其他评论数据失败，将跳过该部分:', error);
      }

      // 获取用户画像数据
      let userPortraitInfo = null;
      try {
        updateStatusIndicator(`👥 获取用户画像 - ${roomId}`, 'info');
        console.log('🔍 开始获取用户画像数据...');
        userPortraitInfo = await getAllUserPortraits(roomId);
        console.log('✅ 用户画像数据获取成功');
      } catch (error) {
        console.warn('⚠️ 获取用户画像数据失败，将跳过该部分:', error);
      }

      // 获取商品列表数据
      let productsListInfo = null;
      try {
        updateStatusIndicator(`🛍️ 获取商品列表 - ${roomId}`, 'info');
        console.log('🔍 开始获取商品列表数据...');
        productsListInfo = await getLiveRoomProductsList(roomId);
        console.log('✅ 商品列表数据获取成功');
      } catch (error) {
        console.warn('⚠️ 获取商品列表数据失败，将跳过该部分:', error);
      }

      updateStatusIndicator(`✅ 直播间 ${roomId} 数据提取完成`, 'success');

      return {
        liveRoomId: roomId,
        basicInfo,
        tradeInfo,
        diagnosisInfo,
        improvedDirectionInfo,
        flowStructureInfo,
        flowSourceInfo,
        hourlyExposureInfo,
        flowTrendInfo,
        flowAnalysisInfo,
        videoSourceInfo,
        channelFlowInfo,
        flowTrendAnalysisInfo,
        coreGroupInfo,
        adBidInfo,
        qianchuanAdBidInfo,
        commentAnalysisInfo,
        allCommentsInfo,
        allOtherCommentsInfo,
        userPortraitInfo,
        productsListInfo,
        extractTime: new Date().toISOString()
      };

    } catch (error) {
      console.error(`❌ 提取直播间 ${roomId} 数据失败:`, error);
      updateStatusIndicator(`❌ 提取直播间 ${roomId} 失败`, 'error');
      throw error;
    }
  }
  /**
   * 查找目标元素并插入上传按钮
   * @returns {boolean} 是否插入成功
   */
  async function insertUploadButton() {
    // 使用全局变量而不是局部变量
    if (window.uploadButtonInserted) {
      console.log('✅ 上传按钮已存在，跳过插入');
      return true;
    }

    console.log('🔍 正在查找目标元素...');
    updateStatusIndicator('🔍 正在查找目标元素...', 'info');

    try {
      // 等待目标元素出现
      const targetElement = await waitForElement('.cp-top-banner-main', 15000);

      if (!targetElement) {
        console.log('❌ 未找到目标元素 .cp-top-banner-main');
        updateStatusIndicator('❌ 未找到目标元素', 'error');
        return false;
      }

      console.log('✅ 找到目标元素，开始创建上传按钮');

      // 创建上传按钮
      const uploadBtn = createUploadButton();

      // 插入按钮到目标元素右侧
      const success = safeInsertElement(uploadBtn, targetElement, 'after');

      if (success) {
        window.uploadButtonInserted = true;
        console.log('✅ 上传按钮插入成功');
        updateStatusIndicator('✅ 上传按钮已就绪', 'success');

        // 3秒后移除状态指示器
        setTimeout(removeStatusIndicator, 3000);
        return true;
      } else {
        console.log('❌ 上传按钮插入失败');
        updateStatusIndicator('❌ 按钮插入失败', 'error');
        return false;
      }

    } catch (error) {
      console.error('❌ 插入上传按钮时发生错误:', error);
      updateStatusIndicator('❌ 插入按钮失败', 'error');
      return false;
    }
  }

  /**
   * 启动DOM观察器
   */
  function startDOMObserver() {
    if (domObserver) {
      domObserver.disconnect();
    }

    console.log('👀 启动DOM观察器...');
    updateStatusIndicator('👀 等待页面加载...', 'info');

    domObserver = new MutationObserver((mutations) => {
      // 检查是否有新的DOM节点添加
      for (const mutation of mutations) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // 尝试插入按钮（异步执行，不等待结果）
          insertUploadButton().then(success => {
            if (success) {
              // 插入成功后停止观察
              domObserver.disconnect();
              domObserver = null;
            }
          }).catch(error => {
            console.error('❌ DOM观察器中插入按钮失败:', error);
          });
          break; // 只尝试一次，避免重复执行
        }
      }
    });

    // 开始观察DOM变化
    domObserver.observe(document.body, {
      childList: true,
      subtree: true
    });

    // 设置超时，避免无限等待
    setTimeout(() => {
      if (domObserver && !window.uploadButtonInserted) {
        console.log('⏰ DOM观察器超时，停止观察');
        domObserver.disconnect();
        domObserver = null;
        updateStatusIndicator('⏰ 页面加载超时，请刷新重试', 'warning');
        setTimeout(removeStatusIndicator, 5000);
      }
    }, 30000); // 30秒超时
  }

    // ==================== 表格增强功能 ====================

  // 表格状态管理
  const tableState = {
    observer: null,
    rowData: new Map(), // 存储每行的状态数据
    isInitialized: false
  };

  /**
   * 初始化表格增强功能
   */
  function initTableEnhancement() {
    console.log('🔧 初始化表格增强功能...');

    // 使用更通用的选择器来查找表格
    const tableSelectors = [
      '.ecom-table-content table',
      '.ecom-table table',
      'table.ecom-table',
      '.ant-table-content table',
      '.ant-table table',
      'table.ant-table',
      'table'
    ];

    // 尝试所有可能的选择器
    const findTable = async () => {
      for (const selector of tableSelectors) {
        const table = await waitForElement(selector, 5000);
        if (table) {
          console.log(`✅ 找到表格元素: ${selector}`);
          return table;
        }
      }
      return null;
    };

    // 查找并增强表格
    findTable().then(table => {
      if (table) {
        console.log('✅ 开始增强表格...');
        enhanceTable(table);
        startTableObserver(table);
        tableState.isInitialized = true;
      } else {
        console.log('❌ 未找到表格元素，将继续监听DOM变化');
        // 设置DOM观察器来监听表格的出现
        const observer = new MutationObserver((mutations) => {
          for (const selector of tableSelectors) {
            const table = document.querySelector(selector);
            if (table && !tableState.isInitialized) {
              console.log(`✅ DOM变化后找到表格元素: ${selector}`);
              observer.disconnect();
              enhanceTable(table);
              startTableObserver(table);
              tableState.isInitialized = true;
              return;
            }
          }
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });

        // 30秒后停止观察，避免无限等待
        setTimeout(() => {
          observer.disconnect();
          console.log('⏰ 表格查找超时');
        }, 30000);
      }
    });
  }

  /**
   * 增强表格 - 添加checkbox列
   */
  function enhanceTable(table) {
    console.log('🔍 开始增强表格，表格结构:', table);

    // 查找表头和表体
    const thead = table.querySelector('thead tr') ||
                  table.querySelector('tr:first-child') ||
                  table.querySelector('.ant-table-thead tr') ||
                  table.querySelector('.ecom-table-thead tr');

    const tbody = table.querySelector('tbody');

    if (thead) {
      console.log('✅ 找到表头元素');
      addTableHeaders(thead);
    } else {
      console.log('❌ 无法找到表头元素');
    }

    // 查找所有数据行
    let dataRows = [];
    if (tbody) {
      dataRows = tbody.querySelectorAll('tr[data-row-key]');
      console.log(`✅ 在表体中找到 ${dataRows.length} 个数据行`);
    } else {
      dataRows = table.querySelectorAll('tr[data-row-key]');
      console.log(`✅ 直接在表格中找到 ${dataRows.length} 个数据行`);
    }

    // 为每行添加checkbox
    dataRows.forEach(row => {
      const rowKey = row.getAttribute('data-row-key');
      if (rowKey) {
        addCheckboxCells(row, rowKey);
        // 初始化行数据状态
        if (!tableState.rowData.has(rowKey)) {
          tableState.rowData.set(rowKey, {
            uploadData: false,
            downloadAudio: false,
            rowElement: row
          });
        }
      }
    });

    console.log(`📊 表格增强完成，处理了 ${dataRows.length} 行数据`);
  }

  /**
   * 添加表头（添加列标题和全选checkbox）
   */
  function addTableHeaders(thead) {
    // 检查是否已经添加过
    if (thead.querySelector('.custom-upload-header')) {
      console.log('⏭️ 表头已添加，跳过');
      return;
    }

    console.log('➕ 添加表头列');

    // 创建上传数据列头（包含全选checkbox）
    const uploadHeader = document.createElement('th');
    uploadHeader.className = 'ecom-table-cell custom-upload-header';
    uploadHeader.style.cssText = 'text-align: center; width: 120px; position: sticky; left: 0px; background: white; z-index: 10; border-right: 1px solid #f0f0f0;';
    uploadHeader.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; padding: 8px; flex-direction: column;">
        <span style="font-weight: 500; margin-bottom: 4px;">上传数据</span>
        <input type="checkbox" id="select-all-upload" style="cursor: pointer;" title="全选上传数据">
      </div>
    `;

    // 创建下载音频列头（包含全选checkbox）
    const audioHeader = document.createElement('th');
    audioHeader.className = 'ecom-table-cell custom-audio-header';
    audioHeader.style.cssText = 'text-align: center; width: 120px; position: sticky; left: 120px; background: white; z-index: 10; border-right: 1px solid #f0f0f0;';
    audioHeader.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; padding: 8px; flex-direction: column;">
        <span style="font-weight: 500; margin-bottom: 4px;">下载音频</span>
        <input type="checkbox" id="select-all-audio" style="cursor: pointer;" title="全选下载音频">
      </div>
    `;

    try {
      // 插入到第一列前面
      thead.insertBefore(uploadHeader, thead.firstChild);
      thead.insertBefore(audioHeader, thead.children[1]);
      console.log('✅ 表头列添加成功');

      // 设置全选checkbox事件
      setupSelectAllEvents();

      // 调整现有列的sticky位置
      adjustStickyPositions(thead);
    } catch (error) {
      console.error('❌ 添加表头列失败:', error);
    }
  }

  /**
   * 设置全选checkbox事件
   */
  function setupSelectAllEvents() {
    const selectAllUpload = document.getElementById('select-all-upload');
    const selectAllAudio = document.getElementById('select-all-audio');

    if (selectAllUpload) {
      selectAllUpload.addEventListener('change', (e) => {
        const isChecked = e.target.checked;
        console.log(`📝 全选上传数据: ${isChecked}`);

        // 更新所有行的上传数据checkbox状态
        tableState.rowData.forEach((data, rowKey) => {
          data.uploadData = isChecked;
          const checkbox = document.querySelector(`.upload-checkbox[data-row-key="${rowKey}"]`);
          if (checkbox) {
            checkbox.checked = isChecked;
          }
        });
      });
    }

    if (selectAllAudio) {
      selectAllAudio.addEventListener('change', (e) => {
        const isChecked = e.target.checked;
        console.log(`🎵 全选下载音频: ${isChecked}`);

        // 更新所有行的下载音频checkbox状态
        tableState.rowData.forEach((data, rowKey) => {
          data.downloadAudio = isChecked;
          const checkbox = document.querySelector(`.audio-checkbox[data-row-key="${rowKey}"]`);
          if (checkbox) {
            checkbox.checked = isChecked;
          }
        });
      });
    }
  }

  /**
   * 为行添加checkbox单元格
   */
  function addCheckboxCells(row, rowKey) {
    // 检查是否已经添加过
    if (row.querySelector('.custom-upload-cell')) {
      return;
    }

    // 创建上传数据checkbox单元格
    const uploadCell = document.createElement('td');
    uploadCell.className = 'ecom-table-cell custom-upload-cell';
    uploadCell.style.cssText = 'text-align: center; position: sticky; left: 0px; background: white; z-index: 5; border-right: 1px solid #f0f0f0; padding: 8px; width: 120px;';
    uploadCell.innerHTML = `
      <input type="checkbox" class="upload-checkbox" data-row-key="${rowKey}" style="cursor: pointer;">
    `;

    // 创建下载音频checkbox单元格
    const audioCell = document.createElement('td');
    audioCell.className = 'ecom-table-cell custom-audio-cell';
    audioCell.style.cssText = 'text-align: center; position: sticky; left: 120px; background: white; z-index: 5; border-right: 1px solid #f0f0f0; padding: 8px; width: 120px;';
    audioCell.innerHTML = `
      <input type="checkbox" class="audio-checkbox" data-row-key="${rowKey}" style="cursor: pointer;">
    `;

    try {
      // 插入到第一列前面
      row.insertBefore(uploadCell, row.firstChild);
      row.insertBefore(audioCell, row.children[1]);

      // 添加事件监听
      setupRowCheckboxEvents(uploadCell, audioCell, rowKey);

      // 调整现有单元格的sticky位置
      adjustRowStickyPositions(row);

      console.log(`✅ 为行 ${rowKey} 添加checkbox成功`);
    } catch (error) {
      console.error(`❌ 为行 ${rowKey} 添加checkbox失败:`, error);
    }
  }

  /**
   * 设置行checkbox事件
   */
  function setupRowCheckboxEvents(uploadCell, audioCell, rowKey) {
    const uploadCheckbox = uploadCell.querySelector('.upload-checkbox');
    const audioCheckbox = audioCell.querySelector('.audio-checkbox');

    uploadCheckbox.addEventListener('change', (e) => {
      const rowData = tableState.rowData.get(rowKey);
      if (rowData) {
        rowData.uploadData = e.target.checked;
        console.log(`📝 行 ${rowKey} 上传数据状态: ${e.target.checked}`);

        // 更新全选checkbox状态
        updateSelectAllState('upload');
      }
    });

    audioCheckbox.addEventListener('change', (e) => {
      const rowData = tableState.rowData.get(rowKey);
      if (rowData) {
        rowData.downloadAudio = e.target.checked;
        console.log(`🎵 行 ${rowKey} 下载音频状态: ${e.target.checked}`);

        // 更新全选checkbox状态
        updateSelectAllState('audio');
      }
    });
  }

  /**
   * 更新全选checkbox状态
   */
  function updateSelectAllState(type) {
    const selectAllCheckbox = document.getElementById(`select-all-${type}`);
    if (!selectAllCheckbox) return;

    const property = type === 'upload' ? 'uploadData' : 'downloadAudio';
    const allRows = Array.from(tableState.rowData.values());

    if (allRows.length === 0) {
      selectAllCheckbox.checked = false;
      selectAllCheckbox.indeterminate = false;
      return;
    }

    const checkedCount = allRows.filter(data => data[property]).length;

    if (checkedCount === 0) {
      selectAllCheckbox.checked = false;
      selectAllCheckbox.indeterminate = false;
    } else if (checkedCount === allRows.length) {
      selectAllCheckbox.checked = true;
      selectAllCheckbox.indeterminate = false;
    } else {
      selectAllCheckbox.checked = false;
      selectAllCheckbox.indeterminate = true;
    }
  }

  /**
   * 调整sticky位置
   */
  function adjustStickyPositions(thead) {
    const cells = thead.querySelectorAll('th');
    let leftOffset = 240; // 前两列的宽度（120 + 120）

    cells.forEach((cell, index) => {
      if (index > 1) { // 跳过前两个自定义列
        if (cell.style.position === 'sticky') {
          cell.style.left = `${leftOffset}px`;
        }
        leftOffset += cell.offsetWidth || 100;
      }
    });
  }

  /**
   * 调整行的sticky位置
   */
  function adjustRowStickyPositions(row) {
    const cells = row.querySelectorAll('td');
    let leftOffset = 240; // 前两列的宽度（120 + 120）

    cells.forEach((cell, index) => {
      if (index > 1) { // 跳过前两个自定义列
        if (cell.style.position === 'sticky') {
          cell.style.left = `${leftOffset}px`;
        }
        leftOffset += cell.offsetWidth || 100;
      }
    });
  }

  /**
   * 启动表格监控
   */
  function startTableObserver(table) {
    if (tableState.observer) {
      tableState.observer.disconnect();
    }

    console.log('👀 启动表格数据监控...');

    tableState.observer = new MutationObserver((mutations) => {
      let newRowsFound = false;

      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // 检查是否有新的数据行
              let newRows = [];

              // 如果节点本身是行
              if (node.matches && node.matches('tr[data-row-key]')) {
                newRows.push(node);
              }

              // 如果节点包含行
              if (node.querySelectorAll) {
                const childRows = node.querySelectorAll('tr[data-row-key]');
                if (childRows.length > 0) {
                  newRows = [...newRows, ...childRows];
                }
              }

              if (newRows.length > 0) {
                newRowsFound = true;
                console.log(`🆕 发现 ${newRows.length} 个新行`);

                newRows.forEach(row => {
                  const rowKey = row.getAttribute('data-row-key');
                  if (rowKey && !tableState.rowData.has(rowKey)) {
                    console.log(`🆕 处理新行: ${rowKey}`);
                    addCheckboxCells(row, rowKey);
                    tableState.rowData.set(rowKey, {
                      uploadData: false,
                      downloadAudio: false,
                      rowElement: row
                    });
                  }
                });
              }
            }
          });
        }
      });

      // 如果找到新行，打印当前状态
      if (newRowsFound) {
        console.log(`📊 当前表格状态: ${tableState.rowData.size} 行数据`);
      }
    });

    // 监控整个表格的变化
    tableState.observer.observe(table, {
      childList: true,
      subtree: true
    });

    // 也监控表格的父元素，以防表格被替换
    if (table.parentElement) {
      tableState.observer.observe(table.parentElement, {
        childList: true
      });
    }
  }

  /**
   * 获取选中的行数据
   */
  function getSelectedRows() {
    const selectedUpload = [];
    const selectedAudio = [];

    tableState.rowData.forEach((data, rowKey) => {
      if (data.uploadData) {
        selectedUpload.push(rowKey);
      }
      if (data.downloadAudio) {
        selectedAudio.push(rowKey);
      }
    });

    return {
      uploadData: selectedUpload,
      downloadAudio: selectedAudio,
      allRowData: Array.from(tableState.rowData.entries())
    };
  }

  /**
   * 清理表格状态
   */
  function cleanupTableState() {
    if (tableState.observer) {
      tableState.observer.disconnect();
      tableState.observer = null;
    }
    tableState.rowData.clear();
    tableState.isInitialized = false;
  }

  // 暴露到全局作用域，方便调试和使用
  window.getSelectedRows = getSelectedRows;
  window.tableState = tableState;
  window.initTableEnhancement = initTableEnhancement;
  window.cleanupTableState = cleanupTableState;

  /**
   * 初始化脚本
   */
  async function initialize() {
    console.log('🚀 巨量百应直播数据下载器启动...');

    // 创建状态指示器
    createStatusIndicator();
    updateStatusIndicator('🚀 脚本启动中...', 'info');

    // 等待页面基本加载完成
    if (document.readyState === 'loading') {
      await new Promise(resolve => {
        document.addEventListener('DOMContentLoaded', resolve);
      });
    }

    // 延迟2秒后开始尝试插入按钮
    setTimeout(() => {
      // 先尝试直接插入
      insertUploadButton().then(success => {
        if (!success) {
          // 如果直接插入失败，启动DOM观察器
          startDOMObserver();
        }
      }).catch(error => {
        console.error('❌ 初始化插入按钮失败:', error);
        // 即使失败也启动DOM观察器作为备用方案
        startDOMObserver();
      });
    }, 2000);

    setTimeout(() => {
      initTableEnhancement();
    }, 3000);
  }

  /**
   * 清理函数
   */
  function cleanup() {
    if (domObserver) {
      domObserver.disconnect();
      domObserver = null;
    }
    removeStatusIndicator();
  }

  // ==================== 脚本入口 ====================

  // 页面卸载时清理资源
  window.addEventListener('beforeunload', cleanup);

  // 启动脚本
  initialize().catch(error => {
    console.error('❌ 脚本初始化失败:', error);
    updateStatusIndicator('❌ 脚本初始化失败', 'error');
  });

})();
