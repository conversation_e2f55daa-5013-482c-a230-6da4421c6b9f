// ==UserScript==
// @name         视频号助手直播数据下载器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  在视频号助手直播报表页面添加数据下载功能
// <AUTHOR>
// @match        https://channels.weixin.qq.com/platform/statistic/live*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(async function () {
  'use strict';

  // ==================== API 配置 ====================
  const API_CONFIG = {
    baseURL: 'https://channels.weixin.qq.com/',
    defaultHeaders: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    },
    timeout: 30000 // 30秒超时
  };

  // ==================== 辅助函数 ====================

  /**
   * 格式化时间戳
   * @param {number} timestamp - 时间戳（毫秒或秒）
   * @returns {string} 格式化后的时间
   */
  function formatTimestamp(timestamp) {
    if (!timestamp) return '-';

    // 如果时间戳是秒级别的，转换为毫秒
    if (timestamp < 10000000000) {
      timestamp = timestamp * 1000;
    }

    const date = new Date(timestamp);

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '-';
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}/${month}/${day} ${hours}:${minutes}`;
  }

  /**
   * 格式化时长
   * @param {number} seconds - 秒数
   * @returns {string} 格式化后的时长
   */
  function formatDuration(seconds) {
    if (!seconds || seconds < 0) return '0秒';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}小时${minutes}分${secs}秒`;
    } else if (minutes > 0) {
      return `${minutes}分${secs}秒`;
    } else {
      return `${secs}秒`;
    }
  }

  /**
   * 下载数据为Markdown文件
   * @param {Object} data - 要下载的数据
   * @param {string} filename - 文件名
   */
  function downloadAsMarkdown(data, filename = 'live-data.md') {
    try {
      const markdownContent = formatDataAsMarkdown(data);
      const blob = new Blob([markdownContent], { type: 'text/markdown;charset=utf-8' });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('✅ Markdown文件下载成功');
    } catch (error) {
      console.error('❌ Markdown文件下载失败:', error);
    }
  }

  // ==================== API 调用函数 ====================

  /**
   * 通用API调用函数
   * @param {string} endpoint - API端点路径
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} API响应数据
   */
  async function callAPI(endpoint, options = {}) {
    const {
      method = 'GET',
      params = {},
      body = null,
      headers = {},
      timeout = API_CONFIG.timeout
    } = options;

    try {
      // 构建完整URL
      const url = new URL(endpoint, API_CONFIG.baseURL);

      // 添加查询参数
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null) {
          url.searchParams.append(key, params[key]);
        }
      });

      console.log(`🌐 调用API: ${method} ${url.toString()}`);

      // 合并请求头
      const finalHeaders = {
        ...API_CONFIG.defaultHeaders,
        ...headers
      };

      // 创建请求配置
      const requestConfig = {
        method,
        headers: finalHeaders,
        credentials: 'include',
        mode: 'cors'
      };

      // 添加请求体（如果是POST请求）
      if (body && (method === 'POST' || method === 'PUT')) {
        requestConfig.body = body;
      }

      // 创建超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      requestConfig.signal = controller.signal;

      try {
        const response = await fetch(url.toString(), requestConfig);
        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`✅ API调用成功:`, data);
        return data;

      } catch (fetchError) {
        clearTimeout(timeoutId);
        throw fetchError;
      }

    } catch (error) {
      console.error(`❌ API调用失败 [${endpoint}]:`, error);
      throw error;
    }
  }

  /**
   * 获取电商转化仪表板数据
   * @param {string} liveObjectId - 直播对象ID
   * @param {string} timestamp - 时间戳
   * @param {Object} options - 其他可选参数
   * @returns {Promise<Object>} 电商转化仪表板数据
   */
  async function getEcConversionDashboardData(liveObjectId, timestamp, options = {}) {
    if (!liveObjectId) {
      throw new Error('直播对象ID不能为空');
    }

    if (!timestamp) {
      throw new Error('时间戳不能为空');
    }

    try {
      const requestBody = {
        liveObjectId,
        diagnosticOption: {
          uptime: "1",
          isWindowFocused: true
        },
        panelTrendingTrafficQueryOption: {
          timeRange: 6,
          isEnabled: true,
          enabledMetricTypes: [3, 4, 1],
          enabledDimensionTypes: [0]
        },
        panelPortraitAudienceQueryOption: {
          enabledDimensionTypes: [2, 13, 12, 9, 3, 4, 10, 5, 1, 14],
          isEnabled: true,
          enabledMetricTypes: [2, 1],
          enabledDimensionPrefectureLevelAdcode: false,
          selectedProvinceLevelAdcode: "",
          enabledFollowerCumulativeWatchUv: false
        },
        timestamp,
        rawKeyBuff: options.rawKeyBuff || null,
        pluginSessionId: options.pluginSessionId || null,
        scene: options.scene || 7,
        reqScene: options.reqScene || 7
      };

      const response = await callAPI('micro/statistic/cgi-bin/mmfinderassistant-bin/statistic/get_ec_conversion_dashboard_data_v3', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (response.errCode !== 0) {
        throw new Error(`API返回错误: ${response.errMsg || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error(`❌ 获取电商转化仪表板数据失败 [${liveObjectId}]:`, error);
      throw error;
    }
  }

  /**
   * 获取电商转化仪表板数据V3
   * @param {string} liveObjectId - 直播对象ID
   * @param {string} timestamp - 时间戳
   * @param {Object} options - 其他可选参数
   * @returns {Promise<Object>} 电商转化仪表板数据V3
   */
  async function getEcConversionDashboardDataV3(liveObjectId, timestamp, options = {}) {
    if (!liveObjectId) {
      throw new Error('直播对象ID不能为空');
    }

    if (!timestamp) {
      throw new Error('时间戳不能为空');
    }

    try {
      const requestBody = {
        liveObjectId,
        diagnosticOption: {
          uptime: options.uptime || "18306",
          isWindowFocused: true
        },
        panelTrendingSourceQueryOption: {
          enabledMetricTypes: [3, 15, 17, 16],
          enabledPromoteType: true,
          enabledTrafficType: true,
          isEnabled: true,
          timeRange: 6
        },
        timestamp,
        rawKeyBuff: options.rawKeyBuff || null,
        pluginSessionId: options.pluginSessionId || null,
        scene: options.scene || 7,
        reqScene: options.reqScene || 7
      };

      const response = await callAPI('micro/statistic/cgi-bin/mmfinderassistant-bin/statistic/get_ec_conversion_dashboard_data_v3', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (response.errCode !== 0) {
        throw new Error(`API返回错误: ${response.errMsg || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error(`❌ 获取电商转化仪表板数据V3失败 [${liveObjectId}]:`, error);
      throw error;
    }
  }

  /**
   * 获取直播诊断能力数据
   * @param {string} liveObjectId - 直播对象ID
   * @param {string} timestamp - 时间戳
   * @param {Object} options - 其他可选参数
   * @returns {Promise<Object>} 直播诊断能力数据
   */
  async function getLiveDiagnoseCapability(liveObjectId, timestamp, options = {}) {
    if (!liveObjectId) {
      throw new Error('直播对象ID不能为空');
    }

    if (!timestamp) {
      throw new Error('时间戳不能为空');
    }

    try {
      const requestBody = {
        liveObjectId,
        timeType: options.timeType || 0,
        cmpType: options.cmpType || 0,
        timestamp,
        rawKeyBuff: options.rawKeyBuff || null,
        pluginSessionId: options.pluginSessionId || null,
        scene: options.scene || 7,
        reqScene: options.reqScene || 7
      };

      const response = await callAPI('micro/statistic/cgi-bin/mmfinderassistant-bin/svrkit/MmFinderECAssistantDataSvr/getLiveDiagnoseCapability', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (response.errCode !== 0) {
        throw new Error(`API返回错误: ${response.errMsg || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error(`❌ 获取直播诊断能力数据失败 [${liveObjectId}]:`, error);
      throw error;
    }
  }

  /**
   * 获取单场直播电商商品数据
   * @param {string} liveObjectId - 直播对象ID
   * @param {string} timestamp - 时间戳
   * @param {Object} options - 其他可选参数
   * @returns {Promise<Object>} 单场直播电商商品数据
   */
  async function getSingleLiveEcSpuData(liveObjectId, timestamp, options = {}) {
    if (!liveObjectId) {
      throw new Error('直播对象ID不能为空');
    }

    if (!timestamp) {
      throw new Error('时间戳不能为空');
    }

    try {
      const requestBody = {
        liveObjectId,
        offset: options.offset || 0,
        limit: options.limit || 15,
        spuType: options.spuType || 0,
        spuThreshold: options.spuThreshold || {
          lowStock: "10",
          unpaidOrder: "10",
          newBuyerConv: "10"
        },
        spuSrc: options.spuSrc || 0,
        fieldList: options.fieldList || ["stock", "create_pv", "pay_pv", "gmv", "clk_pay_ratio"],
        timestamp,
        rawKeyBuff: options.rawKeyBuff || null,
        pluginSessionId: options.pluginSessionId || null,
        scene: options.scene || 7,
        reqScene: options.reqScene || 7
      };

      const response = await callAPI('micro/statistic/cgi-bin/mmfinderassistant-bin/statistic/get_single_live_ec_spu_data_page_v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (response.errCode !== 0) {
        throw new Error(`API返回错误: ${response.errMsg || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error(`❌ 获取单场直播电商商品数据失败 [${liveObjectId}]:`, error);
      throw error;
    }
  }

  /**
   * 获取整场看播用户画像数据
   * @param {string} liveObjectId - 直播对象ID
   * @param {string} timestamp - 时间戳
   * @param {Object} options - 其他可选参数
   * @returns {Promise<Object>} 整场看播用户画像数据
   */
  async function getFullLiveUserPortraitData(liveObjectId, timestamp, options = {}) {
    if (!liveObjectId) {
      throw new Error('直播对象ID不能为空');
    }

    if (!timestamp) {
      throw new Error('时间戳不能为空');
    }

    try {
      console.log('🔍 正在获取用户画像数据...');
      const requestBody = {
        liveObjectId,
        diagnosticOption: {
          uptime: options.uptime || "95",
          isWindowFocused: true
        },
        panelPortraitAudienceQueryOption: {
          enabledDimensionTypes: options.enabledDimensionTypes || [2, 13, 12, 9, 3, 4, 10, 5, 1, 14],
          isEnabled: true,
          enabledMetricTypes: options.enabledMetricTypes || [2, 1],
          enabledDimensionPrefectureLevelAdcode: options.enabledDimensionPrefectureLevelAdcode || true,
          selectedProvinceLevelAdcode: options.selectedProvinceLevelAdcode || "440000",
          enabledFollowerCumulativeWatchUv: true
        },
        timestamp,
        rawKeyBuff: options.rawKeyBuff || null,
        pluginSessionId: options.pluginSessionId || null,
        scene: options.scene || 7,
        reqScene: options.reqScene || 7
      };

      const response = await callAPI('micro/statistic/cgi-bin/mmfinderassistant-bin/statistic/get_ec_conversion_dashboard_data_v3', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (response.errCode !== 0) {
        throw new Error(`API返回错误: ${response.errMsg || '未知错误'}`);
      }

      console.log('✅ 用户画像数据获取成功');
      return response.data;

    } catch (error) {
      console.error(`❌ 获取整场看播用户画像数据失败 [${liveObjectId}]:`, error);
      throw error;
    }
  }

  // ==================== 数据格式化函数 ====================

  /**
   * 格式化电商转化仪表板数据为Markdown
   * @param {Object} dashboardData - 电商转化仪表板数据
   * @returns {string} Markdown格式的电商转化报告
   */
  function formatEcConversionDashboardAsMarkdown(dashboardData) {
    if (!dashboardData) return '';

    let markdown = '# 视频号直播电商转化数据报告\n\n';

    // 处理基础信息
    if (dashboardData.liveObjectId) {
      markdown += '## 基础信息\n\n';
      markdown += `**直播对象ID**: ${dashboardData.liveObjectId}\n`;
      markdown += `**创建时间**: ${formatTimestamp(dashboardData.createTime)}\n`;
      markdown += `**结束时间**: ${formatTimestamp(dashboardData.endTime)}\n`;
      markdown += `**主播昵称**: ${dashboardData.anchorNickname || '-'}\n`;
      markdown += `**直播描述**: ${dashboardData.liveDescription || '-'}\n`;
      markdown += `**是否正在直播**: ${dashboardData.isLiving ? '是' : '否'}\n\n`;
    }

    // 处理概览数据
    if (dashboardData.overview) {
      markdown += '## 数据概览\n\n';
      markdown += '| 指标名称 | 数值 |\n';
      markdown += '|---------|------|\n';

      const overview = dashboardData.overview;
      const metrics = [
        ['实时在线人数', overview.onlineWatchUv],
        ['累计观看人数', overview.cumulativeWatchUv],
        ['人均观看时长(秒)', overview.averageWatchSecondsPerAudience],
        ['累计观看次数', overview.cumulativeWatchPv],
        ['累计新增关注人数', overview.cumulativeNewFollowUv],
        ['累计评论人数', overview.cumulativeCommentUv],
        ['累计评论次数', overview.cumulativeCommentPv],
        ['累计分享人数', overview.cumulativeSharingUv],
        ['累计分享次数', overview.cumulativeSharingPv],
        ['累计点赞次数', overview.cumulativeLikePv],
        ['峰值在线观看人数', overview.peakOnlineWatchUv],
        ['曝光人数', overview.impressionUv],
        ['曝光次数', overview.impressionPv]
      ];

      metrics.forEach(([name, value]) => {
        if (value !== undefined && value !== null) {
          const formattedValue = name.includes('时长') ? formatDuration(value) :
                               name.includes('金额') ? `¥${value}` :
                               value.toLocaleString();
          markdown += `| ${name} | ${formattedValue} |\n`;
        }
      });

      markdown += '\n';
    }

    // 处理趋势流量数据
    if (dashboardData.trendingTraffic) {
      markdown += '## 流量趋势数据\n\n';

      // 在线观看人数趋势
      if (dashboardData.trendingTraffic.onlineWatchUv) {
        markdown += '### 在线观看人数趋势\n\n';
        markdown += '| 时间 | 在线观看人数 |\n';
        markdown += '|------|-------------|\n';

        dashboardData.trendingTraffic.onlineWatchUv.forEach(trend => {
          if (trend.data && trend.data.length > 0) {
            trend.data.forEach(point => {
              const time = formatTimestamp(point.ts);
              markdown += `| ${time} | ${point.value} |\n`;
            });
          }
        });

        markdown += '\n';
      }

      // 进房人数趋势
      if (dashboardData.trendingTraffic.newWatchUv) {
        markdown += '### 进房人数趋势\n\n';
        markdown += '| 时间 | 进房人数 |\n';
        markdown += '|------|----------|\n';

        dashboardData.trendingTraffic.newWatchUv.forEach(trend => {
          if (trend.data && trend.data.length > 0) {
            trend.data.forEach(point => {
              const time = formatTimestamp(point.ts);
              markdown += `| ${time} | ${point.value} |\n`;
            });
          }
        });

        markdown += '\n';
      }

      // 离开人数趋势
      if (dashboardData.trendingTraffic.leavedWatchUv) {
        markdown += '### 离开人数趋势\n\n';
        markdown += '| 时间 | 离开人数 |\n';
        markdown += '|------|----------|\n';

        dashboardData.trendingTraffic.leavedWatchUv.forEach(trend => {
          if (trend.data && trend.data.length > 0) {
            trend.data.forEach(point => {
              const time = formatTimestamp(point.ts);
              markdown += `| ${time} | ${point.value} |\n`;
            });
          }
        });

        markdown += '\n';
      }
    }

    // 处理转化分析数据
    if (dashboardData.conversionAnalysis) {
      markdown += '## 转化分析\n\n';
      markdown += '| 指标名称 | 数值 |\n';
      markdown += '|---------|------|\n';

      const conversion = dashboardData.conversionAnalysis;
      markdown += `| 曝光人数 | ${conversion.impressionUv?.toLocaleString() || '0'} |\n`;
      markdown += `| 新观看人数 | ${conversion.newWatchUv?.toLocaleString() || '0'} |\n`;
      markdown += `| 曝光次数 | ${conversion.impressionPv?.toLocaleString() || '0'} |\n`;
      markdown += `| 新观看次数 | ${conversion.newWatchPv?.toLocaleString() || '0'} |\n`;

      // 计算转化率
      if (conversion.impressionUv && conversion.newWatchUv) {
        const conversionRate = ((conversion.newWatchUv / conversion.impressionUv) * 100).toFixed(2);
        markdown += `| 曝光到观看转化率 | ${conversionRate}% |\n`;
      }

      markdown += '\n';
    }

    return markdown;
  }

    /**
   * 格式化电商转化仪表板V3数据为Markdown
   * @param {Object} dashboardDataV3 - 电商转化仪表板V3数据
   * @returns {string} Markdown格式的报告
   */
    function formatEcConversionDashboardV3AsMarkdown(dashboardDataV3) {
      if (!dashboardDataV3) return '';

      let markdown = '# 渠道流量分析\n\n';

      // 修正数据访问路径 - 直接访问 trendingSource
      if (dashboardDataV3.trendingSource) {
        markdown += '## 流量来源分析\n\n';

        const trendingSource = dashboardDataV3.trendingSource;

        // 处理曝光相关指标（保留原有处理方式）
        const impressionMetrics = [
          { key: 'impressionPv', name: '曝光次数' },
          { key: 'impressionUv', name: '曝光人数' }
        ];

        impressionMetrics.forEach(metric => {
          if (trendingSource[metric.key] && trendingSource[metric.key].length > 0) {
            markdown += `### ${metric.name}\n\n`;
            markdown += '| 类型 | 进房人次 | 占比 | 成交金额 | 占比 | 千次观看成交金额 |\n';
            markdown += '|------|----------|------|----------|------|------------------|\n';

            const metricData = trendingSource[metric.key];

            // 处理数据并计算总数
            const processedData = [];
            let totalCount = 0;

            metricData.forEach(item => {
              if (item.data && item.data.length > 0 && item.dimensions && item.dimensions.length > 0) {
                const value = parseInt(item.data[0].value) || 0;
                const sourceName = item.dimensions[0].value || '未知来源';

                processedData.push({
                  sourceName,
                  value,
                  gmv: 0, // 模拟成交金额数据
                });

                totalCount += value;
              }
            });

            // 按数值排序显示
            processedData.sort((a, b) => b.value - a.value);

            processedData.forEach(item => {
              const countPercent = totalCount > 0 ? ((item.value / totalCount) * 100).toFixed(2) + '%' : '0.00%';
              const gmvPercent = '0.00%';
              const thousandViewAmount = '¥0.00';

              markdown += `| ${item.sourceName} | ${item.value.toLocaleString()} | ${countPercent} | ¥0.00 | ${gmvPercent} | ${thousandViewAmount} |\n`;
            });

            markdown += '\n';

            // 添加汇总行
            markdown += '#### 汇总\n\n';
            markdown += '| 项目 | 数值 |\n';
            markdown += '|------|------|\n';
            markdown += `| 总${metric.name} | ${totalCount.toLocaleString()} |\n`;
            markdown += `| 总成交金额 | ¥0.00 |\n`;
            markdown += '\n';
          }
        });

        // 处理观看相关指标（新的处理方式）
        const watchMetrics = [
          // { key: 'newWatchPv', name: '新观看次数' },
          // { key: 'newWatchUv', name: '新观看人数' }
        ];

        watchMetrics.forEach(metric => {
          if (trendingSource[metric.key] && trendingSource[metric.key].length > 0) {
            markdown += `### ${metric.name}\n\n`;

            const metricData = trendingSource[metric.key];

            // 检查数据结构是否为嵌套格式
            if (metricData[0] && metricData[0].data && Array.isArray(metricData[0].data)) {
              // 如果是嵌套格式，按时间序列展示
              markdown += '| 时间 | 来源 | 数值 |\n';
              markdown += '|------|------|------|\n';

              metricData.forEach(sourceItem => {
                const sourceName = sourceItem.dimensions && sourceItem.dimensions.length > 0 ?
                                  sourceItem.dimensions.map(dim => dim.value).join(' > ') : '未知来源';

                if (sourceItem.data && sourceItem.data.length > 0) {
                  sourceItem.data.forEach(dataPoint => {
                    const time = dataPoint.ts ? formatTimestamp(dataPoint.ts) : '未知时间';
                    const value = parseInt(dataPoint.value) || 0;
                    markdown += `| ${time} | ${sourceName} | ${value.toLocaleString()} |\n`;
                  });
                }
              });
            } else {
              // 如果是简单格式，按来源汇总展示
              markdown += '| 来源 | 数值 | 占比 |\n';
              markdown += '|------|------|------|\n';

              const processedData = [];
              let totalCount = 0;

              metricData.forEach(item => {
                if (typeof item === 'object' && item.value !== undefined) {
                  const value = parseInt(item.value) || 0;
                  const sourceName = item.source || item.name || '未知来源';

                  processedData.push({
                    sourceName,
                    value
                  });

                  totalCount += value;
                }
              });

              // 按数值排序显示
              processedData.sort((a, b) => b.value - a.value);

              processedData.forEach(item => {
                const countPercent = totalCount > 0 ? ((item.value / totalCount) * 100).toFixed(2) + '%' : '0.00%';
                markdown += `| ${item.sourceName} | ${item.value.toLocaleString()} | ${countPercent} |\n`;
              });

              // 添加汇总
              markdown += '\n#### 汇总\n\n';
              markdown += '| 项目 | 数值 |\n';
              markdown += '|------|------|\n';
              markdown += `| 总${metric.name} | ${totalCount.toLocaleString()} |\n`;
            }

            markdown += '\n';
          }
        });

      } else {
        markdown += '暂无渠道来源数据\n\n';
      }

      return markdown;
    }

    /**
     * 获取流量来源类型
     * @param {number} promoteType - 推广类型
     * @param {number} trafficType - 流量类型
     * @returns {number} 流量来源类型
     */
    function getSourceType(promoteType, trafficType) {
      // 根据promoteType和trafficType判断流量类型
      if (promoteType === 1) return 2; // 广告流量
      if (trafficType === 1) return 1; // 私域流量
      return 0; // 公域流量
    }

    /**
     * 获取详细来源名称
     * @param {number} promoteType - 推广类型
     * @param {number} trafficType - 流量类型
     * @returns {string} 详细来源名称
     */
    function getDetailedSourceName(promoteType, trafficType) {
      // 处理 undefined 值
      if (promoteType === undefined || promoteType === null) promoteType = 0;
      if (trafficType === undefined || trafficType === null) trafficType = 0;

      // 根据实际数据结构进行映射
      const sourceMapping = {
        '0_0': '公域流量',
        '0_1': '微信群聊',
        '0_2': '朋友圈',
        '0_3': '搜索发现页',
        '0_4': '服务号引流推荐',
        '0_5': '搜索',
        '0_6': '短视频引流',
        '1_0': '私域流量',
        '1_1': '分享',
        '1_2': '视频号主页推荐',
        '2_0': '加粉流量',
        '2_1': '广告流量'
      };

      const key = `${promoteType}_${trafficType}`;
      return sourceMapping[key] || `来源${promoteType}-${trafficType}`;
    }


  /**
   * 格式化直播诊断能力数据为Markdown
   * @param {Object} diagnoseData - 直播诊断能力数据
   * @returns {string} Markdown格式的报告
   */
  function formatLiveDiagnoseCapabilityAsMarkdown(diagnoseData) {
    if (!diagnoseData) return '';

    let markdown = '# 直播诊断能力数据\n\n';

    // 处理整场用户画像数据
    if (diagnoseData.content && diagnoseData.content.item) {
      markdown += '## 整场用户画像\n\n';

      // 根据指标类型分组显示
      const contentMetrics = diagnoseData.content.item;

      // 查找特定指标
      const effectiveEnterRate = contentMetrics.find(item => item.indicator === '直播有效进房率');
      const avgWatchTime = contentMetrics.find(item => item.indicator === '人均观看时长');
      const likeRate = contentMetrics.find(item => item.indicator === '点赞率');
      const commentRate = contentMetrics.find(item => item.indicator === '评论率');

      // 显示关键指标
      if (effectiveEnterRate || avgWatchTime || likeRate || commentRate) {
        markdown += '### 内容力\n\n';

        if (effectiveEnterRate) {
          const rate = (parseFloat(effectiveEnterRate.val) * 100).toFixed(2);
          const change = (parseFloat(effectiveEnterRate.median7d) * 100).toFixed(2);
          const industry = effectiveEnterRate.withinIndustry ?
            `高于${(parseFloat(effectiveEnterRate.withinIndustry) * 100).toFixed(0)}%的当日同行` : '';

          markdown += `**直播有效进房率**\n\n`;
          markdown += `${rate}%\n\n`;
          markdown += `较近7天中位数 ${change > 0 ? '+' : ''}${change}%\n`;
          if (industry) markdown += `${industry}\n`;
          markdown += '\n';
        }

        if (avgWatchTime) {
          const timeInSeconds = parseFloat(avgWatchTime.val);
          const minutes = Math.floor(timeInSeconds / 60);
          const seconds = Math.floor(timeInSeconds % 60);
          const change = (parseFloat(avgWatchTime.median7d) * 100).toFixed(2);
          const industry = avgWatchTime.withinIndustry ?
            `高于${(parseFloat(avgWatchTime.withinIndustry) * 100).toFixed(0)}%的当日同行` : '';

          markdown += `**人均观看时长**\n\n`;
          markdown += `${minutes}分${seconds}秒\n\n`;
          markdown += `较近7天中位数 ${change > 0 ? '+' : ''}${change}%\n`;
          if (industry) markdown += `${industry}\n`;
          markdown += '\n';
        }

        if (commentRate) {
          const rate = (parseFloat(commentRate.val) * 100).toFixed(2);
          const change = (parseFloat(commentRate.median7d) * 100).toFixed(2);
          const industry = commentRate.withinIndustry ?
            `高于${(parseFloat(commentRate.withinIndustry) * 100).toFixed(0)}%的当日同行` : '';

          markdown += `**评论率**\n\n`;
          markdown += `${rate}%\n\n`;
          markdown += `较近7天中位数 ${change > 0 ? '+' : ''}${change}%\n`;
          if (industry) markdown += `${industry}\n`;
          markdown += '\n';
        }

        if (likeRate) {
          const rate = (parseFloat(likeRate.val) * 100).toFixed(2);
          const change = (parseFloat(likeRate.median7d) * 100).toFixed(2);
          const industry = likeRate.withinIndustry ?
            `高于${(parseFloat(likeRate.withinIndustry) * 100).toFixed(0)}%的当日同行` : '';

          markdown += `**点赞率**\n\n`;
          markdown += `${rate}%\n\n`;
          markdown += `较近7天中位数 ${change > 0 ? '+' : ''}${change}%\n`;
          if (industry) markdown += `${industry}\n`;
          markdown += '\n';
        }
      }
    }

    // 处理转化力数据
    if (diagnoseData.conversion && diagnoseData.conversion.item) {
      markdown += '## 转化力\n\n';

      const conversionMetrics = diagnoseData.conversion.item;

      // 查找特定指标
      const thousandViewAmount = conversionMetrics.find(item => item.indicator === '千次观看成交金额');
      const transactionRate = conversionMetrics.find(item => item.indicator === '成交转化率');
      const newCustomerRate = conversionMetrics.find(item => item.indicator === '新客转化率');
      const bubbleClickRate = conversionMetrics.find(item => item.indicator === '商品气泡点击率');
      const bagClickRate = conversionMetrics.find(item => item.indicator === '购物袋点击率');
      const bagProductClickRate = conversionMetrics.find(item => item.indicator === '购物袋商品点击率');

      if (thousandViewAmount) {
        const amount = parseFloat(thousandViewAmount.val).toFixed(2);
        const change = (parseFloat(thousandViewAmount.median7d) * 100).toFixed(2);

        markdown += `**千次观看成交金额**\n\n`;
        markdown += `¥${amount}\n\n`;
        markdown += `较近7天中位数 ${change > 0 ? '+' : ''}${change}%\n`;
        markdown += '\n';
      }

      if (transactionRate) {
        const rate = (parseFloat(transactionRate.val) * 100).toFixed(2);
        const change = (parseFloat(transactionRate.median7d) * 100).toFixed(2);

        markdown += `**成交转化率(次数)**\n\n`;
        markdown += `${rate}%\n\n`;
        markdown += `较近7天中位数 ${change > 0 ? '+' : ''}${change}%\n`;
        markdown += '\n';
      }

      if (newCustomerRate) {
        const rate = (parseFloat(newCustomerRate.val) * 100).toFixed(2);
        const change = (parseFloat(newCustomerRate.median7d) * 100).toFixed(2);

        markdown += `**新客转化率(次数)**\n\n`;
        markdown += `${rate}%\n\n`;
        markdown += `较近7天中位数 ${change > 0 ? '+' : ''}${change}%\n`;
        markdown += '\n';
      }

      if (bubbleClickRate) {
        const rate = (parseFloat(bubbleClickRate.val) * 100).toFixed(2);
        const change = (parseFloat(bubbleClickRate.median7d) * 100).toFixed(2);

        markdown += `**商品气泡点击率(次数)**\n\n`;
        markdown += `${rate}%\n\n`;
        markdown += `较近7天中位数 ${change > 0 ? '+' : ''}${change}%\n`;
        markdown += '\n';
      }

      if (bagClickRate) {
        const rate = (parseFloat(bagClickRate.val) * 100).toFixed(2);
        const change = (parseFloat(bagClickRate.median7d) * 100).toFixed(2);

        markdown += `**购物袋点击率(次数)**\n\n`;
        markdown += `${rate}%\n\n`;
        markdown += `较近7天中位数 ${change > 0 ? '+' : ''}${change}%\n`;
        markdown += '\n';
      }

      if (bagProductClickRate) {
        const rate = (parseFloat(bagProductClickRate.val) * 100).toFixed(2);
        const change = (parseFloat(bagProductClickRate.median7d) * 100).toFixed(2);

        markdown += `**购物袋商品点击率(次数)**\n\n`;
        markdown += `${rate}%\n\n`;
        markdown += `较近7天中位数 ${change > 0 ? '+' : ''}${change}%\n`;
        markdown += '\n';
      }
    }

    // 添加详细数据表格
    markdown += '## 详细数据\n\n';

    if (diagnoseData.content && diagnoseData.content.item) {
      markdown += '### 内容力指标\n\n';
      markdown += '| 指标名称 | 当前值 | 7天中位数变化 | 行业排名 |\n';
      markdown += '|---------|--------|---------------|----------|\n';

      diagnoseData.content.item.forEach(item => {
        const currentVal = item.valType === 8 ?
          `${(parseFloat(item.val) * 100).toFixed(2)}%` :
          item.valType === 10 ? formatDuration(parseFloat(item.val)) :
          item.val;

        const change = `${parseFloat(item.median7d) > 0 ? '+' : ''}${(parseFloat(item.median7d) * 100).toFixed(2)}%`;

        const industry = item.withinIndustry ?
          `高于${(parseFloat(item.withinIndustry) * 100).toFixed(0)}%同行` : '-';

        markdown += `| ${item.indicator} | ${currentVal} | ${change} | ${industry} |\n`;
      });

      markdown += '\n';
    }

    if (diagnoseData.conversion && diagnoseData.conversion.item) {
      markdown += '### 转化力指标\n\n';
      markdown += '| 指标名称 | 当前值 | 7天中位数变化 |\n';
      markdown += '|---------|--------|---------------|\n';

      diagnoseData.conversion.item.forEach(item => {
        const currentVal = item.valType === 8 ?
          `${(parseFloat(item.val) * 100).toFixed(2)}%` :
          item.valType === 7 ? `¥${parseFloat(item.val).toFixed(2)}` :
          item.val;

        const change = `${parseFloat(item.median7d) > 0 ? '+' : ''}${(parseFloat(item.median7d) * 100).toFixed(2)}%`;

        markdown += `| ${item.indicator} | ${currentVal} | ${change} |\n`;
      });

      markdown += '\n';
    }

    return markdown;
  }

  // ==================== 表格增强功能 ====================

  // 表格状态管理
  const tableState = {
    observer: null,
    rowData: new Map(), // 存储每行的状态
    isInitialized: false
  };

  function getWujieTable() {
    // 查找wujie-app元素
    const wujieApp = document.querySelector('wujie-app');
    if (!wujieApp) {
      console.log('❌ 未找到wujie-app元素');
      return null;
    }

    console.log('✅ 找到wujie-app元素');

    // 尝试获取wujie的shadow DOM或iframe内容
    let wujieDocument = null;

    // 方法1: 检查shadow DOM
    if (wujieApp.shadowRoot) {
      console.log('📍 使用shadow DOM方式访问');
      wujieDocument = wujieApp.shadowRoot;
    }
    // 方法2: 检查iframe
    else {
      const iframe = wujieApp.querySelector('iframe');
      if (iframe && iframe.contentDocument) {
        console.log('📍 使用iframe方式访问');
        wujieDocument = iframe.contentDocument;
      }
    }

    // 方法3: 直接在wujie-app内部查找
    if (!wujieDocument) {
      console.log('📍 直接在wujie-app内部查找');
      wujieDocument = wujieApp;
    }

    if (!wujieDocument) {
      console.log('❌ 无法访问wujie-app内部文档');
      return null;
    }

    // 在wujie内部查找表格
    const tableSelectors = [
      '.common-table-wrap .ant-table-fixed-left table',
      '.ant-table-fixed',
      '.ant-table-content table',
      '.ecom-table',
      'table.ant-table',
      'table'
    ];

    for (const selector of tableSelectors) {
      const table = wujieDocument.querySelector(selector);
      if (table) {
        console.log(`✅ 在wujie-app内找到表格: ${selector}`);
        return table;
      }
    }

    console.log('❌ 在wujie-app内未找到表格');
    return null;
  }

  /**
   * 初始化表格增强功能
   */
  function initTableEnhancement() {
    if (tableState.isInitialized) {
      console.log('⏭️ 表格增强已初始化，跳过');
      return;
    }

    console.log('🚀 开始初始化表格增强功能...');

    // 查找表格
    const table = getWujieTable();
    if (!table) {
      console.log('❌ 未找到表格，稍后重试...');
      setTimeout(initTableEnhancement, 2000);
      return;
    }

    console.log('✅ 找到表格，开始增强...');

    // 添加表头
    const thead = table.querySelector('thead');
    if (thead) {
      addTableHeaders(thead);
    }

    // 增强现有行
    enhanceExistingRows(table);

    // 添加上传按钮
    addUploadButton();

    // 设置监控
    setupTableMonitoring(table);

    tableState.isInitialized = true;
    console.log('✅ 表格增强初始化完成');
  }

  /**
   * 添加表头
   */
  function addTableHeaders(thead) {
    if (thead.querySelector('.custom-checkboxes')) {
      console.log('⏭️ 表头已添加，跳过');
      return;
    }

    const headerRow = thead.querySelector('tr');
    if (!headerRow) return;

    const firstHeader = headerRow.querySelector('th');
    if (!firstHeader) return;

    // 在第一列表头中添加checkbox控制区域
    const checkboxContainer = document.createElement('div');
    checkboxContainer.className = 'custom-checkboxes';
    checkboxContainer.style.cssText = `
      display: flex;
      gap: 4px;
    `;

    checkboxContainer.innerHTML = `
      <div style="display: flex; align-items: center; gap: 4px; font-size: 12px;">
        <input type="checkbox" id="select-all-upload" style="margin: 0;">
        <label for="select-all-upload" style="margin: 0;">上传数据</label>
      </div>
      <div style="display: flex; align-items: center; gap: 4px; font-size: 12px;">
        <input type="checkbox" id="select-all-video" style="margin: 0;">
        <label for="select-all-video" style="margin: 0;">下载视频</label>
      </div>
    `;

    // 将checkbox容器插入到第一列表头的开头
    firstHeader.insertBefore(checkboxContainer, firstHeader.firstChild);

    // 设置第一列表头为flex布局
    firstHeader.style.display = 'flex';
    firstHeader.style.gap = '4px';

    // 添加全选事件
    setupSelectAllEvents();

    console.log('✅ 表头checkbox添加成功');
  }

  /**
   * 设置全选事件
   */
  function setupSelectAllEvents() {
    // 获取wujie文档上下文
    const wujieApp = document.querySelector('wujie-app');
    let wujieDocument = null;

    if (wujieApp?.shadowRoot) {
      wujieDocument = wujieApp.shadowRoot;
    } else if (wujieApp?.querySelector('iframe')?.contentDocument) {
      wujieDocument = wujieApp.querySelector('iframe').contentDocument;
    } else if (wujieApp) {
      wujieDocument = wujieApp;
    } else {
      wujieDocument = document;
    }

    const selectAllUpload = wujieDocument.getElementById('select-all-upload');
    const selectAllVideo = wujieDocument.getElementById('select-all-video');

    if (selectAllUpload) {
      selectAllUpload.addEventListener('change', (e) => {
        const checked = e.target.checked;
        console.log(`📝 全选上传数据: ${checked}, 当前行数: ${tableState.rowData.size}`);

        tableState.rowData.forEach((rowData, rowKey) => {
          rowData.uploadData = checked;

          const checkbox = wujieDocument.querySelector(`.upload-checkbox[data-row-key="${rowKey}"]`);
          if (checkbox) {
            checkbox.checked = checked;
            console.log(`✅ 更新行 ${rowKey} 上传checkbox: ${checked}`);
          } else {
            console.log(`❌ 未找到行 ${rowKey} 的上传checkbox`);
          }
        });
      });
    } else {
      console.log('❌ 未找到全选上传checkbox');
    }

    if (selectAllVideo) {
      selectAllVideo.addEventListener('change', (e) => {
        const checked = e.target.checked;
        console.log(`🎥 全选下载视频: ${checked}, 当前行数: ${tableState.rowData.size}`);

        tableState.rowData.forEach((rowData, rowKey) => {
          rowData.downloadVideo = checked;

          const checkbox = wujieDocument.querySelector(`.video-checkbox[data-row-key="${rowKey}"]`);
          if (checkbox) {
            checkbox.checked = checked;
            console.log(`✅ 更新行 ${rowKey} 视频checkbox: ${checked}`);
          } else {
            console.log(`❌ 未找到行 ${rowKey} 的视频checkbox`);
          }
        });
      });
    } else {
      console.log('❌ 未找到全选视频checkbox');
    }
  }

  /**
   * 增强现有行
   */
  function enhanceExistingRows(table) {
    const tbody = table.querySelector('tbody');
    if (!tbody) return;

    const dataRows = tbody.querySelectorAll('tr[data-row-key]');
    console.log(`📊 发现 ${dataRows.length} 行数据`);

    dataRows.forEach(row => {
      const rowKey = row.getAttribute('data-row-key');
      if (rowKey) {
        addCheckboxesToFirstCell(row, rowKey);
        if (!tableState.rowData.has(rowKey)) {
          tableState.rowData.set(rowKey, {
            uploadData: false,
            downloadVideo: false,
            rowElement: row
          });
        }
      }
    });

    console.log(`📊 表格增强完成，处理了 ${dataRows.length} 行数据`);
  }

  /**
   * 在第一列中添加checkbox
   */
  function addCheckboxesToFirstCell(row, rowKey) {
    // 检查是否已经添加过
    if (row.querySelector('.custom-row-checkboxes')) {
      return;
    }

    const firstCell = row.querySelector('td');
    if (!firstCell) return;

    // 创建checkbox容器
    const checkboxContainer = document.createElement('div');
    checkboxContainer.className = 'custom-row-checkboxes';
    checkboxContainer.style.cssText = `
      display: flex;
      flex-direction: column;
      gap: 2px;
      margin-bottom: 4px;
      padding: 2px;
      border-bottom: 1px solid #f5f5f5;
    `;

    checkboxContainer.innerHTML = `
      <div style="display: flex; align-items: center; gap: 4px; font-size: 11px;">
        <input type="checkbox" class="upload-checkbox" data-row-key="${rowKey}" style="margin: 0; transform: scale(0.8);">
        <span style="color: #666;">上传</span>
      </div>
      <div style="display: flex; align-items: center; gap: 4px; font-size: 11px;">
        <input type="checkbox" class="video-checkbox" data-row-key="${rowKey}" style="margin: 0; transform: scale(0.8);">
        <span style="color: #666;">视频</span>
      </div>
    `;

    // 将checkbox容器插入到第一列的开头
    firstCell.insertBefore(checkboxContainer, firstCell.firstChild);

    firstCell.style.display = 'flex';
    firstCell.style.gap = '4px';

    // 添加事件监听
    setupRowCheckboxEvents(checkboxContainer, rowKey);

    console.log(`✅ 为行 ${rowKey} 添加checkbox成功`);
  }

  /**
   * 设置行checkbox事件
   */
  function setupRowCheckboxEvents(container, rowKey) {
    const uploadCheckbox = container.querySelector('.upload-checkbox');
    const videoCheckbox = container.querySelector('.video-checkbox');

    uploadCheckbox.addEventListener('change', (e) => {
      const rowData = tableState.rowData.get(rowKey);
      if (rowData) {
        rowData.uploadData = e.target.checked;
        console.log(`📝 行 ${rowKey} 上传数据状态: ${e.target.checked}`);
        updateSelectAllState('upload');
      }
    });

    videoCheckbox.addEventListener('change', (e) => {
      const rowData = tableState.rowData.get(rowKey);
      if (rowData) {
        rowData.downloadVideo = e.target.checked;
        console.log(`🎥 行 ${rowKey} 下载视频状态: ${e.target.checked}`);
        updateSelectAllState('video');
      }
    });
  }

  /**
   * 更新全选状态
   */
  function updateSelectAllState(type) {
    const selectAllId = type === 'upload' ? 'select-all-upload' : 'select-all-video';
    const selectAllCheckbox = document.getElementById(selectAllId);

    if (!selectAllCheckbox) return;

    const property = type === 'upload' ? 'uploadData' : 'downloadVideo';
    const allRows = Array.from(tableState.rowData.values());
    const checkedCount = allRows.filter(data => data[property]).length;

    if (checkedCount === 0) {
      selectAllCheckbox.checked = false;
      selectAllCheckbox.indeterminate = false;
    } else if (checkedCount === allRows.length) {
      selectAllCheckbox.checked = true;
      selectAllCheckbox.indeterminate = false;
    } else {
      selectAllCheckbox.checked = false;
      selectAllCheckbox.indeterminate = true;
    }
  }

  /**
   * 获取选中的行数据
   */
  function getSelectedRows() {
    const uploadData = [];
    const downloadVideo = [];

    tableState.rowData.forEach((rowData, rowKey) => {
      if (rowData.uploadData) {
        uploadData.push(rowKey);
      }
      if (rowData.downloadVideo) {
        downloadVideo.push(rowKey);
      }
    });

    return { uploadData, downloadVideo };
  }

  /**
   * 创建上传按钮
   */
  function createUploadButton() {
    const uploadBtn = document.createElement('button');
    uploadBtn.id = 'channels-upload-btn';
    uploadBtn.textContent = '批量上传数据';
    uploadBtn.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      padding: 12px 24px;
      background: #1890ff;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
      transition: all 0.3s ease;
    `;

    // 添加悬停效果
    uploadBtn.addEventListener('mouseenter', () => {
      uploadBtn.style.background = '#40a9ff';
      uploadBtn.style.transform = 'translateY(-1px)';
      uploadBtn.style.boxShadow = '0 4px 12px rgba(24, 144, 255, 0.4)';
    });

    uploadBtn.addEventListener('mouseleave', () => {
      uploadBtn.style.background = '#1890ff';
      uploadBtn.style.transform = 'translateY(0)';
      uploadBtn.style.boxShadow = '0 2px 8px rgba(24, 144, 255, 0.3)';
    });

    // 添加点击事件
    uploadBtn.addEventListener('click', handleBatchUpload);

    return uploadBtn;
  }

  /**
   * 添加上传按钮
   */
  function addUploadButton() {
    // 检查是否已存在
    if (document.getElementById('channels-upload-btn')) {
      return;
    }

    const uploadBtn = createUploadButton();
    document.body.appendChild(uploadBtn);
    console.log('✅ 上传按钮添加成功');
  }

  /**
   * 处理批量上传
   */
  async function handleBatchUpload() {
    const uploadBtn = document.getElementById('channels-upload-btn');

    try {
      uploadBtn.disabled = true;
      uploadBtn.textContent = '处理中...';

      // 获取选中的行数据
      const selectedRows = getSelectedRows();
      const uploadRows = selectedRows.uploadData;
      const videoRows = selectedRows.downloadVideo;

      if (uploadRows.length === 0) {
        showProgress('❌ 请先选择要上传的直播数据', 'error');
        return;
      }

      console.log(`📊 开始批量处理 ${uploadRows.length} 个直播间的数据`);
      showProgress(`🔄 开始批量处理 ${uploadRows.length} 个直播间...`, 'info');

      let successCount = 0;
      let failCount = 0;

      // 逐个处理选中的直播间
      for (let i = 0; i < uploadRows.length; i++) {
        const liveObjectId = uploadRows[i];
        const shouldDownloadVideo = videoRows.includes(liveObjectId);

        try {
          showProgress(`🔄 处理第 ${i + 1}/${uploadRows.length} 个直播间: ${liveObjectId}`, 'info');
          console.log(`📝 开始处理直播间 ${liveObjectId}，下载视频: ${shouldDownloadVideo}`);

          // 提取单个直播间的数据
          const data = await extractSingleChannelLiveData(liveObjectId);
          if (!data) {
            throw new Error('数据提取失败');
          }

          const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
          const filename = `channels-live-review-${liveObjectId}-${timestamp}.md`;

          // 格式化为Markdown
          const markdownContent = formatDataAsMarkdown({extractedData:{apiData:data}});

          // 上传到OSS
          const fileUrl = await uploadToOSS(markdownContent, filename);
          console.log(`✅ 直播间 ${liveObjectId} 数据上传成功: ${fileUrl}`);

          // 获取直播回放链接（仅当勾选下载视频时）
          let liveReplayUrl = '';
          if (shouldDownloadVideo) {
            try {
              const videoUrl = await getLiveReplayVideoUrl(liveObjectId, timestamp);
              liveReplayUrl = videoUrl;
              console.log(`📹 直播间 ${liveObjectId} 回放链接: ${liveReplayUrl}`);
            } catch (error) {
              console.warn(`⚠️ 获取直播间 ${liveObjectId} 回放链接失败:`, error);
            }
          }

          // 记录直播数据
          await recordLiveStreamData(
            liveObjectId,
            data.ecConversionDashboard.anchorNickname, // 需要根据实际情况获取
            data.ecConversionDashboard.liveDescription, // 需要根据实际情况获取
            formatTimestamp(data.ecConversionDashboard.createTime),
            [fileUrl],
            liveReplayUrl
          );

          successCount++;
          console.log(`✅ 直播间 ${liveObjectId} 处理完成 (${i + 1}/${uploadRows.length})`);

        } catch (error) {
          failCount++;
          console.error(`❌ 处理直播间 ${liveObjectId} 失败:`, error);
        }

        // 添加延迟避免请求过于频繁
        if (i < uploadRows.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // 显示最终结果
      const resultMessage = `✅ 批量处理完成！成功: ${successCount}，失败: ${failCount}`;
      showProgress(resultMessage, successCount > 0 ? 'success' : 'error');
      console.log(resultMessage);

      setTimeout(() => hideProgress(), 5000);

    } catch (error) {
      console.error('❌ 批量处理过程中发生错误:', error);
      showProgress('❌ 批量处理失败，请重试', 'error');
    } finally {
      uploadBtn.disabled = false;
      uploadBtn.textContent = '批量上传数据';
    }
  }

  /**
   * 提取单个直播间的完整数据
   */
  async function extractSingleChannelLiveData(liveObjectId) {
    try {
      const timestamp = `${new Date().valueOf()}`;

      // 获取各种数据
      const originalData = await getEcConversionDashboardData(liveObjectId, timestamp);
      const v3Data = await getEcConversionDashboardDataV3(liveObjectId, timestamp);
      const diagnoseData = await getLiveDiagnoseCapability(liveObjectId, timestamp);
      const spuData = await getSingleLiveEcSpuData(liveObjectId, timestamp);
      const fullPortraitData = await getFullLiveUserPortraitData(liveObjectId, timestamp);
      const ecConversionEcData = await getEcConversionDashboardEcData(liveObjectId, timestamp);

      return {
        ecConversionDashboard: originalData,
        ecConversionDashboardV3: v3Data,
        liveDiagnoseCapability: diagnoseData,
        singleLiveEcSpuData: spuData,
        fullLiveUserPortrait: fullPortraitData,
        ecConversionDashboardEcData: ecConversionEcData
      };

    } catch (error) {
      console.error(`❌ 提取直播间 ${liveObjectId} 数据失败:`, error);
      throw error;
    }
  }

    /**
   * 获取电商转化仪表板电商数据
   * @param {string} liveObjectId - 直播对象ID
   * @param {string} timestamp - 时间戳
   * @param {Object} options - 可选参数
   * @returns {Promise<Object>} 电商转化仪表板电商数据
   */
    async function getEcConversionDashboardEcData(liveObjectId, timestamp, options = {}) {
      if (!liveObjectId) {
        throw new Error('直播对象ID不能为空');
      }

      try {
        const requestBody = {
          liveObjectId,
          timestamp,
          rawKeyBuff: options.rawKeyBuff || null,
          pluginSessionId: options.pluginSessionId || null,
          scene: options.scene || 7,
          reqScene: options.reqScene || 7
        };

        const response = await callAPI('micro/statistic/cgi-bin/mmfinderassistant-bin/statistic/get_ec_conversion_dashboard_ec_data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        });

        if (response.errCode !== 0) {
          throw new Error(`API返回错误: ${response.errMsg || '未知错误'}`);
        }

        return response.data;
      } catch (error) {
        console.error('❌ 获取电商转化仪表板电商数据失败:', error);
        throw error;
      }
    }

    /**
     * 格式化电商转化仪表板电商数据为Markdown
     * @param {Object} ecData - 电商转化仪表板电商数据
     * @returns {string} Markdown格式的电商数据报告
     */
    function formatEcConversionDashboardEcDataAsMarkdown(ecData) {
      if (!ecData) return '';

      let markdown = '# 电商转化趋势数据分析\n\n';

      const data = ecData;

      // 处理整体电商趋势数据
      if (data.ecTread) {
        markdown += '## 整体电商趋势\n\n';
        markdown += formatEcTrendSection(data.ecTread, '整体');
      }

      // 处理粉丝电商趋势数据
      if (data.followerEcTrend) {
        markdown += '## 粉丝电商趋势\n\n';
        markdown += formatEcTrendSection(data.followerEcTrend, '粉丝');
      }

      // 处理新买家电商趋势数据
      if (data.newBuyerEcTrend) {
        markdown += '## 新买家电商趋势\n\n';
        markdown += formatEcTrendSection(data.newBuyerEcTrend, '新买家');
      }

      // 处理非粉丝电商趋势数据
      if (data.nonFollowerEcTrend) {
        markdown += '## 非粉丝电商趋势\n\n';
        markdown += formatEcTrendSection(data.nonFollowerEcTrend, '非粉丝');
      }

      // 处理老买家电商趋势数据
      if (data.oldBuyerEcTrend) {
        markdown += '## 老买家电商趋势\n\n';
        markdown += formatEcTrendSection(data.oldBuyerEcTrend, '老买家');
      }

      return markdown;
    }

    /**
     * 格式化电商趋势数据段落
     * @param {Object} trendData - 趋势数据对象
     * @param {string} category - 分类名称
     * @returns {string} 格式化的Markdown段落
     */
    function formatEcTrendSection(trendData, category) {
      let markdown = '';

      // 处理支付PV趋势
      if (trendData.payPvTread && trendData.payPvTread.length > 0) {
        markdown += `### ${category}支付次数趋势\n\n`;
        markdown += formatTrendDataTable(trendData.payPvTread, '支付次数');
      }

      // 处理支付UV趋势
      if (trendData.payUvTread && trendData.payUvTread.length > 0) {
        markdown += `### ${category}支付人数趋势\n\n`;
        markdown += formatTrendDataTable(trendData.payUvTread, '支付人数');
      }

      // 处理支付金额趋势
      if (trendData.payAmountTread && trendData.payAmountTread.length > 0) {
        markdown += `### ${category}支付金额趋势\n\n`;
        markdown += formatTrendDataTable(trendData.payAmountTread, '支付金额', true);
      }

      return markdown;
    }

    /**
     * 格式化趋势数据表格
     * @param {Array} trendArray - 趋势数据数组
     * @param {string} metricName - 指标名称
     * @param {boolean} isAmount - 是否为金额数据
     * @returns {string} 格式化的表格
     */
    function formatTrendDataTable(trendArray, metricName, isAmount = false) {
      let markdown = '';

      // 按时间间隔分组显示
      const stepNames = {
        '60': '1分钟',
        '300': '5分钟',
        '1800': '30分钟',
        '3600': '1小时',
        '10800': '3小时',
        '21600': '6小时',
        '86400': '1天'
      };

      trendArray.forEach((trendItem, index) => {
        const stepName = stepNames[trendItem.step] || `${trendItem.step}秒`;
        const beginTime = formatTimestamp(parseInt(trendItem.beginTs));
        const endTime = formatTimestamp(parseInt(trendItem.endTs));

        markdown += `#### ${stepName}粒度数据\n\n`;
        markdown += `**时间范围**: ${beginTime} - ${endTime}\n\n`;

        if (trendItem.data && trendItem.data.length > 0) {
          // 只显示有数值的数据点
          const validData = trendItem.data.filter(point =>
            point.value && point.value !== '0'
          );

          if (validData.length > 0) {
            markdown += '| 时间 | 数值 |\n';
            markdown += '|------|------|\n';

            validData.forEach(dataPoint => {
              const time = formatTimestamp(dataPoint.ts);
              let value = dataPoint.value;

              // 如果是金额数据，转换为元并添加货币符号
              if (isAmount && value !== '0') {
                value = `¥${(parseFloat(value) / 100).toFixed(2)}`;
              } else {
                value = parseInt(value).toLocaleString();
              }

              markdown += `| ${time} | ${value} |\n`;
            });
          } else {
            markdown += '该时间段内无有效数据\n';
          }
        } else {
          markdown += '暂无数据\n';
        }

        markdown += '\n';
      });

      return markdown;
    }

  /**
   * 上传Markdown文件到阿里云OSS
   */
  async function uploadToOSS(markdownContent, filename) {
    try {
      showProgress('📤 正在上传到OSS...', 'info');

      const blob = new Blob([markdownContent], { type: 'text/markdown;charset=utf-8' });
      const { policy, signature } = await getCDNSignature("6OSB76zlj8l2Hk7hhrnP8QUyUn6amP");

      const formData = new FormData();
      formData.append('OSSAccessKeyId', 'LTAIM23ZKZHBJoDn');
      formData.append('policy', policy);
      formData.append('signature', signature);
      formData.append('key', `upload/${filename}`);
      formData.append('success_action_status', '200');
      formData.append('file', blob);

      return new Promise((resolve, reject) => {
        // 使用XMLHttpRequest上传，以便跟踪进度
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'https://video-clip.oss-cn-shanghai.aliyuncs.com', true);

        // 跟踪上传进度
        xhr.upload.onprogress = function(e) {
          if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            showProgress(`📤 上传中... ${Math.round(percentComplete)}%`, 'info');
          }
        };

        // 处理上传完成
        xhr.onload = function() {
          if (xhr.status === 200) {
            const fileUrl = `https://video-clip.oss-cn-shanghai.aliyuncs.com/upload/${filename}`;
            showProgress('✅ OSS上传成功', 'success');
            console.log('✅ OSS上传成功:', fileUrl);
            resolve(fileUrl);
          } else {
            const errorMsg = `上传失败: ${xhr.status} ${xhr.statusText}`;
            showProgress(`❌ ${errorMsg}`, 'error');
            console.error('❌ OSS上传失败:', errorMsg);
            reject(new Error(errorMsg));
          }
        };

        // 处理上传错误
        xhr.onerror = function() {
          const errorMsg = '上传失败: 网络错误';
          showProgress(`❌ ${errorMsg}`, 'error');
          console.error('❌ OSS上传失败:', errorMsg);
          reject(new Error(errorMsg));
        };

        // 处理上传超时
        xhr.ontimeout = function() {
          const errorMsg = '上传失败: 请求超时';
          showProgress(`❌ ${errorMsg}`, 'error');
          console.error('❌ OSS上传失败:', errorMsg);
          reject(new Error(errorMsg));
        };

        // 设置超时时间（30秒）
        xhr.timeout = 30000;

        // 发送请求
        xhr.send(formData);
      });

    } catch (error) {
      console.error('❌ OSS上传失败:', error);
      showProgress('❌ OSS上传失败', 'error');
      throw error;
    }
  }

  /**
   * 获取直播回放视频链接
   * @param {string} liveObjectId - 直播对象ID
   * @param {string} timestamp - 时间戳
   * @param {Object} options - 其他可选参数
   * @returns {Promise<string>} 回放视频MP4链接
   */
  async function getLiveReplayVideoUrl(liveObjectId, timestamp, options = {}) {
    if (!liveObjectId) {
      throw new Error('直播对象ID不能为空');
    }

    if (!timestamp) {
      throw new Error('时间戳不能为空');
    }

    try {
      console.log('🎥 正在获取直播回放视频链接...');
      const requestBody = {
        objectId: liveObjectId,
        getReplayStreamContext: {
          mode: options.mode || 3,
          scene: options.contextScene || 3,
          videoCodecType: options.videoCodecType || 1,
          useNewLogic: options.useNewLogic || true
        },
        timestamp,
        rawKeyBuff: options.rawKeyBuff || null,
        pluginSessionId: options.pluginSessionId || null,
        scene: options.scene || 7,
        reqScene: options.reqScene || 7
      };

      const response = await callAPI('micro/live/cgi-bin/mmfinderassistant-bin/live/get_live_replay_wonderful_fragment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (response.errCode !== 0) {
        throw new Error(`API返回错误: ${response.errMsg || '未知错误'}`);
      }

      const mp4Url = response.data?.replayStreamUrlInfo?.mp4Url;
      if (!mp4Url) {
        throw new Error('未找到MP4回放链接');
      }

      console.log('✅ 获取直播回放视频链接成功:', mp4Url);
      return mp4Url;

    } catch (error) {
      console.error(`❌ 获取直播回放视频链接失败 [${liveObjectId}]:`, error);
      throw error;
    }
  }

  /**
   * 记录直播流数据
   */
  async function recordLiveStreamData(liveId, anchorName, liveTitle, liveTime, liveDataUrls, liveReplayUrl) {
    try {
      const url = 'https://aiapi.bzy.ai/v2/anon/chatbot/live-stream-records';

      const payload = {
        live_id: liveId,
        anchor_name: anchorName,
        live_title: liveTitle,
        live_time: liveTime,
        live_data_urls: liveDataUrls,
        live_replay_url: liveReplayUrl,
        live_replay_audio_url: '',
        platform: 'channels'
      };

      console.log('📝 记录直播数据请求:', payload);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ 记录直播数据成功:', data);
      return data;
    } catch (error) {
      console.error('❌ 记录直播数据失败:', error);
      throw error;
    }
  }

  const SIGNATURE_EXPIRE_TIME = 2 * 60 * 1000; // 2分钟

// 生成GMT ISO8601格式的时间字符串
function formatGMTISO8601(date) {
  const pad = (n) => (n < 10 ? '0' + n : n);
  return (
    `${date.getUTCFullYear()}-${pad(date.getUTCMonth() + 1)}-${pad(date.getUTCDate())}` +
    `T${pad(date.getUTCHours())}:${pad(date.getUTCMinutes())}:${pad(date.getUTCSeconds())}Z`
  );
}

// 生成Policy配置
function generatePolicyConfig() {
  const expireEnd = new Date(Date.now() + SIGNATURE_EXPIRE_TIME);
  const expiration = formatGMTISO8601(expireEnd);

  const conditions = [
    ["starts-with", "$key", ""],
    ["content-length-range", 0, 1024 * 1024 * 1024 * 5], // 5GB
  ];

  return {
    expiration,
    conditions,
  };
}

// 使用 Web Crypto API 计算 HMAC-SHA1
async function hmacSha1(key, message) {
  const encoder = new TextEncoder();
  const cryptoKey = await window.crypto.subtle.importKey(
    "raw",
    encoder.encode(key),
    { name: "HMAC", hash: "SHA-1" },
    false,
    ["sign"]
  );

  const signature = await window.crypto.subtle.sign(
    "HMAC",
    cryptoKey,
    encoder.encode(message)
  );

  return btoa(String.fromCharCode(...new Uint8Array(signature)));
}

  /**
   * 获取CDN签名
   */
  async function getCDNSignature(accessKeySecret) {
    try {
      const config = generatePolicyConfig();
      const policy = btoa(JSON.stringify(config));

      const signature = await hmacSha1(accessKeySecret, policy);

      return {
        policy,
        signature,
        error: null,
      };
    } catch (err) {
      return {
        policy: null,
        signature: null,
        error: err,
      };
    }
  }

  /**
   * 显示进度信息
   */
  function showProgress(message, type = 'info') {
    let progressEl = document.getElementById('channels-progress-indicator');

    if (!progressEl) {
      progressEl = document.createElement('div');
      progressEl.id = 'channels-progress-indicator';
      progressEl.style.cssText = `
        position: fixed;
        top: 80px;
        right: 20px;
        z-index: 10000;
        padding: 12px 20px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        max-width: 300px;
        word-wrap: break-word;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
      `;
      document.body.appendChild(progressEl);
    }

    // 设置样式
    const styles = {
      info: { background: '#e6f7ff', color: '#1890ff', border: '1px solid #91d5ff' },
      success: { background: '#f6ffed', color: '#52c41a', border: '1px solid #b7eb8f' },
      error: { background: '#fff2f0', color: '#ff4d4f', border: '1px solid #ffccc7' },
      warning: { background: '#fffbe6', color: '#faad14', border: '1px solid #ffe58f' }
    };

    const style = styles[type] || styles.info;
    Object.assign(progressEl.style, style);
    progressEl.textContent = message;
    progressEl.style.display = 'block';
  }

  /**
   * 隐藏进度信息
   */
  function hideProgress() {
    const progressEl = document.getElementById('channels-progress-indicator');
    if (progressEl) {
      progressEl.style.display = 'none';
    }
  }

  /**
   * 设置表格监控
   */
  function setupTableMonitoring(table) {
    if (tableState.observer) {
      tableState.observer.disconnect();
    }

    console.log('👀 启动表格数据监控...');

    tableState.observer = new MutationObserver((mutations) => {
      let shouldUpdateCheckboxes = false;
      let newRowsFound = false;
      let rowsRemoved = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // 检查是否有行被移除
          if (mutation.removedNodes.length > 0) {
            mutation.removedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE && node.matches && node.matches('tr[data-row-key]')) {
                const rowKey = node.getAttribute('data-row-key');
                if (rowKey && tableState.rowData.has(rowKey)) {
                  console.log(`🗑️ 移除行数据: ${rowKey}`);
                  tableState.rowData.delete(rowKey);
                  rowsRemoved = true;
                }
              }
            });
          }

          // 检查是否有新行添加
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              let newRows = [];

              // 如果节点本身是行
              if (node.matches && node.matches('tr[data-row-key]')) {
                newRows.push(node);
              }

              // 如果节点包含行
              if (node.querySelectorAll) {
                const childRows = node.querySelectorAll('tr[data-row-key]');
                if (childRows.length > 0) {
                  newRows = [...newRows, ...childRows];
                }
              }

              if (newRows.length > 0) {
                newRowsFound = true;
                console.log(`🆕 发现 ${newRows.length} 个新行`);

                newRows.forEach(row => {
                  const rowKey = row.getAttribute('data-row-key');
                  if (rowKey) {
                    // 如果是新行，添加checkbox
                    if (!tableState.rowData.has(rowKey)) {
                      console.log(`🆕 处理新行: ${rowKey}`);
                      addCheckboxesToFirstCell(row, rowKey);
                      tableState.rowData.set(rowKey, {
                        uploadData: false,
                        downloadVideo: false,
                        rowElement: row
                      });
                    } else {
                      // 如果是已存在的行（可能是重新渲染），更新checkbox状态
                      console.log(`🔄 更新现有行: ${rowKey}`);
                      const existingData = tableState.rowData.get(rowKey);
                      existingData.rowElement = row;

                      // 确保checkbox存在，如果不存在则添加
                      if (!row.querySelector('.custom-row-checkboxes')) {
                        addCheckboxesToFirstCell(row, rowKey);
                      }

                      // 恢复checkbox状态
                      restoreCheckboxState(row, rowKey, existingData);
                    }
                  }
                });
              }

              // 检查是否是表格内容的大规模更新（如翻页）
              if (node.matches && (node.matches('tbody') || node.matches('.ant-table-tbody'))) {
                shouldUpdateCheckboxes = true;
                console.log('📊 检测到表格内容更新');
              }
            }
          });
        }

        // 监听属性变化（如class变化可能表示状态更新）
        if (mutation.type === 'attributes' && mutation.target.matches && mutation.target.matches('tr[data-row-key]')) {
          const rowKey = mutation.target.getAttribute('data-row-key');
          if (rowKey && tableState.rowData.has(rowKey)) {
            // 确保checkbox状态正确
            const rowData = tableState.rowData.get(rowKey);
            restoreCheckboxState(mutation.target, rowKey, rowData);
          }
        }
      });

      // 如果检测到大规模更新，重新扫描所有行
      if (shouldUpdateCheckboxes) {
        setTimeout(() => {
          scanAndUpdateAllRows(table);
        }, 100);
      }

      // 更新全选状态
      if (newRowsFound || rowsRemoved) {
        setTimeout(() => {
          updateSelectAllState('upload');
          updateSelectAllState('video');
          console.log(`📊 当前表格状态: ${tableState.rowData.size} 行数据`);
        }, 50);
      }
    });

    // 监控表格及其父元素的变化
    tableState.observer.observe(table, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style']
    });

    if (table.parentElement) {
      tableState.observer.observe(table.parentElement, {
        childList: true,
        subtree: true
      });
    }
  }

  /**
   * 恢复checkbox状态
   */
  function restoreCheckboxState(row, rowKey, rowData) {
    const uploadCheckbox = row.querySelector(`.upload-checkbox[data-row-key="${rowKey}"]`);
    const videoCheckbox = row.querySelector(`.video-checkbox[data-row-key="${rowKey}"]`);

    if (uploadCheckbox && uploadCheckbox.checked !== rowData.uploadData) {
      uploadCheckbox.checked = rowData.uploadData;
      console.log(`🔄 恢复行 ${rowKey} 上传checkbox状态: ${rowData.uploadData}`);
    }

    if (videoCheckbox && videoCheckbox.checked !== rowData.downloadVideo) {
      videoCheckbox.checked = rowData.downloadVideo;
      console.log(`🔄 恢复行 ${rowKey} 视频checkbox状态: ${rowData.downloadVideo}`);
    }
  }

  /**
   * 扫描并更新所有行
   */
  function scanAndUpdateAllRows(table) {
    console.log('🔍 扫描并更新所有表格行...');

    const tbody = table.querySelector('tbody');
    if (!tbody) return;

    const currentRows = tbody.querySelectorAll('tr[data-row-key]');
    const currentRowKeys = new Set();

    // 处理当前可见的行
    currentRows.forEach(row => {
      const rowKey = row.getAttribute('data-row-key');
      if (rowKey) {
        currentRowKeys.add(rowKey);

        if (!tableState.rowData.has(rowKey)) {
          // 新行
          console.log(`🆕 扫描发现新行: ${rowKey}`);
          addCheckboxesToFirstCell(row, rowKey);
          tableState.rowData.set(rowKey, {
            uploadData: false,
            downloadVideo: false,
            rowElement: row
          });
        } else {
          // 现有行，更新引用和状态
          const existingData = tableState.rowData.get(rowKey);
          existingData.rowElement = row;

          if (!row.querySelector('.custom-row-checkboxes')) {
            addCheckboxesToFirstCell(row, rowKey);
          }

          restoreCheckboxState(row, rowKey, existingData);
        }
      }
    });

    // 清理不再存在的行数据（可选，如果需要保持翻页状态则注释掉）
    // const rowsToRemove = [];
    // tableState.rowData.forEach((data, rowKey) => {
    //   if (!currentRowKeys.has(rowKey)) {
    //     rowsToRemove.push(rowKey);
    //   }
    // });
    //
    // rowsToRemove.forEach(rowKey => {
    //   console.log(`🗑️ 清理不存在的行数据: ${rowKey}`);
    //   tableState.rowData.delete(rowKey);
    // });

    console.log(`📊 扫描完成，当前 ${currentRows.length} 行，总计 ${tableState.rowData.size} 行数据`);
  }

  // ==================== 初始化 ====================



  // 监听页面变化，重新初始化
  let lastUrl = location.href;
  new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
      lastUrl = url;
      tableState.isInitialized = false;
      setTimeout(initTableEnhancement, 2000);
    }
  }).observe(document, { subtree: true, childList: true });

  /**
   * 格式化单场直播电商商品数据为Markdown
   * @param {Object} spuData - 单场直播电商商品数据
   * @returns {string} Markdown格式的商品数据报告
   */
  function formatSingleLiveEcSpuDataAsMarkdown(spuData) {
    if (!spuData || !spuData.spuDataList || spuData.spuDataList.length === 0) {
      return '# 商品信息\n\n暂无商品数据\n\n';
    }

    let markdown = '# 商品信息\n\n';

    // 添加商品统计概览
    markdown += '## 商品统计概览\n\n';
    markdown += '| 统计项 | 数值 |\n';
    markdown += '|--------|------|\n';
    markdown += `| 商品总数 | ${spuData.totalCnt || 0} |\n`;
    markdown += `| 来源总数 | ${spuData.srcTotal || 0} |\n`;
    markdown += `| 全部来源 | ${spuData.srcAll || 0} |\n`;
    markdown += `| 在售来源 | ${spuData.srcSelling || 0} |\n`;

    if (spuData.recommendWord) {
      markdown += `| 推荐词 | ${spuData.recommendWord} |\n`;
    }

    markdown += '\n';

    // 添加商品详细信息表格
    markdown += '## 商品详细信息\n\n';
    markdown += '| 商品信息 | 库存 | 创建订单数 | 成交订单数 | 成交金额 | 点击成交转化率 |\n';
    markdown += '|----------|------|------------|------------|----------|----------------|\n';

    spuData.spuDataList.forEach(item => {
      const baseData = item.baseData || {};

      // 商品信息（包含图片、名称、价格、来源）
      const productInfo = `**${baseData.spuName || '未知商品'}**<br/>` +
                         `¥${(parseFloat(baseData.price || 0) / 100).toFixed(2)}<br/>` +
                         `${baseData.srcName || '未知来源'}`;

      // 库存
      const stock = item.stock || baseData.stock || 0;

      // 创建订单数
      const createPv = item.createPv || 0;

      // 成交订单数
      const payPv = item.payPv || 0;

      // 成交金额（转换为元）
      const gmv = `¥${(parseFloat(item.gmv || 0) / 100).toFixed(2)}`;

      // 点击成交转化率（转换为百分比）
      const clkPayRatio = `${(parseFloat(item.clkPayRatio || 0) * 100).toFixed(2)}%`;

      markdown += `| ${productInfo} | ${stock.toLocaleString()} | ${createPv} | ${payPv} | ${gmv} | ${clkPayRatio} |\n`;
    });

    markdown += '\n';

    // 添加商品详细信息列表
    markdown += '## 商品详细信息列表\n\n';

    spuData.spuDataList.forEach((item, index) => {
      const baseData = item.baseData || {};

      markdown += `### ${index + 1}. ${baseData.spuName || '未知商品'}\n\n`;

      // 基础信息
      markdown += '**基础信息**\n\n';
      markdown += `- **商品ID**: ${baseData.spuId || '-'}\n`;
      markdown += `- **源商品ID**: ${baseData.srcSpuId || '-'}\n`;
      markdown += `- **商品名称**: ${baseData.spuName || '-'}\n`;
      markdown += `- **价格**: ¥${(parseFloat(baseData.price || 0) / 100).toFixed(2)}\n`;
      markdown += `- **来源**: ${baseData.srcName || '-'}\n`;
      markdown += `- **来源ID**: ${baseData.src || '-'}\n`;
      markdown += `- **是否在架**: ${item.isInShelf ? '是' : '否'}\n`;

      if (baseData.thumbUrl) {
        markdown += `- **商品图片**: ![商品图片](${baseData.thumbUrl})\n`;
      }

      if (item.tag) {
        markdown += `- **标签**: ${item.tag}\n`;
      }

      markdown += '\n';

      // 销售数据
      markdown += '**销售数据**\n\n';
      markdown += `- **库存**: ${(item.stock || baseData.stock || 0).toLocaleString()}\n`;
      markdown += `- **创建订单数**: ${item.createPv || 0}\n`;
      markdown += `- **成交订单数**: ${item.payPv || 0}\n`;
      markdown += `- **成交金额**: ¥${(parseFloat(item.gmv || 0) / 100).toFixed(2)}\n`;
      markdown += `- **点击成交转化率**: ${(parseFloat(item.clkPayRatio || 0) * 100).toFixed(2)}%\n`;

      // 计算转化率
      if (item.createPv && item.payPv) {
        const conversionRate = ((item.payPv / item.createPv) * 100).toFixed(2);
        markdown += `- **订单转化率**: ${conversionRate}%\n`;
      }

      markdown += '\n';
    });

    // 添加数据分析
    if (spuData.spuDataList.length > 1) {
      markdown += '## 数据分析\n\n';

      // 计算总计数据
      const totalCreatePv = spuData.spuDataList.reduce((sum, item) => sum + (item.createPv || 0), 0);
      const totalPayPv = spuData.spuDataList.reduce((sum, item) => sum + (item.payPv || 0), 0);
      const totalGmv = spuData.spuDataList.reduce((sum, item) => sum + parseFloat(item.gmv || 0), 0);
      const totalStock = spuData.spuDataList.reduce((sum, item) => sum + (item.stock || item.baseData?.stock || 0), 0);

      markdown += '### 汇总数据\n\n';
      markdown += '| 指标 | 数值 |\n';
      markdown += '|------|------|\n';
      markdown += `| 总库存 | ${totalStock.toLocaleString()} |\n`;
      markdown += `| 总创建订单数 | ${totalCreatePv} |\n`;
      markdown += `| 总成交订单数 | ${totalPayPv} |\n`;
      markdown += `| 总成交金额 | ¥${(totalGmv / 100).toFixed(2)} |\n`;

      if (totalCreatePv > 0) {
        const overallConversionRate = ((totalPayPv / totalCreatePv) * 100).toFixed(2);
        markdown += `| 整体订单转化率 | ${overallConversionRate}% |\n`;
      }

      markdown += '\n';

      // 找出表现最好的商品
      const bestPerformingProduct = spuData.spuDataList.reduce((best, current) => {
        const currentGmv = parseFloat(current.gmv || 0);
        const bestGmv = parseFloat(best.gmv || 0);
        return currentGmv > bestGmv ? current : best;
      });

      if (bestPerformingProduct) {
        markdown += '### 表现最佳商品\n\n';
        markdown += `**${bestPerformingProduct.baseData?.spuName || '未知商品'}**\n\n`;
        markdown += `- 成交金额: ¥${(parseFloat(bestPerformingProduct.gmv || 0) / 100).toFixed(2)}\n`;
        markdown += `- 成交订单数: ${bestPerformingProduct.payPv || 0}\n`;
        markdown += `- 点击成交转化率: ${(parseFloat(bestPerformingProduct.clkPayRatio || 0) * 100).toFixed(2)}%\n`;
        markdown += '\n';
      }
    }

    return markdown;
  }

  /**
   * 格式化整场看播用户画像数据为Markdown
   * @param {Object} portraitData - 整场看播用户画像数据
   * @returns {string} Markdown格式的用户画像报告
   */
  function formatFullLiveUserPortraitAsMarkdown(portraitData) {
    if (!portraitData) return '';

    let markdown = '# 整场看播用户画像\n\n';

    // 处理基础概览数据
    if (portraitData.overview) {
      markdown += '## 直播概览\n\n';
      const overview = portraitData.overview;

      markdown += '| 指标 | 数值 |\n';
      markdown += '|------|------|\n';
      markdown += `| 直播对象ID | ${portraitData.liveObjectId || '-'} |\n`;
      markdown += `| 主播昵称 | ${portraitData.anchorNickname || '-'} |\n`;
      markdown += `| 直播描述 | ${portraitData.liveDescription || '-'} |\n`;
      markdown += `| 创建时间 | ${portraitData.createTime ? new Date(parseInt(portraitData.createTime) * 1000).toLocaleString() : '-'} |\n`;
      markdown += `| 结束时间 | ${portraitData.endTime ? new Date(parseInt(portraitData.endTime) * 1000).toLocaleString() : '-'} |\n`;
      markdown += `| 是否直播中 | ${portraitData.isLiving ? '是' : '否'} |\n`;
      markdown += `| 实时在线人数 | ${overview.onlineWatchUv || 0} |\n`;
      markdown += `| 累计观看人数 | ${overview.cumulativeWatchUv || 0} |\n`;
      markdown += `| 人均观看时长(秒) | ${overview.averageWatchSecondsPerAudience || 0} |\n`;
      markdown += `| 累计观看次数 | ${overview.cumulativeWatchPv || 0} |\n`;
      markdown += `| 累计新增关注人数 | ${overview.cumulativeNewFollowUv || 0} |\n`;
      markdown += `| 累计新增粉丝团人数 | ${overview.cumulativeNewFansClubUv || 0} |\n`;
      markdown += `| 累计评论人数 | ${overview.cumulativeCommentUv || 0} |\n`;
      markdown += `| 累计评论次数 | ${overview.cumulativeCommentPv || 0} |\n`;
      markdown += `| 累计分享人数 | ${overview.cumulativeSharingUv || 0} |\n`;
      markdown += `| 累计分享次数 | ${overview.cumulativeSharingPv || 0} |\n`;
      markdown += `| 累计点赞次数 | ${overview.cumulativeLikePv || 0} |\n`;
      markdown += `| 峰值在线观看人数 | ${overview.peakOnlineWatchUv || 0} |\n`;
      markdown += `| 曝光人数 | ${overview.impressionUv || 0} |\n`;
      markdown += `| 曝光次数 | ${overview.impressionPv || 0} |\n`;
      markdown += '\n';
    }

    // 处理用户画像数据
    if (portraitData.portraitAudience && portraitData.portraitAudience.cumulativeWatchUv) {
      markdown += formatUserPortraitSection(portraitData.portraitAudience.cumulativeWatchUv, '## 整场看播用户画像分析\n\n');
    }

    // 处理粉丝画像数据
    if (portraitData.portraitAudience && portraitData.portraitAudience.followerCumulativeWatchUv) {
      markdown += formatUserPortraitSection(portraitData.portraitAudience.followerCumulativeWatchUv, '## 整场看播粉丝画像分析\n\n');
    }

    // 处理转化分析数据
    if (portraitData.conversionAnalysis) {
      markdown += '## 转化分析\n\n';
      const conversion = portraitData.conversionAnalysis;

      markdown += '| 指标 | 数值 |\n';
      markdown += '|------|------|\n';
      markdown += `| 曝光人数 | ${conversion.impressionUv || 0} |\n`;
      markdown += `| 新观看人数 | ${conversion.newWatchUv || 0} |\n`;
      markdown += `| 曝光次数 | ${conversion.impressionPv || 0} |\n`;
      markdown += `| 新观看次数 | ${conversion.newWatchPv || 0} |\n`;

      // 计算转化率
      if (conversion.impressionUv && conversion.newWatchUv) {
        const conversionRate = ((conversion.newWatchUv / conversion.impressionUv) * 100).toFixed(2);
        markdown += `| 曝光到观看转化率 | ${conversionRate}% |\n`;
      }

      if (conversion.impressionPv && conversion.newWatchPv) {
        const pvConversionRate = ((conversion.newWatchPv / conversion.impressionPv) * 100).toFixed(2);
        markdown += `| 曝光次数到观看次数转化率 | ${pvConversionRate}% |\n`;
      }

      markdown += '\n';
    }

    return markdown;
  }

  /**
   * 格式化用户画像数据段落
   * @param {Array} portraitDataArray - 用户画像数据数组
   * @param {string} title - 段落标题
   * @returns {string} 格式化的Markdown段落
   */
  function formatUserPortraitSection(portraitDataArray, title) {
    let markdown = title;

    if (!portraitDataArray || !Array.isArray(portraitDataArray) || portraitDataArray.length === 0) {
      markdown += '暂无数据\n\n';
      return markdown;
    }

    // 按维度类型分组数据
    const dimensionGroups = {};
    portraitDataArray.forEach(item => {
      if (item.dimensions && item.dimensions.length > 0) {
        // 当有多个维度时，优先使用 dimensions[1]，否则使用 dimensions[0]
        const primaryDimension = item.dimensions.length > 1 ? item.dimensions[1] : item.dimensions[0];
        const type = primaryDimension.type;

        if (!dimensionGroups[type]) {
          dimensionGroups[type] = [];
        }

        // 获取数据值
        let value = '0';
        if (item.data && item.data.length > 0) {
          value = item.data[0].value || '0';
        }

        // 构建维度标签
        let label = primaryDimension.uxLabel || primaryDimension.value;

        dimensionGroups[type].push({
          label: label,
          value: value,
          dimensionValue: primaryDimension.dimensionValue,
          dimensions: item.dimensions, // 保留完整维度信息
          timestamp: item.data && item.data[0] ? item.data[0].ts : null
        });
      }
    });

    // 维度类型映射
    const dimensionTypeMap = {
      '1': '地域分布（省份）',
      '2': '年龄分布',
      '3': '性别分布',
      '4': '实时观众分布',
      '5': '粉丝分布',
      '9': '用户类型',
      '10': '地域分布（城市）',
      '12': '购买偏好',
      '13': '消费能力',
      '14': '购买行为'
    };

    // 生成各维度的分析
    Object.keys(dimensionGroups).forEach(type => {
      const typeName = dimensionTypeMap[type] || `维度类型${type}`;
      const items = dimensionGroups[type];

      markdown += `### ${typeName}\n\n`;

      // 计算总数用于百分比计算
      const total = items.reduce((sum, item) => sum + parseInt(item.value || 0), 0);

      if (total > 0) {
        markdown += '| 分类 | 人数 | 占比 |\n';
        markdown += '|------|------|------|\n';

        // 按人数排序
        items.sort((a, b) => parseInt(b.value || 0) - parseInt(a.value || 0));

        items.forEach(item => {
          const count = parseInt(item.value || 0);
          const percentage = ((count / total) * 100).toFixed(1);
          markdown += `| ${item.label} | ${count.toLocaleString()} | ${percentage}% |\n`;
        });

        markdown += '\n';

        // 添加主要特征分析
        if (items.length > 0) {
          const topItem = items[0];
          const topPercentage = ((parseInt(topItem.value || 0) / total) * 100).toFixed(1);
          markdown += `**主要特征**: ${topItem.label}占比最高，达到${topPercentage}%\n\n`;
        }
      } else {
        markdown += '暂无数据\n\n';
      }
    });

    // 处理地域详细数据（省份和城市）
    const geoData = portraitDataArray.filter(item =>
      item.dimensions && item.dimensions[0] &&
      (item.dimensions[0].type === '1' || item.dimensions[0].type === '10')
    );

    if (geoData.length > 0) {
      markdown += '### 地域分布详情\n\n';

      // 按省份和城市分组
      const provinceData = geoData.filter(item => item.dimensions[0].type === '1');
      const cityData = geoData.filter(item => item.dimensions[0].type === '10');

      if (provinceData.length > 0) {
        markdown += '#### 省份分布TOP10\n\n';
        markdown += '| 排名 | 省份 | 人数 | 占比 |\n';
        markdown += '|------|------|------|------|\n';

        const totalProvince = provinceData.reduce((sum, item) =>
          sum + parseInt(item.data && item.data[0] ? item.data[0].value : 0), 0
        );

        provinceData
          .sort((a, b) => {
            const aValue = parseInt(a.data && a.data[0] ? a.data[0].value : 0);
            const bValue = parseInt(b.data && b.data[0] ? b.data[0].value : 0);
            return bValue - aValue;
          })
          .slice(0, 10)
          .forEach((item, index) => {
            const value = parseInt(item.data && item.data[0] ? item.data[0].value : 0);
            const percentage = totalProvince > 0 ? ((value / totalProvince) * 100).toFixed(1) : '0.0';
            const label = item.dimensions[0].uxLabel || item.dimensions[0].value;
            markdown += `| ${index + 1} | ${label} | ${value.toLocaleString()} | ${percentage}% |\n`;
          });

        markdown += '\n';
      }

      if (cityData.length > 0) {
        markdown += '#### 城市分布TOP10\n\n';
        markdown += '| 排名 | 城市 | 人数 | 占比 |\n';
        markdown += '|------|------|------|------|\n';

        const totalCity = cityData.reduce((sum, item) =>
          sum + parseInt(item.data && item.data[0] ? item.data[0].value : 0), 0
        );

        cityData
          .sort((a, b) => {
            const aValue = parseInt(a.data && a.data[0] ? a.data[0].value : 0);
            const bValue = parseInt(b.data && b.data[0] ? b.data[0].value : 0);
            return bValue - aValue;
          })
          .slice(0, 10)
          .forEach((item, index) => {
            const value = parseInt(item.data && item.data[0] ? item.data[0].value : 0);
            const percentage = totalCity > 0 ? ((value / totalCity) * 100).toFixed(1) : '0.0';
            const label = item.dimensions[0].uxLabel || item.dimensions[0].value;
            markdown += `| ${index + 1} | ${label} | ${value.toLocaleString()} | ${percentage}% |\n`;
          });

        markdown += '\n';
      }
    }

    return markdown;
  }

  /**
   * 数据格式化器注册表
   * 用于管理不同数据源的格式化函数
   */
  const dataFormatters = {
    // 电商转化数据
    ecConversionDashboard: formatEcConversionDashboardAsMarkdown,
    ecConversionDashboardV3: formatEcConversionDashboardV3AsMarkdown,
    ecConversionDashboardEcData: formatEcConversionDashboardEcDataAsMarkdown,
    // 直播诊断能力数据
    liveDiagnoseCapability: formatLiveDiagnoseCapabilityAsMarkdown,
    // 商品数据
    singleLiveEcSpuData: formatSingleLiveEcSpuDataAsMarkdown,
    // 整场看播用户画像数据
    fullLiveUserPortrait: formatFullLiveUserPortraitAsMarkdown
  };

  /**
   * 注册新的数据格式化器
   * @param {string} dataType - 数据类型
   * @param {Function} formatter - 格式化函数
   */
  function registerDataFormatter(dataType, formatter) {
    dataFormatters[dataType] = formatter;
    console.log(`✅ 已注册数据格式化器: ${dataType}`);
  }

  /**
   * 主要的数据格式化函数
   * @param {Object} data - 要格式化的数据
   * @returns {string} Markdown格式的报告
   */
  function formatDataAsMarkdown(data) {
    // 如果直接传入了markdownContent，直接返回
    if (data.extractedData && data.extractedData.markdownContent) {
      return data.extractedData.markdownContent;
    }

    let markdown = '';

    // 添加标题
    markdown += '# 视频号直播数据报告\n\n';
    markdown += `**生成时间**: ${new Date().toLocaleString('zh-CN')}\n\n`;

    // 处理API数据
    if (data.extractedData && data.extractedData.apiData) {
      const apiData = data.extractedData.apiData;

      // 使用注册表处理所有数据类型
      Object.keys(dataFormatters).forEach(dataType => {
        if (apiData[dataType]) {
          try {
            const formatter = dataFormatters[dataType];
            const formattedContent = formatter(apiData[dataType]);

            if (formattedContent) {
              markdown += formattedContent;
              // 在不同数据源之间添加分隔线
              markdown += '\n---\n\n';
            }
          } catch (error) {
            console.warn(`⚠️ 格式化 ${dataType} 数据时出错:`, error);
            markdown += `## ${dataType} 数据\n\n格式化失败: ${error.message}\n\n`;
          }
        }
      });

      // 移除最后一个多余的分隔线
      if (markdown.endsWith('\n---\n\n')) {
        markdown = markdown.slice(0, -7);
      }
    }

    // 如果没有数据，添加默认内容
    if (markdown === '# 视频号直播数据报告\n\n**生成时间**: ' + new Date().toLocaleString('zh-CN') + '\n\n') {
      markdown += '暂无数据\n\n';
    }

    return markdown;
  }

  /**
   * 获取完整的电商转化数据并生成Markdown报告
   * @param {string} liveObjectId - 直播对象ID
   * @param {Object} options - 可选参数
   * @returns {Promise<Object>} 完整的数据和Markdown报告
   */
  async function getCompleteEcConversionReport(liveObjectId, options = {}) {
    if (!liveObjectId) {
      throw new Error('直播对象ID不能为空');
    }

    console.log(`🚀 开始获取完整电商转化数据报告 [${liveObjectId}]`);

    try {
      const timestamp = `${new Date().valueOf()}`;

      // 第一步：获取基础数据
      console.log('📊 正在获取基础数据...');
      const originalData = await getEcConversionDashboardData(liveObjectId, timestamp, options);
      console.log('✅ 基础数据获取成功');

      // 第二步：获取V3数据
      console.log('📈 正在获取V3数据...');
      const v3Data = await getEcConversionDashboardDataV3(liveObjectId, timestamp, options);
      console.log('✅ V3数据获取成功');

      // 第四步：获取直播诊断能力数据
      console.log('🔍 正在获取直播诊断能力数据...');
      const diagnoseData = await getLiveDiagnoseCapability(liveObjectId, timestamp, options);
      console.log('✅ 直播诊断能力数据获取成功');

      // 第五步：获取商品数据
      console.log('🛍️ 正在获取商品数据...');
      const spuData = await getSingleLiveEcSpuData(liveObjectId, timestamp, options);
      console.log('✅ 商品数据获取成功');

      // 第六步：获取整场看播用户画像数据
      console.log('👥 正在获取整场看播用户画像数据...');
      const fullPortraitData = await getFullLiveUserPortraitData(liveObjectId, timestamp, options);
      console.log('✅ 整场看播用户画像数据获取成功');

      // 第七步：获取电商转化仪表板电商数据
      console.log('🛒 正在获取电商转化仪表板电商数据...');
      const ecConversionEcData = await getEcConversionDashboardEcData(liveObjectId, timestamp, options);
      console.log('✅ 电商转化仪表板电商数据获取成功');

      // 第八步：生成完整的Markdown报告
      console.log('📝 正在生成Markdown报告...');

      // 使用数据格式化器注册表处理数据
      const testData = {
        extractedData: {
          apiData: {
            ecConversionDashboard: originalData,
            ecConversionDashboardV3: v3Data,
            liveDiagnoseCapability: diagnoseData,
            singleLiveEcSpuData: spuData,
            fullLiveUserPortrait: fullPortraitData,
            ecConversionDashboardEcData: ecConversionEcData
          }
        }
      };

      const completeMarkdown = formatDataAsMarkdown(testData);
      console.log('✅ Markdown报告生成成功');

      // 第八步：下载Markdown文件
      const filename = `complete-ec-report-${liveObjectId}-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.md`;

      downloadAsMarkdown({
        extractedData: {
          markdownContent: completeMarkdown
        }
      }, filename);

      console.log(`📄 完整报告已下载: ${filename}`);
      console.log('📊 报告内容预览:');
      console.log(completeMarkdown.substring(0, 500) + '...');

      return {
        success: true,
        originalData,
        v3Data,
        diagnoseData,
        spuData,
        fullPortraitData,
        completeMarkdown,
        filename
      };

    } catch (error) {
      console.error('❌ 获取完整电商转化数据报告失败:', error);
      throw error;
    }
  }

  // ==================== 测试函数 ====================

  /**
   * 测试电商转化仪表板数据API
   * @param {string} liveObjectId - 直播对象ID（可选，默认使用示例ID）
   * @returns {Promise<Object>} 测试结果
   */
  async function testEcConversionDashboardAPI(liveObjectId = "14701015173375068695") {
    console.log(`🧪 开始测试电商转化仪表板API [${liveObjectId}]`);

    try {
      const timestamp = `${new Date().valueOf()}`;

      // 测试API调用
      const dashboardData = await getEcConversionDashboardData(liveObjectId, timestamp);
      console.log('✅ 电商转化仪表板API调用成功:', dashboardData);

      // 测试数据格式化
      const markdownContent = formatEcConversionDashboardAsMarkdown(dashboardData);
      console.log('✅ 数据格式化成功');
      console.log('📄 生成的Markdown内容:');
      console.log(markdownContent);

      // 测试下载功能
      const testData = {
        extractedData: {
          apiData: {
            ecConversionDashboard: dashboardData
          }
        }
      };

      downloadAsMarkdown(testData, `ec-conversion-dashboard-${liveObjectId}.md`);

      return { success: true, data: dashboardData, markdown: markdownContent };
    } catch (error) {
      console.error('❌ 电商转化仪表板API测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 快速测试完整报告生成功能
   * @param {string} liveObjectId - 直播对象ID，默认使用示例ID
   * @returns {Promise<Object>} 测试结果
   */
  async function testCompleteReport(liveObjectId = "14706802472828078606") {
    console.log(`🧪 开始测试完整报告生成功能 [${liveObjectId}]`);

    try {
      const result = await getCompleteEcConversionReport(liveObjectId);

      console.log('✅ 完整报告测试成功!');
      console.log('📊 数据统计:');
      console.log(`- 基础数据字段数: ${Object.keys(result.originalData || {}).length}`);
      console.log(`- V3数据字段数: ${Object.keys(result.v3Data || {}).length}`);
      console.log(`- Markdown内容长度: ${result.completeMarkdown.length} 字符`);
      console.log(`- 下载文件名: ${result.filename}`);

      return result;
    } catch (error) {
      console.error('❌ 完整报告测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 测试直播诊断能力API
   * @param {string} liveObjectId - 直播对象ID（可选，默认使用示例ID）
   * @returns {Promise<Object>} 测试结果
   */
  async function testLiveDiagnoseCapabilityAPI(liveObjectId = "14701015173375068695") {
    console.log(`🧪 开始测试直播诊断能力API [${liveObjectId}]`);

    try {
      const timestamp = `${new Date().valueOf()}`;

      // 测试API调用
      const diagnoseData = await getLiveDiagnoseCapability(liveObjectId, timestamp);
      console.log('✅ 直播诊断能力API调用成功:', diagnoseData);

      // 测试数据格式化
      const markdownContent = formatLiveDiagnoseCapabilityAsMarkdown(diagnoseData);
      console.log('✅ 数据格式化成功');
      console.log('📄 生成的Markdown内容:');
      console.log(markdownContent);

      // 测试下载功能
      const testData = {
        extractedData: {
          apiData: {
            liveDiagnoseCapability: diagnoseData
          }
        }
      };

      downloadAsMarkdown(testData, `live-diagnose-capability-${liveObjectId}.md`);

      return { success: true, data: diagnoseData, markdown: markdownContent };
    } catch (error) {
      console.error('❌ 直播诊断能力API测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 测试单场直播电商商品数据API
   * @param {string} liveObjectId - 直播对象ID（可选，默认使用示例ID）
   * @returns {Promise<Object>} 测试结果
   */
  async function testSingleLiveEcSpuDataAPI(liveObjectId = "14706802472828078606") {
    console.log(`🧪 开始测试单场直播电商商品数据API [${liveObjectId}]`);

    try {
      const timestamp = `${new Date().valueOf()}`;

      // 测试API调用
      const spuData = await getSingleLiveEcSpuData(liveObjectId, timestamp);
      console.log('✅ 单场直播电商商品数据API调用成功:', spuData);

      // 测试数据格式化
      const markdownContent = formatSingleLiveEcSpuDataAsMarkdown(spuData);
      console.log('✅ 数据格式化成功');
      console.log('📄 生成的Markdown内容:');
      console.log(markdownContent);

      // 测试下载功能
      const testData = {
        extractedData: {
          apiData: {
            singleLiveEcSpuData: spuData
          }
        }
      };

      downloadAsMarkdown(testData, `single-live-ec-spu-data-${liveObjectId}.md`);

      return { success: true, data: spuData, markdown: markdownContent };
    } catch (error) {
      console.error('❌ 单场直播电商商品数据API测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 测试整场看播用户画像数据API
   * @param {string} liveObjectId - 直播对象ID（可选，默认使用示例ID）
   * @returns {Promise<Object>} 测试结果
   */
  async function testFullLiveUserPortraitAPI(liveObjectId = "14706802472828078606") {
    console.log(`🧪 开始测试整场看播用户画像数据API [${liveObjectId}]`);

    try {
      const timestamp = `${new Date().valueOf()}`;

      // 测试API调用
      const portraitData = await getFullLiveUserPortraitData(liveObjectId, timestamp);
      console.log('✅ 整场看播用户画像数据API调用成功:', portraitData);

      // 测试数据格式化
      const markdownContent = formatFullLiveUserPortraitAsMarkdown(portraitData);
      console.log('✅ 数据格式化成功');
      console.log('📄 生成的Markdown内容:');
      console.log(markdownContent);

      // 测试下载功能
      const testData = {
        extractedData: {
          apiData: {
            fullLiveUserPortrait: portraitData
          }
        }
      };

      downloadAsMarkdown(testData, `full-live-user-portrait-${liveObjectId}.md`);

      return { success: true, data: portraitData, markdown: markdownContent };
    } catch (error) {
      console.error('❌ 整场看播用户画像数据API测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  // ==================== 初始化 ====================

  // 在脚本加载完成后添加测试按钮
  window.addEventListener('load', () => {

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(initTableEnhancement, 1000);
      });
    } else {
      setTimeout(initTableEnhancement, 1000);
    }
  });

  // 暴露函数到全局作用域，方便控制台调用
  window.testEcConversionDashboardAPI = testEcConversionDashboardAPI;
  window.getEcConversionDashboardData = getEcConversionDashboardData;
  window.getEcConversionDashboardDataV3 = getEcConversionDashboardDataV3;
  window.getLiveDiagnoseCapability = getLiveDiagnoseCapability;
  window.getSingleLiveEcSpuData = getSingleLiveEcSpuData;
  window.getFullLiveUserPortraitData = getFullLiveUserPortraitData;
  window.formatEcConversionDashboardAsMarkdown = formatEcConversionDashboardAsMarkdown;
  window.formatEcConversionDashboardV3AsMarkdown = formatEcConversionDashboardV3AsMarkdown;
  window.formatLiveDiagnoseCapabilityAsMarkdown = formatLiveDiagnoseCapabilityAsMarkdown;
  window.formatSingleLiveEcSpuDataAsMarkdown = formatSingleLiveEcSpuDataAsMarkdown;
  window.formatFullLiveUserPortraitAsMarkdown = formatFullLiveUserPortraitAsMarkdown;
  window.testLiveDiagnoseCapabilityAPI = testLiveDiagnoseCapabilityAPI;
  window.testSingleLiveEcSpuDataAPI = testSingleLiveEcSpuDataAPI;
  window.testFullLiveUserPortraitAPI = testFullLiveUserPortraitAPI;
  window.getCompleteEcConversionReport = getCompleteEcConversionReport;
  window.testCompleteReport = testCompleteReport;
  window.registerDataFormatter = registerDataFormatter;
  window.dataFormatters = dataFormatters;

})();
