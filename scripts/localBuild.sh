#!/bin/bash
# 接受env参数，如果没有指定则默认为stg
env=${1:-stg}
# 根据env执行npm run命令，如果env为prod则执行env=prod npm run build，否则执行env=stg npm run build-stg
if [ $env = "prod" ]; then
  env=prod npm run build
  bucket="llmbot-playground"
else
  env=stg npm run build-stg
  bucket="llmbot-playground-staging"
fi
# 检查npm run命令的返回值，如果不为0则表示失败，退出脚本
if [ $? -ne 0 ]; then
  echo "npm run failed"
  exit 1
fi

echo "y" | ossutil rm "oss://$bucket" -r

ossutil cp -r build/ "oss://$bucket/"
