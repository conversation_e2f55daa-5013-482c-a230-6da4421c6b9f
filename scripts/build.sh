#!/bin/bash -e
ENV=${ENV:-"dev"}
BACKEND_PROTOCOL=${BACKEND_PROTOCOL:-"https"}
BUILD_TIME="$(date +%Y%m%d%H%M)"
IMAGE_TAG=${IMAGE_TAG:-"${ENV}-${BUILD_TIME}"}
BASE_DIR=$(pwd)

if [[ "${ENV}" == "production" ]]; then
  DEFAULT_BACKEND_DOMAIN="api.bzy.ai"
else
  DEFAULT_BACKEND_DOMAIN="${ENV}-aiapi.bzy.ai"
fi

BACKEND_DOMAIN=${BACKEND_DOMAIN:-"${DEFAULT_BACKEND_DOMAIN}"}
BUILD_DIR=${BUILD_DIR:-".buildImage"}

initBuildDir() {
  # 如果不删除经常会出现安装失败的情况
  rm -rf package-lock.json
  rm -rf "${BUILD_DIR}"
  mkdir "${BUILD_DIR}"
}

initBuildDir

cp -r docker/* "${BUILD_DIR}"

# echo '加载缓存...'
# CACHE_DIR="/cache/${CI_PROJECT_PATH}/node_modules"
# if [ ! -d "$CACHE_DIR" ]; then
#   mkdir -p $CACHE_DIR
# fi
# rm -rf node_modules
# ln -sf $CACHE_DIR node_modules

NODE_ENV=development npm config set legacy-peer-deps true

echo '安装依赖...'
NODE_ENV=development npm install

echo '加载第三方NPM包预编译后的DLL缓存...'
CACHE_DLL_DIR="/cache/${CI_PROJECT_PATH}/webpack-dll"

cacheDLLBuildFiles() {
  cp -a webpack-dll/ "${CACHE_DLL_DIR}/"
  cp -a static/scripts/vendors-dll "${CACHE_DLL_DIR}/"
}

restoreDLLBuildFiles() {
  cp -a "${CACHE_DLL_DIR}/build" webpack-dll/
  cp -a "${CACHE_DLL_DIR}/vendors-dll" static/scripts/
}

if [ ! -d "$CACHE_DLL_DIR" ]; then
  npm run dll
  cacheDLLBuildFiles
else
  # 比较两个时间戳文件是否相同
  CACHE_DLL_BUILD_FILE=$(cat "${CACHE_DLL_DIR}/build-time.json")
  PROJECT_DLL_BUILD_FILE=$(cat "${BASE_DIR}/webpack-dll/build-time.json")
  if [ $CACHE_DLL_BUILD_FILE == $PROJECT_DLL_BUILD_FILE ]; then
    # 相同，不需要重新预编译第三方NPM包，加载缓存中DLL文件
    restoreDLLBuildFiles
  else
    # 不相同，重新预编译第三方NPM包，生成DLL文件，进行缓存
    npm run dll
    rm -rf $CACHE_DLL_DIR
    cacheDLLBuildFiles
  fi
fi

echo '开始构建...'
npm run build

cp -r build "${BUILD_DIR}"
